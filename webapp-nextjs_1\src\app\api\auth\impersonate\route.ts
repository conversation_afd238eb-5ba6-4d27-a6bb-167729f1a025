import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    // Estrai il corpo della richiesta
    const body = await request.json()
    const { user_id } = body

    if (!user_id) {
      return NextResponse.json(
        { detail: 'user_id è richiesto' },
        { status: 400 }
      )
    }

    // Estrai il token di autorizzazione dall'header
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { detail: 'Token di autorizzazione mancante' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)

    console.log('🔄 Impersonate API: Proxying request to backend:', `http://localhost:8001/api/auth/impersonate`)
    console.log('🔄 Impersonate API: Request body:', { user_id })

    // Effettua la richiesta al backend FastAPI
    const backendResponse = await fetch('http://localhost:8001/api/auth/impersonate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({ user_id })
    })

    console.log('📡 Impersonate API: Backend response status:', backendResponse.status)

    if (!backendResponse.ok) {
      const errorData = await backendResponse.text()
      console.error('❌ Impersonate API: Backend error:', errorData)
      
      // Prova a parsare come JSON, altrimenti usa il testo
      let errorResponse
      try {
        errorResponse = JSON.parse(errorData)
      } catch {
        errorResponse = { detail: errorData || 'Errore durante l\'impersonificazione' }
      }
      
      return NextResponse.json(errorResponse, { status: backendResponse.status })
    }

    const data = await backendResponse.json()
    console.log('📡 Impersonate API: Backend response data:', data)

    return NextResponse.json(data)
  } catch (error) {
    console.error('❌ Impersonate API: Error:', error)
    return NextResponse.json(
      { detail: 'Errore interno del server durante l\'impersonificazione' },
      { status: 500 }
    )
  }
}
