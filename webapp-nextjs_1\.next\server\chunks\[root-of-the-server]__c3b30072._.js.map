{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_1/src/app/api/cantieri/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\n\nexport async function GET(request: NextRequest) {\n  try {\n    // Estrai il token di autorizzazione dall'header\n    const authHeader = request.headers.get('authorization')\n    \n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return NextResponse.json(\n        { \n          detail: 'Token di autorizzazione mancante' \n        }, \n        { status: 401 }\n      )\n    }\n\n    // Proxy la richiesta al backend FastAPI\n    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'\n    \n    console.log('🔄 Cantieri API: Proxying request to backend:', `${backendUrl}/api/cantieri`)\n    \n    const response = await fetch(`${backendUrl}/api/cantieri`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': authHeader\n      }\n    })\n\n    console.log('📡 Cantieri API: Backend response status:', response.status)\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: 'Errore sconosciuto' }))\n      console.error('❌ Cantieri API: Backend error:', errorData)\n      return NextResponse.json(errorData, { \n        status: response.status \n      })\n    }\n\n    const data = await response.json()\n    console.log('📡 Cantieri API: Backend response data:', data)\n\n    return NextResponse.json(data, { \n      status: response.status,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    })\n\n  } catch (error) {\n    console.error('❌ Cantieri API: Error:', error)\n    return NextResponse.json(\n      { \n        detail: 'Errore interno del server' \n      }, \n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    \n    // Estrai il token di autorizzazione dall'header\n    const authHeader = request.headers.get('authorization')\n    \n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return NextResponse.json(\n        { \n          detail: 'Token di autorizzazione mancante' \n        }, \n        { status: 401 }\n      )\n    }\n\n    // Proxy la richiesta al backend FastAPI\n    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'\n    \n    console.log('🔄 Cantieri API: Proxying POST request to backend:', `${backendUrl}/api/cantieri`)\n    \n    const response = await fetch(`${backendUrl}/api/cantieri`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': authHeader\n      },\n      body: JSON.stringify(body)\n    })\n\n    console.log('📡 Cantieri API: Backend response status:', response.status)\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: 'Errore sconosciuto' }))\n      console.error('❌ Cantieri API: Backend error:', errorData)\n      return NextResponse.json(errorData, { \n        status: response.status \n      })\n    }\n\n    const data = await response.json()\n    console.log('📡 Cantieri API: Backend response data:', data)\n\n    return NextResponse.json(data, { \n      status: response.status,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    })\n\n  } catch (error) {\n    console.error('❌ Cantieri API: POST Error:', error)\n    return NextResponse.json(\n      { \n        detail: 'Errore interno del server' \n      }, \n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,gDAAgD;QAChD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QAEvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,QAAQ;YACV,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,wCAAwC;QACxC,MAAM,aAAa,6DAAmC;QAEtD,QAAQ,GAAG,CAAC,iDAAiD,GAAG,WAAW,aAAa,CAAC;QAEzF,MAAM,WAAW,MAAM,MAAM,GAAG,WAAW,aAAa,CAAC,EAAE;YACzD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB;YACnB;QACF;QAEA,QAAQ,GAAG,CAAC,6CAA6C,SAAS,MAAM;QAExE,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,QAAQ;gBAAqB,CAAC;YACrF,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,WAAW;gBAClC,QAAQ,SAAS,MAAM;YACzB;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,2CAA2C;QAEvD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;YAC7B,QAAQ,SAAS,MAAM;YACvB,SAAS;gBACP,gBAAgB;YAClB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,QAAQ;QACV,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,gDAAgD;QAChD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QAEvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,QAAQ;YACV,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,wCAAwC;QACxC,MAAM,aAAa,6DAAmC;QAEtD,QAAQ,GAAG,CAAC,sDAAsD,GAAG,WAAW,aAAa,CAAC;QAE9F,MAAM,WAAW,MAAM,MAAM,GAAG,WAAW,aAAa,CAAC,EAAE;YACzD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB;YACnB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,QAAQ,GAAG,CAAC,6CAA6C,SAAS,MAAM;QAExE,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,QAAQ;gBAAqB,CAAC;YACrF,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,WAAW;gBAClC,QAAQ,SAAS,MAAM;YACzB;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,2CAA2C;QAEvD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;YAC7B,QAAQ,SAAS,MAAM;YACvB,SAAS;gBACP,gBAAgB;YAClB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,QAAQ;QACV,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}