(()=>{var e={};e.id=3912,e.ids=[3912],e.modules={637:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>P,routeModule:()=>u,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var a={};r.r(a),r.d(a,{GET:()=>c,POST:()=>p});var o=r(96559),s=r(48088),n=r(37719),i=r(32190);async function c(e,{params:t}){try{let r=t.cantiereId,a=e.headers.get("authorization");if(!a||!a.startsWith("Bearer "))return i.NextResponse.json({detail:"Token di autorizzazione mancante"},{status:401});let{searchParams:o}=new URL(e.url),s=new URLSearchParams;o.forEach((e,t)=>{s.append(t,e)});let n=s.toString(),c=`http://localhost:8001/api/parco-cavi/${r}${n?`?${n}`:""}`;console.log("\uD83D\uDD04 Parco-Cavi API: Proxying request to backend:",c);let p=await fetch(c,{method:"GET",headers:{"Content-Type":"application/json",Authorization:a}});if(console.log("\uD83D\uDCE1 Parco-Cavi API: Backend response status:",p.status),!p.ok){let e=await p.json().catch(()=>({detail:"Errore sconosciuto"}));return console.error("❌ Parco-Cavi API: Backend error:",e),i.NextResponse.json(e,{status:p.status})}let u=await p.json();return console.log("\uD83D\uDCE1 Parco-Cavi API: Backend response data:",u),i.NextResponse.json(u,{status:p.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("❌ Parco-Cavi API: Error:",e),i.NextResponse.json({detail:"Errore interno del server"},{status:500})}}async function p(e,{params:t}){try{let r=t.cantiereId,a=await e.json(),o=e.headers.get("authorization");if(!o||!o.startsWith("Bearer "))return i.NextResponse.json({detail:"Token di autorizzazione mancante"},{status:401});let s="http://localhost:8001";console.log("\uD83D\uDD04 Parco-Cavi API: Proxying POST request to backend:",`${s}/api/parco-cavi/${r}`);let n=await fetch(`${s}/api/parco-cavi/${r}`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:o},body:JSON.stringify(a)});if(console.log("\uD83D\uDCE1 Parco-Cavi API: Backend response status:",n.status),!n.ok){let e=await n.json().catch(()=>({detail:"Errore sconosciuto"}));return console.error("❌ Parco-Cavi API: Backend error:",e),i.NextResponse.json(e,{status:n.status})}let c=await n.json();return console.log("\uD83D\uDCE1 Parco-Cavi API: Backend response data:",c),i.NextResponse.json(c,{status:n.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("❌ Parco-Cavi API: POST Error:",e),i.NextResponse.json({detail:"Errore interno del server"},{status:500})}}let u=new o.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/parco-cavi/[cantiereId]/route",pathname:"/api/parco-cavi/[cantiereId]",filename:"route",bundlePath:"app/api/parco-cavi/[cantiereId]/route"},resolvedPagePath:"C:\\CMS\\webapp-nextjs_1\\src\\app\\api\\parco-cavi\\[cantiereId]\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:h}=u;function P(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580],()=>r(637));module.exports=a})();