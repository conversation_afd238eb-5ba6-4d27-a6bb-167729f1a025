"use strict";exports.id=471,exports.ids=[471],exports.modules={13861:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19169:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},26134:(e,t,r)=>{r.d(t,{G$:()=>U,Hs:()=>j,UC:()=>er,VY:()=>eo,ZL:()=>ee,bL:()=>Y,bm:()=>ea,hE:()=>en,hJ:()=>et,l9:()=>Q});var n=r(43210),o=r(70569),a=r(98599),i=r(11273),s=r(96963),l=r(65551),d=r(31355),c=r(32547),u=r(25028),p=r(46059),f=r(14163),g=r(1359),m=r(42247),h=r(63376),y=r(8730),x=r(60687),v="Dialog",[b,j]=(0,i.A)(v),[C,D]=b(v),k=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:i,modal:d=!0}=e,c=n.useRef(null),u=n.useRef(null),[p,f]=(0,l.i)({prop:o,defaultProp:a??!1,onChange:i,caller:v});return(0,x.jsx)(C,{scope:t,triggerRef:c,contentRef:u,contentId:(0,s.B)(),titleId:(0,s.B)(),descriptionId:(0,s.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};k.displayName=v;var R="DialogTrigger",w=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=D(R,r),s=(0,a.s)(t,i.triggerRef);return(0,x.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":z(i.open),...n,ref:s,onClick:(0,o.m)(e.onClick,i.onOpenToggle)})});w.displayName=R;var E="DialogPortal",[I,N]=b(E,{forceMount:void 0}),O=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,i=D(E,t);return(0,x.jsx)(I,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,x.jsx)(p.C,{present:r||i.open,children:(0,x.jsx)(u.Z,{asChild:!0,container:a,children:e})}))})};O.displayName=E;var A="DialogOverlay",_=n.forwardRef((e,t)=>{let r=N(A,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=D(A,e.__scopeDialog);return a.modal?(0,x.jsx)(p.C,{present:n||a.open,children:(0,x.jsx)(P,{...o,ref:t})}):null});_.displayName=A;var F=(0,y.TL)("DialogOverlay.RemoveScroll"),P=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D(A,r);return(0,x.jsx)(m.A,{as:F,allowPinchZoom:!0,shards:[o.contentRef],children:(0,x.jsx)(f.sG.div,{"data-state":z(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),G="DialogContent",L=n.forwardRef((e,t)=>{let r=N(G,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=D(G,e.__scopeDialog);return(0,x.jsx)(p.C,{present:n||a.open,children:a.modal?(0,x.jsx)(T,{...o,ref:t}):(0,x.jsx)(q,{...o,ref:t})})});L.displayName=G;var T=n.forwardRef((e,t)=>{let r=D(G,e.__scopeDialog),i=n.useRef(null),s=(0,a.s)(t,r.contentRef,i);return n.useEffect(()=>{let e=i.current;if(e)return(0,h.Eq)(e)},[]),(0,x.jsx)(M,{...e,ref:s,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),q=n.forwardRef((e,t)=>{let r=D(G,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,x.jsx)(M,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||r.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let n=t.target;r.triggerRef.current?.contains(n)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),M=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:s,...l}=e,u=D(G,r),p=n.useRef(null),f=(0,a.s)(t,p);return(0,g.Oh)(),(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:s,children:(0,x.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":z(u.open),...l,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(X,{titleId:u.titleId}),(0,x.jsx)(J,{contentRef:p,descriptionId:u.descriptionId})]})]})}),B="DialogTitle",S=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D(B,r);return(0,x.jsx)(f.sG.h2,{id:o.titleId,...n,ref:t})});S.displayName=B;var $="DialogDescription",W=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D($,r);return(0,x.jsx)(f.sG.p,{id:o.descriptionId,...n,ref:t})});W.displayName=$;var Z="DialogClose",H=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=D(Z,r);return(0,x.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function z(e){return e?"open":"closed"}H.displayName=Z;var K="DialogTitleWarning",[U,V]=(0,i.q)(K,{contentName:G,titleName:B,docsSlug:"dialog"}),X=({titleId:e})=>{let t=V(K),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return n.useEffect(()=>{e&&(document.getElementById(e)||console.error(r))},[r,e]),null},J=({contentRef:e,descriptionId:t})=>{let r=V("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return n.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&(document.getElementById(t)||console.warn(o))},[o,e,t]),null},Y=k,Q=w,ee=O,et=_,er=L,en=S,eo=W,ea=H},40211:(e,t,r)=>{r.d(t,{C1:()=>D,bL:()=>j});var n=r(43210),o=r(98599),a=r(11273),i=r(70569),s=r(65551),l=r(83721),d=r(18853),c=r(46059),u=r(14163),p=r(60687),f="Checkbox",[g,m]=(0,a.A)(f),[h,y]=g(f);function x(e){let{__scopeCheckbox:t,checked:r,children:o,defaultChecked:a,disabled:i,form:l,name:d,onCheckedChange:c,required:u,value:g="on",internal_do_not_use_render:m}=e,[y,x]=(0,s.i)({prop:r,defaultProp:a??!1,onChange:c,caller:f}),[v,b]=n.useState(null),[j,C]=n.useState(null),D=n.useRef(!1),k=!v||!!l||!!v.closest("form"),R={checked:y,disabled:i,setChecked:x,control:v,setControl:b,name:d,form:l,value:g,hasConsumerStoppedPropagationRef:D,required:u,defaultChecked:!w(a)&&a,isFormControl:k,bubbleInput:j,setBubbleInput:C};return(0,p.jsx)(h,{scope:t,...R,children:"function"==typeof m?m(R):o})}var v="CheckboxTrigger",b=n.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:r,...a},s)=>{let{control:l,value:d,disabled:c,checked:f,required:g,setControl:m,setChecked:h,hasConsumerStoppedPropagationRef:x,isFormControl:b,bubbleInput:j}=y(v,e),C=(0,o.s)(s,m),D=n.useRef(f);return n.useEffect(()=>{let e=l?.form;if(e){let t=()=>h(D.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[l,h]),(0,p.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":w(f)?"mixed":f,"aria-required":g,"data-state":E(f),"data-disabled":c?"":void 0,disabled:c,value:d,...a,ref:C,onKeyDown:(0,i.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.m)(r,e=>{h(e=>!!w(e)||!e),j&&b&&(x.current=e.isPropagationStopped(),x.current||e.stopPropagation())})})});b.displayName=v;var j=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:o,defaultChecked:a,required:i,disabled:s,value:l,onCheckedChange:d,form:c,...u}=e;return(0,p.jsx)(x,{__scopeCheckbox:r,checked:o,defaultChecked:a,disabled:s,required:i,onCheckedChange:d,name:n,form:c,value:l,internal_do_not_use_render:({isFormControl:e})=>(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(b,{...u,ref:t,__scopeCheckbox:r}),e&&(0,p.jsx)(R,{__scopeCheckbox:r})]})})});j.displayName=f;var C="CheckboxIndicator",D=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...o}=e,a=y(C,r);return(0,p.jsx)(c.C,{present:n||w(a.checked)||!0===a.checked,children:(0,p.jsx)(u.sG.span,{"data-state":E(a.checked),"data-disabled":a.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});D.displayName=C;var k="CheckboxBubbleInput",R=n.forwardRef(({__scopeCheckbox:e,...t},r)=>{let{control:a,hasConsumerStoppedPropagationRef:i,checked:s,defaultChecked:c,required:f,disabled:g,name:m,value:h,form:x,bubbleInput:v,setBubbleInput:b}=y(k,e),j=(0,o.s)(r,b),C=(0,l.Z)(s),D=(0,d.X)(a);n.useEffect(()=>{if(!v)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!i.current;if(C!==s&&e){let r=new Event("click",{bubbles:t});v.indeterminate=w(s),e.call(v,!w(s)&&s),v.dispatchEvent(r)}},[v,C,s,i]);let R=n.useRef(!w(s)&&s);return(0,p.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:c??R.current,required:f,disabled:g,name:m,value:h,form:x,...t,tabIndex:-1,ref:j,style:{...t.style,...D,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function w(e){return"indeterminate"===e}function E(e){return w(e)?"indeterminate":e?"checked":"unchecked"}R.displayName=k},99270:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};