(()=>{var e={};e.id=2162,e.ids=[2162],e.modules={675:(e,r,t)=>{Promise.resolve().then(t.bind(t,36200))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10038:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(60687);t(43210);var o=t(85814),i=t.n(o),a=t(23361),n=t(28559),p=t(29523),l=t(12413);function d(){return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-slate-100 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md space-y-6",children:[(0,s.jsxs)("div",{className:"text-center space-y-2",children:[(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center",children:(0,s.jsx)(a.A,{className:"w-8 h-8 text-white"})})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-slate-900",children:"CABLYS"}),(0,s.jsx)("p",{className:"text-slate-600",children:"Recupero Password"})]}),(0,s.jsx)(l.r,{}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)(i(),{href:"/login",children:(0,s.jsxs)(p.$,{variant:"ghost",className:"text-slate-600 hover:text-slate-900",children:[(0,s.jsx)(n.A,{className:"w-4 h-4 mr-2"}),"Torna al Login"]})})})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14747:(e,r,t)=>{Promise.resolve().then(t.bind(t,10038))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20900:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>l});var s=t(65239),o=t(48088),i=t(88170),a=t.n(i),n=t(30893),p={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>n[e]);t.d(r,p);let l={children:["",{children:["forgot-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,36200)),"C:\\CMS\\webapp-nextjs_1\\src\\app\\forgot-password\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\CMS\\webapp-nextjs_1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\CMS\\webapp-nextjs_1\\src\\app\\forgot-password\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/forgot-password/page",pathname:"/forgot-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36200:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs_1\\\\src\\\\app\\\\forgot-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_1\\src\\app\\forgot-password\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,6539,1658,4951,814],()=>t(20900));module.exports=s})();