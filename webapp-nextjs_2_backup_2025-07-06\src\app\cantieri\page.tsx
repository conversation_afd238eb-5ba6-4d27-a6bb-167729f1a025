'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { useAuth } from '@/contexts/AuthContext'
import { cantieriApi } from '@/lib/api'
import { Cantiere } from '@/types'
import {
  Building2,
  Plus,
  Search,
  Loader2,
  Eye,
  EyeOff,
  Copy,
  Settings,
  Lock,
  Edit,
  ArrowRight,
  BarChart3,
  Key,
} from 'lucide-react'

export default function CantieriPage() {
  const { user, isAuthenticated, isLoading, selectCantiere } = useAuth()
  const router = useRouter()
  const [cantieri, setCantieri] = useState<Cantiere[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [cantieriStats, setCantieriStats] = useState<Record<number, { percentuale_avanzamento: number }>>({})
  const [statsLoading, setStatsLoading] = useState(false)
  const [passwordVisibility, setPasswordVisibility] = useState<Record<number, boolean>>({})
  const [revealedPasswords, setRevealedPasswords] = useState<Record<number, string>>({})
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showPasswordDialog, setShowPasswordDialog] = useState(false)
  const [selectedCantiere, setSelectedCantiere] = useState<Cantiere | null>(null)
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  const [formData, setFormData] = useState({
    commessa: '',
    descrizione: '',
    nome_cliente: '',
    indirizzo_cantiere: '',
    citta_cantiere: '',
    nazione_cantiere: '',
    password_cantiere: '',
    codice_univoco: ''
  })
  const [viewPasswordLoading, setViewPasswordLoading] = useState(false)

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated) {
      loadCantieri()
    }
  }, [isAuthenticated])

  const loadCantieri = async () => {
    try {
      setLoading(true)
      const data = await cantieriApi.getCantieri()
      setCantieri(data)

      // Carica le statistiche per ogni cantiere
      await loadCantieriStatistics(data)
    } catch (error) {
      setError('Errore nel caricamento dei cantieri')
    } finally {
      setLoading(false)
    }
  }

  const loadCantieriStatistics = async (cantieriData: Cantiere[]) => {
    try {
      setStatsLoading(true)
      const statsPromises = cantieriData.map(async (cantiere) => {
        try {
          const stats = await cantieriApi.getCantiereStatistics(cantiere.id_cantiere)
          return { id: cantiere.id_cantiere, stats }
        } catch (error) {
          console.error(`Errore nel caricamento statistiche cantiere ${cantiere.id_cantiere}:`, error)
          return { id: cantiere.id_cantiere, stats: { percentuale_avanzamento: 0 } }
        }
      })

      const results = await Promise.all(statsPromises)
      const statsMap = results.reduce((acc, { id, stats }) => {
        acc[id] = stats
        return acc
      }, {} as Record<number, { percentuale_avanzamento: number }>)

      setCantieriStats(statsMap)
    } catch (error) {
      console.error('Errore nel caricamento delle statistiche:', error)
    } finally {
      setStatsLoading(false)
    }
  }

  const togglePasswordVisibility = async (cantiere: Cantiere) => {
    const cantiereId = cantiere.id_cantiere
    const isCurrentlyVisible = passwordVisibility[cantiereId]

    if (isCurrentlyVisible) {
      // Nascondi la password
      setPasswordVisibility(prev => ({ ...prev, [cantiereId]: false }))
      setRevealedPasswords(prev => ({ ...prev, [cantiereId]: '' }))
    } else {
      // Mostra la password - carica se non già caricata
      if (!revealedPasswords[cantiereId]) {
        try {
          setViewPasswordLoading(true)
          const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'
          const token = localStorage.getItem('token') || localStorage.getItem('access_token')
          const response = await fetch(`${backendUrl}/api/cantieri/${cantiereId}/view-password`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            }
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.detail || 'Errore nel recupero password')
          }

          const data = await response.json()
          setRevealedPasswords(prev => ({ ...prev, [cantiereId]: data.password_cantiere }))
          setPasswordVisibility(prev => ({ ...prev, [cantiereId]: true }))
        } catch (error) {
          setError(error instanceof Error ? error.message : 'Errore nel recupero password')
        } finally {
          setViewPasswordLoading(false)
        }
      } else {
        // Password già caricata, mostra semplicemente
        setPasswordVisibility(prev => ({ ...prev, [cantiereId]: true }))
      }
    }
  }

  const handleSelectCantiere = (cantiere: Cantiere) => {
    // Usa la funzione selectCantiere dal context per aggiornare tutto il sistema
    selectCantiere(cantiere)

    // Naviga alla pagina del cantiere specifico
    router.push(`/cantieri/${cantiere.id_cantiere}`)
  }

  const openEditDialog = (cantiere: Cantiere) => {
    setSelectedCantiere(cantiere)
    setFormData({
      commessa: cantiere.commessa || '',
      descrizione: cantiere.descrizione || '',
      nome_cliente: cantiere.nome_cliente || '',
      indirizzo_cantiere: cantiere.indirizzo_cantiere || '',
      citta_cantiere: cantiere.citta_cantiere || '',
      nazione_cantiere: cantiere.nazione_cantiere || '',
      password_cantiere: cantiere.password_cantiere || '',
      codice_univoco: cantiere.codice_univoco || ''
    })
    setShowEditDialog(true)
  }

  const handleCreateCantiere = async () => {
    try {
      await cantieriApi.createCantiere(formData)
      setShowCreateDialog(false)
      setFormData({
        commessa: '',
        descrizione: '',
        nome_cliente: '',
        indirizzo_cantiere: '',
        citta_cantiere: '',
        nazione_cantiere: 'Italia',
        password_cantiere: '',
        codice_univoco: ''
      })
      loadCantieri()
    } catch (error) {
      setError('Errore nella creazione del cantiere')
    }
  }

  const handleEditCantiere = async () => {
    if (!selectedCantiere) return

    try {
      await cantieriApi.updateCantiere(selectedCantiere.id_cantiere, formData)
      setShowEditDialog(false)
      setSelectedCantiere(null)
      loadCantieri()
    } catch (error) {
      setError('Errore nella modifica del cantiere')
    }
  }

  const handleChangePassword = async () => {
    if (!selectedCantiere) return

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setError('Le password non coincidono')
      return
    }

    if (!passwordData.currentPassword) {
      setError('Inserisci la password attuale per confermare il cambio')
      return
    }

    if (!passwordData.newPassword || passwordData.newPassword.length < 6) {
      setError('La nuova password deve essere di almeno 6 caratteri')
      return
    }

    try {
      setLoading(true)
      setError('')

      // Chiamata API per cambio password
      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'
      const response = await fetch(`${backendUrl}/api/cantieri/${selectedCantiere.id_cantiere}/change-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        },
        body: JSON.stringify({
          password_attuale: passwordData.currentPassword,
          password_nuova: passwordData.newPassword,
          conferma_password: passwordData.confirmPassword
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || 'Errore nel cambio password')
      }

      const data = await response.json()

      if (data.success) {
        setPasswordData({
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        })
        setShowPasswordDialog(false)
        setError('')
        alert(data.message || 'Password cambiata con successo')
      } else {
        throw new Error(data.message || 'Errore nel cambio password')
      }

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Errore nel cambio password')
    } finally {
      setLoading(false)
    }
  }



  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
    } catch (err) {
      console.error('Failed to copy to clipboard:', err)
    }
  }

  const filteredCantieri = cantieri.filter(cantiere =>
    cantiere.commessa.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cantiere.descrizione?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cantiere.nome_cliente?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="max-w-[90%] mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <div className="relative w-80">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Cerca cantieri..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8 w-full"
            />
          </div>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button className="relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25">
              <Plus className="mr-2 h-4 w-4" />
              Nuovo Cantiere
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Crea Nuovo Cantiere</DialogTitle>
              <DialogDescription>
                Inserisci i dettagli del nuovo cantiere
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="commessa" className="text-right">
                  Commessa *
                </Label>
                <Input
                  id="commessa"
                  value={formData.commessa}
                  onChange={(e) => setFormData({ ...formData, commessa: e.target.value })}
                  className="col-span-3"
                  placeholder="Nome commessa"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="descrizione" className="text-right">
                  Descrizione
                </Label>
                <Input
                  id="descrizione"
                  value={formData.descrizione}
                  onChange={(e) => setFormData({ ...formData, descrizione: e.target.value })}
                  className="col-span-3"
                  placeholder="Descrizione cantiere"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="nome_cliente" className="text-right">
                  Cliente
                </Label>
                <Input
                  id="nome_cliente"
                  value={formData.nome_cliente}
                  onChange={(e) => setFormData({ ...formData, nome_cliente: e.target.value })}
                  className="col-span-3"
                  placeholder="Nome cliente"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="indirizzo_cantiere" className="text-right">
                  Indirizzo
                </Label>
                <Input
                  id="indirizzo_cantiere"
                  value={formData.indirizzo_cantiere}
                  onChange={(e) => setFormData({ ...formData, indirizzo_cantiere: e.target.value })}
                  className="col-span-3"
                  placeholder="Indirizzo cantiere"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="citta_cantiere" className="text-right">
                  Città
                </Label>
                <Input
                  id="citta_cantiere"
                  value={formData.citta_cantiere}
                  onChange={(e) => setFormData({ ...formData, citta_cantiere: e.target.value })}
                  className="col-span-3"
                  placeholder="Città"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="password_cantiere" className="text-right">
                  Password
                </Label>
                <Input
                  id="password_cantiere"
                  type="password"
                  value={formData.password_cantiere}
                  onChange={(e) => setFormData({ ...formData, password_cantiere: e.target.value })}
                  className="col-span-3"
                  placeholder="Password cantiere"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                Annulla
              </Button>
              <Button onClick={handleCreateCantiere} className="relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25">
                Crea Cantiere
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Dialog Modifica Cantiere */}
        <Dialog open={showEditDialog} onOpenChange={(open) => {
          setShowEditDialog(open)
          if (!open) {
            setSelectedCantiere(null)
            setFormData({
              commessa: '',
              descrizione: '',
              nome_cliente: '',
              indirizzo_cantiere: '',
              citta_cantiere: '',
              nazione_cantiere: 'Italia',
              password_cantiere: '',
              codice_univoco: ''
            })
          }
        }}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Edit className="h-5 w-5" />
                Modifica Cantiere - {selectedCantiere?.commessa}
              </DialogTitle>
              <DialogDescription>
                Modifica i dati del cantiere selezionato
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-commessa" className="text-right">
                  Commessa
                </Label>
                <input
                  id="edit-commessa"
                  value={formData.commessa}
                  onChange={(e) => setFormData({ ...formData, commessa: e.target.value })}
                  className="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-descrizione" className="text-right">
                  Descrizione
                </Label>
                <input
                  id="edit-descrizione"
                  value={formData.descrizione}
                  onChange={(e) => setFormData({ ...formData, descrizione: e.target.value })}
                  className="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-cliente" className="text-right">
                  Cliente
                </Label>
                <input
                  id="edit-cliente"
                  value={formData.nome_cliente}
                  onChange={(e) => setFormData({ ...formData, nome_cliente: e.target.value })}
                  className="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-indirizzo" className="text-right">
                  Indirizzo
                </Label>
                <input
                  id="edit-indirizzo"
                  value={formData.indirizzo_cantiere}
                  onChange={(e) => setFormData({ ...formData, indirizzo_cantiere: e.target.value })}
                  className="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-citta" className="text-right">
                  Città
                </Label>
                <input
                  id="edit-citta"
                  value={formData.citta_cantiere}
                  onChange={(e) => setFormData({ ...formData, citta_cantiere: e.target.value })}
                  className="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* Pulsante Cambia Password invece del campo password */}
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">
                  Password
                </Label>
                <div className="col-span-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setShowPasswordDialog(true)
                    }}
                    className="w-full border-orange-200 text-orange-600 hover:bg-orange-50 hover:border-orange-300"
                  >
                    <Key className="mr-2 h-4 w-4" />
                    Cambia Password
                  </Button>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setShowEditDialog(false)}>
                Annulla
              </Button>
              <Button onClick={handleEditCantiere} className="bg-blue-600 hover:bg-blue-700">
                Salva Modifiche
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Dialog Cambio Password */}
        <Dialog open={showPasswordDialog} onOpenChange={(open) => {
          setShowPasswordDialog(open)
          if (!open) {
            setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' })
            setError('')
          }
        }}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Key className="h-5 w-5" />
                Cambia Password - {selectedCantiere?.commessa}
              </DialogTitle>
              <DialogDescription>
                Inserisci la password attuale e la nuova password per il cantiere
              </DialogDescription>
            </DialogHeader>

            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                {error}
              </div>
            )}

            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="current-password" className="text-right">
                  Password Attuale
                </Label>
                <input
                  id="current-password"
                  type="password"
                  value={passwordData.currentPassword}
                  onChange={(e) => setPasswordData({ ...passwordData, currentPassword: e.target.value })}
                  className="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Inserisci password attuale"
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="new-password" className="text-right">
                  Nuova Password
                </Label>
                <input
                  id="new-password"
                  type="password"
                  value={passwordData.newPassword}
                  onChange={(e) => setPasswordData({ ...passwordData, newPassword: e.target.value })}
                  className="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Inserisci nuova password"
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="confirm-password" className="text-right">
                  Conferma Password
                </Label>
                <input
                  id="confirm-password"
                  type="password"
                  value={passwordData.confirmPassword}
                  onChange={(e) => setPasswordData({ ...passwordData, confirmPassword: e.target.value })}
                  className="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Conferma nuova password"
                />
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setShowPasswordDialog(false)}>
                Annulla
              </Button>
              <Button
                onClick={handleChangePassword}
                disabled={loading || !passwordData.currentPassword || !passwordData.newPassword}
                className="bg-orange-600 hover:bg-orange-700"
              >
                {loading ? 'Cambiando...' : 'Cambia Password'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      <Card>
        <Card>
          <Table>
            <TableHeader>
              <TableRow className="border-b border-gray-200">
                <TableHead className="font-semibold text-gray-700">Commessa</TableHead>
                <TableHead className="font-semibold text-gray-700">Descrizione</TableHead>
                <TableHead className="font-semibold text-gray-700">Cliente</TableHead>
                <TableHead className="font-semibold text-gray-700">Data Creazione</TableHead>
                <TableHead className="font-semibold text-gray-700">Codice Accesso</TableHead>
                <TableHead className="font-semibold text-gray-700">Password Cantiere</TableHead>
                <TableHead className="font-semibold text-gray-700 w-32">Avanzamento</TableHead>
                <TableHead className="font-semibold text-gray-700 text-center">Progresso %</TableHead>
                <TableHead className="text-center font-semibold text-gray-700 w-48">Azioni</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCantieri.map((cantiere) => (
                <TableRow key={cantiere.id_cantiere} className="hover:bg-gray-50/50 transition-colors">
                  <TableCell className="font-semibold text-gray-900 py-4">{cantiere.commessa}</TableCell>
                  <TableCell className="text-gray-700 py-4">{cantiere.descrizione}</TableCell>
                  <TableCell className="text-gray-700 py-4">{cantiere.nome_cliente}</TableCell>
                  <TableCell className="text-gray-600 py-4">{new Date(cantiere.data_creazione).toLocaleDateString()}</TableCell>
                  <TableCell className="py-4">
                    <div className="flex items-center gap-2">
                      <code className="text-sm bg-blue-50 text-blue-700 px-3 py-1.5 rounded-md font-mono border border-blue-200">
                        {cantiere.codice_univoco}
                      </code>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-7 w-7 p-0 text-gray-400 hover:bg-gray-50 hover:text-gray-600 transition-colors"
                        title="Copia codice"
                        onClick={() => copyToClipboard(cantiere.codice_univoco)}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                  <TableCell className="py-4">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-2">
                        {passwordVisibility[cantiere.id_cantiere] && revealedPasswords[cantiere.id_cantiere] ? (
                          // Password visibile
                          <div className="flex items-center gap-2">
                            <code className="text-sm bg-green-50 text-green-700 px-2 py-1 rounded border border-green-200 font-mono">
                              {revealedPasswords[cantiere.id_cantiere]}
                            </code>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="h-7 w-7 p-0 text-green-600 hover:bg-green-50 hover:text-green-700 transition-colors"
                              title="Nascondi password"
                              onClick={() => togglePasswordVisibility(cantiere)}
                            >
                              <EyeOff className="h-4 w-4" />
                            </Button>
                          </div>
                        ) : (
                          // Password nascosta
                          <div className="flex items-center gap-2">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              <span className="text-sm text-gray-600 font-medium">Configurata</span>
                            </div>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="h-7 w-7 p-0 text-blue-600 hover:bg-blue-50 hover:text-blue-700 transition-colors"
                              title="Mostra password"
                              onClick={() => togglePasswordVisibility(cantiere)}
                              disabled={viewPasswordLoading}
                            >
                              {viewPasswordLoading ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <Eye className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="py-4">
                    <div className="flex items-center gap-2">
                      {statsLoading ? (
                        <div className="flex items-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                          <span className="text-sm text-gray-500">Caricamento...</span>
                        </div>
                      ) : (
                        <>
                          {/* Barra di Progresso Migliorata */}
                          <div className="flex-1 min-w-[120px]">
                            <div className="w-full bg-gray-200 rounded-full h-3 shadow-inner">
                              <div
                                className={`h-3 rounded-full transition-all duration-500 ease-out shadow-sm ${
                                  (cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0) >= 90
                                    ? 'bg-gradient-to-r from-green-500 to-green-600'
                                    : (cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0) >= 75
                                    ? 'bg-gradient-to-r from-blue-500 to-blue-600'
                                    : (cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0) >= 50
                                    ? 'bg-gradient-to-r from-yellow-500 to-yellow-600'
                                    : (cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0) >= 25
                                    ? 'bg-gradient-to-r from-orange-500 to-orange-600'
                                    : 'bg-gradient-to-r from-red-500 to-red-600'
                                }`}
                                style={{
                                  width: `${Math.min(cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0, 100)}%`
                                }}
                              ></div>
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="py-4 text-center">
                    {statsLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin text-gray-400 mx-auto" />
                    ) : (
                      <div className="flex items-center justify-center gap-1">
                        <BarChart3 className="h-4 w-4 text-gray-500" />
                        <span className={`text-sm font-semibold ${
                          (cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0) >= 90
                            ? 'text-green-700'
                            : (cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0) >= 75
                            ? 'text-blue-700'
                            : (cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0) >= 50
                            ? 'text-yellow-700'
                            : (cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0) >= 25
                            ? 'text-orange-700'
                            : 'text-red-700'
                        }`}>
                          {(cantieriStats[cantiere.id_cantiere]?.percentuale_avanzamento || 0).toFixed(1)}%
                        </span>
                      </div>
                    )}
                  </TableCell>
                  <TableCell className="text-center py-4">
                    <div className="flex items-center justify-center gap-2">
                      {/* Pulsante Modifica Cantiere */}
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => openEditDialog(cantiere)}
                        className="h-9 px-3 text-gray-600 border-gray-200 hover:bg-gray-50 hover:text-gray-700 hover:border-gray-300 transition-all duration-200 ease-in-out"
                        title="Modifica dati cantiere"
                      >
                        <Edit className="h-4 w-4 mr-1.5" />
                        Modifica
                      </Button>

                      {/* Pulsante Principale - Accedi al Cantiere */}
                      <Button
                        size="sm"
                        onClick={() => handleSelectCantiere(cantiere)}
                        className="h-9 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium transition-colors duration-200 ease-in-out"
                        title="Accedi al cantiere"
                      >
                        <ArrowRight className="h-4 w-4 mr-1.5" />
                        Accedi
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Card>
      </Card>
    </div>
  )
}
