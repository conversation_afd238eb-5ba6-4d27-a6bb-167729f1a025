"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9121],{5845:(e,t,n)=>{n.d(t,{i:()=>l});var r,u=n(12115),o=n(52712),i=(r||(r=n.t(u,2)))[" useInsertionEffect ".trim().toString()]||o.N;function l({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,l,s]=function({defaultProp:e,onChange:t}){let[n,r]=u.useState(e),o=u.useRef(n),l=u.useRef(t);return i(()=>{l.current=t},[t]),u.useEffect(()=>{o.current!==n&&(l.current?.(n),o.current=n)},[n,o]),[n,r,l]}({defaultProp:t,onChange:n}),a=void 0!==e,d=a?e:o;{let t=u.useRef(void 0!==e);u.useEffect(()=>{let e=t.current;if(e!==a){let t=a?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=a},[a,r])}return[d,u.useCallback(t=>{if(a){let n="function"==typeof t?t(e):t;n!==e&&s.current?.(n)}else l(t)},[a,e,l,s])]}Symbol("RADIX:SYNC_STATE")},19178:(e,t,n)=>{n.d(t,{qW:()=>f});var r,u=n(12115),o=n(85185),i=n(63655),l=n(6101),s=n(39033),a=n(95155),d="dismissableLayer.update",c=u.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=u.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:p=!1,onEscapeKeyDown:E,onPointerDownOutside:y,onFocusOutside:b,onInteractOutside:h,onDismiss:N,...w}=e,g=u.useContext(c),[O,C]=u.useState(null),P=null!=(f=null==O?void 0:O.ownerDocument)?f:null==(n=globalThis)?void 0:n.document,[,T]=u.useState({}),L=(0,l.s)(t,e=>C(e)),D=Array.from(g.layers),[M]=[...g.layersWithOutsidePointerEventsDisabled].slice(-1),S=D.indexOf(M),R=O?D.indexOf(O):-1,x=g.layersWithOutsidePointerEventsDisabled.size>0,A=R>=S,k=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,s.c)(e),o=u.useRef(!1),i=u.useRef(()=>{});return u.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){v("dismissableLayer.pointerDownOutside",r,u,{discrete:!0})},u={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...g.branches].some(e=>e.contains(t));A&&!n&&(null==y||y(e),null==h||h(e),e.defaultPrevented||null==N||N())},P),_=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,s.c)(e),o=u.useRef(!1);return u.useEffect(()=>{let e=e=>{e.target&&!o.current&&v("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...g.branches].some(e=>e.contains(t))&&(null==b||b(e),null==h||h(e),e.defaultPrevented||null==N||N())},P);return!function(e,t=globalThis?.document){let n=(0,s.c)(e);u.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{R===g.layers.size-1&&(null==E||E(e),!e.defaultPrevented&&N&&(e.preventDefault(),N()))},P),u.useEffect(()=>{if(O)return p&&(0===g.layersWithOutsidePointerEventsDisabled.size&&(r=P.body.style.pointerEvents,P.body.style.pointerEvents="none"),g.layersWithOutsidePointerEventsDisabled.add(O)),g.layers.add(O),m(),()=>{p&&1===g.layersWithOutsidePointerEventsDisabled.size&&(P.body.style.pointerEvents=r)}},[O,P,p,g]),u.useEffect(()=>()=>{O&&(g.layers.delete(O),g.layersWithOutsidePointerEventsDisabled.delete(O),m())},[O,g]),u.useEffect(()=>{let e=()=>T({});return document.addEventListener(d,e),()=>document.removeEventListener(d,e)},[]),(0,a.jsx)(i.sG.div,{...w,ref:L,style:{pointerEvents:x?A?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.m)(e.onFocusCapture,_.onFocusCapture),onBlurCapture:(0,o.m)(e.onBlurCapture,_.onBlurCapture),onPointerDownCapture:(0,o.m)(e.onPointerDownCapture,k.onPointerDownCapture)})});function m(){let e=new CustomEvent(d);document.dispatchEvent(e)}function v(e,t,n,r){let{discrete:u}=r,o=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),u?(0,i.hO)(o,l):o.dispatchEvent(l)}f.displayName="DismissableLayer",u.forwardRef((e,t)=>{let n=u.useContext(c),r=u.useRef(null),o=(0,l.s)(t,r);return u.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,a.jsx)(i.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch"},28905:(e,t,n)=>{n.d(t,{C:()=>i});var r=n(12115),u=n(6101),o=n(52712),i=e=>{let{present:t,children:n}=e,i=function(e){var t,n;let[u,i]=r.useState(),s=r.useRef(null),a=r.useRef(e),d=r.useRef("none"),[c,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=l(s.current);d.current="mounted"===c?e:"none"},[c]),(0,o.N)(()=>{let t=s.current,n=a.current;if(n!==e){let r=d.current,u=l(t);e?f("MOUNT"):"none"===u||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==u?f("ANIMATION_OUT"):f("UNMOUNT"),a.current=e}},[e,f]),(0,o.N)(()=>{if(u){var e;let t,n=null!=(e=u.ownerDocument.defaultView)?e:window,r=e=>{let r=l(s.current).includes(e.animationName);if(e.target===u&&r&&(f("ANIMATION_END"),!a.current)){let e=u.style.animationFillMode;u.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===u.style.animationFillMode&&(u.style.animationFillMode=e)})}},o=e=>{e.target===u&&(d.current=l(s.current))};return u.addEventListener("animationstart",o),u.addEventListener("animationcancel",r),u.addEventListener("animationend",r),()=>{n.clearTimeout(t),u.removeEventListener("animationstart",o),u.removeEventListener("animationcancel",r),u.removeEventListener("animationend",r)}}f("ANIMATION_END")},[u,f]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{s.current=e?getComputedStyle(e):null,i(e)},[])}}(t),s="function"==typeof n?n({present:i.isPresent}):r.Children.only(n),a=(0,u.s)(i.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,u=r&&"isReactWarning"in r&&r.isReactWarning;return u?e.ref:(u=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s));return"function"==typeof n||i.isPresent?r.cloneElement(s,{ref:a}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},34378:(e,t,n)=>{n.d(t,{Z:()=>s});var r=n(12115),u=n(47650),o=n(63655),i=n(52712),l=n(95155),s=r.forwardRef((e,t)=>{var n,s;let{container:a,...d}=e,[c,f]=r.useState(!1);(0,i.N)(()=>f(!0),[]);let m=a||c&&(null==(s=globalThis)||null==(n=s.document)?void 0:n.body);return m?u.createPortal((0,l.jsx)(o.sG.div,{...d,ref:t}),m):null});s.displayName="Portal"},39033:(e,t,n)=>{n.d(t,{c:()=>u});var r=n(12115);function u(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},46081:(e,t,n)=>{n.d(t,{A:()=>i,q:()=>o});var r=n(12115),u=n(95155);function o(e,t){let n=r.createContext(t),o=e=>{let{children:t,...o}=e,i=r.useMemo(()=>o,Object.values(o));return(0,u.jsx)(n.Provider,{value:i,children:t})};return o.displayName=e+"Provider",[o,function(u){let o=r.useContext(n);if(o)return o;if(void 0!==t)return t;throw Error(`\`${u}\` must be used within \`${e}\``)}]}function i(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let u=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:u}}),[n,u])}};return o.scopeName=e,[function(t,o){let i=r.createContext(o),l=n.length;n=[...n,o];let s=t=>{let{scope:n,children:o,...s}=t,a=n?.[e]?.[l]||i,d=r.useMemo(()=>s,Object.values(s));return(0,u.jsx)(a.Provider,{value:d,children:o})};return s.displayName=t+"Provider",[s,function(n,u){let s=u?.[e]?.[l]||i,a=r.useContext(s);if(a)return a;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let u=n.reduce((t,{useScope:n,scopeName:r})=>{let u=n(e)[`__scope${r}`];return{...t,...u}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:u}),[u])}};return n.scopeName=t.scopeName,n}(o,...t)]}},51154:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},52712:(e,t,n)=>{n.d(t,{N:()=>u});var r=n(12115),u=globalThis?.document?r.useLayoutEffect:()=>{}},54416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},61285:(e,t,n)=>{n.d(t,{B:()=>s});var r,u=n(12115),o=n(52712),i=(r||(r=n.t(u,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function s(e){let[t,n]=u.useState(i());return(0,o.N)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},63655:(e,t,n)=>{n.d(t,{hO:()=>s,sG:()=>l});var r=n(12115),u=n(47650),o=n(99708),i=n(95155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,o.TL)(`Primitive.${t}`),u=r.forwardRef((e,r)=>{let{asChild:u,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(u?n:t,{...o,ref:r})});return u.displayName=`Primitive.${t}`,{...e,[t]:u}},{});function s(e,t){e&&u.flushSync(()=>e.dispatchEvent(t))}},85185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}}}]);