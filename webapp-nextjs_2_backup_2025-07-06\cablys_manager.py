#!/usr/bin/env python
# cablys_manager.py - Manager per sistema CABLYS
import sys
import os
import subprocess
from pathlib import Path

class CablysManager:
    def __init__(self):
        self.base_dir = Path(__file__).resolve().parent
        
    def print_banner(self):
        """Stampa il banner principale"""
        print("\n" + "="*70)
        print("🚀 CABLYS Next.js Webapp Manager")
        print("="*70)
        print("Scegli il metodo di avvio:")
        print()
        print("1. 🏃 Run System Semplice (run_system.py)")
        print("2. ⚡ Run System Avanzato (run_system_advanced.py)")
        print("3. 🏭 Ambiente Produzione (run_production.py)")
        print("4. 🔍 Monitor Sistema (monitor_system.py)")
        print("5. 📊 Status Check")
        print("6. 🛠️  Configurazione")
        print("7. ❌ Esci")
        print("="*70)
    
    def run_simple(self):
        """Avvia il run system semplice"""
        print("\n🏃 Avvio Run System Semplice...")
        try:
            subprocess.run([sys.executable, "run_system.py"], cwd=self.base_dir)
        except KeyboardInterrupt:
            print("\n👋 Run system interrotto")
    
    def run_advanced(self):
        """Avvia il run system avanzato"""
        print("\n⚡ Avvio Run System Avanzato...")
        try:
            subprocess.run([sys.executable, "run_system_advanced.py"], cwd=self.base_dir)
        except KeyboardInterrupt:
            print("\n👋 Run system interrotto")
    
    def run_production(self):
        """Avvia l'ambiente di produzione"""
        print("\n🏭 Avvio Ambiente Produzione...")
        try:
            subprocess.run([sys.executable, "run_production.py"], cwd=self.base_dir)
        except KeyboardInterrupt:
            print("\n👋 Ambiente produzione interrotto")
    
    def run_monitor(self):
        """Avvia il monitor del sistema"""
        print("\n🔍 Avvio Monitor Sistema...")
        print("Opzioni monitor:")
        print("1. Check singolo")
        print("2. Monitoraggio continuo")
        
        choice = input("\nScegli (1-2): ").strip()
        
        if choice == "1":
            subprocess.run([sys.executable, "monitor_system.py"], cwd=self.base_dir)
        elif choice == "2":
            try:
                subprocess.run([sys.executable, "monitor_system.py", "--continuous"], cwd=self.base_dir)
            except KeyboardInterrupt:
                print("\n👋 Monitoraggio interrotto")
        else:
            print("❌ Scelta non valida")
    
    def status_check(self):
        """Verifica rapida dello stato"""
        print("\n📊 Status Check Rapido...")
        subprocess.run([sys.executable, "monitor_system.py"], cwd=self.base_dir)
        input("\nPremi Enter per continuare...")
    
    def show_configuration(self):
        """Mostra le configurazioni disponibili"""
        print("\n🛠️  Configurazioni Disponibili:")
        print("="*50)
        
        configs = [
            ("run_config.json", "Configurazione sviluppo"),
            ("run_config_production.json", "Configurazione produzione"),
            (".env.local", "Variabili ambiente Next.js")
        ]
        
        for config_file, description in configs:
            config_path = self.base_dir / config_file
            status = "✅ Presente" if config_path.exists() else "❌ Mancante"
            print(f"{status} {config_file} - {description}")
        
        print("\n📁 File di avvio disponibili:")
        scripts = [
            ("run_system.py", "Run system semplice"),
            ("run_system_advanced.py", "Run system avanzato"),
            ("run_production.py", "Ambiente produzione"),
            ("monitor_system.py", "Monitor sistema"),
            ("START_CABLYS.bat", "Launcher Windows semplice"),
            ("START_CABLYS_ADVANCED.bat", "Launcher Windows avanzato")
        ]
        
        for script_file, description in scripts:
            script_path = self.base_dir / script_file
            status = "✅" if script_path.exists() else "❌"
            print(f"{status} {script_file} - {description}")
        
        input("\nPremi Enter per continuare...")
    
    def run(self):
        """Loop principale del manager"""
        while True:
            try:
                self.print_banner()
                choice = input("\nScegli un'opzione (1-7): ").strip()
                
                if choice == "1":
                    self.run_simple()
                elif choice == "2":
                    self.run_advanced()
                elif choice == "3":
                    self.run_production()
                elif choice == "4":
                    self.run_monitor()
                elif choice == "5":
                    self.status_check()
                elif choice == "6":
                    self.show_configuration()
                elif choice == "7":
                    print("\n👋 Arrivederci!")
                    break
                else:
                    print("\n❌ Scelta non valida. Riprova.")
                    input("Premi Enter per continuare...")
                    
            except KeyboardInterrupt:
                print("\n\n👋 Manager interrotto. Arrivederci!")
                break
            except Exception as e:
                print(f"\n❌ Errore: {e}")
                input("Premi Enter per continuare...")

def main():
    """Punto di ingresso principale"""
    manager = CablysManager()
    manager.run()

if __name__ == "__main__":
    main()
