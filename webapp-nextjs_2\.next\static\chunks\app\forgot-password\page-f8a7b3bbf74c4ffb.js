(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2162],{24944:(e,r,s)=>{"use strict";s.d(r,{k:()=>l});var t=s(95155);s(12115);var a=s(55863),i=s(59434);function l(e){let{className:r,value:s,...l}=e;return(0,t.jsx)(a.bL,{"data-slot":"progress",className:(0,i.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",r),...l,children:(0,t.jsx)(a.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(s||0),"%)")}})})}},28883:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},30285:(e,r,s)=>{"use strict";s.d(r,{$:()=>d,r:()=>n});var t=s(95155);s(12115);var a=s(99708),i=s(74466),l=s(59434);let n=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:r,variant:s,size:i,asChild:d=!1,...c}=e,o=d?a.DX:"button";return(0,t.jsx)(o,{"data-slot":"button",className:(0,l.cn)(n({variant:s,size:i,className:r})),...c})}},55365:(e,r,s)=>{"use strict";s.d(r,{Fc:()=>d,TN:()=>c});var t=s(95155),a=s(12115),i=s(74466),l=s(59434);let n=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),d=a.forwardRef((e,r)=>{let{className:s,variant:a,...i}=e;return(0,t.jsx)("div",{ref:r,role:"alert",className:(0,l.cn)(n({variant:a}),s),...i})});d.displayName="Alert",a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("h5",{ref:r,className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",s),...a})}).displayName="AlertTitle";let c=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("text-sm [&_p]:leading-relaxed",s),...a})});c.displayName="AlertDescription"},59434:(e,r,s)=>{"use strict";s.d(r,{cn:()=>i});var t=s(52596),a=s(39688);function i(){for(var e=arguments.length,r=Array(e),s=0;s<e;s++)r[s]=arguments[s];return(0,a.QP)((0,t.$)(r))}},62523:(e,r,s)=>{"use strict";s.d(r,{p:()=>i});var t=s(95155);s(12115);var a=s(59434);function i(e){let{className:r,type:s,...i}=e;return(0,t.jsx)("input",{type:s,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...i})}},63438:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>j});var t=s(95155),a=s(12115),i=s(6874),l=s.n(i),n=s(3493),d=s(35169),c=s(30285),o=s(62523),u=s(85057),x=s(66695),m=s(55365);s(24944);var p=s(28883),v=s(40646),h=s(1243),g=s(75525),f=s(59434);function b(){let[e,r]=(0,a.useState)({email:"",userType:"user"}),[s,i]=(0,a.useState)(!1),[l,n]=(0,a.useState)(null),d=async s=>{s.preventDefault(),i(!0),n(null);try{if(!e.email)throw Error("L'indirizzo email \xe8 obbligatorio");let s=await fetch("/api/password/request-password-reset",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e.email,user_type:e.userType})}),t=await s.json();if(s.ok&&t.success)n({type:"success",text:"Se l'email \xe8 registrata, riceverai le istruzioni per il reset della password."}),r({email:"",userType:"user"});else throw Error(t.detail||t.message||"Errore durante la richiesta di reset")}catch(e){n({type:"error",text:e instanceof Error?e.message:"Errore durante la richiesta di reset"})}finally{i(!1)}};return(0,t.jsxs)(x.Zp,{className:"w-full max-w-md mx-auto",children:[(0,t.jsxs)(x.aR,{className:"space-y-1",children:[(0,t.jsxs)(x.ZB,{className:"text-2xl flex items-center gap-2",children:[(0,t.jsx)(p.A,{className:"h-5 w-5 text-mariner-600"}),"Recupera Password"]}),(0,t.jsx)(x.BT,{children:"Inserisci la tua email per ricevere le istruzioni di reset"})]}),(0,t.jsxs)(x.Wu,{children:[(0,t.jsxs)("form",{onSubmit:d,className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{children:"Tipo di Account"}),(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,t.jsx)("input",{type:"radio",value:"user",checked:"user"===e.userType,onChange:e=>r(r=>({...r,userType:e.target.value})),className:"text-mariner-600"}),(0,t.jsx)("span",{children:"Utente"})]}),(0,t.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,t.jsx)("input",{type:"radio",value:"cantiere",checked:"cantiere"===e.userType,onChange:e=>r(r=>({...r,userType:e.target.value})),className:"text-mariner-600"}),(0,t.jsx)("span",{children:"Cantiere"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"email",children:"Indirizzo Email"}),(0,t.jsx)(o.p,{id:"email",type:"email",value:e.email,onChange:e=>r(r=>({...r,email:e.target.value})),placeholder:"<EMAIL>",required:!0})]}),l&&(0,t.jsxs)(m.Fc,{className:(0,f.cn)("success"===l.type?"border-green-200 bg-green-50":"border-red-200 bg-red-50"),children:["success"===l.type?(0,t.jsx)(v.A,{className:"h-4 w-4 text-green-600"}):(0,t.jsx)(h.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)(m.TN,{className:(0,f.cn)("success"===l.type?"text-green-800":"text-red-800"),children:l.text})]}),(0,t.jsx)(c.$,{type:"submit",className:"w-full bg-mariner-600 hover:bg-mariner-700",disabled:s,children:s?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Invio in corso..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Invia Link di Reset"]})})]}),(0,t.jsx)("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-start gap-2",children:[(0,t.jsx)(g.A,{className:"h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,t.jsx)("p",{className:"font-medium mb-1",children:"Informazioni sulla Sicurezza"}),(0,t.jsxs)("ul",{className:"space-y-1 text-xs",children:[(0,t.jsx)("li",{children:"• Il link di reset \xe8 valido per 30 minuti"}),(0,t.jsx)("li",{children:"• Pu\xf2 essere utilizzato una sola volta"}),(0,t.jsx)("li",{children:"• Se non ricevi l'email, controlla la cartella spam"}),(0,t.jsx)("li",{children:"• Per motivi di sicurezza, non riveleremo se l'email \xe8 registrata"})]})]})]})})]})]})}function j(){return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-slate-100 flex items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"w-full max-w-md space-y-6",children:[(0,t.jsxs)("div",{className:"text-center space-y-2",children:[(0,t.jsx)("div",{className:"flex justify-center",children:(0,t.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center",children:(0,t.jsx)(n.A,{className:"w-8 h-8 text-white"})})}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-slate-900",children:"CABLYS"}),(0,t.jsx)("p",{className:"text-slate-600",children:"Recupero Password"})]}),(0,t.jsx)(b,{}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)(l(),{href:"/login",children:(0,t.jsxs)(c.$,{variant:"ghost",className:"text-slate-600 hover:text-slate-900",children:[(0,t.jsx)(d.A,{className:"w-4 h-4 mr-2"}),"Torna al Login"]})})})]})})}},66695:(e,r,s)=>{"use strict";s.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>n,Zp:()=>i,aR:()=>l});var t=s(95155);s(12115);var a=s(59434);function i(e){let{className:r,...s}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...s})}function l(e){let{className:r,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...s})}function n(e){let{className:r,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",r),...s})}function d(e){let{className:r,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",r),...s})}function c(e){let{className:r,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",r),...s})}},85057:(e,r,s)=>{"use strict";s.d(r,{J:()=>l});var t=s(95155);s(12115);var a=s(40968),i=s(59434);function l(e){let{className:r,...s}=e;return(0,t.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...s})}},88418:(e,r,s)=>{Promise.resolve().then(s.bind(s,63438))}},e=>{var r=r=>e(e.s=r);e.O(0,[3455,416,8441,1684,7358],()=>r(88418)),_N_E=e.O()}]);