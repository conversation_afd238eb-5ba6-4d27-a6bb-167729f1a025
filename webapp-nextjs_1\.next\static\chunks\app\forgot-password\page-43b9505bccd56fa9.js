(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2162],{3493:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("cable",[["path",{d:"M17 21v-2a1 1 0 0 1-1-1v-1a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v1a1 1 0 0 1-1 1",key:"10bnsj"}],["path",{d:"M19 15V6.5a1 1 0 0 0-7 0v11a1 1 0 0 1-7 0V9",key:"1eqmu1"}],["path",{d:"M21 21v-2h-4",key:"14zm7j"}],["path",{d:"M3 5h4V3",key:"z442eg"}],["path",{d:"M7 5a1 1 0 0 1 1 1v1a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a1 1 0 0 1 1-1V3",key:"ebdjd7"}]])},65765:(e,s,a)=>{Promise.resolve().then(a.bind(a,69028))},69028:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>h});var t=a(95155);a(12115);var l=a(6874),r=a.n(l),c=a(3493),n=a(35169),d=a(30285),i=a(59471);function h(){return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-slate-100 flex items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"w-full max-w-md space-y-6",children:[(0,t.jsxs)("div",{className:"text-center space-y-2",children:[(0,t.jsx)("div",{className:"flex justify-center",children:(0,t.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center",children:(0,t.jsx)(c.A,{className:"w-8 h-8 text-white"})})}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-slate-900",children:"CABLYS"}),(0,t.jsx)("p",{className:"text-slate-600",children:"Recupero Password"})]}),(0,t.jsx)(i.r,{}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)(r(),{href:"/login",children:(0,t.jsxs)(d.$,{variant:"ghost",className:"text-slate-600 hover:text-slate-900",children:[(0,t.jsx)(n.A,{className:"w-4 h-4 mr-2"}),"Torna al Login"]})})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[3455,2600,6127,9471,8441,1684,7358],()=>s(65765)),_N_E=e.O()}]);