(()=>{var e={};e.id=2026,e.ids=[2026],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},61658:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>u,serverHooks:()=>h,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>d});var o={};r.r(o),r.d(o,{DELETE:()=>l,POST:()=>c});var a=r(96559),s=r(48088),n=r(37719),i=r(32190);async function c(e,{params:t}){try{let{cantiereId:r,cavoId:o}=t,a=await e.json();if(!r||!o)return i.NextResponse.json({error:"Parametri cantiere ID e cavo ID richiesti"},{status:400});if(!a.lato||!["partenza","arrivo"].includes(a.lato))return i.NextResponse.json({error:'Lato richiesto: "partenza" o "arrivo"'},{status:400});let s=e.headers.get("authorization");if(!s)return i.NextResponse.json({error:"Token di autenticazione richiesto"},{status:401});let n=process.env.BACKEND_URL||"http://localhost:8000",c=await fetch(`${n}/cavi/${r}/${o}/collegamento`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:s},body:JSON.stringify({lato:a.lato,responsabile:a.responsabile||"cantiere"})});if(!c.ok){let e=await c.json().catch(()=>({}));return i.NextResponse.json({error:e.detail||`Errore backend: ${c.status}`,detail:e.detail},{status:c.status})}let l=await c.json();return i.NextResponse.json(l)}catch(e){return console.error("Errore nel collegamento cavo:",e),i.NextResponse.json({error:"Errore interno del server",detail:e.message},{status:500})}}async function l(e,{params:t}){try{let{cantiereId:r,cavoId:o}=t,a={};try{a=await e.json()}catch{a={}}if(!r||!o)return i.NextResponse.json({error:"Parametri cantiere ID e cavo ID richiesti"},{status:400});let s=e.headers.get("authorization");if(!s)return i.NextResponse.json({error:"Token di autenticazione richiesto"},{status:401});let n=process.env.BACKEND_URL||"http://localhost:8000";if(a.lato){let e=`${n}/cavi/${r}/${o}/collegamento/${a.lato}`,t=await fetch(e,{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:s}});if(!t.ok){let e=await t.json().catch(()=>({}));return i.NextResponse.json({error:e.detail||`Errore backend: ${t.status}`,detail:e.detail},{status:t.status})}let c=await t.json();return i.NextResponse.json(c)}{let e=null;try{let t=`${n}/cavi/${r}/${o}/collegamento/partenza`,a=await fetch(t,{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:s}});a.ok&&(e=await a.json())}catch(e){}try{let t=`${n}/cavi/${r}/${o}/collegamento/arrivo`,a=await fetch(t,{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:s}});a.ok&&(e=await a.json())}catch(e){}if(e)return i.NextResponse.json(e);return i.NextResponse.json({error:"Nessun collegamento da scollegare"},{status:400})}}catch(e){return console.error("Errore nello scollegamento cavo:",e),i.NextResponse.json({error:"Errore interno del server",detail:e.message},{status:500})}}let u=new a.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/cavi/[cantiereId]/[cavoId]/collegamento/route",pathname:"/api/cavi/[cantiereId]/[cavoId]/collegamento",filename:"route",bundlePath:"app/api/cavi/[cantiereId]/[cavoId]/collegamento/route"},resolvedPagePath:"C:\\CMS\\webapp-nextjs_1\\src\\app\\api\\cavi\\[cantiereId]\\[cavoId]\\collegamento\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:p,workUnitAsyncStorage:d,serverHooks:h}=u;function v(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:d})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[4447,580],()=>r(61658));module.exports=o})();