# Run System per CABLYS Next.js Webapp

Questo sistema di avvio automatizza l'esecuzione simultanea del backend FastAPI e del frontend Next.js per l'applicazione CABLYS.

## Prerequisiti

Prima di utilizzare il run system, assicurati di avere installato:

1. **Python 3.8+** con uvicorn
2. **Node.js 18+** con npm
3. **PostgreSQL** con database configurato
4. **Dipendenze Python** installate nella directory `../webapp`
5. **Dipendenze Node.js** installate in questa directory

## Installazione Dipendenze

### Backend (FastAPI)
```bash
cd ../webapp
pip install -r requirements.txt
```

### Frontend (Next.js)
```bash
cd webapp-nextjs
npm install
```

## Utilizzo

### Windows
Doppio click su `run_system.bat` oppure:
```cmd
run_system.bat
```

### Linux/macOS
```bash
./run_system.sh
```

### Manuale (qualsiasi sistema)
```bash
python run_system.py
```

## Cosa fa il Run System

1. **Verifica dipendenze**: Controlla che Python, Node.js, npm e uvicorn siano installati
2. **Avvia il backend**: Lancia il server FastAPI sulla porta 8001 con auto-reload
3. **Avvia il frontend**: Lancia il server Next.js sulla porta 3000 in modalità sviluppo
4. **Monitoraggio**: Mantiene entrambi i servizi attivi
5. **Terminazione pulita**: Termina entrambi i servizi con Ctrl+C

## Porte utilizzate

- **Backend API**: http://localhost:8001
- **Frontend Next.js**: http://localhost:3000

## Configurazione

Il sistema utilizza le seguenti configurazioni:

### Backend
- Porta: 8001
- Host: 0.0.0.0
- Auto-reload: Abilitato
- Directory di lavoro: `../webapp`

### Frontend
- Porta: 3000 (default Next.js)
- Modalità: Sviluppo con Turbopack
- Directory di lavoro: `webapp-nextjs`

## Risoluzione Problemi

### Errore "Backend non trovato"
- Verifica che la directory `../webapp/backend` esista
- Controlla che uvicorn sia installato: `pip install uvicorn`

### Errore "node_modules non trovato"
- Esegui `npm install` nella directory webapp-nextjs

### Errore di porta occupata
- Verifica che le porte 8001 e 3000 non siano già in uso
- Termina eventuali processi esistenti

### Errore di connessione database
- Verifica che PostgreSQL sia in esecuzione
- Controlla le credenziali nel file di configurazione del backend

## Sviluppo

Il run system è configurato per lo sviluppo con:
- Auto-reload del backend quando i file Python cambiano
- Hot-reload del frontend quando i file Next.js cambiano
- Logging dettagliato per il debugging

## Note

- Il sistema condivide lo stesso backend FastAPI con la webapp React originale
- Entrambe le applicazioni possono funzionare simultaneamente su porte diverse
- Il database PostgreSQL è condiviso tra tutte le applicazioni
