"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9471],{28883:(e,s,r)=>{r.d(s,{A:()=>a});let a=(0,r(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},59471:(e,s,r)=>{r.d(s,{G:()=>f,r:()=>y});var a=r(95155),t=r(12115),i=r(30285),l=r(62523),n=r(85057),c=r(66695),o=r(55365),d=r(24944),m=r(32919),x=r(78749),h=r(92657),u=r(40646),p=r(54861),w=r(1243),j=r(75525),g=r(28883),N=r(59434);function f(){var e;let[s,r]=(0,t.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[g,f]=(0,t.useState)({current:!1,new:!1,confirm:!1}),[y,b]=(0,t.useState)({score:0,feedback:[],isValid:!1}),[v,P]=(0,t.useState)(!1),[k,A]=(0,t.useState)(null),z=async e=>{if(!e)return void b({score:0,feedback:[],isValid:!1});try{let s=await fetch("/api/password/validate-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({password:e})});if(s.ok){let e=await s.json();b({score:e.strength_score,feedback:e.suggestions||[],isValid:e.is_valid})}}catch(e){}},S=(e,s)=>{r(r=>({...r,[e]:s})),"newPassword"===e&&z(s)},T=async e=>{e.preventDefault(),P(!0),A(null);try{if(!s.currentPassword||!s.newPassword||!s.confirmPassword)throw Error("Tutti i campi sono obbligatori");if(s.newPassword!==s.confirmPassword)throw Error("Le nuove password non corrispondono");if(!y.isValid)throw Error("La nuova password non rispetta i requisiti di sicurezza");let e=await fetch("/api/password/change-password",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))},body:JSON.stringify({current_password:s.currentPassword,new_password:s.newPassword,confirm_password:s.confirmPassword})}),a=await e.json();if(e.ok&&a.success)A({type:"success",text:a.message}),r({currentPassword:"",newPassword:"",confirmPassword:""}),b({score:0,feedback:[],isValid:!1});else throw Error(a.detail||a.message||"Errore durante il cambio password")}catch(e){A({type:"error",text:e instanceof Error?e.message:"Errore durante il cambio password"})}finally{P(!1)}},C=e=>{f(s=>({...s,[e]:!s[e]}))};return(0,a.jsxs)(c.Zp,{className:"w-full max-w-md mx-auto",children:[(0,a.jsxs)(c.aR,{className:"space-y-1",children:[(0,a.jsxs)(c.ZB,{className:"text-2xl flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-5 w-5 text-mariner-600"}),"Cambia Password"]}),(0,a.jsx)(c.BT,{children:"Aggiorna la tua password per mantenere l'account sicuro"})]}),(0,a.jsx)(c.Wu,{children:(0,a.jsxs)("form",{onSubmit:T,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(n.J,{htmlFor:"currentPassword",children:"Password Attuale"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(l.p,{id:"currentPassword",type:g.current?"text":"password",value:s.currentPassword,onChange:e=>S("currentPassword",e.target.value),className:"pr-10",required:!0}),(0,a.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>C("current"),children:g.current?(0,a.jsx)(x.A,{className:"h-4 w-4 text-gray-400"}):(0,a.jsx)(h.A,{className:"h-4 w-4 text-gray-400"})})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(n.J,{htmlFor:"newPassword",children:"Nuova Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(l.p,{id:"newPassword",type:g.new?"text":"password",value:s.newPassword,onChange:e=>S("newPassword",e.target.value),className:"pr-10",required:!0}),(0,a.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>C("new"),children:g.new?(0,a.jsx)(x.A,{className:"h-4 w-4 text-gray-400"}):(0,a.jsx)(h.A,{className:"h-4 w-4 text-gray-400"})})]}),s.newPassword&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{children:"Forza password:"}),(0,a.jsx)("span",{className:(0,N.cn)("font-medium",y.score<2?"text-red-600":y.score<4?"text-yellow-600":"text-green-600"),children:(e=y.score)<2?"Debole":e<4?"Media":"Forte"})]}),(0,a.jsx)(d.k,{value:y.score/5*100,className:"h-2"}),y.feedback.length>0&&(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"Suggerimenti:"}),(0,a.jsx)("ul",{className:"list-disc list-inside space-y-1",children:y.feedback.map((e,s)=>(0,a.jsx)("li",{children:e},s))})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(n.J,{htmlFor:"confirmPassword",children:"Conferma Nuova Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(l.p,{id:"confirmPassword",type:g.confirm?"text":"password",value:s.confirmPassword,onChange:e=>S("confirmPassword",e.target.value),className:"pr-10",required:!0}),(0,a.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>C("confirm"),children:g.confirm?(0,a.jsx)(x.A,{className:"h-4 w-4 text-gray-400"}):(0,a.jsx)(h.A,{className:"h-4 w-4 text-gray-400"})})]}),s.confirmPassword&&(0,a.jsx)("div",{className:"flex items-center gap-2 text-sm",children:s.newPassword===s.confirmPassword?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.A,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)("span",{className:"text-green-600",children:"Le password corrispondono"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"h-4 w-4 text-red-600"}),(0,a.jsx)("span",{className:"text-red-600",children:"Le password non corrispondono"})]})})]}),k&&(0,a.jsxs)(o.Fc,{className:(0,N.cn)("success"===k.type?"border-green-200 bg-green-50":"border-red-200 bg-red-50"),children:["success"===k.type?(0,a.jsx)(u.A,{className:"h-4 w-4 text-green-600"}):(0,a.jsx)(w.A,{className:"h-4 w-4 text-red-600"}),(0,a.jsx)(o.TN,{className:(0,N.cn)("success"===k.type?"text-green-800":"text-red-800"),children:k.text})]}),(0,a.jsx)(i.$,{type:"submit",className:"w-full bg-mariner-600 hover:bg-mariner-700",disabled:v||!y.isValid||s.newPassword!==s.confirmPassword,children:v?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Aggiornamento..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Cambia Password"]})})]})})]})}function y(){let[e,s]=(0,t.useState)({email:"",userType:"user"}),[r,d]=(0,t.useState)(!1),[m,x]=(0,t.useState)(null),h=async r=>{r.preventDefault(),d(!0),x(null);try{if(!e.email)throw Error("L'indirizzo email \xe8 obbligatorio");let r=await fetch("/api/password/request-password-reset",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e.email,user_type:e.userType})}),a=await r.json();if(r.ok&&a.success)x({type:"success",text:"Se l'email \xe8 registrata, riceverai le istruzioni per il reset della password."}),s({email:"",userType:"user"});else throw Error(a.detail||a.message||"Errore durante la richiesta di reset")}catch(e){x({type:"error",text:e instanceof Error?e.message:"Errore durante la richiesta di reset"})}finally{d(!1)}};return(0,a.jsxs)(c.Zp,{className:"w-full max-w-md mx-auto",children:[(0,a.jsxs)(c.aR,{className:"space-y-1",children:[(0,a.jsxs)(c.ZB,{className:"text-2xl flex items-center gap-2",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 text-mariner-600"}),"Recupera Password"]}),(0,a.jsx)(c.BT,{children:"Inserisci la tua email per ricevere le istruzioni di reset"})]}),(0,a.jsxs)(c.Wu,{children:[(0,a.jsxs)("form",{onSubmit:h,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(n.J,{children:"Tipo di Account"}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,a.jsx)("input",{type:"radio",value:"user",checked:"user"===e.userType,onChange:e=>s(s=>({...s,userType:e.target.value})),className:"text-mariner-600"}),(0,a.jsx)("span",{children:"Utente"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,a.jsx)("input",{type:"radio",value:"cantiere",checked:"cantiere"===e.userType,onChange:e=>s(s=>({...s,userType:e.target.value})),className:"text-mariner-600"}),(0,a.jsx)("span",{children:"Cantiere"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(n.J,{htmlFor:"email",children:"Indirizzo Email"}),(0,a.jsx)(l.p,{id:"email",type:"email",value:e.email,onChange:e=>s(s=>({...s,email:e.target.value})),placeholder:"<EMAIL>",required:!0})]}),m&&(0,a.jsxs)(o.Fc,{className:(0,N.cn)("success"===m.type?"border-green-200 bg-green-50":"border-red-200 bg-red-50"),children:["success"===m.type?(0,a.jsx)(u.A,{className:"h-4 w-4 text-green-600"}):(0,a.jsx)(w.A,{className:"h-4 w-4 text-red-600"}),(0,a.jsx)(o.TN,{className:(0,N.cn)("success"===m.type?"text-green-800":"text-red-800"),children:m.text})]}),(0,a.jsx)(i.$,{type:"submit",className:"w-full bg-mariner-600 hover:bg-mariner-700",disabled:r,children:r?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Invio in corso..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Invia Link di Reset"]})})]}),(0,a.jsx)("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)(j.A,{className:"h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"Informazioni sulla Sicurezza"}),(0,a.jsxs)("ul",{className:"space-y-1 text-xs",children:[(0,a.jsx)("li",{children:"• Il link di reset \xe8 valido per 30 minuti"}),(0,a.jsx)("li",{children:"• Pu\xf2 essere utilizzato una sola volta"}),(0,a.jsx)("li",{children:"• Se non ricevi l'email, controlla la cartella spam"}),(0,a.jsx)("li",{children:"• Per motivi di sicurezza, non riveleremo se l'email \xe8 registrata"})]})]})]})})]})]})}}}]);