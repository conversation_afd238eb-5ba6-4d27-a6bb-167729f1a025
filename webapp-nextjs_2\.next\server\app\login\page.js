(()=>{var e={};e.id=520,e.ids=[520],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3851:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>_});var s=r(60687),a=r(43210),i=r(16189),o=r(44493),n=r(29523),l=r(89667),d=r(80013),c=r(96834),u=r(63213),p=r(23361),m=r(58869),g=r(17313),x=r(41550),v=r(64021),b=r(12597),h=r(13861),f=r(93613),w=r(41862),y=r(81806);let j=()=>{let[e,t]=(0,a.useState)([]),[r,s]=(0,a.useState)({totalEvents:0,loginAttempts:0,blockedRequests:0,suspiciousActivity:0,lastEventTime:null}),i=(e,r,a="medium")=>{let i={type:e,timestamp:Date.now(),details:r,severity:a};t(e=>[...e,i].slice(-100)),s(t=>({totalEvents:t.totalEvents+1,loginAttempts:t.loginAttempts+ +("login_attempt"===e),blockedRequests:t.blockedRequests+ +("high"===a||"critical"===a),suspiciousActivity:t.suspiciousActivity+ +("suspicious_activity"===e),lastEventTime:i.timestamp})),console.log({type:e,details:r,timestamp:new Date(i.timestamp).toISOString()})};(0,a.useEffect)(()=>{let e=()=>{(window.outerHeight-window.innerHeight>160||window.outerWidth-window.innerWidth>160)&&i("suspicious_activity",{action:"devtools_detected",windowSize:{outer:[window.outerWidth,window.outerHeight],inner:[window.innerWidth,window.innerHeight]}},"low")},t=e=>{let t=e.clipboardData?.getData("text")||"";[/script/gi,/javascript:/gi,/vbscript:/gi,/onload|onerror|onclick/gi,/<iframe|<object|<embed/gi,/union.*select/gi,/drop.*table/gi].some(e=>e.test(t))&&(e.preventDefault(),i("suspicious_activity",{action:"malicious_paste_blocked",content:t.substring(0,100)},"high"))},r=Storage.prototype.setItem;Storage.prototype.setItem=function(e,t){return/<script|javascript:|vbscript:/gi.test(t)?void i("suspicious_activity",{action:"malicious_storage_attempt",key:e,value:t.substring(0,50)},"high"):r.call(this,e,t)};let s=e=>{let t=e.message.toLowerCase();["script error","permission denied","access denied","blocked by cors","network error"].some(e=>t.includes(e))&&i("suspicious_activity",{action:"suspicious_js_error",message:e.message,filename:e.filename,lineno:e.lineno},"medium")},a=e=>{5e3>performance.now()&&i("suspicious_activity",{action:"rapid_page_exit",timeOnPage:performance.now()},"low")};window.addEventListener("resize",e),window.addEventListener("paste",t),window.addEventListener("error",s),window.addEventListener("beforeunload",a);let o=setInterval(e,5e3);return()=>{window.removeEventListener("resize",e),window.removeEventListener("paste",t),window.removeEventListener("error",s),window.removeEventListener("beforeunload",a),clearInterval(o),Storage.prototype.setItem=r}},[]);let o=(t=10)=>{let r=Date.now()-60*t*1e3;return e.filter(e=>e.timestamp>r)};return{events:e,metrics:r,getRecentEvents:o,getEventsByType:t=>e.filter(e=>e.type===t),getEventsBySeverity:t=>e.filter(e=>e.severity===t),isUnderAttack:()=>o(5).filter(e=>"high"===e.severity||"critical"===e.severity).length>3,getThreatLevel:()=>{let e=o(10),t=e.filter(e=>"critical"===e.severity).length,r=e.filter(e=>"high"===e.severity).length;return t>0?"critical":r>2?"high":e.length>10?"medium":"low"},logSecurityEvent:i,logLoginAttempt:(e,t,r)=>{i("login_attempt",{username:e,success:t,userAgent:navigator.userAgent,timestamp:Date.now(),...r},t?"low":"medium")},logFormSubmission:(e,t,r)=>{i("form_submission",{formType:e,success:t,userAgent:navigator.userAgent,...r},"low")},logSuspiciousActivity:(e,t)=>{i("suspicious_activity",{activity:e,userAgent:navigator.userAgent,url:window.location.href,...t},"high")},logRateLimitHit:(e,t)=>{i("rate_limit_hit",{endpoint:e,userAgent:navigator.userAgent,...t},"medium")}}};function _(){let[e,t]=(0,a.useState)("user"),[r,_]=(0,a.useState)({username:"",password:"",codice_cantiere:"",password_cantiere:""}),[A,N]=(0,a.useState)(""),[C,k]=(0,a.useState)(!1),[z,E]=(0,a.useState)({}),[V,S]=(0,a.useState)(!1),[P,q]=(0,a.useState)(!1),[R,L]=(0,a.useState)({}),{login:T,loginCantiere:M}=(0,u.A)(),I=(0,i.useRouter)(),{logLoginAttempt:D,logSuspiciousActivity:Z,getThreatLevel:$}=j(),F=()=>"user"===e?""!==r.username.trim()&&""!==r.password.trim():""!==r.codice_cantiere.trim()&&""!==r.password_cantiere.trim(),B=()=>{let t={};return"user"===e?(r.username&&0!==r.username.trim().length||(t.username="Username \xe8 obbligatorio"),r.password||(t.password="Password \xe8 obbligatoria")):(r.codice_cantiere.trim()?r.codice_cantiere.length<3&&(t.codice_cantiere="Codice cantiere troppo corto"):t.codice_cantiere="Codice cantiere \xe8 obbligatorio",r.password_cantiere||(t.password_cantiere="Password cantiere \xe8 obbligatoria")),E(t),0===Object.keys(t).length},U=(e,t)=>{let r={...R};"username"===e||"codice_cantiere"===e?r[e]=""===t.trim():("password"===e||"password_cantiere"===e)&&(r[e]=""===t.trim()),L(r)},G=async t=>{t.preventDefault(),N(""),E({});let s="user"===e?r.username:r.codice_cantiere;if(!(0,y.Eb)(`login-${s}`,5,3e5)){N("Troppi tentativi di login. Riprova tra 5 minuti."),Z("rate_limit_exceeded",{loginType:e,identifier:s});return}if(!B())return;let a=$();if("critical"===a){N("Sistema temporaneamente non disponibile per motivi di sicurezza."),Z("login_blocked_threat_level",{threatLevel:a});return}k(!0);try{if("user"===e){let t=await T(r.username,r.password);t.success&&t.user?(D(r.username,!0,{ruolo:t.user.ruolo}),"owner"===t.user.ruolo?I.push("/admin"):"user"===t.user.ruolo?I.push("/cantieri"):"cantieri_user"===t.user.ruolo?I.push("/cavi"):I.push("/cantieri")):(D(r.username,!1,{error:t.error,loginType:e}),N(t.error||"Credenziali non valide"))}else{let t=await M(r.codice_cantiere,r.password_cantiere);t.success?(D(r.codice_cantiere,!0,{type:"cantiere"}),I.push("/cavi")):(D(r.codice_cantiere,!1,{error:t.error,loginType:e}),N(t.error||"Credenziali cantiere non valide"))}}catch(t){D("user"===e?r.username:r.codice_cantiere,!1,{error:t.message||"Errore imprevisto",loginType:e}),N("Errore durante il login. Riprova.")}finally{k(!1)}},O=(e,t)=>{_(r=>({...r,[e]:t})),void 0!==R[e]&&U(e,t),A&&""!==t.trim()&&N("")},W=(e,t)=>{U(e,t)};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-slate-100 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md space-y-6",children:[(0,s.jsxs)("div",{className:"text-center space-y-2",children:[(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center",children:(0,s.jsx)(p.A,{className:"w-8 h-8 text-white"})})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-slate-900",children:"CABLYS"}),(0,s.jsx)("p",{className:"text-slate-600",children:"Cable Installation Advance System"}),(0,s.jsx)("p",{className:"text-sm text-slate-700 font-medium",children:"Sistema di gestione cavi di nuova generazione"})]}),(0,s.jsxs)("div",{className:"flex gap-0",role:"tablist","aria-label":"Tipo di accesso",children:[(0,s.jsxs)("button",{type:"button",className:`flex-1 flex items-center justify-center gap-2 px-4 py-3 font-medium transition-all duration-200 relative focus:outline-none ${"user"===e?"text-slate-900":"text-slate-600 hover:text-slate-800"}`,onClick:()=>t("user"),"aria-pressed":"user"===e,role:"tab","aria-selected":"user"===e,id:"tab-user","aria-controls":"panel-user",children:[(0,s.jsx)(m.A,{className:"w-4 h-4","aria-hidden":"true"}),"Accesso Utente","user"===e&&(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-0.5 bg-blue-600 rounded-full","aria-hidden":"true"})]}),(0,s.jsxs)("button",{type:"button",className:`flex-1 flex items-center justify-center gap-2 px-4 py-3 font-medium transition-all duration-200 relative focus:outline-none ${"cantiere"===e?"text-slate-900":"text-slate-600 hover:text-slate-800"}`,onClick:()=>t("cantiere"),"aria-pressed":"cantiere"===e,role:"tab","aria-selected":"cantiere"===e,id:"tab-cantiere","aria-controls":"panel-cantiere",children:[(0,s.jsx)(g.A,{className:"w-4 h-4","aria-hidden":"true"}),"Accesso Cantiere","cantiere"===e&&(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-0.5 bg-green-600 rounded-full","aria-hidden":"true"})]})]}),(0,s.jsxs)(o.Zp,{children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsx)(o.ZB,{className:"flex items-center gap-2",children:"user"===e?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(m.A,{className:"w-5 h-5 text-blue-600"}),"Login Utente"]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(g.A,{className:"w-5 h-5 text-green-600"}),"Login Cantiere"]})}),(0,s.jsx)(o.BT,{children:"user"===e?"Accedi con le tue credenziali utente":"Accedi con il codice cantiere"})]}),(0,s.jsx)(o.Wu,{children:(0,s.jsxs)("form",{onSubmit:G,className:"space-y-4",role:"tabpanel",id:"user"===e?"panel-user":"panel-cantiere","aria-labelledby":"user"===e?"tab-user":"tab-cantiere",noValidate:!0,children:["user"===e?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d.J,{htmlFor:"username",children:"Username"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(x.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400"}),(0,s.jsx)(l.p,{id:"username",type:"text",placeholder:"Inserisci username",value:r.username,onChange:e=>O("username",e.target.value),onBlur:e=>W("username",e.target.value),required:!0,disabled:C,className:`pl-10 ${R.username?"border-red-500 focus-visible:border-red-500 focus-visible:ring-red-500/20":""}`,"aria-invalid":R.username,"aria-describedby":R.username?"username-error":void 0,autoComplete:"username"}),R.username&&(0,s.jsx)("p",{id:"username-error",className:"text-sm text-red-600 mt-1",children:"Il campo username \xe8 obbligatorio"})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d.J,{htmlFor:"password",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400"}),(0,s.jsx)(l.p,{id:"password",type:V?"text":"password",placeholder:"Inserisci password",value:r.password,onChange:e=>O("password",e.target.value),onBlur:e=>W("password",e.target.value),required:!0,disabled:C,className:`pl-10 pr-10 ${R.password?"border-red-500 focus-visible:border-red-500 focus-visible:ring-red-500/20":""}`,"aria-invalid":R.password,"aria-describedby":R.password?"password-error":void 0,autoComplete:"current-password"}),R.password&&(0,s.jsx)("p",{id:"password-error",className:"text-sm text-red-600 mt-1",children:"Il campo password \xe8 obbligatorio"}),(0,s.jsx)("button",{type:"button",onClick:()=>S(!V),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 focus:outline-none focus:text-slate-600",disabled:C,"aria-label":V?"Nascondi password":"Mostra password",children:V?(0,s.jsx)(b.A,{className:"w-4 h-4"}):(0,s.jsx)(h.A,{className:"w-4 h-4"})})]})]})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d.J,{htmlFor:"codice_cantiere",children:"Codice Cantiere"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400"}),(0,s.jsx)(l.p,{id:"codice_cantiere",type:"text",placeholder:"Inserisci codice cantiere",value:r.codice_cantiere,onChange:e=>O("codice_cantiere",e.target.value),onBlur:e=>W("codice_cantiere",e.target.value),required:!0,disabled:C,className:`pl-10 ${R.codice_cantiere?"border-red-500 focus-visible:border-red-500 focus-visible:ring-red-500/20":""}`,"aria-invalid":R.codice_cantiere,"aria-describedby":R.codice_cantiere?"codice-cantiere-error":void 0,autoComplete:"off"}),R.codice_cantiere&&(0,s.jsx)("p",{id:"codice-cantiere-error",className:"text-sm text-red-600 mt-1",children:"Il campo codice cantiere \xe8 obbligatorio"})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d.J,{htmlFor:"password_cantiere",children:"Password Cantiere"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400"}),(0,s.jsx)(l.p,{id:"password_cantiere",type:P?"text":"password",placeholder:"Inserisci password cantiere",value:r.password_cantiere,onChange:e=>O("password_cantiere",e.target.value),onBlur:e=>W("password_cantiere",e.target.value),required:!0,disabled:C,className:`pl-10 pr-10 ${R.password_cantiere?"border-red-500 focus-visible:border-red-500 focus-visible:ring-red-500/20":""}`,"aria-invalid":R.password_cantiere,"aria-describedby":R.password_cantiere?"password-cantiere-error":void 0,autoComplete:"current-password"}),R.password_cantiere&&(0,s.jsx)("p",{id:"password-cantiere-error",className:"text-sm text-red-600 mt-1",children:"Il campo password cantiere \xe8 obbligatorio"}),(0,s.jsx)("button",{type:"button",onClick:()=>q(!P),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 focus:outline-none focus:text-slate-600",disabled:C,"aria-label":P?"Nascondi password":"Mostra password",children:P?(0,s.jsx)(b.A,{className:"w-4 h-4"}):(0,s.jsx)(h.A,{className:"w-4 h-4"})})]})]})]}),A&&(0,s.jsxs)("div",{className:"flex items-center gap-2 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm",children:[(0,s.jsx)(f.A,{className:"w-5 h-5 text-red-500 flex-shrink-0"}),(0,s.jsx)("span",{className:"text-sm text-red-800 font-medium",children:A})]}),(0,s.jsx)(n.$,{type:"submit",className:`w-full transition-all duration-200 ${!F()&&!C?"opacity-50 cursor-not-allowed bg-slate-300 hover:bg-slate-300 text-slate-500":"hover:shadow-md active:scale-[0.98] focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"}`,disabled:C||!F(),children:C?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(w.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Accesso in corso..."]}):"Accedi"}),"user"===e&&(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("button",{type:"button",className:"text-sm text-blue-700 hover:text-blue-900 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded px-1 py-1 transition-colors duration-200",onClick:()=>I.push("/forgot-password"),"aria-label":"Recupera password dimenticata",children:"Password dimenticata?"})})]})})]}),(0,s.jsxs)("div",{className:"text-center space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-center gap-2",children:[(0,s.jsx)(c.E,{variant:"secondary",className:"bg-blue-100 text-blue-800",children:"Next.js 15"}),(0,s.jsx)(c.E,{variant:"secondary",className:"bg-green-100 text-green-800",children:"PWA Ready"})]}),(0,s.jsx)("p",{className:"text-xs text-slate-500",children:"Sistema di gestione cavi di nuova generazione"})]})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},12597:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41550:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>n,Zp:()=>i,aR:()=>o});var s=r(60687);r(43210);var a=r(4780);function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},45880:(e,t,r)=>{Promise.resolve().then(r.bind(r,94934))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},55608:(e,t,r)=>{Promise.resolve().then(r.bind(r,3851))},58869:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64021:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},78148:(e,t,r)=>{"use strict";r.d(t,{b:()=>n});var s=r(43210),a=r(14163),i=r(60687),o=s.forwardRef((e,t)=>(0,i.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var n=o},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,r)=>{"use strict";r.d(t,{J:()=>o});var s=r(60687);r(43210);var a=r(78148),i=r(4780);function o({className:e,...t}){return(0,s.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},81630:e=>{"use strict";e.exports=require("http")},81806:(e,t,r)=>{"use strict";r.d(t,{Eb:()=>g,GN:()=>x});let s=/[<>\"'&\x00-\x1f\x7f-\x9f]/g,a=/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,i=/(<script|javascript:|vbscript:|onload|onerror|onclick)/gi,o=e=>"string"!=typeof e?"":e.trim().replace(s,"").replace(/\s+/g," ").substring(0,1e3),n=e=>{let t=o(e);return t.length<3?{isValid:!1,error:"Username deve essere almeno 3 caratteri"}:t.length>20?{isValid:!1,error:"Username non pu\xf2 superare 20 caratteri"}:/^[a-zA-Z0-9._-]+$/.test(t)?/^[._-]|[._-]$/.test(t)?{isValid:!1,error:"Username non pu\xf2 iniziare o finire con caratteri speciali"}:{isValid:!0}:{isValid:!1,error:"Username pu\xf2 contenere solo lettere, numeri, punti, underscore e trattini"}},l=e=>{if(!e||e.length<8)return{isValid:!1,error:"Password deve essere almeno 8 caratteri",strength:0};if(e.length>128)return{isValid:!1,error:"Password troppo lunga (max 128 caratteri)",strength:0};let t=0;return(/[a-z]/.test(e)&&t++,/[A-Z]/.test(e)&&t++,/[0-9]/.test(e)&&t++,/[^a-zA-Z0-9]/.test(e)&&t++,e.length>=12&&t++,t<3)?{isValid:!1,error:"Password deve contenere almeno: 1 minuscola, 1 maiuscola, 1 numero o 1 carattere speciale",strength:t}:["password","123456","admin","qwerty","letmein"].some(t=>e.toLowerCase().includes(t))?{isValid:!1,error:"Password troppo comune",strength:t}:{isValid:!0,strength:t}},d=e=>{let t=o(e);return t?t.length>254?{isValid:!1,error:"Email troppo lunga"}:/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(t)?{isValid:!0}:{isValid:!1,error:"Formato email non valido"}:{isValid:!1,error:"Email \xe8 obbligatoria"}},c=(e,t=255)=>o(e).length>t?{isValid:!1,error:`Testo troppo lungo (max ${t} caratteri)`}:i.test(e)||a.test(e)?{isValid:!1,error:"Contenuto non consentito rilevato"}:{isValid:!0},u=e=>{let t=o(e);return t?t.length<2?{isValid:!1,error:"Ragione sociale troppo corta"}:t.length>100?{isValid:!1,error:"Ragione sociale troppo lunga (max 100 caratteri)"}:/^[a-zA-Z0-9\s\.\-&']+$/.test(t)?{isValid:!0}:{isValid:!1,error:"Ragione sociale contiene caratteri non consentiti"}:{isValid:!1,error:"Ragione sociale \xe8 obbligatoria"}},p=e=>{if(!e)return{isValid:!0};let t=o(e).replace(/\s/g,"");return t.length<8||t.length>15?{isValid:!1,error:"VAT deve essere tra 8 e 15 caratteri"}:/^[A-Z0-9]+$/i.test(t)?{isValid:!0}:{isValid:!1,error:"VAT pu\xf2 contenere solo lettere e numeri"}},m=new Map,g=(e,t,r)=>{let s=Date.now(),a=m.get(e);return!a||s>a.resetTime?(m.set(e,{count:1,resetTime:s+r}),!0):!(a.count>=t)&&(a.count++,!0)},x=e=>{let t={},r=n(e.username);if(r.isValid||(t.username=r.error),e.password){let r=l(e.password);r.isValid||(t.password=r.error)}let s=u(e.ragione_sociale);if(s.isValid||(t.ragione_sociale=s.error),e.email){let r=d(e.email);r.isValid||(t.email=r.error)}if(e.vat){let r=p(e.vat);r.isValid||(t.vat=r.error)}if(e.indirizzo){let r=c(e.indirizzo,200);r.isValid||(t.indirizzo=r.error)}if(e.nazione){let r=c(e.nazione,50);r.isValid||(t.nazione=r.error)}if(e.referente_aziendale){let r=c(e.referente_aziendale,100);r.isValid||(t.referente_aziendale=r.error)}return{isValid:0===Object.keys(t).length,errors:t}}},83997:e=>{"use strict";e.exports=require("tty")},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var s=r(60687);r(43210);var a=r(4780);function i({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},90707:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),o=r.n(i),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let d={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,94934)),"C:\\CMS\\webapp-nextjs_2\\src\\app\\login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\CMS\\webapp-nextjs_2\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\CMS\\webapp-nextjs_2\\src\\app\\login\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94735:e=>{"use strict";e.exports=require("events")},94934:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs_2\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_2\\src\\app\\login\\page.tsx","default")},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var s=r(60687);r(43210);var a=r(8730),i=r(24224),o=r(4780);let n=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...i}){let l=r?a.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,o.cn)(n({variant:t}),e),...i})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,588,658,415],()=>r(90707));module.exports=s})();