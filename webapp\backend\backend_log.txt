INFO:backend.database:Tentativo di connessione al database: postgresql://postgres:Taranto@localhost:5432/cantieri
INFO:backend.database:Connessione al database riuscita
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\pydantic\_internal\_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'orm_mode' has been renamed to 'from_attributes'
  warnings.warn(message, UserWarning)
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\pydantic\_internal\_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'orm_mode' has been renamed to 'from_attributes'
  warnings.warn(message, UserWarning)
INFO:     Started server process [10724]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8001 (Press CTRL+C to quit)
INFO:backend.database:Nuova sessione del database creata
Stringa di connessione al database: postgresql://postgres:Taranto@localhost:5432/cantieri
Directory temporanea creata: C:\CMS\webapp\static\temp
Email service non disponibile - modalita testing attiva
Audit logger non disponibile - modalita testing attiva
Configurazione CORS con origini: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:8080', 'http://localhost', 'http://localhost:5500', 'http://127.0.0.1:5500', '*']
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1751897169}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-07-06 16:09:54,355 INFO sqlalchemy.engine.Engine select pg_catalog.version()
INFO:sqlalchemy.engine.Engine:select pg_catalog.version()
2025-07-06 16:09:54,355 INFO sqlalchemy.engine.Engine [raw sql] {}
INFO:sqlalchemy.engine.Engine:[raw sql] {}
2025-07-06 16:09:54,356 INFO sqlalchemy.engine.Engine select current_schema()
INFO:sqlalchemy.engine.Engine:select current_schema()
2025-07-06 16:09:54,356 INFO sqlalchemy.engine.Engine [raw sql] {}
INFO:sqlalchemy.engine.Engine:[raw sql] {}
2025-07-06 16:09:54,356 INFO sqlalchemy.engine.Engine show standard_conforming_strings
INFO:sqlalchemy.engine.Engine:show standard_conforming_strings
2025-07-06 16:09:54,356 INFO sqlalchemy.engine.Engine [raw sql] {}
INFO:sqlalchemy.engine.Engine:[raw sql] {}
2025-07-06 16:09:54,357 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-06 16:09:54,360 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-07-06 16:09:54,361 INFO sqlalchemy.engine.Engine [generated in 0.00036s] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[generated in 0.00036s] {'id_utente_1': 2, 'param_1': 1}
INFO:backend.database:Sessione del database chiusa
Utente a autenticato con successo
Test token riuscito per utente: a, ruolo: user, impersonato: False
2025-07-06 16:09:54,367 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:54223 - "POST /api/auth/test-token HTTP/1.1" 200 OK
INFO:     127.0.0.1:54223 - "GET /api/cantieri HTTP/1.1" 307 Temporary Redirect
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1751897169}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-07-06 16:09:54,527 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-06 16:09:54,527 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-07-06 16:09:54,527 INFO sqlalchemy.engine.Engine [cached since 0.1668s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.1668s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
2025-07-06 16:09:54,532 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_utente = %(id_utente_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_utente = %(id_utente_1)s
2025-07-06 16:09:54,532 INFO sqlalchemy.engine.Engine [generated in 0.00032s] {'id_utente_1': 2}
INFO:sqlalchemy.engine.Engine:[generated in 0.00032s] {'id_utente_1': 2}
INFO:backend.database:Sessione del database chiusa
2025-07-06 16:09:54,538 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:54227 - "GET /api/cantieri/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:54223 - "GET /api/cantieri HTTP/1.1" 307 Temporary Redirect
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1751897169}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-07-06 16:09:54,774 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-06 16:09:54,774 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-07-06 16:09:54,775 INFO sqlalchemy.engine.Engine [cached since 0.4143s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.4143s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
2025-07-06 16:09:54,777 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_utente = %(id_utente_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_utente = %(id_utente_1)s
2025-07-06 16:09:54,777 INFO sqlalchemy.engine.Engine [cached since 0.2448s ago] {'id_utente_1': 2}
INFO:sqlalchemy.engine.Engine:[cached since 0.2448s ago] {'id_utente_1': 2}
INFO:backend.database:Sessione del database chiusa
2025-07-06 16:09:54,779 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:54227 - "GET /api/cantieri/ HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1751897169}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-07-06 16:09:54,788 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-06 16:09:54,789 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-07-06 16:09:54,789 INFO sqlalchemy.engine.Engine [cached since 0.4287s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.4287s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
2025-07-06 16:09:54,792 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
2025-07-06 16:09:54,793 INFO sqlalchemy.engine.Engine [generated in 0.00037s] {'id_cantiere_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[generated in 0.00037s] {'id_cantiere_1': 2, 'param_1': 1}
2025-07-06 16:09:54,805 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s) AS anon_1
2025-07-06 16:09:54,805 INFO sqlalchemy.engine.Engine [generated in 0.00044s] {'id_cantiere_1': 2}
INFO:sqlalchemy.engine.Engine:[generated in 0.00044s] {'id_cantiere_1': 2}
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1751897169}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-07-06 16:09:54,815 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_installazione = %(stato_installazione_1)s) AS anon_1
INFO:backend.database:Nuova sessione del database creata
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_installazione = %(stato_installazione_1)s) AS anon_1
2025-07-06 16:09:54,816 INFO sqlalchemy.engine.Engine [generated in 0.00060s] {'id_cantiere_1': 2, 'stato_installazione_1': 'Installato'}
INFO:sqlalchemy.engine.Engine:[generated in 0.00060s] {'id_cantiere_1': 2, 'stato_installazione_1': 'Installato'}
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1751897169}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-07-06 16:09:54,825 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.collegamenti > %(collegamenti_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.collegamenti > %(collegamenti_1)s) AS anon_1
2025-07-06 16:09:54,825 INFO sqlalchemy.engine.Engine [generated in 0.00069s] {'id_cantiere_1': 2, 'collegamenti_1': 0}
INFO:backend.database:Nuova sessione del database creata
INFO:sqlalchemy.engine.Engine:[generated in 0.00069s] {'id_cantiere_1': 2, 'collegamenti_1': 0}
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1751897169}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-07-06 16:09:54,835 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_certificazione = %(stato_certificazione_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_certificazione = %(stato_certificazione_1)s) AS anon_1
2025-07-06 16:09:54,835 INFO sqlalchemy.engine.Engine [generated in 0.00031s] {'id_cantiere_1': 2, 'stato_certificazione_1': 'Certificato'}
INFO:sqlalchemy.engine.Engine:[generated in 0.00031s] {'id_cantiere_1': 2, 'stato_certificazione_1': 'Certificato'}
INFO:backend.database:Sessione del database chiusa
2025-07-06 16:09:54,838 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:54223 - "GET /api/cantieri/2/statistics HTTP/1.1" 200 OK
2025-07-06 16:09:54,899 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-06 16:09:54,899 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-07-06 16:09:54,900 INFO sqlalchemy.engine.Engine [cached since 0.5394s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.5394s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
2025-07-06 16:09:54,906 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
2025-07-06 16:09:54,907 INFO sqlalchemy.engine.Engine [cached since 0.1145s ago] {'id_cantiere_1': 5, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.1145s ago] {'id_cantiere_1': 5, 'param_1': 1}
2025-07-06 16:09:54,912 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s) AS anon_1
2025-07-06 16:09:54,912 INFO sqlalchemy.engine.Engine [cached since 0.108s ago] {'id_cantiere_1': 5}
INFO:sqlalchemy.engine.Engine:[cached since 0.108s ago] {'id_cantiere_1': 5}
2025-07-06 16:09:54,917 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_installazione = %(stato_installazione_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_installazione = %(stato_installazione_1)s) AS anon_1
2025-07-06 16:09:54,917 INFO sqlalchemy.engine.Engine [cached since 0.1018s ago] {'id_cantiere_1': 5, 'stato_installazione_1': 'Installato'}
INFO:sqlalchemy.engine.Engine:[cached since 0.1018s ago] {'id_cantiere_1': 5, 'stato_installazione_1': 'Installato'}
2025-07-06 16:09:54,920 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.collegamenti > %(collegamenti_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.collegamenti > %(collegamenti_1)s) AS anon_1
2025-07-06 16:09:54,920 INFO sqlalchemy.engine.Engine [cached since 0.09508s ago] {'id_cantiere_1': 5, 'collegamenti_1': 0}
INFO:sqlalchemy.engine.Engine:[cached since 0.09508s ago] {'id_cantiere_1': 5, 'collegamenti_1': 0}
2025-07-06 16:09:54,921 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_certificazione = %(stato_certificazione_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_certificazione = %(stato_certificazione_1)s) AS anon_1
2025-07-06 16:09:54,922 INFO sqlalchemy.engine.Engine [cached since 0.08673s ago] {'id_cantiere_1': 5, 'stato_certificazione_1': 'Certificato'}
INFO:sqlalchemy.engine.Engine:[cached since 0.08673s ago] {'id_cantiere_1': 5, 'stato_certificazione_1': 'Certificato'}
INFO:backend.database:Sessione del database chiusa
2025-07-06 16:09:54,923 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:54232 - "GET /api/cantieri/5/statistics HTTP/1.1" 200 OK
2025-07-06 16:09:55,009 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-06 16:09:55,009 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-07-06 16:09:55,009 INFO sqlalchemy.engine.Engine [cached since 0.6492s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.6492s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1751897169}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-07-06 16:09:55,013 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-06 16:09:55,014 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-07-06 16:09:55,014 INFO sqlalchemy.engine.Engine [cached since 0.6536s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.6536s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
2025-07-06 16:09:55,017 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
Utente a autenticato con successo
2025-07-06 16:09:55,018 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-06 16:09:55,018 INFO sqlalchemy.engine.Engine [cached since 0.2255s ago] {'id_cantiere_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-06 16:09:55,019 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:[cached since 0.2255s ago] {'id_cantiere_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-07-06 16:09:55,019 INFO sqlalchemy.engine.Engine [cached since 0.6591s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.6591s ago] {'id_utente_1': 2, 'param_1': 1}
2025-07-06 16:09:55,020 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
2025-07-06 16:09:55,021 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s) AS anon_1
2025-07-06 16:09:55,021 INFO sqlalchemy.engine.Engine [cached since 0.229s ago] {'id_cantiere_1': 1, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.229s ago] {'id_cantiere_1': 1, 'param_1': 1}
2025-07-06 16:09:55,022 INFO sqlalchemy.engine.Engine [cached since 0.2171s ago] {'id_cantiere_1': 2}
INFO:sqlalchemy.engine.Engine:[cached since 0.2171s ago] {'id_cantiere_1': 2}
2025-07-06 16:09:55,024 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_installazione = %(stato_installazione_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_installazione = %(stato_installazione_1)s) AS anon_1
2025-07-06 16:09:55,024 INFO sqlalchemy.engine.Engine [cached since 0.209s ago] {'id_cantiere_1': 2, 'stato_installazione_1': 'Installato'}
INFO:sqlalchemy.engine.Engine:[cached since 0.209s ago] {'id_cantiere_1': 2, 'stato_installazione_1': 'Installato'}
2025-07-06 16:09:55,026 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s) AS anon_1
Utente a autenticato con successo
2025-07-06 16:09:55,027 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.collegamenti > %(collegamenti_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.collegamenti > %(collegamenti_1)s) AS anon_1
2025-07-06 16:09:55,027 INFO sqlalchemy.engine.Engine [cached since 0.2225s ago] {'id_cantiere_1': 1}
2025-07-06 16:09:55,028 INFO sqlalchemy.engine.Engine [cached since 0.2032s ago] {'id_cantiere_1': 2, 'collegamenti_1': 0}
INFO:sqlalchemy.engine.Engine:[cached since 0.2225s ago] {'id_cantiere_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.2032s ago] {'id_cantiere_1': 2, 'collegamenti_1': 0}
2025-07-06 16:09:55,029 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
2025-07-06 16:09:55,030 INFO sqlalchemy.engine.Engine [cached since 0.2374s ago] {'id_cantiere_1': 4, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.2374s ago] {'id_cantiere_1': 4, 'param_1': 1}
2025-07-06 16:09:55,032 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_certificazione = %(stato_certificazione_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_certificazione = %(stato_certificazione_1)s) AS anon_1
2025-07-06 16:09:55,033 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_installazione = %(stato_installazione_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_installazione = %(stato_installazione_1)s) AS anon_1
2025-07-06 16:09:55,033 INFO sqlalchemy.engine.Engine [cached since 0.1981s ago] {'id_cantiere_1': 2, 'stato_certificazione_1': 'Certificato'}
INFO:sqlalchemy.engine.Engine:[cached since 0.1981s ago] {'id_cantiere_1': 2, 'stato_certificazione_1': 'Certificato'}
2025-07-06 16:09:55,034 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s) AS anon_1
2025-07-06 16:09:55,034 INFO sqlalchemy.engine.Engine [cached since 0.219s ago] {'id_cantiere_1': 1, 'stato_installazione_1': 'Installato'}
INFO:sqlalchemy.engine.Engine:[cached since 0.219s ago] {'id_cantiere_1': 1, 'stato_installazione_1': 'Installato'}
2025-07-06 16:09:55,035 INFO sqlalchemy.engine.Engine [cached since 0.2302s ago] {'id_cantiere_1': 4}
INFO:sqlalchemy.engine.Engine:[cached since 0.2302s ago] {'id_cantiere_1': 4}
2025-07-06 16:09:55,037 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.collegamenti > %(collegamenti_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.collegamenti > %(collegamenti_1)s) AS anon_1
2025-07-06 16:09:55,037 INFO sqlalchemy.engine.Engine [cached since 0.2123s ago] {'id_cantiere_1': 1, 'collegamenti_1': 0}
INFO:backend.database:Sessione del database chiusa
2025-07-06 16:09:55,038 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:[cached since 0.2123s ago] {'id_cantiere_1': 1, 'collegamenti_1': 0}
2025-07-06 16:09:55,039 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_installazione = %(stato_installazione_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_installazione = %(stato_installazione_1)s) AS anon_1
2025-07-06 16:09:55,039 INFO sqlalchemy.engine.Engine [cached since 0.2239s ago] {'id_cantiere_1': 4, 'stato_installazione_1': 'Installato'}
INFO:sqlalchemy.engine.Engine:[cached since 0.2239s ago] {'id_cantiere_1': 4, 'stato_installazione_1': 'Installato'}
INFO:     127.0.0.1:54223 - "GET /api/cantieri/2/statistics HTTP/1.1" 200 OK
2025-07-06 16:09:55,041 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_certificazione = %(stato_certificazione_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_certificazione = %(stato_certificazione_1)s) AS anon_1
2025-07-06 16:09:55,042 INFO sqlalchemy.engine.Engine [cached since 0.2067s ago] {'id_cantiere_1': 1, 'stato_certificazione_1': 'Certificato'}
INFO:sqlalchemy.engine.Engine:[cached since 0.2067s ago] {'id_cantiere_1': 1, 'stato_certificazione_1': 'Certificato'}
2025-07-06 16:09:55,042 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.collegamenti > %(collegamenti_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.collegamenti > %(collegamenti_1)s) AS anon_1
2025-07-06 16:09:55,043 INFO sqlalchemy.engine.Engine [cached since 0.218s ago] {'id_cantiere_1': 4, 'collegamenti_1': 0}
INFO:sqlalchemy.engine.Engine:[cached since 0.218s ago] {'id_cantiere_1': 4, 'collegamenti_1': 0}
DEBUG IAP - Cantiere 1: total=95, installati=49, collegati=5, certificati=0
DEBUG IAP - Calcolo: sforzo_solo_installati=88.0, sforzo_solo_collegati=17.5, sforzo_certificati=0.0
DEBUG IAP - Risultato: numeratore=105.5, denominatore=380.0, IAP=27.76%
2025-07-06 16:09:55,044 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_certificazione = %(stato_certificazione_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_certificazione = %(stato_certificazione_1)s) AS anon_1
2025-07-06 16:09:55,044 INFO sqlalchemy.engine.Engine [cached since 0.2092s ago] {'id_cantiere_1': 4, 'stato_certificazione_1': 'Certificato'}
INFO:sqlalchemy.engine.Engine:[cached since 0.2092s ago] {'id_cantiere_1': 4, 'stato_certificazione_1': 'Certificato'}
INFO:backend.database:Sessione del database chiusa
2025-07-06 16:09:55,045 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:54227 - "GET /api/cantieri/1/statistics HTTP/1.1" 200 OK
INFO:backend.database:Sessione del database chiusa
2025-07-06 16:09:55,047 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:54229 - "GET /api/cantieri/4/statistics HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1751897169}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-07-06 16:09:55,095 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-06 16:09:55,095 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-07-06 16:09:55,096 INFO sqlalchemy.engine.Engine [cached since 0.7353s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.7353s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
2025-07-06 16:09:55,098 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
2025-07-06 16:09:55,099 INFO sqlalchemy.engine.Engine [cached since 0.3062s ago] {'id_cantiere_1': 5, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.3062s ago] {'id_cantiere_1': 5, 'param_1': 1}
2025-07-06 16:09:55,100 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s) AS anon_1
2025-07-06 16:09:55,100 INFO sqlalchemy.engine.Engine [cached since 0.2958s ago] {'id_cantiere_1': 5}
INFO:sqlalchemy.engine.Engine:[cached since 0.2958s ago] {'id_cantiere_1': 5}
2025-07-06 16:09:55,102 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_installazione = %(stato_installazione_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_installazione = %(stato_installazione_1)s) AS anon_1
2025-07-06 16:09:55,102 INFO sqlalchemy.engine.Engine [cached since 0.2869s ago] {'id_cantiere_1': 5, 'stato_installazione_1': 'Installato'}
INFO:sqlalchemy.engine.Engine:[cached since 0.2869s ago] {'id_cantiere_1': 5, 'stato_installazione_1': 'Installato'}
2025-07-06 16:09:55,104 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.collegamenti > %(collegamenti_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.collegamenti > %(collegamenti_1)s) AS anon_1
2025-07-06 16:09:55,104 INFO sqlalchemy.engine.Engine [cached since 0.2792s ago] {'id_cantiere_1': 5, 'collegamenti_1': 0}
INFO:sqlalchemy.engine.Engine:[cached since 0.2792s ago] {'id_cantiere_1': 5, 'collegamenti_1': 0}
2025-07-06 16:09:55,105 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_certificazione = %(stato_certificazione_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_certificazione = %(stato_certificazione_1)s) AS anon_1
2025-07-06 16:09:55,105 INFO sqlalchemy.engine.Engine [cached since 0.2706s ago] {'id_cantiere_1': 5, 'stato_certificazione_1': 'Certificato'}
INFO:sqlalchemy.engine.Engine:[cached since 0.2706s ago] {'id_cantiere_1': 5, 'stato_certificazione_1': 'Certificato'}
INFO:backend.database:Sessione del database chiusa
2025-07-06 16:09:55,107 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:54223 - "GET /api/cantieri/5/statistics HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1751897169}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-07-06 16:09:55,178 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-06 16:09:55,178 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-07-06 16:09:55,178 INFO sqlalchemy.engine.Engine [cached since 0.8181s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.8181s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:backend.database:Nuova sessione del database creata
Utente a autenticato con successo
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1751897169}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-07-06 16:09:55,182 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-06 16:09:55,182 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
2025-07-06 16:09:55,183 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-07-06 16:09:55,183 INFO sqlalchemy.engine.Engine [cached since 0.3904s ago] {'id_cantiere_1': 1, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.3904s ago] {'id_cantiere_1': 1, 'param_1': 1}
2025-07-06 16:09:55,183 INFO sqlalchemy.engine.Engine [cached since 0.8227s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.8227s ago] {'id_utente_1': 2, 'param_1': 1}
2025-07-06 16:09:55,184 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s) AS anon_1
Utente a autenticato con successo
2025-07-06 16:09:55,185 INFO sqlalchemy.engine.Engine [cached since 0.3804s ago] {'id_cantiere_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.3804s ago] {'id_cantiere_1': 1}
2025-07-06 16:09:55,187 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
2025-07-06 16:09:55,188 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_installazione = %(stato_installazione_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_installazione = %(stato_installazione_1)s) AS anon_1
2025-07-06 16:09:55,188 INFO sqlalchemy.engine.Engine [cached since 0.3956s ago] {'id_cantiere_1': 4, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.3956s ago] {'id_cantiere_1': 4, 'param_1': 1}
2025-07-06 16:09:55,188 INFO sqlalchemy.engine.Engine [cached since 0.3728s ago] {'id_cantiere_1': 1, 'stato_installazione_1': 'Installato'}
INFO:sqlalchemy.engine.Engine:[cached since 0.3728s ago] {'id_cantiere_1': 1, 'stato_installazione_1': 'Installato'}
2025-07-06 16:09:55,190 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s) AS anon_1
2025-07-06 16:09:55,191 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.collegamenti > %(collegamenti_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.collegamenti > %(collegamenti_1)s) AS anon_1
2025-07-06 16:09:55,191 INFO sqlalchemy.engine.Engine [cached since 0.3868s ago] {'id_cantiere_1': 4}
INFO:sqlalchemy.engine.Engine:[cached since 0.3868s ago] {'id_cantiere_1': 4}
2025-07-06 16:09:55,191 INFO sqlalchemy.engine.Engine [cached since 0.3668s ago] {'id_cantiere_1': 1, 'collegamenti_1': 0}
INFO:sqlalchemy.engine.Engine:[cached since 0.3668s ago] {'id_cantiere_1': 1, 'collegamenti_1': 0}
2025-07-06 16:09:55,193 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_installazione = %(stato_installazione_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_installazione = %(stato_installazione_1)s) AS anon_1
2025-07-06 16:09:55,194 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_certificazione = %(stato_certificazione_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_certificazione = %(stato_certificazione_1)s) AS anon_1
2025-07-06 16:09:55,194 INFO sqlalchemy.engine.Engine [cached since 0.3788s ago] {'id_cantiere_1': 4, 'stato_installazione_1': 'Installato'}
INFO:sqlalchemy.engine.Engine:[cached since 0.3788s ago] {'id_cantiere_1': 4, 'stato_installazione_1': 'Installato'}
2025-07-06 16:09:55,194 INFO sqlalchemy.engine.Engine [cached since 0.3594s ago] {'id_cantiere_1': 1, 'stato_certificazione_1': 'Certificato'}
INFO:sqlalchemy.engine.Engine:[cached since 0.3594s ago] {'id_cantiere_1': 1, 'stato_certificazione_1': 'Certificato'}
2025-07-06 16:09:55,195 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.collegamenti > %(collegamenti_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.collegamenti > %(collegamenti_1)s) AS anon_1
DEBUG IAP - Cantiere 1: total=95, installati=49, collegati=5, certificati=0
DEBUG IAP - Calcolo: sforzo_solo_installati=88.0, sforzo_solo_collegati=17.5, sforzo_certificati=0.0
DEBUG IAP - Risultato: numeratore=105.5, denominatore=380.0, IAP=27.76%
2025-07-06 16:09:55,196 INFO sqlalchemy.engine.Engine [cached since 0.3712s ago] {'id_cantiere_1': 4, 'collegamenti_1': 0}
INFO:sqlalchemy.engine.Engine:[cached since 0.3712s ago] {'id_cantiere_1': 4, 'collegamenti_1': 0}
INFO:backend.database:Sessione del database chiusa
2025-07-06 16:09:55,197 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-06 16:09:55,198 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_certificazione = %(stato_certificazione_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1 
FROM (SELECT cavi.id_cavo AS cavi_id_cavo, cavi.id_cantiere AS cavi_id_cantiere, cavi.revisione_ufficiale AS cavi_revisione_ufficiale, cavi.sistema AS cavi_sistema, cavi.utility AS cavi_utility, cavi.colore_cavo AS cavi_colore_cavo, cavi.tipologia AS cavi_tipologia, cavi.n_conduttori AS cavi_n_conduttori, cavi.sezione AS cavi_sezione, cavi.sh AS cavi_sh, cavi.ubicazione_partenza AS cavi_ubicazione_partenza, cavi.utenza_partenza AS cavi_utenza_partenza, cavi.descrizione_utenza_partenza AS cavi_descrizione_utenza_partenza, cavi.ubicazione_arrivo AS cavi_ubicazione_arrivo, cavi.utenza_arrivo AS cavi_utenza_arrivo, cavi.descrizione_utenza_arrivo AS cavi_descrizione_utenza_arrivo, cavi.metri_teorici AS cavi_metri_teorici, cavi.metratura_reale AS cavi_metratura_reale, cavi.responsabile_posa AS cavi_responsabile_posa, cavi.id_bobina AS cavi_id_bobina, cavi.stato_installazione AS cavi_stato_installazione, cavi.modificato_manualmente AS cavi_modificato_manualmente, cavi.timestamp AS cavi_timestamp, cavi.collegamenti AS cavi_collegamenti, cavi.responsabile_partenza AS cavi_responsabile_partenza, cavi.responsabile_arrivo AS cavi_responsabile_arrivo, cavi.comanda_posa AS cavi_comanda_posa, cavi.comanda_partenza AS cavi_comanda_partenza, cavi.comanda_arrivo AS cavi_comanda_arrivo, cavi.comanda_certificazione AS cavi_comanda_certificazione, cavi.stato_certificazione AS cavi_stato_certificazione, cavi.data_certificazione_cavo AS cavi_data_certificazione_cavo, cavi.numero_persone_impiegate AS cavi_numero_persone_impiegate, cavi.sistemazione AS cavi_sistemazione, cavi.fascettatura AS cavi_fascettatura, cavi.ore_lavoro_posa AS cavi_ore_lavoro_posa, cavi.data_inizio_lavoro AS cavi_data_inizio_lavoro, cavi.data_fine_lavoro AS cavi_data_fine_lavoro, cavi.ore_lavoro_collegamento_partenza AS cavi_ore_lavoro_collegamento_partenza, cavi.ore_lavoro_collegamento_arrivo AS cavi_ore_lavoro_collegamento_arrivo, cavi.ore_lavoro_certificazione AS cavi_ore_lavoro_certificazione, cavi.id_tipologia_cavo AS cavi_id_tipologia_cavo 
FROM cavi 
WHERE cavi.id_cantiere = %(id_cantiere_1)s AND cavi.stato_certificazione = %(stato_certificazione_1)s) AS anon_1
2025-07-06 16:09:55,199 INFO sqlalchemy.engine.Engine [cached since 0.3637s ago] {'id_cantiere_1': 4, 'stato_certificazione_1': 'Certificato'}
INFO:sqlalchemy.engine.Engine:[cached since 0.3637s ago] {'id_cantiere_1': 4, 'stato_certificazione_1': 'Certificato'}
INFO:     127.0.0.1:54223 - "GET /api/cantieri/1/statistics HTTP/1.1" 200 OK
INFO:backend.database:Sessione del database chiusa
2025-07-06 16:09:55,201 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:54227 - "GET /api/cantieri/4/statistics HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1751897169}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-07-06 16:09:57,718 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-06 16:09:57,719 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-07-06 16:09:57,719 INFO sqlalchemy.engine.Engine [cached since 3.359s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 3.359s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
2025-07-06 16:09:57,722 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
2025-07-06 16:09:57,722 INFO sqlalchemy.engine.Engine [cached since 2.93s ago] {'id_cantiere_1': 1, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 2.93s ago] {'id_cantiere_1': 1, 'param_1': 1}
INFO:backend.database:Sessione del database chiusa
2025-07-06 16:09:57,724 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:54223 - "GET /api/cantieri/1 HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1751897169}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-07-06 16:09:57,890 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-06 16:09:57,891 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-07-06 16:09:57,891 INFO sqlalchemy.engine.Engine [cached since 3.53s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 3.53s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
2025-07-06 16:09:57,893 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
2025-07-06 16:09:57,893 INFO sqlalchemy.engine.Engine [cached since 3.101s ago] {'id_cantiere_1': 1, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 3.101s ago] {'id_cantiere_1': 1, 'param_1': 1}
INFO:backend.database:Sessione del database chiusa
2025-07-06 16:09:57,896 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:54223 - "GET /api/cantieri/1 HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1751897169}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-07-06 16:09:58,233 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-06 16:09:58,233 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-07-06 16:09:58,234 INFO sqlalchemy.engine.Engine [cached since 3.873s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 3.873s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
API get_cavi chiamata con cantiere_id=1, tipo_cavo=None, stato_installazione=None, tipologia=None, sort_by=None, sort_order=asc, utente=a
Tipo di cantiere_id: <class 'int'>, valore: 1
cantiere_id convertito a intero: 1
2025-07-06 16:09:58,236 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
2025-07-06 16:09:58,236 INFO sqlalchemy.engine.Engine [cached since 3.444s ago] {'id_cantiere_1': 1, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 3.444s ago] {'id_cantiere_1': 1, 'param_1': 1}
Query cantiere eseguita: SELECT * FROM cantieri WHERE id_cantiere = 1
Cantiere trovato: a (ID: 1)
Verifica permessi: utente_id=2, cantiere.id_utente=2, ruolo=user
Esecuzione query cavi per cantiere_id=1
Query SQL generata: 
                SELECT c.id_cavo, c.id_cantiere, c.revisione_ufficiale, c.sistema, c.utility, c.colore_cavo, c.tipologia,
                       c.n_conduttori, c.sezione, c.sh, c.ubicazione_partenza, c.utenza_partenza, c.descrizione_utenza_partenza,
                       c.ubicazione_arrivo, c.utenza_arrivo, c.descrizione_utenza_arrivo, c.metri_teorici, c.metratura_reale,
                       c.responsabile_posa, c.id_bobina, c.stato_installazione, c.modificato_manualmente, c.timestamp,
                       c.collegamenti, c.responsabile_partenza, c.responsabile_arrivo, c.comanda_posa, c.comanda_partenza, c.comanda_arrivo,
                       CASE WHEN cert.id_certificazione IS NOT NULL THEN true ELSE false END as certificato
                FROM cavi c
                LEFT JOIN certificazionicavi cert ON c.id_cavo = cert.id_cavo AND c.id_cantiere = cert.id_cantiere
                WHERE c.id_cantiere = :cantiere_id
             ORDER BY id_cavo
2025-07-06 16:09:58,238 INFO sqlalchemy.engine.Engine 
                SELECT c.id_cavo, c.id_cantiere, c.revisione_ufficiale, c.sistema, c.utility, c.colore_cavo, c.tipologia,
                       c.n_conduttori, c.sezione, c.sh, c.ubicazione_partenza, c.utenza_partenza, c.descrizione_utenza_partenza,
                       c.ubicazione_arrivo, c.utenza_arrivo, c.descrizione_utenza_arrivo, c.metri_teorici, c.metratura_reale,
                       c.responsabile_posa, c.id_bobina, c.stato_installazione, c.modificato_manualmente, c.timestamp,
                       c.collegamenti, c.responsabile_partenza, c.responsabile_arrivo, c.comanda_posa, c.comanda_partenza, c.comanda_arrivo,
                       CASE WHEN cert.id_certificazione IS NOT NULL THEN true ELSE false END as certificato
                FROM cavi c
                LEFT JOIN certificazionicavi cert ON c.id_cavo = cert.id_cavo AND c.id_cantiere = cert.id_cantiere
                WHERE c.id_cantiere = %(cantiere_id)s
             ORDER BY id_cavo
INFO:sqlalchemy.engine.Engine:
                SELECT c.id_cavo, c.id_cantiere, c.revisione_ufficiale, c.sistema, c.utility, c.colore_cavo, c.tipologia,
                       c.n_conduttori, c.sezione, c.sh, c.ubicazione_partenza, c.utenza_partenza, c.descrizione_utenza_partenza,
                       c.ubicazione_arrivo, c.utenza_arrivo, c.descrizione_utenza_arrivo, c.metri_teorici, c.metratura_reale,
                       c.responsabile_posa, c.id_bobina, c.stato_installazione, c.modificato_manualmente, c.timestamp,
                       c.collegamenti, c.responsabile_partenza, c.responsabile_arrivo, c.comanda_posa, c.comanda_partenza, c.comanda_arrivo,
                       CASE WHEN cert.id_certificazione IS NOT NULL THEN true ELSE false END as certificato
                FROM cavi c
                LEFT JOIN certificazionicavi cert ON c.id_cavo = cert.id_cavo AND c.id_cantiere = cert.id_cantiere
                WHERE c.id_cantiere = %(cantiere_id)s
             ORDER BY id_cavo
2025-07-06 16:09:58,238 INFO sqlalchemy.engine.Engine [generated in 0.00018s] {'cantiere_id': 1}
INFO:sqlalchemy.engine.Engine:[generated in 0.00018s] {'cantiere_id': 1}
INFO:backend.database:Sessione del database chiusa
Trovati 95 cavi per il cantiere 1 con filtro tipo_cavo=None
Tipo di dato restituito: <class 'sqlalchemy.engine.row.Row'>
Esempio di riga: ('C001', 1, 'REV1', 'Lighting', 'Signal', 'Grigio', 'LIYCY', '0', '3X2.5MM2+2.5YG', 'N', 'Quadro Secondario', 'QP-92', 'Quadro piano 1', 'Pompa V1', 'UT-97', 'Utenza piano 2', 71.5, 30.0, '', 'C1_B2', 'SPARE', 3, datetime.datetime(2025, 7, 2, 5, 47, 45, 685483), 0, None, None, None, None, None, True)
Nomi delle colonne: RMKeyView(['id_cavo', 'id_cantiere', 'revisione_ufficiale', 'sistema', 'utility', 'colore_cavo', 'tipologia', 'n_conduttori', 'sezione', 'sh', 'ubicazione_partenza', 'utenza_partenza', 'descrizione_utenza_partenza', 'ubicazione_arrivo', 'utenza_arrivo', 'descrizione_utenza_arrivo', 'metri_teorici', 'metratura_reale', 'responsabile_posa', 'id_bobina', 'stato_installazione', 'modificato_manualmente', 'timestamp', 'collegamenti', 'responsabile_partenza', 'responsabile_arrivo', 'comanda_posa', 'comanda_partenza', 'comanda_arrivo', 'certificato'])
Convertiti 95 cavi in dizionari
Primi cavi trovati:
  1. ID: C001, Stato: SPARE
  2. ID: C002, Stato: Installato
  3. ID: C004, Stato: Installato
  4. ID: C005, Stato: Installato
  5. ID: C006, Stato: Da installare
2025-07-06 16:09:58,253 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:54223 - "GET /api/cavi/1 HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': 1751897169}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-07-06 16:09:58,421 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-06 16:09:58,421 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-07-06 16:09:58,421 INFO sqlalchemy.engine.Engine [cached since 4.061s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 4.061s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
2025-07-06 16:09:58,423 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_cantiere = %(id_cantiere_1)s 
 LIMIT %(param_1)s
2025-07-06 16:09:58,423 INFO sqlalchemy.engine.Engine [cached since 3.631s ago] {'id_cantiere_1': 1, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 3.631s ago] {'id_cantiere_1': 1, 'param_1': 1}
2025-07-06 16:09:58,424 INFO sqlalchemy.engine.Engine 
                SELECT revisione_ufficiale
                FROM cavi
                WHERE id_cantiere = %(cantiere_id)s
                AND revisione_ufficiale IS NOT NULL
                AND revisione_ufficiale != ''
                AND revisione_ufficiale != '00'
                AND revisione_ufficiale != 'TBD'
                ORDER BY revisione_ufficiale DESC
                LIMIT 1
            
INFO:sqlalchemy.engine.Engine:
                SELECT revisione_ufficiale
                FROM cavi
                WHERE id_cantiere = %(cantiere_id)s
                AND revisione_ufficiale IS NOT NULL
                AND revisione_ufficiale != ''
                AND revisione_ufficiale != '00'
                AND revisione_ufficiale != 'TBD'
                ORDER BY revisione_ufficiale DESC
                LIMIT 1
            
2025-07-06 16:09:58,424 INFO sqlalchemy.engine.Engine [generated in 0.00013s] {'cantiere_id': 1}
INFO:sqlalchemy.engine.Engine:[generated in 0.00013s] {'cantiere_id': 1}
INFO:backend.database:Sessione del database chiusa
2025-07-06 16:09:58,425 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:54242 - "GET /api/cavi/1/revisione-corrente HTTP/1.1" 200 OK
