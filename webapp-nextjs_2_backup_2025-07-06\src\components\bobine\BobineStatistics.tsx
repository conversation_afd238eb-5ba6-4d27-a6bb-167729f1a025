'use client'

import { useMemo } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Package, 
  CheckCircle, 
  Clock, 
  AlertTriangle,
  AlertCircle,
  BarChart3
} from 'lucide-react'

interface Bob<PERSON> {
  id_bobina: string
  numero_bobina: string
  utility: string
  tipologia: string
  sezione: string
  metri_totali: number
  metri_residui: number
  stato_bobina: string
  ubicazione_bobina?: string
  fornitore?: string
  n_DDT?: string
  data_DDT?: string
  configurazione?: string
}

interface BobineStatisticsProps {
  bobine: Bobina[]
  filteredBobine: Bobina[]
  className?: string
}

export default function BobineStatistics({
  bobine,
  filteredBobine,
  className
}: BobineStatisticsProps) {
  const stats = useMemo(() => {
    const totalBobine = bobine.length
    const filteredCount = filteredBobine.length
    
    // Stati bobine
    const disponibili = filteredBobine.filter(b => 
      b.stato_bobina === 'Disponibile'
    ).length
    
    const inUso = filteredBobine.filter(b => 
      b.stato_bobina === 'In uso'
    ).length
    
    const terminate = filteredBobine.filter(b => 
      b.stato_bobina === 'Terminata'
    ).length
    
    const over = filteredBobine.filter(b => 
      b.stato_bobina === 'Over'
    ).length
    
    // Calcoli metrature
    const metriTotali = filteredBobine.reduce((sum, b) => sum + (b.metri_totali || 0), 0)
    const metriResidui = filteredBobine.reduce((sum, b) => sum + (b.metri_residui || 0), 0)
    const metriUtilizzati = metriTotali - metriResidui
    
    // Percentuale utilizzo
    const percentualeUtilizzo = metriTotali > 0 ? Math.round((metriUtilizzati / metriTotali) * 100) : 0
    
    return {
      totalBobine,
      filteredCount,
      disponibili,
      inUso,
      terminate,
      over,
      metriTotali,
      metriResidui,
      metriUtilizzati,
      percentualeUtilizzo
    }
  }, [bobine, filteredBobine])

  return (
    <Card className={className}>
      <CardContent className="p-1.5">
        {/* Header */}
        <div className="flex items-center justify-between mb-1">
          <div className="flex items-center space-x-1.5">
            <BarChart3 className="h-3.5 w-3.5 text-mariner-600" />
            <span className="text-xs font-semibold text-mariner-900">Statistiche Bobine</span>
          </div>
        </div>

        {/* Statistics distributed across full width */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2">

          {/* Total bobine */}
          <div className="flex items-center space-x-1.5 bg-mariner-50 px-1.5 py-1 rounded-lg">
            <Package className="h-3.5 w-3.5 text-mariner-600" />
            <div>
              <div className="font-bold text-mariner-900 text-sm">{stats.filteredCount}</div>
              <div className="text-xs text-mariner-600">di {stats.totalBobine} bobine</div>
            </div>
          </div>

          {/* Disponibili */}
          <div className="flex items-center space-x-1.5 bg-green-50 px-1.5 py-1 rounded-lg">
            <CheckCircle className="h-3.5 w-3.5 text-green-600" />
            <div>
              <div className="font-bold text-green-700 text-sm">{stats.disponibili}</div>
              <div className="text-xs text-green-600">disponibili</div>
            </div>
          </div>

          {/* In uso */}
          <div className="flex items-center space-x-1.5 bg-yellow-50 px-1.5 py-1 rounded-lg">
            <Clock className="h-3.5 w-3.5 text-yellow-600" />
            <div>
              <div className="font-bold text-yellow-700 text-sm">{stats.inUso}</div>
              <div className="text-xs text-yellow-600">in uso</div>
            </div>
          </div>

          {/* Terminate */}
          <div className="flex items-center space-x-1.5 bg-red-50 px-1.5 py-1 rounded-lg">
            <AlertTriangle className="h-3.5 w-3.5 text-red-600" />
            <div>
              <div className="font-bold text-red-700 text-sm">{stats.terminate}</div>
              <div className="text-xs text-red-600">terminate</div>
            </div>
          </div>

          {/* Over */}
          <div className="flex items-center space-x-1.5 bg-red-50 px-1.5 py-1 rounded-lg">
            <AlertCircle className="h-3.5 w-3.5 text-red-600" />
            <div>
              <div className="font-bold text-red-700 text-sm">{stats.over}</div>
              <div className="text-xs text-red-600">over</div>
            </div>
          </div>

          {/* Meters progress */}
          <div className="flex items-center space-x-1.5 bg-indigo-50 px-1.5 py-1 rounded-lg">
            <div className="h-3.5 w-3.5 flex items-center justify-center">
              <div className="h-2 w-2 bg-indigo-600 rounded-full"></div>
            </div>
            <div>
              <div className="font-bold text-indigo-700 text-sm">{stats.metriUtilizzati.toLocaleString()}m</div>
              <div className="text-xs text-indigo-600">di {stats.metriTotali.toLocaleString()}m</div>
            </div>
          </div>

        </div>

        {/* Utilizzo Progress bar - Colori morbidi */}
        {stats.filteredCount > 0 && (
          <div className="mt-2 bg-gray-50 p-2 rounded-lg">
            <div className="flex justify-between text-xs font-medium text-gray-700 mb-1">
              <span>Utilizzo Complessivo Bobine</span>
              <span className={`font-bold ${
                stats.percentualeUtilizzo >= 80 ? 'text-amber-700' :
                stats.percentualeUtilizzo >= 60 ? 'text-orange-700' :
                stats.percentualeUtilizzo >= 40 ? 'text-yellow-700' : 'text-emerald-700'
              }`}>
                {stats.percentualeUtilizzo}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-500 ease-in-out ${
                  stats.percentualeUtilizzo >= 80 ? 'bg-gradient-to-r from-amber-500 to-amber-600' :
                  stats.percentualeUtilizzo >= 60 ? 'bg-gradient-to-r from-orange-500 to-orange-600' :
                  stats.percentualeUtilizzo >= 40 ? 'bg-gradient-to-r from-yellow-500 to-yellow-600' :
                  'bg-gradient-to-r from-emerald-500 to-emerald-600'
                }`}
                style={{ width: `${Math.min(stats.percentualeUtilizzo, 100)}%` }}
              />
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-0.5">
              <span>Metri utilizzati vs totali disponibili</span>
              <span>{stats.metriResidui.toLocaleString()}m residui</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
