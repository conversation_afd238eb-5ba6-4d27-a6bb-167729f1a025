'use client'

import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Button } from '@/components/ui/button'
import { LogOut, Loader2 } from 'lucide-react'

interface LogoutConfirmDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm?: () => void
}

export default function LogoutConfirmDialog({
  isOpen,
  onClose,
  onConfirm
}: LogoutConfirmDialogProps) {
  const { logout, user, cantiere, isImpersonating, impersonatedUser } = useAuth()
  const [isLoggingOut, setIsLoggingOut] = useState(false)

  const handleLogout = async () => {
    setIsLoggingOut(true)
    
    try {
      if (onConfirm) {
        onConfirm()
      }
      
      // <PERSON><PERSON><PERSON> delay per mostrare il loading
      await new Promise(resolve => setTimeout(resolve, 500))
      
      logout()
    } catch (error) {
      console.error('Errore durante logout:', error)
      setIsLoggingOut(false)
    }
  }

  const getLogoutMessage = () => {
    if (isImpersonating && impersonatedUser) {
      return `Vuoi tornare al menu amministratore? Stai attualmente impersonando l'utente "${impersonatedUser.username}".`
    }
    
    if (user) {
      return `Sei sicuro di voler uscire? Sarai disconnesso dall'account "${user.username}".`
    }
    
    if (cantiere) {
      return `Sei sicuro di voler uscire? Sarai disconnesso dal cantiere "${cantiere.commessa}".`
    }
    
    return 'Sei sicuro di voler uscire?'
  }

  const getLogoutButtonText = () => {
    if (isImpersonating) {
      return 'Torna al menu admin'
    }
    
    return 'Esci'
  }

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center space-x-2">
            <LogOut className="w-5 h-5 text-red-600" />
            <span>Conferma logout</span>
          </AlertDialogTitle>
          <AlertDialogDescription className="text-base">
            {getLogoutMessage()}
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <AlertDialogFooter>
          <AlertDialogCancel 
            onClick={onClose}
            disabled={isLoggingOut}
          >
            Annulla
          </AlertDialogCancel>
          
          <AlertDialogAction
            onClick={handleLogout}
            disabled={isLoggingOut}
            className="bg-red-600 hover:bg-red-700 focus:ring-red-500"
          >
            {isLoggingOut ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Disconnessione...
              </>
            ) : (
              <>
                <LogOut className="w-4 h-4 mr-2" />
                {getLogoutButtonText()}
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
