(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3698],{7958:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>ei});var s=t(95155),r=t(12115);t(40975);var i=t(66695),l=t(59434),n=t(51154);let d=r.forwardRef((e,a)=>{let{className:t,variant:r="primary",size:i="md",loading:d=!1,glow:o=!1,icon:c,children:m,disabled:x,...u}=e,h=x||d;return(0,s.jsxs)("button",{className:(0,l.cn)("relative overflow-hidden font-medium rounded-lg transition-all duration-300 ease-in-out transform focus:outline-none",{primary:"btn-primary",secondary:"btn-secondary",success:"btn-success",danger:"btn-danger",outline:"btn-outline",quick:"btn-quick"}[r],{sm:"btn-sm",md:"px-6 py-3",lg:"btn-lg"}[i],o&&"quick"!==r&&"btn-glow",h&&"opacity-50 cursor-not-allowed hover:shadow-none",t),disabled:h,ref:a,...u,children:[(0,s.jsx)("span",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent translate-x-[-100%] transition-transform duration-700 ease-in-out group-hover:translate-x-[100%]"}),(0,s.jsxs)("span",{className:"relative flex items-center justify-center gap-2",children:[d?(0,s.jsx)(n.A,{className:"h-4 w-4 animate-spin btn-icon"}):c?(0,s.jsx)("span",{className:"btn-icon",children:c}):null,m]})]})});d.displayName="AnimatedButton";let o=e=>(0,s.jsx)(d,{variant:"primary",...e}),c=e=>(0,s.jsx)(d,{variant:"secondary",...e}),m=e=>(0,s.jsx)(d,{variant:"danger",...e});var x=t(1243),u=t(54416),h=t(13717),p=t(14186),g=t(40646),b=t(62525),v=t(30285);function j(e){let{user:a,onEdit:t,onToggleStatus:i,onDelete:l}=e,[n,d]=(0,r.useState)(!1);return n?(0,s.jsxs)("div",{className:"flex items-center gap-1 bg-red-50 border border-red-200 rounded-md p-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 text-red-600"}),(0,s.jsx)("span",{className:"text-xs text-red-700 font-medium",children:"Eliminare?"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1 ml-2",children:[(0,s.jsx)(v.$,{size:"sm",variant:"destructive",onClick:e=>{e.preventDefault(),e.stopPropagation(),d(!1),l()},className:"h-6 px-2 text-xs",children:"S\xec"}),(0,s.jsx)(v.$,{size:"sm",variant:"outline",onClick:e=>{e.preventDefault(),e.stopPropagation(),d(!1)},className:"h-6 px-2 text-xs",children:(0,s.jsx)(u.A,{className:"h-3 w-3"})})]})]}):(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),t()},type:"button",className:"p-1.5 rounded-md hover:bg-blue-50 transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1","aria-label":"Modifica utente ".concat(a.username),children:(0,s.jsx)(h.A,{className:"h-4 w-4 text-blue-600 hover:text-blue-700"})}),(0,s.jsx)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-slate-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10",children:"Modifica utente"})]}),(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),(!a.abilitato||window.confirm("Sei sicuro di voler disabilitare l'utente \"".concat(a.username,"\"?\n\nL'utente non potr\xe0 pi\xf9 accedere al sistema.")))&&i()},disabled:"owner"===a.ruolo,type:"button",className:"p-1.5 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 ".concat("owner"===a.ruolo?"opacity-50 cursor-not-allowed":"hover:scale-105 hover:bg-slate-50 focus:ring-slate-500"),"aria-label":a.abilitato?"Disabilita utente ".concat(a.username):"Abilita utente ".concat(a.username),children:a.abilitato?(0,s.jsx)(p.A,{className:"h-4 w-4 text-red-500 hover:text-red-600"}):(0,s.jsx)(g.A,{className:"h-4 w-4 text-green-500 hover:text-green-600"})}),"owner"!==a.ruolo&&(0,s.jsx)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-slate-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10",children:a.abilitato?"Disabilita utente":"Abilita utente"})]}),(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),d(!0)},disabled:"owner"===a.ruolo,type:"button",className:"p-1.5 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 ".concat("owner"===a.ruolo?"opacity-50 cursor-not-allowed":"hover:scale-105 hover:bg-red-50 focus:ring-red-500"),"aria-label":"Elimina utente ".concat(a.username),children:(0,s.jsx)(b.A,{className:"h-4 w-4 text-red-500 hover:text-red-600"})}),"owner"!==a.ruolo&&(0,s.jsx)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-slate-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10",children:"Elimina utente"})]})]})}var f=t(26126),N=t(62523),w=t(63743),A=t(85127),y=t(17313),z=t(40283),S=t(35695),E=t(25731),T=t(85057),R=t(59409),C=t(47262),_=t(61610),k=t(85339),U=t(71007),I=t(75525),D=t(78749),L=t(92657),V=t(23227),O=t(28883),P=t(4516),G=t(381),Z=t(81284),F=t(4229);function B(e){let{user:a,onSave:t,onCancel:l}=e,[n,d]=(0,r.useState)({username:"",password:"",ruolo:"user",data_scadenza:"",abilitato:!0,ragione_sociale:"",indirizzo:"",nazione:"",email:"",vat:"",referente_aziendale:""}),[m,x]=(0,r.useState)({}),[b,j]=(0,r.useState)(!1),[w,A]=(0,r.useState)(""),[y,z]=(0,r.useState)(""),[S,B]=(0,r.useState)(!1),[M,W]=(0,r.useState)(0),[X,$]=(0,r.useState)({}),[J,q]=(0,r.useState)(!1),H=e=>{let a=0;return e.length>=8&&(a+=25),/[a-z]/.test(e)&&(a+=25),/[A-Z]/.test(e)&&(a+=25),/[0-9]/.test(e)&&(a+=25),/[^A-Za-z0-9]/.test(e)&&(a+=25),Math.min(a,100)},Q=(e,t)=>{let s="pending";switch(e){case"username":s=t&&t.length>=3?"valid":"invalid";break;case"password":s=a?!t||t.length>=8?"valid":"invalid":t&&t.length>=8?"valid":"invalid";break;case"ragione_sociale":s=t&&t.length>=2?"valid":"invalid";break;case"email":s=t?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t)?"valid":"invalid":"valid";break;default:s="valid"}return $(a=>({...a,[e]:s})),s};(0,r.useEffect)(()=>{if(a){let e={username:a.username||"",password:"",ruolo:a.ruolo||"user",data_scadenza:a.data_scadenza?a.data_scadenza.split("T")[0]:"",abilitato:void 0===a.abilitato||a.abilitato,ragione_sociale:a.ragione_sociale||"",indirizzo:a.indirizzo||"",nazione:a.nazione||"",email:a.email||"",vat:a.vat||"",referente_aziendale:a.referente_aziendale||""};d(e),Object.entries(e).forEach(e=>{let[a,t]=e;Q(a,t)})}},[a]),(0,r.useEffect)(()=>{q((a?["username","ragione_sociale"]:["username","password","ragione_sociale"]).every(e=>"valid"===X[e]))},[X,a]);let Y=(e,a)=>{d(t=>({...t,[e]:a})),Q(e,a),"password"===e&&W(H(a)),m[e]&&x(a=>({...a,[e]:""})),y&&z(""),w&&A("")},K=()=>{let e=(0,_.GN)({username:n.username,password:a?void 0:n.password,ragione_sociale:n.ragione_sociale,email:n.email,vat:n.vat,indirizzo:n.indirizzo,nazione:n.nazione,referente_aziendale:n.referente_aziendale});return x(e.errors),e.isValid},ee=async e=>{e.preventDefault();let s="user-form-".concat((null==a?void 0:a.id_utente)||"new","-").concat(Date.now());if(!(0,_.Eb)(s,5,6e4))return void A("Troppi tentativi. Riprova tra un minuto.");if(K()){j(!0),A("");try{let e,s={...n};a||(s.ruolo="user"),a&&!s.password.trim()&&delete s.password,s.data_scadenza&&(s.data_scadenza=s.data_scadenza),e=a?await E.dG.updateUser(a.id_utente,s):await E.dG.createUser(s),z(a?"Utente aggiornato con successo!":"Nuovo utente creato con successo!"),setTimeout(()=>{t(e)},1500)}catch(e){var r,i;A((null==(i=e.response)||null==(r=i.data)?void 0:r.detail)||e.message||"Errore durante il salvataggio dell'utente")}finally{j(!1)}}},ea=e=>{let{status:a}=e;return"valid"===a?(0,s.jsx)(g.A,{className:"h-4 w-4 text-green-500"}):"invalid"===a?(0,s.jsx)(k.A,{className:"h-4 w-4 text-red-500"}):null};return(0,s.jsxs)(i.Zp,{className:"shadow-lg",children:[(0,s.jsx)(i.aR,{className:"bg-gradient-to-r from-blue-50 to-indigo-50 border-b",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:a?(0,s.jsx)(h.A,{className:"h-5 w-5 text-blue-600"}):(0,s.jsx)(U.A,{className:"h-5 w-5 text-blue-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)(i.ZB,{className:"text-xl",children:a?"Modifica Utente: ".concat(a.username):"Crea Nuovo Utente Standard"}),(0,s.jsx)(i.BT,{children:a?"Aggiorna le informazioni dell'utente esistente":"Inserisci i dati per creare un nuovo utente nel sistema"})]})]})}),(0,s.jsxs)(i.Wu,{className:"p-6",children:[w&&(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6 flex items-center gap-3 animate-in slide-in-from-top-2",children:[(0,s.jsx)(k.A,{className:"h-5 w-5 text-red-600 flex-shrink-0"}),(0,s.jsx)("p",{className:"text-red-600",children:w})]}),y&&(0,s.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-6 flex items-center gap-3 animate-in slide-in-from-top-2",children:[(0,s.jsx)(g.A,{className:"h-5 w-5 text-green-600 flex-shrink-0"}),(0,s.jsx)("p",{className:"text-green-600",children:y})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm text-slate-600 mb-2",children:[(0,s.jsx)("span",{children:"Completamento form"}),(0,s.jsx)("span",{children:J?"✓ Completo":"In corso..."})]}),(0,s.jsx)("div",{className:"w-full bg-slate-200 rounded-full h-2",children:(0,s.jsx)("div",{className:"h-2 rounded-full transition-all duration-500 ".concat(J?"bg-green-500":"bg-blue-500"),style:{width:"".concat(J?100:60,"%")}})})]}),(0,s.jsxs)("form",{onSubmit:ee,className:"space-y-8",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 pb-2 border-b border-slate-200",children:[(0,s.jsx)(I.A,{className:"h-5 w-5 text-blue-600"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-slate-900",children:"Credenziali di Accesso"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(T.J,{htmlFor:"username",className:"flex items-center gap-2",children:["Username *",(0,s.jsx)(ea,{status:X.username||"pending"})]}),(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)(N.p,{id:"username",value:n.username,onChange:e=>Y("username",e.target.value),disabled:b,className:"".concat(m.username?"border-red-500":"valid"===X.username?"border-green-500":""," transition-colors duration-200"),placeholder:"Inserisci username univoco"})}),m.username&&(0,s.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,s.jsx)(k.A,{className:"h-3 w-3"}),m.username]}),"valid"===X.username&&!m.username&&(0,s.jsxs)("p",{className:"text-sm text-green-600 flex items-center gap-1",children:[(0,s.jsx)(g.A,{className:"h-3 w-3"}),"Username valido"]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(T.J,{htmlFor:"password",className:"flex items-center gap-2",children:[a?"Nuova Password (lascia vuoto per non modificare)":"Password *",(0,s.jsx)(ea,{status:X.password||"pending"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(N.p,{id:"password",type:S?"text":"password",value:n.password,onChange:e=>Y("password",e.target.value),disabled:b,className:"".concat(m.password?"border-red-500":"valid"===X.password?"border-green-500":""," pr-10 transition-colors duration-200"),placeholder:a?"Lascia vuoto per mantenere la password attuale":"Inserisci password sicura"}),(0,s.jsx)(v.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>B(!S),disabled:b,children:S?(0,s.jsx)(D.A,{className:"h-4 w-4 text-gray-400"}):(0,s.jsx)(L.A,{className:"h-4 w-4 text-gray-400"})})]}),m.password&&(0,s.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,s.jsx)(k.A,{className:"h-3 w-3"}),m.password]}),(0,s.jsx)(()=>n.password?(0,s.jsxs)("div",{className:"mt-2 space-y-1",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,s.jsx)("span",{className:"text-slate-600",children:"Forza password:"}),(0,s.jsx)("span",{className:"font-medium ".concat(M<50?"text-red-600":M<75?"text-yellow-600":"text-green-600"),children:M<25?"Molto debole":M<50?"Debole":M<75?"Media":M<100?"Forte":"Molto forte"})]}),(0,s.jsx)("div",{className:"w-full bg-slate-200 rounded-full h-2",children:(0,s.jsx)("div",{className:"h-2 rounded-full transition-all duration-300 ".concat(M<50?"bg-red-500":M<75?"bg-yellow-500":"bg-green-500"),style:{width:"".concat(M,"%")}})})]}):null,{})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 pb-2 border-b border-slate-200",children:[(0,s.jsx)(V.A,{className:"h-5 w-5 text-blue-600"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-slate-900",children:"Informazioni Aziendali"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(T.J,{htmlFor:"ragione_sociale",className:"flex items-center gap-2",children:["Ragione Sociale *",(0,s.jsx)(ea,{status:X.ragione_sociale||"pending"})]}),(0,s.jsx)(N.p,{id:"ragione_sociale",value:n.ragione_sociale,onChange:e=>Y("ragione_sociale",e.target.value),disabled:b,className:"".concat(m.ragione_sociale?"border-red-500":"valid"===X.ragione_sociale?"border-green-500":""," transition-colors duration-200"),placeholder:"Nome dell'azienda o organizzazione"}),m.ragione_sociale&&(0,s.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,s.jsx)(k.A,{className:"h-3 w-3"}),m.ragione_sociale]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(T.J,{htmlFor:"email",className:"flex items-center gap-2",children:[(0,s.jsx)(O.A,{className:"h-4 w-4"}),"Email",(0,s.jsx)(ea,{status:X.email||"pending"})]}),(0,s.jsx)(N.p,{id:"email",type:"email",value:n.email,onChange:e=>Y("email",e.target.value),disabled:b,className:"".concat(m.email?"border-red-500":"valid"===X.email?"border-green-500":""," transition-colors duration-200"),placeholder:"<EMAIL>"}),m.email&&(0,s.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,s.jsx)(k.A,{className:"h-3 w-3"}),m.email]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(T.J,{htmlFor:"indirizzo",className:"flex items-center gap-2",children:[(0,s.jsx)(P.A,{className:"h-4 w-4"}),"Indirizzo"]}),(0,s.jsx)(N.p,{id:"indirizzo",value:n.indirizzo,onChange:e=>Y("indirizzo",e.target.value),disabled:b,placeholder:"Via, numero civico, citt\xe0",className:"transition-colors duration-200"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"nazione",children:"Nazione"}),(0,s.jsx)(N.p,{id:"nazione",value:n.nazione,onChange:e=>Y("nazione",e.target.value),disabled:b,placeholder:"Italia",className:"transition-colors duration-200"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"vat",children:"Partita IVA"}),(0,s.jsx)(N.p,{id:"vat",value:n.vat,onChange:e=>Y("vat",e.target.value),disabled:b,placeholder:"*************",className:"transition-colors duration-200"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"referente_aziendale",children:"Referente Aziendale"}),(0,s.jsx)(N.p,{id:"referente_aziendale",value:n.referente_aziendale,onChange:e=>Y("referente_aziendale",e.target.value),disabled:b,placeholder:"Nome e cognome del referente",className:"transition-colors duration-200"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 pb-2 border-b border-slate-200",children:[(0,s.jsx)(G.A,{className:"h-5 w-5 text-blue-600"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-slate-900",children:"Configurazioni Account"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(T.J,{htmlFor:"data_scadenza",className:"flex items-center gap-2",children:[(0,s.jsx)(p.A,{className:"h-4 w-4"}),"Data Scadenza"]}),(0,s.jsx)(N.p,{id:"data_scadenza",type:"date",value:n.data_scadenza,onChange:e=>Y("data_scadenza",e.target.value),disabled:b,className:"transition-colors duration-200"}),(0,s.jsx)("p",{className:"text-xs text-slate-500",children:"Lascia vuoto per account senza scadenza"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"ruolo",children:"Ruolo Utente"}),a?(0,s.jsxs)(R.l6,{value:n.ruolo,onValueChange:e=>Y("ruolo",e),disabled:b,children:[(0,s.jsx)(R.bq,{className:"transition-colors duration-200",children:(0,s.jsx)(R.yv,{})}),(0,s.jsxs)(R.gC,{children:[(0,s.jsx)(R.eb,{value:"user",children:"User Standard"}),(0,s.jsx)(R.eb,{value:"cantieri_user",children:"Cantieri User"})]})]}):(0,s.jsxs)("div",{className:"px-3 py-2 bg-blue-50 border border-blue-200 rounded-md text-sm text-blue-700 flex items-center gap-2",children:[(0,s.jsx)(f.E,{variant:"outline",className:"bg-blue-100 text-blue-700",children:"User Standard"}),(0,s.jsx)("span",{children:"Ruolo predefinito per nuovi utenti"})]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3 p-4 bg-slate-50 rounded-lg",children:[(0,s.jsx)(C.S,{id:"abilitato",checked:n.abilitato,onCheckedChange:e=>Y("abilitato",e),disabled:b||a&&"owner"===a.ruolo}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)(T.J,{htmlFor:"abilitato",className:"font-medium",children:"Account Abilitato"}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"L'utente pu\xf2 accedere al sistema e utilizzare le funzionalit\xe0"})]}),n.abilitato?(0,s.jsx)(f.E,{className:"bg-green-100 text-green-700",children:"Attivo"}):(0,s.jsx)(f.E,{variant:"outline",className:"bg-red-100 text-red-700",children:"Disabilitato"})]})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-center gap-4 pt-8 border-t border-slate-200",children:[(0,s.jsx)("div",{className:"text-sm text-slate-600",children:J?(0,s.jsxs)("span",{className:"flex items-center gap-1 text-green-600",children:[(0,s.jsx)(g.A,{className:"h-4 w-4"}),"Form completato correttamente"]}):(0,s.jsxs)("span",{className:"flex items-center gap-1",children:[(0,s.jsx)(Z.A,{className:"h-4 w-4"}),"Completa i campi obbligatori per continuare"]})}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsx)(c,{type:"button",onClick:l,disabled:b,icon:(0,s.jsx)(u.A,{className:"h-4 w-4"}),className:"min-w-[120px]",children:"Annulla"}),(0,s.jsx)(o,{type:"submit",loading:b,disabled:!J,icon:(0,s.jsx)(F.A,{className:"h-4 w-4"}),glow:J,className:"min-w-[120px]",children:b?"Salvataggio...":a?"Aggiorna Utente":"Crea Utente"})]})]})]})]})]})}var M=t(54213),W=t(53904);function X(){let[e,a]=(0,r.useState)(null),[t,l]=(0,r.useState)(!1),[d,c]=(0,r.useState)(""),m=async()=>{l(!0),c("");try{let e=await E.dG.getDatabaseData();a(e)}catch(a){var e,t;c((null==(t=a.response)||null==(e=t.data)?void 0:e.detail)||a.message||"Errore durante il caricamento dei dati del database")}finally{l(!1)}};(0,r.useEffect)(()=>{m()},[]);let x=(e,a,t)=>{if(!a||0===a.length)return(0,s.jsxs)("div",{className:"text-center py-4 text-slate-500 border rounded-lg",children:["Nessun dato disponibile per ",t]});let r=Object.keys(a[0]);return(0,s.jsxs)("div",{className:"border rounded-lg overflow-hidden mb-6",children:[(0,s.jsxs)("div",{className:"bg-slate-100 px-4 py-3 border-b",children:[(0,s.jsx)("h4",{className:"font-medium text-slate-900",children:t}),(0,s.jsxs)("p",{className:"text-sm text-slate-600",children:["Totale record: ",a.length]})]}),(0,s.jsx)("div",{className:"overflow-x-auto max-h-96",children:(0,s.jsxs)(A.XI,{children:[(0,s.jsx)(A.A0,{className:"sticky top-0 bg-slate-50",children:(0,s.jsx)(A.Hj,{children:r.map(e=>(0,s.jsx)(A.nd,{className:"font-medium",children:e},e))})}),(0,s.jsx)(A.BF,{children:a.map((e,a)=>(0,s.jsx)(A.Hj,{children:r.map(a=>(0,s.jsx)(A.nA,{className:"font-mono text-sm",children:null!==e[a]&&void 0!==e[a]?String(e[a]):(0,s.jsx)("span",{className:"text-slate-400",children:"NULL"})},a))},a))})]})})]})},u=[{key:"users",title:"Utenti",description:"Tutti gli utenti del sistema"},{key:"cantieri",title:"Cantieri",description:"Tutti i cantieri/progetti"},{key:"cavi",title:"Cavi",description:"Tutti i cavi installati"},{key:"parco_cavi",title:"Bobine",description:"Tutte le bobine del parco cavi"},{key:"strumenti_certificati",title:"Strumenti",description:"Strumenti certificati"},{key:"certificazioni_cavi",title:"Certificazioni",description:"Certificazioni dei cavi"}];return(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(M.A,{className:"h-5 w-5"}),"Visualizzazione Database Raw"]}),(0,s.jsx)(o,{size:"sm",onClick:m,loading:t,icon:(0,s.jsx)(W.A,{className:"h-4 w-4"}),children:"Aggiorna"})]})}),(0,s.jsxs)(i.Wu,{className:"space-y-6",children:[(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(L.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-blue-900",children:"Visualizzazione Raw del Database"}),(0,s.jsx)("p",{className:"text-sm text-blue-700 mt-1",children:"Questa sezione mostra i dati grezzi delle tabelle del database. Utile per debugging e analisi dei dati."})]})]})}),t?(0,s.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,s.jsx)(n.A,{className:"h-8 w-8 animate-spin mr-3"}),(0,s.jsx)("span",{className:"text-lg",children:"Caricamento dati database..."})]}):d?(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:[(0,s.jsx)("p",{className:"text-red-600 font-medium",children:"Errore durante il caricamento:"}),(0,s.jsx)("p",{className:"text-red-600",children:d})]}):e?(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-sm text-blue-700",children:"Visualizzazione completa di tutte le tabelle del database. I dati sono mostrati in formato raw per debugging e analisi."})}),u.map(a=>e[a.key]&&(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-slate-900",children:a.title}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:a.description})]}),x(a.key,e[a.key],a.title)]},a.key)),(0,s.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,s.jsx)("h4",{className:"font-medium text-slate-900 mb-2",children:"Riepilogo Database"}),(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 text-sm",children:u.map(a=>(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsxs)("span",{className:"text-slate-600",children:[a.title,":"]}),(0,s.jsxs)("span",{className:"font-medium",children:[e[a.key]?e[a.key].length:0," record"]})]},a.key))})]})]}):(0,s.jsx)("div",{className:"text-center py-12 text-slate-500",children:"Nessun dato disponibile"})]})]})}var $=t(40133);function J(){let{user:e}=(0,z.A)(),[a,t]=(0,r.useState)(""),[l,n]=(0,r.useState)(!1),[d,o]=(0,r.useState)(""),[u,h]=(0,r.useState)(!1),[g,j]=(0,r.useState)(!1),[f,w]=(0,r.useState)(""),[A,y]=(0,r.useState)(""),[S,R]=(0,r.useState)(10),[_,k]=(0,r.useState)(!1),[U,V]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e;return _&&S>0?e=setInterval(()=>{R(e=>e-1)},1e3):0===S&&(V(!0),k(!1)),()=>clearInterval(e)},[_,S]);let O=async()=>{if(!U)return void w("Devi completare il countdown di sicurezza");j(!0),w(""),y("");try{if(!d.trim())throw Error("Password amministratore richiesta");await E.dG.resetDatabase(),y("Database resettato con successo! Tutti i dati sono stati eliminati."),t(""),n(!1),o(""),V(!1),k(!1),R(10)}catch(t){var e,a;w((null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||t.message||"Errore durante il reset del database")}finally{j(!1)}},P="RESET DATABASE"===a&&l&&d.trim()&&!g&&!_,G=U&&!g;return(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsxs)(i.ZB,{className:"flex items-center gap-2 text-red-600",children:[(0,s.jsx)($.A,{className:"h-5 w-5"}),"Reset Database"]})}),(0,s.jsxs)(i.Wu,{className:"space-y-6",children:[(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(x.A,{className:"h-6 w-6 text-red-600 mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-bold text-red-900 text-lg",children:"⚠️ ATTENZIONE - OPERAZIONE IRREVERSIBILE"}),(0,s.jsxs)("div",{className:"text-red-700 mt-2 space-y-2",children:[(0,s.jsx)("p",{className:"font-medium",children:"Questa operazione eliminer\xe0 PERMANENTEMENTE tutti i dati dal database:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm",children:[(0,s.jsx)("li",{children:"Tutti gli utenti (eccetto l'amministratore principale)"}),(0,s.jsx)("li",{children:"Tutti i cantieri e i progetti"}),(0,s.jsx)("li",{children:"Tutti i cavi installati"}),(0,s.jsx)("li",{children:"Tutte le bobine del parco cavi"}),(0,s.jsx)("li",{children:"Tutti i comandi e le certificazioni"}),(0,s.jsx)("li",{children:"Tutti i report e i dati di produttivit\xe0"})]}),(0,s.jsx)("p",{className:"font-bold text-red-800 mt-3",children:"NON \xc8 POSSIBILE RECUPERARE I DATI DOPO IL RESET!"})]})]})]})}),f&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-red-600",children:f})}),A&&(0,s.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-green-600",children:A})}),(0,s.jsxs)("div",{className:"space-y-4 border-t pt-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-slate-900 mb-4",children:"Conferma Reset Database"}),(0,s.jsx)("p",{className:"text-sm text-slate-600 mb-4",children:"Per procedere con il reset, devi confermare l'operazione seguendo questi passaggi:"})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(T.J,{htmlFor:"confirm-text",className:"text-sm font-medium",children:["1. Digita esattamente: ",(0,s.jsx)("code",{className:"bg-slate-100 px-2 py-1 rounded text-red-600 font-bold",children:"RESET DATABASE"})]}),(0,s.jsx)(N.p,{id:"confirm-text",value:a,onChange:e=>t(e.target.value),placeholder:"Digita: RESET DATABASE",disabled:g||_,className:"RESET DATABASE"===a?"border-green-500":""})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)(C.S,{id:"confirm-checkbox",checked:l,onCheckedChange:n,disabled:g||_}),(0,s.jsx)(T.J,{htmlFor:"confirm-checkbox",className:"text-sm leading-relaxed",children:"2. Confermo di aver compreso che questa operazione eliminer\xe0 TUTTI i dati dal database in modo PERMANENTE e IRREVERSIBILE. Ho effettuato un backup se necessario."})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(T.J,{htmlFor:"admin-password",className:"text-sm font-medium flex items-center gap-2",children:[(0,s.jsx)(I.A,{className:"h-4 w-4 text-blue-600"}),"3. Inserisci la tua password di amministratore per confermare l'identit\xe0"]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(N.p,{id:"admin-password",type:u?"text":"password",value:d,onChange:e=>o(e.target.value),placeholder:"Password amministratore",disabled:g||_,className:d.trim()?"border-green-500":""}),(0,s.jsx)(v.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0",onClick:()=>h(!u),disabled:g||_,children:u?(0,s.jsx)(D.A,{className:"h-4 w-4"}):(0,s.jsx)(L.A,{className:"h-4 w-4"})})]})]})]}),(0,s.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,s.jsx)("h5",{className:"font-medium text-slate-900 mb-3",children:"Stato Conferma:"}),(0,s.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-3 h-3 rounded-full ".concat("RESET DATABASE"===a?"bg-green-500":"bg-red-500")}),(0,s.jsxs)("span",{children:["Testo di conferma: ","RESET DATABASE"===a?"✓ Corretto":"✗ Richiesto"]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(l?"bg-green-500":"bg-red-500")}),(0,s.jsxs)("span",{children:["Checkbox confermata: ",l?"✓ S\xec":"✗ Richiesta"]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(d.trim()?"bg-green-500":"bg-red-500")}),(0,s.jsxs)("span",{children:["Password amministratore: ",d.trim()?"✓ Inserita":"✗ Richiesta"]})]}),_&&(0,s.jsxs)("div",{className:"flex items-center gap-2 mt-3 p-2 bg-orange-50 border border-orange-200 rounded",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 text-orange-600"}),(0,s.jsxs)("span",{className:"text-orange-700 font-medium",children:["Countdown di sicurezza: ",S," secondi"]})]}),U&&(0,s.jsxs)("div",{className:"flex items-center gap-2 mt-3 p-2 bg-red-50 border border-red-200 rounded",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 text-red-600"}),(0,s.jsx)("span",{className:"text-red-700 font-medium",children:"⚠️ Pronto per il reset - Ultima possibilit\xe0 di annullare"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[!U&&!_&&(0,s.jsx)(m,{onClick:()=>{"RESET DATABASE"===a&&l&&d.trim()?(k(!0),R(10),V(!1),w("")):w("Completa tutti i passaggi di conferma prima di procedere")},disabled:!P,className:"w-full",size:"lg",icon:(0,s.jsx)(p.A,{className:"h-5 w-5"}),children:"INIZIA COUNTDOWN DI SICUREZZA (10 secondi)"}),_&&(0,s.jsxs)("div",{className:"w-full p-4 bg-orange-50 border-2 border-orange-300 rounded-lg text-center",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,s.jsx)(p.A,{className:"h-6 w-6 text-orange-600 animate-pulse"}),(0,s.jsxs)("span",{className:"text-lg font-bold text-orange-700",children:["Countdown: ",S," secondi"]})]}),(0,s.jsx)("p",{className:"text-sm text-orange-600 mt-2",children:"Il pulsante di reset si attiver\xe0 al termine del countdown"})]}),U&&(0,s.jsx)(m,{onClick:O,disabled:!G,className:"w-full animate-pulse",size:"lg",loading:g,icon:(0,s.jsx)(b.A,{className:"h-5 w-5"}),glow:!0,children:g?"RESET IN CORSO...":"\uD83D\uDEA8 RESET DATABASE - ELIMINA TUTTI I DATI \uD83D\uDEA8"}),!P&&!U&&!_&&(0,s.jsx)("p",{className:"text-center text-sm text-slate-500",children:"Completa tutti i passaggi di conferma per iniziare il countdown"}),U&&(0,s.jsx)(c,{onClick:()=>{V(!1),k(!1),R(10)},className:"w-full",size:"lg",disabled:g,children:"ANNULLA RESET"})]})]}),(0,s.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm",children:[(0,s.jsx)("h5",{className:"font-medium text-blue-900 mb-2",children:"Informazioni Tecniche:"}),(0,s.jsxs)("ul",{className:"text-blue-700 space-y-1",children:[(0,s.jsx)("li",{children:"• Il reset manterr\xe0 la struttura delle tabelle"}),(0,s.jsx)("li",{children:"• L'utente amministratore principale verr\xe0 ricreato"}),(0,s.jsx)("li",{children:"• Le configurazioni di sistema verranno ripristinate ai valori di default"}),(0,s.jsx)("li",{children:"• L'operazione pu\xf2 richiedere alcuni minuti per completarsi"})]})]})]})]})}var q=t(3493),H=t(43332),Q=t(48136),Y=t(57434),K=t(84616);function ee(){let[e,a]=(0,r.useState)("categorie");return(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(q.A,{className:"h-5 w-5"}),"Database Tipologie Cavi"]})}),(0,s.jsxs)(i.Wu,{className:"space-y-6",children:[(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(q.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-blue-900",children:"Database Enciclopedico Tipologie Cavi"}),(0,s.jsx)("p",{className:"text-sm text-blue-700 mt-1",children:"Gestisci il database delle tipologie di cavi organizzato per categorie, produttori, standard e tipologie specifiche. Questo database serve come riferimento per la classificazione e gestione dei cavi nei progetti."})]})]})}),(0,s.jsxs)(y.tU,{value:e,onValueChange:a,className:"w-full",children:[(0,s.jsxs)(y.j7,{className:"grid w-full grid-cols-4",children:[(0,s.jsxs)(y.Xi,{value:"categorie",className:"flex items-center gap-2",children:[(0,s.jsx)(H.A,{className:"h-4 w-4"}),"Categorie"]}),(0,s.jsxs)(y.Xi,{value:"produttori",className:"flex items-center gap-2",children:[(0,s.jsx)(Q.A,{className:"h-4 w-4"}),"Produttori"]}),(0,s.jsxs)(y.Xi,{value:"standard",className:"flex items-center gap-2",children:[(0,s.jsx)(Y.A,{className:"h-4 w-4"}),"Standard"]}),(0,s.jsxs)(y.Xi,{value:"tipologie",className:"flex items-center gap-2",children:[(0,s.jsx)(q.A,{className:"h-4 w-4"}),"Tipologie"]})]}),(0,s.jsxs)(y.av,{value:"categorie",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Categorie Cavi"}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci le categorie principali di cavi (es. Energia, Controllo, Strumentazione, ecc.)"})]}),(0,s.jsxs)(v.$,{children:[(0,s.jsx)(K.A,{className:"h-4 w-4 mr-2"}),"Nuova Categoria"]})]}),(0,s.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,s.jsx)(H.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,s.jsx)("p",{children:"Gestione categorie cavi - Da implementare"}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile creare, modificare ed eliminare le categorie di cavi"})]})]}),(0,s.jsxs)(y.av,{value:"produttori",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Produttori"}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci l'elenco dei produttori di cavi (es. Prysmian, Nexans, General Cable, ecc.)"})]}),(0,s.jsxs)(v.$,{children:[(0,s.jsx)(K.A,{className:"h-4 w-4 mr-2"}),"Nuovo Produttore"]})]}),(0,s.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,s.jsx)(Q.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,s.jsx)("p",{children:"Gestione produttori - Da implementare"}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire l'anagrafica dei produttori di cavi"})]})]}),(0,s.jsxs)(y.av,{value:"standard",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Standard e Normative"}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci gli standard tecnici e le normative (es. CEI, IEC, EN, CENELEC, ecc.)"})]}),(0,s.jsxs)(v.$,{children:[(0,s.jsx)(K.A,{className:"h-4 w-4 mr-2"}),"Nuovo Standard"]})]}),(0,s.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,s.jsx)(Y.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,s.jsx)("p",{children:"Gestione standard - Da implementare"}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire gli standard tecnici e le normative di riferimento"})]})]}),(0,s.jsxs)(y.av,{value:"tipologie",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Tipologie Specifiche"}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci le tipologie specifiche di cavi con tutte le caratteristiche tecniche"})]}),(0,s.jsxs)(v.$,{children:[(0,s.jsx)(K.A,{className:"h-4 w-4 mr-2"}),"Nuova Tipologia"]})]}),(0,s.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,s.jsx)(q.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,s.jsx)("p",{children:"Gestione tipologie - Da implementare"}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire le tipologie specifiche con caratteristiche tecniche dettagliate"})]})]})]}),(0,s.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,s.jsx)("h5",{className:"font-medium text-slate-900 mb-2",children:"Struttura Database Tipologie:"}),(0,s.jsxs)("div",{className:"text-sm text-slate-600 space-y-1",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Categorie:"})," Classificazione principale (Energia, Controllo, Strumentazione, Dati, ecc.)"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Produttori:"})," Aziende produttrici con informazioni di contatto"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Standard:"})," Normative tecniche di riferimento (CEI, IEC, EN, CENELEC)"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Tipologie:"})," Specifiche tecniche dettagliate per ogni tipo di cavo"]})]})]})]})]})}var ea=t(17580),et=t(12318),es=t(47924),er=t(70306);function ei(){let e=(0,S.useRouter)(),[a,t]=(0,r.useState)("visualizza-utenti"),[l,d]=(0,r.useState)(""),[c,m]=(0,r.useState)([]),[x,u]=(0,r.useState)([]),[p,g]=(0,r.useState)(!0),[b,v]=(0,r.useState)(""),[T,R]=(0,r.useState)(null),[C,_]=(0,r.useState)({open:!1,message:"",severity:"success"}),{user:k,impersonateUser:U}=(0,z.A)();(0,r.useEffect)(()=>{I()},[a]);let I=async()=>{try{if(g(!0),v(""),"visualizza-utenti"===a||"crea-utente"===a||"accedi-come-utente"===a){let e=await E.dG.getUsers();m(e)}else if("cantieri"===a){let e=await E._I.getCantieri();u(e)}}catch(a){var e,t;v((null==(t=a.response)||null==(e=t.data)?void 0:e.detail)||a.message||"Errore durante il caricamento dei dati")}finally{g(!1)}},D=e=>{R(e),t("modifica-utente")},L=async e=>{try{await E.dG.toggleUserStatus(e),I()}catch(e){var a,t;v((null==(t=e.response)||null==(a=t.data)?void 0:a.detail)||"Errore durante la modifica dello stato utente")}},V=async e=>{if(confirm("Sei sicuro di voler eliminare questo utente?"))try{await E.dG.deleteUser(e),I()}catch(e){var a,t;v((null==(t=e.response)||null==(a=t.data)?void 0:a.detail)||"Errore durante l'eliminazione dell'utente")}},O=e=>{R(null),t("visualizza-utenti"),I()},P=()=>{R(null),t("visualizza-utenti")},G=async a=>{try{await U(a.id_utente),"user"===a.ruolo?e.push("/cantieri"):"cantieri_user"===a.ruolo?e.push("/cavi"):e.push("/")}catch(e){var t,s;v((null==(s=e.response)||null==(t=s.data)?void 0:t.detail)||e.message||"Errore durante l'impersonificazione")}},Z=e=>{let a="NEUTRAL";switch(e){case"owner":a="PROGRESS";break;case"user":a="INFO";break;case"cantieri_user":a="SUCCESS";break;default:a="NEUTRAL"}let t=(0,w.qn)(a);return(0,s.jsx)(f.E,{className:t.badge,children:e})},F=(e,a)=>{let t="SUCCESS",r="Attivo",i="●";if(e)if(a){let e=new Date(a),s=new Date;e<s?(t="ERROR",r="Scaduto",i="⚠"):e.getTime()-s.getTime()<6048e5?(t="WARNING",r="In Scadenza",i="⏰"):i="✓"}else i="✓";else t="ERROR",r="Disabilitato",i="●";let l=(0,w.qn)(t);return(0,s.jsxs)(f.E,{className:"".concat(l.badge," flex items-center gap-1"),children:[(0,s.jsx)("span",{className:"text-xs",role:"img","aria-hidden":"true",children:i}),(0,s.jsx)("span",{children:r})]})},W=c.filter(e=>{var a,t,s;return(null==(a=e.username)?void 0:a.toLowerCase().includes(l.toLowerCase()))||(null==(t=e.ragione_sociale)?void 0:t.toLowerCase().includes(l.toLowerCase()))||(null==(s=e.email)?void 0:s.toLowerCase().includes(l.toLowerCase()))});return k&&"owner"===k.ruolo?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,s.jsx)("div",{className:"max-w-[90%] mx-auto space-y-6",children:(0,s.jsxs)(y.tU,{value:a,onValueChange:t,className:"w-full",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"bg-white rounded-lg border border-slate-200 shadow-sm p-1",children:(0,s.jsxs)(y.j7,{className:"grid w-full ".concat(T?"grid-cols-5":"grid-cols-4"," gap-1 h-auto bg-transparent p-0"),children:[(0,s.jsxs)(y.Xi,{value:"visualizza-utenti",className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent",children:[(0,s.jsx)(ea.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"font-medium",children:"Gestione Utenti"})]}),(0,s.jsxs)(y.Xi,{value:"crea-utente",className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent",children:[(0,s.jsx)(et.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"font-medium",children:"Crea Nuovo Utente"})]}),T&&(0,s.jsxs)(y.Xi,{value:"modifica-utente",className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent",children:[(0,s.jsx)(h.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"font-medium",children:"Modifica Utente"})]}),(0,s.jsxs)(y.Xi,{value:"database-tipologie-cavi",className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent",children:[(0,s.jsx)(q.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"font-medium",children:"Database Tipologie Cavi"})]}),(0,s.jsxs)(y.Xi,{value:"visualizza-database-raw",className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent",children:[(0,s.jsx)(M.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"font-medium",children:"Gestione Dati Avanzata"})]})]})}),(0,s.jsx)("div",{className:"bg-gradient-to-r from-red-50 to-orange-50 rounded-lg border border-red-200 shadow-sm p-1",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 px-4 py-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full animate-pulse"}),(0,s.jsx)("span",{className:"text-sm font-medium text-red-800",children:"Impostazioni Avanzate e Pericolose"})]}),(0,s.jsx)(y.j7,{className:"h-auto bg-transparent p-0",children:(0,s.jsxs)(y.Xi,{value:"reset-database",className:"admin-tab-danger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-red-100 data-[state=active]:text-red-700 data-[state=active]:border-red-300 data-[state=active]:shadow-sm hover:bg-red-100 hover:text-red-700 border border-transparent text-red-600 font-medium",children:[(0,s.jsx)($.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Reset Database"})]})})]})})]}),(0,s.jsxs)(y.av,{value:"visualizza-utenti",className:"space-y-4",children:[b&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-red-600",children:b})}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(ea.A,{className:"h-5 w-5 text-blue-600"}),"Lista Utenti"]}),(0,s.jsx)(i.BT,{children:"Gestisci tutti gli utenti del sistema CABLYS"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(es.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,s.jsx)(N.p,{placeholder:"Cerca per username, email o ragione sociale...",value:l,onChange:e=>d(e.target.value),className:"pl-10 w-80"})]}),(0,s.jsxs)(f.E,{variant:"outline",className:"text-xs",children:[W.length," utenti"]})]})]})}),(0,s.jsx)(i.Wu,{children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsx)("div",{className:"rounded-md border",children:(0,s.jsxs)(A.XI,{children:[(0,s.jsx)(A.A0,{children:(0,s.jsxs)(A.Hj,{children:[(0,s.jsx)(A.nd,{className:"w-[50px] text-center",children:"ID"}),(0,s.jsx)(A.nd,{className:"w-[100px]",children:"Username"}),(0,s.jsx)(A.nd,{className:"w-[80px] text-center",children:"Password"}),(0,s.jsx)(A.nd,{className:"w-[80px] text-center",children:"Ruolo"}),(0,s.jsx)(A.nd,{className:"w-[180px]",children:"Ragione Sociale"}),(0,s.jsx)(A.nd,{className:"w-[160px]",children:"Email"}),(0,s.jsx)(A.nd,{className:"w-[80px] text-center",children:"VAT"}),(0,s.jsx)(A.nd,{className:"w-[80px] text-center",children:"Nazione"}),(0,s.jsx)(A.nd,{className:"w-[120px]",children:"Referente"}),(0,s.jsx)(A.nd,{className:"w-[90px] text-center",children:"Scadenza"}),(0,s.jsx)(A.nd,{className:"w-[80px] text-center",children:"Stato"}),(0,s.jsx)(A.nd,{className:"w-[100px] text-center",children:"Azioni"})]})}),(0,s.jsx)(A.BF,{children:p?(0,s.jsx)(A.Hj,{children:(0,s.jsx)(A.nA,{colSpan:12,className:"text-center py-8",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)(n.A,{className:"h-4 w-4 animate-spin"}),"Caricamento..."]})})}):0===c.length?(0,s.jsx)(A.Hj,{children:(0,s.jsx)(A.nA,{colSpan:12,className:"text-center py-8 text-slate-500",children:"Nessun utente trovato"})}):W.map(e=>(0,s.jsxs)(A.Hj,{className:"users-table-row",children:[(0,s.jsx)(A.nA,{className:"text-center",children:(0,s.jsxs)(f.E,{variant:"outline",className:"text-xs font-mono",children:["#",e.id_utente]})}),(0,s.jsx)(A.nA,{className:"font-semibold text-slate-900",children:e.username}),(0,s.jsx)(A.nA,{className:"text-center",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-1",children:[(0,s.jsx)("div",{className:"flex gap-1",children:[...Array(8)].map((e,a)=>(0,s.jsx)("div",{className:"w-1.5 h-1.5 bg-slate-400 rounded-full"},a))}),(0,s.jsx)("div",{className:"w-2 h-2 rounded-full ml-2 ".concat(e.password_plain?"bg-green-500":"bg-red-500"),title:e.password_plain?"Password configurata":"Password non configurata"})]})}),(0,s.jsx)(A.nA,{className:"text-center",children:Z(e.ruolo)}),(0,s.jsx)(A.nA,{className:"max-w-[250px] truncate",title:e.ragione_sociale,children:(0,s.jsx)("span",{className:"text-slate-900",children:e.ragione_sociale||"-"})}),(0,s.jsx)(A.nA,{className:"max-w-[200px] truncate text-sm text-slate-600",title:e.email,children:e.email||"-"}),(0,s.jsx)(A.nA,{className:"text-center text-sm text-slate-600",children:e.vat||"-"}),(0,s.jsx)(A.nA,{className:"text-center text-sm text-slate-600",children:e.nazione||"-"}),(0,s.jsx)(A.nA,{className:"max-w-[150px] truncate text-sm text-slate-600",title:e.referente_aziendale,children:e.referente_aziendale||"-"}),(0,s.jsx)(A.nA,{className:"text-center text-sm text-slate-600",children:e.data_scadenza?new Date(e.data_scadenza).toLocaleDateString("it-IT"):"N/A"}),(0,s.jsx)(A.nA,{className:"text-center",children:F(e.abilitato,e.data_scadenza)}),(0,s.jsx)(A.nA,{className:"text-center",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)(j,{user:e,onEdit:()=>D(e),onToggleStatus:()=>L(e.id_utente),onDelete:()=>V(e.id_utente)}),(0,s.jsx)(o,{size:"sm",onClick:()=>G(e),disabled:"owner"===e.ruolo||!e.abilitato,className:"px-3 py-1.5 text-xs",icon:(0,s.jsx)(er.A,{className:"h-3.5 w-3.5"}),children:"Entra"})]})})]},e.id_utente))})]})})})})]})]}),(0,s.jsx)(y.av,{value:"crea-utente",className:"space-y-4",children:(0,s.jsx)(B,{user:null,onSave:O,onCancel:P})}),T&&(0,s.jsx)(y.av,{value:"modifica-utente",className:"space-y-4",children:(0,s.jsx)(B,{user:T,onSave:O,onCancel:P})}),(0,s.jsx)(y.av,{value:"database-tipologie-cavi",className:"space-y-4",children:(0,s.jsx)(ee,{})}),(0,s.jsx)(y.av,{value:"visualizza-database-raw",className:"space-y-4",children:(0,s.jsx)(X,{})}),(0,s.jsx)(y.av,{value:"reset-database",className:"space-y-4",children:(0,s.jsx)(J,{})})]})})}):(window.location.replace("/login"),null)}},17313:(e,a,t)=>{"use strict";t.d(a,{Xi:()=>o,av:()=>c,j7:()=>d,tU:()=>n});var s=t(95155),r=t(12115),i=t(60704),l=t(59434);let n=i.bL,d=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(i.B8,{ref:a,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...r})});d.displayName=i.B8.displayName;let o=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(i.l9,{ref:a,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...r})});o.displayName=i.l9.displayName;let c=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(i.UC,{ref:a,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...r})});c.displayName=i.UC.displayName},26126:(e,a,t)=>{"use strict";t.d(a,{E:()=>d});var s=t(95155);t(12115);var r=t(99708),i=t(74466),l=t(59434);let n=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:a,variant:t,asChild:i=!1,...d}=e,o=i?r.DX:"span";return(0,s.jsx)(o,{"data-slot":"badge",className:(0,l.cn)(n({variant:t}),a),...d})}},30285:(e,a,t)=>{"use strict";t.d(a,{$:()=>d,r:()=>n});var s=t(95155);t(12115);var r=t(99708),i=t(74466),l=t(59434);let n=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:a,variant:t,size:i,asChild:d=!1,...o}=e,c=d?r.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,l.cn)(n({variant:t,size:i,className:a})),...o})}},40975:()=>{},47262:(e,a,t)=>{"use strict";t.d(a,{S:()=>n});var s=t(95155);t(12115);var r=t(76981),i=t(5196),l=t(59434);function n(e){let{className:a,...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"checkbox",className:(0,l.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...t,children:(0,s.jsx)(r.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(i.A,{className:"size-3.5"})})})}},59409:(e,a,t)=>{"use strict";t.d(a,{bq:()=>m,eb:()=>u,gC:()=>x,l6:()=>o,yv:()=>c});var s=t(95155);t(12115);var r=t(38715),i=t(66474),l=t(5196),n=t(47863),d=t(59434);function o(e){let{...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"select",...a})}function c(e){let{...a}=e;return(0,s.jsx)(r.WT,{"data-slot":"select-value",...a})}function m(e){let{className:a,size:t="default",children:l,...n}=e;return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...n,children:[l,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function x(e){let{className:a,children:t,position:i="popper",...l}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:i,...l,children:[(0,s.jsx)(h,{}),(0,s.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(p,{})]})})}function u(e){let{className:a,children:t,...i}=e;return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...i,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(l.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:t})]})}function h(e){let{className:a,...t}=e;return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,s.jsx)(n.A,{className:"size-4"})})}function p(e){let{className:a,...t}=e;return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,s.jsx)(i.A,{className:"size-4"})})}},59434:(e,a,t)=>{"use strict";t.d(a,{cn:()=>i});var s=t(52596),r=t(39688);function i(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,r.QP)((0,s.$)(a))}},61610:(e,a,t)=>{"use strict";t.d(a,{Eb:()=>h,GN:()=>p});let s=/[<>\"'&\x00-\x1f\x7f-\x9f]/g,r=/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,i=/(<script|javascript:|vbscript:|onload|onerror|onclick)/gi,l=e=>"string"!=typeof e?"":e.trim().replace(s,"").replace(/\s+/g," ").substring(0,1e3),n=e=>{let a=l(e);return a.length<3?{isValid:!1,error:"Username deve essere almeno 3 caratteri"}:a.length>20?{isValid:!1,error:"Username non pu\xf2 superare 20 caratteri"}:/^[a-zA-Z0-9._-]+$/.test(a)?/^[._-]|[._-]$/.test(a)?{isValid:!1,error:"Username non pu\xf2 iniziare o finire con caratteri speciali"}:{isValid:!0}:{isValid:!1,error:"Username pu\xf2 contenere solo lettere, numeri, punti, underscore e trattini"}},d=e=>{if(!e||e.length<8)return{isValid:!1,error:"Password deve essere almeno 8 caratteri",strength:0};if(e.length>128)return{isValid:!1,error:"Password troppo lunga (max 128 caratteri)",strength:0};let a=0;return(/[a-z]/.test(e)&&a++,/[A-Z]/.test(e)&&a++,/[0-9]/.test(e)&&a++,/[^a-zA-Z0-9]/.test(e)&&a++,e.length>=12&&a++,a<3)?{isValid:!1,error:"Password deve contenere almeno: 1 minuscola, 1 maiuscola, 1 numero o 1 carattere speciale",strength:a}:["password","123456","admin","qwerty","letmein"].some(a=>e.toLowerCase().includes(a))?{isValid:!1,error:"Password troppo comune",strength:a}:{isValid:!0,strength:a}},o=e=>{let a=l(e);return a?a.length>254?{isValid:!1,error:"Email troppo lunga"}:/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(a)?{isValid:!0}:{isValid:!1,error:"Formato email non valido"}:{isValid:!1,error:"Email \xe8 obbligatoria"}},c=function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:255;return l(e).length>a?{isValid:!1,error:"Testo troppo lungo (max ".concat(a," caratteri)")}:i.test(e)||r.test(e)?{isValid:!1,error:"Contenuto non consentito rilevato"}:{isValid:!0}},m=e=>{let a=l(e);return a?a.length<2?{isValid:!1,error:"Ragione sociale troppo corta"}:a.length>100?{isValid:!1,error:"Ragione sociale troppo lunga (max 100 caratteri)"}:/^[a-zA-Z0-9\s\.\-&']+$/.test(a)?{isValid:!0}:{isValid:!1,error:"Ragione sociale contiene caratteri non consentiti"}:{isValid:!1,error:"Ragione sociale \xe8 obbligatoria"}},x=e=>{if(!e)return{isValid:!0};let a=l(e).replace(/\s/g,"");return a.length<8||a.length>15?{isValid:!1,error:"VAT deve essere tra 8 e 15 caratteri"}:/^[A-Z0-9]+$/i.test(a)?{isValid:!0}:{isValid:!1,error:"VAT pu\xf2 contenere solo lettere e numeri"}},u=new Map,h=(e,a,t)=>{let s=Date.now(),r=u.get(e);return!r||s>r.resetTime?(u.set(e,{count:1,resetTime:s+t}),!0):!(r.count>=a)&&(r.count++,!0)},p=e=>{let a={},t=n(e.username);if(t.isValid||(a.username=t.error),e.password){let t=d(e.password);t.isValid||(a.password=t.error)}let s=m(e.ragione_sociale);if(s.isValid||(a.ragione_sociale=s.error),e.email){let t=o(e.email);t.isValid||(a.email=t.error)}if(e.vat){let t=x(e.vat);t.isValid||(a.vat=t.error)}if(e.indirizzo){let t=c(e.indirizzo,200);t.isValid||(a.indirizzo=t.error)}if(e.nazione){let t=c(e.nazione,50);t.isValid||(a.nazione=t.error)}if(e.referente_aziendale){let t=c(e.referente_aziendale,100);t.isValid||(a.referente_aziendale=t.error)}return{isValid:0===Object.keys(a).length,errors:a}}},62523:(e,a,t)=>{"use strict";t.d(a,{p:()=>l});var s=t(95155),r=t(12115),i=t(59434);let l=r.forwardRef((e,a)=>{let{className:t,type:r,...l}=e;return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),ref:a,...l})});l.displayName="Input"},63743:(e,a,t)=>{"use strict";t.d(a,{Fw:()=>c,NM:()=>o,Nj:()=>d,Tr:()=>l,mU:()=>s,qn:()=>x});let s={PRIMARY:{bg:"bg-blue-50",text:"text-blue-600",border:"border-blue-300",hover:"hover:bg-blue-50",active:"hover:border-blue-400",hex:"#007bff"},NEUTRAL:{text_dark:"text-gray-800",text_medium:"text-gray-600",text_light:"text-gray-500",bg_white:"bg-white",bg_light:"bg-gray-50",border:"border-gray-300",hex_dark:"#343A40",hex_medium:"#6c757d",hex_light:"#DEE2E6"},STATUS:{SUCCESS:{bg:"bg-green-50",text:"text-green-700",border:"border-green-200",hex:"#28A745"},WARNING:{bg:"bg-orange-50",text:"text-orange-700",border:"border-orange-200",hex:"#FD7E14"},ERROR:{bg:"bg-red-50",text:"text-red-700",border:"border-red-200",hex:"#DC3545"}}};s.STATUS.SUCCESS,s.STATUS.WARNING,s.NEUTRAL,s.STATUS.ERROR,s.NEUTRAL,s.STATUS.ERROR;let r={DA_INSTALLARE:s.NEUTRAL,INSTALLATO:s.STATUS.SUCCESS,COLLEGATO_PARTENZA:s.STATUS.WARNING,COLLEGATO_ARRIVO:s.STATUS.WARNING,COLLEGATO:s.STATUS.SUCCESS,CERTIFICATO:s.STATUS.SUCCESS,SPARE:s.STATUS.WARNING,ERRORE:s.STATUS.ERROR},i={ATTIVA:s.STATUS.SUCCESS,COMPLETATA:s.STATUS.SUCCESS,ANNULLATA:s.NEUTRAL,IN_CORSO:s.STATUS.WARNING,ERRORE:s.STATUS.ERROR},l=e=>{let a=r[null==e?void 0:e.toUpperCase().replace(/\s+/g,"_")]||r.ERRORE;return{badge:"".concat(a.bg," ").concat(a.text," rounded-full px-3 py-1 text-xs font-medium"),text:a.text,bg:a.bg,border:a.border,hex:a.hex}},n=()=>({button:"inline-flex items-center justify-center gap-1 px-3 py-2 text-xs font-medium bg-white text-gray-800 border-b-2 border-[#315cfd] rounded-full cursor-pointer min-w-[4rem] h-8 transition-colors duration-300 hover:border-2 hover:bg-[#315cfd] hover:text-white",text:"text-gray-800",border:"border-b-[#315cfd]",hover:"hover:bg-[#315cfd] hover:text-white hover:border-2"}),d=()=>n(),o=()=>({text:"inline-flex items-center gap-1 px-2 py-1 text-xs font-medium ".concat(s.NEUTRAL.text_light),color:s.NEUTRAL.text_light}),c=e=>{let a=i[null==e?void 0:e.toUpperCase().replace(/\s+/g,"_")]||i.ERRORE;return{badge:"".concat(a.bg," ").concat(a.text," ").concat(a.border),button:"".concat(a.bg," ").concat(a.text," ").concat(a.border," ").concat(a.hover),alert:"".concat(a.bg," ").concat(a.text," ").concat(a.border),text:a.text,bg:a.bg,border:a.border,hover:a.hover,hex:a.hex}};s.STATUS.ERROR,s.STATUS.WARNING,s.NEUTRAL,s.NEUTRAL;let m={SUCCESS:s.STATUS.SUCCESS,WARNING:s.STATUS.WARNING,ERROR:s.STATUS.ERROR,NEUTRAL:s.NEUTRAL,PRIMARY:s.PRIMARY,PROGRESS:s.STATUS.SUCCESS,INFO:s.PRIMARY},x=e=>{let a=m[e]||m.NEUTRAL;return{badge:"".concat(a.bg," ").concat(a.text," rounded-full px-3 py-1 text-xs font-medium"),text:a.text,bg:a.bg,border:a.border,hex:a.hex}}},66695:(e,a,t)=>{"use strict";t.d(a,{BT:()=>d,Wu:()=>o,ZB:()=>n,Zp:()=>i,aR:()=>l});var s=t(95155);t(12115);var r=t(59434);function i(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...t})}function l(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...t})}function n(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",a),...t})}function d(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",a),...t})}function o(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",a),...t})}},85057:(e,a,t)=>{"use strict";t.d(a,{J:()=>l});var s=t(95155);t(12115);var r=t(40968),i=t(59434);function l(e){let{className:a,...t}=e;return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...t})}},85127:(e,a,t)=>{"use strict";t.d(a,{A0:()=>l,BF:()=>n,Hj:()=>d,XI:()=>i,nA:()=>c,nd:()=>o});var s=t(95155);t(12115);var r=t(59434);function i(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm border-collapse",a),...t})})}function l(e){let{className:a,...t}=e;return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",a),...t})}function n(e){let{className:a,...t}=e;return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",a),...t})}function d(e){let{className:a,...t}=e;return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("data-[state=selected]:bg-muted border-b",a),...t})}function o(e){let{className:a,...t}=e;return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...t})}function c(e){let{className:a,...t}=e;return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...t})}},85717:(e,a,t)=>{Promise.resolve().then(t.bind(t,7958))}},e=>{var a=a=>e(e.s=a);e.O(0,[8902,3464,3455,1909,9384,6955,3845,1059,283,8441,1684,7358],()=>a(85717)),_N_E=e.O()}]);