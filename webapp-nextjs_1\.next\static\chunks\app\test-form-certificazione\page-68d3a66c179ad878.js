(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5408],{7946:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>h});var i=t(95155),c=t(12115),s=t(66695),n=t(30285),o=t(26126),r=t(75021),l=t(57434),p=t(50589),d=t(40646),m=t(38164),g=t(381),x=t(71539),u=t(74782);function h(){let[e,a]=(0,c.useState)(!1),[t]=(0,c.useState)(1);return(0,i.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:[(0,i.jsxs)("div",{className:"max-w-6xl mx-auto space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("h1",{className:"text-3xl font-bold text-slate-900 flex items-center gap-3",children:[(0,i.jsx)(r.A,{className:"h-8 w-8 text-blue-600"}),"Test Form Certificazione CEI 64-8"]}),(0,i.jsx)("p",{className:"text-slate-600 mt-1",children:"Test delle automazioni e dell'interfaccia utente"})]}),(0,i.jsxs)(n.$,{onClick:()=>a(!0),disabled:e,children:[(0,i.jsx)(l.A,{className:"h-4 w-4 mr-2"}),"Apri Form Certificazione"]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)(s.Zp,{children:[(0,i.jsxs)(s.aR,{children:[(0,i.jsxs)(s.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(p.A,{className:"h-5 w-5 text-blue-500"}),"Dati Meteorologici Automatici"]}),(0,i.jsx)(s.BT,{children:"Test del caricamento automatico"})]}),(0,i.jsx)(s.Wu,{children:(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,i.jsx)("span",{className:"text-sm",children:"Caricamento automatico all'apertura del form"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,i.jsx)("span",{className:"text-sm",children:"Visualizzazione temperatura e umidit\xe0"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,i.jsx)("span",{className:"text-sm",children:"Precompilazione campi temperatura/umidit\xe0"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,i.jsx)("span",{className:"text-sm",children:"Indicatore fonte dati (demo/live)"})]})]})})]}),(0,i.jsxs)(s.Zp,{children:[(0,i.jsxs)(s.aR,{children:[(0,i.jsxs)(s.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(m.A,{className:"h-5 w-5 text-green-500"}),"Collegamento Automatico Cavi"]}),(0,i.jsx)(s.BT,{children:"Test della logica CEI 64-8"})]}),(0,i.jsx)(s.Wu,{children:(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,i.jsx)("span",{className:"text-sm",children:"Verifica stato collegamento cavo"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,i.jsx)("span",{className:"text-sm",children:"Dialog di conferma se non collegato"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,i.jsx)("span",{className:"text-sm",children:'Collegamento automatico a "cantiere"'})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,i.jsx)("span",{className:"text-sm",children:"Aggiornamento stato cavi"})]})]})})]}),(0,i.jsxs)(s.Zp,{children:[(0,i.jsxs)(s.aR,{children:[(0,i.jsxs)(s.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(g.A,{className:"h-5 w-5 text-purple-500"}),"Validazione Automatica"]}),(0,i.jsx)(s.BT,{children:"Test della conformit\xe0 automatica"})]}),(0,i.jsx)(s.Wu,{children:(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,i.jsx)("span",{className:"text-sm",children:"Calcolo automatico conformit\xe0"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,i.jsx)("span",{className:"text-sm",children:"Confronto con valore minimo isolamento"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,i.jsx)("span",{className:"text-sm",children:"Impostazione stato certificato"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,i.jsx)("span",{className:"text-sm",children:"Esito complessivo automatico"})]})]})})]}),(0,i.jsxs)(s.Zp,{children:[(0,i.jsxs)(s.aR,{children:[(0,i.jsxs)(s.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(x.A,{className:"h-5 w-5 text-orange-500"}),"Interfaccia Utente"]}),(0,i.jsx)(s.BT,{children:"Test dell'esperienza utente"})]}),(0,i.jsx)(s.Wu,{children:(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,i.jsx)("span",{className:"text-sm",children:"Layout responsive e moderno"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,i.jsx)("span",{className:"text-sm",children:"Feedback visivo per operazioni"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,i.jsx)("span",{className:"text-sm",children:"Validazione campi in tempo reale"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(d.A,{className:"h-4 w-4 text-green-500"}),(0,i.jsx)("span",{className:"text-sm",children:"Gestione errori e stati di caricamento"})]})]})})]})]}),(0,i.jsxs)(s.Zp,{children:[(0,i.jsxs)(s.aR,{children:[(0,i.jsxs)(s.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(r.A,{className:"h-5 w-5"}),"Istruzioni per il Test"]}),(0,i.jsx)(s.BT,{children:"Come testare le funzionalit\xe0 implementate"})]}),(0,i.jsx)(s.Wu,{children:(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,i.jsx)("h4",{className:"font-semibold text-blue-900 mb-2",children:"1. Test Dati Meteorologici"}),(0,i.jsx)("p",{className:"text-blue-800 text-sm",children:'Apri il form e verifica che nella sezione "Condizioni Ambientali" vengano mostrati automaticamente temperatura e umidit\xe0. I campi dovrebbero essere precompilati.'})]}),(0,i.jsxs)("div",{className:"p-4 bg-green-50 border border-green-200 rounded-lg",children:[(0,i.jsx)("h4",{className:"font-semibold text-green-900 mb-2",children:"2. Test Collegamento Automatico"}),(0,i.jsx)("p",{className:"text-green-800 text-sm",children:"Seleziona un cavo non completamente collegato e prova a salvare. Dovrebbe apparire un dialog di conferma per il collegamento automatico."})]}),(0,i.jsxs)("div",{className:"p-4 bg-purple-50 border border-purple-200 rounded-lg",children:[(0,i.jsx)("h4",{className:"font-semibold text-purple-900 mb-2",children:"3. Test Validazione Automatica"}),(0,i.jsx)("p",{className:"text-purple-800 text-sm",children:'Inserisci un valore di isolamento (es. 600 MΩ) e verifica che lo stato venga automaticamente impostato su "CONFORME" se superiore al minimo.'})]}),(0,i.jsxs)("div",{className:"p-4 bg-orange-50 border border-orange-200 rounded-lg",children:[(0,i.jsx)("h4",{className:"font-semibold text-orange-900 mb-2",children:"4. Test Interfaccia"}),(0,i.jsx)("p",{className:"text-orange-800 text-sm",children:"Verifica la responsivit\xe0, i feedback visivi, la validazione dei campi e la gestione degli stati di caricamento durante le operazioni."})]})]})})]}),(0,i.jsxs)(s.Zp,{children:[(0,i.jsxs)(s.aR,{children:[(0,i.jsx)(s.ZB,{children:"Risultati Attesi"}),(0,i.jsx)(s.BT,{children:"Cosa dovrebbe succedere durante il test"})]}),(0,i.jsx)(s.Wu,{children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(o.E,{className:"bg-green-100 text-green-800 border-green-200",children:"✅ Dati meteorologici caricati automaticamente"}),(0,i.jsx)(o.E,{className:"bg-green-100 text-green-800 border-green-200",children:"✅ Campi temperatura/umidit\xe0 precompilati"}),(0,i.jsx)(o.E,{className:"bg-green-100 text-green-800 border-green-200",children:"✅ Dialog conferma collegamento automatico"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(o.E,{className:"bg-green-100 text-green-800 border-green-200",children:"✅ Stato conformit\xe0 calcolato automaticamente"}),(0,i.jsx)(o.E,{className:"bg-green-100 text-green-800 border-green-200",children:"✅ Interfaccia responsive e moderna"}),(0,i.jsx)(o.E,{className:"bg-green-100 text-green-800 border-green-200",children:"✅ Feedback visivo per tutte le operazioni"})]})]})})]})]}),e&&(0,i.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,i.jsx)("div",{className:"bg-white rounded-lg max-w-6xl w-full max-h-[95vh] min-w-[800px] overflow-y-auto",children:(0,i.jsx)("div",{className:"p-6",children:(0,i.jsx)(u.A,{cantiereId:t,certificazione:null,strumenti:[],onSuccess:()=>{a(!1),alert("✅ Certificazione creata con successo!\n\nVerifica che:\n- I dati meteorologici siano stati caricati automaticamente\n- Il collegamento automatico sia stato eseguito se necessario\n- La conformit\xe0 sia stata determinata automaticamente")},onCancel:()=>{a(!1)}})})})})]})}},25731:(e,a,t)=>{"use strict";t.d(a,{AR:()=>d,At:()=>r,CV:()=>p,Fw:()=>l,ZQ:()=>o,_I:()=>j,dG:()=>b,km:()=>m,kw:()=>g,l9:()=>x,mg:()=>h,om:()=>u,ug:()=>v});let i=t(23464).A.create({baseURL:"http://localhost:3000",timeout:3e4,headers:{"Content-Type":"application/json"}}),c=()=>localStorage.getItem("token")||localStorage.getItem("access_token"),s=()=>{["token","access_token","user_data","cantiere_data","selectedCantiereId","selectedCantiereName","isImpersonating","impersonatedUser"].forEach(e=>{localStorage.removeItem(e)})};i.interceptors.request.use(e=>{let a=c();return a&&(e.headers.Authorization="Bearer ".concat(a)),e},e=>Promise.reject(e)),i.interceptors.response.use(e=>e,e=>{var a;return(null==(a=e.response)?void 0:a.status)===401&&(console.log("\uD83D\uDEA8 API: Token non valido o scaduto, pulizia dati e reindirizzamento"),s(),window.location.pathname.includes("/login")||(window.location.href="/login")),Promise.reject(e)});let n={get:async(e,a)=>(await i.get(e,a)).data,post:async(e,a,t)=>(await i.post(e,a,t)).data,put:async(e,a,t)=>(await i.put(e,a,t)).data,delete:async(e,a)=>(await i.delete(e,a)).data},o={login:async e=>(await i.post("/api/auth/login",{username:e.username,password:e.password})).data,loginCantiere:e=>n.post("/api/auth/login/cantiere",{codice_univoco:e.codice_cantiere,password:e.password_cantiere}),verifyToken:()=>n.post("/api/auth/test-token"),logout:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),window.location.href="/login"}},r={getCavi:(e,a)=>n.get("/api/cavi/".concat(e),{params:a}),getCavo:(e,a)=>n.get("/api/cavi/".concat(e,"/").concat(a)),checkCavo:(e,a)=>n.get("/api/cavi/".concat(e,"/check/").concat(a)),createCavo:(e,a)=>n.post("/api/cavi/".concat(e),a),updateCavo:(e,a,t)=>n.put("/api/cavi/".concat(e,"/").concat(a),t),deleteCavo:(e,a,t)=>n.delete("/api/cavi/".concat(e,"/").concat(a),{data:t}),updateMetriPosati:(e,a,t,i,c)=>n.post("/api/cavi/".concat(e,"/").concat(a,"/metri-posati"),{metri_posati:t,id_bobina:i,force_over:c||!1}),updateBobina:(e,a,t,i)=>n.post("/api/cavi/".concat(e,"/").concat(a,"/bobina"),{id_bobina:t,force_over:i||!1}),cancelInstallation:(e,a)=>n.post("/api/cavi/".concat(e,"/").concat(a,"/cancel-installation")),collegaCavo:(e,a,t,i)=>n.post("/api/cavi/".concat(e,"/").concat(a,"/collegamento"),{lato:t,responsabile:i}),scollegaCavo:(e,a,t)=>{let i={};return t&&(i.data={lato:t}),n.delete("/api/cavi/".concat(e,"/").concat(a,"/collegamento"),i)},markAsSpare:function(e,a,t){let i=!(arguments.length>3)||void 0===arguments[3]||arguments[3];return t?n.post("/api/cavi/".concat(e,"/").concat(a,"/mark-as-spare"),{force:i}):n.post("/api/cavi/".concat(e,"/").concat(a,"/reactivate-spare"),{})},debugCavi:e=>n.get("/api/cavi/debug/".concat(e)),debugCaviRaw:e=>n.get("/api/cavi/debug/raw/".concat(e))},l={getBobine:(e,a)=>n.get("/api/parco-cavi/".concat(e),{params:a}),getBobina:(e,a)=>n.get("/api/parco-cavi/".concat(e,"/").concat(a)),getBobineCompatibili:(e,a)=>n.get("/api/parco-cavi/".concat(e,"/compatibili"),{params:a}),createBobina:(e,a)=>n.post("/api/parco-cavi/".concat(e),a),updateBobina:(e,a,t)=>n.put("/api/parco-cavi/".concat(e,"/").concat(a),t),deleteBobina:(e,a)=>n.delete("/api/parco-cavi/".concat(e,"/").concat(a)),isFirstBobinaInsertion:e=>n.get("/api/parco-cavi/".concat(e,"/is-first-insertion")),updateBobina:(e,a,t)=>n.put("/api/parco-cavi/".concat(e,"/").concat(a),t),deleteBobina:(e,a)=>n.delete("/api/parco-cavi/".concat(e,"/").concat(a)),checkDisponibilita:(e,a,t)=>n.get("/api/parco-cavi/".concat(e,"/").concat(a,"/disponibilita"),{params:{metri_richiesti:t}})},p={getComande:e=>n.get("/api/comande/cantiere/".concat(e)),getComanda:(e,a)=>n.get("/api/comande/".concat(a)),getCaviComanda:e=>n.get("/api/comande/".concat(e,"/cavi")),createComanda:(e,a)=>n.post("/api/comande/cantiere/".concat(e),a),createComandaWithCavi:(e,a,t)=>n.post("/api/comande/cantiere/".concat(e,"/crea-con-cavi"),a,{params:{lista_id_cavi:t}}),updateDatiComanda:(e,a,t)=>n.put("/api/comande/".concat(e,"/").concat(a),t),updateComanda:(e,a,t)=>n.put("/api/comande/cantiere/".concat(e,"/").concat(a),t),deleteComanda:(e,a)=>n.delete("/api/comande/cantiere/".concat(e,"/").concat(a)),assegnaCavi:(e,a,t)=>n.post("/api/comande/cantiere/".concat(e,"/").concat(a,"/assegna-cavi"),{cavi_ids:t}),rimuoviCavi:(e,a,t)=>n.delete("/api/comande/cantiere/".concat(e,"/").concat(a,"/rimuovi-cavi"),{data:{cavi_ids:t}}),getStatistiche:e=>n.get("/api/comande/cantiere/".concat(e,"/statistiche")),cambiaStato:(e,a,t)=>n.put("/api/comande/cantiere/".concat(e,"/").concat(a,"/stato"),{nuovo_stato:t})},d={getResponsabili:e=>n.get("/api/responsabili/cantiere/".concat(e)),createResponsabile:(e,a)=>n.post("/api/responsabili/".concat(e),a),updateResponsabile:(e,a,t)=>n.put("/api/responsabili/".concat(e,"/").concat(a),t),deleteResponsabile:(e,a)=>n.delete("/api/responsabili/".concat(e,"/").concat(a))},m={getCertificazioni:(e,a)=>n.get("/api/cantieri/".concat(e,"/certificazioni"),{params:a?{filtro_cavo:a}:{}}),createCertificazione:(e,a)=>n.post("/api/cantieri/".concat(e,"/certificazioni"),a),getCertificazione:(e,a)=>n.get("/api/cantieri/".concat(e,"/certificazioni/").concat(a)),updateCertificazione:(e,a,t)=>n.put("/api/cantieri/".concat(e,"/certificazioni/").concat(a),t),deleteCertificazione:(e,a)=>n.delete("/api/cantieri/".concat(e,"/certificazioni/").concat(a)),generatePDF:(e,a)=>n.get("/api/cantieri/".concat(e,"/certificazioni/").concat(a,"/pdf"),{responseType:"blob"}),getStatistiche:e=>n.get("/api/cantieri/".concat(e,"/certificazioni/statistiche")),exportCertificazioni:(e,a)=>n.get("/api/cantieri/".concat(e,"/certificazioni/export"),{params:a,responseType:"blob"}),generateReport:function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"completo";return n.get("/api/cantieri/".concat(e,"/certificazioni/report/").concat(a))},bulkDelete:(e,a)=>n.post("/api/cantieri/".concat(e,"/certificazioni/bulk-delete"),{ids:a}),generateBulkPdf:(e,a)=>n.post("/api/cantieri/".concat(e,"/certificazioni/bulk-pdf"),{ids:a},{responseType:"blob"}),validateCertificazione:(e,a)=>n.post("/api/cantieri/".concat(e,"/certificazioni/validate"),a)},g={getStrumenti:e=>n.get("/api/cantieri/".concat(e,"/strumenti")),createStrumento:(e,a)=>n.post("/api/cantieri/".concat(e,"/strumenti"),a),updateStrumento:(e,a,t)=>n.put("/api/cantieri/".concat(e,"/strumenti/").concat(a),t),deleteStrumento:(e,a)=>n.delete("/api/cantieri/".concat(e,"/strumenti/").concat(a))},x={getRapporti:function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:100;return n.get("/api/cantieri/".concat(e,"/rapporti"),{params:{skip:a,limit:t}})},createRapporto:(e,a)=>n.post("/api/cantieri/".concat(e,"/rapporti"),a),getRapporto:(e,a)=>n.get("/api/cantieri/".concat(e,"/rapporti/").concat(a)),updateRapporto:(e,a,t)=>n.put("/api/cantieri/".concat(e,"/rapporti/").concat(a),t),deleteRapporto:(e,a)=>n.delete("/api/cantieri/".concat(e,"/rapporti/").concat(a)),aggiornaStatistiche:(e,a)=>n.post("/api/cantieri/".concat(e,"/rapporti/").concat(a,"/aggiorna-statistiche"))},u={getNonConformita:e=>n.get("/api/cantieri/".concat(e,"/non-conformita")),createNonConformita:(e,a)=>n.post("/api/cantieri/".concat(e,"/non-conformita"),a),updateNonConformita:(e,a,t)=>n.put("/api/cantieri/".concat(e,"/non-conformita/").concat(a),t),deleteNonConformita:(e,a)=>n.delete("/api/cantieri/".concat(e,"/non-conformita/").concat(a))},h={importCavi:(e,a,t)=>{let i=new FormData;return i.append("file",a),i.append("revisione",t),n.post("/api/excel/".concat(e,"/import-cavi"),i,{headers:{"Content-Type":"multipart/form-data"}})},importBobine:(e,a)=>{let t=new FormData;return t.append("file",a),n.post("/api/excel/".concat(e,"/import-parco-bobine"),t,{headers:{"Content-Type":"multipart/form-data"}})},exportCavi:e=>n.get("/api/excel/".concat(e,"/export-cavi"),{responseType:"blob"}),exportBobine:e=>n.get("/api/excel/".concat(e,"/export-parco-bobine"),{responseType:"blob"})},v={getReportAvanzamento:e=>n.get("/api/reports/".concat(e,"/avanzamento")),getReportBOQ:e=>n.get("/api/reports/".concat(e,"/boq")),getReportUtilizzoBobine:e=>n.get("/api/reports/".concat(e,"/storico-bobine")),getReportProgress:e=>n.get("/api/reports/".concat(e,"/progress")),getReportPosaPeriodo:(e,a,t)=>{let i=new URLSearchParams;a&&i.append("data_inizio",a),t&&i.append("data_fine",t);let c=i.toString();return n.get("/api/reports/".concat(e,"/posa-periodo").concat(c?"?".concat(c):""))}},j={getCantieri:()=>n.get("/api/cantieri"),getCantiere:e=>n.get("/api/cantieri/".concat(e)),createCantiere:e=>n.post("/api/cantieri",e),updateCantiere:(e,a)=>n.put("/api/cantieri/".concat(e),a),getCantiereStatistics:e=>n.get("/api/cantieri/".concat(e,"/statistics")),getWeatherData:e=>n.get("/api/cantieri/".concat(e,"/weather"))},b={getUsers:()=>n.get("/api/users"),getUser:e=>n.get("/api/users/".concat(e)),createUser:e=>n.post("/api/users",e),updateUser:(e,a)=>n.put("/api/users/".concat(e),a),deleteUser:e=>n.delete("/api/users/".concat(e)),toggleUserStatus:e=>n.get("/api/users/toggle/".concat(e)),checkExpiredUsers:()=>n.get("/api/users/check-expired"),impersonateUser:e=>n.post("/api/auth/impersonate",{user_id:e}),getDatabaseData:()=>n.get("/api/users/db-raw"),resetDatabase:()=>n.post("/api/admin/reset-database")}},37051:(e,a,t)=>{Promise.resolve().then(t.bind(t,7946))}},e=>{var a=a=>e(e.s=a);e.O(0,[3464,3455,1909,9384,6955,168,2904,4782,8441,1684,7358],()=>a(37051)),_N_E=e.O()}]);