{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_1/src/app/api/auth/impersonate/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Estrai il corpo della richiesta\n    const body = await request.json()\n    const { user_id } = body\n\n    if (!user_id) {\n      return NextResponse.json(\n        { detail: 'user_id è richiesto' },\n        { status: 400 }\n      )\n    }\n\n    // Estrai il token di autorizzazione dall'header\n    const authHeader = request.headers.get('authorization')\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return NextResponse.json(\n        { detail: 'Token di autorizzazione mancante' },\n        { status: 401 }\n      )\n    }\n\n    const token = authHeader.substring(7)\n\n    console.log('🔄 Impersonate API: Proxying request to backend:', `http://localhost:8001/api/auth/impersonate`)\n    console.log('🔄 Impersonate API: Request body:', { user_id })\n\n    // Effettua la richiesta al backend FastAPI\n    const backendResponse = await fetch('http://localhost:8001/api/auth/impersonate', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${token}`,\n      },\n      body: JSON.stringify({ user_id })\n    })\n\n    console.log('📡 Impersonate API: Backend response status:', backendResponse.status)\n\n    if (!backendResponse.ok) {\n      const errorData = await backendResponse.text()\n      console.error('❌ Impersonate API: Backend error:', errorData)\n      \n      // Prova a parsare come JSON, altrimenti usa il testo\n      let errorResponse\n      try {\n        errorResponse = JSON.parse(errorData)\n      } catch {\n        errorResponse = { detail: errorData || 'Errore durante l\\'impersonificazione' }\n      }\n      \n      return NextResponse.json(errorResponse, { status: backendResponse.status })\n    }\n\n    const data = await backendResponse.json()\n    console.log('📡 Impersonate API: Backend response data:', data)\n\n    return NextResponse.json(data)\n  } catch (error) {\n    console.error('❌ Impersonate API: Error:', error)\n    return NextResponse.json(\n      { detail: 'Errore interno del server durante l\\'impersonificazione' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,kCAAkC;QAClC,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,OAAO,EAAE,GAAG;QAEpB,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,QAAQ;YAAsB,GAChC;gBAAE,QAAQ;YAAI;QAElB;QAEA,gDAAgD;QAChD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QACvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,QAAQ;YAAmC,GAC7C;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,QAAQ,WAAW,SAAS,CAAC;QAEnC,QAAQ,GAAG,CAAC,oDAAoD,CAAC,0CAA0C,CAAC;QAC5G,QAAQ,GAAG,CAAC,qCAAqC;YAAE;QAAQ;QAE3D,2CAA2C;QAC3C,MAAM,kBAAkB,MAAM,MAAM,8CAA8C;YAChF,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACpC;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAQ;QACjC;QAEA,QAAQ,GAAG,CAAC,gDAAgD,gBAAgB,MAAM;QAElF,IAAI,CAAC,gBAAgB,EAAE,EAAE;YACvB,MAAM,YAAY,MAAM,gBAAgB,IAAI;YAC5C,QAAQ,KAAK,CAAC,qCAAqC;YAEnD,qDAAqD;YACrD,IAAI;YACJ,IAAI;gBACF,gBAAgB,KAAK,KAAK,CAAC;YAC7B,EAAE,OAAM;gBACN,gBAAgB;oBAAE,QAAQ,aAAa;gBAAuC;YAChF;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,eAAe;gBAAE,QAAQ,gBAAgB,MAAM;YAAC;QAC3E;QAEA,MAAM,OAAO,MAAM,gBAAgB,IAAI;QACvC,QAAQ,GAAG,CAAC,8CAA8C;QAE1D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,QAAQ;QAA0D,GACpE;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}