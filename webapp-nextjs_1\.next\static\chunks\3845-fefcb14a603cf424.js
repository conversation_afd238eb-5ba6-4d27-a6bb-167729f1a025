"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3845],{1243:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},14186:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},35695:(e,t,r)=>{var a=r(18999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},53904:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},57434:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},60704:(e,t,r)=>{r.d(t,{B8:()=>M,UC:()=>T,bL:()=>C,l9:()=>D});var a=r(12115),n=r(85185),o=r(46081),i=r(89196),u=r(28905),s=r(63655),l=r(94315),c=r(5845),d=r(61285),f=r(95155),p="Tabs",[v,h]=(0,o.A)(p,[i.RG]),m=(0,i.RG)(),[b,y]=v(p),w=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,onValueChange:n,defaultValue:o,orientation:i="horizontal",dir:u,activationMode:v="automatic",...h}=e,m=(0,l.jH)(u),[y,w]=(0,c.i)({prop:a,onChange:n,defaultProp:null!=o?o:"",caller:p});return(0,f.jsx)(b,{scope:r,baseId:(0,d.B)(),value:y,onValueChange:w,orientation:i,dir:m,activationMode:v,children:(0,f.jsx)(s.sG.div,{dir:m,"data-orientation":i,...h,ref:t})})});w.displayName=p;var g="TabsList",k=a.forwardRef((e,t)=>{let{__scopeTabs:r,loop:a=!0,...n}=e,o=y(g,r),u=m(r);return(0,f.jsx)(i.bL,{asChild:!0,...u,orientation:o.orientation,dir:o.dir,loop:a,children:(0,f.jsx)(s.sG.div,{role:"tablist","aria-orientation":o.orientation,...n,ref:t})})});k.displayName=g;var A="TabsTrigger",R=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,disabled:o=!1,...u}=e,l=y(A,r),c=m(r),d=I(l.baseId,a),p=j(l.baseId,a),v=a===l.value;return(0,f.jsx)(i.q7,{asChild:!0,...c,focusable:!o,active:v,children:(0,f.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":v,"aria-controls":p,"data-state":v?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:d,...u,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(a)}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(a)}),onFocus:(0,n.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;v||o||!e||l.onValueChange(a)})})})});R.displayName=A;var x="TabsContent",F=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,forceMount:o,children:i,...l}=e,c=y(x,r),d=I(c.baseId,n),p=j(c.baseId,n),v=n===c.value,h=a.useRef(v);return a.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(u.C,{present:o||v,children:r=>{let{present:a}=r;return(0,f.jsx)(s.sG.div,{"data-state":v?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":d,hidden:!a,id:p,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:a&&i})}})});function I(e,t){return"".concat(e,"-trigger-").concat(t)}function j(e,t){return"".concat(e,"-content-").concat(t)}F.displayName=x;var C=w,M=k,D=R,T=F},89196:(e,t,r)=>{r.d(t,{RG:()=>k,bL:()=>D,q7:()=>T});var a=r(12115),n=r(85185),o=r(37328),i=r(6101),u=r(46081),s=r(61285),l=r(63655),c=r(39033),d=r(5845),f=r(94315),p=r(95155),v="rovingFocusGroup.onEntryFocus",h={bubbles:!1,cancelable:!0},m="RovingFocusGroup",[b,y,w]=(0,o.N)(m),[g,k]=(0,u.A)(m,[w]),[A,R]=g(m),x=a.forwardRef((e,t)=>(0,p.jsx)(b.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(b.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(F,{...e,ref:t})})}));x.displayName=m;var F=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:o,loop:u=!1,dir:s,currentTabStopId:b,defaultCurrentTabStopId:w,onCurrentTabStopIdChange:g,onEntryFocus:k,preventScrollOnEntryFocus:R=!1,...x}=e,F=a.useRef(null),I=(0,i.s)(t,F),j=(0,f.jH)(s),[C,D]=(0,d.i)({prop:b,defaultProp:null!=w?w:null,onChange:g,caller:m}),[T,P]=a.useState(!1),E=(0,c.c)(k),G=y(r),L=a.useRef(!1),[S,K]=a.useState(0);return a.useEffect(()=>{let e=F.current;if(e)return e.addEventListener(v,E),()=>e.removeEventListener(v,E)},[E]),(0,p.jsx)(A,{scope:r,orientation:o,dir:j,loop:u,currentTabStopId:C,onItemFocus:a.useCallback(e=>D(e),[D]),onItemShiftTab:a.useCallback(()=>P(!0),[]),onFocusableItemAdd:a.useCallback(()=>K(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>K(e=>e-1),[]),children:(0,p.jsx)(l.sG.div,{tabIndex:T||0===S?-1:0,"data-orientation":o,...x,ref:I,style:{outline:"none",...e.style},onMouseDown:(0,n.m)(e.onMouseDown,()=>{L.current=!0}),onFocus:(0,n.m)(e.onFocus,e=>{let t=!L.current;if(e.target===e.currentTarget&&t&&!T){let t=new CustomEvent(v,h);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=G().filter(e=>e.focusable);M([e.find(e=>e.active),e.find(e=>e.id===C),...e].filter(Boolean).map(e=>e.ref.current),R)}}L.current=!1}),onBlur:(0,n.m)(e.onBlur,()=>P(!1))})})}),I="RovingFocusGroupItem",j=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:o=!0,active:i=!1,tabStopId:u,children:c,...d}=e,f=(0,s.B)(),v=u||f,h=R(I,r),m=h.currentTabStopId===v,w=y(r),{onFocusableItemAdd:g,onFocusableItemRemove:k,currentTabStopId:A}=h;return a.useEffect(()=>{if(o)return g(),()=>k()},[o,g,k]),(0,p.jsx)(b.ItemSlot,{scope:r,id:v,focusable:o,active:i,children:(0,p.jsx)(l.sG.span,{tabIndex:m?0:-1,"data-orientation":h.orientation,...d,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{o?h.onItemFocus(v):e.preventDefault()}),onFocus:(0,n.m)(e.onFocus,()=>h.onItemFocus(v)),onKeyDown:(0,n.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void h.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var a;let n=(a=e.key,"rtl"!==r?a:"ArrowLeft"===a?"ArrowRight":"ArrowRight"===a?"ArrowLeft":a);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(n))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(n)))return C[n]}(e,h.orientation,h.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=w().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let a=r.indexOf(e.currentTarget);r=h.loop?function(e,t){return e.map((r,a)=>e[(t+a)%e.length])}(r,a+1):r.slice(a+1)}setTimeout(()=>M(r))}}),children:"function"==typeof c?c({isCurrentTabStop:m,hasTabStop:null!=A}):c})})});j.displayName=I;var C={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function M(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let a of e)if(a===r||(a.focus({preventScroll:t}),document.activeElement!==r))return}var D=x,T=j}}]);