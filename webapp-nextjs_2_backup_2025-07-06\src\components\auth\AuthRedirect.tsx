'use client'

import { useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'

/**
 * Componente per gestire i reindirizzamenti automatici basati sullo stato di autenticazione
 * Previene loop infiniti e gestisce correttamente i diversi tipi di utente
 */
export default function AuthRedirect() {
  const { user, cantiere, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const [hasRedirected, setHasRedirected] = useState(false)

  useEffect(() => {
    // Non fare nulla se stiamo ancora caricando
    if (isLoading) {
      console.log('🔀 AuthRedirect: Ancora in caricamento, attendo...')
      return
    }

    // Non fare nulla se abbiamo già reindirizzato di recente
    if (hasRedirected) {
      console.log('🔀 AuthRedirect: Reindirizzamento già effettuato, skip')
      return
    }

    console.log('🔀 AuthRedirect: Controllo reindirizzamento', {
      pathname,
      isAuthenticated,
      user: user ? { id: user.id_utente, ruolo: user.ruolo } : null,
      cantiere: cantiere ? { id: cantiere.id_cantiere } : null
    })

    // Pagine che non richiedono reindirizzamento automatico
    const noRedirectPages = [
      '/login',
      '/forgot-password',
      '/reset-password',
      '/change-password',
      '/simple-login',
      '/debug-login',
      '/test-login'
    ]

    if (noRedirectPages.includes(pathname)) {
      console.log('🔀 AuthRedirect: Pagina esclusa dal reindirizzamento automatico')
      return
    }

    // CRITICO: Se non autenticato, reindirizza SOLO al login
    if (!isAuthenticated) {
      console.log('🔀 AuthRedirect: Utente NON autenticato, reindirizzamento a /login')
      setHasRedirected(true)
      router.replace('/login')
      return
    }

    // SOLO se realmente autenticato con dati validi, gestisci i reindirizzamenti
    if (isAuthenticated && (user || cantiere)) {
      console.log('🔀 AuthRedirect: Utente autenticato, gestione reindirizzamenti basati su ruolo')

      // Utente admin (owner) - DEVE avere user valido
      if (user?.ruolo === 'owner') {
        if (pathname === '/' || pathname === '/login') {
          console.log('🔀 AuthRedirect: Admin su homepage, reindirizzamento a /admin')
          setHasRedirected(true)
          router.replace('/admin')
          return
        }
      }

      // Utente standard - DEVE avere user valido
      else if (user?.ruolo === 'user') {
        if (pathname === '/' || pathname === '/login') {
          console.log('🔀 AuthRedirect: Utente standard su homepage, reindirizzamento a /cantieri')
          setHasRedirected(true)
          router.replace('/cantieri')
          return
        }

        // Impedisci l'accesso alle pagine admin
        if (pathname.startsWith('/admin')) {
          console.log('🔀 AuthRedirect: Utente standard tenta accesso admin, reindirizzamento a /cantieri')
          setHasRedirected(true)
          router.replace('/cantieri')
          return
        }
      }

      // Utente cantiere - DEVE avere user valido
      else if (user?.ruolo === 'cantieri_user') {
        if (pathname === '/' || pathname === '/login') {
          console.log('🔀 AuthRedirect: Utente cantiere su homepage, reindirizzamento a /cavi')
          setHasRedirected(true)
          router.replace('/cavi')
          return
        }

        // Impedisci l'accesso a pagine non autorizzate
        if (!pathname.startsWith('/cavi')) {
          console.log('🔀 AuthRedirect: Utente cantiere su pagina non autorizzata, reindirizzamento a /cavi')
          setHasRedirected(true)
          router.replace('/cavi')
          return
        }
      }

      // Login cantiere (solo cantiere, nessun utente) - DEVE avere cantiere valido
      else if (cantiere && !user) {
        if (pathname === '/' || pathname === '/login') {
          console.log('🔀 AuthRedirect: Login cantiere su homepage, reindirizzamento a /cavi')
          setHasRedirected(true)
          router.replace('/cavi')
          return
        }

        // Impedisci l'accesso a tutte le altre pagine
        if (!pathname.startsWith('/cavi')) {
          console.log('🔀 AuthRedirect: Login cantiere su pagina non autorizzata, reindirizzamento a /cavi')
          setHasRedirected(true)
          router.replace('/cavi')
          return
        }
      }

      // Se isAuthenticated è true ma non abbiamo né user né cantiere validi, c'è un problema
      else {
        console.error('🔀 AuthRedirect: Stato inconsistente - isAuthenticated=true ma nessun user/cantiere valido')
        setHasRedirected(true)
        router.replace('/login')
        return
      }
    }

    // Reset del flag di reindirizzamento dopo un timeout
    const resetTimeout = setTimeout(() => {
      setHasRedirected(false)
    }, 2000)

    return () => {
      clearTimeout(resetTimeout)
    }
  }, [isAuthenticated, isLoading, user, cantiere, pathname, router, hasRedirected])

  // Questo componente non renderizza nulla
  return null
}
