'use client'

import { useState, useCallback, useEffect, useMemo } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Card, CardContent } from '@/components/ui/card'
import {
  Search,
  X,
  CheckSquare,
  Square,
  Filter,
  ChevronDown,
  ChevronUp,
  Zap,
  Package,
  Clock,
  CheckCircle,
  AlertTriangle,
  Settings
} from 'lucide-react'
import { Cavo } from '@/types'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'

interface SmartCaviFilterProps {
  cavi: Cavo[]
  onFilteredDataChange?: (filteredCavi: Cavo[]) => void
  loading?: boolean
  selectionEnabled?: boolean
  onSelectionToggle?: () => void
}

export default function SmartCaviFilter({
  cavi = [],
  onFilteredDataChange,
  loading = false,
  selectionEnabled = false,
  onSelectionToggle
}: SmartCaviFilterProps) {
  const [searchText, setSearchText] = useState('')
  const [searchType, setSearchType] = useState<'contains' | 'equals'>('contains')
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false)

  // Filtri avanzati
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [connectionFilter, setConnectionFilter] = useState<string>('all')
  const [certificationFilter, setCertificationFilter] = useState<string>('all')
  const [systemFilter, setSystemFilter] = useState<string>('all')
  const [meterRangeMin, setMeterRangeMin] = useState<string>('')
  const [meterRangeMax, setMeterRangeMax] = useState<string>('')

  // Normalize string for search
  const normalizeString = useCallback((str: string | null | undefined): string => {
    if (!str) return ''
    return str.toString().toLowerCase().trim()
  }, [])

  // Extract cable info for advanced search
  const getCavoInfo = useCallback((idCavo: string) => {
    const match = idCavo.match(/^([A-Z]+)(\d+)([A-Z]*)$/)
    if (match) {
      return {
        prefix: match[1],
        number: match[2],
        suffix: match[3] || ''
      }
    }
    return { prefix: '', number: idCavo, suffix: '' }
  }, [])

  // Check if a cable matches a search term
  const cavoMatchesTerm = useCallback((cavo: Cavo, term: string, exactMatch: boolean): boolean => {
    const normalizedTerm = normalizeString(term)
    
    if (!normalizedTerm) return true

    // Basic cable info
    const cavoId = normalizeString(cavo.id_cavo)
    const { prefix: cavoPrefix, number: cavoNumber, suffix: cavoSuffix } = getCavoInfo(cavo.id_cavo || '')
    
    // Cable properties
    const tipologia = normalizeString(cavo.tipologia)
    const formazione = normalizeString(cavo.formazione || cavo.sezione)
    const utility = normalizeString(cavo.utility)
    const sistema = normalizeString(cavo.sistema)
    
    // Locations
    const ubicazionePartenza = normalizeString(cavo.da || cavo.ubicazione_partenza)
    const ubicazioneArrivo = normalizeString(cavo.a || cavo.ubicazione_arrivo)
    const utenzaPartenza = normalizeString(cavo.utenza_partenza)
    const utenzaArrivo = normalizeString(cavo.utenza_arrivo)
    
    // Reel info
    const bobina = normalizeString(cavo.id_bobina)
    const bobinaDisplay = cavo.id_bobina === 'BOBINA_VUOTA' ? 'bobina vuota' :
                         cavo.id_bobina === null ? '' :
                         normalizeString(cavo.id_bobina)

    // All text fields to search
    const textFields = [
      cavoId, cavoPrefix, cavoNumber, cavoSuffix, tipologia, formazione, utility, sistema,
      ubicazionePartenza, ubicazioneArrivo, utenzaPartenza, utenzaArrivo,
      bobina, bobinaDisplay
    ]

    // Numeric fields for range search
    const numericFields = [
      { value: cavo.metri_teorici, name: 'metri_teorici' },
      { value: cavo.metratura_reale || cavo.metri_posati, name: 'metratura_reale' },
      { value: parseFloat(formazione), name: 'formazione' }
    ]

    // Check for range queries (e.g., ">100", "<=50")
    const rangeMatch = normalizedTerm.match(/^([><=]+)(\d+(?:\.\d+)?)$/)
    if (rangeMatch) {
      const operator = rangeMatch[1]
      const value = parseFloat(rangeMatch[2])
      
      return numericFields.some(field => {
        if (field.value == null || isNaN(field.value)) return false
        
        switch (operator) {
          case '>': return field.value > value
          case '>=': return field.value >= value
          case '<': return field.value < value
          case '<=': return field.value <= value
          case '=': return field.value === value
          default: return false
        }
      })
    }

    // Check for exact numeric match
    const numericTerm = parseFloat(normalizedTerm)
    if (!isNaN(numericTerm)) {
      const numericMatch = numericFields.some(field => 
        field.value != null && !isNaN(field.value) && field.value === numericTerm
      )
      if (numericMatch) return true
    }

    // Text search
    if (exactMatch) {
      return textFields.some(field => field === normalizedTerm)
    } else {
      return textFields.some(field => field.includes(normalizedTerm))
    }
  }, [normalizeString, getCavoInfo])

  // Funzione per applicare filtri avanzati
  const applyAdvancedFilters = useCallback((caviToFilter: Cavo[]) => {
    let filtered = caviToFilter

    // Filtro stato installazione
    if (statusFilter !== 'all') {
      filtered = filtered.filter(cavo => {
        switch (statusFilter) {
          case 'installati':
            return cavo.stato_installazione === 'Installato' ||
                   (cavo.metri_posati && cavo.metri_posati > 0) ||
                   (cavo.metratura_reale && cavo.metratura_reale > 0)
          case 'in_corso':
            return cavo.stato_installazione === 'In corso'
          case 'da_installare':
            return cavo.stato_installazione !== 'Installato' &&
                   cavo.stato_installazione !== 'In corso' &&
                   !(cavo.metri_posati && cavo.metri_posati > 0) &&
                   !(cavo.metratura_reale && cavo.metratura_reale > 0)
          default:
            return true
        }
      })
    }

    // Filtro collegamento
    if (connectionFilter !== 'all') {
      filtered = filtered.filter(cavo => {
        const collegamento = cavo.collegamento || cavo.collegamenti || 0
        switch (connectionFilter) {
          case 'collegati':
            return collegamento === 3
          case 'parziali':
            return collegamento === 1 || collegamento === 2
          case 'non_collegati':
            return collegamento === 0
          default:
            return true
        }
      })
    }

    // Filtro certificazione
    if (certificationFilter !== 'all') {
      filtered = filtered.filter(cavo => {
        const isCertified = cavo.certificato === true ||
                           cavo.certificato === 'SI' ||
                           cavo.certificato === 'CERTIFICATO'
        return certificationFilter === 'certificati' ? isCertified : !isCertified
      })
    }

    // Filtro sistema
    if (systemFilter !== 'all') {
      filtered = filtered.filter(cavo =>
        normalizeString(cavo.sistema) === normalizeString(systemFilter)
      )
    }

    // Filtro range metri
    if (meterRangeMin || meterRangeMax) {
      filtered = filtered.filter(cavo => {
        const metri = cavo.metri_posati || cavo.metratura_reale || 0
        const min = meterRangeMin ? parseFloat(meterRangeMin) : 0
        const max = meterRangeMax ? parseFloat(meterRangeMax) : Infinity
        return metri >= min && metri <= max
      })
    }

    return filtered
  }, [statusFilter, connectionFilter, certificationFilter, systemFilter, meterRangeMin, meterRangeMax, normalizeString])

  // Apply filter
  const applyFilter = useCallback(() => {
    let filtered = cavi

    // Prima applica la ricerca testuale
    if (searchText.trim()) {
      const searchTerms = searchText.split(',')
        .map(term => term.trim())
        .filter(term => term.length > 0)

      if (searchType === 'equals') {
        if (searchTerms.length === 1) {
          filtered = filtered.filter(cavo => cavoMatchesTerm(cavo, searchTerms[0], true))
        } else {
          filtered = filtered.filter(cavo =>
            searchTerms.every(term => cavoMatchesTerm(cavo, term, true))
          )
        }
      } else {
        filtered = filtered.filter(cavo =>
          searchTerms.some(term => cavoMatchesTerm(cavo, term, false))
        )
      }
    }

    // Poi applica i filtri avanzati
    filtered = applyAdvancedFilters(filtered)

    onFilteredDataChange?.(filtered)
  }, [searchText, searchType, cavi, cavoMatchesTerm, applyAdvancedFilters])

  // Calcola valori unici per i filtri
  const uniqueValues = useMemo(() => {
    const systems = [...new Set(cavi.map(c => c.sistema).filter(Boolean))].sort()
    return { systems }
  }, [cavi])

  // Funzioni per gestire i filtri
  const clearAllFilters = () => {
    setSearchText('')
    setStatusFilter('all')
    setConnectionFilter('all')
    setCertificationFilter('all')
    setSystemFilter('all')
    setMeterRangeMin('')
    setMeterRangeMax('')
  }

  const hasActiveFilters = searchText.trim() ||
                          statusFilter !== 'all' ||
                          connectionFilter !== 'all' ||
                          certificationFilter !== 'all' ||
                          systemFilter !== 'all' ||
                          meterRangeMin ||
                          meterRangeMax

  // Apply filter when dependencies change
  useEffect(() => {
    applyFilter()
  }, [searchText, searchType, cavi, statusFilter, connectionFilter, certificationFilter, systemFilter, meterRangeMin, meterRangeMax])

  const handleSearchTextChange = (value: string) => {
    setSearchText(value)
  }

  const clearFilter = () => {
    setSearchText('')
    setSearchType('contains')
  }

  return (
    <Card className="mb-4 shadow-sm border-slate-200">
      <CardContent className="p-4">
        {/* Header con controlli principali */}
        <div className="flex items-center gap-3 mb-4">
          {/* Search input principale */}
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              placeholder="Ricerca intelligente cavi (ID, sistema, metri, stato...)..."
              value={searchText}
              onChange={(e) => handleSearchTextChange(e.target.value)}
              disabled={loading}
              className="pl-10 pr-12 h-10 border-slate-300 focus:border-blue-500 focus:ring-blue-500"
            />
            {searchText && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-slate-100"
                onClick={clearFilter}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>

          {/* Search type selector */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="w-36">
                  <Select value={searchType} onValueChange={(value: 'contains' | 'equals') => setSearchType(value)}>
                    <SelectTrigger className="h-10 border-slate-300">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="contains">Contiene</SelectItem>
                      <SelectItem value="equals">Uguale a</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>Modalità di ricerca: contiene o corrispondenza esatta</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Filtri avanzati toggle */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant={isAdvancedOpen ? "default" : "outline"}
                  size="sm"
                  onClick={() => setIsAdvancedOpen(!isAdvancedOpen)}
                  className="flex items-center gap-2 transition-all duration-200 hover:scale-105"
                >
                  <Filter className="h-4 w-4" />
                  Filtri
                  {isAdvancedOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                  {hasActiveFilters && (
                    <Badge variant="secondary" className="ml-1 bg-blue-100 text-blue-800 text-xs">
                      Attivi
                    </Badge>
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Apri filtri avanzati per ricerca dettagliata</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Clear all filters - matching webapp vecchia style */}
          {hasActiveFilters && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={clearAllFilters}
                    disabled={loading}
                    className="flex items-center gap-2 transition-all duration-200"
                  >
                    <X className="h-4 w-4" />
                    Pulisci
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Rimuovi tutti i filtri attivi</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* Selection toggle button - matching webapp vecchia exactly */}
          {onSelectionToggle && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={selectionEnabled ? "default" : "outline"}
                    size="sm"
                    onClick={onSelectionToggle}
                    className="flex items-center gap-2 transition-all duration-200 hover:scale-105 font-normal hover:font-bold"
                  >
                    {selectionEnabled ? <CheckSquare className="h-4 w-4" /> : <Square className="h-4 w-4" />}
                    {selectionEnabled ? 'Disabilita Selezione' : 'Abilita Selezione'}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{selectionEnabled ? 'Disabilita la selezione multipla' : 'Abilita la selezione multipla'}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>

        {/* Filtri Avanzati - Sezione Espandibile */}
        {isAdvancedOpen && (
          <div className="mt-4 pt-4 border-t border-slate-200">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">

              {/* Filtro Stato Installazione */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-slate-700 flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  Stato Installazione
                </Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="h-9 border-slate-300">
                    <SelectValue placeholder="Tutti gli stati" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tutti gli stati</SelectItem>
                    <SelectItem value="installati">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full" />
                        Installati
                      </div>
                    </SelectItem>
                    <SelectItem value="in_corso">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full" />
                        In Corso
                      </div>
                    </SelectItem>
                    <SelectItem value="da_installare">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-gray-500 rounded-full" />
                        Da Installare
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Filtro Collegamento */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-slate-700 flex items-center gap-2">
                  <Zap className="w-4 h-4 text-blue-600" />
                  Collegamento
                </Label>
                <Select value={connectionFilter} onValueChange={setConnectionFilter}>
                  <SelectTrigger className="h-9 border-slate-300">
                    <SelectValue placeholder="Tutti i collegamenti" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tutti i collegamenti</SelectItem>
                    <SelectItem value="collegati">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full" />
                        Collegati (3/3)
                      </div>
                    </SelectItem>
                    <SelectItem value="parziali">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full" />
                        Parziali (1-2/3)
                      </div>
                    </SelectItem>
                    <SelectItem value="non_collegati">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-gray-500 rounded-full" />
                        Non Collegati (0/3)
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Filtro Certificazione */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-slate-700 flex items-center gap-2">
                  <Package className="w-4 h-4 text-purple-600" />
                  Certificazione
                </Label>
                <Select value={certificationFilter} onValueChange={setCertificationFilter}>
                  <SelectTrigger className="h-9 border-slate-300">
                    <SelectValue placeholder="Tutte le certificazioni" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tutte le certificazioni</SelectItem>
                    <SelectItem value="certificati">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-purple-500 rounded-full" />
                        Certificati
                      </div>
                    </SelectItem>
                    <SelectItem value="non_certificati">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-gray-500 rounded-full" />
                        Non Certificati
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Filtro Sistema */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-slate-700 flex items-center gap-2">
                  <Settings className="w-4 h-4 text-indigo-600" />
                  Sistema
                </Label>
                <Select value={systemFilter} onValueChange={setSystemFilter}>
                  <SelectTrigger className="h-9 border-slate-300">
                    <SelectValue placeholder="Tutti i sistemi" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tutti i sistemi</SelectItem>
                    {uniqueValues.systems.map(system => (
                      <SelectItem key={system} value={system}>
                        {system}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Range Metri */}
            <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-slate-700">Range Metri Installati</Label>
                <div className="flex items-center gap-2">
                  <Input
                    type="number"
                    placeholder="Min"
                    value={meterRangeMin}
                    onChange={(e) => setMeterRangeMin(e.target.value)}
                    className="h-9 border-slate-300"
                  />
                  <span className="text-slate-500">-</span>
                  <Input
                    type="number"
                    placeholder="Max"
                    value={meterRangeMax}
                    onChange={(e) => setMeterRangeMax(e.target.value)}
                    className="h-9 border-slate-300"
                  />
                </div>
              </div>
            </div>

            {/* Filtri attivi summary */}
            {hasActiveFilters && (
              <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Filter className="w-4 h-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-800">Filtri Attivi:</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearAllFilters}
                    className="text-blue-600 hover:text-blue-800 hover:bg-blue-100"
                  >
                    <X className="w-4 h-4 mr-1" />
                    Rimuovi Tutti
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {searchText && (
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                      Ricerca: "{searchText}"
                    </Badge>
                  )}
                  {statusFilter !== 'all' && (
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                      Stato: {statusFilter}
                    </Badge>
                  )}
                  {connectionFilter !== 'all' && (
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                      Collegamento: {connectionFilter}
                    </Badge>
                  )}
                  {certificationFilter !== 'all' && (
                    <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                      Certificazione: {certificationFilter}
                    </Badge>
                  )}
                  {systemFilter !== 'all' && (
                    <Badge variant="secondary" className="bg-indigo-100 text-indigo-800">
                      Sistema: {systemFilter}
                    </Badge>
                  )}
                  {(meterRangeMin || meterRangeMax) && (
                    <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                      Metri: {meterRangeMin || '0'} - {meterRangeMax || '∞'}
                    </Badge>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Search help text */}
        {searchText && !isAdvancedOpen && (
          <div className="mt-3 p-2 bg-slate-50 rounded-lg border border-slate-200">
            <div className="text-xs text-slate-600 flex items-center gap-2">
              <span>💡</span>
              <span>Suggerimenti: Usa virgole per ricerche multiple • Operatori: &gt;100, &lt;=50 per numeri</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
