{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/cavi/CaviTable.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useMemo, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { getCavoColorClasses } from '@/utils/softColors'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { TableRow, TableCell } from '@/components/ui/table'\nimport { Cavo } from '@/types'\nimport FilterableTable, { ColumnDef } from '@/components/common/FilterableTable'\nimport SmartCaviFilter from './SmartCaviFilter'\nimport TruncatedText from '@/components/common/TruncatedText'\n\nimport {\n  MoreHorizontal,\n  Cable,\n  Settings,\n  Zap,\n  CheckCircle,\n  AlertCircle,\n  Clock,\n  Package,\n  Unplug,\n  FileText,\n  Download,\n  X,\n  ArrowUpDown,\n  ArrowUp,\n  ArrowDown,\n  Eye,\n  Plus,\n  Edit,\n  Trash2,\n  ChevronLeft,\n  ChevronRight,\n  ChevronsLeft,\n  ChevronsRight,\n  <PERSON>,\n  Filter,\n  Info,\n  Link,\n  Unlink,\n  ChevronDown\n} from 'lucide-react'\nimport {\n  DisconnectCableDialog,\n  GeneratePDFDialog,\n  CertificationErrorDialog,\n  PDFGenerationConfig\n} from './CaviActionDialogs'\nimport { useToastActions } from '@/components/ui/toast-notification'\nimport {\n  Tooltip,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from '@/components/ui/tooltip'\n\ninterface CaviTableProps {\n  cavi: Cavo[]\n  loading?: boolean\n  selectionEnabled?: boolean\n  selectedCavi?: string[]\n  onSelectionChange?: (selectedIds: string[]) => void\n  onStatusAction?: (cavo: Cavo, action: string) => void\n  onContextMenuAction?: (cavo: Cavo, action: string) => void\n  onDisconnectCable?: (cavoId: string) => Promise<void>\n  onGeneratePDF?: (config: PDFGenerationConfig) => Promise<void>\n  onCertifyCable?: (cavoId: string) => Promise<void>\n}\n\ntype SortField = 'id_cavo' | 'sistema' | 'metri_teorici' | 'metri_posati' | 'stato'\ntype SortDirection = 'asc' | 'desc'\n\ninterface DialogState {\n  disconnect: { isOpen: boolean; cavo: Cavo | null }\n  generatePDF: { isOpen: boolean; cavo: Cavo | null }\n  certificationError: { isOpen: boolean; cavo: Cavo | null; missingRequirements: string[] }\n}\n\nexport default function CaviTable({\n  cavi = [],\n  loading = false,\n  selectionEnabled = false,\n  selectedCavi = [],\n  onSelectionChange,\n  onStatusAction,\n  onContextMenuAction,\n  onDisconnectCable,\n  onGeneratePDF,\n  onCertifyCable\n}: CaviTableProps) {\n  const [smartFilteredCavi, setSmartFilteredCavi] = useState(cavi)\n  const [filteredCavi, setFilteredCavi] = useState(cavi)\n  const [internalSelectionEnabled, setInternalSelectionEnabled] = useState(selectionEnabled)\n  const [sortField, setSortField] = useState<SortField>('id_cavo')\n  const [sortDirection, setSortDirection] = useState<SortDirection>('asc')\n  const [dialogs, setDialogs] = useState<DialogState>({\n    disconnect: { isOpen: false, cavo: null },\n    generatePDF: { isOpen: false, cavo: null },\n    certificationError: { isOpen: false, cavo: null, missingRequirements: [] }\n  })\n\n  // Stati per la paginazione\n  const [currentPage, setCurrentPage] = useState(1)\n  const [itemsPerPage, setItemsPerPage] = useState(25)\n  const [showBulkActions, setShowBulkActions] = useState(false)\n\n  const toast = useToastActions()\n\n  // Funzioni per gestire i dialog\n  const openDialog = (type: keyof DialogState, cavo: Cavo, missingRequirements: string[] = []) => {\n    setDialogs(prev => ({\n      ...prev,\n      [type]: { isOpen: true, cavo, missingRequirements }\n    }))\n  }\n\n  const closeDialog = (type: keyof DialogState) => {\n    setDialogs(prev => ({\n      ...prev,\n      [type]: { isOpen: false, cavo: null, missingRequirements: [] }\n    }))\n  }\n\n  // Funzione per ordinamento\n  const handleSort = (field: SortField) => {\n    if (sortField === field) {\n      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc')\n    } else {\n      setSortField(field)\n      setSortDirection('asc')\n    }\n  }\n\n  // Ordinamento dei cavi\n  const sortedCavi = useMemo(() => {\n    const sorted = [...smartFilteredCavi].sort((a, b) => {\n      let aValue: any = a[sortField]\n      let bValue: any = b[sortField]\n\n      // Gestione valori numerici\n      if (sortField === 'metri_teorici' || sortField === 'metri_posati') {\n        aValue = parseFloat(aValue) || 0\n        bValue = parseFloat(bValue) || 0\n      }\n\n      // Gestione stringhe\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase()\n      }\n      if (typeof bValue === 'string') {\n        bValue = bValue.toLowerCase()\n      }\n\n      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1\n      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1\n      return 0\n    })\n    return sorted\n  }, [smartFilteredCavi, sortField, sortDirection])\n\n  // Aggiorna i cavi quando cambiano i cavi originali\n  useEffect(() => {\n    setSmartFilteredCavi(cavi)\n  }, [cavi])\n\n  // Calcolo paginazione\n  const paginatedData = useMemo(() => {\n    const startIndex = (currentPage - 1) * itemsPerPage\n    const endIndex = startIndex + itemsPerPage\n    const paginatedCavi = sortedCavi.slice(startIndex, endIndex)\n\n    const totalPages = Math.ceil(sortedCavi.length / itemsPerPage)\n    const hasNextPage = currentPage < totalPages\n    const hasPrevPage = currentPage > 1\n\n    return {\n      cavi: paginatedCavi,\n      totalItems: sortedCavi.length,\n      totalPages,\n      hasNextPage,\n      hasPrevPage,\n      startIndex: startIndex + 1,\n      endIndex: Math.min(endIndex, sortedCavi.length)\n    }\n  }, [sortedCavi, currentPage, itemsPerPage])\n\n  // Aggiorna filteredCavi quando cambiano i cavi paginati\n  useEffect(() => {\n    setFilteredCavi(paginatedData.cavi)\n  }, [paginatedData.cavi])\n\n  // Reset pagina quando cambiano i filtri\n  useEffect(() => {\n    setCurrentPage(1)\n  }, [smartFilteredCavi.length])\n\n  // Gestione filtri intelligenti\n  const handleSmartFilterChange = (filtered: Cavo[]) => {\n    setSmartFilteredCavi(filtered)\n  }\n\n  // Gestione filtri tabella\n  const handleTableFilterChange = (filtered: Cavo[]) => {\n    setFilteredCavi(filtered)\n  }\n\n  // Gestione azioni sui cavi\n  const handleDisconnectCable = async (cavoId: string) => {\n    try {\n      if (onDisconnectCable) {\n        await onDisconnectCable(cavoId)\n        toast.cavoDisconnected(cavoId)\n      }\n    } catch (error) {\n      toast.error('Errore Scollegamento', 'Impossibile scollegare il cavo. Riprova.')\n    }\n  }\n\n  const handleGeneratePDF = async (config: PDFGenerationConfig) => {\n    try {\n      if (onGeneratePDF) {\n        await onGeneratePDF(config)\n        toast.pdfGenerated(config.fileName, config.cavoId)\n      }\n    } catch (error) {\n      toast.error('Errore Generazione PDF', 'Impossibile generare il certificato. Riprova.')\n    }\n  }\n\n  const handleCertifyCable = async (cavo: Cavo) => {\n    // Verifica requisiti per certificazione\n    const missingRequirements: string[] = []\n\n    if (!cavo.metri_posati || parseFloat(cavo.metri_posati) === 0) {\n      missingRequirements.push('Metri posati non inseriti')\n    }\n\n    if (cavo.stato !== 'Collegato') {\n      missingRequirements.push('Cavo non collegato')\n    }\n\n    if (!cavo.data_installazione) {\n      missingRequirements.push('Data installazione mancante')\n    }\n\n    if (missingRequirements.length > 0) {\n      openDialog('certificationError', cavo, missingRequirements)\n      return\n    }\n\n    try {\n      if (onCertifyCable) {\n        toast.actionInProgress('Certificazione', cavo.id_cavo)\n        await onCertifyCable(cavo.id_cavo)\n        toast.success('Cavo Certificato', `Il cavo ${cavo.id_cavo} è stato certificato con successo.`)\n      }\n    } catch (error) {\n      toast.certificationError(cavo.id_cavo, 'Errore durante il processo di certificazione')\n    }\n  }\n\n  const handleSelectionToggle = () => {\n    setInternalSelectionEnabled(!internalSelectionEnabled)\n    setShowBulkActions(!internalSelectionEnabled && selectedCavi.length > 0)\n  }\n\n  // Funzioni per la paginazione\n  const goToPage = (page: number) => {\n    setCurrentPage(Math.max(1, Math.min(page, paginatedData.totalPages)))\n  }\n\n  const goToFirstPage = () => goToPage(1)\n  const goToLastPage = () => goToPage(paginatedData.totalPages)\n  const goToPrevPage = () => goToPage(currentPage - 1)\n  const goToNextPage = () => goToPage(currentPage + 1)\n\n  // Funzioni per selezione multipla\n  const selectAllVisible = () => {\n    const visibleIds = paginatedData.cavi.map(c => c.id_cavo)\n    const newSelection = [...new Set([...selectedCavi, ...visibleIds])]\n    onSelectionChange?.(newSelection)\n  }\n\n  const deselectAllVisible = () => {\n    const visibleIds = new Set(paginatedData.cavi.map(c => c.id_cavo))\n    const newSelection = selectedCavi.filter(id => !visibleIds.has(id))\n    onSelectionChange?.(newSelection)\n  }\n\n  const selectAll = () => {\n    const allIds = sortedCavi.map(c => c.id_cavo)\n    onSelectionChange?.(allIds)\n  }\n\n  const deselectAll = () => {\n    onSelectionChange?.([])\n  }\n\n  // Verifica se tutti i cavi visibili sono selezionati\n  const allVisibleSelected = paginatedData.cavi.length > 0 &&\n    paginatedData.cavi.every(cavo => selectedCavi.includes(cavo.id_cavo))\n\n  const someVisibleSelected = paginatedData.cavi.some(cavo => selectedCavi.includes(cavo.id_cavo))\n\n  // Componente per header ordinabile\n  const SortableHeader = ({ field, children, className = \"\" }: {\n    field: SortField;\n    children: React.ReactNode;\n    className?: string\n  }) => (\n    <TooltipProvider>\n      <Tooltip>\n        <TooltipTrigger asChild>\n          <button\n            onClick={() => handleSort(field)}\n            className={`flex items-center gap-2 font-medium text-left hover:text-blue-600 transition-colors ${className}`}\n          >\n            {children}\n            {sortField === field ? (\n              sortDirection === 'asc' ? (\n                <ArrowUp className=\"w-4 h-4 text-blue-600\" />\n              ) : (\n                <ArrowDown className=\"w-4 h-4 text-blue-600\" />\n              )\n            ) : (\n              <ArrowUpDown className=\"w-4 h-4 text-slate-400\" />\n            )}\n          </button>\n        </TooltipTrigger>\n        <TooltipContent>\n          <p>Clicca per ordinare per {children}</p>\n        </TooltipContent>\n      </Tooltip>\n    </TooltipProvider>\n  )\n\n  // Componente per pulsanti azione con tooltip\n  const ActionButton = ({\n    onClick,\n    icon: Icon,\n    tooltip,\n    variant = \"default\",\n    disabled = false,\n    className = \"\"\n  }: {\n    onClick: () => void;\n    icon: any;\n    tooltip: string;\n    variant?: \"default\" | \"danger\" | \"success\" | \"warning\";\n    disabled?: boolean;\n    className?: string;\n  }) => {\n    const variantClasses = {\n      default: \"bg-blue-600 hover:bg-blue-700 text-white\",\n      danger: \"bg-red-600 hover:bg-red-700 text-white\",\n      success: \"bg-green-600 hover:bg-green-700 text-white\",\n      warning: \"bg-orange-600 hover:bg-orange-700 text-white\"\n    }\n\n    return (\n      <TooltipProvider>\n        <Tooltip>\n          <TooltipTrigger asChild>\n            <Button\n              size=\"sm\"\n              onClick={onClick}\n              disabled={disabled}\n              className={`h-8 w-8 p-0 ${variantClasses[variant]} ${className} transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100`}\n            >\n              <Icon className=\"w-4 h-4\" />\n            </Button>\n          </TooltipTrigger>\n          <TooltipContent>\n            <p>{tooltip}</p>\n          </TooltipContent>\n        </Tooltip>\n      </TooltipProvider>\n    )\n  }\n\n  // Gestione selezione\n  const handleSelectAll = (checked: boolean) => {\n    if (onSelectionChange) {\n      onSelectionChange(checked ? filteredCavi.map(c => c.id_cavo) : [])\n    }\n  }\n\n  const handleSelectCavo = (cavoId: string, checked: boolean) => {\n    if (onSelectionChange) {\n      const newSelection = checked\n        ? [...selectedCavi, cavoId]\n        : selectedCavi.filter(id => id !== cavoId)\n      onSelectionChange(newSelection)\n    }\n  }\n\n  // Bulk action handlers\n  const handleBulkExport = async () => {\n    try {\n      const response = await fetch('/api/cavi/export', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          selectedIds: selectedCavi,\n          cantiereId: 1 // TODO: Get from context\n        })\n      })\n\n      if (response.ok) {\n        const blob = await response.blob()\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = `cavi_export_${new Date().toISOString().split('T')[0]}.csv`\n        document.body.appendChild(a)\n        a.click()\n        window.URL.revokeObjectURL(url)\n        document.body.removeChild(a)\n      } else {\n        const error = await response.json()\n        alert(`Errore durante l'esportazione: ${error.error}`)\n      }\n    } catch (error) {\n      alert('Errore durante l\\'esportazione')\n    }\n  }\n\n  const handleBulkStatusChange = async () => {\n    const newStatus = prompt('Inserisci il nuovo stato (Da installare, In corso, Installato):')\n    if (!newStatus) return\n\n    try {\n      const response = await fetch('/api/cavi/bulk-status', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          selectedIds: selectedCavi,\n          cantiereId: 1, // TODO: Get from context\n          newStatus\n        })\n      })\n\n      const result = await response.json()\n      if (result.success) {\n        alert(result.message)\n        // TODO: Refresh data\n      } else {\n        alert(`Errore: ${result.error}`)\n      }\n    } catch (error) {\n      alert('Errore durante il cambio stato')\n    }\n  }\n\n  const handleBulkAssignCommand = () => {\n    // TODO: Implementare modal per selezione comanda\n    alert(`Assegnazione comanda per ${selectedCavi.length} cavi`)\n  }\n\n  const handleBulkDelete = async () => {\n    if (!confirm(`Sei sicuro di voler eliminare ${selectedCavi.length} cavi?`)) {\n      return\n    }\n\n    try {\n      const response = await fetch('/api/cavi/bulk-delete', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          selectedIds: selectedCavi,\n          cantiereId: 1 // TODO: Get from context\n        })\n      })\n\n      const result = await response.json()\n      if (result.success) {\n        alert(result.message)\n        // TODO: Refresh data\n      } else {\n        alert(`Errore: ${result.error}`)\n      }\n    } catch (error) {\n      alert('Errore durante l\\'eliminazione')\n    }\n  }\n\n  // Define columns matching original webapp structure\n  const columns: ColumnDef[] = useMemo(() => {\n    const baseColumns: ColumnDef[] = [\n      {\n        field: 'id_cavo',\n        headerName: 'ID',\n        dataType: 'text',\n        width: 70,\n        align: 'left',\n        renderHeader: () => (\n          <SortableHeader field=\"id_cavo\">ID</SortableHeader>\n        ),\n        renderCell: (row: Cavo) => (\n          <span className=\"font-semibold text-mariner-900\">{row.id_cavo}</span>\n        )\n      },\n      {\n        field: 'sistema',\n        headerName: 'Sistema',\n        dataType: 'text',\n        width: 80,\n        renderHeader: () => (\n          <SortableHeader field=\"sistema\">Sistema</SortableHeader>\n        ),\n        renderCell: (row: Cavo) => (\n          <TruncatedText text={row.sistema || ''} maxLength={8} />\n        )\n      },\n      {\n        field: 'utility',\n        headerName: 'Utility',\n        dataType: 'text',\n        width: 80,\n        renderCell: (row: Cavo) => (\n          <TruncatedText text={row.utility || ''} maxLength={8} />\n        )\n      },\n      {\n        field: 'tipologia',\n        headerName: 'Tipologia',\n        dataType: 'text',\n        width: 100,\n        renderCell: (row: Cavo) => (\n          <TruncatedText text={row.tipologia || ''} maxLength={12} />\n        )\n      },\n      {\n        field: 'formazione',\n        headerName: 'Form.',\n        dataType: 'text',\n        align: 'left',\n        width: 60,\n        renderCell: (row: Cavo) => row.formazione || row.sezione\n      },\n      {\n        field: 'metri_teorici',\n        headerName: 'M.Teor.',\n        dataType: 'number',\n        align: 'left',\n        width: 70,\n        renderHeader: () => (\n          <SortableHeader field=\"metri_teorici\">M.Teor.</SortableHeader>\n        ),\n        renderCell: (row: Cavo) => row.metri_teorici ? row.metri_teorici.toFixed(1) : '0'\n      },\n      {\n        field: 'metri_posati',\n        headerName: 'M.Reali',\n        dataType: 'number',\n        align: 'left',\n        width: 70,\n        renderHeader: () => (\n          <SortableHeader field=\"metri_posati\">M.Reali</SortableHeader>\n        ),\n        renderCell: (row: Cavo) => {\n          const metri = row.metri_posati || row.metratura_reale || 0\n          return metri ? metri.toFixed(1) : '0'\n        }\n      },\n      {\n        field: 'ubicazione_partenza',\n        headerName: 'Da',\n        dataType: 'text',\n        width: 140,\n        renderCell: (row: Cavo) => (\n          <TruncatedText\n            text={row.da || row.ubicazione_partenza || ''}\n            maxLength={18}\n          />\n        )\n      },\n      {\n        field: 'ubicazione_arrivo',\n        headerName: 'A',\n        dataType: 'text',\n        width: 140,\n        renderCell: (row: Cavo) => (\n          <TruncatedText\n            text={row.a || row.ubicazione_arrivo || ''}\n            maxLength={18}\n          />\n        )\n      },\n      {\n        field: 'id_bobina',\n        headerName: 'Bobina',\n        dataType: 'text',\n        width: 100,\n        align: 'center',\n        renderCell: (row: Cavo) => getBobinaButton(row)\n      },\n      {\n        field: 'stato_installazione',\n        headerName: 'Stato',\n        dataType: 'text',\n        align: 'left',\n        width: 130,\n        renderHeader: () => (\n          <SortableHeader field=\"stato\">Stato</SortableHeader>\n        ),\n        renderCell: (row: Cavo) => getStatusButton(row)\n      },\n      {\n        field: 'collegamenti',\n        headerName: 'Collegamenti',\n        dataType: 'text',\n        align: 'left',\n        width: 160,\n        disableSort: true,\n        renderCell: (row: Cavo) => getConnectionButton(row)\n      },\n      {\n        field: 'certificato',\n        headerName: 'Certificato',\n        dataType: 'text',\n        align: 'left',\n        width: 170,\n        disableSort: true,\n        renderCell: (row: Cavo) => getCertificationButton(row)\n      },\n\n    ]\n\n    // Add selection column if enabled\n    if (internalSelectionEnabled) {\n      baseColumns.unshift({\n        field: 'selection',\n        headerName: '',\n        disableFilter: true,\n        disableSort: true,\n        width: 50,\n        align: 'left',\n        renderHeader: () => (\n          <Checkbox\n            checked={selectedCavi.length === filteredCavi.length && filteredCavi.length > 0}\n            onCheckedChange={handleSelectAll}\n          />\n        ),\n        renderCell: (row: Cavo) => (\n          <Checkbox\n            checked={selectedCavi.includes(row.id_cavo)}\n            onCheckedChange={(checked) => handleSelectCavo(row.id_cavo, checked as boolean)}\n            onClick={(e) => e.stopPropagation()}\n          />\n        )\n      })\n    }\n\n    return baseColumns\n  }, [internalSelectionEnabled, selectedCavi, filteredCavi, handleSelectAll, handleSelectCavo])\n\n  // Custom row renderer for selection and context menu\n  const renderRow = (row: Cavo, index: number) => {\n    const isSelected = selectedCavi.includes(row.id_cavo)\n    const hasMultipleSelection = selectedCavi.length > 1\n\n    const handleContextMenu = (e: React.MouseEvent) => {\n      e.preventDefault()\n      // Per ora disabilitiamo il context menu per evitare errori di hydration\n      // TODO: Implementare context menu senza wrapper div\n      console.log('Context menu per cavo:', row.id_cavo)\n    }\n\n    return (\n      <TableRow\n        key={row.id_cavo}\n        className={`\n          ${isSelected ? 'bg-blue-50 border-blue-200' : 'bg-white'}\n          hover:bg-blue-50 hover:border-blue-200 hover:shadow-sm\n          cursor-pointer border-b border-gray-200\n          transition-all duration-200 ease-in-out\n          ${isSelected ? 'ring-1 ring-blue-300' : ''}\n        `}\n        onClick={() => internalSelectionEnabled && handleSelectCavo(row.id_cavo, !isSelected)}\n        onContextMenu={handleContextMenu}\n      >\n        {columns.map((column) => (\n          <TableCell\n            key={column.field}\n            className={`\n              py-2 px-2 text-sm text-left\n              ${isSelected ? 'text-blue-900' : 'text-gray-900'}\n              transition-colors duration-200\n            `}\n            style={{ width: column.width, ...column.cellStyle }}\n            onClick={(e) => {\n              // Prevent row click for action columns\n              if (['stato_installazione', 'collegamenti', 'certificato'].includes(column.field)) {\n                e.stopPropagation()\n              }\n            }}\n          >\n            {column.renderCell ? column.renderCell(row) : (row[column.field] || <span className=\"text-gray-400\">-</span>)}\n          </TableCell>\n        ))}\n      </TableRow>\n    )\n  }\n\n  // Funzione per la colonna Bobina (Indicativa - Azione Implicita)\n  const getBobinaButton = (cavo: Cavo) => {\n    const idBobina = cavo.id_bobina\n\n    if (!idBobina || idBobina === 'N/A') {\n      return <span className=\"text-gray-400\">-</span>\n    }\n\n    if (idBobina === 'BOBINA_VUOTA') {\n      return (\n        <Badge\n          variant=\"outline\"\n          className=\"text-xs px-2 py-0.5 text-gray-500 border-gray-300 bg-gray-50\"\n        >\n          Vuota\n        </Badge>\n      )\n    }\n\n    // Estrai il numero della bobina usando i pattern esistenti\n    let displayValue = idBobina\n    let match = idBobina.match(/_B(.+)$/)\n    if (match) {\n      displayValue = match[1]\n    } else {\n      match = idBobina.match(/_b(.+)$/)\n      if (match) {\n        displayValue = match[1]\n      } else {\n        match = idBobina.match(/c\\d+_[bB](\\d+)$/)\n        if (match) {\n          displayValue = match[1]\n        } else {\n          match = idBobina.match(/(\\d+)$/)\n          if (match) {\n            displayValue = match[1]\n          }\n        }\n      }\n    }\n\n    return (\n      <TooltipProvider>\n        <Tooltip>\n          <TooltipTrigger asChild>\n            <Badge\n              variant=\"outline\"\n              className=\"cursor-pointer hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 hover:shadow-sm transition-all duration-200 px-2 py-1 font-medium text-slate-700 border-slate-300 bg-white flex items-center gap-1\"\n              onClick={(e) => {\n                e.stopPropagation()\n                // Determina l'azione basata sullo stato del cavo\n                const metriInstallati = cavo.metri_posati || cavo.metratura_reale || 0\n                const isInstalled = metriInstallati > 0\n\n                if (isInstalled) {\n                  // Se il cavo è installato, permetti modifica bobina\n                  onStatusAction?.(cavo, 'modify_reel')\n                } else {\n                  // Se il cavo non è installato, permetti inserimento metri\n                  onStatusAction?.(cavo, 'insert_meters')\n                }\n              }}\n            >\n              <span>{displayValue}</span>\n              <ChevronDown className=\"h-3 w-3 opacity-60\" />\n            </Badge>\n          </TooltipTrigger>\n          <TooltipContent>\n            <p>\n              {(() => {\n                const metriInstallati = cavo.metri_posati || cavo.metratura_reale || 0\n                const isInstalled = metriInstallati > 0\n                return isInstalled\n                  ? \"Clicca per modificare bobina\"\n                  : \"Clicca per inserire metri posati\"\n              })()}\n            </p>\n          </TooltipContent>\n        </Tooltip>\n      </TooltipProvider>\n    )\n  }\n\n  // Funzioni di utilità per lo stato\n  const getStatusBadge = (cavo: Cavo) => {\n    // Verifica se il cavo è assegnato a una comanda\n    const comandaPosa = cavo.comanda_posa\n    const comandaPartenza = cavo.comanda_partenza\n    const comandaArrivo = cavo.comanda_arrivo\n    const comandaCertificazione = cavo.comanda_certificazione\n\n    // Trova la comanda attiva (priorità: posa > partenza > arrivo > certificazione)\n    const comandaAttiva = comandaPosa || comandaPartenza || comandaArrivo || comandaCertificazione\n\n    // Se c'è una comanda attiva e lo stato è \"In corso\", mostra il codice comanda\n    if (comandaAttiva && cavo.stato_installazione === 'In corso') {\n      const colorClasses = getCavoColorClasses('IN_CORSO')\n      return (\n        <Badge\n          className={`cursor-pointer ${colorClasses.badge} ${colorClasses.hover}`}\n          onClick={() => onStatusAction?.(cavo, 'view_command', comandaAttiva)}\n        >\n          {comandaAttiva}\n        </Badge>\n      )\n    }\n\n    // Logica normale per gli altri stati\n    const stato = cavo.stato_installazione || 'Da installare'\n    const colorClasses = getCavoColorClasses(stato)\n\n    return (\n      <Badge className={colorClasses.badge}>\n        {stato}\n      </Badge>\n    )\n  }\n\n  const getStatusButton = (cavo: Cavo) => {\n    // Verifica se il cavo è installato controllando metri_posati o metratura_reale\n    const metriInstallati = cavo.metri_posati || cavo.metratura_reale || 0\n    const isInstalled = metriInstallati > 0\n\n    // Verifica se il cavo è assegnato a una comanda attiva\n    const comandaPosa = cavo.comanda_posa\n    const comandaPartenza = cavo.comanda_partenza\n    const comandaArrivo = cavo.comanda_arrivo\n    const comandaCertificazione = cavo.comanda_certificazione\n    const comandaAttiva = comandaPosa || comandaPartenza || comandaArrivo || comandaCertificazione\n\n    // Se c'è una comanda attiva e lo stato è \"In corso\", mostra il codice comanda (questo rimane cliccabile)\n    if (comandaAttiva && cavo.stato_installazione === 'In corso') {\n      return (\n        <TooltipProvider>\n          <Tooltip>\n            <TooltipTrigger asChild>\n              <Badge\n                className=\"bg-blue-500 text-white cursor-pointer hover:bg-blue-600 transition-colors duration-200 px-3 py-1 font-medium\"\n                onClick={(e) => {\n                  e.stopPropagation()\n                  onStatusAction?.(cavo, 'view_command', comandaAttiva)\n                }}\n              >\n                {comandaAttiva}\n              </Badge>\n            </TooltipTrigger>\n            <TooltipContent>\n              <p>Visualizza dettagli comanda</p>\n            </TooltipContent>\n          </Tooltip>\n        </TooltipProvider>\n      )\n    }\n\n    // Determina lo stato del cavo - BADGE PURAMENTE INDICATIVI (non cliccabili)\n    const stato = cavo.stato_installazione || 'Da installare'\n\n    if (stato === 'Installato' || isInstalled) {\n      return (\n        <Badge className=\"bg-green-100 text-green-700 px-3 py-1 font-medium border border-green-200\">\n          Installato\n        </Badge>\n      )\n    } else if (stato === 'In corso') {\n      return (\n        <Badge className=\"bg-yellow-100 text-yellow-700 px-3 py-1 font-medium border border-yellow-200\">\n          In corso\n        </Badge>\n      )\n    } else {\n      return (\n        <TooltipProvider>\n          <Tooltip>\n            <TooltipTrigger asChild>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                className=\"h-7 px-2 text-xs font-medium border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-colors duration-200\"\n                onClick={(e) => {\n                  e.stopPropagation()\n                  onStatusAction?.(cavo, 'inserisci_metri')\n                }}\n              >\n                <Plus className=\"h-3 w-3 mr-1\" />\n                Da installare\n              </Button>\n            </TooltipTrigger>\n            <TooltipContent>\n              <p>Clicca per inserire metri posati</p>\n            </TooltipContent>\n          </Tooltip>\n        </TooltipProvider>\n      )\n    }\n  }\n\n  const getConnectionButton = (cavo: Cavo) => {\n    // Verifica se il cavo è installato o in corso di installazione\n    const metriInstallati = cavo.metri_posati || cavo.metratura_reale || 0\n    const statoInstallazione = cavo.stato_installazione || 'Da installare'\n    const isInstalled = metriInstallati > 0 || statoInstallazione === 'Installato' || statoInstallazione === 'In corso'\n    const collegamento = cavo.collegamento || cavo.collegamenti || 0\n\n    // \"Non disponibile\" - Design distinto per elementi non interattivi\n    if (!isInstalled) {\n      return (\n        <TooltipProvider>\n          <Tooltip>\n            <TooltipTrigger asChild>\n              <span className=\"text-gray-400 text-sm px-2 py-1 flex items-center gap-1\">\n                <Info className=\"h-3 w-3\" />\n                Non disponibile\n              </span>\n            </TooltipTrigger>\n            <TooltipContent>\n              <p>Il collegamento non può essere gestito perché il cavo non è installato</p>\n            </TooltipContent>\n          </Tooltip>\n        </TooltipProvider>\n      )\n    }\n\n    // Pulsanti di azione - Stile outline con hover states chiari\n    let label, icon, actionType, buttonClass\n\n    switch (collegamento) {\n      case 0:\n        label = \"Collega\"\n        icon = Link\n        actionType = \"connect_cable\"\n        buttonClass = \"border-blue-300 text-blue-700 hover:bg-blue-50 hover:border-blue-400\"\n        break\n      case 1:\n        label = \"Completa Arrivo\"\n        icon = Link\n        actionType = \"connect_arrival\"\n        buttonClass = \"border-yellow-300 text-yellow-700 hover:bg-yellow-50 hover:border-yellow-400\"\n        break\n      case 2:\n        label = \"Completa Partenza\"\n        icon = Link\n        actionType = \"connect_departure\"\n        buttonClass = \"border-yellow-300 text-yellow-700 hover:bg-yellow-50 hover:border-yellow-400\"\n        break\n      case 3:\n        label = \"Scollega\"\n        icon = Unlink\n        actionType = \"disconnect_cable\"\n        buttonClass = \"border-red-300 text-red-700 hover:bg-red-50 hover:border-red-400\"\n        break\n      default:\n        label = \"Gestisci\"\n        icon = Settings\n        actionType = \"manage_connections\"\n        buttonClass = \"border-blue-300 text-blue-700 hover:bg-blue-50 hover:border-blue-400\"\n        break\n    }\n\n    const IconComponent = icon\n\n    return (\n      <Button\n        variant=\"outline\"\n        size=\"sm\"\n        className={`h-7 px-2 text-xs font-medium transition-colors duration-200 ${buttonClass}`}\n        onClick={(e) => {\n          e.stopPropagation()\n          if (actionType === 'disconnect_cable') {\n            openDialog('disconnect', cavo)\n          } else {\n            onStatusAction?.(cavo, actionType)\n          }\n        }}\n      >\n        <IconComponent className=\"h-3 w-3 mr-1\" />\n        {label}\n      </Button>\n    )\n  }\n\n  const getCertificationButton = (cavo: Cavo) => {\n    // Verifica se il cavo è installato o in corso di installazione\n    const metriInstallati = cavo.metri_posati || cavo.metratura_reale || 0\n    const statoInstallazione = cavo.stato_installazione || 'Da installare'\n    const isInstalled = metriInstallati > 0 || statoInstallazione === 'Installato' || statoInstallazione === 'In corso'\n    const isCertified = cavo.certificato === true || cavo.certificato === 'SI' || cavo.certificato === 'CERTIFICATO'\n    const isRejected = cavo.certificato === false || cavo.certificato === 'NO' || cavo.certificato === 'RIFIUTATO'\n\n    // \"Non disponibile\" - Design distinto per elementi non interattivi\n    if (!isInstalled) {\n      return (\n        <TooltipProvider>\n          <Tooltip>\n            <TooltipTrigger asChild>\n              <span className=\"text-gray-400 text-sm px-2 py-1 flex items-center gap-1\">\n                <Info className=\"h-3 w-3\" />\n                Non disponibile\n              </span>\n            </TooltipTrigger>\n            <TooltipContent>\n              <p>La certificazione non può essere gestita perché il cavo non è installato</p>\n            </TooltipContent>\n          </Tooltip>\n        </TooltipProvider>\n      )\n    }\n\n    // Certificato - Badge di stato + pulsante download\n    if (isCertified) {\n      return (\n        <div className=\"flex items-center gap-2\">\n          <Badge className=\"bg-green-100 text-green-700 px-2 py-1 text-xs font-medium border border-green-200\">\n            <CheckCircle className=\"h-3 w-3 mr-1\" />\n            Certificato\n          </Badge>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            className=\"h-6 w-6 p-0 border-blue-300 text-blue-700 hover:bg-blue-50 hover:border-blue-400\"\n            onClick={(e) => {\n              e.stopPropagation()\n              openDialog('generatePDF', cavo)\n            }}\n          >\n            <Download className=\"h-3 w-3\" />\n          </Button>\n        </div>\n      )\n    }\n\n    // Non Certificato/Rifiutato - Badge di stato (non cliccabile)\n    if (isRejected) {\n      return (\n        <Badge className=\"bg-red-100 text-red-700 px-2 py-1 text-xs font-medium border border-red-200\">\n          <X className=\"h-3 w-3 mr-1\" />\n          Non Certificato\n        </Badge>\n      )\n    }\n\n    // Azione \"Certifica\" - Pulsante chiaro\n    return (\n      <Button\n        variant=\"outline\"\n        size=\"sm\"\n        className=\"h-7 px-2 text-xs font-medium border-purple-300 text-purple-700 hover:bg-purple-50 hover:border-purple-400 transition-colors duration-200\"\n        onClick={(e) => {\n          e.stopPropagation()\n          handleCertifyCable(cavo)\n        }}\n      >\n        <CheckCircle className=\"h-3 w-3 mr-1\" />\n        Certifica\n      </Button>\n    )\n  }\n\n  return (\n    <div className=\"relative\">\n      {/* Smart Filter */}\n      <SmartCaviFilter\n        cavi={cavi}\n        onFilteredDataChange={handleSmartFilterChange}\n        loading={loading}\n        selectionEnabled={internalSelectionEnabled}\n        onSelectionToggle={handleSelectionToggle}\n      />\n\n      {/* Controlli Tabella e Paginazione */}\n      <div className=\"mb-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 bg-slate-50 p-4 rounded-lg border border-slate-200\">\n        {/* Info e controlli selezione */}\n        <div className=\"flex items-center gap-4\">\n          <div className=\"text-sm text-slate-600\">\n            Mostrando <span className=\"font-semibold\">{paginatedData.startIndex}</span> - <span className=\"font-semibold\">{paginatedData.endIndex}</span> di <span className=\"font-semibold\">{paginatedData.totalItems}</span> cavi\n          </div>\n\n          {internalSelectionEnabled && (\n            <div className=\"flex items-center gap-2\">\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger asChild>\n                    <div\n                      onClick={allVisibleSelected ? deselectAllVisible : selectAllVisible}\n                      className=\"flex items-center gap-2 px-3 py-2 text-sm font-medium border border-slate-200 rounded-md hover:bg-slate-50 hover:border-slate-300 transition-all duration-200 cursor-pointer\"\n                    >\n                      <Checkbox\n                        checked={allVisibleSelected}\n                        ref={(el) => {\n                          if (el) el.indeterminate = someVisibleSelected && !allVisibleSelected\n                        }}\n                      />\n                      Pagina\n                    </div>\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>{allVisibleSelected ? 'Deseleziona tutti i cavi visibili' : 'Seleziona tutti i cavi visibili'}</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger asChild>\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={selectedCavi.length === sortedCavi.length ? deselectAll : selectAll}\n                      className=\"flex items-center gap-2\"\n                    >\n                      <Users className=\"w-4 h-4\" />\n                      Tutti ({sortedCavi.length})\n                    </Button>\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>{selectedCavi.length === sortedCavi.length ? 'Deseleziona tutti i cavi' : 'Seleziona tutti i cavi filtrati'}</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n\n              {selectedCavi.length > 0 && (\n                <Badge variant=\"secondary\" className=\"bg-blue-100 text-blue-800\">\n                  {selectedCavi.length} selezionati\n                </Badge>\n              )}\n            </div>\n          )}\n        </div>\n\n        {/* Controlli per pagina */}\n        <div className=\"flex items-center gap-2\">\n          <span className=\"text-sm text-slate-600\">Righe per pagina:</span>\n          <select\n            value={itemsPerPage}\n            onChange={(e) => {\n              setItemsPerPage(Number(e.target.value))\n              setCurrentPage(1)\n            }}\n            className=\"border border-slate-300 rounded px-2 py-1 text-sm\"\n          >\n            <option value={10}>10</option>\n            <option value={25}>25</option>\n            <option value={50}>50</option>\n            <option value={100}>100</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Filterable Table */}\n      <FilterableTable\n        data={smartFilteredCavi}\n        columns={columns}\n        loading={loading}\n        emptyMessage=\"Nessun cavo disponibile\"\n        onFilteredDataChange={handleTableFilterChange}\n        renderRow={renderRow}\n      />\n\n      {/* Controlli Paginazione */}\n      {paginatedData.totalPages > 1 && (\n        <div className=\"mt-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 bg-white p-4 rounded-lg border border-slate-200\">\n          <div className=\"text-sm text-slate-600\">\n            Pagina <span className=\"font-semibold\">{currentPage}</span> di <span className=\"font-semibold\">{paginatedData.totalPages}</span>\n          </div>\n\n          <div className=\"flex items-center gap-2\">\n            <TooltipProvider>\n              <Tooltip>\n                <TooltipTrigger asChild>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={goToFirstPage}\n                    disabled={!paginatedData.hasPrevPage}\n                    className=\"p-2\"\n                  >\n                    <ChevronsLeft className=\"w-4 h-4\" />\n                  </Button>\n                </TooltipTrigger>\n                <TooltipContent>\n                  <p>Prima pagina</p>\n                </TooltipContent>\n              </Tooltip>\n            </TooltipProvider>\n\n            <TooltipProvider>\n              <Tooltip>\n                <TooltipTrigger asChild>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={goToPrevPage}\n                    disabled={!paginatedData.hasPrevPage}\n                    className=\"p-2\"\n                  >\n                    <ChevronLeft className=\"w-4 h-4\" />\n                  </Button>\n                </TooltipTrigger>\n                <TooltipContent>\n                  <p>Pagina precedente</p>\n                </TooltipContent>\n              </Tooltip>\n            </TooltipProvider>\n\n            {/* Numeri di pagina */}\n            <div className=\"flex items-center gap-1\">\n              {Array.from({ length: Math.min(5, paginatedData.totalPages) }, (_, i) => {\n                let pageNum: number\n                if (paginatedData.totalPages <= 5) {\n                  pageNum = i + 1\n                } else if (currentPage <= 3) {\n                  pageNum = i + 1\n                } else if (currentPage >= paginatedData.totalPages - 2) {\n                  pageNum = paginatedData.totalPages - 4 + i\n                } else {\n                  pageNum = currentPage - 2 + i\n                }\n\n                return (\n                  <Button\n                    key={pageNum}\n                    variant={currentPage === pageNum ? \"default\" : \"outline\"}\n                    size=\"sm\"\n                    onClick={() => goToPage(pageNum)}\n                    className=\"w-8 h-8 p-0\"\n                  >\n                    {pageNum}\n                  </Button>\n                )\n              })}\n            </div>\n\n            <TooltipProvider>\n              <Tooltip>\n                <TooltipTrigger asChild>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={goToNextPage}\n                    disabled={!paginatedData.hasNextPage}\n                    className=\"p-2\"\n                  >\n                    <ChevronRight className=\"w-4 h-4\" />\n                  </Button>\n                </TooltipTrigger>\n                <TooltipContent>\n                  <p>Pagina successiva</p>\n                </TooltipContent>\n              </Tooltip>\n            </TooltipProvider>\n\n            <TooltipProvider>\n              <Tooltip>\n                <TooltipTrigger asChild>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={goToLastPage}\n                    disabled={!paginatedData.hasNextPage}\n                    className=\"p-2\"\n                  >\n                    <ChevronsRight className=\"w-4 h-4\" />\n                  </Button>\n                </TooltipTrigger>\n                <TooltipContent>\n                  <p>Ultima pagina</p>\n                </TooltipContent>\n              </Tooltip>\n            </TooltipProvider>\n          </div>\n        </div>\n      )}\n\n      {/* Barra Azioni Bulk - appare solo quando ci sono elementi selezionati */}\n      {internalSelectionEnabled && selectedCavi.length > 0 && (\n        <div className=\"fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-white border border-slate-300 rounded-lg shadow-xl z-50 p-4 min-w-[600px]\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"flex items-center gap-2\">\n                <div className=\"w-3 h-3 bg-blue-500 rounded-full animate-pulse\"></div>\n                <Badge variant=\"secondary\" className=\"bg-blue-100 text-blue-800 font-semibold\">\n                  {selectedCavi.length} cavi selezionati\n                </Badge>\n              </div>\n\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger asChild>\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={deselectAll}\n                      className=\"text-slate-600 hover:text-slate-800\"\n                    >\n                      <X className=\"w-4 h-4 mr-1\" />\n                      Deseleziona tutto\n                    </Button>\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>Rimuovi la selezione da tutti i cavi</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n            </div>\n\n            <div className=\"flex items-center gap-2\">\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger asChild>\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => {\n                        // Logica per esportazione bulk\n                        toast.actionInProgress('Esportazione', `${selectedCavi.length} cavi`)\n                      }}\n                      className=\"flex items-center gap-2 hover:bg-green-50 hover:border-green-300\"\n                    >\n                      <Download className=\"w-4 h-4\" />\n                      Esporta\n                    </Button>\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>Esporta i cavi selezionati in Excel</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger asChild>\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => {\n                        // Logica per generazione PDF bulk\n                        toast.actionInProgress('Generazione PDF', `${selectedCavi.length} cavi`)\n                      }}\n                      className=\"flex items-center gap-2 hover:bg-blue-50 hover:border-blue-300\"\n                    >\n                      <FileText className=\"w-4 h-4\" />\n                      PDF Bulk\n                    </Button>\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>Genera PDF per tutti i cavi selezionati</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger asChild>\n                    <Button\n                      size=\"sm\"\n                      onClick={() => {\n                        // Logica per cambio stato bulk\n                        toast.actionInProgress('Aggiornamento Stato', `${selectedCavi.length} cavi`)\n                      }}\n                      className=\"flex items-center gap-2 hover:bg-yellow-50 hover:border-yellow-300\"\n                    >\n                      <Settings className=\"w-4 h-4\" />\n                      Stato\n                    </Button>\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>Cambia stato per tutti i cavi selezionati</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger asChild>\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => {\n                        // Logica per assegnazione comanda bulk\n                        toast.actionInProgress('Assegnazione Comanda', `${selectedCavi.length} cavi`)\n                      }}\n                      className=\"flex items-center gap-2 hover:bg-purple-50 hover:border-purple-300\"\n                    >\n                      <Package className=\"w-4 h-4\" />\n                      Comanda\n                    </Button>\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>Assegna comanda a tutti i cavi selezionati</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n\n              <div className=\"w-px h-6 bg-slate-300\"></div>\n\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger asChild>\n                    <Button\n                      variant=\"destructive\"\n                      size=\"sm\"\n                      onClick={() => {\n                        // Logica per eliminazione bulk con conferma\n                        if (confirm(`Sei sicuro di voler eliminare ${selectedCavi.length} cavi selezionati?`)) {\n                          toast.actionInProgress('Eliminazione', `${selectedCavi.length} cavi`)\n                        }\n                      }}\n                      className=\"flex items-center gap-2\"\n                    >\n                      <Trash2 className=\"w-4 h-4\" />\n                      Elimina\n                    </Button>\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>Elimina tutti i cavi selezionati (azione irreversibile)</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Dialog Components */}\n      <DisconnectCableDialog\n        isOpen={dialogs.disconnect.isOpen}\n        onClose={() => closeDialog('disconnect')}\n        onConfirm={handleDisconnectCable}\n        cavo={dialogs.disconnect.cavo}\n      />\n\n      <GeneratePDFDialog\n        isOpen={dialogs.generatePDF.isOpen}\n        onClose={() => closeDialog('generatePDF')}\n        onGenerate={handleGeneratePDF}\n        cavo={dialogs.generatePDF.cavo}\n      />\n\n      <CertificationErrorDialog\n        isOpen={dialogs.certificationError.isOpen}\n        onClose={() => closeDialog('certificationError')}\n        cavo={dialogs.certificationError.cavo}\n        missingRequirements={dialogs.certificationError.missingRequirements}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA+BA;AAMA;AACA;AApDA;;;;;;;;;;;;;;;AAiFe,SAAS,UAAU,EAChC,OAAO,EAAE,EACT,UAAU,KAAK,EACf,mBAAmB,KAAK,EACxB,eAAe,EAAE,EACjB,iBAAiB,EACjB,cAAc,EACd,mBAAmB,EACnB,iBAAiB,EACjB,aAAa,EACb,cAAc,EACC;IACf,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;IACtD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QAClD,YAAY;YAAE,QAAQ;YAAO,MAAM;QAAK;QACxC,aAAa;YAAE,QAAQ;YAAO,MAAM;QAAK;QACzC,oBAAoB;YAAE,QAAQ;YAAO,MAAM;YAAM,qBAAqB,EAAE;QAAC;IAC3E;IAEA,2BAA2B;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,QAAQ,CAAA,GAAA,iJAAA,CAAA,kBAAe,AAAD;IAE5B,gCAAgC;IAChC,MAAM,aAAa,CAAC,MAAyB,MAAY,sBAAgC,EAAE;QACzF,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;oBAAE,QAAQ;oBAAM;oBAAM;gBAAoB;YACpD,CAAC;IACH;IAEA,MAAM,cAAc,CAAC;QACnB,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;oBAAE,QAAQ;oBAAO,MAAM;oBAAM,qBAAqB,EAAE;gBAAC;YAC/D,CAAC;IACH;IAEA,2BAA2B;IAC3B,MAAM,aAAa,CAAC;QAClB,IAAI,cAAc,OAAO;YACvB,iBAAiB,CAAA,OAAQ,SAAS,QAAQ,SAAS;QACrD,OAAO;YACL,aAAa;YACb,iBAAiB;QACnB;IACF;IAEA,uBAAuB;IACvB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACzB,MAAM,SAAS;eAAI;SAAkB,CAAC,IAAI,CAAC,CAAC,GAAG;YAC7C,IAAI,SAAc,CAAC,CAAC,UAAU;YAC9B,IAAI,SAAc,CAAC,CAAC,UAAU;YAE9B,2BAA2B;YAC3B,IAAI,cAAc,mBAAmB,cAAc,gBAAgB;gBACjE,SAAS,WAAW,WAAW;gBAC/B,SAAS,WAAW,WAAW;YACjC;YAEA,oBAAoB;YACpB,IAAI,OAAO,WAAW,UAAU;gBAC9B,SAAS,OAAO,WAAW;YAC7B;YACA,IAAI,OAAO,WAAW,UAAU;gBAC9B,SAAS,OAAO,WAAW;YAC7B;YAEA,IAAI,SAAS,QAAQ,OAAO,kBAAkB,QAAQ,CAAC,IAAI;YAC3D,IAAI,SAAS,QAAQ,OAAO,kBAAkB,QAAQ,IAAI,CAAC;YAC3D,OAAO;QACT;QACA,OAAO;IACT,GAAG;QAAC;QAAmB;QAAW;KAAc;IAEhD,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qBAAqB;IACvB,GAAG;QAAC;KAAK;IAET,sBAAsB;IACtB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,MAAM,aAAa,CAAC,cAAc,CAAC,IAAI;QACvC,MAAM,WAAW,aAAa;QAC9B,MAAM,gBAAgB,WAAW,KAAK,CAAC,YAAY;QAEnD,MAAM,aAAa,KAAK,IAAI,CAAC,WAAW,MAAM,GAAG;QACjD,MAAM,cAAc,cAAc;QAClC,MAAM,cAAc,cAAc;QAElC,OAAO;YACL,MAAM;YACN,YAAY,WAAW,MAAM;YAC7B;YACA;YACA;YACA,YAAY,aAAa;YACzB,UAAU,KAAK,GAAG,CAAC,UAAU,WAAW,MAAM;QAChD;IACF,GAAG;QAAC;QAAY;QAAa;KAAa;IAE1C,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gBAAgB,cAAc,IAAI;IACpC,GAAG;QAAC,cAAc,IAAI;KAAC;IAEvB,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe;IACjB,GAAG;QAAC,kBAAkB,MAAM;KAAC;IAE7B,+BAA+B;IAC/B,MAAM,0BAA0B,CAAC;QAC/B,qBAAqB;IACvB;IAEA,0BAA0B;IAC1B,MAAM,0BAA0B,CAAC;QAC/B,gBAAgB;IAClB;IAEA,2BAA2B;IAC3B,MAAM,wBAAwB,OAAO;QACnC,IAAI;YACF,IAAI,mBAAmB;gBACrB,MAAM,kBAAkB;gBACxB,MAAM,gBAAgB,CAAC;YACzB;QACF,EAAE,OAAO,OAAO;YACd,MAAM,KAAK,CAAC,wBAAwB;QACtC;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,IAAI,eAAe;gBACjB,MAAM,cAAc;gBACpB,MAAM,YAAY,CAAC,OAAO,QAAQ,EAAE,OAAO,MAAM;YACnD;QACF,EAAE,OAAO,OAAO;YACd,MAAM,KAAK,CAAC,0BAA0B;QACxC;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,wCAAwC;QACxC,MAAM,sBAAgC,EAAE;QAExC,IAAI,CAAC,KAAK,YAAY,IAAI,WAAW,KAAK,YAAY,MAAM,GAAG;YAC7D,oBAAoB,IAAI,CAAC;QAC3B;QAEA,IAAI,KAAK,KAAK,KAAK,aAAa;YAC9B,oBAAoB,IAAI,CAAC;QAC3B;QAEA,IAAI,CAAC,KAAK,kBAAkB,EAAE;YAC5B,oBAAoB,IAAI,CAAC;QAC3B;QAEA,IAAI,oBAAoB,MAAM,GAAG,GAAG;YAClC,WAAW,sBAAsB,MAAM;YACvC;QACF;QAEA,IAAI;YACF,IAAI,gBAAgB;gBAClB,MAAM,gBAAgB,CAAC,kBAAkB,KAAK,OAAO;gBACrD,MAAM,eAAe,KAAK,OAAO;gBACjC,MAAM,OAAO,CAAC,oBAAoB,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC,kCAAkC,CAAC;YAC/F;QACF,EAAE,OAAO,OAAO;YACd,MAAM,kBAAkB,CAAC,KAAK,OAAO,EAAE;QACzC;IACF;IAEA,MAAM,wBAAwB;QAC5B,4BAA4B,CAAC;QAC7B,mBAAmB,CAAC,4BAA4B,aAAa,MAAM,GAAG;IACxE;IAEA,8BAA8B;IAC9B,MAAM,WAAW,CAAC;QAChB,eAAe,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,cAAc,UAAU;IACpE;IAEA,MAAM,gBAAgB,IAAM,SAAS;IACrC,MAAM,eAAe,IAAM,SAAS,cAAc,UAAU;IAC5D,MAAM,eAAe,IAAM,SAAS,cAAc;IAClD,MAAM,eAAe,IAAM,SAAS,cAAc;IAElD,kCAAkC;IAClC,MAAM,mBAAmB;QACvB,MAAM,aAAa,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO;QACxD,MAAM,eAAe;eAAI,IAAI,IAAI;mBAAI;mBAAiB;aAAW;SAAE;QACnE,oBAAoB;IACtB;IAEA,MAAM,qBAAqB;QACzB,MAAM,aAAa,IAAI,IAAI,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO;QAChE,MAAM,eAAe,aAAa,MAAM,CAAC,CAAA,KAAM,CAAC,WAAW,GAAG,CAAC;QAC/D,oBAAoB;IACtB;IAEA,MAAM,YAAY;QAChB,MAAM,SAAS,WAAW,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO;QAC5C,oBAAoB;IACtB;IAEA,MAAM,cAAc;QAClB,oBAAoB,EAAE;IACxB;IAEA,qDAAqD;IACrD,MAAM,qBAAqB,cAAc,IAAI,CAAC,MAAM,GAAG,KACrD,cAAc,IAAI,CAAC,KAAK,CAAC,CAAA,OAAQ,aAAa,QAAQ,CAAC,KAAK,OAAO;IAErE,MAAM,sBAAsB,cAAc,IAAI,CAAC,IAAI,CAAC,CAAA,OAAQ,aAAa,QAAQ,CAAC,KAAK,OAAO;IAE9F,mCAAmC;IACnC,MAAM,iBAAiB,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,EAIxD,iBACC,8OAAC,mIAAA,CAAA,kBAAe;sBACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;kCACN,8OAAC,mIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,8OAAC;4BACC,SAAS,IAAM,WAAW;4BAC1B,WAAW,CAAC,oFAAoF,EAAE,WAAW;;gCAE5G;gCACA,cAAc,QACb,kBAAkB,sBAChB,8OAAC,4MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAEnB,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;yDAGvB,8OAAC,wNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAI7B,8OAAC,mIAAA,CAAA,iBAAc;kCACb,cAAA,8OAAC;;gCAAE;gCAAyB;;;;;;;;;;;;;;;;;;;;;;;IAMpC,6CAA6C;IAC7C,MAAM,eAAe,CAAC,EACpB,OAAO,EACP,MAAM,IAAI,EACV,OAAO,EACP,UAAU,SAAS,EACnB,WAAW,KAAK,EAChB,YAAY,EAAE,EAQf;QACC,MAAM,iBAAiB;YACrB,SAAS;YACT,QAAQ;YACR,SAAS;YACT,SAAS;QACX;QAEA,qBACE,8OAAC,mIAAA,CAAA,kBAAe;sBACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;kCACN,8OAAC,mIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAS;4BACT,UAAU;4BACV,WAAW,CAAC,YAAY,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU,qHAAqH,CAAC;sCAErL,cAAA,8OAAC;gCAAK,WAAU;;;;;;;;;;;;;;;;kCAGpB,8OAAC,mIAAA,CAAA,iBAAc;kCACb,cAAA,8OAAC;sCAAG;;;;;;;;;;;;;;;;;;;;;;IAKd;IAEA,qBAAqB;IACrB,MAAM,kBAAkB,CAAC;QACvB,IAAI,mBAAmB;YACrB,kBAAkB,UAAU,aAAa,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,IAAI,EAAE;QACnE;IACF;IAEA,MAAM,mBAAmB,CAAC,QAAgB;QACxC,IAAI,mBAAmB;YACrB,MAAM,eAAe,UACjB;mBAAI;gBAAc;aAAO,GACzB,aAAa,MAAM,CAAC,CAAA,KAAM,OAAO;YACrC,kBAAkB;QACpB;IACF;IAEA,uBAAuB;IACvB,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,aAAa;oBACb,YAAY,EAAE,yBAAyB;gBACzC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;gBACvC,MAAM,IAAI,SAAS,aAAa,CAAC;gBACjC,EAAE,IAAI,GAAG;gBACT,EAAE,QAAQ,GAAG,CAAC,YAAY,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;gBACxE,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,EAAE,KAAK;gBACP,OAAO,GAAG,CAAC,eAAe,CAAC;gBAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,CAAC,+BAA+B,EAAE,MAAM,KAAK,EAAE;YACvD;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,yBAAyB;QAC7B,MAAM,YAAY,OAAO;QACzB,IAAI,CAAC,WAAW;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,aAAa;oBACb,YAAY;oBACZ;gBACF;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,OAAO,OAAO;YACpB,qBAAqB;YACvB,OAAO;gBACL,MAAM,CAAC,QAAQ,EAAE,OAAO,KAAK,EAAE;YACjC;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,0BAA0B;QAC9B,iDAAiD;QACjD,MAAM,CAAC,yBAAyB,EAAE,aAAa,MAAM,CAAC,KAAK,CAAC;IAC9D;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,QAAQ,CAAC,8BAA8B,EAAE,aAAa,MAAM,CAAC,MAAM,CAAC,GAAG;YAC1E;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,aAAa;oBACb,YAAY,EAAE,yBAAyB;gBACzC;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,OAAO,OAAO;YACpB,qBAAqB;YACvB,OAAO;gBACL,MAAM,CAAC,QAAQ,EAAE,OAAO,KAAK,EAAE;YACjC;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,oDAAoD;IACpD,MAAM,UAAuB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACnC,MAAM,cAA2B;YAC/B;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,cAAc,kBACZ,8OAAC;wBAAe,OAAM;kCAAU;;;;;;gBAElC,YAAY,CAAC,oBACX,8OAAC;wBAAK,WAAU;kCAAkC,IAAI,OAAO;;;;;;YAEjE;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,cAAc,kBACZ,8OAAC;wBAAe,OAAM;kCAAU;;;;;;gBAElC,YAAY,CAAC,oBACX,8OAAC,6IAAA,CAAA,UAAa;wBAAC,MAAM,IAAI,OAAO,IAAI;wBAAI,WAAW;;;;;;YAEvD;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,YAAY,CAAC,oBACX,8OAAC,6IAAA,CAAA,UAAa;wBAAC,MAAM,IAAI,OAAO,IAAI;wBAAI,WAAW;;;;;;YAEvD;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,YAAY,CAAC,oBACX,8OAAC,6IAAA,CAAA,UAAa;wBAAC,MAAM,IAAI,SAAS,IAAI;wBAAI,WAAW;;;;;;YAEzD;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,YAAY,CAAC,MAAc,IAAI,UAAU,IAAI,IAAI,OAAO;YAC1D;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,cAAc,kBACZ,8OAAC;wBAAe,OAAM;kCAAgB;;;;;;gBAExC,YAAY,CAAC,MAAc,IAAI,aAAa,GAAG,IAAI,aAAa,CAAC,OAAO,CAAC,KAAK;YAChF;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,cAAc,kBACZ,8OAAC;wBAAe,OAAM;kCAAe;;;;;;gBAEvC,YAAY,CAAC;oBACX,MAAM,QAAQ,IAAI,YAAY,IAAI,IAAI,eAAe,IAAI;oBACzD,OAAO,QAAQ,MAAM,OAAO,CAAC,KAAK;gBACpC;YACF;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,YAAY,CAAC,oBACX,8OAAC,6IAAA,CAAA,UAAa;wBACZ,MAAM,IAAI,EAAE,IAAI,IAAI,mBAAmB,IAAI;wBAC3C,WAAW;;;;;;YAGjB;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,YAAY,CAAC,oBACX,8OAAC,6IAAA,CAAA,UAAa;wBACZ,MAAM,IAAI,CAAC,IAAI,IAAI,iBAAiB,IAAI;wBACxC,WAAW;;;;;;YAGjB;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,YAAY,CAAC,MAAc,gBAAgB;YAC7C;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,cAAc,kBACZ,8OAAC;wBAAe,OAAM;kCAAQ;;;;;;gBAEhC,YAAY,CAAC,MAAc,gBAAgB;YAC7C;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,aAAa;gBACb,YAAY,CAAC,MAAc,oBAAoB;YACjD;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,aAAa;gBACb,YAAY,CAAC,MAAc,uBAAuB;YACpD;SAED;QAED,kCAAkC;QAClC,IAAI,0BAA0B;YAC5B,YAAY,OAAO,CAAC;gBAClB,OAAO;gBACP,YAAY;gBACZ,eAAe;gBACf,aAAa;gBACb,OAAO;gBACP,OAAO;gBACP,cAAc,kBACZ,8OAAC,oIAAA,CAAA,WAAQ;wBACP,SAAS,aAAa,MAAM,KAAK,aAAa,MAAM,IAAI,aAAa,MAAM,GAAG;wBAC9E,iBAAiB;;;;;;gBAGrB,YAAY,CAAC,oBACX,8OAAC,oIAAA,CAAA,WAAQ;wBACP,SAAS,aAAa,QAAQ,CAAC,IAAI,OAAO;wBAC1C,iBAAiB,CAAC,UAAY,iBAAiB,IAAI,OAAO,EAAE;wBAC5D,SAAS,CAAC,IAAM,EAAE,eAAe;;;;;;YAGvC;QACF;QAEA,OAAO;IACT,GAAG;QAAC;QAA0B;QAAc;QAAc;QAAiB;KAAiB;IAE5F,qDAAqD;IACrD,MAAM,YAAY,CAAC,KAAW;QAC5B,MAAM,aAAa,aAAa,QAAQ,CAAC,IAAI,OAAO;QACpD,MAAM,uBAAuB,aAAa,MAAM,GAAG;QAEnD,MAAM,oBAAoB,CAAC;YACzB,EAAE,cAAc;YAChB,wEAAwE;YACxE,oDAAoD;YACpD,QAAQ,GAAG,CAAC,0BAA0B,IAAI,OAAO;QACnD;QAEA,qBACE,8OAAC,iIAAA,CAAA,WAAQ;YAEP,WAAW,CAAC;UACV,EAAE,aAAa,+BAA+B,WAAW;;;;UAIzD,EAAE,aAAa,yBAAyB,GAAG;QAC7C,CAAC;YACD,SAAS,IAAM,4BAA4B,iBAAiB,IAAI,OAAO,EAAE,CAAC;YAC1E,eAAe;sBAEd,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,iIAAA,CAAA,YAAS;oBAER,WAAW,CAAC;;cAEV,EAAE,aAAa,kBAAkB,gBAAgB;;YAEnD,CAAC;oBACD,OAAO;wBAAE,OAAO,OAAO,KAAK;wBAAE,GAAG,OAAO,SAAS;oBAAC;oBAClD,SAAS,CAAC;wBACR,uCAAuC;wBACvC,IAAI;4BAAC;4BAAuB;4BAAgB;yBAAc,CAAC,QAAQ,CAAC,OAAO,KAAK,GAAG;4BACjF,EAAE,eAAe;wBACnB;oBACF;8BAEC,OAAO,UAAU,GAAG,OAAO,UAAU,CAAC,OAAQ,GAAG,CAAC,OAAO,KAAK,CAAC,kBAAI,8OAAC;wBAAK,WAAU;kCAAgB;;;;;;mBAd/F,OAAO,KAAK;;;;;WAbhB,IAAI,OAAO;;;;;IAgCtB;IAEA,iEAAiE;IACjE,MAAM,kBAAkB,CAAC;QACvB,MAAM,WAAW,KAAK,SAAS;QAE/B,IAAI,CAAC,YAAY,aAAa,OAAO;YACnC,qBAAO,8OAAC;gBAAK,WAAU;0BAAgB;;;;;;QACzC;QAEA,IAAI,aAAa,gBAAgB;YAC/B,qBACE,8OAAC,iIAAA,CAAA,QAAK;gBACJ,SAAQ;gBACR,WAAU;0BACX;;;;;;QAIL;QAEA,2DAA2D;QAC3D,IAAI,eAAe;QACnB,IAAI,QAAQ,SAAS,KAAK,CAAC;QAC3B,IAAI,OAAO;YACT,eAAe,KAAK,CAAC,EAAE;QACzB,OAAO;YACL,QAAQ,SAAS,KAAK,CAAC;YACvB,IAAI,OAAO;gBACT,eAAe,KAAK,CAAC,EAAE;YACzB,OAAO;gBACL,QAAQ,SAAS,KAAK,CAAC;gBACvB,IAAI,OAAO;oBACT,eAAe,KAAK,CAAC,EAAE;gBACzB,OAAO;oBACL,QAAQ,SAAS,KAAK,CAAC;oBACvB,IAAI,OAAO;wBACT,eAAe,KAAK,CAAC,EAAE;oBACzB;gBACF;YACF;QACF;QAEA,qBACE,8OAAC,mIAAA,CAAA,kBAAe;sBACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;kCACN,8OAAC,mIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,8OAAC,iIAAA,CAAA,QAAK;4BACJ,SAAQ;4BACR,WAAU;4BACV,SAAS,CAAC;gCACR,EAAE,eAAe;gCACjB,iDAAiD;gCACjD,MAAM,kBAAkB,KAAK,YAAY,IAAI,KAAK,eAAe,IAAI;gCACrE,MAAM,cAAc,kBAAkB;gCAEtC,IAAI,aAAa;oCACf,oDAAoD;oCACpD,iBAAiB,MAAM;gCACzB,OAAO;oCACL,0DAA0D;oCAC1D,iBAAiB,MAAM;gCACzB;4BACF;;8CAEA,8OAAC;8CAAM;;;;;;8CACP,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG3B,8OAAC,mIAAA,CAAA,iBAAc;kCACb,cAAA,8OAAC;sCACE,CAAC;gCACA,MAAM,kBAAkB,KAAK,YAAY,IAAI,KAAK,eAAe,IAAI;gCACrE,MAAM,cAAc,kBAAkB;gCACtC,OAAO,cACH,iCACA;4BACN,CAAC;;;;;;;;;;;;;;;;;;;;;;IAMb;IAEA,mCAAmC;IACnC,MAAM,iBAAiB,CAAC;QACtB,gDAAgD;QAChD,MAAM,cAAc,KAAK,YAAY;QACrC,MAAM,kBAAkB,KAAK,gBAAgB;QAC7C,MAAM,gBAAgB,KAAK,cAAc;QACzC,MAAM,wBAAwB,KAAK,sBAAsB;QAEzD,gFAAgF;QAChF,MAAM,gBAAgB,eAAe,mBAAmB,iBAAiB;QAEzE,8EAA8E;QAC9E,IAAI,iBAAiB,KAAK,mBAAmB,KAAK,YAAY;YAC5D,MAAM,eAAe,CAAA,GAAA,0HAAA,CAAA,sBAAmB,AAAD,EAAE;YACzC,qBACE,8OAAC,iIAAA,CAAA,QAAK;gBACJ,WAAW,CAAC,eAAe,EAAE,aAAa,KAAK,CAAC,CAAC,EAAE,aAAa,KAAK,EAAE;gBACvE,SAAS,IAAM,iBAAiB,MAAM,gBAAgB;0BAErD;;;;;;QAGP;QAEA,qCAAqC;QACrC,MAAM,QAAQ,KAAK,mBAAmB,IAAI;QAC1C,MAAM,eAAe,CAAA,GAAA,0HAAA,CAAA,sBAAmB,AAAD,EAAE;QAEzC,qBACE,8OAAC,iIAAA,CAAA,QAAK;YAAC,WAAW,aAAa,KAAK;sBACjC;;;;;;IAGP;IAEA,MAAM,kBAAkB,CAAC;QACvB,+EAA+E;QAC/E,MAAM,kBAAkB,KAAK,YAAY,IAAI,KAAK,eAAe,IAAI;QACrE,MAAM,cAAc,kBAAkB;QAEtC,uDAAuD;QACvD,MAAM,cAAc,KAAK,YAAY;QACrC,MAAM,kBAAkB,KAAK,gBAAgB;QAC7C,MAAM,gBAAgB,KAAK,cAAc;QACzC,MAAM,wBAAwB,KAAK,sBAAsB;QACzD,MAAM,gBAAgB,eAAe,mBAAmB,iBAAiB;QAEzE,yGAAyG;QACzG,IAAI,iBAAiB,KAAK,mBAAmB,KAAK,YAAY;YAC5D,qBACE,8OAAC,mIAAA,CAAA,kBAAe;0BACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;sCACN,8OAAC,mIAAA,CAAA,iBAAc;4BAAC,OAAO;sCACrB,cAAA,8OAAC,iIAAA,CAAA,QAAK;gCACJ,WAAU;gCACV,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB,iBAAiB,MAAM,gBAAgB;gCACzC;0CAEC;;;;;;;;;;;sCAGL,8OAAC,mIAAA,CAAA,iBAAc;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;QAKb;QAEA,4EAA4E;QAC5E,MAAM,QAAQ,KAAK,mBAAmB,IAAI;QAE1C,IAAI,UAAU,gBAAgB,aAAa;YACzC,qBACE,8OAAC,iIAAA,CAAA,QAAK;gBAAC,WAAU;0BAA4E;;;;;;QAIjG,OAAO,IAAI,UAAU,YAAY;YAC/B,qBACE,8OAAC,iIAAA,CAAA,QAAK;gBAAC,WAAU;0BAA+E;;;;;;QAIpG,OAAO;YACL,qBACE,8OAAC,mIAAA,CAAA,kBAAe;0BACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;sCACN,8OAAC,mIAAA,CAAA,iBAAc;4BAAC,OAAO;sCACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB,iBAAiB,MAAM;gCACzB;;kDAEA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;sCAIrC,8OAAC,mIAAA,CAAA,iBAAc;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;QAKb;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,+DAA+D;QAC/D,MAAM,kBAAkB,KAAK,YAAY,IAAI,KAAK,eAAe,IAAI;QACrE,MAAM,qBAAqB,KAAK,mBAAmB,IAAI;QACvD,MAAM,cAAc,kBAAkB,KAAK,uBAAuB,gBAAgB,uBAAuB;QACzG,MAAM,eAAe,KAAK,YAAY,IAAI,KAAK,YAAY,IAAI;QAE/D,mEAAmE;QACnE,IAAI,CAAC,aAAa;YAChB,qBACE,8OAAC,mIAAA,CAAA,kBAAe;0BACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;sCACN,8OAAC,mIAAA,CAAA,iBAAc;4BAAC,OAAO;sCACrB,cAAA,8OAAC;gCAAK,WAAU;;kDACd,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;sCAIhC,8OAAC,mIAAA,CAAA,iBAAc;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;QAKb;QAEA,6DAA6D;QAC7D,IAAI,OAAO,MAAM,YAAY;QAE7B,OAAQ;YACN,KAAK;gBACH,QAAQ;gBACR,OAAO,kMAAA,CAAA,OAAI;gBACX,aAAa;gBACb,cAAc;gBACd;YACF,KAAK;gBACH,QAAQ;gBACR,OAAO,kMAAA,CAAA,OAAI;gBACX,aAAa;gBACb,cAAc;gBACd;YACF,KAAK;gBACH,QAAQ;gBACR,OAAO,kMAAA,CAAA,OAAI;gBACX,aAAa;gBACb,cAAc;gBACd;YACF,KAAK;gBACH,QAAQ;gBACR,OAAO,sMAAA,CAAA,SAAM;gBACb,aAAa;gBACb,cAAc;gBACd;YACF;gBACE,QAAQ;gBACR,OAAO,0MAAA,CAAA,WAAQ;gBACf,aAAa;gBACb,cAAc;gBACd;QACJ;QAEA,MAAM,gBAAgB;QAEtB,qBACE,8OAAC,kIAAA,CAAA,SAAM;YACL,SAAQ;YACR,MAAK;YACL,WAAW,CAAC,4DAA4D,EAAE,aAAa;YACvF,SAAS,CAAC;gBACR,EAAE,eAAe;gBACjB,IAAI,eAAe,oBAAoB;oBACrC,WAAW,cAAc;gBAC3B,OAAO;oBACL,iBAAiB,MAAM;gBACzB;YACF;;8BAEA,8OAAC;oBAAc,WAAU;;;;;;gBACxB;;;;;;;IAGP;IAEA,MAAM,yBAAyB,CAAC;QAC9B,+DAA+D;QAC/D,MAAM,kBAAkB,KAAK,YAAY,IAAI,KAAK,eAAe,IAAI;QACrE,MAAM,qBAAqB,KAAK,mBAAmB,IAAI;QACvD,MAAM,cAAc,kBAAkB,KAAK,uBAAuB,gBAAgB,uBAAuB;QACzG,MAAM,cAAc,KAAK,WAAW,KAAK,QAAQ,KAAK,WAAW,KAAK,QAAQ,KAAK,WAAW,KAAK;QACnG,MAAM,aAAa,KAAK,WAAW,KAAK,SAAS,KAAK,WAAW,KAAK,QAAQ,KAAK,WAAW,KAAK;QAEnG,mEAAmE;QACnE,IAAI,CAAC,aAAa;YAChB,qBACE,8OAAC,mIAAA,CAAA,kBAAe;0BACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;sCACN,8OAAC,mIAAA,CAAA,iBAAc;4BAAC,OAAO;sCACrB,cAAA,8OAAC;gCAAK,WAAU;;kDACd,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;sCAIhC,8OAAC,mIAAA,CAAA,iBAAc;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;QAKb;QAEA,mDAAmD;QACnD,IAAI,aAAa;YACf,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iIAAA,CAAA,QAAK;wBAAC,WAAU;;0CACf,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAG1C,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,CAAC;4BACR,EAAE,eAAe;4BACjB,WAAW,eAAe;wBAC5B;kCAEA,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;;;;;;QAI5B;QAEA,8DAA8D;QAC9D,IAAI,YAAY;YACd,qBACE,8OAAC,iIAAA,CAAA,QAAK;gBAAC,WAAU;;kCACf,8OAAC,4LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;QAIpC;QAEA,uCAAuC;QACvC,qBACE,8OAAC,kIAAA,CAAA,SAAM;YACL,SAAQ;YACR,MAAK;YACL,WAAU;YACV,SAAS,CAAC;gBACR,EAAE,eAAe;gBACjB,mBAAmB;YACrB;;8BAEA,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;gBAAiB;;;;;;;IAI9C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,6IAAA,CAAA,UAAe;gBACd,MAAM;gBACN,sBAAsB;gBACtB,SAAS;gBACT,kBAAkB;gBAClB,mBAAmB;;;;;;0BAIrB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCAAyB;kDAC5B,8OAAC;wCAAK,WAAU;kDAAiB,cAAc,UAAU;;;;;;oCAAQ;kDAAG,8OAAC;wCAAK,WAAU;kDAAiB,cAAc,QAAQ;;;;;;oCAAQ;kDAAI,8OAAC;wCAAK,WAAU;kDAAiB,cAAc,UAAU;;;;;;oCAAQ;;;;;;;4BAGnN,0CACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,mIAAA,CAAA,kBAAe;kDACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;8DACN,8OAAC,mIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,8OAAC;wDACC,SAAS,qBAAqB,qBAAqB;wDACnD,WAAU;;0EAEV,8OAAC,oIAAA,CAAA,WAAQ;gEACP,SAAS;gEACT,KAAK,CAAC;oEACJ,IAAI,IAAI,GAAG,aAAa,GAAG,uBAAuB,CAAC;gEACrD;;;;;;4DACA;;;;;;;;;;;;8DAIN,8OAAC,mIAAA,CAAA,iBAAc;8DACb,cAAA,8OAAC;kEAAG,qBAAqB,sCAAsC;;;;;;;;;;;;;;;;;;;;;;kDAKrE,8OAAC,mIAAA,CAAA,kBAAe;kDACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;8DACN,8OAAC,mIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,aAAa,MAAM,KAAK,WAAW,MAAM,GAAG,cAAc;wDACnE,WAAU;;0EAEV,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAY;4DACrB,WAAW,MAAM;4DAAC;;;;;;;;;;;;8DAG9B,8OAAC,mIAAA,CAAA,iBAAc;8DACb,cAAA,8OAAC;kEAAG,aAAa,MAAM,KAAK,WAAW,MAAM,GAAG,6BAA6B;;;;;;;;;;;;;;;;;;;;;;oCAKlF,aAAa,MAAM,GAAG,mBACrB,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;;4CAClC,aAAa,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;kCAQ/B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAyB;;;;;;0CACzC,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC;oCACT,gBAAgB,OAAO,EAAE,MAAM,CAAC,KAAK;oCACrC,eAAe;gCACjB;gCACA,WAAU;;kDAEV,8OAAC;wCAAO,OAAO;kDAAI;;;;;;kDACnB,8OAAC;wCAAO,OAAO;kDAAI;;;;;;kDACnB,8OAAC;wCAAO,OAAO;kDAAI;;;;;;kDACnB,8OAAC;wCAAO,OAAO;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAM1B,8OAAC,+IAAA,CAAA,UAAe;gBACd,MAAM;gBACN,SAAS;gBACT,SAAS;gBACT,cAAa;gBACb,sBAAsB;gBACtB,WAAW;;;;;;YAIZ,cAAc,UAAU,GAAG,mBAC1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BAAyB;0CAC/B,8OAAC;gCAAK,WAAU;0CAAiB;;;;;;4BAAmB;0CAAI,8OAAC;gCAAK,WAAU;0CAAiB,cAAc,UAAU;;;;;;;;;;;;kCAG1H,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,mIAAA,CAAA,kBAAe;0CACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;sDACN,8OAAC,mIAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC,cAAc,WAAW;gDACpC,WAAU;0DAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAG5B,8OAAC,mIAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;0CAKT,8OAAC,mIAAA,CAAA,kBAAe;0CACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;sDACN,8OAAC,mIAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC,cAAc,WAAW;gDACpC,WAAU;0DAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAG3B,8OAAC,mIAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;0CAMT,8OAAC;gCAAI,WAAU;0CACZ,MAAM,IAAI,CAAC;oCAAE,QAAQ,KAAK,GAAG,CAAC,GAAG,cAAc,UAAU;gCAAE,GAAG,CAAC,GAAG;oCACjE,IAAI;oCACJ,IAAI,cAAc,UAAU,IAAI,GAAG;wCACjC,UAAU,IAAI;oCAChB,OAAO,IAAI,eAAe,GAAG;wCAC3B,UAAU,IAAI;oCAChB,OAAO,IAAI,eAAe,cAAc,UAAU,GAAG,GAAG;wCACtD,UAAU,cAAc,UAAU,GAAG,IAAI;oCAC3C,OAAO;wCACL,UAAU,cAAc,IAAI;oCAC9B;oCAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;wCAEL,SAAS,gBAAgB,UAAU,YAAY;wCAC/C,MAAK;wCACL,SAAS,IAAM,SAAS;wCACxB,WAAU;kDAET;uCANI;;;;;gCASX;;;;;;0CAGF,8OAAC,mIAAA,CAAA,kBAAe;0CACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;sDACN,8OAAC,mIAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC,cAAc,WAAW;gDACpC,WAAU;0DAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAG5B,8OAAC,mIAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;0CAKT,8OAAC,mIAAA,CAAA,kBAAe;0CACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;sDACN,8OAAC,mIAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC,cAAc,WAAW;gDACpC,WAAU;0DAEV,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAG7B,8OAAC,mIAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASd,4BAA4B,aAAa,MAAM,GAAG,mBACjD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;;gDAClC,aAAa,MAAM;gDAAC;;;;;;;;;;;;;8CAIzB,8OAAC,mIAAA,CAAA,kBAAe;8CACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;0DACN,8OAAC,mIAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;;sEAEV,8OAAC,4LAAA,CAAA,IAAC;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;0DAIlC,8OAAC,mIAAA,CAAA,iBAAc;0DACb,cAAA,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMX,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,mIAAA,CAAA,kBAAe;8CACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;0DACN,8OAAC,mIAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;wDACP,+BAA+B;wDAC/B,MAAM,gBAAgB,CAAC,gBAAgB,GAAG,aAAa,MAAM,CAAC,KAAK,CAAC;oDACtE;oDACA,WAAU;;sEAEV,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;0DAIpC,8OAAC,mIAAA,CAAA,iBAAc;0DACb,cAAA,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;8CAKT,8OAAC,mIAAA,CAAA,kBAAe;8CACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;0DACN,8OAAC,mIAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;wDACP,kCAAkC;wDAClC,MAAM,gBAAgB,CAAC,mBAAmB,GAAG,aAAa,MAAM,CAAC,KAAK,CAAC;oDACzE;oDACA,WAAU;;sEAEV,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;0DAIpC,8OAAC,mIAAA,CAAA,iBAAc;0DACb,cAAA,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;8CAKT,8OAAC,mIAAA,CAAA,kBAAe;8CACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;0DACN,8OAAC,mIAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS;wDACP,+BAA+B;wDAC/B,MAAM,gBAAgB,CAAC,uBAAuB,GAAG,aAAa,MAAM,CAAC,KAAK,CAAC;oDAC7E;oDACA,WAAU;;sEAEV,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;0DAIpC,8OAAC,mIAAA,CAAA,iBAAc;0DACb,cAAA,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;8CAKT,8OAAC,mIAAA,CAAA,kBAAe;8CACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;0DACN,8OAAC,mIAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;wDACP,uCAAuC;wDACvC,MAAM,gBAAgB,CAAC,wBAAwB,GAAG,aAAa,MAAM,CAAC,KAAK,CAAC;oDAC9E;oDACA,WAAU;;sEAEV,8OAAC,wMAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;0DAInC,8OAAC,mIAAA,CAAA,iBAAc;0DACb,cAAA,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;8CAKT,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC,mIAAA,CAAA,kBAAe;8CACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;0DACN,8OAAC,mIAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;wDACP,4CAA4C;wDAC5C,IAAI,QAAQ,CAAC,8BAA8B,EAAE,aAAa,MAAM,CAAC,kBAAkB,CAAC,GAAG;4DACrF,MAAM,gBAAgB,CAAC,gBAAgB,GAAG,aAAa,MAAM,CAAC,KAAK,CAAC;wDACtE;oDACF;oDACA,WAAU;;sEAEV,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;0DAIlC,8OAAC,mIAAA,CAAA,iBAAc;0DACb,cAAA,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUjB,8OAAC,+IAAA,CAAA,wBAAqB;gBACpB,QAAQ,QAAQ,UAAU,CAAC,MAAM;gBACjC,SAAS,IAAM,YAAY;gBAC3B,WAAW;gBACX,MAAM,QAAQ,UAAU,CAAC,IAAI;;;;;;0BAG/B,8OAAC,+IAAA,CAAA,oBAAiB;gBAChB,QAAQ,QAAQ,WAAW,CAAC,MAAM;gBAClC,SAAS,IAAM,YAAY;gBAC3B,YAAY;gBACZ,MAAM,QAAQ,WAAW,CAAC,IAAI;;;;;;0BAGhC,8OAAC,+IAAA,CAAA,2BAAwB;gBACvB,QAAQ,QAAQ,kBAAkB,CAAC,MAAM;gBACzC,SAAS,IAAM,YAAY;gBAC3B,MAAM,QAAQ,kBAAkB,CAAC,IAAI;gBACrC,qBAAqB,QAAQ,kBAAkB,CAAC,mBAAmB;;;;;;;;;;;;AAI3E", "debugId": null}}]}