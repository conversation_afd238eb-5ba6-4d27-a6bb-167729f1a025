#!/usr/bin/env python3
"""
Script per creare backup della webapp-nextjs_2
"""

import os
import shutil
import datetime
import zipfile
import sys

def create_backup():
    """Crea un backup completo della webapp-nextjs_2"""
    
    # Directory corrente (webapp-nextjs_2)
    source_dir = os.getcwd()
    
    # Directory parent (CMS)
    parent_dir = os.path.dirname(source_dir)
    
    # Timestamp per il backup
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Nome del backup
    backup_name = f"webapp-nextjs_2_backup_{timestamp}"
    backup_dir = os.path.join(parent_dir, backup_name)
    backup_zip = f"{backup_dir}.zip"
    
    print(f"🔄 Creazione backup di webapp-nextjs_2...")
    print(f"📁 Directory sorgente: {source_dir}")
    print(f"💾 Backup directory: {backup_dir}")
    print(f"📦 Backup ZIP: {backup_zip}")
    
    try:
        # Lista delle directory/file da escludere dal backup
        exclude_dirs = {
            'node_modules',
            '.next',
            'dist',
            'build',
            '.git',
            '__pycache__',
            '.env.local',
            '.env.production',
            'backup_*'
        }
        
        # Crea directory di backup temporanea
        os.makedirs(backup_dir, exist_ok=True)
        
        # Copia i file escludendo le directory specificate
        for root, dirs, files in os.walk(source_dir):
            # Rimuovi le directory da escludere dalla lista
            dirs[:] = [d for d in dirs if d not in exclude_dirs and not d.startswith('.')]
            
            # Calcola il path relativo
            rel_path = os.path.relpath(root, source_dir)
            if rel_path == '.':
                dest_root = backup_dir
            else:
                dest_root = os.path.join(backup_dir, rel_path)
            
            # Crea la directory di destinazione
            os.makedirs(dest_root, exist_ok=True)
            
            # Copia i file
            for file in files:
                if not file.startswith('.') and not file.endswith('.log'):
                    src_file = os.path.join(root, file)
                    dest_file = os.path.join(dest_root, file)
                    shutil.copy2(src_file, dest_file)
        
        print(f"✅ Backup directory creato: {backup_dir}")
        
        # Crea il file ZIP
        print(f"📦 Creazione file ZIP...")
        with zipfile.ZipFile(backup_zip, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(backup_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, backup_dir)
                    zipf.write(file_path, arc_name)
        
        # Rimuovi la directory temporanea
        shutil.rmtree(backup_dir)
        
        # Verifica dimensione del backup
        backup_size = os.path.getsize(backup_zip)
        backup_size_mb = backup_size / (1024 * 1024)
        
        print(f"✅ Backup completato!")
        print(f"📦 File ZIP: {backup_zip}")
        print(f"📏 Dimensione: {backup_size_mb:.2f} MB")
        print(f"🕒 Timestamp: {timestamp}")
        
        # Crea un file di log del backup
        log_file = os.path.join(parent_dir, f"backup_log_{timestamp}.txt")
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write(f"Backup webapp-nextjs_2\n")
            f.write(f"Data: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Directory sorgente: {source_dir}\n")
            f.write(f"File backup: {backup_zip}\n")
            f.write(f"Dimensione: {backup_size_mb:.2f} MB\n")
            f.write(f"Stato: Completato con successo\n")
            f.write(f"\nDirectory escluse dal backup:\n")
            for exclude in exclude_dirs:
                f.write(f"  - {exclude}\n")
        
        print(f"📝 Log salvato: {log_file}")
        
        return backup_zip
        
    except Exception as e:
        print(f"❌ Errore durante la creazione del backup: {str(e)}")
        # Pulisci in caso di errore
        if os.path.exists(backup_dir):
            shutil.rmtree(backup_dir)
        if os.path.exists(backup_zip):
            os.remove(backup_zip)
        return None

def list_backups():
    """Lista tutti i backup esistenti"""
    parent_dir = os.path.dirname(os.getcwd())
    backups = []
    
    for file in os.listdir(parent_dir):
        if file.startswith('webapp-nextjs_2_backup_') and file.endswith('.zip'):
            backup_path = os.path.join(parent_dir, file)
            backup_size = os.path.getsize(backup_path)
            backup_size_mb = backup_size / (1024 * 1024)
            backup_time = os.path.getmtime(backup_path)
            backup_date = datetime.datetime.fromtimestamp(backup_time)
            
            backups.append({
                'file': file,
                'path': backup_path,
                'size_mb': backup_size_mb,
                'date': backup_date
            })
    
    # Ordina per data (più recente prima)
    backups.sort(key=lambda x: x['date'], reverse=True)
    
    if backups:
        print(f"\n📋 Backup esistenti:")
        for i, backup in enumerate(backups, 1):
            print(f"  {i}. {backup['file']}")
            print(f"     📅 Data: {backup['date'].strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"     📏 Dimensione: {backup['size_mb']:.2f} MB")
            print()
    else:
        print("📋 Nessun backup trovato.")
    
    return backups

if __name__ == "__main__":
    print("🔧 Sistema di Backup webapp-nextjs_2")
    print("=" * 50)
    
    if len(sys.argv) > 1 and sys.argv[1] == "list":
        list_backups()
    else:
        # Lista backup esistenti
        print("📋 Backup esistenti:")
        existing_backups = list_backups()
        
        print("\n" + "=" * 50)
        
        # Crea nuovo backup
        backup_file = create_backup()
        
        if backup_file:
            print(f"\n🎉 Backup creato con successo!")
            print(f"💾 Percorso: {backup_file}")
            print(f"\n💡 Per ripristinare questo backup:")
            print(f"   1. Estrai il file ZIP")
            print(f"   2. Sostituisci la directory webapp-nextjs_2")
            print(f"   3. Esegui: npm install")
            print(f"   4. Esegui: npm run build")
        else:
            print(f"\n❌ Backup fallito!")
            sys.exit(1)
