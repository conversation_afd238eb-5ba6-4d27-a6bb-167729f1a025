(()=>{var e={};e.id=1669,e.ids=[1669],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9593:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("cloud",[["path",{d:"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z",key:"p7xjir"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11270:(e,t,s)=>{Promise.resolve().then(s.bind(s,87303))},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29358:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>o});var a=s(65239),r=s(48088),i=s(88170),n=s.n(i),l=s(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(t,c);let o={children:["",{children:["test-certificazioni",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,87303)),"C:\\CMS\\webapp-nextjs_1\\src\\app\\test-certificazioni\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\CMS\\webapp-nextjs_1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\CMS\\webapp-nextjs_1\\src\\app\\test-certificazioni\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/test-certificazioni/page",pathname:"/test-certificazioni",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},33873:e=>{"use strict";e.exports=require("path")},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>o,ZB:()=>l,Zp:()=>i,aR:()=>n});var a=s(60687);s(43210);var r=s(4780);function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...t})}},45989:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("test-tube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5c-1.4 0-2.5-1.1-2.5-2.5V2",key:"125lnx"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]])},47342:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60773:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var a=s(60687),r=s(43210),i=s(44493),n=s(29523),l=s(96834),c=s(5336),o=s(93613),d=s(45989),u=s(41862),m=s(10022),x=s(9593),p=s(47342),h=s(84027),g=s(62185);function f(){let[e,t]=(0,r.useState)(!1),[s,f]=(0,r.useState)([]),[v]=(0,r.useState)(1),j=async()=>{t(!0),f([]);let e=[];try{let t=await g._I.getWeatherData(v);e.push({test:"Dati Meteorologici",status:"success",message:`Temperatura: ${t.data.temperature}\xb0C, Umidit\xe0: ${t.data.humidity}%`,data:t.data})}catch(t){e.push({test:"Dati Meteorologici",status:"error",message:t.response?.data?.detail||t.message})}try{let t=await g.At.getCavi(v),s=t.filter(e=>3>(e.collegamenti||0));e.push({test:"Caricamento Cavi",status:"success",message:`${t.length} cavi totali, ${s.length} non completamente collegati`,data:{totali:t.length,nonCollegati:s.length}})}catch(t){e.push({test:"Caricamento Cavi",status:"error",message:t.response?.data?.detail||t.message})}try{let t=await g.kw.getStrumenti(v),s=t.filter(e=>"ATTIVO"===e.stato);e.push({test:"Caricamento Strumenti",status:"success",message:`${t.length} strumenti totali, ${s.length} attivi`,data:{totali:t.length,attivi:s.length}})}catch(t){e.push({test:"Caricamento Strumenti",status:"error",message:t.response?.data?.detail||t.message})}try{let t=await g.km.getCertificazioni(v),s=t.filter(e=>"CONFORME"===e.stato_certificato);e.push({test:"Caricamento Certificazioni",status:"success",message:`${t.length} certificazioni totali, ${s.length} conformi`,data:{totali:t.length,conformi:s.length}})}catch(t){e.push({test:"Caricamento Certificazioni",status:"error",message:t.response?.data?.detail||t.message})}try{let t=(await g.At.getCavi(v)).find(e=>3>(e.collegamenti||0));t?e.push({test:"Collegamento Automatico",status:"info",message:`Cavo ${t.id_cavo} disponibile per test collegamento (collegamenti: ${t.collegamenti||0}/3)`,data:{cavoId:t.id_cavo,collegamenti:t.collegamenti||0}}):e.push({test:"Collegamento Automatico",status:"warning",message:"Nessun cavo disponibile per test collegamento (tutti gi\xe0 collegati)"})}catch(t){e.push({test:"Collegamento Automatico",status:"error",message:t.response?.data?.detail||t.message})}f(e),t(!1)},b=e=>{switch(e){case"success":return(0,a.jsx)(c.A,{className:"h-5 w-5 text-green-600"});case"error":return(0,a.jsx)(o.A,{className:"h-5 w-5 text-red-600"});case"warning":return(0,a.jsx)(o.A,{className:"h-5 w-5 text-yellow-600"});case"info":return(0,a.jsx)(d.A,{className:"h-5 w-5 text-blue-600"});default:return(0,a.jsx)(u.A,{className:"h-5 w-5 animate-spin"})}},N=e=>{switch(e){case"success":return(0,a.jsx)(l.E,{className:"bg-green-100 text-green-800 border-green-200",children:"Successo"});case"error":return(0,a.jsx)(l.E,{className:"bg-red-100 text-red-800 border-red-200",children:"Errore"});case"warning":return(0,a.jsx)(l.E,{className:"bg-yellow-100 text-yellow-800 border-yellow-200",children:"Attenzione"});case"info":return(0,a.jsx)(l.E,{className:"bg-blue-100 text-blue-800 border-blue-200",children:"Info"});default:return(0,a.jsx)(l.E,{variant:"outline",children:"In corso"})}};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-slate-900 flex items-center gap-3",children:[(0,a.jsx)(d.A,{className:"h-8 w-8 text-blue-600"}),"Test Sistema Certificazioni"]}),(0,a.jsx)("p",{className:"text-slate-600 mt-1",children:"Verifica funzionalit\xe0 e automazioni CEI 64-8"})]}),(0,a.jsx)(n.$,{onClick:j,disabled:e,children:e?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Test in corso..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Avvia Test"]})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-5 w-5"}),"Funzionalit\xe0 Implementate"]}),(0,a.jsx)(i.BT,{children:"Sistema certificazioni CEI 64-8 con automazioni"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 text-blue-500"}),(0,a.jsx)("span",{className:"font-medium",children:"Dati Meteorologici Automatici"})]}),(0,a.jsx)("p",{className:"text-sm text-slate-600 ml-6",children:"Temperatura e umidit\xe0 rilevate automaticamente per il cantiere"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 text-green-500"}),(0,a.jsx)("span",{className:"font-medium",children:"Collegamento Automatico Cavi"})]}),(0,a.jsx)("p",{className:"text-sm text-slate-600 ml-6",children:"Collegamento automatico durante certificazione CEI 64-8"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 text-purple-500"}),(0,a.jsx)("span",{className:"font-medium",children:"Validazione Automatica"})]}),(0,a.jsx)("p",{className:"text-sm text-slate-600 ml-6",children:"Determinazione automatica conformit\xe0 in base ai valori"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 text-orange-500"}),(0,a.jsx)("span",{className:"font-medium",children:"Gestione Strumenti"})]}),(0,a.jsx)("p",{className:"text-sm text-slate-600 ml-6",children:"Tracking calibrazione e notifiche scadenze"})]})]})})]}),s.length>0&&(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Risultati Test"}),(0,a.jsx)(i.BT,{children:"Verifica delle funzionalit\xe0 del sistema"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:s.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-start gap-3 p-3 rounded-lg border",children:[b(e.status),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"font-medium",children:e.test}),N(e.status)]}),(0,a.jsx)("p",{className:"text-sm text-slate-600 mt-1",children:e.message}),e.data&&(0,a.jsx)("pre",{className:"text-xs bg-slate-100 p-2 rounded mt-2 overflow-auto",children:JSON.stringify(e.data,null,2)})]})]},t))})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Accesso al Sistema"}),(0,a.jsx)(i.BT,{children:"Link per testare l'interfaccia completa"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(n.$,{asChild:!0,children:(0,a.jsxs)("a",{href:"/certificazioni",target:"_blank",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Sistema Certificazioni"]})}),(0,a.jsx)(n.$,{variant:"outline",asChild:!0,children:(0,a.jsxs)("a",{href:"/cavi",target:"_blank",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Gestione Cavi"]})})]})})]})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84027:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},87303:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs_1\\\\src\\\\app\\\\test-certificazioni\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_1\\src\\app\\test-certificazioni\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>c});var a=s(60687);s(43210);var r=s(8730),i=s(24224),n=s(4780);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c({className:e,variant:t,asChild:s=!1,...i}){let c=s?r.DX:"span";return(0,a.jsx)(c,{"data-slot":"badge",className:(0,n.cn)(l({variant:t}),e),...i})}},97358:(e,t,s)=>{Promise.resolve().then(s.bind(s,60773))}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,6539,1658,4951],()=>s(29358));module.exports=a})();