'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Loader2, AlertCircle, Calculator, Search, CheckCircle, AlertTriangle, X } from 'lucide-react'
import { Cavo } from '@/types'
import { parcoCaviApi, caviApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'
import { useToast } from '@/hooks/use-toast'

import ModificaBobinaDialog from './ModificaBobinaDialog'

interface Bobina {
  id_bobina: string
  numero_bobina?: string
  tipologia: string
  formazione: string
  metri_residui: number
  fornitore?: string
  stato_bobina?: string
}

interface FormData {
  metri_posati: string
  id_bobina: string
}

interface FormErrors {
  metri_posati?: string
  id_bobina?: string
}

interface FormWarnings {
  metri_posati?: string
}

interface InserisciMetriDialogProps {
  open: boolean
  onClose: () => void
  cavo: Cavo | null
  cantiere?: { id_cantiere: number; commessa: string } | null
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

export default function InserisciMetriDialog({
  open,
  onClose,
  cavo,
  cantiere: cantiereProp,
  onSuccess,
  onError
}: InserisciMetriDialogProps) {
  const { cantiere: cantiereAuth } = useAuth()

  // Usa il cantiere passato come prop o quello dall'auth come fallback
  const cantiere = cantiereProp || cantiereAuth

  // Stati per il form
  const [formData, setFormData] = useState<FormData>({
    metri_posati: '',
    id_bobina: ''
  })

  // Debug formData changes
  useEffect(() => {
    console.log('📊 InserisciMetriDialog: FormData aggiornato:', {
      hasMetri: !!formData.metri_posati,
      hasBobina: !!formData.id_bobina,
      metri_posati: formData.metri_posati,
      id_bobina: formData.id_bobina
    })
  }, [formData])
  const [formErrors, setFormErrors] = useState<FormErrors>({})
  const [formWarnings, setFormWarnings] = useState<FormWarnings>({})
  const [saving, setSaving] = useState(false)

  // Stati per bobine
  const [bobine, setBobine] = useState<Bobina[]>([])
  const [bobineLoading, setBobineLoading] = useState(false)

  // Stati per la ricerca delle bobine
  const [searchText, setSearchText] = useState('')

  // Stati per dialoghi
  const [showModificaBobinaDialog, setShowModificaBobinaDialog] = useState(false)

  // Carica bobine quando si apre il dialog
  useEffect(() => {
    if (open && cantiere) {
      loadBobine()
    }
  }, [open, cantiere])

  // Reset form quando si apre il dialog
  useEffect(() => {
    if (open && cavo) {
      setFormData({
        metri_posati: '',
        id_bobina: ''
      })
      setFormErrors({})
      setFormWarnings({})
      setSearchText('')
    }
  }, [open, cavo])

  // Funzione per estrarre il numero della bobina dall'ID completo
  const getBobinaNumber = (idBobina: string) => {
    if (!idBobina || idBobina === 'BOBINA_VUOTA') return 'VUOTA'
    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}
    if (idBobina && idBobina.includes('_B')) {
      return idBobina.split('_B')[1]
    }
    // Cerca nella lista bobine per ottenere il numero_bobina
    const bobina = bobine.find(b => b.id_bobina === idBobina)
    return bobina ? bobina.numero_bobina || idBobina : idBobina
  }

  // Filtra le bobine compatibili
  const getBobineCompatibili = () => {
    if (!cavo) return []

    return bobine.filter(bobina => {
      const isCompatible = bobina.tipologia === cavo.tipologia &&
                          bobina.sezione === cavo.sezione
      const matchesSearch = searchText === '' ||
                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase()) ||
                           (bobina.tipologia && bobina.tipologia.toLowerCase().includes(searchText.toLowerCase())) ||
                           (bobina.numero_bobina && bobina.numero_bobina.toLowerCase().includes(searchText.toLowerCase()))
      return isCompatible && matchesSearch && bobina.metri_residui > 0
    })
  }

  // Filtra le bobine incompatibili
  const getBobineIncompatibili = () => {
    if (!cavo) return []

    return bobine.filter(bobina => {
      const isIncompatible = bobina.tipologia !== cavo.tipologia ||
                            bobina.sezione !== cavo.sezione
      const matchesSearch = searchText === '' ||
                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase()) ||
                           (bobina.tipologia && bobina.tipologia.toLowerCase().includes(searchText.toLowerCase())) ||
                           (bobina.numero_bobina && bobina.numero_bobina.toLowerCase().includes(searchText.toLowerCase()))
      return isIncompatible && matchesSearch && bobina.metri_residui > 0
    })
  }

  // Filtra bobine in base alla ricerca
  const bobineFiltrate = bobine.filter(bobina => {
    if (!searchText) return true
    const searchLower = searchText.toLowerCase()
    return (
      bobina.id_bobina.toLowerCase().includes(searchLower) ||
      bobina.tipologia.toLowerCase().includes(searchLower) ||
      bobina.formazione.toLowerCase().includes(searchLower) ||
      getBobinaNumber(bobina.id_bobina).toLowerCase().includes(searchLower)
    )
  })

  // Usa le funzioni di filtro per evitare duplicazioni
  const bobineCompatibili = getBobineCompatibili()
  const bobineIncompatibili = getBobineIncompatibili()

  // Gestisce la selezione di una bobina
  const handleBobinaSelect = (bobina: Bobina) => {
    console.log('🎯 Bobina selezionata:', {
      id: bobina.id_bobina,
      numero: getBobinaNumber(bobina.id_bobina),
      tipologia: bobina.tipologia,
      formazione: bobina.formazione,
      metri_residui: bobina.metri_residui
    })

    setFormData(prev => ({ ...prev, id_bobina: bobina.id_bobina }))
    setFormErrors(prev => ({ ...prev, id_bobina: undefined }))
  }

  // Gestisce la selezione di BOBINA VUOTA
  // Permette di posare il cavo senza assegnare una bobina specifica
  // Il cavo potrà essere collegato a una bobina in seguito tramite ModificaBobinaDialog
  const handleBobinaVuotaSelect = () => {
    console.log('🎯 BOBINA VUOTA selezionata - cavo sarà posato senza bobina specifica')
    setFormData(prev => ({ ...prev, id_bobina: 'BOBINA_VUOTA' }))

    // Reset completo degli errori - rimuovi la chiave invece di impostarla vuota
    setFormErrors(prev => {
      const newErrors = { ...prev }
      delete newErrors.id_bobina  // Rimuovi completamente la chiave
      return newErrors
    })
  }

  // Carica bobine quando si apre il dialog
  useEffect(() => {
    if (open && cavo) {

      if (cantiere) {
        loadBobine()
      } else {
      }

      setFormData({
        metri_posati: '0', // Default a 0 come nell'originale
        id_bobina: ''
      })
      setFormErrors({})
      setFormWarnings({})
      setSearchText('')
    }
  }, [open, cavo, cantiere])

  // Validazione real-time dei metri posati
  useEffect(() => {
    if (formData.metri_posati && cavo) {
      validateMetriPosati(parseFloat(formData.metri_posati))
    } else {
      setFormErrors(prev => ({ ...prev, metri_posati: undefined }))
      setFormWarnings(prev => ({ ...prev, metri_posati: undefined }))
    }
  }, [formData.metri_posati, cavo])

  const validateMetriPosati = (metri: number) => {
    if (!cavo) return

    let errors: FormErrors = { ...formErrors }
    let warnings: FormWarnings = { ...formWarnings }

    // Rimuovi errori/warning precedenti per metri_posati
    delete errors.metri_posati
    delete warnings.metri_posati

    // NESSUN ERRORE BLOCCANTE - Solo warning informativi
    // I warning non impediscono il salvataggio

    if (metri > (cavo.metri_teorici || 0) * 1.1) {
      warnings.metri_posati = `Attenzione: i metri posati superano del 10% i metri teorici (${cavo.metri_teorici}m)`
    } else if (metri > (cavo.metri_teorici || 0)) {
      warnings.metri_posati = 'Metratura superiore ai metri teorici'
    }

    setFormErrors(errors)
    setFormWarnings(warnings)
  }

  const loadBobine = async () => {
    console.log({
      cavo: !!cavo,
      cantiere: !!cantiere,
      cavoId: cavo?.id_cavo,
      cantiereId: cantiere?.id_cantiere
    })

    if (!cavo || !cantiere) {
      return
    }

    try {
      setBobineLoading(true)

      // Carica tutte le bobine disponibili
      const response = await parcoCaviApi.getBobine(cantiere.id_cantiere)

      // Gestisce diversi formati di risposta
      let bobineData = []
      if (Array.isArray(response)) {
        bobineData = response
      } else if (response && Array.isArray(response.data)) {
        bobineData = response.data
      } else if (response && response.bobine && Array.isArray(response.bobine)) {
        bobineData = response.bobine
      } else {
        throw new Error('Formato risposta API non valido')
      }

      // Filtra solo per stato (disponibile o in uso) e metri residui > 0
      const bobineUtilizzabili = bobineData.filter((bobina: Bobina) =>
        bobina.stato_bobina !== 'Terminata' &&
        bobina.stato_bobina !== 'Over' &&
        bobina.metri_residui > 0
      )

      if (cavo) {
        console.log({
          tipologia: cavo.tipologia,
          sezione: cavo.sezione
        })

        // Separa bobine compatibili e incompatibili
        const bobineCompatibili = bobineUtilizzabili.filter(bobina => {
          const isCompatible = bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione
          return isCompatible
        })
        const bobineNonCompatibili = bobineUtilizzabili.filter(bobina =>
          !(bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione)
        )

        // Ordina entrambi gli array per metri residui (decrescente)
        bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui)
        bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui)

        // Concatena gli array: prima le compatibili, poi le non compatibili
        const bobineOrdinate = [...bobineCompatibili, ...bobineNonCompatibili]
        setBobine(bobineOrdinate)
      } else {
        // Se non c'è un cavo selezionato, mostra tutte le bobine ordinate per metri residui
        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui)
        setBobine(bobineUtilizzabili)
      }
    } catch (error: any) {
      console.log({
        message: error.message,
        response: error.response,
        status: error.response?.status,
        data: error.response?.data
      })

      // Non mostrare errore se è solo un problema di rete, permetti di usare BOBINA_VUOTA
      if (error.response?.status !== 404) {
        onError('Errore nel caricamento delle bobine. Puoi comunque usare BOBINA VUOTA.')
      }
      setBobine([])
    } finally {
      setBobineLoading(false)
    }
  }



  const handleSave = async () => {
    console.log({
      cavo: cavo?.id_cavo,
      metri_posati: formData.metri_posati,
      id_bobina: formData.id_bobina
    })

    if (!cavo) {
      return
    }

    // Validazioni di base (solo controlli essenziali)
    if (!formData.metri_posati || parseFloat(formData.metri_posati) < 0) {
      onError('Inserire metri posati validi (≥ 0)')
      return
    }

    if (!formData.id_bobina) {
      onError('Selezionare una bobina o BOBINA VUOTA')
      return
    }

    const metri = parseFloat(formData.metri_posati)

    // Gestione stato OVER per bobine reali (NON BLOCCANTE)
    if (formData.id_bobina !== 'BOBINA_VUOTA') {
      const bobina = bobine.find(b => b.id_bobina === formData.id_bobina)
      if (bobina && metri > bobina.metri_residui) {
        // OVER state - salva comunque ma avvisa
        // Il salvataggio continua - lo stato OVER viene gestito dal backend
      }
    }

    try {
      setSaving(true)

      if (!cantiere) {
        throw new Error('Cantiere non selezionato')
      }

      // Aggiorna metri posati tramite API
      console.log({
        cantiere: cantiere.id_cantiere,
        cavo: cavo.id_cavo,
        metri: metri,
        bobina: formData.id_bobina,  // Mostra il valore reale che viene passato
        isBobinaVuota: formData.id_bobina === 'BOBINA_VUOTA'
      })

      await caviApi.updateMetriPosati(
        cantiere.id_cantiere,
        cavo.id_cavo,
        metri,
        formData.id_bobina,  // Passa sempre il valore, incluso 'BOBINA_VUOTA'
        true  // force_over: true per permettere bobine incompatibili e OVER state
      )

      onSuccess(`Metri posati aggiornati con successo per il cavo ${cavo.id_cavo}: ${metri}m`)
      onClose()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante il salvataggio dei metri posati'
      onError(errorMessage)
    } finally {
      setSaving(false)
    }
  }

  const handleClose = () => {
    if (!saving) {
      setFormData({ metri_posati: '', id_bobina: '' })
      setFormErrors({})
      setFormWarnings({})
      setSearchText('')
      onClose()
    }
  }

  if (!cavo) return null

  return (
    <>
      <Dialog open={open} onOpenChange={handleClose}>
        <DialogContent className="max-w-7xl h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <Calculator className="h-5 w-5" />
              Inserisci Metri Posati - {cavo.id_cavo}
            </DialogTitle>
            <DialogDescription>
              Inserisci i metri effettivamente posati per il cavo e seleziona una bobina o usa BOBINA VUOTA
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto space-y-6">
            {/* Sezione informazioni cavo e metri posati */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
              {/* Informazioni cavo - 2/3 della larghezza */}
              <div className="lg:col-span-2">
                <div className="p-4 bg-blue-50 rounded-lg border-2 border-blue-200">
                  <h3 className="font-semibold text-blue-800 mb-3">Informazioni Cavo</h3>
                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div><strong>Tipologia:</strong> {cavo.tipologia || 'N/A'}</div>
                    <div><strong>Da:</strong> {cavo.ubicazione_partenza || 'N/A'}</div>
                    <div><strong>Formazione:</strong> {cavo.sezione || 'N/A'}</div>
                    <div><strong>A:</strong> {cavo.ubicazione_arrivo || 'N/A'}</div>
                    <div><strong>Metri teorici:</strong> {cavo.metri_teorici || 'N/A'} m</div>
                    <div><strong>Già posati:</strong> {cavo.metratura_reale || 0} m</div>
                  </div>
                </div>
              </div>

              {/* Campo metri posati - 1/3 della larghezza */}
              <div className="lg:col-span-1">
                <div className="p-4 bg-blue-50 rounded-lg border-2 border-blue-300 h-full">
                  <h3 className="font-semibold text-blue-800 mb-3">Metri da Installare</h3>
                  <div className="space-y-2">
                    <Label htmlFor="metri" className="text-sm font-medium">
                      Metri Posati
                    </Label>
                    <div className="relative">
                      <Input
                        id="metri"
                        type="number"
                        value={formData.metri_posati}
                        onChange={(e) => setFormData(prev => ({ ...prev, metri_posati: e.target.value }))}
                        placeholder="Inserisci metri posati"
                        disabled={saving}
                        step="0.1"
                        min="0"
                        className="text-lg font-bold text-center border-2 border-blue-400 focus:border-blue-600"
                        autoFocus
                      />
                      <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-lg font-bold text-blue-600">
                        m
                      </span>
                    </div>
                    {formErrors.metri_posati && (
                      <p className="text-sm text-red-600">{formErrors.metri_posati}</p>
                    )}
                    {formWarnings.metri_posati && (
                      <p className="text-sm text-amber-600">{formWarnings.metri_posati}</p>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Selezione bobina */}
            <div className="space-y-4">
              <h3 className="font-semibold text-blue-800 text-lg">Selezione Bobina</h3>

              {/* Controlli di ricerca e BOBINA VUOTA */}
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="grid grid-cols-1 sm:grid-cols-12 gap-3 items-center">
                  {/* Campo di ricerca - 5 colonne */}
                  <div className="sm:col-span-5">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="ID, tipologia, formazione..."
                        value={searchText}
                        onChange={(e) => setSearchText(e.target.value)}
                        className="pl-10"
                        disabled={saving}
                      />
                      {searchText && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                          onClick={() => setSearchText('')}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* Pulsante BOBINA VUOTA - 7 colonne */}
                  <div className="sm:col-span-7">
                    <Button
                      type="button"
                      variant={formData.id_bobina === 'BOBINA_VUOTA' ? 'default' : 'outline'}
                      className={`w-full h-10 font-bold flex items-center justify-center gap-2 ${
                        formData.id_bobina === 'BOBINA_VUOTA'
                          ? 'bg-green-600 hover:bg-green-700 text-white'
                          : 'border-blue-400 text-blue-700 hover:bg-blue-50'
                      }`}
                      onClick={handleBobinaVuotaSelect}
                      disabled={saving}
                    >
                      {formData.id_bobina === 'BOBINA_VUOTA' && (
                        <CheckCircle className="h-5 w-5" />
                      )}
                      BOBINA VUOTA
                    </Button>
                  </div>
                </div>

                {/* Messaggio informativo per BOBINA VUOTA */}
                {formData.id_bobina === 'BOBINA_VUOTA' && (
                  <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-start gap-2">
                      <CheckCircle className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                      <div className="text-sm text-blue-800">
                        <p className="font-medium">Bobina Vuota Selezionata</p>
                        <p className="mt-1">
                          Il cavo sarà posato senza assegnazione di bobina specifica.
                          Potrai collegarlo a una bobina in seguito tramite la funzione "Modifica Bobina".
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {bobineLoading ? (
                <div className="flex items-center justify-center p-8">
                  <Loader2 className="h-6 w-6 animate-spin mr-2" />
                  <span>Caricamento bobine...</span>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Bobine compatibili */}
                  <div>
                    <h4 className="font-medium text-green-700 mb-2 flex items-center gap-2">
                      <CheckCircle className="h-4 w-4" />
                      Bobine Compatibili ({bobineCompatibili.length})
                    </h4>
                    <div className="max-h-72 overflow-y-auto border rounded-lg">
                      {bobineCompatibili.length === 0 ? (
                        <div className="p-4 text-center text-gray-500">
                          Nessuna bobina compatibile trovata
                        </div>
                      ) : (
                        <div className="divide-y">
                          {bobineCompatibili.map((bobina) => (
                            <div
                              key={bobina.id_bobina}
                              className={`p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ${
                                formData.id_bobina === bobina.id_bobina
                                  ? 'bg-green-100 border-green-500 shadow-md'
                                  : 'border-gray-200 hover:bg-green-50 hover:border-green-300'
                              }`}
                              onClick={() => handleBobinaSelect(bobina)}
                            >
                              <div className="flex justify-between items-center">
                                <div className="flex items-center gap-3 flex-1 min-w-0">
                                  {formData.id_bobina === bobina.id_bobina && (
                                    <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                                  )}
                                  <div className="font-bold text-base min-w-fit">
                                    {getBobinaNumber(bobina.id_bobina)}
                                  </div>
                                  <div className="text-sm text-gray-600 truncate">
                                    {bobina.tipologia} - {bobina.sezione}
                                  </div>
                                </div>
                                <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300 font-medium min-w-fit">
                                  {bobina.metri_residui}m
                                </Badge>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Bobine incompatibili */}
                  <div>
                    <h4 className="font-medium text-amber-700 mb-2 flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4" />
                      Bobine Incompatibili ({bobineIncompatibili.length})
                    </h4>
                    <div className="max-h-72 overflow-y-auto border rounded-lg">
                      {bobineIncompatibili.length === 0 ? (
                        <div className="p-4 text-center text-gray-500">
                          Nessuna bobina incompatibile trovata
                        </div>
                      ) : (
                        <div className="divide-y">
                          {bobineIncompatibili.map((bobina) => (
                            <div
                              key={bobina.id_bobina}
                              className={`p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ${
                                formData.id_bobina === bobina.id_bobina
                                  ? 'bg-amber-100 border-amber-500 shadow-md'
                                  : 'border-gray-200 hover:bg-amber-50 hover:border-amber-300'
                              }`}
                              onClick={() => handleBobinaSelect(bobina)}
                            >
                              <div className="flex justify-between items-center">
                                <div className="flex items-center gap-3 flex-1 min-w-0">
                                  {formData.id_bobina === bobina.id_bobina && (
                                    <CheckCircle className="h-5 w-5 text-amber-600 flex-shrink-0" />
                                  )}
                                  <div className="font-bold text-base min-w-fit">
                                    {getBobinaNumber(bobina.id_bobina)}
                                  </div>
                                  <div className="text-sm text-gray-600 truncate">
                                    {bobina.tipologia} - {bobina.sezione}
                                  </div>
                                </div>
                                <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-300 font-medium min-w-fit">
                                  {bobina.metri_residui}m
                                </Badge>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {bobine.length === 0 && !bobineLoading && (
                <Alert className="border-amber-200 bg-amber-50">
                  <AlertTriangle className="h-4 w-4 text-amber-600" />
                  <AlertDescription className="text-amber-800">
                    Non ci sono bobine disponibili. Puoi procedere con BOBINA VUOTA o aggiungere prima una nuova bobina.
                  </AlertDescription>
                </Alert>
              )}

              {formErrors.id_bobina && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{formErrors.id_bobina}</AlertDescription>
                </Alert>
              )}
            </div>
          </div>

          <DialogFooter className="flex-shrink-0 border-t pt-4 mt-4 flex justify-between items-center">
            {/* Pulsante Modifica Bobina a sinistra (solo se cavo è installato) */}
            <div>
              {cavo.stato_installazione === 'installato' && cavo.id_bobina && (
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowModificaBobinaDialog(true)
                  }}
                  disabled={saving}
                  className="text-blue-600 border-blue-300 hover:bg-blue-50"
                >
                  Modifica Bobina
                </Button>
              )}
            </div>

            {/* Pulsanti principali a destra */}
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleClose} disabled={saving}>
                Annulla
              </Button>
              <Button
                onClick={handleSave}
                disabled={
                  saving ||
                  !formData.metri_posati ||
                  parseFloat(formData.metri_posati) < 0 ||
                  !formData.id_bobina
                }
                className="bg-mariner-600 hover:bg-mariner-700 text-white disabled:bg-gray-400 disabled:text-gray-200"
              >
                {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Salva
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog per modifica bobina */}
      <ModificaBobinaDialog
        open={showModificaBobinaDialog}
        onClose={() => setShowModificaBobinaDialog(false)}
        cavo={cavo}
        onSuccess={(message) => {
          onSuccess(message)
          setShowModificaBobinaDialog(false)
          onClose() // Chiudi anche il dialog principale dopo il successo
        }}
        onError={onError}
      />
    </>
  )
}
