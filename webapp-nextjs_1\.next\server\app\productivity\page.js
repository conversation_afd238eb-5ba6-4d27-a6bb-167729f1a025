(()=>{var e={};e.id=451,e.ids=[451],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10833:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var a=s(60687),r=s(43210),i=s(44493),n=s(29523),l=s(96834),c=s(46657),d=s(28947),o=s(45583),x=s(25541),m=s(41312),u=s(40228),p=s(48730),f=s(53411),v=s(58559);function h(){let[e,t]=(0,r.useState)("week");return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,a.jsxs)("div",{className:"max-w-[90%] mx-auto space-y-6",children:[(0,a.jsx)("div",{className:"flex justify-end mb-6",children:(0,a.jsx)("div",{className:"flex gap-2",children:["day","week","month"].map(s=>(0,a.jsx)(n.$,{variant:e===s?"default":"outline",size:"sm",onClick:()=>t(s),className:"capitalize",children:"day"===s?"Oggi":"week"===s?"Settimana":"Mese"},s))})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsxs)(i.Zp,{className:"border-l-4 border-l-blue-500",children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium text-slate-600",children:"Avanzamento Totale"}),(0,a.jsx)(d.A,{className:"h-4 w-4 text-blue-500"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-slate-900",children:[71.2,"%"]}),(0,a.jsx)(c.k,{value:71.2,className:"mt-2"}),(0,a.jsxs)("p",{className:"text-xs text-slate-500 mt-2",children:[890," di ",1250," cavi"]})]})]}),(0,a.jsxs)(i.Zp,{className:"border-l-4 border-l-green-500",children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium text-slate-600",children:"Velocit\xe0 Installazione"}),(0,a.jsx)(o.A,{className:"h-4 w-4 text-green-500"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-slate-900",children:12.5}),(0,a.jsx)("p",{className:"text-xs text-slate-500",children:"cavi/ora per persona"}),(0,a.jsxs)("div",{className:"flex items-center mt-2",children:[(0,a.jsx)(x.A,{className:"h-3 w-3 text-green-500 mr-1"}),(0,a.jsx)("span",{className:"text-xs text-green-600",children:"+12% vs settimana scorsa"})]})]})]}),(0,a.jsxs)(i.Zp,{className:"border-l-4 border-l-orange-500",children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium text-slate-600",children:"Team Attivi"}),(0,a.jsx)(m.A,{className:"h-4 w-4 text-orange-500"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-slate-900",children:8}),(0,a.jsx)("p",{className:"text-xs text-slate-500",children:"squadre operative"}),(0,a.jsx)("div",{className:"flex items-center mt-2",children:(0,a.jsx)(l.E,{variant:"secondary",className:"text-xs",children:"16 persone"})})]})]}),(0,a.jsxs)(i.Zp,{className:"border-l-4 border-l-purple-500",children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium text-slate-600",children:"Completamento Stimato"}),(0,a.jsx)(u.A,{className:"h-4 w-4 text-purple-500"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-slate-900",children:"15 giorni"}),(0,a.jsx)("p",{className:"text-xs text-slate-500",children:"al ritmo attuale"}),(0,a.jsxs)("div",{className:"flex items-center mt-2",children:[(0,a.jsx)(p.A,{className:"h-3 w-3 text-purple-500 mr-1"}),(0,a.jsx)("span",{className:"text-xs text-purple-600",children:"Aggiornato ora"})]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(f.A,{className:"h-5 w-5 text-blue-600"}),"Performance Team"]}),(0,a.jsx)(i.BT,{children:"Statistiche dettagliate per squadra"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:[{name:"Team Alpha",members:4,installed:156,connected:98,certified:67,efficiency:92},{name:"Team Beta",members:3,installed:134,connected:89,certified:54,efficiency:88},{name:"Team Gamma",members:5,installed:189,connected:145,certified:89,efficiency:95},{name:"Team Delta",members:4,installed:167,connected:123,certified:78,efficiency:90}].map((e,t)=>(0,a.jsx)("div",{className:"flex items-center justify-between p-3 bg-slate-50 rounded-lg",children:(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium text-slate-900",children:e.name}),(0,a.jsxs)(l.E,{variant:e.efficiency>=90?"default":"secondary",children:[e.efficiency,"% efficienza"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-sm text-slate-600",children:[(0,a.jsxs)("div",{children:["Installati: ",(0,a.jsx)("span",{className:"font-medium text-slate-900",children:e.installed})]}),(0,a.jsxs)("div",{children:["Collegati: ",(0,a.jsx)("span",{className:"font-medium text-slate-900",children:e.connected})]}),(0,a.jsxs)("div",{children:["Certificati: ",(0,a.jsx)("span",{className:"font-medium text-slate-900",children:e.certified})]})]}),(0,a.jsx)(c.k,{value:e.efficiency,className:"mt-2 h-2"})]})},t))})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(v.A,{className:"h-5 w-5 text-green-600"}),"Attivit\xe0 Recenti"]}),(0,a.jsx)(i.BT,{children:"Ultime operazioni completate"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"space-y-3",children:[{time:"10:30",action:"Installazione completata",details:"Cavo FG16OM4-24 - Settore A",team:"Alpha"},{time:"10:15",action:"Collegamento certificato",details:"Cavo MM-OM3-12 - Settore B",team:"Beta"},{time:"09:45",action:"Nuova installazione",details:"Cavo SM-G652D-48 - Settore C",team:"Gamma"},{time:"09:30",action:"Test completato",details:"Cavo FG16OM4-12 - Settore A",team:"Delta"}].map((e,t)=>(0,a.jsxs)("div",{className:"flex items-start gap-3 p-3 bg-slate-50 rounded-lg",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-900",children:e.action}),(0,a.jsx)("span",{className:"text-xs text-slate-500",children:e.time})]}),(0,a.jsx)("p",{className:"text-sm text-slate-600 truncate",children:e.details}),(0,a.jsx)(l.E,{variant:"outline",className:"mt-1 text-xs",children:e.team})]})]},t))})})]})]})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},25541:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28947:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30535:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs_1\\\\src\\\\app\\\\productivity\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_1\\src\\app\\productivity\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},40552:(e,t,s)=>{Promise.resolve().then(s.bind(s,10833))},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>l,Zp:()=>i,aR:()=>n});var a=s(60687);s(43210);var r=s(4780);function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...t})}},45583:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},46657:(e,t,s)=>{"use strict";s.d(t,{k:()=>N});var a=s(60687),r=s(43210),i=s(11273),n=s(14163),l="Progress",[c,d]=(0,i.A)(l),[o,x]=c(l),m=r.forwardRef((e,t)=>{var s,r;let{__scopeProgress:i,value:l=null,max:c,getValueLabel:d=f,...x}=e;(c||0===c)&&!j(c)&&console.error((s=`${c}`,`Invalid prop \`max\` of value \`${s}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let m=j(c)?c:100;null===l||g(l,m)||console.error((r=`${l}`,`Invalid prop \`value\` of value \`${r}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let u=g(l,m)?l:null,p=h(u)?d(u,m):void 0;return(0,a.jsx)(o,{scope:i,value:u,max:m,children:(0,a.jsx)(n.sG.div,{"aria-valuemax":m,"aria-valuemin":0,"aria-valuenow":h(u)?u:void 0,"aria-valuetext":p,role:"progressbar","data-state":v(u,m),"data-value":u??void 0,"data-max":m,...x,ref:t})})});m.displayName=l;var u="ProgressIndicator",p=r.forwardRef((e,t)=>{let{__scopeProgress:s,...r}=e,i=x(u,s);return(0,a.jsx)(n.sG.div,{"data-state":v(i.value,i.max),"data-value":i.value??void 0,"data-max":i.max,...r,ref:t})});function f(e,t){return`${Math.round(e/t*100)}%`}function v(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function h(e){return"number"==typeof e}function j(e){return h(e)&&!isNaN(e)&&e>0}function g(e,t){return h(e)&&!isNaN(e)&&e<=t&&e>=0}p.displayName=u;var b=s(4780);function N({className:e,value:t,...s}){return(0,a.jsx)(m,{"data-slot":"progress",className:(0,b.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...s,children:(0,a.jsx)(p,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},77e3:(e,t,s)=>{Promise.resolve().then(s.bind(s,30535))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88636:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var a=s(65239),r=s(48088),i=s(88170),n=s.n(i),l=s(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(t,c);let d={children:["",{children:["productivity",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,30535)),"C:\\CMS\\webapp-nextjs_1\\src\\app\\productivity\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\CMS\\webapp-nextjs_1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\CMS\\webapp-nextjs_1\\src\\app\\productivity\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/productivity/page",pathname:"/productivity",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94735:e=>{"use strict";e.exports=require("events")},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>c});var a=s(60687);s(43210);var r=s(8730),i=s(24224),n=s(4780);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c({className:e,variant:t,asChild:s=!1,...i}){let c=s?r.DX:"span";return(0,a.jsx)(c,{"data-slot":"badge",className:(0,n.cn)(l({variant:t}),e),...i})}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,6539,1658,4951],()=>s(88636));module.exports=a})();