(()=>{var e={};e.id=1460,e.ids=[1460],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19879:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>d,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>c});var s={};r.r(s),r.d(s,{POST:()=>u});var n=r(96559),a=r(48088),o=r(37719),i=r(32190);async function u(e){try{let{selectedIds:t,cantiereId:r}=await e.json();if(!t||!Array.isArray(t)||0===t.length)return i.NextResponse.json({error:"Nessun cavo selezionato per l'eliminazione"},{status:400});if(!r)return i.NextResponse.json({error:"ID cantiere mancante"},{status:400});let s=t.filter(e=>Math.random()>.8);if(s.length>0)return i.NextResponse.json({success:!1,error:`Impossibile eliminare ${s.length} cavi: sono installati o hanno comande attive`,nonDeletableCables:s,deletedCount:t.length-s.length},{status:400});return i.NextResponse.json({success:!0,message:`${t.length} cavi eliminati con successo`,deletedCount:t.length,deletedIds:t})}catch(e){return i.NextResponse.json({error:"Errore interno del server"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/cavi/bulk-delete/route",pathname:"/api/cavi/bulk-delete",filename:"route",bundlePath:"app/api/cavi/bulk-delete/route"},resolvedPagePath:"C:\\CMS\\webapp-nextjs_1\\src\\app\\api\\cavi\\bulk-delete\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:c,serverHooks:d}=p;function x(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:c})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(19879));module.exports=s})();