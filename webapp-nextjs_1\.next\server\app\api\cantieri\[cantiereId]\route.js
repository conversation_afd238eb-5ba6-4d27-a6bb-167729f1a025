(()=>{var e={};e.id=5512,e.ids=[5512],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53210:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>c,PUT:()=>u});var a=r(96559),n=r(48088),o=r(37719),i=r(32190);async function c(e,{params:t}){try{let r=t.cantiereId,s=e.headers.get("authorization");if(!s||!s.startsWith("Bearer "))return i.NextResponse.json({detail:"Token di autorizzazione mancante"},{status:401});let a="http://localhost:8001";console.log("\uD83D\uDD04 Cantiere API: Proxying request to backend:",`${a}/api/cantieri/${r}`);let n=await fetch(`${a}/api/cantieri/${r}`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:s}});if(console.log("\uD83D\uDCE1 Cantiere API: Backend response status:",n.status),!n.ok){let e=await n.json().catch(()=>({detail:"Errore sconosciuto"}));return console.error("❌ Cantiere API: Backend error:",e),i.NextResponse.json(e,{status:n.status})}let o=await n.json();return console.log("\uD83D\uDCE1 Cantiere API: Backend response data:",o),i.NextResponse.json(o,{status:n.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("❌ Cantiere API: Error:",e),i.NextResponse.json({detail:"Errore interno del server"},{status:500})}}async function u(e,{params:t}){try{let r=t.cantiereId,s=await e.json(),a=e.headers.get("authorization");if(!a||!a.startsWith("Bearer "))return i.NextResponse.json({detail:"Token di autorizzazione mancante"},{status:401});let n="http://localhost:8001";console.log("\uD83D\uDD04 Cantiere API: Proxying PUT request to backend:",`${n}/api/cantieri/${r}`);let o=await fetch(`${n}/api/cantieri/${r}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:a},body:JSON.stringify(s)});if(console.log("\uD83D\uDCE1 Cantiere API: Backend response status:",o.status),!o.ok){let e=await o.json().catch(()=>({detail:"Errore sconosciuto"}));return console.error("❌ Cantiere API: Backend error:",e),i.NextResponse.json(e,{status:o.status})}let c=await o.json();return console.log("\uD83D\uDCE1 Cantiere API: Backend response data:",c),i.NextResponse.json(c,{status:o.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("❌ Cantiere API: PUT Error:",e),i.NextResponse.json({detail:"Errore interno del server"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/cantieri/[cantiereId]/route",pathname:"/api/cantieri/[cantiereId]",filename:"route",bundlePath:"app/api/cantieri/[cantiereId]/route"},resolvedPagePath:"C:\\CMS\\webapp-nextjs_1\\src\\app\\api\\cantieri\\[cantiereId]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:h}=p;function x(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(53210));module.exports=s})();