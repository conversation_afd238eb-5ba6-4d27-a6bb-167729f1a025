{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/lib/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'\n\n// Configurazione base per l'API\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'\n\n// Crea istanza axios con configurazione base\nconst apiClient: AxiosInstance = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n})\n\n// Interceptor per aggiungere il token di autenticazione\napiClient.interceptors.request.use(\n  (config) => {\n    // Verifica se siamo nel browser prima di accedere a localStorage\n    if (typeof window !== 'undefined') {\n      const token = localStorage.getItem('token')\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`\n      }\n    }\n    return config\n  },\n  (error) => {\n    return Promise.reject(error)\n  }\n)\n\n// Interceptor per gestire le risposte e gli errori\napiClient.interceptors.response.use(\n  (response: AxiosResponse) => {\n    return response\n  },\n  (error) => {\n    if (error.response?.status === 401 && typeof window !== 'undefined') {\n      // Token scaduto o non valido\n      localStorage.removeItem('token')\n      localStorage.removeItem('access_token')\n      localStorage.removeItem('user_data')\n      localStorage.removeItem('cantiere_data')\n      window.location.href = '/login'\n    }\n    return Promise.reject(error)\n  }\n)\n\n// Tipi per le risposte API\nexport interface ApiResponse<T = any> {\n  data: T\n  message?: string\n  status: number\n}\n\nexport interface PaginatedResponse<T> {\n  items: T[]\n  total: number\n  page: number\n  size: number\n  pages: number\n}\n\n// Funzioni helper per le chiamate API\nexport const api = {\n  // GET request\n  get: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await apiClient.get<T>(url, config)\n    return response.data\n  },\n\n  // POST request\n  post: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await apiClient.post<T>(url, data, config)\n    return response.data\n  },\n\n  // PUT request\n  put: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await apiClient.put<T>(url, data, config)\n    return response.data\n  },\n\n  // PATCH request\n  patch: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await apiClient.patch<T>(url, data, config)\n    return response.data\n  },\n\n  // DELETE request\n  delete: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await apiClient.delete<T>(url, config)\n    return response.data\n  },\n}\n\n// Servizi API specifici per CABLYS\nexport const authApi = {\n  // Login utente - usa FormData per OAuth2PasswordRequestForm\n  login: async (credentials: { username: string; password: string }) => {\n    const formData = new FormData()\n    formData.append('username', credentials.username)\n    formData.append('password', credentials.password)\n\n    const response = await apiClient.post('/api/auth/login', formData, {\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded',\n      },\n    })\n    return response.data\n  },\n\n  // Login cantiere - usa JSON per CantiereLogin\n  loginCantiere: async (credentials: { codice_cantiere: string; password_cantiere: string }) => {\n    const response = await apiClient.post('/api/auth/login/cantiere', {\n      codice_univoco: credentials.codice_cantiere,\n      password: credentials.password_cantiere\n    })\n    return response.data\n  },\n\n  // Verifica token\n  verifyToken: async () => {\n    const response = await apiClient.post('/api/auth/test-token')\n    return response.data\n  },\n\n  // Logout\n  logout: () => {\n    localStorage.removeItem('access_token')\n    localStorage.removeItem('user_data')\n    window.location.href = '/login'\n  }\n}\n\nexport const caviApi = {\n  // Ottieni tutti i cavi\n  getCavi: (cantiereId: number, params?: {\n    tipo_cavo?: number,\n    stato_installazione?: string,\n    tipologia?: string,\n    sort_by?: string,\n    sort_order?: string\n  }) =>\n    api.get<any[]>(`/api/cavi/${cantiereId}`, { params }),\n\n  // Ottieni cavo specifico\n  getCavo: (cantiereId: number, idCavo: string) =>\n    api.get<any>(`/api/cavi/${cantiereId}/${idCavo}`),\n\n  // Verifica se cavo esiste\n  checkCavo: (cantiereId: number, idCavo: string) =>\n    api.get<any>(`/api/cavi/${cantiereId}/check/${idCavo}`),\n\n  // Crea nuovo cavo\n  createCavo: (cantiereId: number, cavo: any) =>\n    api.post<any>(`/api/cavi/${cantiereId}`, cavo),\n\n  // Aggiorna cavo\n  updateCavo: (cantiereId: number, idCavo: string, updates: any) =>\n    api.put<any>(`/api/cavi/${cantiereId}/${idCavo}`, updates),\n\n  // Elimina cavo\n  deleteCavo: (cantiereId: number, idCavo: string, options?: any) =>\n    api.delete(`/api/cavi/${cantiereId}/${idCavo}`, { data: options }),\n\n  // Aggiorna metri posati\n  updateMetriPosati: (cantiereId: number, idCavo: string, metri: number, idBobina?: string, forceOver?: boolean) =>\n    api.post<any>(`/api/cavi/${cantiereId}/${idCavo}/metri-posati`, {\n      metri_posati: metri,\n      id_bobina: idBobina,\n      force_over: forceOver || false\n    }),\n\n  // Aggiorna bobina\n  updateBobina: (cantiereId: number, idCavo: string, idBobina: string, forceOver?: boolean) =>\n    api.post<any>(`/api/cavi/${cantiereId}/${idCavo}/bobina`, {\n      id_bobina: idBobina,\n      force_over: forceOver || false\n    }),\n\n  // Annulla installazione (resetta completamente il cavo)\n  cancelInstallation: (cantiereId: number, idCavo: string) =>\n    api.post<any>(`/api/cavi/${cantiereId}/${idCavo}/cancel-installation`),\n\n  // Gestione collegamenti\n  collegaCavo: (cantiereId: number, idCavo: string, lato: 'partenza' | 'arrivo', responsabile?: string) =>\n    api.post<any>(`/api/cavi/${cantiereId}/${idCavo}/collegamento`, {\n      lato,\n      responsabile\n    }),\n\n  // Scollega cavo\n  scollegaCavo: (cantiereId: number, idCavo: string, lato?: 'partenza' | 'arrivo') =>\n    api.delete(`/api/cavi/${cantiereId}/${idCavo}/collegamento`, {\n      data: { lato }\n    }),\n\n  // Marca come spare\n  markAsSpare: (cantiereId: number, idCavo: string, spare: boolean) =>\n    api.put<any>(`/api/cavi/${cantiereId}/${idCavo}/spare`, {\n      spare: spare ? 1 : 0\n    }),\n\n  // Debug endpoints\n  debugCavi: (cantiereId: number) =>\n    api.get<any>(`/api/cavi/debug/${cantiereId}`),\n\n  debugCaviRaw: (cantiereId: number) =>\n    api.get<any>(`/api/cavi/debug/raw/${cantiereId}`),\n}\n\nexport const parcoCaviApi = {\n  // Ottieni tutte le bobine\n  getBobine: (cantiereId: number, params?: {\n    filtro?: string,\n    tipologia?: string,\n    sezione?: string,    // sezione nel DB = formazione sistema\n    disponibili_only?: boolean\n  }) =>\n    api.get<any[]>(`/api/parco-cavi/${cantiereId}`, { params }),\n\n  // Ottieni bobina specifica\n  getBobina: (cantiereId: number, idBobina: string) =>\n    api.get<any>(`/api/parco-cavi/${cantiereId}/${idBobina}`),\n\n  // Ottieni bobine compatibili\n  getBobineCompatibili: (cantiereId: number, params: {\n    tipologia?: string,\n    n_conduttori?: string,\n    sezione?: string     // sezione nel DB = formazione sistema\n  }) =>\n    api.get<any[]>(`/api/parco-cavi/${cantiereId}/compatibili`, { params }),\n\n  // Crea nuova bobina\n  createBobina: (cantiereId: number, bobina: any) =>\n    api.post<any>(`/api/parco-cavi/${cantiereId}`, bobina),\n\n  // Aggiorna bobina esistente\n  updateBobina: (cantiereId: number, bobinaNumero: string, bobina: any) =>\n    api.put<any>(`/api/parco-cavi/${cantiereId}/${bobinaNumero}`, bobina),\n\n  // Elimina bobina\n  deleteBobina: (cantiereId: number, bobinaNumero: string) =>\n    api.delete<any>(`/api/parco-cavi/${cantiereId}/${bobinaNumero}`),\n\n  // Verifica se è il primo inserimento bobina\n  isFirstBobinaInsertion: (cantiereId: number) =>\n    api.get<{is_first_insertion: boolean, configurazione: string}>(`/api/parco-cavi/${cantiereId}/is-first-insertion`),\n\n  // Aggiorna bobina\n  updateBobina: (cantiereId: number, idBobina: string, updates: any) =>\n    api.put<any>(`/api/parco-cavi/${cantiereId}/${idBobina}`, updates),\n\n  // Elimina bobina\n  deleteBobina: (cantiereId: number, idBobina: string) =>\n    api.delete(`/api/parco-cavi/${cantiereId}/${idBobina}`),\n\n  // Verifica disponibilità metri\n  checkDisponibilita: (cantiereId: number, idBobina: string, metriRichiesti: number) =>\n    api.get<any>(`/api/parco-cavi/${cantiereId}/${idBobina}/disponibilita`, {\n      params: { metri_richiesti: metriRichiesti }\n    }),\n}\n\nexport const comandeApi = {\n  // Ottieni tutte le comande\n  getComande: (cantiereId: number) =>\n    api.get<any[]>(`/api/comande/cantiere/${cantiereId}`),\n\n  // Ottieni comanda specifica\n  getComanda: (cantiereId: number, codiceComanda: string) =>\n    api.get<any>(`/api/comande/${codiceComanda}`),\n\n  // Ottieni cavi di una comanda\n  getCaviComanda: (codiceComanda: string) =>\n    api.get<any>(`/api/comande/${codiceComanda}/cavi`),\n\n  // Crea nuova comanda\n  createComanda: (cantiereId: number, comanda: any) =>\n    api.post<any>(`/api/comande/cantiere/${cantiereId}`, comanda),\n\n  // Crea comanda con cavi\n  createComandaWithCavi: (cantiereId: number, comanda: any, caviIds: string[]) =>\n    api.post<any>(`/api/comande/cantiere/${cantiereId}/crea-con-cavi`, comanda, {\n      params: { lista_id_cavi: caviIds }\n    }),\n\n  // Aggiorna dati comanda (posa, collegamento, certificazione)\n  updateDatiComanda: (codiceComanda: string, endpoint: string, data: any) =>\n    api.put<any>(`/api/comande/${codiceComanda}/${endpoint}`, data),\n\n  // Aggiorna comanda\n  updateComanda: (cantiereId: number, codiceComanda: string, updates: any) =>\n    api.put<any>(`/api/comande/cantiere/${cantiereId}/${codiceComanda}`, updates),\n\n  // Elimina comanda\n  deleteComanda: (cantiereId: number, codiceComanda: string) =>\n    api.delete(`/api/comande/cantiere/${cantiereId}/${codiceComanda}`),\n\n  // Assegna cavi a comanda\n  assegnaCavi: (cantiereId: number, codiceComanda: string, caviIds: string[]) =>\n    api.post<any>(`/api/comande/cantiere/${cantiereId}/${codiceComanda}/assegna-cavi`, { cavi_ids: caviIds }),\n\n  // Rimuovi cavi da comanda\n  rimuoviCavi: (cantiereId: number, codiceComanda: string, caviIds: string[]) =>\n    api.delete(`/api/comande/cantiere/${cantiereId}/${codiceComanda}/rimuovi-cavi`, {\n      data: { cavi_ids: caviIds }\n    }),\n\n  // Ottieni statistiche comande\n  getStatistiche: (cantiereId: number) =>\n    api.get<any>(`/api/comande/cantiere/${cantiereId}/statistiche`),\n\n  // Cambia stato comanda\n  cambiaStato: (cantiereId: number, codiceComanda: string, nuovoStato: string) =>\n    api.put<any>(`/api/comande/cantiere/${cantiereId}/${codiceComanda}/stato`, {\n      nuovo_stato: nuovoStato\n    }),\n}\n\nexport const responsabiliApi = {\n  // Ottieni tutti i responsabili\n  getResponsabili: (cantiereId: number) =>\n    api.get<any[]>(`/api/responsabili/cantiere/${cantiereId}`),\n\n  // Crea nuovo responsabile\n  createResponsabile: (cantiereId: number, responsabile: any) =>\n    api.post<any>(`/api/responsabili/${cantiereId}`, responsabile),\n\n  // Aggiorna responsabile\n  updateResponsabile: (cantiereId: number, id: number, updates: any) =>\n    api.put<any>(`/api/responsabili/${cantiereId}/${id}`, updates),\n\n  // Elimina responsabile\n  deleteResponsabile: (cantiereId: number, id: number) =>\n    api.delete(`/api/responsabili/${cantiereId}/${id}`),\n}\n\nexport const certificazioniApi = {\n  // Ottieni certificazioni\n  getCertificazioni: (cantiereId: number) =>\n    api.get<any[]>(`/api/cantieri/${cantiereId}/certificazioni`),\n\n  // Crea certificazione\n  createCertificazione: (cantiereId: number, certificazione: any) =>\n    api.post<any>(`/api/cantieri/${cantiereId}/certificazioni`, certificazione),\n\n  // Genera PDF certificato\n  generatePDF: (cantiereId: number, idCavo: string) =>\n    api.get(`/api/cantieri/${cantiereId}/pdf-cei-64-8/${idCavo}`, {\n      responseType: 'blob'\n    }),\n}\n\nexport const excelApi = {\n  // Import cavi da Excel\n  importCavi: (cantiereId: number, file: File, revisione: string) => {\n    const formData = new FormData()\n    formData.append('file', file)\n    formData.append('revisione', revisione)\n    return api.post<any>(`/api/excel/${cantiereId}/import-cavi`, formData, {\n      headers: { 'Content-Type': 'multipart/form-data' }\n    })\n  },\n\n  // Import bobine da Excel\n  importBobine: (cantiereId: number, file: File) => {\n    const formData = new FormData()\n    formData.append('file', file)\n    return api.post<any>(`/api/excel/${cantiereId}/import-parco-bobine`, formData, {\n      headers: { 'Content-Type': 'multipart/form-data' }\n    })\n  },\n\n  // Export cavi\n  exportCavi: (cantiereId: number) =>\n    api.get(`/api/excel/${cantiereId}/export-cavi`, {\n      responseType: 'blob'\n    }),\n\n  // Export bobine\n  exportBobine: (cantiereId: number) =>\n    api.get(`/api/excel/${cantiereId}/export-parco-bobine`, {\n      responseType: 'blob'\n    }),\n}\n\nexport const reportsApi = {\n  // Report avanzamento\n  getReportAvanzamento: (cantiereId: number) =>\n    api.get<any>(`/api/reports/${cantiereId}/avanzamento`),\n\n  // Report BOQ\n  getReportBOQ: (cantiereId: number) =>\n    api.get<any>(`/api/reports/${cantiereId}/boq`),\n\n  // Report utilizzo bobine (storico bobine)\n  getReportUtilizzoBobine: (cantiereId: number) =>\n    api.get<any>(`/api/reports/${cantiereId}/storico-bobine`),\n\n  // Report progress\n  getReportProgress: (cantiereId: number) =>\n    api.get<any>(`/api/reports/${cantiereId}/progress`),\n\n  // Report posa per periodo\n  getReportPosaPeriodo: (cantiereId: number, dataInizio?: string, dataFine?: string) => {\n    const params = new URLSearchParams()\n    if (dataInizio) params.append('data_inizio', dataInizio)\n    if (dataFine) params.append('data_fine', dataFine)\n    const queryString = params.toString()\n    return api.get<any>(`/api/reports/${cantiereId}/posa-periodo${queryString ? `?${queryString}` : ''}`)\n  },\n}\n\nexport const cantieriApi = {\n  // Ottieni tutti i cantieri\n  getCantieri: () =>\n    api.get<any[]>('/api/cantieri'),\n\n  // Ottieni cantiere specifico\n  getCantiere: (id: number) =>\n    api.get<any>(`/api/cantieri/${id}`),\n\n  // Crea nuovo cantiere\n  createCantiere: (cantiere: any) =>\n    api.post<any>('/api/cantieri', cantiere),\n\n  // Aggiorna cantiere\n  updateCantiere: (id: number, updates: any) =>\n    api.put<any>(`/api/cantieri/${id}`, updates),\n\n  // Ottieni statistiche cantiere\n  getCantiereStatistics: (id: number) =>\n    api.get<{\n      cantiere_id: number\n      total_cavi: number\n      installati: number\n      collegati: number\n      certificati: number\n      percentuale_avanzamento: number\n      iap: number\n    }>(`/api/cantieri/${id}/statistics`),\n}\n\nexport const usersApi = {\n  // Ottieni tutti gli utenti (solo admin)\n  getUsers: () =>\n    api.get<any[]>('/api/users'),\n\n  // Ottieni utente specifico\n  getUser: (id: number) =>\n    api.get<any>(`/api/users/${id}`),\n\n  // Crea nuovo utente\n  createUser: (user: any) =>\n    api.post<any>('/api/users', user),\n\n  // Aggiorna utente\n  updateUser: (id: number, updates: any) =>\n    api.put<any>(`/api/users/${id}`, updates),\n\n  // Elimina utente\n  deleteUser: (id: number) =>\n    api.delete(`/api/users/${id}`),\n\n  // Abilita/Disabilita utente\n  toggleUserStatus: (id: number) =>\n    api.get<any>(`/api/users/toggle/${id}`),\n\n  // Verifica utenti scaduti\n  checkExpiredUsers: () =>\n    api.get<any>('/api/users/check-expired'),\n\n  // Impersona utente\n  impersonateUser: (userId: number) =>\n    api.post<any>('/api/auth/impersonate', { user_id: userId }),\n\n  // Ottieni dati database raw\n  getDatabaseData: () =>\n    api.get<any>('/api/users/db-raw'),\n\n  // Reset database\n  resetDatabase: () =>\n    api.post<any>('/api/admin/reset-database'),\n}\n\nexport default apiClient\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGqB;AAHrB;;AAEA,gCAAgC;AAChC,MAAM,eAAe,6DAAmC;AAExD,6CAA6C;AAC7C,MAAM,YAA2B,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC5C,SAAS;IACT,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,wDAAwD;AACxD,UAAU,YAAY,CAAC,OAAO,CAAC,GAAG,CAChC,CAAC;IACC,iEAAiE;IACjE,wCAAmC;QACjC,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,OAAO;YACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;QAClD;IACF;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,mDAAmD;AACnD,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC;IACC,OAAO;AACT,GACA,CAAC;IACC,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,aAAkB,aAAa;QACnE,6BAA6B;QAC7B,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAmBK,MAAM,MAAM;IACjB,cAAc;IACd,KAAK,OAAgB,KAAa;QAChC,MAAM,WAAW,MAAM,UAAU,GAAG,CAAI,KAAK;QAC7C,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,MAAM,OAAgB,KAAa,MAAY;QAC7C,MAAM,WAAW,MAAM,UAAU,IAAI,CAAI,KAAK,MAAM;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,KAAK,OAAgB,KAAa,MAAY;QAC5C,MAAM,WAAW,MAAM,UAAU,GAAG,CAAI,KAAK,MAAM;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,OAAO,OAAgB,KAAa,MAAY;QAC9C,MAAM,WAAW,MAAM,UAAU,KAAK,CAAI,KAAK,MAAM;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,iBAAiB;IACjB,QAAQ,OAAgB,KAAa;QACnC,MAAM,WAAW,MAAM,UAAU,MAAM,CAAI,KAAK;QAChD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,UAAU;IACrB,4DAA4D;IAC5D,OAAO,OAAO;QACZ,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,YAAY,YAAY,QAAQ;QAChD,SAAS,MAAM,CAAC,YAAY,YAAY,QAAQ;QAEhD,MAAM,WAAW,MAAM,UAAU,IAAI,CAAC,mBAAmB,UAAU;YACjE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,8CAA8C;IAC9C,eAAe,OAAO;QACpB,MAAM,WAAW,MAAM,UAAU,IAAI,CAAC,4BAA4B;YAChE,gBAAgB,YAAY,eAAe;YAC3C,UAAU,YAAY,iBAAiB;QACzC;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,iBAAiB;IACjB,aAAa;QACX,MAAM,WAAW,MAAM,UAAU,IAAI,CAAC;QACtC,OAAO,SAAS,IAAI;IACtB;IAEA,SAAS;IACT,QAAQ;QACN,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;AACF;AAEO,MAAM,UAAU;IACrB,uBAAuB;IACvB,SAAS,CAAC,YAAoB,SAO5B,IAAI,GAAG,CAAQ,CAAC,UAAU,EAAE,YAAY,EAAE;YAAE;QAAO;IAErD,yBAAyB;IACzB,SAAS,CAAC,YAAoB,SAC5B,IAAI,GAAG,CAAM,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,QAAQ;IAElD,0BAA0B;IAC1B,WAAW,CAAC,YAAoB,SAC9B,IAAI,GAAG,CAAM,CAAC,UAAU,EAAE,WAAW,OAAO,EAAE,QAAQ;IAExD,kBAAkB;IAClB,YAAY,CAAC,YAAoB,OAC/B,IAAI,IAAI,CAAM,CAAC,UAAU,EAAE,YAAY,EAAE;IAE3C,gBAAgB;IAChB,YAAY,CAAC,YAAoB,QAAgB,UAC/C,IAAI,GAAG,CAAM,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,QAAQ,EAAE;IAEpD,eAAe;IACf,YAAY,CAAC,YAAoB,QAAgB,UAC/C,IAAI,MAAM,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,QAAQ,EAAE;YAAE,MAAM;QAAQ;IAElE,wBAAwB;IACxB,mBAAmB,CAAC,YAAoB,QAAgB,OAAe,UAAmB,YACxF,IAAI,IAAI,CAAM,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,OAAO,aAAa,CAAC,EAAE;YAC9D,cAAc;YACd,WAAW;YACX,YAAY,aAAa;QAC3B;IAEF,kBAAkB;IAClB,cAAc,CAAC,YAAoB,QAAgB,UAAkB,YACnE,IAAI,IAAI,CAAM,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,OAAO,OAAO,CAAC,EAAE;YACxD,WAAW;YACX,YAAY,aAAa;QAC3B;IAEF,wDAAwD;IACxD,oBAAoB,CAAC,YAAoB,SACvC,IAAI,IAAI,CAAM,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,OAAO,oBAAoB,CAAC;IAEvE,wBAAwB;IACxB,aAAa,CAAC,YAAoB,QAAgB,MAA6B,eAC7E,IAAI,IAAI,CAAM,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,OAAO,aAAa,CAAC,EAAE;YAC9D;YACA;QACF;IAEF,gBAAgB;IAChB,cAAc,CAAC,YAAoB,QAAgB,OACjD,IAAI,MAAM,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,OAAO,aAAa,CAAC,EAAE;YAC3D,MAAM;gBAAE;YAAK;QACf;IAEF,mBAAmB;IACnB,aAAa,CAAC,YAAoB,QAAgB,QAChD,IAAI,GAAG,CAAM,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,OAAO,MAAM,CAAC,EAAE;YACtD,OAAO,QAAQ,IAAI;QACrB;IAEF,kBAAkB;IAClB,WAAW,CAAC,aACV,IAAI,GAAG,CAAM,CAAC,gBAAgB,EAAE,YAAY;IAE9C,cAAc,CAAC,aACb,IAAI,GAAG,CAAM,CAAC,oBAAoB,EAAE,YAAY;AACpD;AAEO,MAAM,eAAe;IAC1B,0BAA0B;IAC1B,WAAW,CAAC,YAAoB,SAM9B,IAAI,GAAG,CAAQ,CAAC,gBAAgB,EAAE,YAAY,EAAE;YAAE;QAAO;IAE3D,2BAA2B;IAC3B,WAAW,CAAC,YAAoB,WAC9B,IAAI,GAAG,CAAM,CAAC,gBAAgB,EAAE,WAAW,CAAC,EAAE,UAAU;IAE1D,6BAA6B;IAC7B,sBAAsB,CAAC,YAAoB,SAKzC,IAAI,GAAG,CAAQ,CAAC,gBAAgB,EAAE,WAAW,YAAY,CAAC,EAAE;YAAE;QAAO;IAEvE,oBAAoB;IACpB,cAAc,CAAC,YAAoB,SACjC,IAAI,IAAI,CAAM,CAAC,gBAAgB,EAAE,YAAY,EAAE;IAEjD,4BAA4B;IAC5B,cAAc,CAAC,YAAoB,cAAsB,SACvD,IAAI,GAAG,CAAM,CAAC,gBAAgB,EAAE,WAAW,CAAC,EAAE,cAAc,EAAE;IAEhE,iBAAiB;IACjB,cAAc,CAAC,YAAoB,eACjC,IAAI,MAAM,CAAM,CAAC,gBAAgB,EAAE,WAAW,CAAC,EAAE,cAAc;IAEjE,4CAA4C;IAC5C,wBAAwB,CAAC,aACvB,IAAI,GAAG,CAAwD,CAAC,gBAAgB,EAAE,WAAW,mBAAmB,CAAC;IAEnH,kBAAkB;IAClB,cAAc,CAAC,YAAoB,UAAkB,UACnD,IAAI,GAAG,CAAM,CAAC,gBAAgB,EAAE,WAAW,CAAC,EAAE,UAAU,EAAE;IAE5D,iBAAiB;IACjB,cAAc,CAAC,YAAoB,WACjC,IAAI,MAAM,CAAC,CAAC,gBAAgB,EAAE,WAAW,CAAC,EAAE,UAAU;IAExD,+BAA+B;IAC/B,oBAAoB,CAAC,YAAoB,UAAkB,iBACzD,IAAI,GAAG,CAAM,CAAC,gBAAgB,EAAE,WAAW,CAAC,EAAE,SAAS,cAAc,CAAC,EAAE;YACtE,QAAQ;gBAAE,iBAAiB;YAAe;QAC5C;AACJ;AAEO,MAAM,aAAa;IACxB,2BAA2B;IAC3B,YAAY,CAAC,aACX,IAAI,GAAG,CAAQ,CAAC,sBAAsB,EAAE,YAAY;IAEtD,4BAA4B;IAC5B,YAAY,CAAC,YAAoB,gBAC/B,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,eAAe;IAE9C,8BAA8B;IAC9B,gBAAgB,CAAC,gBACf,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,cAAc,KAAK,CAAC;IAEnD,qBAAqB;IACrB,eAAe,CAAC,YAAoB,UAClC,IAAI,IAAI,CAAM,CAAC,sBAAsB,EAAE,YAAY,EAAE;IAEvD,wBAAwB;IACxB,uBAAuB,CAAC,YAAoB,SAAc,UACxD,IAAI,IAAI,CAAM,CAAC,sBAAsB,EAAE,WAAW,cAAc,CAAC,EAAE,SAAS;YAC1E,QAAQ;gBAAE,eAAe;YAAQ;QACnC;IAEF,6DAA6D;IAC7D,mBAAmB,CAAC,eAAuB,UAAkB,OAC3D,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,cAAc,CAAC,EAAE,UAAU,EAAE;IAE5D,mBAAmB;IACnB,eAAe,CAAC,YAAoB,eAAuB,UACzD,IAAI,GAAG,CAAM,CAAC,sBAAsB,EAAE,WAAW,CAAC,EAAE,eAAe,EAAE;IAEvE,kBAAkB;IAClB,eAAe,CAAC,YAAoB,gBAClC,IAAI,MAAM,CAAC,CAAC,sBAAsB,EAAE,WAAW,CAAC,EAAE,eAAe;IAEnE,yBAAyB;IACzB,aAAa,CAAC,YAAoB,eAAuB,UACvD,IAAI,IAAI,CAAM,CAAC,sBAAsB,EAAE,WAAW,CAAC,EAAE,cAAc,aAAa,CAAC,EAAE;YAAE,UAAU;QAAQ;IAEzG,0BAA0B;IAC1B,aAAa,CAAC,YAAoB,eAAuB,UACvD,IAAI,MAAM,CAAC,CAAC,sBAAsB,EAAE,WAAW,CAAC,EAAE,cAAc,aAAa,CAAC,EAAE;YAC9E,MAAM;gBAAE,UAAU;YAAQ;QAC5B;IAEF,8BAA8B;IAC9B,gBAAgB,CAAC,aACf,IAAI,GAAG,CAAM,CAAC,sBAAsB,EAAE,WAAW,YAAY,CAAC;IAEhE,uBAAuB;IACvB,aAAa,CAAC,YAAoB,eAAuB,aACvD,IAAI,GAAG,CAAM,CAAC,sBAAsB,EAAE,WAAW,CAAC,EAAE,cAAc,MAAM,CAAC,EAAE;YACzE,aAAa;QACf;AACJ;AAEO,MAAM,kBAAkB;IAC7B,+BAA+B;IAC/B,iBAAiB,CAAC,aAChB,IAAI,GAAG,CAAQ,CAAC,2BAA2B,EAAE,YAAY;IAE3D,0BAA0B;IAC1B,oBAAoB,CAAC,YAAoB,eACvC,IAAI,IAAI,CAAM,CAAC,kBAAkB,EAAE,YAAY,EAAE;IAEnD,wBAAwB;IACxB,oBAAoB,CAAC,YAAoB,IAAY,UACnD,IAAI,GAAG,CAAM,CAAC,kBAAkB,EAAE,WAAW,CAAC,EAAE,IAAI,EAAE;IAExD,uBAAuB;IACvB,oBAAoB,CAAC,YAAoB,KACvC,IAAI,MAAM,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,EAAE,IAAI;AACtD;AAEO,MAAM,oBAAoB;IAC/B,yBAAyB;IACzB,mBAAmB,CAAC,aAClB,IAAI,GAAG,CAAQ,CAAC,cAAc,EAAE,WAAW,eAAe,CAAC;IAE7D,sBAAsB;IACtB,sBAAsB,CAAC,YAAoB,iBACzC,IAAI,IAAI,CAAM,CAAC,cAAc,EAAE,WAAW,eAAe,CAAC,EAAE;IAE9D,yBAAyB;IACzB,aAAa,CAAC,YAAoB,SAChC,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,WAAW,cAAc,EAAE,QAAQ,EAAE;YAC5D,cAAc;QAChB;AACJ;AAEO,MAAM,WAAW;IACtB,uBAAuB;IACvB,YAAY,CAAC,YAAoB,MAAY;QAC3C,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,SAAS,MAAM,CAAC,aAAa;QAC7B,OAAO,IAAI,IAAI,CAAM,CAAC,WAAW,EAAE,WAAW,YAAY,CAAC,EAAE,UAAU;YACrE,SAAS;gBAAE,gBAAgB;YAAsB;QACnD;IACF;IAEA,yBAAyB;IACzB,cAAc,CAAC,YAAoB;QACjC,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,OAAO,IAAI,IAAI,CAAM,CAAC,WAAW,EAAE,WAAW,oBAAoB,CAAC,EAAE,UAAU;YAC7E,SAAS;gBAAE,gBAAgB;YAAsB;QACnD;IACF;IAEA,cAAc;IACd,YAAY,CAAC,aACX,IAAI,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW,YAAY,CAAC,EAAE;YAC9C,cAAc;QAChB;IAEF,gBAAgB;IAChB,cAAc,CAAC,aACb,IAAI,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW,oBAAoB,CAAC,EAAE;YACtD,cAAc;QAChB;AACJ;AAEO,MAAM,aAAa;IACxB,qBAAqB;IACrB,sBAAsB,CAAC,aACrB,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,WAAW,YAAY,CAAC;IAEvD,aAAa;IACb,cAAc,CAAC,aACb,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,WAAW,IAAI,CAAC;IAE/C,0CAA0C;IAC1C,yBAAyB,CAAC,aACxB,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,WAAW,eAAe,CAAC;IAE1D,kBAAkB;IAClB,mBAAmB,CAAC,aAClB,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,WAAW,SAAS,CAAC;IAEpD,0BAA0B;IAC1B,sBAAsB,CAAC,YAAoB,YAAqB;QAC9D,MAAM,SAAS,IAAI;QACnB,IAAI,YAAY,OAAO,MAAM,CAAC,eAAe;QAC7C,IAAI,UAAU,OAAO,MAAM,CAAC,aAAa;QACzC,MAAM,cAAc,OAAO,QAAQ;QACnC,OAAO,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,WAAW,aAAa,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;IACtG;AACF;AAEO,MAAM,cAAc;IACzB,2BAA2B;IAC3B,aAAa,IACX,IAAI,GAAG,CAAQ;IAEjB,6BAA6B;IAC7B,aAAa,CAAC,KACZ,IAAI,GAAG,CAAM,CAAC,cAAc,EAAE,IAAI;IAEpC,sBAAsB;IACtB,gBAAgB,CAAC,WACf,IAAI,IAAI,CAAM,iBAAiB;IAEjC,oBAAoB;IACpB,gBAAgB,CAAC,IAAY,UAC3B,IAAI,GAAG,CAAM,CAAC,cAAc,EAAE,IAAI,EAAE;IAEtC,+BAA+B;IAC/B,uBAAuB,CAAC,KACtB,IAAI,GAAG,CAQJ,CAAC,cAAc,EAAE,GAAG,WAAW,CAAC;AACvC;AAEO,MAAM,WAAW;IACtB,wCAAwC;IACxC,UAAU,IACR,IAAI,GAAG,CAAQ;IAEjB,2BAA2B;IAC3B,SAAS,CAAC,KACR,IAAI,GAAG,CAAM,CAAC,WAAW,EAAE,IAAI;IAEjC,oBAAoB;IACpB,YAAY,CAAC,OACX,IAAI,IAAI,CAAM,cAAc;IAE9B,kBAAkB;IAClB,YAAY,CAAC,IAAY,UACvB,IAAI,GAAG,CAAM,CAAC,WAAW,EAAE,IAAI,EAAE;IAEnC,iBAAiB;IACjB,YAAY,CAAC,KACX,IAAI,MAAM,CAAC,CAAC,WAAW,EAAE,IAAI;IAE/B,4BAA4B;IAC5B,kBAAkB,CAAC,KACjB,IAAI,GAAG,CAAM,CAAC,kBAAkB,EAAE,IAAI;IAExC,0BAA0B;IAC1B,mBAAmB,IACjB,IAAI,GAAG,CAAM;IAEf,mBAAmB;IACnB,iBAAiB,CAAC,SAChB,IAAI,IAAI,CAAM,yBAAyB;YAAE,SAAS;QAAO;IAE3D,4BAA4B;IAC5B,iBAAiB,IACf,IAAI,GAAG,CAAM;IAEf,iBAAiB;IACjB,eAAe,IACb,IAAI,IAAI,CAAM;AAClB;uCAEe", "debugId": null}}, {"offset": {"line": 435, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'\nimport { User, Cantier<PERSON> } from '@/types'\nimport { authApi, usersApi } from '@/lib/api'\n\n// Interfacce per i risultati del login\ninterface LoginResult {\n  success: boolean\n  error?: string\n  user?: User\n}\n\ninterface LoginCantiereResult {\n  success: boolean\n  error?: string\n  cantiere?: Cantiere\n}\n\ninterface AuthContextType {\n  user: User | null\n  cantiere: Cantiere | null\n  isAuthenticated: boolean\n  isLoading: boolean\n  isImpersonating: boolean\n  impersonatedUser: any | null\n  expirationWarning: string | null\n  daysUntilExpiration: number | null\n  expirationDate: string | null\n  login: (username: string, password: string) => Promise<LoginResult>\n  loginCantiere: (codice_cantiere: string, password_cantiere: string) => Promise<LoginCantiereResult>\n  logout: () => void\n  checkAuth: () => Promise<void>\n  impersonateUser: (userId: number) => Promise<any>\n  selectCantiere: (cantiere: Cantiere) => void\n  clearCantiere: () => void\n  dismissExpirationWarning: () => void\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\ninterface AuthProviderProps {\n  children: ReactNode\n}\n\nexport function AuthProvider({ children }: AuthProviderProps) {\n  const [user, setUser] = useState<User | null>(null)\n  const [cantiere, setCantiere] = useState<Cantiere | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n  const [isImpersonating, setIsImpersonating] = useState(() => {\n    if (typeof window !== 'undefined') {\n      return localStorage.getItem('isImpersonating') === 'true'\n    }\n    return false\n  })\n  const [impersonatedUser, setImpersonatedUser] = useState<any | null>(() => {\n    if (typeof window !== 'undefined') {\n      const stored = localStorage.getItem('impersonatedUser')\n      return stored ? JSON.parse(stored) : null\n    }\n    return null\n  })\n\n  // Stati per warning di scadenza\n  const [expirationWarning, setExpirationWarning] = useState<string | null>(null)\n  const [daysUntilExpiration, setDaysUntilExpiration] = useState<number | null>(null)\n  const [expirationDate, setExpirationDate] = useState<string | null>(null)\n\n  // Definizione più rigorosa di isAuthenticated\n  const isAuthenticated = (!!user && user.id_utente) || (!!cantiere && cantiere.id_cantiere)\n\n  // Verifica l'autenticazione al caricamento\n  useEffect(() => {\n    checkAuth()\n  }, [])\n\n  // Carica il cantiere selezionato dal localStorage all'avvio (come nella webapp originale)\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      const cantiereId = localStorage.getItem('selectedCantiereId')\n      const cantiereName = localStorage.getItem('selectedCantiereName')\n\n      if (cantiereId && !cantiere) {\n        const cantiereData = {\n          id_cantiere: parseInt(cantiereId, 10),\n          commessa: cantiereName || `Cantiere ${cantiereId}`,\n          codice_univoco: '',\n          id_utente: user?.id_utente || 0\n        }\n        setCantiere(cantiereData)\n      }\n    }\n  }, [user, cantiere])\n\n  const checkAuth = async () => {\n    try {\n      // Verifica se siamo nel browser\n      if (typeof window === 'undefined') {\n        setIsLoading(false)\n        return\n      }\n\n      // Prima di tutto, imposta loading a true\n      setIsLoading(true)\n\n      // Pulisci eventuali token non validi o scaduti\n      const token = localStorage.getItem('token')\n\n      if (token) {\n        try {\n          // Verifica la validità del token\n          const userData = await authApi.verifyToken()\n\n          // Imposta i dati dell'utente come nel sistema React originale\n          const userInfo = {\n            id_utente: userData.user_id,\n            username: userData.username,\n            ruolo: userData.role\n          }\n          setUser(userInfo)\n\n          // Gestisci warning di scadenza se presenti\n          if (userData.expiration_warning) {\n            console.log('⚠️ AuthContext: Warning scadenza ricevuto:', userData.expiration_warning)\n            setExpirationWarning(userData.expiration_warning)\n            setDaysUntilExpiration(userData.days_until_expiration)\n            setExpirationDate(userData.expiration_date)\n          } else {\n            // Pulisci warning precedenti\n            setExpirationWarning(null)\n            setDaysUntilExpiration(null)\n            setExpirationDate(null)\n          }\n\n          // Gestisci l'impersonificazione\n          const impersonatingState = userData.is_impersonated === true\n          setIsImpersonating(impersonatingState)\n\n          if (impersonatingState && userData.impersonated_id) {\n            const impersonatedUserData = {\n              id: userData.impersonated_id,\n              username: userData.impersonated_username,\n              role: userData.impersonated_role\n            }\n            setImpersonatedUser(impersonatedUserData)\n            if (typeof window !== 'undefined') {\n              localStorage.setItem('impersonatedUser', JSON.stringify(impersonatedUserData))\n              localStorage.setItem('isImpersonating', 'true')\n            }\n          } else {\n            setImpersonatedUser(null)\n            if (typeof window !== 'undefined') {\n              localStorage.removeItem('impersonatedUser')\n              localStorage.removeItem('isImpersonating')\n            }\n          }\n\n          // Se è un utente cantiere, gestisci i dati del cantiere\n          if (userData.role === 'cantieri_user' && userData.cantiere_id) {\n            const cantiereData = {\n              id_cantiere: userData.cantiere_id,\n              commessa: userData.cantiere_name || `Cantiere ${userData.cantiere_id}`,\n              codice_univoco: '',\n              id_utente: userData.user_id\n            }\n            setCantiere(cantiereData)\n          } else {\n            // Prova a caricare i dati del cantiere dal localStorage\n            const cantiereDataString = localStorage.getItem('cantiere_data')\n            if (cantiereDataString) {\n              try {\n                const cantiereData = JSON.parse(cantiereDataString)\n                setCantiere(cantiereData)\n              } catch (error) {\n                localStorage.removeItem('cantiere_data')\n              }\n            }\n          }\n        } catch (tokenError) {\n          // Se il token non è valido, rimuovilo\n          localStorage.removeItem('token')\n          localStorage.removeItem('access_token')\n          localStorage.removeItem('user_data')\n          localStorage.removeItem('cantiere_data')\n          setUser(null)\n          setCantiere(null)\n        }\n      } else {\n        setUser(null)\n        setCantiere(null)\n      }\n    } catch (error) {\n      // In caso di errore generale, assicurati che l'utente non sia autenticato\n      if (typeof window !== 'undefined') {\n        localStorage.removeItem('token')\n        localStorage.removeItem('access_token')\n        localStorage.removeItem('user_data')\n        localStorage.removeItem('cantiere_data')\n      }\n      setUser(null)\n      setCantiere(null)\n    } finally {\n      // Assicurati che loading sia impostato a false alla fine\n      setTimeout(() => {\n        setIsLoading(false)\n      }, 500) // Aggiungi un piccolo ritardo come nel sistema React originale\n    }\n  }\n\n  const login = async (username: string, password: string): Promise<LoginResult> => {\n    try {\n      setIsLoading(true)\n      const response = await authApi.login({ username, password })\n\n      if (typeof window !== 'undefined') {\n        // Salva il token come nel sistema React originale\n        localStorage.setItem('token', response.access_token)\n\n        // Il backend restituisce i dati dell'utente direttamente nella risposta\n        const userData = {\n          id_utente: response.user_id,\n          username: response.username,\n          ruolo: response.role\n        }\n\n        setUser(userData)\n        setCantiere(null)\n\n        // Salva i dati utente per persistenza\n        localStorage.setItem('user_data', JSON.stringify(userData))\n\n        // Gestisci warning di scadenza se presenti\n        if (response.expiration_warning) {\n          console.log('⚠️ AuthContext: Warning scadenza ricevuto:', response.expiration_warning)\n          setExpirationWarning(response.expiration_warning)\n          setDaysUntilExpiration(response.days_until_expiration)\n          setExpirationDate(response.expiration_date)\n        } else {\n          // Pulisci warning precedenti\n          setExpirationWarning(null)\n          setDaysUntilExpiration(null)\n          setExpirationDate(null)\n        }\n\n        return {\n          success: true,\n          user: userData\n        }\n      }\n\n      return {\n        success: false,\n        error: 'Errore durante il salvataggio dei dati'\n      }\n    } catch (error: any) {\n      console.error('❌ AuthContext: Errore durante login:', error)\n\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante il login'\n\n      return {\n        success: false,\n        error: errorMessage\n      }\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const loginCantiere = async (codice_cantiere: string, password_cantiere: string): Promise<LoginCantiereResult> => {\n    try {\n      setIsLoading(true)\n      const response = await authApi.loginCantiere({ codice_cantiere, password_cantiere })\n\n      if (typeof window !== 'undefined') {\n        // Salva il token come nel sistema React originale\n        localStorage.setItem('token', response.access_token)\n\n        // Il backend restituisce i dati del cantiere direttamente nella risposta\n        const cantiereData = {\n          id_cantiere: response.cantiere_id,\n          commessa: response.cantiere_name,\n          codice_univoco: codice_cantiere,\n          id_utente: response.user_id\n        }\n\n        // Salva i dati del cantiere nel localStorage\n        localStorage.setItem('cantiere_data', JSON.stringify(cantiereData))\n\n        setCantiere(cantiereData)\n        setUser(null)\n\n        return {\n          success: true,\n          cantiere: cantiereData\n        }\n      }\n\n      return {\n        success: false,\n        error: 'Errore durante il salvataggio dei dati del cantiere'\n      }\n    } catch (error: any) {\n      console.error('❌ AuthContext: Errore durante login cantiere:', error)\n\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante il login del cantiere'\n\n      return {\n        success: false,\n        error: errorMessage\n      }\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const impersonateUser = async (userId: number) => {\n    try {\n      // Chiama l'endpoint di impersonificazione\n      const response = await usersApi.impersonateUser(userId)\n\n      if (typeof window !== 'undefined') {\n        // Salva il token nel localStorage\n        localStorage.setItem('token', response.access_token)\n\n        // Salva i dati dell'utente impersonato\n        const impersonatedUserData = {\n          id: response.impersonated_id,\n          username: response.impersonated_username,\n          role: response.impersonated_role\n        }\n\n        // Salva i dati dell'utente impersonato nel localStorage\n        localStorage.setItem('impersonatedUser', JSON.stringify(impersonatedUserData))\n        setImpersonatedUser(impersonatedUserData)\n\n        // Imposta lo stato di impersonificazione a true\n        setIsImpersonating(true)\n        localStorage.setItem('isImpersonating', 'true')\n\n        return { impersonatedUser: impersonatedUserData }\n      }\n    } catch (error) {\n      throw error\n    }\n  }\n\n  // Funzione per selezionare un cantiere (come nella webapp originale)\n  const selectCantiere = (cantiere: Cantiere) => {\n    if (cantiere && cantiere.id_cantiere) {\n      // Usa commessa come nome del cantiere, con fallback su nome se commessa non è disponibile\n      const cantiereName = cantiere.commessa || `Cantiere ${cantiere.id_cantiere}`\n\n      // Salva nel localStorage\n      localStorage.setItem('selectedCantiereId', cantiere.id_cantiere.toString())\n      localStorage.setItem('selectedCantiereName', cantiereName)\n\n      // Aggiorna lo stato\n      setCantiere({\n        ...cantiere,\n        commessa: cantiereName\n      })\n    }\n  }\n\n  const logout = () => {\n    if (typeof window !== 'undefined') {\n      // Logout sempre completo - rimuovi tutto\n      localStorage.clear() // Pulisce tutto il localStorage\n      sessionStorage.clear() // Pulisce anche sessionStorage\n\n      // Reset stati\n      setUser(null)\n      setCantiere(null)\n      setIsImpersonating(false)\n      setImpersonatedUser(null)\n      setExpirationWarning(null)\n      setDaysUntilExpiration(null)\n      setExpirationDate(null)\n\n      // Forza reload completo della pagina per evitare cache\n      window.location.replace('/login')\n    }\n  }\n\n  const clearCantiere = () => {\n    setCantiere(null)\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('selectedCantiereId')\n      localStorage.removeItem('selectedCantiereName')\n      localStorage.removeItem('cantiere_data')\n    }\n  }\n\n  const dismissExpirationWarning = () => {\n    setExpirationWarning(null)\n    setDaysUntilExpiration(null)\n    setExpirationDate(null)\n  }\n\n  const value: AuthContextType = {\n    user,\n    cantiere,\n    isAuthenticated,\n    isLoading,\n    isImpersonating,\n    impersonatedUser,\n    expirationWarning,\n    daysUntilExpiration,\n    expirationDate,\n    login,\n    loginCantiere,\n    logout,\n    checkAuth,\n    impersonateUser,\n    selectCantiere,\n    clearCantiere,\n    dismissExpirationWarning,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAuCA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANgB;AAYT,SAAS,aAAa,EAAE,QAAQ,EAAqB;;IAC1D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;iCAAE;YACrD,wCAAmC;gBACjC,OAAO,aAAa,OAAO,CAAC,uBAAuB;YACrD;;QAEF;;IACA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;iCAAc;YACnE,wCAAmC;gBACjC,MAAM,SAAS,aAAa,OAAO,CAAC;gBACpC,OAAO,SAAS,KAAK,KAAK,CAAC,UAAU;YACvC;;QAEF;;IAEA,gCAAgC;IAChC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,8CAA8C;IAC9C,MAAM,kBAAkB,AAAC,CAAC,CAAC,QAAQ,KAAK,SAAS,IAAM,CAAC,CAAC,YAAY,SAAS,WAAW;IAEzF,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG,EAAE;IAEL,0FAA0F;IAC1F,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,wCAAmC;gBACjC,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,MAAM,eAAe,aAAa,OAAO,CAAC;gBAE1C,IAAI,cAAc,CAAC,UAAU;oBAC3B,MAAM,eAAe;wBACnB,aAAa,SAAS,YAAY;wBAClC,UAAU,gBAAgB,CAAC,SAAS,EAAE,YAAY;wBAClD,gBAAgB;wBAChB,WAAW,MAAM,aAAa;oBAChC;oBACA,YAAY;gBACd;YACF;QACF;iCAAG;QAAC;QAAM;KAAS;IAEnB,MAAM,YAAY;QAChB,IAAI;YACF,gCAAgC;YAChC,uCAAmC;;YAGnC;YAEA,yCAAyC;YACzC,aAAa;YAEb,+CAA+C;YAC/C,MAAM,QAAQ,aAAa,OAAO,CAAC;YAEnC,IAAI,OAAO;gBACT,IAAI;oBACF,iCAAiC;oBACjC,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,WAAW;oBAE1C,8DAA8D;oBAC9D,MAAM,WAAW;wBACf,WAAW,SAAS,OAAO;wBAC3B,UAAU,SAAS,QAAQ;wBAC3B,OAAO,SAAS,IAAI;oBACtB;oBACA,QAAQ;oBAER,2CAA2C;oBAC3C,IAAI,SAAS,kBAAkB,EAAE;wBAC/B,QAAQ,GAAG,CAAC,8CAA8C,SAAS,kBAAkB;wBACrF,qBAAqB,SAAS,kBAAkB;wBAChD,uBAAuB,SAAS,qBAAqB;wBACrD,kBAAkB,SAAS,eAAe;oBAC5C,OAAO;wBACL,6BAA6B;wBAC7B,qBAAqB;wBACrB,uBAAuB;wBACvB,kBAAkB;oBACpB;oBAEA,gCAAgC;oBAChC,MAAM,qBAAqB,SAAS,eAAe,KAAK;oBACxD,mBAAmB;oBAEnB,IAAI,sBAAsB,SAAS,eAAe,EAAE;wBAClD,MAAM,uBAAuB;4BAC3B,IAAI,SAAS,eAAe;4BAC5B,UAAU,SAAS,qBAAqB;4BACxC,MAAM,SAAS,iBAAiB;wBAClC;wBACA,oBAAoB;wBACpB,wCAAmC;4BACjC,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;4BACxD,aAAa,OAAO,CAAC,mBAAmB;wBAC1C;oBACF,OAAO;wBACL,oBAAoB;wBACpB,wCAAmC;4BACjC,aAAa,UAAU,CAAC;4BACxB,aAAa,UAAU,CAAC;wBAC1B;oBACF;oBAEA,wDAAwD;oBACxD,IAAI,SAAS,IAAI,KAAK,mBAAmB,SAAS,WAAW,EAAE;wBAC7D,MAAM,eAAe;4BACnB,aAAa,SAAS,WAAW;4BACjC,UAAU,SAAS,aAAa,IAAI,CAAC,SAAS,EAAE,SAAS,WAAW,EAAE;4BACtE,gBAAgB;4BAChB,WAAW,SAAS,OAAO;wBAC7B;wBACA,YAAY;oBACd,OAAO;wBACL,wDAAwD;wBACxD,MAAM,qBAAqB,aAAa,OAAO,CAAC;wBAChD,IAAI,oBAAoB;4BACtB,IAAI;gCACF,MAAM,eAAe,KAAK,KAAK,CAAC;gCAChC,YAAY;4BACd,EAAE,OAAO,OAAO;gCACd,aAAa,UAAU,CAAC;4BAC1B;wBACF;oBACF;gBACF,EAAE,OAAO,YAAY;oBACnB,sCAAsC;oBACtC,aAAa,UAAU,CAAC;oBACxB,aAAa,UAAU,CAAC;oBACxB,aAAa,UAAU,CAAC;oBACxB,aAAa,UAAU,CAAC;oBACxB,QAAQ;oBACR,YAAY;gBACd;YACF,OAAO;gBACL,QAAQ;gBACR,YAAY;YACd;QACF,EAAE,OAAO,OAAO;YACd,0EAA0E;YAC1E,wCAAmC;gBACjC,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;YAC1B;YACA,QAAQ;YACR,YAAY;QACd,SAAU;YACR,yDAAyD;YACzD,WAAW;gBACT,aAAa;YACf,GAAG,KAAK,+DAA+D;;QACzE;IACF;IAEA,MAAM,QAAQ,OAAO,UAAkB;QACrC,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,KAAK,CAAC;gBAAE;gBAAU;YAAS;YAE1D,wCAAmC;gBACjC,kDAAkD;gBAClD,aAAa,OAAO,CAAC,SAAS,SAAS,YAAY;gBAEnD,wEAAwE;gBACxE,MAAM,WAAW;oBACf,WAAW,SAAS,OAAO;oBAC3B,UAAU,SAAS,QAAQ;oBAC3B,OAAO,SAAS,IAAI;gBACtB;gBAEA,QAAQ;gBACR,YAAY;gBAEZ,sCAAsC;gBACtC,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;gBAEjD,2CAA2C;gBAC3C,IAAI,SAAS,kBAAkB,EAAE;oBAC/B,QAAQ,GAAG,CAAC,8CAA8C,SAAS,kBAAkB;oBACrF,qBAAqB,SAAS,kBAAkB;oBAChD,uBAAuB,SAAS,qBAAqB;oBACrD,kBAAkB,SAAS,eAAe;gBAC5C,OAAO;oBACL,6BAA6B;oBAC7B,qBAAqB;oBACrB,uBAAuB;oBACvB,kBAAkB;gBACpB;gBAEA,OAAO;oBACL,SAAS;oBACT,MAAM;gBACR;YACF;;QAMF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,wCAAwC;YAEtD,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YAEtE,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,OAAO,iBAAyB;QACpD,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,aAAa,CAAC;gBAAE;gBAAiB;YAAkB;YAElF,wCAAmC;gBACjC,kDAAkD;gBAClD,aAAa,OAAO,CAAC,SAAS,SAAS,YAAY;gBAEnD,yEAAyE;gBACzE,MAAM,eAAe;oBACnB,aAAa,SAAS,WAAW;oBACjC,UAAU,SAAS,aAAa;oBAChC,gBAAgB;oBAChB,WAAW,SAAS,OAAO;gBAC7B;gBAEA,6CAA6C;gBAC7C,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;gBAErD,YAAY;gBACZ,QAAQ;gBAER,OAAO;oBACL,SAAS;oBACT,UAAU;gBACZ;YACF;;QAMF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,iDAAiD;YAE/D,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YAEtE,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,0CAA0C;YAC1C,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,eAAe,CAAC;YAEhD,wCAAmC;gBACjC,kCAAkC;gBAClC,aAAa,OAAO,CAAC,SAAS,SAAS,YAAY;gBAEnD,uCAAuC;gBACvC,MAAM,uBAAuB;oBAC3B,IAAI,SAAS,eAAe;oBAC5B,UAAU,SAAS,qBAAqB;oBACxC,MAAM,SAAS,iBAAiB;gBAClC;gBAEA,wDAAwD;gBACxD,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;gBACxD,oBAAoB;gBAEpB,gDAAgD;gBAChD,mBAAmB;gBACnB,aAAa,OAAO,CAAC,mBAAmB;gBAExC,OAAO;oBAAE,kBAAkB;gBAAqB;YAClD;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,qEAAqE;IACrE,MAAM,iBAAiB,CAAC;QACtB,IAAI,YAAY,SAAS,WAAW,EAAE;YACpC,0FAA0F;YAC1F,MAAM,eAAe,SAAS,QAAQ,IAAI,CAAC,SAAS,EAAE,SAAS,WAAW,EAAE;YAE5E,yBAAyB;YACzB,aAAa,OAAO,CAAC,sBAAsB,SAAS,WAAW,CAAC,QAAQ;YACxE,aAAa,OAAO,CAAC,wBAAwB;YAE7C,oBAAoB;YACpB,YAAY;gBACV,GAAG,QAAQ;gBACX,UAAU;YACZ;QACF;IACF;IAEA,MAAM,SAAS;QACb,wCAAmC;YACjC,yCAAyC;YACzC,aAAa,KAAK,GAAG,gCAAgC;;YACrD,eAAe,KAAK,GAAG,+BAA+B;;YAEtD,cAAc;YACd,QAAQ;YACR,YAAY;YACZ,mBAAmB;YACnB,oBAAoB;YACpB,qBAAqB;YACrB,uBAAuB;YACvB,kBAAkB;YAElB,uDAAuD;YACvD,OAAO,QAAQ,CAAC,OAAO,CAAC;QAC1B;IACF;IAEA,MAAM,gBAAgB;QACpB,YAAY;QACZ,wCAAmC;YACjC,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;QAC1B;IACF;IAEA,MAAM,2BAA2B;QAC/B,qBAAqB;QACrB,uBAAuB;QACvB,kBAAkB;IACpB;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;IA3XgB;KAAA", "debugId": null}}, {"offset": {"line": 820, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst TooltipProvider = TooltipPrimitive.Provider\n\nconst Tooltip = TooltipPrimitive.Root\n\nconst TooltipTrigger = TooltipPrimitive.Trigger\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Content\n    ref={ref}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,kBAAkB,sKAAA,CAAA,WAAyB;AAEjD,MAAM,UAAU,sKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,sKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGpC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,sKAAA,CAAA,UAAwB;QACvB,KAAK;QACL,YAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sYACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG,sKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 863, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/layout/Navbar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport Link from 'next/link'\nimport { usePathname, useRouter } from 'next/navigation'\nimport { Button } from '@/components/ui/button'\nimport { PrimaryButton, QuickButton } from '@/components/ui/animated-button'\nimport { Badge } from '@/components/ui/badge'\nimport { useAuth } from '@/contexts/AuthContext'\nimport {\n  Cable,\n  Home,\n  Activity,\n  BarChart3,\n  Settings,\n  Users,\n  Menu,\n  X,\n  Building2,\n  ClipboardList,\n  FileText,\n  LogOut,\n  Package,\n  ChevronDown,\n  Upload,\n  Download,\n  Loader2,\n  Zap,\n  Sparkles,\n  ArrowRight\n} from 'lucide-react'\nimport {\n  Tooltip,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from '@/components/ui/tooltip'\n\nconst getNavigation = (userRole: string | undefined, isImpersonating: boolean, impersonatedUser: any, cantiereId?: number) => {\n  // Home button - testo personalizzato come nella webapp originale\n  const homeButton = {\n    name: userRole === 'owner' ? \"Menu Admin\" :\n          userRole === 'user' ? \"Lista Cantieri\" :\n          userRole === 'cantieri_user' ? \"Gestione Cavi\" : \"Home\",\n    href: userRole === 'owner' ? '/admin' :\n          userRole === 'user' ? '/cantieri' :\n          userRole === 'cantieri_user' ? '/cavi' : '/',\n    icon: Home\n  }\n\n  if (userRole === 'owner' && !isImpersonating) {\n    // Solo amministratore - solo il pulsante Home che va al pannello admin\n    return [homeButton]\n  }\n\n  if (userRole === 'user' || (isImpersonating && impersonatedUser?.role === 'user')) {\n    // Utente standard - Home + eventualmente cantieri se impersonificato\n    const nav = [homeButton]\n    if (isImpersonating) {\n      nav.push({ name: 'Cantieri', href: '/cantieri', icon: Building2 })\n    }\n\n    // Se un cantiere è selezionato, aggiungi i menu di gestione come nella webapp originale\n    if (cantiereId) {\n      nav.push(\n        { name: 'Visualizza Cavi', href: '/cavi', icon: Cable },\n        { name: 'Parco Cavi', href: '/parco-cavi', icon: Package },\n        { name: 'Gestione Excel', href: '/excel', icon: FileText, hasDropdown: true },\n        { name: 'Report', href: '/reports', icon: BarChart3 },\n        { name: 'Gestione Comande', href: '/comande', icon: ClipboardList },\n        { name: 'Produttività', href: '/productivity', icon: Activity },\n      )\n    }\n\n    return nav\n  }\n\n  if (userRole === 'cantieri_user' || (isImpersonating && impersonatedUser?.role === 'cantieri_user')) {\n    // Utente cantiere - menu completo come nella webapp originale\n    const nav = [homeButton]\n\n    // Se un cantiere è selezionato, aggiungi i menu di gestione\n    if (cantiereId) {\n      // Se non è cantieri_user diretto, aggiungi Visualizza Cavi\n      if (userRole !== 'cantieri_user') {\n        nav.push({ name: 'Visualizza Cavi', href: '/cavi', icon: Cable })\n      }\n\n      nav.push(\n        { name: 'Parco Cavi', href: '/parco-cavi', icon: Package },\n        { name: 'Gestione Excel', href: '/excel', icon: FileText, hasDropdown: true },\n        { name: 'Report', href: '/reports', icon: BarChart3 },\n        { name: 'Gestione Comande', href: '/comande', icon: ClipboardList },\n        { name: 'Produttività', href: '/productivity', icon: Activity },\n      )\n    }\n\n    return nav\n  }\n\n  // Default\n  return [homeButton]\n}\n\nexport function Navbar() {\n  const [isOpen, setIsOpen] = useState(false)\n  const [excelDropdownOpen, setExcelDropdownOpen] = useState(false)\n  const [isNavigating, setIsNavigating] = useState(false)\n  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({})\n  const [hoveredItem, setHoveredItem] = useState<string | null>(null)\n  const dropdownRef = useRef<HTMLDivElement>(null)\n  const pathname = usePathname()\n  const router = useRouter()\n  const { user, cantiere, isAuthenticated, isImpersonating, impersonatedUser, logout } = useAuth()\n\n  // Recupera l'ID del cantiere selezionato dal localStorage o dal context\n  const cantiereId = cantiere?.id_cantiere || (typeof window !== 'undefined' ? parseInt(localStorage.getItem('selectedCantiereId') || '0') : 0)\n  const cantiereName = cantiere?.commessa || (typeof window !== 'undefined' ? localStorage.getItem('selectedCantiereName') : '') || `Cantiere ${cantiereId}`\n\n  const navigation = getNavigation(user?.ruolo, isImpersonating, impersonatedUser, cantiereId)\n\n  // Gestione navigazione con loading states\n  const handleNavigation = async (href: string, itemName: string) => {\n    if (pathname === href) return\n\n    setLoadingStates(prev => ({ ...prev, [itemName]: true }))\n    setIsNavigating(true)\n\n    try {\n      await router.push(href)\n    } catch (error) {\n      console.error('Navigation error:', error)\n    } finally {\n      // Delay per mostrare il feedback visivo\n      setTimeout(() => {\n        setLoadingStates(prev => ({ ...prev, [itemName]: false }))\n        setIsNavigating(false)\n        setIsOpen(false) // Chiudi menu mobile\n      }, 300)\n    }\n  }\n\n  // Gestione logout con loading\n  const handleLogout = async () => {\n    setLoadingStates(prev => ({ ...prev, 'logout': true }))\n    try {\n      await logout()\n    } catch (error) {\n      console.error('Logout error:', error)\n    } finally {\n      setLoadingStates(prev => ({ ...prev, 'logout': false }))\n    }\n  }\n\n  // Chiudi dropdown quando si clicca fuori\n  useEffect(() => {\n    function handleClickOutside(event: MouseEvent) {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setExcelDropdownOpen(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside)\n    }\n  }, [])\n\n  // Reset loading states quando cambia pathname\n  useEffect(() => {\n    setLoadingStates({})\n    setIsNavigating(false)\n  }, [pathname])\n\n  // Non mostrare navbar nella pagina di login\n  if (pathname === '/login') {\n    return null\n  }\n\n  // Se non autenticato, non mostrare navbar\n  if (!isAuthenticated) {\n    return null\n  }\n\n  return (\n    <nav className=\"fixed top-0 left-0 right-0 z-50 bg-white border-b border-slate-200 shadow-sm\">\n      <div className=\"max-w-[90%] mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n\n          {/* Tutto a sinistra: Logo + Navigation */}\n          <div className=\"flex items-center space-x-6\">\n            {/* Logo e Brand con animazioni */}\n            <TooltipProvider>\n              <Tooltip>\n                <TooltipTrigger asChild>\n                  <div\n                    className=\"flex items-center space-x-3 cursor-pointer group transition-all duration-300 hover:scale-105\"\n                    onClick={() => handleNavigation('/', 'Home')}\n                  >\n                    <div className=\"relative w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center group-hover:shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300\">\n                      {loadingStates['Home'] ? (\n                        <Loader2 className=\"w-5 h-5 text-white animate-spin\" />\n                      ) : (\n                        <Cable className=\"w-5 h-5 text-white group-hover:rotate-12 transition-transform duration-300\" />\n                      )}\n                      <div className=\"absolute inset-0 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg opacity-0 group-hover:opacity-20 transition-opacity duration-300\"></div>\n                    </div>\n                    <div className=\"hidden sm:block\">\n                      <h1 className=\"text-xl font-bold text-slate-900 group-hover:text-blue-700 transition-colors duration-300\">\n                        CABLYS\n                        <Sparkles className=\"inline w-4 h-4 ml-1 text-blue-500 opacity-0 group-hover:opacity-100 transition-all duration-300\" />\n                      </h1>\n                      <p className=\"text-xs text-slate-500 -mt-1 group-hover:text-slate-600 transition-colors duration-300\">\n                        Cable Installation System\n                      </p>\n                    </div>\n                  </div>\n                </TooltipTrigger>\n                <TooltipContent>\n                  <p>Torna alla home</p>\n                </TooltipContent>\n              </Tooltip>\n            </TooltipProvider>\n\n            {/* Navigation Desktop - allontanata dal logo */}\n            <div className=\"hidden md:flex items-center space-x-1\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href ||\n                             (item.href !== '/' && pathname.startsWith(item.href))\n              const Icon = item.icon\n\n              // Gestione speciale per il dropdown Excel\n              if (item.hasDropdown && item.name === 'Gestione Excel') {\n                return (\n                  <div key={item.name} className=\"relative\" ref={dropdownRef}>\n                    <TooltipProvider>\n                      <Tooltip>\n                        <TooltipTrigger asChild>\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            className={`group flex items-center space-x-2 px-3 py-2 transition-all duration-300 ease-in-out rounded-lg border border-transparent relative overflow-hidden ${\n                              isActive\n                                ? 'bg-gradient-to-r from-blue-100 to-blue-50 text-blue-700 shadow-sm border-blue-200'\n                                : 'text-slate-600 hover:text-slate-900 hover:bg-gradient-to-r hover:from-blue-50 hover:to-slate-50 hover:border-blue-200 hover:shadow-sm'\n                            }`}\n                            onClick={() => setExcelDropdownOpen(!excelDropdownOpen)}\n                            onMouseEnter={() => setHoveredItem(item.name)}\n                            onMouseLeave={() => setHoveredItem(null)}\n                          >\n                            <Icon className={`w-4 h-4 transition-all duration-300 ${hoveredItem === item.name ? 'scale-110' : ''}`} />\n                            <span className=\"hidden lg:inline font-medium\">{item.name}</span>\n                            <ChevronDown className={`w-3 h-3 transition-transform duration-300 ${excelDropdownOpen ? 'rotate-180' : ''}`} />\n\n                            {/* Effetto shimmer al hover */}\n                            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-all duration-700\"></div>\n                          </Button>\n                        </TooltipTrigger>\n                        <TooltipContent>\n                          <p>Gestione file Excel</p>\n                        </TooltipContent>\n                      </Tooltip>\n                    </TooltipProvider>\n\n                    {excelDropdownOpen && (\n                      <div className=\"absolute top-full left-0 mt-2 w-52 bg-white border border-slate-200 rounded-xl shadow-xl z-50 overflow-hidden animate-in slide-in-from-top-2 duration-200\">\n                        <div className=\"py-2\">\n                          <div\n                            className=\"block px-4 py-3 text-sm text-slate-700 hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 hover:text-green-700 transition-all duration-200 cursor-pointer group\"\n                            onClick={() => {\n                              handleNavigation('/excel/import', 'Importa Excel')\n                              setExcelDropdownOpen(false)\n                            }}\n                          >\n                            <div className=\"flex items-center space-x-3\">\n                              <Upload className=\"w-4 h-4 group-hover:scale-110 transition-transform duration-200\" />\n                              <span className=\"font-medium\">Importa Excel</span>\n                              <ArrowRight className=\"w-3 h-3 opacity-0 group-hover:opacity-100 transition-all duration-200 ml-auto\" />\n                            </div>\n                          </div>\n                          <div\n                            className=\"block px-4 py-3 text-sm text-slate-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-cyan-50 hover:text-blue-700 transition-all duration-200 cursor-pointer group\"\n                            onClick={() => {\n                              handleNavigation('/excel/export', 'Esporta Excel')\n                              setExcelDropdownOpen(false)\n                            }}\n                          >\n                            <div className=\"flex items-center space-x-3\">\n                              <Download className=\"w-4 h-4 group-hover:scale-110 transition-transform duration-200\" />\n                              <span className=\"font-medium\">Esporta Excel</span>\n                              <ArrowRight className=\"w-3 h-3 opacity-0 group-hover:opacity-100 transition-all duration-200 ml-auto\" />\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                )\n              }\n\n              return (\n                <TooltipProvider key={item.name}>\n                  <Tooltip>\n                    <TooltipTrigger asChild>\n                      <div\n                        className={`group relative cursor-pointer transition-all duration-300 ease-in-out rounded-lg border border-transparent overflow-hidden ${\n                          isActive\n                            ? 'bg-gradient-to-r from-blue-100 to-blue-50 text-blue-700 shadow-sm border-blue-200'\n                            : 'text-slate-600 hover:text-slate-900 hover:bg-gradient-to-r hover:from-blue-50 hover:to-slate-50 hover:border-blue-200 hover:shadow-sm'\n                        }`}\n                        onClick={() => handleNavigation(item.href, item.name)}\n                        onMouseEnter={() => setHoveredItem(item.name)}\n                        onMouseLeave={() => setHoveredItem(null)}\n                      >\n                        <div className=\"flex items-center space-x-2 px-3 py-2\">\n                          {loadingStates[item.name] ? (\n                            <Loader2 className=\"w-4 h-4 animate-spin\" />\n                          ) : (\n                            <Icon className={`w-4 h-4 transition-all duration-300 ${\n                              hoveredItem === item.name ? 'scale-110' : ''\n                            } ${isActive ? 'text-blue-600' : ''}`} />\n                          )}\n                          <span className={`hidden lg:inline font-medium transition-all duration-300 ${\n                            isActive ? 'text-blue-700' : ''\n                          }`}>\n                            {item.name}\n                          </span>\n\n                          {/* Badge per Produttività */}\n                          {item.name.includes('Produttività') && (\n                            <Zap className=\"w-3 h-3 text-yellow-500 animate-pulse\" />\n                          )}\n                        </div>\n\n                        {/* Effetto shimmer al hover */}\n                        <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-all duration-700\"></div>\n\n                        {/* Indicatore attivo */}\n                        {isActive && (\n                          <div className=\"absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600\"></div>\n                        )}\n                      </div>\n                    </TooltipTrigger>\n                    <TooltipContent>\n                      <p>{item.name}</p>\n                    </TooltipContent>\n                  </Tooltip>\n                </TooltipProvider>\n              )\n            })}\n            </div>\n          </div>\n\n          {/* User Info a destra con più margine */}\n          <div className=\"flex items-center space-x-4 ml-8\">\n            {/* Display cantiere selezionato - versione migliorata */}\n            {cantiereId && cantiereId > 0 && (\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger asChild>\n                    <div className=\"hidden sm:flex items-center space-x-2 px-3 py-2 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg hover:shadow-sm transition-all duration-300 cursor-default group\">\n                      <Building2 className=\"w-4 h-4 text-blue-600 group-hover:scale-110 transition-transform duration-300\" />\n                      <div className=\"text-sm\">\n                        <span className=\"text-blue-900 font-semibold\">{cantiereName}</span>\n                        <div className=\"w-2 h-2 bg-green-400 rounded-full inline-block ml-2 animate-pulse\"></div>\n                      </div>\n                    </div>\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>Cantiere attivo: {cantiereName}</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n            )}\n\n            <div className=\"hidden sm:flex items-center space-x-3\">\n              {/* User Avatar e Info */}\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger asChild>\n                    <div className=\"flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-slate-50 transition-all duration-300 cursor-default group\">\n                      <div className=\"text-right\">\n                        <p className=\"text-sm font-semibold text-slate-900 group-hover:text-slate-700 transition-colors duration-300\">\n                          {isImpersonating && impersonatedUser ? impersonatedUser.username : user?.username}\n                        </p>\n                        <p className=\"text-xs text-slate-500 group-hover:text-slate-600 transition-colors duration-300\">\n                          {user?.ruolo === 'owner' ? 'Amministratore' :\n                           user?.ruolo === 'user' ? 'Utente Standard' :\n                           user?.ruolo === 'cantieri_user' ? 'Utente Cantiere' : user?.ruolo || ''}\n                        </p>\n                      </div>\n                      <div className=\"relative\">\n                        <div className=\"w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center group-hover:shadow-lg group-hover:shadow-green-500/25 transition-all duration-300\">\n                          <Users className=\"w-4 h-4 text-white\" />\n                        </div>\n                        <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white animate-pulse\"></div>\n                      </div>\n                    </div>\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>Utente: {isImpersonating && impersonatedUser ? impersonatedUser.username : user?.username}</p>\n                    <p>Ruolo: {user?.ruolo}</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n\n              {/* Logout Button */}\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger asChild>\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={handleLogout}\n                      disabled={loadingStates['logout']}\n                      className=\"group relative overflow-hidden hover:bg-red-50 hover:text-red-600 transition-all duration-300 ease-in-out rounded-lg border border-transparent hover:border-red-200 hover:shadow-sm\"\n                    >\n                      {loadingStates['logout'] ? (\n                        <Loader2 className=\"w-4 h-4 animate-spin\" />\n                      ) : (\n                        <LogOut className=\"w-4 h-4 group-hover:scale-110 transition-transform duration-300\" />\n                      )}\n\n                      {/* Effetto shimmer al hover */}\n                      <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-red-100 to-transparent opacity-0 group-hover:opacity-30 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-all duration-700\"></div>\n                    </Button>\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>{isImpersonating ? 'Torna al menu admin' : 'Logout'}</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n            </div>\n\n            {/* Mobile menu button migliorato */}\n            <div className=\"md:hidden\">\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger asChild>\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={() => setIsOpen(!isOpen)}\n                      className=\"group relative overflow-hidden text-slate-600 hover:bg-blue-50 hover:text-blue-600 transition-all duration-300 ease-in-out rounded-lg border border-transparent hover:border-blue-200 hover:shadow-sm\"\n                    >\n                      <div className=\"relative z-10\">\n                        {isOpen ? (\n                          <X className=\"w-5 h-5 group-hover:rotate-90 transition-transform duration-300\" />\n                        ) : (\n                          <Menu className=\"w-5 h-5 group-hover:scale-110 transition-transform duration-300\" />\n                        )}\n                      </div>\n\n                      {/* Effetto shimmer al hover */}\n                      <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-blue-100 to-transparent opacity-0 group-hover:opacity-30 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-all duration-700\"></div>\n                    </Button>\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>{isOpen ? 'Chiudi menu' : 'Apri menu'}</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Navigation migliorato */}\n      {isOpen && (\n        <div className=\"md:hidden border-t border-slate-200 bg-gradient-to-b from-white to-slate-50 shadow-lg animate-in slide-in-from-top-2 duration-300\">\n          <div className=\"px-4 pt-4 pb-6 space-y-2\">\n            {/* Info utente mobile */}\n            <div className=\"mb-4 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center\">\n                  <Users className=\"w-4 h-4 text-white\" />\n                </div>\n                <div>\n                  <p className=\"text-sm font-semibold text-slate-900\">\n                    {isImpersonating && impersonatedUser ? impersonatedUser.username : user?.username}\n                  </p>\n                  <p className=\"text-xs text-slate-600\">\n                    {user?.ruolo === 'owner' ? 'Amministratore' :\n                     user?.ruolo === 'user' ? 'Utente Standard' :\n                     user?.ruolo === 'cantieri_user' ? 'Utente Cantiere' : user?.ruolo || ''}\n                  </p>\n                </div>\n              </div>\n\n              {/* Cantiere mobile */}\n              {cantiereId && cantiereId > 0 && (\n                <div className=\"mt-2 pt-2 border-t border-blue-200\">\n                  <div className=\"flex items-center space-x-2\">\n                    <Building2 className=\"w-3 h-3 text-blue-600\" />\n                    <span className=\"text-xs text-blue-900 font-medium\">{cantiereName}</span>\n                    <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\"></div>\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Navigation items */}\n            {navigation.map((item) => {\n              const isActive = pathname === item.href ||\n                             (item.href !== '/' && pathname.startsWith(item.href))\n              const Icon = item.icon\n\n              return (\n                <div\n                  key={item.name}\n                  className={`group relative overflow-hidden rounded-lg transition-all duration-300 ${\n                    isActive\n                      ? 'bg-gradient-to-r from-blue-100 to-blue-50 border border-blue-200 shadow-sm'\n                      : 'hover:bg-gradient-to-r hover:from-blue-50 hover:to-slate-50 hover:border-blue-200 border border-transparent'\n                  }`}\n                  onClick={() => {\n                    handleNavigation(item.href, item.name)\n                    setIsOpen(false)\n                  }}\n                >\n                  <div className=\"flex items-center space-x-3 p-3 cursor-pointer\">\n                    {loadingStates[item.name] ? (\n                      <Loader2 className=\"w-5 h-5 animate-spin text-blue-600\" />\n                    ) : (\n                      <Icon className={`w-5 h-5 transition-all duration-300 group-hover:scale-110 ${\n                        isActive ? 'text-blue-600' : 'text-slate-600 group-hover:text-blue-600'\n                      }`} />\n                    )}\n                    <span className={`font-medium transition-all duration-300 ${\n                      isActive ? 'text-blue-700' : 'text-slate-700 group-hover:text-blue-700'\n                    }`}>\n                      {item.name}\n                    </span>\n\n                    {/* Badge per Produttività */}\n                    {item.name.includes('Produttività') && (\n                      <Zap className=\"w-4 h-4 text-yellow-500 animate-pulse ml-auto\" />\n                    )}\n\n                    {isActive && (\n                      <ArrowRight className=\"w-4 h-4 text-blue-600 ml-auto\" />\n                    )}\n                  </div>\n\n                  {/* Effetto shimmer al hover */}\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-all duration-700\"></div>\n                </div>\n              )\n            })}\n\n            {/* Logout button mobile */}\n            <div className=\"mt-4 pt-4 border-t border-slate-200\">\n              <div\n                className=\"group relative overflow-hidden rounded-lg transition-all duration-300 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 hover:border-red-200 border border-transparent cursor-pointer\"\n                onClick={() => {\n                  handleLogout()\n                  setIsOpen(false)\n                }}\n              >\n                <div className=\"flex items-center space-x-3 p-3\">\n                  {loadingStates['logout'] ? (\n                    <Loader2 className=\"w-5 h-5 animate-spin text-red-600\" />\n                  ) : (\n                    <LogOut className=\"w-5 h-5 text-slate-600 group-hover:text-red-600 group-hover:scale-110 transition-all duration-300\" />\n                  )}\n                  <span className=\"font-medium text-slate-700 group-hover:text-red-700 transition-all duration-300\">\n                    {isImpersonating ? 'Torna al menu admin' : 'Logout'}\n                  </span>\n                </div>\n\n                {/* Effetto shimmer al hover */}\n                <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-red-100 to-transparent opacity-0 group-hover:opacity-30 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-all duration-700\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsBA;;;AA/BA;;;;;;;AAsCA,MAAM,gBAAgB,CAAC,UAA8B,iBAA0B,kBAAuB;IACpG,iEAAiE;IACjE,MAAM,aAAa;QACjB,MAAM,aAAa,UAAU,eACvB,aAAa,SAAS,mBACtB,aAAa,kBAAkB,kBAAkB;QACvD,MAAM,aAAa,UAAU,WACvB,aAAa,SAAS,cACtB,aAAa,kBAAkB,UAAU;QAC/C,MAAM,sMAAA,CAAA,OAAI;IACZ;IAEA,IAAI,aAAa,WAAW,CAAC,iBAAiB;QAC5C,uEAAuE;QACvE,OAAO;YAAC;SAAW;IACrB;IAEA,IAAI,aAAa,UAAW,mBAAmB,kBAAkB,SAAS,QAAS;QACjF,qEAAqE;QACrE,MAAM,MAAM;YAAC;SAAW;QACxB,IAAI,iBAAiB;YACnB,IAAI,IAAI,CAAC;gBAAE,MAAM;gBAAY,MAAM;gBAAa,MAAM,mNAAA,CAAA,YAAS;YAAC;QAClE;QAEA,wFAAwF;QACxF,IAAI,YAAY;YACd,IAAI,IAAI,CACN;gBAAE,MAAM;gBAAmB,MAAM;gBAAS,MAAM,uMAAA,CAAA,QAAK;YAAC,GACtD;gBAAE,MAAM;gBAAc,MAAM;gBAAe,MAAM,2MAAA,CAAA,UAAO;YAAC,GACzD;gBAAE,MAAM;gBAAkB,MAAM;gBAAU,MAAM,iNAAA,CAAA,WAAQ;gBAAE,aAAa;YAAK,GAC5E;gBAAE,MAAM;gBAAU,MAAM;gBAAY,MAAM,qNAAA,CAAA,YAAS;YAAC,GACpD;gBAAE,MAAM;gBAAoB,MAAM;gBAAY,MAAM,2NAAA,CAAA,gBAAa;YAAC,GAClE;gBAAE,MAAM;gBAAgB,MAAM;gBAAiB,MAAM,6MAAA,CAAA,WAAQ;YAAC;QAElE;QAEA,OAAO;IACT;IAEA,IAAI,aAAa,mBAAoB,mBAAmB,kBAAkB,SAAS,iBAAkB;QACnG,8DAA8D;QAC9D,MAAM,MAAM;YAAC;SAAW;QAExB,4DAA4D;QAC5D,IAAI,YAAY;YACd,2DAA2D;YAC3D,IAAI,aAAa,iBAAiB;gBAChC,IAAI,IAAI,CAAC;oBAAE,MAAM;oBAAmB,MAAM;oBAAS,MAAM,uMAAA,CAAA,QAAK;gBAAC;YACjE;YAEA,IAAI,IAAI,CACN;gBAAE,MAAM;gBAAc,MAAM;gBAAe,MAAM,2MAAA,CAAA,UAAO;YAAC,GACzD;gBAAE,MAAM;gBAAkB,MAAM;gBAAU,MAAM,iNAAA,CAAA,WAAQ;gBAAE,aAAa;YAAK,GAC5E;gBAAE,MAAM;gBAAU,MAAM;gBAAY,MAAM,qNAAA,CAAA,YAAS;YAAC,GACpD;gBAAE,MAAM;gBAAoB,MAAM;gBAAY,MAAM,2NAAA,CAAA,gBAAa;YAAC,GAClE;gBAAE,MAAM;gBAAgB,MAAM;gBAAiB,MAAM,6MAAA,CAAA,WAAQ;YAAC;QAElE;QAEA,OAAO;IACT;IAEA,UAAU;IACV,OAAO;QAAC;KAAW;AACrB;AAEO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IAC7E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAE7F,wEAAwE;IACxE,MAAM,aAAa,UAAU,eAAe,CAAC,uCAAgC,SAAS,aAAa,OAAO,CAAC,yBAAyB,2CAAQ;IAC5I,MAAM,eAAe,UAAU,YAAY,CAAC,uCAAgC,aAAa,OAAO,CAAC,8DAA4B,KAAK,CAAC,SAAS,EAAE,YAAY;IAE1J,MAAM,aAAa,cAAc,MAAM,OAAO,iBAAiB,kBAAkB;IAEjF,0CAA0C;IAC1C,MAAM,mBAAmB,OAAO,MAAc;QAC5C,IAAI,aAAa,MAAM;QAEvB,iBAAiB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,SAAS,EAAE;YAAK,CAAC;QACvD,gBAAgB;QAEhB,IAAI;YACF,MAAM,OAAO,IAAI,CAAC;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;QACrC,SAAU;YACR,wCAAwC;YACxC,WAAW;gBACT,iBAAiB,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,CAAC,SAAS,EAAE;oBAAM,CAAC;gBACxD,gBAAgB;gBAChB,UAAU,OAAO,qBAAqB;;YACxC,GAAG;QACL;IACF;IAEA,8BAA8B;IAC9B,MAAM,eAAe;QACnB,iBAAiB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,UAAU;YAAK,CAAC;QACrD,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,iBAAiB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,UAAU;gBAAM,CAAC;QACxD;IACF;IAEA,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,SAAS,mBAAmB,KAAiB;gBAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;oBAC9E,qBAAqB;gBACvB;YACF;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;oCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;2BAAG,EAAE;IAEL,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,iBAAiB,CAAC;YAClB,gBAAgB;QAClB;2BAAG;QAAC;KAAS;IAEb,4CAA4C;IAC5C,IAAI,aAAa,UAAU;QACzB,OAAO;IACT;IAEA,0CAA0C;IAC1C,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAGb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,sIAAA,CAAA,kBAAe;8CACd,cAAA,6LAAC,sIAAA,CAAA,UAAO;;0DACN,6LAAC,sIAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,6LAAC;oDACC,WAAU;oDACV,SAAS,IAAM,iBAAiB,KAAK;;sEAErC,6LAAC;4DAAI,WAAU;;gEACZ,aAAa,CAAC,OAAO,iBACpB,6LAAC,oNAAA,CAAA,UAAO;oEAAC,WAAU;;;;;yFAEnB,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EAEnB,6LAAC;oEAAI,WAAU;;;;;;;;;;;;sEAEjB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;;wEAA4F;sFAExG,6LAAC,6MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;;;;;;;8EAEtB,6LAAC;oEAAE,WAAU;8EAAyF;;;;;;;;;;;;;;;;;;;;;;;0DAM5G,6LAAC,sIAAA,CAAA,iBAAc;0DACb,cAAA,6LAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;8CAMT,6LAAC;oCAAI,WAAU;8CACd,WAAW,GAAG,CAAC,CAAC;wCACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACvB,KAAK,IAAI,KAAK,OAAO,SAAS,UAAU,CAAC,KAAK,IAAI;wCAClE,MAAM,OAAO,KAAK,IAAI;wCAEtB,0CAA0C;wCAC1C,IAAI,KAAK,WAAW,IAAI,KAAK,IAAI,KAAK,kBAAkB;4CACtD,qBACE,6LAAC;gDAAoB,WAAU;gDAAW,KAAK;;kEAC7C,6LAAC,sIAAA,CAAA,kBAAe;kEACd,cAAA,6LAAC,sIAAA,CAAA,UAAO;;8EACN,6LAAC,sIAAA,CAAA,iBAAc;oEAAC,OAAO;8EACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAW,CAAC,kJAAkJ,EAC5J,WACI,sFACA,yIACJ;wEACF,SAAS,IAAM,qBAAqB,CAAC;wEACrC,cAAc,IAAM,eAAe,KAAK,IAAI;wEAC5C,cAAc,IAAM,eAAe;;0FAEnC,6LAAC;gFAAK,WAAW,CAAC,oCAAoC,EAAE,gBAAgB,KAAK,IAAI,GAAG,cAAc,IAAI;;;;;;0FACtG,6LAAC;gFAAK,WAAU;0FAAgC,KAAK,IAAI;;;;;;0FACzD,6LAAC,uNAAA,CAAA,cAAW;gFAAC,WAAW,CAAC,0CAA0C,EAAE,oBAAoB,eAAe,IAAI;;;;;;0FAG5G,6LAAC;gFAAI,WAAU;;;;;;;;;;;;;;;;;8EAGnB,6LAAC,sIAAA,CAAA,iBAAc;8EACb,cAAA,6LAAC;kFAAE;;;;;;;;;;;;;;;;;;;;;;oDAKR,mCACC,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,WAAU;oEACV,SAAS;wEACP,iBAAiB,iBAAiB;wEAClC,qBAAqB;oEACvB;8EAEA,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,yMAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,6LAAC;gFAAK,WAAU;0FAAc;;;;;;0FAC9B,6LAAC,qNAAA,CAAA,aAAU;gFAAC,WAAU;;;;;;;;;;;;;;;;;8EAG1B,6LAAC;oEACC,WAAU;oEACV,SAAS;wEACP,iBAAiB,iBAAiB;wEAClC,qBAAqB;oEACvB;8EAEA,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,6MAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;0FACpB,6LAAC;gFAAK,WAAU;0FAAc;;;;;;0FAC9B,6LAAC,qNAAA,CAAA,aAAU;gFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+CAxDxB,KAAK,IAAI;;;;;wCAgEvB;wCAEA,qBACE,6LAAC,sIAAA,CAAA,kBAAe;sDACd,cAAA,6LAAC,sIAAA,CAAA,UAAO;;kEACN,6LAAC,sIAAA,CAAA,iBAAc;wDAAC,OAAO;kEACrB,cAAA,6LAAC;4DACC,WAAW,CAAC,2HAA2H,EACrI,WACI,sFACA,yIACJ;4DACF,SAAS,IAAM,iBAAiB,KAAK,IAAI,EAAE,KAAK,IAAI;4DACpD,cAAc,IAAM,eAAe,KAAK,IAAI;4DAC5C,cAAc,IAAM,eAAe;;8EAEnC,6LAAC;oEAAI,WAAU;;wEACZ,aAAa,CAAC,KAAK,IAAI,CAAC,iBACvB,6LAAC,oNAAA,CAAA,UAAO;4EAAC,WAAU;;;;;iGAEnB,6LAAC;4EAAK,WAAW,CAAC,oCAAoC,EACpD,gBAAgB,KAAK,IAAI,GAAG,cAAc,GAC3C,CAAC,EAAE,WAAW,kBAAkB,IAAI;;;;;;sFAEvC,6LAAC;4EAAK,WAAW,CAAC,yDAAyD,EACzE,WAAW,kBAAkB,IAC7B;sFACC,KAAK,IAAI;;;;;;wEAIX,KAAK,IAAI,CAAC,QAAQ,CAAC,iCAClB,6LAAC,mMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;;;;;;;8EAKnB,6LAAC;oEAAI,WAAU;;;;;;gEAGd,0BACC,6LAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;kEAIrB,6LAAC,sIAAA,CAAA,iBAAc;kEACb,cAAA,6LAAC;sEAAG,KAAK,IAAI;;;;;;;;;;;;;;;;;2CA3CG,KAAK,IAAI;;;;;oCAgDnC;;;;;;;;;;;;sCAKF,6LAAC;4BAAI,WAAU;;gCAEZ,cAAc,aAAa,mBAC1B,6LAAC,sIAAA,CAAA,kBAAe;8CACd,cAAA,6LAAC,sIAAA,CAAA,UAAO;;0DACN,6LAAC,sIAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,mNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAA+B;;;;;;8EAC/C,6LAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;0DAIrB,6LAAC,sIAAA,CAAA,iBAAc;0DACb,cAAA,6LAAC;;wDAAE;wDAAkB;;;;;;;;;;;;;;;;;;;;;;;8CAM7B,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC,sIAAA,CAAA,kBAAe;sDACd,cAAA,6LAAC,sIAAA,CAAA,UAAO;;kEACN,6LAAC,sIAAA,CAAA,iBAAc;wDAAC,OAAO;kEACrB,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFACV,mBAAmB,mBAAmB,iBAAiB,QAAQ,GAAG,MAAM;;;;;;sFAE3E,6LAAC;4EAAE,WAAU;sFACV,MAAM,UAAU,UAAU,mBAC1B,MAAM,UAAU,SAAS,oBACzB,MAAM,UAAU,kBAAkB,oBAAoB,MAAM,SAAS;;;;;;;;;;;;8EAG1E,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;;;;;;sFAEnB,6LAAC;4EAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;kEAIrB,6LAAC,sIAAA,CAAA,iBAAc;;0EACb,6LAAC;;oEAAE;oEAAS,mBAAmB,mBAAmB,iBAAiB,QAAQ,GAAG,MAAM;;;;;;;0EACpF,6LAAC;;oEAAE;oEAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;sDAMvB,6LAAC,sIAAA,CAAA,kBAAe;sDACd,cAAA,6LAAC,sIAAA,CAAA,UAAO;;kEACN,6LAAC,sIAAA,CAAA,iBAAc;wDAAC,OAAO;kEACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS;4DACT,UAAU,aAAa,CAAC,SAAS;4DACjC,WAAU;;gEAET,aAAa,CAAC,SAAS,iBACtB,6LAAC,oNAAA,CAAA,UAAO;oEAAC,WAAU;;;;;yFAEnB,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAIpB,6LAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;kEAGnB,6LAAC,sIAAA,CAAA,iBAAc;kEACb,cAAA,6LAAC;sEAAG,kBAAkB,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOtD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,sIAAA,CAAA,kBAAe;kDACd,cAAA,6LAAC,sIAAA,CAAA,UAAO;;8DACN,6LAAC,sIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,UAAU,CAAC;wDAC1B,WAAU;;0EAEV,6LAAC;gEAAI,WAAU;0EACZ,uBACC,6LAAC,+LAAA,CAAA,IAAC;oEAAC,WAAU;;;;;yFAEb,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAKpB,6LAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;8DAGnB,6LAAC,sIAAA,CAAA,iBAAc;8DACb,cAAA,6LAAC;kEAAG,SAAS,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAU1C,wBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DACV,mBAAmB,mBAAmB,iBAAiB,QAAQ,GAAG,MAAM;;;;;;8DAE3E,6LAAC;oDAAE,WAAU;8DACV,MAAM,UAAU,UAAU,mBAC1B,MAAM,UAAU,SAAS,oBACzB,MAAM,UAAU,kBAAkB,oBAAoB,MAAM,SAAS;;;;;;;;;;;;;;;;;;gCAM3E,cAAc,aAAa,mBAC1B,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;gDAAK,WAAU;0DAAqC;;;;;;0DACrD,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;wBAOtB,WAAW,GAAG,CAAC,CAAC;4BACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACvB,KAAK,IAAI,KAAK,OAAO,SAAS,UAAU,CAAC,KAAK,IAAI;4BAClE,MAAM,OAAO,KAAK,IAAI;4BAEtB,qBACE,6LAAC;gCAEC,WAAW,CAAC,sEAAsE,EAChF,WACI,+EACA,+GACJ;gCACF,SAAS;oCACP,iBAAiB,KAAK,IAAI,EAAE,KAAK,IAAI;oCACrC,UAAU;gCACZ;;kDAEA,6LAAC;wCAAI,WAAU;;4CACZ,aAAa,CAAC,KAAK,IAAI,CAAC,iBACvB,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAEnB,6LAAC;gDAAK,WAAW,CAAC,0DAA0D,EAC1E,WAAW,kBAAkB,4CAC7B;;;;;;0DAEJ,6LAAC;gDAAK,WAAW,CAAC,wCAAwC,EACxD,WAAW,kBAAkB,4CAC7B;0DACC,KAAK,IAAI;;;;;;4CAIX,KAAK,IAAI,CAAC,QAAQ,CAAC,iCAClB,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAGhB,0BACC,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAK1B,6LAAC;wCAAI,WAAU;;;;;;;+BApCV,KAAK,IAAI;;;;;wBAuCpB;sCAGA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAU;gCACV,SAAS;oCACP;oCACA,UAAU;gCACZ;;kDAEA,6LAAC;wCAAI,WAAU;;4CACZ,aAAa,CAAC,SAAS,iBACtB,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAEnB,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAEpB,6LAAC;gDAAK,WAAU;0DACb,kBAAkB,wBAAwB;;;;;;;;;;;;kDAK/C,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/B;GA3dgB;;QAOG,qIAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;QAC+D,kIAAA,CAAA,UAAO;;;KAThF", "debugId": null}}, {"offset": {"line": 2140, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/hooks/useSessionPersistence.ts"], "sourcesContent": ["'use client'\n\nimport { useEffect, useRef } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\n\n/**\n * Hook per gestire la persistenza della sessione e prevenire stati \"fantasma\"\n * Risolve il bug dove l'utente appare loggato ma i reindirizzamenti non funzionano\n */\nexport const useSessionPersistence = () => {\n  const { isAuthenticated, user, cantiere, checkAuth, logout } = useAuth()\n  const heartbeatInterval = useRef<NodeJS.Timeout | null>(null)\n  const lastActivityTime = useRef<number>(Date.now())\n\n  // Aggiorna il timestamp dell'ultima attività\n  const updateActivity = () => {\n    lastActivityTime.current = Date.now()\n  }\n\n  // Verifica periodicamente la validità della sessione\n  const startHeartbeat = () => {\n    if (heartbeatInterval.current) {\n      clearInterval(heartbeatInterval.current)\n    }\n\n    heartbeatInterval.current = setInterval(async () => {\n      try {\n        // Verifica se la sessione è ancora valida\n        if (isAuthenticated && (user || cantiere)) {\n          console.log('🔄 SessionPersistence: Verifica heartbeat sessione')\n          await checkAuth()\n        }\n      } catch (error) {\n        console.error('🔄 SessionPersistence: Errore durante heartbeat:', error)\n        // Se il heartbeat fallisce, forza il logout per evitare stati inconsistenti\n        logout()\n      }\n    }, 5 * 60 * 1000) // Ogni 5 minuti\n  }\n\n  // Ferma il heartbeat\n  const stopHeartbeat = () => {\n    if (heartbeatInterval.current) {\n      clearInterval(heartbeatInterval.current)\n      heartbeatInterval.current = null\n    }\n  }\n\n  // Gestisce la visibilità della pagina (quando l'utente torna alla tab)\n  const handleVisibilityChange = async () => {\n    if (document.visibilityState === 'visible' && isAuthenticated) {\n      console.log('🔄 SessionPersistence: Pagina tornata visibile, verifica sessione')\n      try {\n        await checkAuth()\n      } catch (error) {\n        console.error('🔄 SessionPersistence: Errore durante verifica visibilità:', error)\n        logout()\n      }\n    }\n  }\n\n  // Gestisce il focus della finestra\n  const handleWindowFocus = async () => {\n    if (isAuthenticated) {\n      console.log('🔄 SessionPersistence: Finestra tornata in focus, verifica sessione')\n      try {\n        await checkAuth()\n      } catch (error) {\n        console.error('🔄 SessionPersistence: Errore durante verifica focus:', error)\n        logout()\n      }\n    }\n  }\n\n  // Gestisce la chiusura della finestra/tab\n  const handleBeforeUnload = () => {\n    // Salva il timestamp dell'ultima attività\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('lastActivity', lastActivityTime.current.toString())\n    }\n  }\n\n  // Verifica se la sessione è scaduta per inattività\n  const checkSessionExpiry = () => {\n    if (typeof window !== 'undefined' && isAuthenticated) {\n      const lastActivity = localStorage.getItem('lastActivity')\n      if (lastActivity) {\n        const timeDiff = Date.now() - parseInt(lastActivity, 10)\n        const maxInactivity = 24 * 60 * 60 * 1000 // 24 ore\n\n        if (timeDiff > maxInactivity) {\n          console.log('🔄 SessionPersistence: Sessione scaduta per inattività')\n          logout()\n          return false\n        }\n      }\n    }\n    return true\n  }\n\n  useEffect(() => {\n    if (typeof window === 'undefined') return\n\n    // Se non autenticato, non fare nulla\n    if (!isAuthenticated) {\n      console.log('🔄 SessionPersistence: Utente non autenticato, skip')\n      stopHeartbeat()\n      return\n    }\n\n    // Verifica la scadenza della sessione all'avvio\n    if (!checkSessionExpiry()) {\n      return\n    }\n\n    console.log('🔄 SessionPersistence: Attivazione per utente autenticato')\n\n    // Avvia il heartbeat per sessioni autenticate\n    startHeartbeat()\n\n    // Aggiungi event listeners\n    document.addEventListener('visibilitychange', handleVisibilityChange)\n    window.addEventListener('focus', handleWindowFocus)\n    window.addEventListener('beforeunload', handleBeforeUnload)\n\n    // Event listeners per tracciare l'attività dell'utente\n    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click']\n    activityEvents.forEach(event => {\n      document.addEventListener(event, updateActivity, { passive: true })\n    })\n\n    return () => {\n      // Cleanup\n      console.log('🔄 SessionPersistence: Cleanup event listeners')\n      stopHeartbeat()\n      document.removeEventListener('visibilitychange', handleVisibilityChange)\n      window.removeEventListener('focus', handleWindowFocus)\n      window.removeEventListener('beforeunload', handleBeforeUnload)\n\n      activityEvents.forEach(event => {\n        document.removeEventListener(event, updateActivity)\n      })\n    }\n  }, [isAuthenticated, user, cantiere])\n\n  // Cleanup finale\n  useEffect(() => {\n    return () => {\n      stopHeartbeat()\n    }\n  }, [])\n\n  return {\n    updateActivity,\n    checkSessionExpiry\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;;AAHA;;;AASO,MAAM,wBAAwB;;IACnC,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACrE,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IACxD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU,KAAK,GAAG;IAEhD,6CAA6C;IAC7C,MAAM,iBAAiB;QACrB,iBAAiB,OAAO,GAAG,KAAK,GAAG;IACrC;IAEA,qDAAqD;IACrD,MAAM,iBAAiB;QACrB,IAAI,kBAAkB,OAAO,EAAE;YAC7B,cAAc,kBAAkB,OAAO;QACzC;QAEA,kBAAkB,OAAO,GAAG,YAAY;YACtC,IAAI;gBACF,0CAA0C;gBAC1C,IAAI,mBAAmB,CAAC,QAAQ,QAAQ,GAAG;oBACzC,QAAQ,GAAG,CAAC;oBACZ,MAAM;gBACR;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oDAAoD;gBAClE,4EAA4E;gBAC5E;YACF;QACF,GAAG,IAAI,KAAK,MAAM,gBAAgB;;IACpC;IAEA,qBAAqB;IACrB,MAAM,gBAAgB;QACpB,IAAI,kBAAkB,OAAO,EAAE;YAC7B,cAAc,kBAAkB,OAAO;YACvC,kBAAkB,OAAO,GAAG;QAC9B;IACF;IAEA,uEAAuE;IACvE,MAAM,yBAAyB;QAC7B,IAAI,SAAS,eAAe,KAAK,aAAa,iBAAiB;YAC7D,QAAQ,GAAG,CAAC;YACZ,IAAI;gBACF,MAAM;YACR,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8DAA8D;gBAC5E;YACF;QACF;IACF;IAEA,mCAAmC;IACnC,MAAM,oBAAoB;QACxB,IAAI,iBAAiB;YACnB,QAAQ,GAAG,CAAC;YACZ,IAAI;gBACF,MAAM;YACR,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yDAAyD;gBACvE;YACF;QACF;IACF;IAEA,0CAA0C;IAC1C,MAAM,qBAAqB;QACzB,0CAA0C;QAC1C,wCAAmC;YACjC,aAAa,OAAO,CAAC,gBAAgB,iBAAiB,OAAO,CAAC,QAAQ;QACxE;IACF;IAEA,mDAAmD;IACnD,MAAM,qBAAqB;QACzB,IAAI,aAAkB,eAAe,iBAAiB;YACpD,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,IAAI,cAAc;gBAChB,MAAM,WAAW,KAAK,GAAG,KAAK,SAAS,cAAc;gBACrD,MAAM,gBAAgB,KAAK,KAAK,KAAK,KAAK,SAAS;;gBAEnD,IAAI,WAAW,eAAe;oBAC5B,QAAQ,GAAG,CAAC;oBACZ;oBACA,OAAO;gBACT;YACF;QACF;QACA,OAAO;IACT;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,uCAAmC;;YAAK;YAExC,qCAAqC;YACrC,IAAI,CAAC,iBAAiB;gBACpB,QAAQ,GAAG,CAAC;gBACZ;gBACA;YACF;YAEA,gDAAgD;YAChD,IAAI,CAAC,sBAAsB;gBACzB;YACF;YAEA,QAAQ,GAAG,CAAC;YAEZ,8CAA8C;YAC9C;YAEA,2BAA2B;YAC3B,SAAS,gBAAgB,CAAC,oBAAoB;YAC9C,OAAO,gBAAgB,CAAC,SAAS;YACjC,OAAO,gBAAgB,CAAC,gBAAgB;YAExC,uDAAuD;YACvD,MAAM,iBAAiB;gBAAC;gBAAa;gBAAa;gBAAY;gBAAU;gBAAc;aAAQ;YAC9F,eAAe,OAAO;mDAAC,CAAA;oBACrB,SAAS,gBAAgB,CAAC,OAAO,gBAAgB;wBAAE,SAAS;oBAAK;gBACnE;;YAEA;mDAAO;oBACL,UAAU;oBACV,QAAQ,GAAG,CAAC;oBACZ;oBACA,SAAS,mBAAmB,CAAC,oBAAoB;oBACjD,OAAO,mBAAmB,CAAC,SAAS;oBACpC,OAAO,mBAAmB,CAAC,gBAAgB;oBAE3C,eAAe,OAAO;2DAAC,CAAA;4BACrB,SAAS,mBAAmB,CAAC,OAAO;wBACtC;;gBACF;;QACF;0CAAG;QAAC;QAAiB;QAAM;KAAS;IAEpC,iBAAiB;IACjB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR;mDAAO;oBACL;gBACF;;QACF;0CAAG,EAAE;IAEL,OAAO;QACL;QACA;IACF;AACF;GAnJa;;QACoD,kIAAA,CAAA,UAAO", "debugId": null}}, {"offset": {"line": 2321, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground\",\n        destructive:\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n))\nAlert.displayName = \"Alert\"\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nAlertTitle.displayName = \"AlertTitle\"\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n))\nAlertDescription.displayName = \"AlertDescription\"\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,6LAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2398, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/auth/ExpirationWarning.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Button } from '@/components/ui/button'\nimport { AlertTriangle, X, Calendar, Clock } from 'lucide-react'\nimport { useAuth } from '@/contexts/AuthContext'\n\nexport default function ExpirationWarning() {\n  const { \n    expirationWarning, \n    daysUntilExpiration, \n    expirationDate, \n    dismissExpirationWarning \n  } = useAuth()\n\n  if (!expirationWarning) {\n    return null\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('it-IT', {\n      day: '2-digit',\n      month: '2-digit',\n      year: 'numeric'\n    })\n  }\n\n  const getAlertVariant = () => {\n    if (daysUntilExpiration === 0) return 'destructive'\n    if (daysUntilExpiration === 1) return 'destructive'\n    return 'default'\n  }\n\n  const getIcon = () => {\n    if (daysUntilExpiration === 0) return <AlertTriangle className=\"h-4 w-4\" />\n    if (daysUntilExpiration === 1) return <Clock className=\"h-4 w-4\" />\n    return <Calendar className=\"h-4 w-4\" />\n  }\n\n  return (\n    <Alert variant={getAlertVariant()} className=\"mb-4 border-l-4\">\n      <div className=\"flex items-start justify-between\">\n        <div className=\"flex items-start space-x-2\">\n          {getIcon()}\n          <div className=\"flex-1\">\n            <AlertDescription className=\"font-medium\">\n              {expirationWarning}\n            </AlertDescription>\n            {expirationDate && (\n              <AlertDescription className=\"text-sm mt-1 opacity-90\">\n                Data di scadenza: {formatDate(expirationDate)}\n              </AlertDescription>\n            )}\n            <AlertDescription className=\"text-sm mt-2 opacity-80\">\n              Contatta l'amministratore per rinnovare il tuo account.\n            </AlertDescription>\n          </div>\n        </div>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={dismissExpirationWarning}\n          className=\"h-6 w-6 p-0 hover:bg-transparent\"\n        >\n          <X className=\"h-4 w-4\" />\n        </Button>\n      </div>\n    </Alert>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;AAQe,SAAS;;IACtB,MAAM,EACJ,iBAAiB,EACjB,mBAAmB,EACnB,cAAc,EACd,wBAAwB,EACzB,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEV,IAAI,CAAC,mBAAmB;QACtB,OAAO;IACT;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,KAAK;YACL,OAAO;YACP,MAAM;QACR;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,wBAAwB,GAAG,OAAO;QACtC,IAAI,wBAAwB,GAAG,OAAO;QACtC,OAAO;IACT;IAEA,MAAM,UAAU;QACd,IAAI,wBAAwB,GAAG,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;QAC/D,IAAI,wBAAwB,GAAG,qBAAO,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QACvD,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC7B;IAEA,qBACE,6LAAC,oIAAA,CAAA,QAAK;QAAC,SAAS;QAAmB,WAAU;kBAC3C,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;wBACZ;sCACD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,mBAAgB;oCAAC,WAAU;8CACzB;;;;;;gCAEF,gCACC,6LAAC,oIAAA,CAAA,mBAAgB;oCAAC,WAAU;;wCAA0B;wCACjC,WAAW;;;;;;;8CAGlC,6LAAC,oIAAA,CAAA,mBAAgB;oCAAC,WAAU;8CAA0B;;;;;;;;;;;;;;;;;;8BAK1D,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKvB;GA9DwB;;QAMlB,kIAAA,CAAA,UAAO;;;KANW", "debugId": null}}, {"offset": {"line": 2555, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/layout/MainContent.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useSessionPersistence } from '@/hooks/useSessionPersistence'\nimport ExpirationWarning from '@/components/auth/ExpirationWarning'\n\ninterface MainContentProps {\n  children: React.ReactNode\n}\n\nexport default function MainContent({ children }: MainContentProps) {\n  const { isAuthenticated } = useAuth()\n\n  // SEMPRE chiamare gli hooks - la logica condizionale è DENTRO l'hook\n  useSessionPersistence()\n\n  return (\n    <main className=\"pt-16\">\n      {isAuthenticated && (\n        <div className=\"container mx-auto px-4 py-2\">\n          <ExpirationWarning />\n        </div>\n      )}\n      {children}\n    </main>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;;;AALA;;;;AAWe,SAAS,YAAY,EAAE,QAAQ,EAAoB;;IAChE,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAElC,qEAAqE;IACrE,CAAA,GAAA,wIAAA,CAAA,wBAAqB,AAAD;IAEpB,qBACE,6LAAC;QAAK,WAAU;;YACb,iCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,kJAAA,CAAA,UAAiB;;;;;;;;;;YAGrB;;;;;;;AAGP;GAhBwB;;QACM,kIAAA,CAAA,UAAO;QAGnC,wIAAA,CAAA,wBAAqB;;;KAJC", "debugId": null}}, {"offset": {"line": 2614, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/hooks/use-toast.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\nexport interface Toast {\n  id: string\n  title?: string\n  description?: string\n  variant?: 'default' | 'destructive'\n  duration?: number\n}\n\ninterface ToastState {\n  toasts: Toast[]\n}\n\nconst TOAST_LIMIT = 1\nconst TOAST_REMOVE_DELAY = 1000000\n\nlet count = 0\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_VALUE\n  return count.toString()\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId)\n    dispatch({\n      type: 'REMOVE_TOAST',\n      toastId: toastId,\n    })\n  }, TOAST_REMOVE_DELAY)\n\n  toastTimeouts.set(toastId, timeout)\n}\n\nexport const reducer = (state: ToastState, action: any): ToastState => {\n  switch (action.type) {\n    case 'ADD_TOAST':\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      }\n\n    case 'UPDATE_TOAST':\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      }\n\n    case 'DISMISS_TOAST': {\n      const { toastId } = action\n\n      if (toastId) {\n        addToRemoveQueue(toastId)\n      } else {\n        state.toasts.forEach((toast) => {\n          addToRemoveQueue(toast.id)\n        })\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      }\n    }\n    case 'REMOVE_TOAST':\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        }\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      }\n  }\n}\n\nconst listeners: Array<(state: ToastState) => void> = []\n\nlet memoryState: ToastState = { toasts: [] }\n\nfunction dispatch(action: any) {\n  memoryState = reducer(memoryState, action)\n  listeners.forEach((listener) => {\n    listener(memoryState)\n  })\n}\n\ntype Toast2 = Omit<Toast, 'id'>\n\nfunction toast({ ...props }: Toast2) {\n  const id = genId()\n\n  const update = (props: Partial<Toast>) =>\n    dispatch({\n      type: 'UPDATE_TOAST',\n      toast: { ...props, id },\n    })\n  const dismiss = () => dispatch({ type: 'DISMISS_TOAST', toastId: id })\n\n  dispatch({\n    type: 'ADD_TOAST',\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open: boolean) => {\n        if (!open) dismiss()\n      },\n    },\n  })\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  }\n}\n\nfunction useToast() {\n  const [state, setState] = useState<ToastState>(memoryState)\n\n  useEffect(() => {\n    listeners.push(setState)\n    return () => {\n      const index = listeners.indexOf(setState)\n      if (index > -1) {\n        listeners.splice(index, 1)\n      }\n    }\n  }, [])\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: 'DISMISS_TOAST', toastId }),\n  }\n}\n\nexport { useToast, toast }\n"], "names": [], "mappings": ";;;;;AAEA;;AAFA;;AAgBA,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAE3B,IAAI,QAAQ;AAEZ,SAAS;IACP,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,SAAS;IACtC,OAAO,MAAM,QAAQ;AACvB;AAEA,MAAM,gBAAgB,IAAI;AAE1B,MAAM,mBAAmB,CAAC;IACxB,IAAI,cAAc,GAAG,CAAC,UAAU;QAC9B;IACF;IAEA,MAAM,UAAU,WAAW;QACzB,cAAc,MAAM,CAAC;QACrB,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF,GAAG;IAEH,cAAc,GAAG,CAAC,SAAS;AAC7B;AAEO,MAAM,UAAU,CAAC,OAAmB;IACzC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ;oBAAC,OAAO,KAAK;uBAAK,MAAM,MAAM;iBAAC,CAAC,KAAK,CAAC,GAAG;YACnD;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,GAAG,OAAO,KAAK;oBAAC,IAAI;YAE3D;QAEF,KAAK;YAAiB;gBACpB,MAAM,EAAE,OAAO,EAAE,GAAG;gBAEpB,IAAI,SAAS;oBACX,iBAAiB;gBACnB,OAAO;oBACL,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;wBACpB,iBAAiB,MAAM,EAAE;oBAC3B;gBACF;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,WAAW,YAAY,YAC5B;4BACE,GAAG,CAAC;4BACJ,MAAM;wBACR,IACA;gBAER;YACF;QACA,KAAK;YACH,IAAI,OAAO,OAAO,KAAK,WAAW;gBAChC,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,EAAE;gBACZ;YACF;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,OAAO;YAC5D;IACJ;AACF;AAEA,MAAM,YAAgD,EAAE;AAExD,IAAI,cAA0B;IAAE,QAAQ,EAAE;AAAC;AAE3C,SAAS,SAAS,MAAW;IAC3B,cAAc,QAAQ,aAAa;IACnC,UAAU,OAAO,CAAC,CAAC;QACjB,SAAS;IACX;AACF;AAIA,SAAS,MAAM,EAAE,GAAG,OAAe;IACjC,MAAM,KAAK;IAEX,MAAM,SAAS,CAAC,QACd,SAAS;YACP,MAAM;YACN,OAAO;gBAAE,GAAG,KAAK;gBAAE;YAAG;QACxB;IACF,MAAM,UAAU,IAAM,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAG;IAEpE,SAAS;QACP,MAAM;QACN,OAAO;YACL,GAAG,KAAK;YACR;YACA,MAAM;YACN,cAAc,CAAC;gBACb,IAAI,CAAC,MAAM;YACb;QACF;IACF;IAEA,OAAO;QACL,IAAI;QACJ;QACA;IACF;AACF;AAEA,SAAS;;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,UAAU,IAAI,CAAC;YACf;sCAAO;oBACL,MAAM,QAAQ,UAAU,OAAO,CAAC;oBAChC,IAAI,QAAQ,CAAC,GAAG;wBACd,UAAU,MAAM,CAAC,OAAO;oBAC1B;gBACF;;QACF;6BAAG,EAAE;IAEL,OAAO;QACL,GAAG,KAAK;QACR;QACA,SAAS,CAAC,UAAqB,SAAS;gBAAE,MAAM;gBAAiB;YAAQ;IAC3E;AACF;GAlBS", "debugId": null}}, {"offset": {"line": 2769, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/toaster.tsx"], "sourcesContent": ["'use client'\n\nimport { useToast } from '@/hooks/use-toast'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Button } from '@/components/ui/button'\nimport { X, CheckCircle, AlertCircle } from 'lucide-react'\n\nexport function Toaster() {\n  const { toasts, dismiss } = useToast()\n\n  return (\n    <div className=\"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\">\n      {toasts.map((toast) => (\n        <div\n          key={toast.id}\n          className=\"group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\"\n          style={{\n            backgroundColor: toast.variant === 'destructive' ? '#fef2f2' : '#f0f9ff',\n            borderColor: toast.variant === 'destructive' ? '#fecaca' : '#bae6fd',\n          }}\n        >\n          <div className=\"flex items-start space-x-2\">\n            {toast.variant === 'destructive' ? (\n              <AlertCircle className=\"h-4 w-4 text-red-600 mt-0.5\" />\n            ) : (\n              <CheckCircle className=\"h-4 w-4 text-green-600 mt-0.5\" />\n            )}\n            <div className=\"grid gap-1\">\n              {toast.title && (\n                <div className={`text-sm font-semibold ${\n                  toast.variant === 'destructive' ? 'text-red-900' : 'text-gray-900'\n                }`}>\n                  {toast.title}\n                </div>\n              )}\n              {toast.description && (\n                <div className={`text-sm ${\n                  toast.variant === 'destructive' ? 'text-red-700' : 'text-gray-700'\n                }`}>\n                  {toast.description}\n                </div>\n              )}\n            </div>\n          </div>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"absolute right-1 top-1 h-6 w-6 p-0 hover:bg-transparent\"\n            onClick={() => dismiss(toast.id)}\n          >\n            <X className=\"h-3 w-3\" />\n          </Button>\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAAA;;;AALA;;;;AAOO,SAAS;;IACd,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAEnC,qBACE,6LAAC;QAAI,WAAU;kBACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;gBAEC,WAAU;gBACV,OAAO;oBACL,iBAAiB,MAAM,OAAO,KAAK,gBAAgB,YAAY;oBAC/D,aAAa,MAAM,OAAO,KAAK,gBAAgB,YAAY;gBAC7D;;kCAEA,6LAAC;wBAAI,WAAU;;4BACZ,MAAM,OAAO,KAAK,8BACjB,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CAEzB,6LAAC;gCAAI,WAAU;;oCACZ,MAAM,KAAK,kBACV,6LAAC;wCAAI,WAAW,CAAC,sBAAsB,EACrC,MAAM,OAAO,KAAK,gBAAgB,iBAAiB,iBACnD;kDACC,MAAM,KAAK;;;;;;oCAGf,MAAM,WAAW,kBAChB,6LAAC;wCAAI,WAAW,CAAC,QAAQ,EACvB,MAAM,OAAO,KAAK,gBAAgB,iBAAiB,iBACnD;kDACC,MAAM,WAAW;;;;;;;;;;;;;;;;;;kCAK1B,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,QAAQ,MAAM,EAAE;kCAE/B,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;eApCV,MAAM,EAAE;;;;;;;;;;AA0CvB;GAjDgB;;QACc,+HAAA,CAAA,WAAQ;;;KADtB", "debugId": null}}, {"offset": {"line": 2889, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/toast-notification.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, createContext, useContext, ReactNode } from 'react'\nimport { createPortal } from 'react-dom'\nimport { X, CheckCircle, AlertTriangle, AlertCircle, Info } from 'lucide-react'\n\nexport type ToastType = 'success' | 'error' | 'warning' | 'info'\n\nexport interface Toast {\n  id: string\n  type: ToastType\n  title: string\n  description?: string\n  duration?: number\n  action?: {\n    label: string\n    onClick: () => void\n  }\n}\n\ninterface ToastContextType {\n  toasts: Toast[]\n  addToast: (toast: Omit<Toast, 'id'>) => void\n  removeToast: (id: string) => void\n  clearToasts: () => void\n}\n\nconst ToastContext = createContext<ToastContextType | undefined>(undefined)\n\nexport function useToast() {\n  const context = useContext(ToastContext)\n  if (!context) {\n    throw new Error('useToast must be used within a ToastProvider')\n  }\n  return context\n}\n\nconst toastConfig = {\n  success: {\n    icon: CheckCircle,\n    bgColor: 'bg-green-50',\n    borderColor: 'border-green-200',\n    iconColor: 'text-green-600',\n    titleColor: 'text-green-900',\n    descColor: 'text-green-700'\n  },\n  error: {\n    icon: AlertCircle,\n    bgColor: 'bg-red-50',\n    borderColor: 'border-red-200',\n    iconColor: 'text-red-600',\n    titleColor: 'text-red-900',\n    descColor: 'text-red-700'\n  },\n  warning: {\n    icon: AlertTriangle,\n    bgColor: 'bg-orange-50',\n    borderColor: 'border-orange-200',\n    iconColor: 'text-orange-600',\n    titleColor: 'text-orange-900',\n    descColor: 'text-orange-700'\n  },\n  info: {\n    icon: Info,\n    bgColor: 'bg-blue-50',\n    borderColor: 'border-blue-200',\n    iconColor: 'text-blue-600',\n    titleColor: 'text-blue-900',\n    descColor: 'text-blue-700'\n  }\n}\n\nfunction ToastItem({ toast, onRemove }: { toast: Toast; onRemove: (id: string) => void }) {\n  const [isVisible, setIsVisible] = useState(false)\n  const [isLeaving, setIsLeaving] = useState(false)\n  const config = toastConfig[toast.type]\n  const IconComponent = config.icon\n\n  useEffect(() => {\n    // Animazione di entrata\n    const timer = setTimeout(() => setIsVisible(true), 50)\n    return () => clearTimeout(timer)\n  }, [])\n\n  useEffect(() => {\n    if (toast.duration !== 0) {\n      const timer = setTimeout(() => {\n        handleRemove()\n      }, toast.duration || 5000)\n      return () => clearTimeout(timer)\n    }\n  }, [toast.duration])\n\n  const handleRemove = () => {\n    setIsLeaving(true)\n    setTimeout(() => onRemove(toast.id), 300)\n  }\n\n  return (\n    <div\n      className={`\n        transform transition-all duration-300 ease-in-out mb-3\n        ${isVisible && !isLeaving \n          ? 'translate-x-0 opacity-100 scale-100' \n          : 'translate-x-full opacity-0 scale-95'\n        }\n      `}\n    >\n      <div className={`\n        max-w-sm w-full ${config.bgColor} ${config.borderColor} border-l-4 \n        rounded-lg shadow-lg p-4 pointer-events-auto\n      `}>\n        <div className=\"flex items-start\">\n          <div className=\"flex-shrink-0\">\n            <IconComponent className={`w-5 h-5 ${config.iconColor}`} />\n          </div>\n          \n          <div className=\"ml-3 flex-1\">\n            <p className={`text-sm font-medium ${config.titleColor}`}>\n              {toast.title}\n            </p>\n            {toast.description && (\n              <p className={`mt-1 text-sm ${config.descColor}`}>\n                {toast.description}\n              </p>\n            )}\n            {toast.action && (\n              <div className=\"mt-3\">\n                <button\n                  onClick={toast.action.onClick}\n                  className={`\n                    text-sm font-medium ${config.iconColor} \n                    hover:underline focus:outline-none focus:underline\n                  `}\n                >\n                  {toast.action.label}\n                </button>\n              </div>\n            )}\n          </div>\n          \n          <div className=\"ml-4 flex-shrink-0\">\n            <button\n              onClick={handleRemove}\n              className={`\n                inline-flex ${config.descColor} hover:${config.titleColor} \n                focus:outline-none focus:${config.titleColor} transition-colors\n              `}\n            >\n              <X className=\"w-4 h-4\" />\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\nfunction ToastContainer({ toasts, removeToast }: { toasts: Toast[]; removeToast: (id: string) => void }) {\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) return null\n\n  return createPortal(\n    <div className=\"fixed top-4 right-4 z-50 pointer-events-none\">\n      <div className=\"flex flex-col-reverse\">\n        {toasts.map((toast) => (\n          <ToastItem\n            key={toast.id}\n            toast={toast}\n            onRemove={removeToast}\n          />\n        ))}\n      </div>\n    </div>,\n    document.body\n  )\n}\n\nexport function ToastProvider({ children }: { children: ReactNode }) {\n  const [toasts, setToasts] = useState<Toast[]>([])\n\n  const addToast = (toast: Omit<Toast, 'id'>) => {\n    const id = Math.random().toString(36).substr(2, 9)\n    setToasts(prev => [...prev, { ...toast, id }])\n  }\n\n  const removeToast = (id: string) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id))\n  }\n\n  const clearToasts = () => {\n    setToasts([])\n  }\n\n  return (\n    <ToastContext.Provider value={{ toasts, addToast, removeToast, clearToasts }}>\n      {children}\n      <ToastContainer toasts={toasts} removeToast={removeToast} />\n    </ToastContext.Provider>\n  )\n}\n\n// Hook di convenienza per toast comuni\nexport function useToastActions() {\n  const { addToast } = useToast()\n\n  return {\n    success: (title: string, description?: string, action?: Toast['action']) =>\n      addToast({ type: 'success', title, description, action }),\n    \n    error: (title: string, description?: string, action?: Toast['action']) =>\n      addToast({ type: 'error', title, description, action, duration: 7000 }),\n    \n    warning: (title: string, description?: string, action?: Toast['action']) =>\n      addToast({ type: 'warning', title, description, action, duration: 6000 }),\n    \n    info: (title: string, description?: string, action?: Toast['action']) =>\n      addToast({ type: 'info', title, description, action }),\n\n    // Toast specifici per azioni sui cavi\n    cavoDisconnected: (cavoId: string) =>\n      addToast({\n        type: 'success',\n        title: 'Cavo Scollegato',\n        description: `Il cavo ${cavoId} è stato scollegato con successo.`\n      }),\n\n    pdfGenerated: (fileName: string, cavoId: string) =>\n      addToast({\n        type: 'success',\n        title: 'PDF Generato',\n        description: `Certificato per il cavo ${cavoId} salvato come ${fileName}.`,\n        action: {\n          label: 'Apri Cartella',\n          onClick: () => {\n            // Logica per aprire la cartella dei download\n            console.log('Apertura cartella download...')\n          }\n        }\n      }),\n\n    certificationError: (cavoId: string, reason: string) =>\n      addToast({\n        type: 'error',\n        title: 'Certificazione Fallita',\n        description: `Impossibile certificare il cavo ${cavoId}: ${reason}`,\n        duration: 8000\n      }),\n\n    actionInProgress: (action: string, cavoId: string) =>\n      addToast({\n        type: 'info',\n        title: `${action} in Corso`,\n        description: `Elaborazione del cavo ${cavoId}...`,\n        duration: 3000\n      })\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AA2BA,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANgB;AAQhB,MAAM,cAAc;IAClB,SAAS;QACP,MAAM,8NAAA,CAAA,cAAW;QACjB,SAAS;QACT,aAAa;QACb,WAAW;QACX,YAAY;QACZ,WAAW;IACb;IACA,OAAO;QACL,MAAM,uNAAA,CAAA,cAAW;QACjB,SAAS;QACT,aAAa;QACb,WAAW;QACX,YAAY;QACZ,WAAW;IACb;IACA,SAAS;QACP,MAAM,2NAAA,CAAA,gBAAa;QACnB,SAAS;QACT,aAAa;QACb,WAAW;QACX,YAAY;QACZ,WAAW;IACb;IACA,MAAM;QACJ,MAAM,qMAAA,CAAA,OAAI;QACV,SAAS;QACT,aAAa;QACb,WAAW;QACX,YAAY;QACZ,WAAW;IACb;AACF;AAEA,SAAS,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAoD;;IACtF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,WAAW,CAAC,MAAM,IAAI,CAAC;IACtC,MAAM,gBAAgB,OAAO,IAAI;IAEjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,wBAAwB;YACxB,MAAM,QAAQ;6CAAW,IAAM,aAAa;4CAAO;YACnD;uCAAO,IAAM,aAAa;;QAC5B;8BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,MAAM,QAAQ,KAAK,GAAG;gBACxB,MAAM,QAAQ;iDAAW;wBACvB;oBACF;gDAAG,MAAM,QAAQ,IAAI;gBACrB;2CAAO,IAAM,aAAa;;YAC5B;QACF;8BAAG;QAAC,MAAM,QAAQ;KAAC;IAEnB,MAAM,eAAe;QACnB,aAAa;QACb,WAAW,IAAM,SAAS,MAAM,EAAE,GAAG;IACvC;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC;;QAEV,EAAE,aAAa,CAAC,YACZ,wCACA,sCACH;MACH,CAAC;kBAED,cAAA,6LAAC;YAAI,WAAW,CAAC;wBACC,EAAE,OAAO,OAAO,CAAC,CAAC,EAAE,OAAO,WAAW,CAAC;;MAEzD,CAAC;sBACC,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAc,WAAW,CAAC,QAAQ,EAAE,OAAO,SAAS,EAAE;;;;;;;;;;;kCAGzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAW,CAAC,oBAAoB,EAAE,OAAO,UAAU,EAAE;0CACrD,MAAM,KAAK;;;;;;4BAEb,MAAM,WAAW,kBAChB,6LAAC;gCAAE,WAAW,CAAC,aAAa,EAAE,OAAO,SAAS,EAAE;0CAC7C,MAAM,WAAW;;;;;;4BAGrB,MAAM,MAAM,kBACX,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS,MAAM,MAAM,CAAC,OAAO;oCAC7B,WAAW,CAAC;wCACU,EAAE,OAAO,SAAS,CAAC;;kBAEzC,CAAC;8CAEA,MAAM,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;kCAM3B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,SAAS;4BACT,WAAW,CAAC;4BACE,EAAE,OAAO,SAAS,CAAC,OAAO,EAAE,OAAO,UAAU,CAAC;yCACjC,EAAE,OAAO,UAAU,CAAC;cAC/C,CAAC;sCAED,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B;IApFS;KAAA;AAsFT,SAAS,eAAe,EAAE,MAAM,EAAE,WAAW,EAA0D;;IACrG,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,WAAW;QACb;mCAAG,EAAE;IAEL,IAAI,CAAC,SAAS,OAAO;IAErB,qBAAO,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,gBAChB,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;oBAEC,OAAO;oBACP,UAAU;mBAFL,MAAM,EAAE;;;;;;;;;;;;;;cAOrB,SAAS,IAAI;AAEjB;IAvBS;MAAA;AAyBF,SAAS,cAAc,EAAE,QAAQ,EAA2B;;IACjE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAEhD,MAAM,WAAW,CAAC;QAChB,MAAM,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QAChD,UAAU,CAAA,OAAQ;mBAAI;gBAAM;oBAAE,GAAG,KAAK;oBAAE;gBAAG;aAAE;IAC/C;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,MAAM,cAAc;QAClB,UAAU,EAAE;IACd;IAEA,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAQ;YAAU;YAAa;QAAY;;YACxE;0BACD,6LAAC;gBAAe,QAAQ;gBAAQ,aAAa;;;;;;;;;;;;AAGnD;IAtBgB;MAAA;AAyBT,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG;IAErB,OAAO;QACL,SAAS,CAAC,OAAe,aAAsB,SAC7C,SAAS;gBAAE,MAAM;gBAAW;gBAAO;gBAAa;YAAO;QAEzD,OAAO,CAAC,OAAe,aAAsB,SAC3C,SAAS;gBAAE,MAAM;gBAAS;gBAAO;gBAAa;gBAAQ,UAAU;YAAK;QAEvE,SAAS,CAAC,OAAe,aAAsB,SAC7C,SAAS;gBAAE,MAAM;gBAAW;gBAAO;gBAAa;gBAAQ,UAAU;YAAK;QAEzE,MAAM,CAAC,OAAe,aAAsB,SAC1C,SAAS;gBAAE,MAAM;gBAAQ;gBAAO;gBAAa;YAAO;QAEtD,sCAAsC;QACtC,kBAAkB,CAAC,SACjB,SAAS;gBACP,MAAM;gBACN,OAAO;gBACP,aAAa,CAAC,QAAQ,EAAE,OAAO,iCAAiC,CAAC;YACnE;QAEF,cAAc,CAAC,UAAkB,SAC/B,SAAS;gBACP,MAAM;gBACN,OAAO;gBACP,aAAa,CAAC,wBAAwB,EAAE,OAAO,cAAc,EAAE,SAAS,CAAC,CAAC;gBAC1E,QAAQ;oBACN,OAAO;oBACP,SAAS;wBACP,6CAA6C;wBAC7C,QAAQ,GAAG,CAAC;oBACd;gBACF;YACF;QAEF,oBAAoB,CAAC,QAAgB,SACnC,SAAS;gBACP,MAAM;gBACN,OAAO;gBACP,aAAa,CAAC,gCAAgC,EAAE,OAAO,EAAE,EAAE,QAAQ;gBACnE,UAAU;YACZ;QAEF,kBAAkB,CAAC,QAAgB,SACjC,SAAS;gBACP,MAAM;gBACN,OAAO,GAAG,OAAO,SAAS,CAAC;gBAC3B,aAAa,CAAC,sBAAsB,EAAE,OAAO,GAAG,CAAC;gBACjD,UAAU;YACZ;IACJ;AACF;IAtDgB;;QACO", "debugId": null}}]}