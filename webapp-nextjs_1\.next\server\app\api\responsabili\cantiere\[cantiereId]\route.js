(()=>{var e={};e.id=854,e.ids=[854],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},46298:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>d});var n={};t.r(n),t.d(n,{GET:()=>l,POST:()=>p});var s=t(96559),o=t(48088),a=t(37719),i=t(32190);async function l(e,{params:r}){try{let e=parseInt(r.cantiereId);if(isNaN(e))return i.NextResponse.json({error:"ID cantiere non valido"},{status:400});let t=[{id_responsabile:1,nome_responsabile:"Mario Rossi",telefono:"+39 ************",email:"<EMAIL>",experience_level:"Senior",id_cantiere:e,data_creazione:"2024-01-15T10:00:00Z",attivo:!0},{id_responsabile:2,nome_responsabile:"Luigi Verdi",telefono:"+39 ************",email:"<EMAIL>",experience_level:"Senior",id_cantiere:e,data_creazione:"2024-01-20T14:30:00Z",attivo:!0},{id_responsabile:3,nome_responsabile:"Anna Bianchi",telefono:"+39 ************",email:"<EMAIL>",experience_level:"Junior",id_cantiere:e,data_creazione:"2024-02-01T09:15:00Z",attivo:!0}];return i.NextResponse.json({success:!0,data:t,total:t.length})}catch(e){return console.error("Errore nel recupero responsabili:",e),i.NextResponse.json({error:"Errore interno del server"},{status:500})}}async function p(e,{params:r}){try{let t=parseInt(r.cantiereId),n=await e.json();if(isNaN(t))return i.NextResponse.json({error:"ID cantiere non valido"},{status:400});if(!n.nome_responsabile)return i.NextResponse.json({error:"Nome responsabile obbligatorio"},{status:400});let s={id_responsabile:Math.floor(1e3*Math.random())+100,nome_responsabile:n.nome_responsabile,telefono:n.telefono||null,email:n.email||null,experience_level:n.experience_level||"Senior",id_cantiere:t,data_creazione:new Date().toISOString(),attivo:!0};return i.NextResponse.json({success:!0,data:s},{status:201})}catch(e){return console.error("Errore nella creazione responsabile:",e),i.NextResponse.json({error:"Errore interno del server"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/responsabili/cantiere/[cantiereId]/route",pathname:"/api/responsabili/cantiere/[cantiereId]",filename:"route",bundlePath:"app/api/responsabili/cantiere/[cantiereId]/route"},resolvedPagePath:"C:\\CMS\\webapp-nextjs_1\\src\\app\\api\\responsabili\\cantiere\\[cantiereId]\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:u,workUnitAsyncStorage:d,serverHooks:x}=c;function m(){return(0,a.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:d})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[4447,580],()=>t(46298));module.exports=n})();