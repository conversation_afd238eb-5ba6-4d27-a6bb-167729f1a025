'use client'

import { useState } from 'react'
import { MoreHorizontal, Edit, Clock, CheckCircle, Trash2 } from 'lucide-react'

interface InlineActionsMenuProps {
  user: {
    id_utente: number
    ruolo: string
    abilitato: boolean
  }
  onEdit: () => void
  onToggleStatus: () => void
  onDelete: () => void
}

export default function InlineActionsMenu({ 
  user, 
  onEdit, 
  onToggleStatus, 
  onDelete 
}: InlineActionsMenuProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  if (isExpanded) {
    return (
      <div className="flex items-center gap-1">
        {/* Icone espanse */}
        <button
          onClick={onEdit}
          className="p-1 rounded hover:bg-blue-50 transition-colors"
          title="Modifica"
        >
          <Edit className="h-3.5 w-3.5 text-slate-600 hover:text-blue-600" />
        </button>
        
        <button
          onClick={onToggleStatus}
          disabled={user.ruolo === 'owner'}
          className={`p-1 rounded transition-colors ${
            user.ruolo === 'owner' 
              ? 'opacity-50 cursor-not-allowed' 
              : 'hover:bg-slate-50'
          }`}
          title={user.abilitato ? 'Disabilita' : 'Abilita'}
        >
          {user.abilitato ? (
            <Clock className="h-3.5 w-3.5 text-red-500" />
          ) : (
            <CheckCircle className="h-3.5 w-3.5 text-green-500" />
          )}
        </button>
        
        <button
          onClick={onDelete}
          disabled={user.ruolo === 'owner'}
          className={`p-1 rounded transition-colors ${
            user.ruolo === 'owner' 
              ? 'opacity-50 cursor-not-allowed' 
              : 'hover:bg-red-50'
          }`}
          title="Elimina"
        >
          <Trash2 className="h-3.5 w-3.5 text-red-500" />
        </button>
        
        {/* Pulsante per comprimere */}
        <button
          onClick={() => setIsExpanded(false)}
          className="p-1 rounded hover:bg-slate-100 transition-colors ml-1"
          title="Comprimi"
        >
          <MoreHorizontal className="h-3 w-3 text-slate-400" />
        </button>
      </div>
    )
  }

  return (
    <button
      onClick={() => setIsExpanded(true)}
      className="p-1 rounded hover:bg-slate-100 transition-colors"
      title="Espandi azioni"
    >
      <MoreHorizontal className="h-3 w-3 text-slate-600" />
    </button>
  )
}
