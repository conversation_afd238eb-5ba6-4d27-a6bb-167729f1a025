{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_1/src/app/api/auth/login/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Estrai i dati - supporta sia JSON che FormData\n    let username: string\n    let password: string\n\n    const contentType = request.headers.get('content-type')\n\n    if (contentType?.includes('application/json')) {\n      const body = await request.json()\n      username = body.username\n      password = body.password\n    } else {\n      const formData = await request.formData()\n      username = formData.get('username') as string\n      password = formData.get('password') as string\n    }\n\n    if (!username || !password) {\n      return NextResponse.json(\n        {\n          detail: 'Username e password sono richiesti'\n        },\n        { status: 400 }\n      )\n    }\n\n    // Proxy la richiesta al backend FastAPI\n    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'\n    \n    // Crea FormData per il backend\n    const backendFormData = new FormData()\n    backendFormData.append('username', username)\n    backendFormData.append('password', password)\n\n    console.log('🔄 Auth API: Proxying login request to backend:', `${backendUrl}/api/auth/login`)\n    \n    const response = await fetch(`${backendUrl}/api/auth/login`, {\n      method: 'POST',\n      body: backendFormData\n    })\n\n    console.log('📡 Auth API: Backend response status:', response.status)\n\n    const data = await response.json()\n    console.log('📡 Auth API: Backend response data:', data)\n\n    return NextResponse.json(data, { \n      status: response.status,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    })\n\n  } catch (error) {\n    console.error('❌ Auth API: Login error:', error)\n    return NextResponse.json(\n      { \n        detail: 'Errore interno del server' \n      }, \n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,iDAAiD;QACjD,IAAI;QACJ,IAAI;QAEJ,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC;QAExC,IAAI,aAAa,SAAS,qBAAqB;YAC7C,MAAM,OAAO,MAAM,QAAQ,IAAI;YAC/B,WAAW,KAAK,QAAQ;YACxB,WAAW,KAAK,QAAQ;QAC1B,OAAO;YACL,MAAM,WAAW,MAAM,QAAQ,QAAQ;YACvC,WAAW,SAAS,GAAG,CAAC;YACxB,WAAW,SAAS,GAAG,CAAC;QAC1B;QAEA,IAAI,CAAC,YAAY,CAAC,UAAU;YAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,QAAQ;YACV,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,wCAAwC;QACxC,MAAM,aAAa,6DAAmC;QAEtD,+BAA+B;QAC/B,MAAM,kBAAkB,IAAI;QAC5B,gBAAgB,MAAM,CAAC,YAAY;QACnC,gBAAgB,MAAM,CAAC,YAAY;QAEnC,QAAQ,GAAG,CAAC,mDAAmD,GAAG,WAAW,eAAe,CAAC;QAE7F,MAAM,WAAW,MAAM,MAAM,GAAG,WAAW,eAAe,CAAC,EAAE;YAC3D,QAAQ;YACR,MAAM;QACR;QAEA,QAAQ,GAAG,CAAC,yCAAyC,SAAS,MAAM;QAEpE,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,uCAAuC;QAEnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;YAC7B,QAAQ,SAAS,MAAM;YACvB,SAAS;gBACP,gBAAgB;YAClB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,QAAQ;QACV,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}