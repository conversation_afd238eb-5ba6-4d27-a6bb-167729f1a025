(()=>{var e={};e.id=2313,e.ids=[2313],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45575:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>u,serverHooks:()=>l,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>c});var s={};r.r(s),r.d(s,{POST:()=>p});var a=r(96559),o=r(48088),n=r(37719),i=r(32190);async function p(e){try{let t=await e.json(),r=await fetch("http://localhost:8001/api/password/verify-reset-token",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),s=await r.json();return i.NextResponse.json(s,{status:r.status,headers:{"Content-Type":"application/json"}})}catch(e){return i.NextResponse.json({success:!1,detail:"Errore interno del server"},{status:500})}}let u=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/password/verify-reset-token/route",pathname:"/api/password/verify-reset-token",filename:"route",bundlePath:"app/api/password/verify-reset-token/route"},resolvedPagePath:"C:\\CMS\\webapp-nextjs_1\\src\\app\\api\\password\\verify-reset-token\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:c,serverHooks:l}=u;function x(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:c})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(45575));module.exports=s})();