'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { usersApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'
import { User } from '@/types'
import { LogIn, Loader2 } from 'lucide-react'

export default function QuickImpersonate() {
  const router = useRouter()
  const { impersonateUser } = useAuth()
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(false)
  const [loadingUsers, setLoadingUsers] = useState(true)

  // Carica gli utenti all'avvio
  useEffect(() => {
    loadUsers()
  }, [])

  const loadUsers = async () => {
    try {
      setLoadingUsers(true)
      const usersData = await usersApi.getUsers()
      // Filtra solo utenti abilitati e non owner
      const availableUsers = usersData.filter(user => 
        user.abilitato && user.ruolo !== 'owner'
      )
      setUsers(availableUsers)
    } catch (error) {
    } finally {
      setLoadingUsers(false)
    }
  }

  const handleImpersonate = async (userId: string) => {
    if (!userId) return

    setLoading(true)
    try {
      const selectedUser = users.find(user => user.id_utente === parseInt(userId))
      if (!selectedUser) return

      await impersonateUser(parseInt(userId))

      // Reindirizza in base al ruolo dell'utente impersonato
      if (selectedUser.ruolo === 'user') {
        router.push('/cantieri')
      } else if (selectedUser.ruolo === 'cantieri_user') {
        router.push('/cavi')
      } else {
        router.push('/')
      }
    } catch (error) {
    } finally {
      setLoading(false)
    }
  }

  const getRuoloBadge = (ruolo: string) => {
    switch (ruolo) {
      case 'user':
        return <Badge className="bg-blue-100 text-blue-800 text-xs">User</Badge>
      case 'cantieri_user':
        return <Badge className="bg-green-100 text-green-800 text-xs">Cantieri</Badge>
      default:
        return <Badge variant="secondary" className="text-xs">{ruolo}</Badge>
    }
  }

  if (loadingUsers) {
    return (
      <div className="flex items-center text-slate-500">
        <Loader2 className="h-4 w-4 animate-spin" />
      </div>
    )
  }

  return (
    <div className="flex items-center">
      <Select onValueChange={handleImpersonate} disabled={loading}>
        <SelectTrigger className="w-[200px] h-8 text-xs border-slate-300 bg-white">
          <div className="flex items-center gap-2">
            <LogIn className="h-3 w-3 text-slate-500" />
            <SelectValue placeholder="Accedi come..." />
          </div>
        </SelectTrigger>
        <SelectContent>
          {users.length === 0 ? (
            <SelectItem value="none" disabled>
              Nessun utente disponibile
            </SelectItem>
          ) : (
            users.map((user) => (
              <SelectItem key={user.id_utente} value={user.id_utente.toString()}>
                <div className="flex items-center justify-between w-full">
                  <div className="flex flex-col">
                    <span className="font-medium text-sm">{user.username}</span>
                    <span className="text-xs text-slate-500">{user.ragione_sociale || 'N/A'}</span>
                  </div>
                  <div className="ml-3">
                    {getRuoloBadge(user.ruolo)}
                  </div>
                </div>
              </SelectItem>
            ))
          )}
        </SelectContent>
      </Select>
      {loading && (
        <Loader2 className="h-4 w-4 animate-spin ml-2 text-blue-600" />
      )}
    </div>
  )
}
