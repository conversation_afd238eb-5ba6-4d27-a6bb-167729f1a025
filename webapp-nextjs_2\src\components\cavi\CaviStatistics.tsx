'use client'

import { useMemo, useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Cable,
  CheckCircle,
  Clock,
  AlertTriangle,
  Zap,
  Package,
  BarChart3,
  Filter,
  X,
  TrendingUp,
  Activity
} from 'lucide-react'
import { Cavo } from '@/types'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'

export type FilterType = 'all' | 'installati' | 'in_corso' | 'da_installare' | 'collegati' | 'certificati'

interface CaviStatisticsProps {
  cavi: Cavo[]
  filteredCavi: Cavo[]
  className?: string
  revisioneCorrente?: string
  activeFilter?: FilterType
  onFilterChange?: (filter: FilterType) => void
}

export default function CaviStatistics({
  cavi,
  filteredCavi,
  className,
  revisioneCorrente,
  activeFilter = 'all',
  onFilterChange
}: CaviStatisticsProps) {
  const stats = useMemo(() => {
    const totalCavi = cavi.length
    const filteredCount = filteredCavi.length
    
    // Installation status
    const installati = filteredCavi.filter(c => 
      c.stato_installazione === 'Installato' || 
      (c.metri_posati && c.metri_posati > 0) ||
      (c.metratura_reale && c.metratura_reale > 0)
    ).length
    
    const inCorso = filteredCavi.filter(c => 
      c.stato_installazione === 'In corso'
    ).length
    
    const daInstallare = filteredCount - installati - inCorso
    
    // Connection status
    const collegati = filteredCavi.filter(c => {
      const collegamento = c.collegamento || c.collegamenti || 0
      return collegamento === 3 // Both sides connected
    }).length
    
    const parzialmenteCollegati = filteredCavi.filter(c => {
      const collegamento = c.collegamento || c.collegamenti || 0
      return collegamento === 1 || collegamento === 2 // One side connected
    }).length
    
    const nonCollegati = filteredCavi.filter(c => {
      const collegamento = c.collegamento || c.collegamenti || 0
      return collegamento === 0 && (c.metri_posati > 0 || c.metratura_reale > 0)
    }).length
    
    // Certification status
    const certificati = filteredCavi.filter(c => 
      c.certificato === true || 
      c.certificato === 'SI' || 
      c.certificato === 'CERTIFICATO'
    ).length
    
    // Meters calculation
    const metriTotali = filteredCavi.reduce((sum, c) => sum + (c.metri_teorici || 0), 0)
    const metriInstallati = filteredCavi.reduce((sum, c) => {
      const metri = c.metri_posati || c.metratura_reale || 0
      return sum + metri
    }, 0)

    // Calcolo IAP (Indice di Avanzamento Ponderato) come nella webapp originale
    const calculateIAP = (nTot: number, nInst: number, nColl: number, nCert: number): number => {
      // Pesi per le fasi del progetto
      const Wp = 2.0  // Peso fase Posa
      const Wc = 1.5  // Peso fase Collegamento
      const Wz = 0.5  // Peso fase Certificazione

      // Se non ci sono cavi, ritorna 0
      if (nTot === 0) return 0

      // Calcolo del numeratore (Sforzo Completato)
      const sforzoSoloInstallati = (nInst - nColl) * Wp
      const sforzoSoloCollegati = (nColl - nCert) * (Wp + Wc)
      const sforzoCertificati = nCert * (Wp + Wc + Wz)
      const numeratore = sforzoSoloInstallati + sforzoSoloCollegati + sforzoCertificati

      // Calcolo del denominatore (Sforzo Massimo Previsto)
      const denominatore = nTot * (Wp + Wc + Wz)

      // Calcolo finale dell'IAP in percentuale
      const iap = (numeratore / denominatore) * 100

      return Math.round(iap * 100) / 100 // Arrotonda a 2 decimali
    }

    const percentualeInstallazione = calculateIAP(filteredCount, installati, collegati, certificati)
    
    return {
      totalCavi,
      filteredCount,
      installati,
      inCorso,
      daInstallare,
      collegati,
      parzialmenteCollegati,
      nonCollegati,
      certificati,
      metriTotali,
      metriInstallati,
      percentualeInstallazione
    }
  }, [cavi, filteredCavi])

  // Componente KPI interattivo
  const InteractiveKPI = ({
    icon: Icon,
    label,
    value,
    total,
    color,
    filterType,
    tooltip,
    trend
  }: {
    icon: any;
    label: string;
    value: number;
    total?: number;
    color: string;
    filterType: FilterType;
    tooltip: string;
    trend?: number;
  }) => {
    const isActive = activeFilter === filterType
    const percentage = total ? Math.round((value / total) * 100) : 0

    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div
              onClick={() => onFilterChange?.(isActive ? 'all' : filterType)}
              className={`
                relative p-3 rounded-lg border-2 transition-all duration-200 cursor-pointer
                hover:scale-105 hover:shadow-md group
                ${isActive
                  ? `${color} border-current shadow-lg ring-2 ring-offset-2 ring-current/20`
                  : 'bg-white border-slate-200 hover:border-slate-300'
                }
              `}
            >
              {/* Indicatore filtro attivo */}
              {isActive && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-600 rounded-full flex items-center justify-center">
                  <Filter className="w-2 h-2 text-white" />
                </div>
              )}

              {/* Header con icona e trend */}
              <div className="flex items-center justify-between mb-2">
                <Icon className={`w-5 h-5 ${isActive ? 'text-white' : 'text-slate-600'}`} />
                {trend !== undefined && (
                  <div className={`flex items-center text-xs ${isActive ? 'text-white/80' : 'text-slate-500'}`}>
                    <TrendingUp className="w-3 h-3 mr-1" />
                    {trend > 0 ? '+' : ''}{trend}%
                  </div>
                )}
              </div>

              {/* Valore principale */}
              <div className={`text-2xl font-bold ${isActive ? 'text-white' : 'text-slate-900'}`}>
                {value}
              </div>

              {/* Label e percentuale */}
              <div className="flex items-center justify-between mt-1">
                <span className={`text-sm font-medium ${isActive ? 'text-white/90' : 'text-slate-600'}`}>
                  {label}
                </span>
                {total && (
                  <span className={`text-xs ${isActive ? 'text-white/80' : 'text-slate-500'}`}>
                    {percentage}%
                  </span>
                )}
              </div>

              {/* Barra di progresso */}
              {total && (
                <div className="mt-2 w-full bg-slate-200 rounded-full h-1.5">
                  <div
                    className={`h-1.5 rounded-full transition-all duration-300 ${
                      isActive ? 'bg-white/80' : color.replace('bg-', 'bg-').replace('text-', 'bg-')
                    }`}
                    style={{ width: `${Math.min(percentage, 100)}%` }}
                  />
                </div>
              )}

              {/* Effetto hover */}
              <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-transparent to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
            </div>
          </TooltipTrigger>
          <TooltipContent side="bottom" className="max-w-xs">
            <div className="text-center">
              <p className="font-medium">{tooltip}</p>
              {total && (
                <p className="text-xs text-slate-500 mt-1">
                  Clicca per filtrare • {value} di {total} cavi ({percentage}%)
                </p>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  }

  return (
    <Card className={className}>
      <CardContent className="p-1.5">
        {/* Header with revision and filter controls */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5 text-mariner-600" />
            <span className="text-lg font-semibold text-mariner-900">Statistiche Cavi</span>
            {activeFilter !== 'all' && (
              <Badge variant="secondary" className="bg-blue-100 text-blue-800 flex items-center gap-1">
                <Filter className="w-3 h-3" />
                Filtro attivo
                <button
                  onClick={() => onFilterChange?.('all')}
                  className="ml-1 hover:bg-blue-200 rounded-full p-0.5"
                >
                  <X className="w-3 h-3" />
                </button>
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            {revisioneCorrente && (
              <Badge variant="outline" className="text-sm font-medium">
                Rev. {revisioneCorrente}
              </Badge>
            )}
            <Badge variant="secondary" className="bg-slate-100 text-slate-700">
              {stats.filteredCount} di {stats.totalCavi} cavi
            </Badge>
          </div>
        </div>

        {/* KPI Interattivi */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">

          {/* KPI Installazione */}
          <InteractiveKPI
            icon={CheckCircle}
            label="Installati"
            value={stats.installati}
            total={stats.filteredCount}
            color="bg-green-500 text-white"
            filterType="installati"
            tooltip="Cavi completamente installati con metri posati registrati"
            trend={5}
          />

          <InteractiveKPI
            icon={Clock}
            label="In Corso"
            value={stats.inCorso}
            total={stats.filteredCount}
            color="bg-yellow-500 text-white"
            filterType="in_corso"
            tooltip="Cavi in fase di installazione"
            trend={-2}
          />

          <InteractiveKPI
            icon={AlertTriangle}
            label="Da Installare"
            value={stats.daInstallare}
            total={stats.filteredCount}
            color="bg-gray-500 text-white"
            filterType="da_installare"
            tooltip="Cavi non ancora iniziati"
            trend={-3}
          />

          {/* KPI Collegamento */}
          <InteractiveKPI
            icon={Zap}
            label="Collegati"
            value={stats.collegati}
            total={stats.filteredCount}
            color="bg-blue-500 text-white"
            filterType="collegati"
            tooltip="Cavi completamente collegati su entrambi i lati"
            trend={8}
          />

          {/* KPI Certificazione */}
          <InteractiveKPI
            icon={Package}
            label="Certificati"
            value={stats.certificati}
            total={stats.filteredCount}
            color="bg-purple-500 text-white"
            filterType="certificati"
            tooltip="Cavi con certificazione completata"
            trend={12}
          />

          {/* KPI Metri */}
          <div className="bg-gradient-to-br from-indigo-50 to-indigo-100 p-4 rounded-lg border border-indigo-200">
            <div className="flex items-center justify-between mb-2">
              <Activity className="w-5 h-5 text-indigo-600" />
              <span className="text-xs text-indigo-600 font-medium">
                {Math.round((stats.metriInstallati / stats.metriTotali) * 100)}%
              </span>
            </div>
            <div className="text-2xl font-bold text-indigo-900 mb-1">
              {stats.metriInstallati.toLocaleString()}m
            </div>
            <div className="text-sm text-indigo-700 mb-2">
              di {stats.metriTotali.toLocaleString()}m
            </div>
            <div className="w-full bg-indigo-200 rounded-full h-2">
              <div
                className="h-2 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-full transition-all duration-300"
                style={{ width: `${Math.min((stats.metriInstallati / stats.metriTotali) * 100, 100)}%` }}
              />
            </div>
          </div>

        </div>

        {/* IAP Progress bar - Versione Interattiva */}
        {stats.filteredCount > 0 && (
          <div className="mt-6 bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-xl border border-slate-200 shadow-sm">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5 text-slate-600" />
                <span className="text-sm font-semibold text-slate-700">IAP - Indice Avanzamento Ponderato</span>
              </div>
              <div className="flex items-center gap-2">
                <span className={`text-2xl font-bold ${
                  stats.percentualeInstallazione >= 80 ? 'text-emerald-600' :
                  stats.percentualeInstallazione >= 50 ? 'text-yellow-600' :
                  stats.percentualeInstallazione >= 25 ? 'text-orange-600' : 'text-red-600'
                }`}>
                  {stats.percentualeInstallazione.toFixed(1)}%
                </span>
                <div className={`w-3 h-3 rounded-full ${
                  stats.percentualeInstallazione >= 80 ? 'bg-emerald-500' :
                  stats.percentualeInstallazione >= 50 ? 'bg-yellow-500' :
                  stats.percentualeInstallazione >= 25 ? 'bg-orange-500' : 'bg-red-500'
                } animate-pulse`} />
              </div>
            </div>

            <div className="relative w-full bg-slate-200 rounded-full h-3 mb-3 overflow-hidden">
              <div
                className={`h-3 rounded-full transition-all duration-1000 ease-out relative ${
                  stats.percentualeInstallazione >= 80 ? 'bg-gradient-to-r from-emerald-400 via-emerald-500 to-emerald-600' :
                  stats.percentualeInstallazione >= 50 ? 'bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600' :
                  stats.percentualeInstallazione >= 25 ? 'bg-gradient-to-r from-orange-400 via-orange-500 to-orange-600' :
                  'bg-gradient-to-r from-red-400 via-red-500 to-red-600'
                }`}
                style={{ width: `${Math.min(stats.percentualeInstallazione, 100)}%` }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse" />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 text-xs">
              <div className="space-y-1">
                <div className="text-slate-600 font-medium">Composizione Pesi:</div>
                <div className="text-slate-500">
                  • Posa: <span className="font-semibold text-green-600">2.0</span><br/>
                  • Collegamento: <span className="font-semibold text-blue-600">1.5</span><br/>
                  • Certificazione: <span className="font-semibold text-purple-600">0.5</span>
                </div>
              </div>
              <div className="space-y-1">
                <div className="text-slate-600 font-medium">Stato Attuale:</div>
                <div className="text-slate-500">
                  • <span className="font-semibold text-green-600">{stats.installati}</span> Installati<br/>
                  • <span className="font-semibold text-blue-600">{stats.collegati}</span> Collegati<br/>
                  • <span className="font-semibold text-purple-600">{stats.certificati}</span> Certificati
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
