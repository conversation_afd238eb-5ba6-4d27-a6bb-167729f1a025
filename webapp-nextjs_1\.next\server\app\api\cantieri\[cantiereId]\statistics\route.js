(()=>{var e={};e.id=7202,e.ids=[7202],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29677:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>l,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>d});var r={};s.r(r),s.d(r,{GET:()=>c});var a=s(96559),i=s(48088),n=s(37719),o=s(32190);async function c(e,{params:t}){try{let s=t.cantiereId,r=e.headers.get("authorization");if(!r||!r.startsWith("Bearer "))return o.NextResponse.json({detail:"Token di autorizzazione mancante"},{status:401});let a="http://localhost:8001";console.log("\uD83D\uDD04 Statistics API: Proxying request to backend:",`${a}/api/cantieri/${s}/statistics`);let i=await fetch(`${a}/api/cantieri/${s}/statistics`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:r}});if(console.log("\uD83D\uDCE1 Statistics API: Backend response status:",i.status),!i.ok){let e=await i.json().catch(()=>({detail:"Errore sconosciuto"}));return console.error("❌ Statistics API: Backend error:",e),o.NextResponse.json(e,{status:i.status})}let n=await i.json();return console.log("\uD83D\uDCE1 Statistics API: Backend response data:",n),o.NextResponse.json(n,{status:i.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("❌ Statistics API: Error:",e),o.NextResponse.json({detail:"Errore interno del server"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/cantieri/[cantiereId]/statistics/route",pathname:"/api/cantieri/[cantiereId]/statistics",filename:"route",bundlePath:"app/api/cantieri/[cantiereId]/statistics/route"},resolvedPagePath:"C:\\CMS\\webapp-nextjs_1\\src\\app\\api\\cantieri\\[cantiereId]\\statistics\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:u,workUnitAsyncStorage:d,serverHooks:l}=p;function x(){return(0,n.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:d})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580],()=>s(29677));module.exports=r})();