"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7955],{381:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(19946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},6740:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(19946).A)("calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]])},9428:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(19946).A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},12767:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(19946).A)("chevrons-right",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]])},13052:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(19946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},17649:(e,r,t)=>{t.d(r,{UC:()=>L,VY:()=>z,ZD:()=>G,ZL:()=>I,bL:()=>F,hE:()=>T,hJ:()=>q,l9:()=>N,rc:()=>O});var a=t(12115),o=t(46081),n=t(6101),l=t(15452),i=t(85185),s=t(99708),d=t(95155),u="AlertDialog",[c,p]=(0,o.A)(u,[l.Hs]),f=(0,l.Hs)(),h=e=>{let{__scopeAlertDialog:r,...t}=e,a=f(r);return(0,d.jsx)(l.bL,{...a,...t,modal:!0})};h.displayName=u;var v=a.forwardRef((e,r)=>{let{__scopeAlertDialog:t,...a}=e,o=f(t);return(0,d.jsx)(l.l9,{...o,...a,ref:r})});v.displayName="AlertDialogTrigger";var y=e=>{let{__scopeAlertDialog:r,...t}=e,a=f(r);return(0,d.jsx)(l.ZL,{...a,...t})};y.displayName="AlertDialogPortal";var m=a.forwardRef((e,r)=>{let{__scopeAlertDialog:t,...a}=e,o=f(t);return(0,d.jsx)(l.hJ,{...o,...a,ref:r})});m.displayName="AlertDialogOverlay";var x="AlertDialogContent",[g,k]=c(x),A=(0,s.Dc)("AlertDialogContent"),w=a.forwardRef((e,r)=>{let{__scopeAlertDialog:t,children:o,...s}=e,u=f(t),c=a.useRef(null),p=(0,n.s)(r,c),h=a.useRef(null);return(0,d.jsx)(l.G$,{contentName:x,titleName:b,docsSlug:"alert-dialog",children:(0,d.jsx)(g,{scope:t,cancelRef:h,children:(0,d.jsxs)(l.UC,{role:"alertdialog",...u,...s,ref:p,onOpenAutoFocus:(0,i.m)(s.onOpenAutoFocus,e=>{var r;e.preventDefault(),null==(r=h.current)||r.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,d.jsx)(A,{children:o}),(0,d.jsx)(P,{contentRef:c})]})})})});w.displayName=x;var b="AlertDialogTitle",R=a.forwardRef((e,r)=>{let{__scopeAlertDialog:t,...a}=e,o=f(t);return(0,d.jsx)(l.hE,{...o,...a,ref:r})});R.displayName=b;var j="AlertDialogDescription",C=a.forwardRef((e,r)=>{let{__scopeAlertDialog:t,...a}=e,o=f(t);return(0,d.jsx)(l.VY,{...o,...a,ref:r})});C.displayName=j;var D=a.forwardRef((e,r)=>{let{__scopeAlertDialog:t,...a}=e,o=f(t);return(0,d.jsx)(l.bm,{...o,...a,ref:r})});D.displayName="AlertDialogAction";var E="AlertDialogCancel",M=a.forwardRef((e,r)=>{let{__scopeAlertDialog:t,...a}=e,{cancelRef:o}=k(E,t),i=f(t),s=(0,n.s)(r,o);return(0,d.jsx)(l.bm,{...i,...a,ref:s})});M.displayName=E;var P=e=>{let{contentRef:r}=e,t="`".concat(x,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(x,"` by passing a `").concat(j,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(x,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return a.useEffect(()=>{var e;document.getElementById(null==(e=r.current)?void 0:e.getAttribute("aria-describedby"))||console.warn(t)},[t,r]),null},F=h,N=v,I=y,q=m,L=w,O=D,G=M,T=R,z=C},18979:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(19946).A)("square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]])},19145:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(19946).A)("square-check-big",[["path",{d:"M21 10.656V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.344",key:"2acyp4"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},20547:(e,r,t)=>{t.d(r,{UC:()=>K,ZL:()=>H,bL:()=>V,l9:()=>B});var a=t(12115),o=t(85185),n=t(6101),l=t(46081),i=t(19178),s=t(92293),d=t(25519),u=t(61285),c=t(35152),p=t(34378),f=t(28905),h=t(63655),v=t(99708),y=t(5845),m=t(38168),x=t(93795),g=t(95155),k="Popover",[A,w]=(0,l.A)(k,[c.Bk]),b=(0,c.Bk)(),[R,j]=A(k),C=e=>{let{__scopePopover:r,children:t,open:o,defaultOpen:n,onOpenChange:l,modal:i=!1}=e,s=b(r),d=a.useRef(null),[p,f]=a.useState(!1),[h,v]=(0,y.i)({prop:o,defaultProp:null!=n&&n,onChange:l,caller:k});return(0,g.jsx)(c.bL,{...s,children:(0,g.jsx)(R,{scope:r,contentId:(0,u.B)(),triggerRef:d,open:h,onOpenChange:v,onOpenToggle:a.useCallback(()=>v(e=>!e),[v]),hasCustomAnchor:p,onCustomAnchorAdd:a.useCallback(()=>f(!0),[]),onCustomAnchorRemove:a.useCallback(()=>f(!1),[]),modal:i,children:t})})};C.displayName=k;var D="PopoverAnchor";a.forwardRef((e,r)=>{let{__scopePopover:t,...o}=e,n=j(D,t),l=b(t),{onCustomAnchorAdd:i,onCustomAnchorRemove:s}=n;return a.useEffect(()=>(i(),()=>s()),[i,s]),(0,g.jsx)(c.Mz,{...l,...o,ref:r})}).displayName=D;var E="PopoverTrigger",M=a.forwardRef((e,r)=>{let{__scopePopover:t,...a}=e,l=j(E,t),i=b(t),s=(0,n.s)(r,l.triggerRef),d=(0,g.jsx)(h.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":S(l.open),...a,ref:s,onClick:(0,o.m)(e.onClick,l.onOpenToggle)});return l.hasCustomAnchor?d:(0,g.jsx)(c.Mz,{asChild:!0,...i,children:d})});M.displayName=E;var P="PopoverPortal",[F,N]=A(P,{forceMount:void 0}),I=e=>{let{__scopePopover:r,forceMount:t,children:a,container:o}=e,n=j(P,r);return(0,g.jsx)(F,{scope:r,forceMount:t,children:(0,g.jsx)(f.C,{present:t||n.open,children:(0,g.jsx)(p.Z,{asChild:!0,container:o,children:a})})})};I.displayName=P;var q="PopoverContent",L=a.forwardRef((e,r)=>{let t=N(q,e.__scopePopover),{forceMount:a=t.forceMount,...o}=e,n=j(q,e.__scopePopover);return(0,g.jsx)(f.C,{present:a||n.open,children:n.modal?(0,g.jsx)(G,{...o,ref:r}):(0,g.jsx)(T,{...o,ref:r})})});L.displayName=q;var O=(0,v.TL)("PopoverContent.RemoveScroll"),G=a.forwardRef((e,r)=>{let t=j(q,e.__scopePopover),l=a.useRef(null),i=(0,n.s)(r,l),s=a.useRef(!1);return a.useEffect(()=>{let e=l.current;if(e)return(0,m.Eq)(e)},[]),(0,g.jsx)(x.A,{as:O,allowPinchZoom:!0,children:(0,g.jsx)(z,{...e,ref:i,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var r;e.preventDefault(),s.current||null==(r=t.triggerRef.current)||r.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let r=e.detail.originalEvent,t=0===r.button&&!0===r.ctrlKey;s.current=2===r.button||t},{checkForDefaultPrevented:!1}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),T=a.forwardRef((e,r)=>{let t=j(q,e.__scopePopover),o=a.useRef(!1),n=a.useRef(!1);return(0,g.jsx)(z,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:r=>{var a,l;null==(a=e.onCloseAutoFocus)||a.call(e,r),r.defaultPrevented||(o.current||null==(l=t.triggerRef.current)||l.focus(),r.preventDefault()),o.current=!1,n.current=!1},onInteractOutside:r=>{var a,l;null==(a=e.onInteractOutside)||a.call(e,r),r.defaultPrevented||(o.current=!0,"pointerdown"===r.detail.originalEvent.type&&(n.current=!0));let i=r.target;(null==(l=t.triggerRef.current)?void 0:l.contains(i))&&r.preventDefault(),"focusin"===r.detail.originalEvent.type&&n.current&&r.preventDefault()}})}),z=a.forwardRef((e,r)=>{let{__scopePopover:t,trapFocus:a,onOpenAutoFocus:o,onCloseAutoFocus:n,disableOutsidePointerEvents:l,onEscapeKeyDown:u,onPointerDownOutside:p,onFocusOutside:f,onInteractOutside:h,...v}=e,y=j(q,t),m=b(t);return(0,s.Oh)(),(0,g.jsx)(d.n,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:o,onUnmountAutoFocus:n,children:(0,g.jsx)(i.qW,{asChild:!0,disableOutsidePointerEvents:l,onInteractOutside:h,onEscapeKeyDown:u,onPointerDownOutside:p,onFocusOutside:f,onDismiss:()=>y.onOpenChange(!1),children:(0,g.jsx)(c.UC,{"data-state":S(y.open),role:"dialog",id:y.contentId,...m,...v,ref:r,style:{...v.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),_="PopoverClose";function S(e){return e?"open":"closed"}a.forwardRef((e,r)=>{let{__scopePopover:t,...a}=e,n=j(_,t);return(0,g.jsx)(h.sG.button,{type:"button",...a,ref:r,onClick:(0,o.m)(e.onClick,()=>n.onOpenChange(!1))})}).displayName=_,a.forwardRef((e,r)=>{let{__scopePopover:t,...a}=e,o=b(t);return(0,g.jsx)(c.i3,{...o,...a,ref:r})}).displayName="PopoverArrow";var V=C,B=M,H=I,K=L},21492:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(19946).A)("arrow-up-down",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]])},24357:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(19946).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},38164:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(19946).A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},39881:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(19946).A)("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]])},42355:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(19946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},52278:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(19946).A)("chevrons-left",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]])},54059:(e,r,t)=>{t.d(r,{C1:()=>z,bL:()=>G,q7:()=>T});var a=t(12115),o=t(85185),n=t(6101),l=t(46081),i=t(63655),s=t(89196),d=t(5845),u=t(94315),c=t(11275),p=t(45503),f=t(28905),h=t(95155),v="Radio",[y,m]=(0,l.A)(v),[x,g]=y(v),k=a.forwardRef((e,r)=>{let{__scopeRadio:t,name:l,checked:s=!1,required:d,disabled:u,value:c="on",onCheck:p,form:f,...v}=e,[y,m]=a.useState(null),g=(0,n.s)(r,e=>m(e)),k=a.useRef(!1),A=!y||f||!!y.closest("form");return(0,h.jsxs)(x,{scope:t,checked:s,disabled:u,children:[(0,h.jsx)(i.sG.button,{type:"button",role:"radio","aria-checked":s,"data-state":R(s),"data-disabled":u?"":void 0,disabled:u,value:c,...v,ref:g,onClick:(0,o.m)(e.onClick,e=>{s||null==p||p(),A&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})}),A&&(0,h.jsx)(b,{control:y,bubbles:!k.current,name:l,value:c,checked:s,required:d,disabled:u,form:f,style:{transform:"translateX(-100%)"}})]})});k.displayName=v;var A="RadioIndicator",w=a.forwardRef((e,r)=>{let{__scopeRadio:t,forceMount:a,...o}=e,n=g(A,t);return(0,h.jsx)(f.C,{present:a||n.checked,children:(0,h.jsx)(i.sG.span,{"data-state":R(n.checked),"data-disabled":n.disabled?"":void 0,...o,ref:r})})});w.displayName=A;var b=a.forwardRef((e,r)=>{let{__scopeRadio:t,control:o,checked:l,bubbles:s=!0,...d}=e,u=a.useRef(null),f=(0,n.s)(u,r),v=(0,p.Z)(l),y=(0,c.X)(o);return a.useEffect(()=>{let e=u.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(v!==l&&r){let t=new Event("click",{bubbles:s});r.call(e,l),e.dispatchEvent(t)}},[v,l,s]),(0,h.jsx)(i.sG.input,{type:"radio","aria-hidden":!0,defaultChecked:l,...d,tabIndex:-1,ref:f,style:{...d.style,...y,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function R(e){return e?"checked":"unchecked"}b.displayName="RadioBubbleInput";var j=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],C="RadioGroup",[D,E]=(0,l.A)(C,[s.RG,m]),M=(0,s.RG)(),P=m(),[F,N]=D(C),I=a.forwardRef((e,r)=>{let{__scopeRadioGroup:t,name:a,defaultValue:o,value:n,required:l=!1,disabled:c=!1,orientation:p,dir:f,loop:v=!0,onValueChange:y,...m}=e,x=M(t),g=(0,u.jH)(f),[k,A]=(0,d.i)({prop:n,defaultProp:null!=o?o:null,onChange:y,caller:C});return(0,h.jsx)(F,{scope:t,name:a,required:l,disabled:c,value:k,onValueChange:A,children:(0,h.jsx)(s.bL,{asChild:!0,...x,orientation:p,dir:g,loop:v,children:(0,h.jsx)(i.sG.div,{role:"radiogroup","aria-required":l,"aria-orientation":p,"data-disabled":c?"":void 0,dir:g,...m,ref:r})})})});I.displayName=C;var q="RadioGroupItem",L=a.forwardRef((e,r)=>{let{__scopeRadioGroup:t,disabled:l,...i}=e,d=N(q,t),u=d.disabled||l,c=M(t),p=P(t),f=a.useRef(null),v=(0,n.s)(r,f),y=d.value===i.value,m=a.useRef(!1);return a.useEffect(()=>{let e=e=>{j.includes(e.key)&&(m.current=!0)},r=()=>m.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",r),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",r)}},[]),(0,h.jsx)(s.q7,{asChild:!0,...c,focusable:!u,active:y,children:(0,h.jsx)(k,{disabled:u,required:d.required,checked:y,...p,...i,name:d.name,ref:v,onCheck:()=>d.onValueChange(i.value),onKeyDown:(0,o.m)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,o.m)(i.onFocus,()=>{var e;m.current&&(null==(e=f.current)||e.click())})})})});L.displayName=q;var O=a.forwardRef((e,r)=>{let{__scopeRadioGroup:t,...a}=e,o=P(t);return(0,h.jsx)(w,{...o,...a,ref:r})});O.displayName="RadioGroupIndicator";var G=I,T=L,z=O},54213:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(19946).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},54861:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(19946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},58832:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(19946).A)("arrow-down",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]])},64261:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(19946).A)("file-spreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]])},66932:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(19946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},69037:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(19946).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},77855:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(19946).A)("unlink",[["path",{d:"m18.84 12.25 1.72-1.71h-.02a5.004 5.004 0 0 0-.12-7.07 5.006 5.006 0 0 0-6.95 0l-1.72 1.71",key:"yqzxt4"}],["path",{d:"m5.17 11.75-1.71 1.71a5.004 5.004 0 0 0 .12 7.07 5.006 5.006 0 0 0 6.95 0l1.71-1.71",key:"4qinb0"}],["line",{x1:"8",x2:"8",y1:"2",y2:"5",key:"1041cp"}],["line",{x1:"2",x2:"5",y1:"8",y2:"8",key:"14m1p5"}],["line",{x1:"16",x2:"16",y1:"19",y2:"22",key:"rzdirn"}],["line",{x1:"19",x2:"22",y1:"16",y2:"16",key:"ox905f"}]])},89196:(e,r,t)=>{t.d(r,{RG:()=>A,bL:()=>P,q7:()=>F});var a=t(12115),o=t(85185),n=t(37328),l=t(6101),i=t(46081),s=t(61285),d=t(63655),u=t(39033),c=t(5845),p=t(94315),f=t(95155),h="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},y="RovingFocusGroup",[m,x,g]=(0,n.N)(y),[k,A]=(0,i.A)(y,[g]),[w,b]=k(y),R=a.forwardRef((e,r)=>(0,f.jsx)(m.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(m.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(j,{...e,ref:r})})}));R.displayName=y;var j=a.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,orientation:n,loop:i=!1,dir:s,currentTabStopId:m,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:k,onEntryFocus:A,preventScrollOnEntryFocus:b=!1,...R}=e,j=a.useRef(null),C=(0,l.s)(r,j),D=(0,p.jH)(s),[E,P]=(0,c.i)({prop:m,defaultProp:null!=g?g:null,onChange:k,caller:y}),[F,N]=a.useState(!1),I=(0,u.c)(A),q=x(t),L=a.useRef(!1),[O,G]=a.useState(0);return a.useEffect(()=>{let e=j.current;if(e)return e.addEventListener(h,I),()=>e.removeEventListener(h,I)},[I]),(0,f.jsx)(w,{scope:t,orientation:n,dir:D,loop:i,currentTabStopId:E,onItemFocus:a.useCallback(e=>P(e),[P]),onItemShiftTab:a.useCallback(()=>N(!0),[]),onFocusableItemAdd:a.useCallback(()=>G(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>G(e=>e-1),[]),children:(0,f.jsx)(d.sG.div,{tabIndex:F||0===O?-1:0,"data-orientation":n,...R,ref:C,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{L.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let r=!L.current;if(e.target===e.currentTarget&&r&&!F){let r=new CustomEvent(h,v);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){let e=q().filter(e=>e.focusable);M([e.find(e=>e.active),e.find(e=>e.id===E),...e].filter(Boolean).map(e=>e.ref.current),b)}}L.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>N(!1))})})}),C="RovingFocusGroupItem",D=a.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,focusable:n=!0,active:l=!1,tabStopId:i,children:u,...c}=e,p=(0,s.B)(),h=i||p,v=b(C,t),y=v.currentTabStopId===h,g=x(t),{onFocusableItemAdd:k,onFocusableItemRemove:A,currentTabStopId:w}=v;return a.useEffect(()=>{if(n)return k(),()=>A()},[n,k,A]),(0,f.jsx)(m.ItemSlot,{scope:t,id:h,focusable:n,active:l,children:(0,f.jsx)(d.sG.span,{tabIndex:y?0:-1,"data-orientation":v.orientation,...c,ref:r,onMouseDown:(0,o.m)(e.onMouseDown,e=>{n?v.onItemFocus(h):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>v.onItemFocus(h)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void v.onItemShiftTab();if(e.target!==e.currentTarget)return;let r=function(e,r,t){var a;let o=(a=e.key,"rtl"!==t?a:"ArrowLeft"===a?"ArrowRight":"ArrowRight"===a?"ArrowLeft":a);if(!("vertical"===r&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===r&&["ArrowUp","ArrowDown"].includes(o)))return E[o]}(e,v.orientation,v.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let t=g().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)t.reverse();else if("prev"===r||"next"===r){"prev"===r&&t.reverse();let a=t.indexOf(e.currentTarget);t=v.loop?function(e,r){return e.map((t,a)=>e[(r+a)%e.length])}(t,a+1):t.slice(a+1)}setTimeout(()=>M(t))}}),children:"function"==typeof u?u({isCurrentTabStop:y,hasTabStop:null!=w}):u})})});D.displayName=C;var E={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function M(e){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=document.activeElement;for(let a of e)if(a===t||(a.focus({preventScroll:r}),document.activeElement!==t))return}var P=R,F=D}}]);