(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8569],{35695:(e,t,o)=>{"use strict";var n=o(18999);o.o(n,"useParams")&&o.d(t,{useParams:function(){return n.useParams}}),o.o(n,"usePathname")&&o.d(t,{usePathname:function(){return n.usePathname}}),o.o(n,"useRouter")&&o.d(t,{useRouter:function(){return n.useRouter}}),o.o(n,"useSearchParams")&&o.d(t,{useSearchParams:function(){return n.useSearchParams}})},54228:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>i});var n=o(95155),a=o(12115),s=o(35695);function i(){let[e,t]=(0,a.useState)(""),[o,i]=(0,a.useState)(""),[r,l]=(0,a.useState)(""),[d,c]=(0,a.useState)(!1),u=(0,s.useRouter)(),p=async t=>{t.preventDefault(),c(!0),l("Logging in...");try{console.log("Starting login with:",{username:e,password:o});let t=new FormData;t.append("username",e),t.append("password",o),console.log("Sending request to backend...");let n=await fetch("http://localhost:8001/api/auth/login",{method:"POST",body:t});console.log("Response status:",n.status);let a=await n.json();console.log("Response data:",a),n.ok?(localStorage.setItem("token",a.access_token),l("SUCCESS: Login riuscito! Token salvato. Reindirizzamento..."),setTimeout(()=>{"owner"===a.role?(console.log("Redirecting to /admin"),u.push("/admin")):"user"===a.role?(console.log("Redirecting to /cantieri"),u.push("/cantieri")):(console.log("Redirecting to /"),u.push("/"))},1e3)):l("ERROR: ".concat(n.status," - ").concat(a.detail||"Login failed"))}catch(e){console.error("Login error:",e),l("EXCEPTION: ".concat(e.message))}finally{c(!1)}};return(0,n.jsx)("div",{style:{minHeight:"100vh",display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"#f8f9fa",fontFamily:"Arial, sans-serif"},children:(0,n.jsxs)("div",{style:{backgroundColor:"white",padding:"40px",borderRadius:"10px",boxShadow:"0 4px 6px rgba(0, 0, 0, 0.1)",width:"100%",maxWidth:"400px"},children:[(0,n.jsx)("h1",{style:{textAlign:"center",marginBottom:"30px",color:"#333"},children:"Simple Login Test"}),(0,n.jsxs)("form",{onSubmit:p,style:{marginBottom:"20px"},children:[(0,n.jsxs)("div",{style:{marginBottom:"15px"},children:[(0,n.jsx)("label",{style:{display:"block",marginBottom:"5px",fontWeight:"bold"},children:"Username:"}),(0,n.jsx)("input",{type:"text",value:e,onChange:e=>t(e.target.value),placeholder:"admin",required:!0,disabled:d,style:{width:"100%",padding:"10px",border:"1px solid #ddd",borderRadius:"5px",fontSize:"16px"}})]}),(0,n.jsxs)("div",{style:{marginBottom:"20px"},children:[(0,n.jsx)("label",{style:{display:"block",marginBottom:"5px",fontWeight:"bold"},children:"Password:"}),(0,n.jsx)("input",{type:"password",value:o,onChange:e=>i(e.target.value),placeholder:"admin",required:!0,disabled:d,style:{width:"100%",padding:"10px",border:"1px solid #ddd",borderRadius:"5px",fontSize:"16px"}})]}),(0,n.jsx)("button",{type:"submit",disabled:d,style:{width:"100%",padding:"12px",backgroundColor:d?"#ccc":"#007bff",color:"white",border:"none",borderRadius:"5px",fontSize:"16px",cursor:d?"not-allowed":"pointer"},children:d?"Logging in...":"Login"})]}),(0,n.jsx)("div",{style:{backgroundColor:"#f8f9fa",padding:"15px",borderRadius:"5px",whiteSpace:"pre-wrap",minHeight:"100px",fontSize:"14px",fontFamily:"monospace"},children:r||"Enter credentials and click Login..."}),(0,n.jsx)("div",{style:{marginTop:"20px",textAlign:"center"},children:(0,n.jsx)("small",{style:{color:"#666"},children:"Test credentials: admin/admin"})})]})})}},68870:(e,t,o)=>{Promise.resolve().then(o.bind(o,54228))}},e=>{var t=t=>e(e.s=t);e.O(0,[8441,1684,7358],()=>t(68870)),_N_E=e.O()}]);