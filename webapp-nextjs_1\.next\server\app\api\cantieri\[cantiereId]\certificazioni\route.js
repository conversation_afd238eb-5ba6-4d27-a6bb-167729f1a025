(()=>{var e={};e.id=6926,e.ids=[6926],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},56014:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>z,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>f});var i={};t.r(i),t.d(i,{GET:()=>u,POST:()=>d});var n=t(96559),o=t(48088),a=t(37719),s=t(32190);let c=process.env.BACKEND_URL||"http://localhost:8001";async function u(e,{params:r}){try{console.log("\uD83D\uDD04 Certificazioni API: Proxying GET request to backend:",`${c}/api/cantieri/${r.cantiereId}/certificazioni`);let t=e.headers.get("authorization");if(!t)return s.NextResponse.json({error:"Token di autorizzazione mancante"},{status:401});let{searchParams:i}=new URL(e.url),n=i.toString(),o=`${c}/api/cantieri/${r.cantiereId}/certificazioni${n?`?${n}`:""}`,a=await fetch(o,{method:"GET",headers:{Authorization:t,"Content-Type":"application/json"}});if(console.log("\uD83D\uDCE1 Certificazioni API: Backend response status:",a.status),!a.ok){let e=await a.text();return console.error("❌ Certificazioni API: Backend error:",e),s.NextResponse.json({error:"Errore dal backend",details:e},{status:a.status})}let u=await a.json();return console.log("\uD83D\uDCE1 Certificazioni API: Backend response data:",u),s.NextResponse.json(u)}catch(e){return console.error("❌ Certificazioni API: Error:",e),s.NextResponse.json({error:"Errore interno del server",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function d(e,{params:r}){try{console.log("\uD83D\uDD04 Certificazioni API: Proxying POST request to backend:",`${c}/api/cantieri/${r.cantiereId}/certificazioni`);let t=e.headers.get("authorization");if(!t)return s.NextResponse.json({error:"Token di autorizzazione mancante"},{status:401});let i=await e.json();console.log("\uD83D\uDCE1 Certificazioni API: Request body:",JSON.stringify(i,null,2)),console.log("\uD83D\uDCE1 Certificazioni API: Sending to backend URL:",`${c}/api/cantieri/${r.cantiereId}/certificazioni`);let n=await fetch(`${c}/api/cantieri/${r.cantiereId}/certificazioni`,{method:"POST",headers:{Authorization:t,"Content-Type":"application/json"},body:JSON.stringify(i)});if(console.log("\uD83D\uDCE1 Certificazioni API: Backend response status:",n.status),!n.ok){let e=await n.text();return console.error("❌ Certificazioni API: Backend error:",e),s.NextResponse.json({error:"Errore dal backend",details:e},{status:n.status})}let o=await n.json();return console.log("\uD83D\uDCE1 Certificazioni API: Backend response data:",o),s.NextResponse.json(o)}catch(e){return console.error("❌ Certificazioni API: Error:",e),s.NextResponse.json({error:"Errore interno del server",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/cantieri/[cantiereId]/certificazioni/route",pathname:"/api/cantieri/[cantiereId]/certificazioni",filename:"route",bundlePath:"app/api/cantieri/[cantiereId]/certificazioni/route"},resolvedPagePath:"C:\\CMS\\webapp-nextjs_1\\src\\app\\api\\cantieri\\[cantiereId]\\certificazioni\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:l,workUnitAsyncStorage:f,serverHooks:z}=p;function x(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:f})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[4447,580],()=>t(56014));module.exports=i})();