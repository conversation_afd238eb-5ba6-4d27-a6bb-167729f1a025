"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6865],{15452:(e,t,r)=>{r.d(t,{G$:()=>W,Hs:()=>x,UC:()=>er,VY:()=>eo,ZL:()=>ee,bL:()=>Q,bm:()=>ea,hE:()=>en,hJ:()=>et,l9:()=>X});var n=r(12115),o=r(85185),a=r(6101),l=r(46081),i=r(61285),s=r(5845),u=r(19178),c=r(25519),d=r(34378),f=r(28905),p=r(63655),v=r(92293),h=r(93795),m=r(38168),y=r(99708),g=r(95155),b="Dialog",[w,x]=(0,l.A)(b),[D,R]=w(b),j=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:l,modal:u=!0}=e,c=n.useRef(null),d=n.useRef(null),[f,p]=(0,s.i)({prop:o,defaultProp:null!=a&&a,onChange:l,caller:b});return(0,g.jsx)(D,{scope:t,triggerRef:c,contentRef:d,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:u,children:r})};j.displayName=b;var k="DialogTrigger",A=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=R(k,r),i=(0,a.s)(t,l.triggerRef);return(0,g.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":U(l.open),...n,ref:i,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});A.displayName=k;var C="DialogPortal",[I,F]=w(C,{forceMount:void 0}),E=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,l=R(C,t);return(0,g.jsx)(I,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,g.jsx)(f.C,{present:r||l.open,children:(0,g.jsx)(d.Z,{asChild:!0,container:a,children:e})}))})};E.displayName=C;var M="DialogOverlay",N=n.forwardRef((e,t)=>{let r=F(M,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=R(M,e.__scopeDialog);return a.modal?(0,g.jsx)(f.C,{present:n||a.open,children:(0,g.jsx)(G,{...o,ref:t})}):null});N.displayName=M;var T=(0,y.TL)("DialogOverlay.RemoveScroll"),G=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(M,r);return(0,g.jsx)(h.A,{as:T,allowPinchZoom:!0,shards:[o.contentRef],children:(0,g.jsx)(p.sG.div,{"data-state":U(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),_="DialogContent",O=n.forwardRef((e,t)=>{let r=F(_,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=R(_,e.__scopeDialog);return(0,g.jsx)(f.C,{present:n||a.open,children:a.modal?(0,g.jsx)(P,{...o,ref:t}):(0,g.jsx)(q,{...o,ref:t})})});O.displayName=_;var P=n.forwardRef((e,t)=>{let r=R(_,e.__scopeDialog),l=n.useRef(null),i=(0,a.s)(t,r.contentRef,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,m.Eq)(e)},[]),(0,g.jsx)(L,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),q=n.forwardRef((e,t)=>{let r=R(_,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,g.jsx)(L,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,l;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(o.current||null==(l=r.triggerRef.current)||l.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var n,l;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let i=t.target;(null==(l=r.triggerRef.current)?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),L=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,d=R(_,r),f=n.useRef(null),p=(0,a.s)(t,f);return(0,v.Oh)(),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,g.jsx)(u.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":U(d.open),...s,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(Y,{titleId:d.titleId}),(0,g.jsx)($,{contentRef:f,descriptionId:d.descriptionId})]})]})}),S="DialogTitle",V=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(S,r);return(0,g.jsx)(p.sG.h2,{id:o.titleId,...n,ref:t})});V.displayName=S;var B="DialogDescription",K=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(B,r);return(0,g.jsx)(p.sG.p,{id:o.descriptionId,...n,ref:t})});K.displayName=B;var H="DialogClose",z=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=R(H,r);return(0,g.jsx)(p.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function U(e){return e?"open":"closed"}z.displayName=H;var Z="DialogTitleWarning",[W,J]=(0,l.q)(Z,{contentName:_,titleName:S,docsSlug:"dialog"}),Y=e=>{let{titleId:t}=e,r=J(Z),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},$=e=>{let{contentRef:t,descriptionId:r}=e,o=J("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&n&&(document.getElementById(r)||console.warn(a))},[a,t,r]),null},Q=j,X=A,ee=E,et=N,er=O,en=V,eo=K,ea=z},23837:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("file-down",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M12 18v-6",key:"17g6i2"}],["path",{d:"m9 15 3 3 3-3",key:"1npd3o"}]])},37108:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},47924:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},60704:(e,t,r)=>{r.d(t,{B8:()=>F,UC:()=>M,bL:()=>I,l9:()=>E});var n=r(12115),o=r(85185),a=r(46081),l=r(89196),i=r(28905),s=r(63655),u=r(94315),c=r(5845),d=r(61285),f=r(95155),p="Tabs",[v,h]=(0,a.A)(p,[l.RG]),m=(0,l.RG)(),[y,g]=v(p),b=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:o,defaultValue:a,orientation:l="horizontal",dir:i,activationMode:v="automatic",...h}=e,m=(0,u.jH)(i),[g,b]=(0,c.i)({prop:n,onChange:o,defaultProp:null!=a?a:"",caller:p});return(0,f.jsx)(y,{scope:r,baseId:(0,d.B)(),value:g,onValueChange:b,orientation:l,dir:m,activationMode:v,children:(0,f.jsx)(s.sG.div,{dir:m,"data-orientation":l,...h,ref:t})})});b.displayName=p;var w="TabsList",x=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...o}=e,a=g(w,r),i=m(r);return(0,f.jsx)(l.bL,{asChild:!0,...i,orientation:a.orientation,dir:a.dir,loop:n,children:(0,f.jsx)(s.sG.div,{role:"tablist","aria-orientation":a.orientation,...o,ref:t})})});x.displayName=w;var D="TabsTrigger",R=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:a=!1,...i}=e,u=g(D,r),c=m(r),d=A(u.baseId,n),p=C(u.baseId,n),v=n===u.value;return(0,f.jsx)(l.q7,{asChild:!0,...c,focusable:!a,active:v,children:(0,f.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":v,"aria-controls":p,"data-state":v?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:d,...i,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():u.onValueChange(n)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&u.onValueChange(n)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==u.activationMode;v||a||!e||u.onValueChange(n)})})})});R.displayName=D;var j="TabsContent",k=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:o,forceMount:a,children:l,...u}=e,c=g(j,r),d=A(c.baseId,o),p=C(c.baseId,o),v=o===c.value,h=n.useRef(v);return n.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(i.C,{present:a||v,children:r=>{let{present:n}=r;return(0,f.jsx)(s.sG.div,{"data-state":v?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":d,hidden:!n,id:p,tabIndex:0,...u,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:n&&l})}})});function A(e,t){return"".concat(e,"-trigger-").concat(t)}function C(e,t){return"".concat(e,"-content-").concat(t)}k.displayName=j;var I=b,F=x,E=R,M=k},72713:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},81284:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},83744:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("file-up",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M12 12v6",key:"3ahymv"}],["path",{d:"m15 15-3-3-3 3",key:"15xj92"}]])},85339:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},89196:(e,t,r)=>{r.d(t,{RG:()=>x,bL:()=>E,q7:()=>M});var n=r(12115),o=r(85185),a=r(37328),l=r(6101),i=r(46081),s=r(61285),u=r(63655),c=r(39033),d=r(5845),f=r(94315),p=r(95155),v="rovingFocusGroup.onEntryFocus",h={bubbles:!1,cancelable:!0},m="RovingFocusGroup",[y,g,b]=(0,a.N)(m),[w,x]=(0,i.A)(m,[b]),[D,R]=w(m),j=n.forwardRef((e,t)=>(0,p.jsx)(y.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(y.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(k,{...e,ref:t})})}));j.displayName=m;var k=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:i=!1,dir:s,currentTabStopId:y,defaultCurrentTabStopId:b,onCurrentTabStopIdChange:w,onEntryFocus:x,preventScrollOnEntryFocus:R=!1,...j}=e,k=n.useRef(null),A=(0,l.s)(t,k),C=(0,f.jH)(s),[I,E]=(0,d.i)({prop:y,defaultProp:null!=b?b:null,onChange:w,caller:m}),[M,N]=n.useState(!1),T=(0,c.c)(x),G=g(r),_=n.useRef(!1),[O,P]=n.useState(0);return n.useEffect(()=>{let e=k.current;if(e)return e.addEventListener(v,T),()=>e.removeEventListener(v,T)},[T]),(0,p.jsx)(D,{scope:r,orientation:a,dir:C,loop:i,currentTabStopId:I,onItemFocus:n.useCallback(e=>E(e),[E]),onItemShiftTab:n.useCallback(()=>N(!0),[]),onFocusableItemAdd:n.useCallback(()=>P(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>P(e=>e-1),[]),children:(0,p.jsx)(u.sG.div,{tabIndex:M||0===O?-1:0,"data-orientation":a,...j,ref:A,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{_.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!_.current;if(e.target===e.currentTarget&&t&&!M){let t=new CustomEvent(v,h);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=G().filter(e=>e.focusable);F([e.find(e=>e.active),e.find(e=>e.id===I),...e].filter(Boolean).map(e=>e.ref.current),R)}}_.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>N(!1))})})}),A="RovingFocusGroupItem",C=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:l=!1,tabStopId:i,children:c,...d}=e,f=(0,s.B)(),v=i||f,h=R(A,r),m=h.currentTabStopId===v,b=g(r),{onFocusableItemAdd:w,onFocusableItemRemove:x,currentTabStopId:D}=h;return n.useEffect(()=>{if(a)return w(),()=>x()},[a,w,x]),(0,p.jsx)(y.ItemSlot,{scope:r,id:v,focusable:a,active:l,children:(0,p.jsx)(u.sG.span,{tabIndex:m?0:-1,"data-orientation":h.orientation,...d,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?h.onItemFocus(v):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>h.onItemFocus(v)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void h.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return I[o]}(e,h.orientation,h.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=b().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=h.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>F(r))}}),children:"function"==typeof c?c({isCurrentTabStop:m,hasTabStop:null!=D}):c})})});C.displayName=A;var I={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function F(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var E=j,M=C}}]);