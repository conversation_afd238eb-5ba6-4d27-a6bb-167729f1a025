(()=>{var e={};e.id=1943,e.ids=[1943],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},94098:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>I,routeModule:()=>l,serverHooks:()=>x,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>z});var i={};t.r(i),t.d(i,{DELETE:()=>p,GET:()=>d,PUT:()=>u});var o=t(96559),n=t(48088),a=t(37719),s=t(32190);let c=process.env.BACKEND_URL||"http://localhost:8001";async function d(e,{params:r}){try{console.log("\uD83D\uDD04 Certificazione API: Proxying GET request to backend:",`${c}/api/cantieri/${r.cantiereId}/certificazioni/${r.certificazioneId}`);let t=e.headers.get("authorization");if(!t)return s.NextResponse.json({error:"Token di autorizzazione mancante"},{status:401});let i=await fetch(`${c}/api/cantieri/${r.cantiereId}/certificazioni/${r.certificazioneId}`,{method:"GET",headers:{Authorization:t,"Content-Type":"application/json"}});if(console.log("\uD83D\uDCE1 Certificazione API: Backend response status:",i.status),!i.ok){let e=await i.text();return console.error("❌ Certificazione API: Backend error:",e),s.NextResponse.json({error:"Errore dal backend",details:e},{status:i.status})}let o=await i.json();return console.log("\uD83D\uDCE1 Certificazione API: Backend response data:",o),s.NextResponse.json(o)}catch(e){return console.error("❌ Certificazione API: Error:",e),s.NextResponse.json({error:"Errore interno del server",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function u(e,{params:r}){try{console.log("\uD83D\uDD04 Certificazione API: Proxying PUT request to backend:",`${c}/api/cantieri/${r.cantiereId}/certificazioni/${r.certificazioneId}`);let t=e.headers.get("authorization");if(!t)return s.NextResponse.json({error:"Token di autorizzazione mancante"},{status:401});let i=await e.json();console.log("\uD83D\uDCE1 Certificazione API: Request body:",i);let o=await fetch(`${c}/api/cantieri/${r.cantiereId}/certificazioni/${r.certificazioneId}`,{method:"PUT",headers:{Authorization:t,"Content-Type":"application/json"},body:JSON.stringify(i)});if(console.log("\uD83D\uDCE1 Certificazione API: Backend response status:",o.status),!o.ok){let e=await o.text();return console.error("❌ Certificazione API: Backend error:",e),s.NextResponse.json({error:"Errore dal backend",details:e},{status:o.status})}let n=await o.json();return console.log("\uD83D\uDCE1 Certificazione API: Backend response data:",n),s.NextResponse.json(n)}catch(e){return console.error("❌ Certificazione API: Error:",e),s.NextResponse.json({error:"Errore interno del server",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function p(e,{params:r}){try{console.log("\uD83D\uDD04 Certificazione API: Proxying DELETE request to backend:",`${c}/api/cantieri/${r.cantiereId}/certificazioni/${r.certificazioneId}`);let t=e.headers.get("authorization");if(!t)return s.NextResponse.json({error:"Token di autorizzazione mancante"},{status:401});let i=await fetch(`${c}/api/cantieri/${r.cantiereId}/certificazioni/${r.certificazioneId}`,{method:"DELETE",headers:{Authorization:t,"Content-Type":"application/json"}});if(console.log("\uD83D\uDCE1 Certificazione API: Backend response status:",i.status),!i.ok){let e=await i.text();return console.error("❌ Certificazione API: Backend error:",e),s.NextResponse.json({error:"Errore dal backend",details:e},{status:i.status})}if(204===i.status)return new s.NextResponse(null,{status:204});let o=await i.json();return console.log("\uD83D\uDCE1 Certificazione API: Backend response data:",o),s.NextResponse.json(o)}catch(e){return console.error("❌ Certificazione API: Error:",e),s.NextResponse.json({error:"Errore interno del server",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let l=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/cantieri/[cantiereId]/certificazioni/[certificazioneId]/route",pathname:"/api/cantieri/[cantiereId]/certificazioni/[certificazioneId]",filename:"route",bundlePath:"app/api/cantieri/[cantiereId]/certificazioni/[certificazioneId]/route"},resolvedPagePath:"C:\\CMS\\webapp-nextjs_1\\src\\app\\api\\cantieri\\[cantiereId]\\certificazioni\\[certificazioneId]\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:f,workUnitAsyncStorage:z,serverHooks:x}=l;function I(){return(0,a.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:z})}},96487:()=>{}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[4447,580],()=>t(94098));module.exports=i})();