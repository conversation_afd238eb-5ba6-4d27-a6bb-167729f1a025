(()=>{var e={};e.id=3986,e.ids=[3986],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,a,i)=>{"use strict";i.d(a,{A0:()=>l,BF:()=>n,Hj:()=>o,XI:()=>r,nA:()=>d,nd:()=>c});var t=i(60687);i(43210);var s=i(4780);function r({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,t.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm border-collapse",e),...a})})}function l({className:e,...a}){return(0,t.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",e),...a})}function n({className:e,...a}){return(0,t.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",e),...a})}function o({className:e,...a}){return(0,t.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("data-[state=selected]:bg-muted border-b",e),...a})}function c({className:e,...a}){return(0,t.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...a})}function d({className:e,...a}){return(0,t.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...a})}},10698:(e,a,i)=>{"use strict";i.r(a),i.d(a,{default:()=>t});let t=(0,i(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs_1\\\\src\\\\app\\\\cavi\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_1\\src\\app\\cavi\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15067:(e,a,i)=>{Promise.resolve().then(i.bind(i,93411))},15391:(e,a,i)=>{"use strict";i.d(a,{Fw:()=>d,NM:()=>c,Nj:()=>o,Tr:()=>l,mU:()=>t,qn:()=>x});let t={PRIMARY:{bg:"bg-blue-50",text:"text-blue-600",border:"border-blue-300",hover:"hover:bg-blue-50",active:"hover:border-blue-400",hex:"#007bff"},NEUTRAL:{text_dark:"text-gray-800",text_medium:"text-gray-600",text_light:"text-gray-500",bg_white:"bg-white",bg_light:"bg-gray-50",border:"border-gray-300",hex_dark:"#343A40",hex_medium:"#6c757d",hex_light:"#DEE2E6"},STATUS:{SUCCESS:{bg:"bg-green-50",text:"text-green-700",border:"border-green-200",hex:"#28A745"},WARNING:{bg:"bg-orange-50",text:"text-orange-700",border:"border-orange-200",hex:"#FD7E14"},ERROR:{bg:"bg-red-50",text:"text-red-700",border:"border-red-200",hex:"#DC3545"}}};t.STATUS.SUCCESS,t.STATUS.WARNING,t.NEUTRAL,t.STATUS.ERROR,t.NEUTRAL,t.STATUS.ERROR;let s={DA_INSTALLARE:t.NEUTRAL,INSTALLATO:t.STATUS.SUCCESS,COLLEGATO_PARTENZA:t.STATUS.WARNING,COLLEGATO_ARRIVO:t.STATUS.WARNING,COLLEGATO:t.STATUS.SUCCESS,CERTIFICATO:t.STATUS.SUCCESS,SPARE:t.STATUS.WARNING,ERRORE:t.STATUS.ERROR},r={ATTIVA:t.STATUS.SUCCESS,COMPLETATA:t.STATUS.SUCCESS,ANNULLATA:t.NEUTRAL,IN_CORSO:t.STATUS.WARNING,ERRORE:t.STATUS.ERROR},l=e=>{let a=s[e?.toUpperCase().replace(/\s+/g,"_")]||s.ERRORE;return{badge:`${a.bg} ${a.text} rounded-full px-3 py-1 text-xs font-medium`,text:a.text,bg:a.bg,border:a.border,hex:a.hex}},n=()=>({button:"inline-flex items-center justify-center gap-1 px-3 py-2 text-xs font-medium bg-white text-gray-800 border-b-2 border-[#315cfd] rounded-full cursor-pointer min-w-[4rem] h-8 transition-colors duration-300 hover:border-2 hover:bg-[#315cfd] hover:text-white",text:"text-gray-800",border:"border-b-[#315cfd]",hover:"hover:bg-[#315cfd] hover:text-white hover:border-2"}),o=()=>n(),c=()=>({text:`inline-flex items-center gap-1 px-2 py-1 text-xs font-medium ${t.NEUTRAL.text_light}`,color:t.NEUTRAL.text_light}),d=e=>{let a=r[e?.toUpperCase().replace(/\s+/g,"_")]||r.ERRORE;return{badge:`${a.bg} ${a.text} ${a.border}`,button:`${a.bg} ${a.text} ${a.border} ${a.hover}`,alert:`${a.bg} ${a.text} ${a.border}`,text:a.text,bg:a.bg,border:a.border,hover:a.hover,hex:a.hex}};t.STATUS.ERROR,t.STATUS.WARNING,t.NEUTRAL,t.NEUTRAL;let m={SUCCESS:t.STATUS.SUCCESS,WARNING:t.STATUS.WARNING,ERROR:t.STATUS.ERROR,NEUTRAL:t.NEUTRAL,PRIMARY:t.PRIMARY,PROGRESS:t.STATUS.SUCCESS,INFO:t.PRIMARY},x=e=>{let a=m[e]||m.NEUTRAL;return{badge:`${a.bg} ${a.text} rounded-full px-3 py-1 text-xs font-medium`,text:a.text,bg:a.bg,border:a.border,hex:a.hex}}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19538:(e,a,i)=>{"use strict";i.r(a),i.d(a,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c});var t=i(65239),s=i(48088),r=i(88170),l=i.n(r),n=i(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);i.d(a,o);let c={children:["",{children:["cavi",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,10698)),"C:\\CMS\\webapp-nextjs_1\\src\\app\\cavi\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(i.bind(i,98741)),"C:\\CMS\\webapp-nextjs_1\\src\\app\\cavi\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,94431)),"C:\\CMS\\webapp-nextjs_1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\CMS\\webapp-nextjs_1\\src\\app\\cavi\\page.tsx"],m={require:i,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/cavi/page",pathname:"/cavi",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,a,i)=>{"use strict";i.d(a,{A:()=>t});let t=(0,i(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:e=>{"use strict";e.exports=require("path")},34570:(e,a,i)=>{"use strict";i.d(a,{A:()=>o});var t=i(60687),s=i(43210),r=i(16189),l=i(63213),n=i(41862);function o({children:e,requiredRole:a,requiresUser:i=!1,requiresCantiere:o=!1,redirectTo:c="/login"}){let{user:d,cantiere:m,isAuthenticated:x,isLoading:u}=(0,l.A)();(0,r.useRouter)();let[p,h]=(0,s.useState)(!0);return u||p?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,t.jsx)(n.A,{className:"h-8 w-8 animate-spin text-blue-600"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Verifica autenticazione..."})]})}):(0,t.jsx)(t.Fragment,{children:e})}},40510:()=>{},45583:(e,a,i)=>{"use strict";i.d(a,{A:()=>t});let t=(0,i(62688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},47342:(e,a,i)=>{"use strict";i.d(a,{A:()=>t});let t=(0,i(62688).A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},49411:(e,a,i)=>{Promise.resolve().then(i.bind(i,10698))},50756:(e,a,i)=>{Promise.resolve().then(i.bind(i,98741))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56896:(e,a,i)=>{"use strict";i.d(a,{S:()=>n});var t=i(60687);i(43210);var s=i(40211),r=i(13964),l=i(4780);function n({className:e,...a}){return(0,t.jsx)(s.bL,{"data-slot":"checkbox",className:(0,l.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...a,children:(0,t.jsx)(s.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,t.jsx)(r.A,{className:"size-3.5"})})})}},58372:(e,a,i)=>{Promise.resolve().then(i.bind(i,87471))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70615:(e,a,i)=>{"use strict";i.d(a,{A:()=>t});let t=(0,i(62688).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86561:(e,a,i)=>{"use strict";i.d(a,{A:()=>t});let t=(0,i(62688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},87471:(e,a,i)=>{"use strict";i.r(a),i.d(a,{default:()=>r});var t=i(60687),s=i(34570);function r({children:e}){return(0,t.jsx)(s.A,{children:e})}},93411:(e,a,i)=>{"use strict";i.r(a),i.d(a,{default:()=>aK});var t=i(60687),s=i(43210),r=i(16189),l=i(44493),n=i(91821),o=i(63213),c=i(76628),d=i(25653),m=i(62185),x=i(29523),u=i(96834),p=i(56896),h=i(15391),v=i(6211),g=i(89667),b=i(15079),j=i(70569),f=i(98599),N=i(11273),y=i(31355),C=i(1359),_=i(32547),w=i(96963),z=i(55509),A=i(25028),S=i(46059),k=i(14163),E=i(8730),T=i(65551),I=i(63376),R=i(42247),O="Popover",[$,F]=(0,N.A)(O,[z.Bk]),M=(0,z.Bk)(),[L,P]=$(O),D=e=>{let{__scopePopover:a,children:i,open:r,defaultOpen:l,onOpenChange:n,modal:o=!1}=e,c=M(a),d=s.useRef(null),[m,x]=s.useState(!1),[u,p]=(0,T.i)({prop:r,defaultProp:l??!1,onChange:n,caller:O});return(0,t.jsx)(z.bL,{...c,children:(0,t.jsx)(L,{scope:a,contentId:(0,w.B)(),triggerRef:d,open:u,onOpenChange:p,onOpenToggle:s.useCallback(()=>p(e=>!e),[p]),hasCustomAnchor:m,onCustomAnchorAdd:s.useCallback(()=>x(!0),[]),onCustomAnchorRemove:s.useCallback(()=>x(!1),[]),modal:o,children:i})})};D.displayName=O;var U="PopoverAnchor";s.forwardRef((e,a)=>{let{__scopePopover:i,...r}=e,l=P(U,i),n=M(i),{onCustomAnchorAdd:o,onCustomAnchorRemove:c}=l;return s.useEffect(()=>(o(),()=>c()),[o,c]),(0,t.jsx)(z.Mz,{...n,...r,ref:a})}).displayName=U;var B="PopoverTrigger",G=s.forwardRef((e,a)=>{let{__scopePopover:i,...s}=e,r=P(B,i),l=M(i),n=(0,f.s)(a,r.triggerRef),o=(0,t.jsx)(k.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":r.open,"aria-controls":r.contentId,"data-state":ea(r.open),...s,ref:n,onClick:(0,j.m)(e.onClick,r.onOpenToggle)});return r.hasCustomAnchor?o:(0,t.jsx)(z.Mz,{asChild:!0,...l,children:o})});G.displayName=B;var V="PopoverPortal",[q,J]=$(V,{forceMount:void 0}),W=e=>{let{__scopePopover:a,forceMount:i,children:s,container:r}=e,l=P(V,a);return(0,t.jsx)(q,{scope:a,forceMount:i,children:(0,t.jsx)(S.C,{present:i||l.open,children:(0,t.jsx)(A.Z,{asChild:!0,container:r,children:s})})})};W.displayName=V;var Z="PopoverContent",H=s.forwardRef((e,a)=>{let i=J(Z,e.__scopePopover),{forceMount:s=i.forceMount,...r}=e,l=P(Z,e.__scopePopover);return(0,t.jsx)(S.C,{present:s||l.open,children:l.modal?(0,t.jsx)(X,{...r,ref:a}):(0,t.jsx)(Y,{...r,ref:a})})});H.displayName=Z;var K=(0,E.TL)("PopoverContent.RemoveScroll"),X=s.forwardRef((e,a)=>{let i=P(Z,e.__scopePopover),r=s.useRef(null),l=(0,f.s)(a,r),n=s.useRef(!1);return s.useEffect(()=>{let e=r.current;if(e)return(0,I.Eq)(e)},[]),(0,t.jsx)(R.A,{as:K,allowPinchZoom:!0,children:(0,t.jsx)(Q,{...e,ref:l,trapFocus:i.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,j.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),n.current||i.triggerRef.current?.focus()}),onPointerDownOutside:(0,j.m)(e.onPointerDownOutside,e=>{let a=e.detail.originalEvent,i=0===a.button&&!0===a.ctrlKey;n.current=2===a.button||i},{checkForDefaultPrevented:!1}),onFocusOutside:(0,j.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),Y=s.forwardRef((e,a)=>{let i=P(Z,e.__scopePopover),r=s.useRef(!1),l=s.useRef(!1);return(0,t.jsx)(Q,{...e,ref:a,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{e.onCloseAutoFocus?.(a),a.defaultPrevented||(r.current||i.triggerRef.current?.focus(),a.preventDefault()),r.current=!1,l.current=!1},onInteractOutside:a=>{e.onInteractOutside?.(a),a.defaultPrevented||(r.current=!0,"pointerdown"===a.detail.originalEvent.type&&(l.current=!0));let t=a.target;i.triggerRef.current?.contains(t)&&a.preventDefault(),"focusin"===a.detail.originalEvent.type&&l.current&&a.preventDefault()}})}),Q=s.forwardRef((e,a)=>{let{__scopePopover:i,trapFocus:s,onOpenAutoFocus:r,onCloseAutoFocus:l,disableOutsidePointerEvents:n,onEscapeKeyDown:o,onPointerDownOutside:c,onFocusOutside:d,onInteractOutside:m,...x}=e,u=P(Z,i),p=M(i);return(0,C.Oh)(),(0,t.jsx)(_.n,{asChild:!0,loop:!0,trapped:s,onMountAutoFocus:r,onUnmountAutoFocus:l,children:(0,t.jsx)(y.qW,{asChild:!0,disableOutsidePointerEvents:n,onInteractOutside:m,onEscapeKeyDown:o,onPointerDownOutside:c,onFocusOutside:d,onDismiss:()=>u.onOpenChange(!1),children:(0,t.jsx)(z.UC,{"data-state":ea(u.open),role:"dialog",id:u.contentId,...p,...x,ref:a,style:{...x.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),ee="PopoverClose";function ea(e){return e?"open":"closed"}s.forwardRef((e,a)=>{let{__scopePopover:i,...s}=e,r=P(ee,i);return(0,t.jsx)(k.sG.button,{type:"button",...s,ref:a,onClick:(0,j.m)(e.onClick,()=>r.onOpenChange(!1))})}).displayName=ee,s.forwardRef((e,a)=>{let{__scopePopover:i,...s}=e,r=M(i);return(0,t.jsx)(z.i3,{...r,...s,ref:a})}).displayName="PopoverArrow";var ei=i(4780);let et=s.forwardRef(({className:e,align:a="center",sideOffset:i=4,...s},r)=>(0,t.jsx)(W,{children:(0,t.jsx)(H,{ref:r,align:a,sideOffset:i,className:(0,ei.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...s})}));et.displayName=H.displayName;var es=i(62688);let er=(0,es.A)("arrow-up-down",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]]),el=(0,es.A)("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]),en=(0,es.A)("arrow-down",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);var eo=i(11860);let ec=(0,es.A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]),ed=(0,es.A)("chevrons-left",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]]),em=(0,es.A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),ex=(0,es.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),eu=(0,es.A)("chevrons-right",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]]);function ep({data:e=[],columns:a=[],loading:i=!1,emptyMessage:r="Nessun dato disponibile",onFilteredDataChange:n,renderRow:o,className:c,pagination:d=!0,defaultRowsPerPage:m=25}){let[p,h]=(0,s.useState)({key:null,direction:null}),[g,j]=(0,s.useState)({}),[f,N]=(0,s.useState)({}),[y,C]=(0,s.useState)(0),[_,w]=(0,s.useState)(m),z=i=>{let t=a.find(e=>e.field===i);return t?.getFilterValue?[...new Set(e.map(e=>t.getFilterValue(e)).filter(Boolean))].sort():[...new Set(e.map(e=>e[i]).filter(Boolean))].sort()},A=(0,s.useMemo)(()=>{let i=[...e];return Object.entries(g).forEach(([e,t])=>{!t.value||Array.isArray(t.value)&&0===t.value.length||"string"==typeof t.value&&""===t.value.trim()||(i=i.filter(i=>{let s=a.find(a=>a.field===e),r=s?.getFilterValue?s.getFilterValue(i):i[e];if("select"===t.type)return(Array.isArray(t.value)?t.value:[t.value]).includes(r);if("text"===t.type){let e=t.value.toLowerCase(),a=String(r||"").toLowerCase();return"equals"===t.operator?a===e:a.includes(e)}if("number"===t.type){let e=parseFloat(r),a=parseFloat(t.value);if(isNaN(e)||isNaN(a))return!1;switch(t.operator){case"equals":default:return e===a;case"gt":return e>a;case"lt":return e<a;case"gte":return e>=a;case"lte":return e<=a}}return!0}))}),p.key&&p.direction&&i.sort((e,a)=>{let i=e[p.key],t=a[p.key];if(null==i&&null==t)return 0;if(null==i)return"asc"===p.direction?-1:1;if(null==t)return"asc"===p.direction?1:-1;let s=parseFloat(i),r=parseFloat(t),l=!isNaN(s)&&!isNaN(r),n=0;return n=l?s-r:String(i).localeCompare(String(t)),"asc"===p.direction?n:-n}),i},[e,g,p]),S=(0,s.useMemo)(()=>{if(!d)return A;let e=y*_,a=e+_;return A.slice(e,a)},[A,y,_,d]),k=Math.ceil(A.length/_),E=y*_+1,T=Math.min((y+1)*_,A.length),I=e=>{let i=a.find(a=>a.field===e);i?.disableSort||h(a=>{if(a.key===e){if("asc"===a.direction)return{key:e,direction:"desc"};if("desc"===a.direction)return{key:null,direction:null}}return{key:e,direction:"asc"}})},R=(e,a)=>{j(i=>({...i,[e]:{...i[e],...a}}))},O=e=>{j(a=>{let i={...a};return delete i[e],i})},$=e=>p.key!==e?(0,t.jsx)(er,{className:"h-3 w-3"}):"asc"===p.direction?(0,t.jsx)(el,{className:"h-3 w-3"}):"desc"===p.direction?(0,t.jsx)(en,{className:"h-3 w-3"}):(0,t.jsx)(er,{className:"h-3 w-3"}),F=Object.keys(g).length>0;return i?(0,t.jsx)(l.Zp,{className:c,children:(0,t.jsx)(l.Wu,{className:"p-6",children:(0,t.jsx)("div",{className:"text-center",children:"Caricamento..."})})}):(0,t.jsxs)("div",{className:c,children:[F&&(0,t.jsxs)("div",{className:"mb-4 flex flex-wrap gap-2 items-center",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Filtri attivi:"}),Object.entries(g).map(([e,i])=>{let s=a.find(a=>a.field===e);if(!s)return null;let r=Array.isArray(i.value)?i.value.join(", "):String(i.value);return(0,t.jsxs)(u.E,{variant:"secondary",className:"gap-1",children:[s.headerName,": ",r,(0,t.jsx)(x.$,{variant:"ghost",size:"sm",className:"h-auto p-0 hover:bg-transparent",onClick:()=>O(e),children:(0,t.jsx)(eo.A,{className:"h-3 w-3"})})]},e)}),(0,t.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>{j({})},className:"h-6 px-2 text-xs",children:"Pulisci tutti"})]}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-0",children:(0,t.jsxs)(v.XI,{children:[(0,t.jsx)(v.A0,{children:(0,t.jsx)(v.Hj,{className:"bg-mariner-50 hover:bg-mariner-50",children:a.map(a=>(0,t.jsx)(v.nd,{className:(0,ei.cn)("font-semibold text-mariner-900 border-b border-mariner-200","center"===a.align&&"text-center","right"===a.align&&"text-right"),style:{width:a.width,...a.headerStyle},children:a.renderHeader?a.renderHeader():(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,t.jsx)("span",{className:"truncate",children:a.headerName}),(0,t.jsxs)("div",{className:"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:[!a.disableSort&&(0,t.jsx)(x.$,{variant:"ghost",size:"sm",className:"h-4 w-4 p-0 hover:bg-mariner-100",onClick:()=>I(a.field),children:$(a.field)}),!a.disableFilter&&(0,t.jsxs)(D,{open:f[a.field],onOpenChange:e=>N(i=>({...i,[a.field]:e})),children:[(0,t.jsx)(G,{asChild:!0,children:(0,t.jsx)(x.$,{variant:"ghost",size:"sm",className:(0,ei.cn)("h-4 w-4 p-0 hover:bg-mariner-100",g[a.field]&&"text-mariner-600 opacity-100"),children:(0,t.jsx)(ec,{className:"h-2.5 w-2.5"})})}),(0,t.jsx)(et,{className:"w-64",align:"start",children:(0,t.jsx)(eh,{column:a,data:e,currentFilter:g[a.field],onFilterChange:e=>R(a.field,e),onClearFilter:()=>O(a.field),getUniqueValues:()=>z(a.field)})})]})]})]}),g[a.field]&&(0,t.jsx)("div",{className:"absolute -top-1 -right-1 h-2 w-2 bg-mariner-600 rounded-full"})]})},a.field))})}),(0,t.jsx)(v.BF,{children:S.length>0?S.map((e,i)=>o?o(e,y*_+i):(0,t.jsx)(v.Hj,{className:"hover:bg-mariner-50 border-b border-mariner-100",children:a.map(a=>(0,t.jsx)(v.nA,{className:(0,ei.cn)("py-2 px-4","center"===a.align&&"text-center","right"===a.align&&"text-right"),style:a.cellStyle,children:a.renderCell?a.renderCell(e):e[a.field]},a.field))},i)):(0,t.jsx)(v.Hj,{children:(0,t.jsx)(v.nA,{colSpan:a.length,className:"text-center py-8 text-muted-foreground",children:r})})})]})})}),d&&A.length>0&&(0,t.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Righe per pagina:"}),(0,t.jsxs)(b.l6,{value:_.toString(),onValueChange:e=>{w(Number(e)),C(0)},children:[(0,t.jsx)(b.bq,{className:"w-20",children:(0,t.jsx)(b.yv,{})}),(0,t.jsxs)(b.gC,{children:[(0,t.jsx)(b.eb,{value:"10",children:"10"}),(0,t.jsx)(b.eb,{value:"25",children:"25"}),(0,t.jsx)(b.eb,{value:"50",children:"50"}),(0,t.jsx)(b.eb,{value:"100",children:"100"}),(0,t.jsx)(b.eb,{value:A.length.toString(),children:"Tutto"})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:A.length>0?`${E}-${T} di ${A.length}`:"0 di 0"}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>C(0),disabled:0===y,className:"h-8 w-8 p-0",children:(0,t.jsx)(ed,{className:"h-4 w-4"})}),(0,t.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>C(e=>Math.max(0,e-1)),disabled:0===y,className:"h-8 w-8 p-0",children:(0,t.jsx)(em,{className:"h-4 w-4"})}),(0,t.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>C(e=>Math.min(k-1,e+1)),disabled:y>=k-1,className:"h-8 w-8 p-0",children:(0,t.jsx)(ex,{className:"h-4 w-4"})}),(0,t.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>C(k-1),disabled:y>=k-1,className:"h-8 w-8 p-0",children:(0,t.jsx)(eu,{className:"h-4 w-4"})})]})]})]})]})}function eh({column:e,currentFilter:a,onFilterChange:i,onClearFilter:r,getUniqueValues:l}){let[n,o]=(0,s.useState)(a?.value||""),[c,d]=(0,s.useState)(a?.operator||"contains"),m=l(),u="number"!==e.dataType&&m.length<=20,h="number"===e.dataType,v=()=>{u?i({type:"select",value:Array.isArray(n)?n:[n]}):h?i({type:"number",value:n,operator:c}):i({type:"text",value:n,operator:c})};return(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"font-medium text-sm",children:["Filtra ",e.headerName]}),u?(0,t.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:m.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.S,{id:`filter-${e}`,checked:Array.isArray(n)?n.includes(e):n===e,onCheckedChange:a=>{Array.isArray(n)?o(a?[...n,e]:n.filter(a=>a!==e)):o(a?[e]:[])}}),(0,t.jsx)("label",{htmlFor:`filter-${e}`,className:"text-sm",children:e})]},e))}):(0,t.jsxs)("div",{className:"space-y-2",children:[h&&(0,t.jsxs)(b.l6,{value:c,onValueChange:d,children:[(0,t.jsx)(b.bq,{children:(0,t.jsx)(b.yv,{})}),(0,t.jsxs)(b.gC,{children:[(0,t.jsx)(b.eb,{value:"equals",children:"Uguale a"}),(0,t.jsx)(b.eb,{value:"gt",children:"Maggiore di"}),(0,t.jsx)(b.eb,{value:"lt",children:"Minore di"}),(0,t.jsx)(b.eb,{value:"gte",children:"Maggiore o uguale"}),(0,t.jsx)(b.eb,{value:"lte",children:"Minore o uguale"})]})]}),!h&&(0,t.jsxs)(b.l6,{value:c,onValueChange:d,children:[(0,t.jsx)(b.bq,{children:(0,t.jsx)(b.yv,{})}),(0,t.jsxs)(b.gC,{children:[(0,t.jsx)(b.eb,{value:"contains",children:"Contiene"}),(0,t.jsx)(b.eb,{value:"equals",children:"Uguale a"})]})]}),(0,t.jsx)(g.p,{placeholder:`Cerca ${e.headerName.toLowerCase()}...`,value:n,onChange:e=>o(e.target.value),onKeyDown:e=>"Enter"===e.key&&v()})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(x.$,{size:"sm",onClick:v,children:"Applica"}),(0,t.jsx)(x.$,{size:"sm",variant:"outline",onClick:r,children:"Pulisci"})]})]})}var ev=i(99270);let eg=(0,es.A)("square-check",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]),eb=(0,es.A)("square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]),ej=(0,es.A)("square-minus",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M8 12h8",key:"1wcyev"}]]);function ef({cavi:e=[],onFilteredDataChange:a,loading:i=!1,selectionEnabled:r=!1,onSelectionToggle:n,selectedCount:o=0,totalCount:c=0}){let[d,m]=(0,s.useState)(""),[u,p]=(0,s.useState)("contains"),h=e=>e?e.toString().toLowerCase().trim():"",v=e=>{let a=e.match(/^([A-Z]+)(\d+)([A-Z]*)$/);return a?{prefix:a[1],number:a[2],suffix:a[3]||""}:{prefix:"",number:e,suffix:""}},j=(0,s.useCallback)((e,a,i)=>{let t=h(a);if(!t)return!0;let s=h(e.id_cavo),{prefix:r,number:l,suffix:n}=v(e.id_cavo||""),o=h(e.tipologia),c=h(e.formazione||e.sezione),d=h(e.utility),m=h(e.sistema),x=h(e.da||e.ubicazione_partenza),u=h(e.a||e.ubicazione_arrivo),p=h(e.utenza_partenza),g=h(e.utenza_arrivo),b=[s,r,l,n,o,c,d,m,x,u,p,g,h(e.id_bobina),"BOBINA_VUOTA"===e.id_bobina?"bobina vuota":null===e.id_bobina?"":h(e.id_bobina)],j=[{value:e.metri_teorici,name:"metri_teorici"},{value:e.metratura_reale||e.metri_posati,name:"metratura_reale"},{value:parseFloat(c),name:"formazione"}],f=t.match(/^([><=]+)(\d+(?:\.\d+)?)$/);if(f){let e=f[1],a=parseFloat(f[2]);return j.some(i=>{if(null==i.value||isNaN(i.value))return!1;switch(e){case">":return i.value>a;case">=":return i.value>=a;case"<":return i.value<a;case"<=":return i.value<=a;case"=":return i.value===a;default:return!1}})}let N=parseFloat(t);return!!(!isNaN(N)&&j.some(e=>null!=e.value&&!isNaN(e.value)&&e.value===N))||(i?b.some(e=>e===t):b.some(e=>e.includes(t)))},[]);(0,s.useCallback)(()=>{if(!d.trim())return void a?.(e);let i=d.split(",").map(e=>e.trim()).filter(e=>e.length>0),t=[];t="equals"===u?1===i.length?e.filter(e=>j(e,i[0],!0)):e.filter(e=>i.every(a=>j(e,a,!0))):e.filter(e=>i.some(a=>j(e,a,!1))),a?.(t)},[d,u,e,j]);let f=e=>{m(e)},N=()=>{m(""),p("contains")};return(0,t.jsx)(l.Zp,{className:"mb-1",children:(0,t.jsxs)(l.Wu,{className:"p-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsxs)("div",{className:"flex-1 relative",children:[(0,t.jsx)(ev.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(g.p,{placeholder:"Cerca per ID, sistema, utility, tipologia, ubicazione...",value:d,onChange:e=>f(e.target.value),disabled:i,className:"pl-10 pr-10 h-8","aria-label":"Campo di ricerca intelligente per cavi"}),d&&(0,t.jsx)(x.$,{variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-5 w-5 p-0",onClick:N,children:(0,t.jsx)(eo.A,{className:"h-2.5 w-2.5"})})]}),(0,t.jsx)("div",{className:"w-32",children:(0,t.jsxs)(b.l6,{value:u,onValueChange:e=>p(e),children:[(0,t.jsx)(b.bq,{className:"h-8",children:(0,t.jsx)(b.yv,{})}),(0,t.jsxs)(b.gC,{children:[(0,t.jsx)(b.eb,{value:"contains",children:"Contiene"}),(0,t.jsx)(b.eb,{value:"equals",children:"Uguale a"})]})]})}),d&&(0,t.jsxs)(x.$,{variant:"outline",size:"sm",onClick:N,disabled:i,className:"transition-all duration-200 hover:scale-105","aria-label":"Pulisci ricerca",children:[(0,t.jsx)(eo.A,{className:"h-4 w-4 mr-1"}),"Pulisci"]}),n&&c>0&&(0,t.jsxs)(x.$,{variant:r?"default":"outline",size:"sm",onClick:n,className:"flex items-center gap-2 transition-all duration-200 hover:scale-105","aria-label":r?"Disabilita modalit\xe0 selezione":"Abilita modalit\xe0 selezione",children:[r?(0,t.jsx)(eg,{className:"h-4 w-4"}):(0,t.jsx)(eb,{className:"h-4 w-4"}),r?"Disabilita Selezione":"Abilita Selezione"]}),r&&o>0&&(0,t.jsxs)(x.$,{variant:"outline",size:"sm",onClick:()=>{},className:"flex items-center gap-2 transition-all duration-200 hover:scale-105 text-orange-600 border-orange-300 hover:bg-orange-50","aria-label":`Deseleziona tutti i ${o} cavi selezionati`,children:[(0,t.jsx)(ej,{className:"h-4 w-4"}),"Deseleziona Tutto (",o,")"]})]}),d&&(0,t.jsx)("div",{className:"mt-0.5 text-xs text-muted-foreground",children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDCA1"}),(0,t.jsx)("span",{children:"• Virgole per multipli"}),(0,t.jsx)("span",{children:"• >100, <=50 per numeri"})]})})]})})}function eN({text:e,maxLength:a=20,className:i=""}){let[r,l]=(0,s.useState)(!1),[n,o]=(0,s.useState)({x:0,y:0});if(!e)return(0,t.jsx)("span",{className:"text-gray-400",children:"-"});let c=e.length>a,d=c?`${e.substring(0,a)}...`:e;return c?(0,t.jsxs)("div",{className:"relative inline-block",children:[(0,t.jsx)("span",{className:`cursor-help ${i}`,style:{textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden",maxWidth:"100%",display:"inline-block"},onMouseEnter:e=>{o({x:e.clientX,y:e.clientY}),l(!0)},onMouseMove:e=>{o({x:e.clientX,y:e.clientY})},onMouseLeave:()=>l(!1),title:e,children:d}),r&&(0,t.jsxs)("div",{className:"fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg pointer-events-none",style:{top:n.y-40,left:n.x-150,maxWidth:"300px",wordWrap:"break-word",whiteSpace:"normal"},children:[e,(0,t.jsx)("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0",style:{borderLeft:"5px solid transparent",borderRight:"5px solid transparent",borderTop:"5px solid #1f2937"}})]})]}):(0,t.jsx)("span",{className:i,children:e})}var ey=i(51215),eC=i(13861),e_=i(63143),ew=i(88233),ez=i(96474),eA=i(47342),eS=i(19080),ek=i(86561);let eE=(0,es.A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]),eT=(0,es.A)("pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]]);var eI=i(70615),eR=i(10022),eO=i(31158);function e$({isOpen:e,position:a,cavo:i,isSelected:r,hasMultipleSelection:l,totalSelectedCount:n,onAction:o,onClose:c}){let d=(0,s.useRef)(null);if(!e||!i)return null;let m=e=>{o(i,e),c()},u=[{section:"Gestione Cavi",items:[{icon:eC.A,label:"Visualizza Dettagli",action:"view_details"},{icon:e_.A,label:"Modifica Cavo",action:"edit"},{icon:ew.A,label:"Elimina Cavo",action:"delete",destructive:!0},{icon:ez.A,label:"Aggiungi Nuovo Cavo",action:"add_new"}]},{section:"Comandi",items:[{icon:eA.A,label:"Gestisci Collegamenti",action:"manage_connections"},{icon:eS.A,label:"Gestisci Bobina",action:"manage_reel"},{icon:ek.A,label:"Certifica Cavo",action:"create_certificate"},{icon:eE,label:"Avvia Installazione",action:"start_installation"},{icon:eT,label:"Sospendi Installazione",action:"pause_installation"}]},{section:"Generale",items:[{icon:eI.A,label:"Copia ID",action:"copy_id"},{icon:eR.A,label:"Copia Dettagli",action:"copy_details"},{icon:eO.A,label:"Esporta Dati",action:"export_data"}]}],p={x:Math.min(a.x,window.innerWidth-250),y:Math.min(a.y,window.innerHeight-400)};return(0,ey.createPortal)((0,t.jsxs)("div",{ref:d,className:"fixed z-[9999] bg-white border-2 border-gray-300 rounded-lg shadow-2xl py-2 min-w-[220px] max-w-[300px]",style:{left:p.x,top:p.y,boxShadow:"0 10px 25px rgba(0, 0, 0, 0.2)"},children:[(0,t.jsxs)("div",{className:"px-3 py-2 border-b border-gray-100",children:[(0,t.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:["Cavo: ",i.id_cavo]}),r&&(0,t.jsxs)("div",{className:"text-xs text-blue-600",children:["Selezionato ",l?`(${n} totali)`:""]})]}),u.map((e,a)=>(0,t.jsxs)("div",{children:[a>0&&(0,t.jsx)("hr",{className:"my-1 border-gray-200"}),(0,t.jsxs)("div",{className:"px-2 py-1",children:[(0,t.jsx)("div",{className:"text-xs font-medium text-gray-500 px-2 py-1",children:e.section}),e.items.map(e=>(0,t.jsxs)(x.$,{variant:"ghost",size:"sm",className:`w-full justify-start text-left h-8 px-2 ${e.destructive?"text-red-600 hover:text-red-700 hover:bg-red-50":"text-gray-700 hover:text-gray-900 hover:bg-gray-50"}`,onClick:()=>m(e.action),children:[(0,t.jsx)(e.icon,{className:"w-4 h-4 mr-2"}),e.label]},e.action))]})]},e.section))]}),document.body)}var eF=i(78272),eM=i(5336),eL=i(48730),eP=i(93613),eD=i(96882);function eU({cavi:e=[],loading:a=!1,selectionEnabled:i=!1,selectedCavi:r=[],onSelectionChange:l,onStatusAction:n,onContextMenuAction:o}){let[c,d]=(0,s.useState)(e),[m,g]=(0,s.useState)(e),[b,j]=(0,s.useState)(i),[f,N]=(0,s.useState)({isOpen:!1,position:{x:0,y:0},cavo:null}),y=e=>{l&&l(e?m.map(e=>e.id_cavo):[])},C=(e,a)=>{l&&l(a?[...r,e]:r.filter(a=>a!==e))},_=async()=>{try{let e=await fetch("/api/cavi/export",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({selectedIds:r,cantiereId:1})});if(e.ok){let a=await e.blob(),i=window.URL.createObjectURL(a),t=document.createElement("a");t.href=i,t.download=`cavi_export_${new Date().toISOString().split("T")[0]}.csv`,document.body.appendChild(t),t.click(),window.URL.revokeObjectURL(i),document.body.removeChild(t)}else{let a=await e.json();alert(`Errore durante l'esportazione: ${a.error}`)}}catch(e){alert("Errore durante l'esportazione")}},w=async()=>{let e=prompt("Inserisci il nuovo stato (Da installare, In corso, Installato):");if(e)try{let a=await fetch("/api/cavi/bulk-status",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({selectedIds:r,cantiereId:1,newStatus:e})}),i=await a.json();i.success?alert(i.message):alert(`Errore: ${i.error}`)}catch(e){alert("Errore durante il cambio stato")}},z=()=>{let e=m.filter(e=>{let a=e.metri_posati>0||e.metratura_reale>0,i=r.includes(e.id_cavo),t=!0!==e.certificato&&"SI"!==e.certificato&&"CERTIFICATO"!==e.certificato;return i&&a&&t});if(0===e.length)return void alert("Nessun cavo selezionato pu\xf2 essere certificato. I cavi devono essere installati e non ancora certificati.");if(e.length!==r.length){let a=r.length-e.length;if(!confirm(`${a} cavi selezionati non possono essere certificati (non installati o gi\xe0 certificati). Procedere con la certificazione di ${e.length} cavi?`))return}n&&n(e[0],"bulk_certify",e)},A=()=>{alert(`Assegnazione comanda per ${r.length} cavi`)},S=async()=>{if(confirm(`Sei sicuro di voler eliminare ${r.length} cavi?`))try{let e=await fetch("/api/cavi/bulk-delete",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({selectedIds:r,cantiereId:1})}),a=await e.json();a.success?alert(a.message):alert(`Errore: ${a.error}`)}catch(e){alert("Errore durante l'eliminazione")}},k=(0,s.useMemo)(()=>{let e=[{field:"id_cavo",headerName:"ID",dataType:"text",width:70,align:"left",renderCell:e=>(0,t.jsx)("span",{className:"font-semibold text-mariner-900",children:e.id_cavo})},{field:"sistema",headerName:"Sistema",dataType:"text",width:80,renderCell:e=>(0,t.jsx)(eN,{text:e.sistema||"",maxLength:8})},{field:"utility",headerName:"Utility",dataType:"text",width:80,renderCell:e=>(0,t.jsx)(eN,{text:e.utility||"",maxLength:8})},{field:"tipologia",headerName:"Tipologia",dataType:"text",width:100,renderCell:e=>(0,t.jsx)(eN,{text:e.tipologia||"",maxLength:12})},{field:"formazione",headerName:"Form.",dataType:"text",align:"left",width:60,renderCell:e=>e.formazione||e.sezione},{field:"metri_teorici",headerName:"M.Teor.",dataType:"number",align:"left",width:70,renderCell:e=>e.metri_teorici?e.metri_teorici.toFixed(1):"0"},{field:"metri_posati",headerName:"M.Reali",dataType:"number",align:"left",width:70,renderCell:e=>{let a=e.metri_posati||e.metratura_reale||0;return a?a.toFixed(1):"0"}},{field:"ubicazione_partenza",headerName:"Da",dataType:"text",width:140,renderCell:e=>(0,t.jsx)(eN,{text:e.da||e.ubicazione_partenza||"",maxLength:18})},{field:"ubicazione_arrivo",headerName:"A",dataType:"text",width:140,renderCell:e=>(0,t.jsx)(eN,{text:e.a||e.ubicazione_arrivo||"",maxLength:18})},{field:"id_bobina",headerName:"Bobina",dataType:"text",width:80,align:"center",renderCell:e=>R(e)},{field:"stato_installazione",headerName:"Stato",dataType:"text",align:"left",width:120,disableSort:!0,getFilterValue:e=>E(e),renderCell:e=>O(e)},{field:"collegamenti",headerName:"Collegamenti",dataType:"text",align:"left",width:180,disableSort:!0,getFilterValue:e=>T(e),renderCell:e=>$(e)},{field:"certificato",headerName:"Certificato",dataType:"text",align:"left",width:130,disableSort:!0,getFilterValue:e=>I(e),renderCell:e=>F(e)}];return b&&e.unshift({field:"selection",headerName:"",disableFilter:!0,disableSort:!0,width:50,align:"left",renderHeader:()=>(0,t.jsx)(p.S,{checked:r.length===m.length&&m.length>0,onCheckedChange:y}),renderCell:e=>(0,t.jsx)(p.S,{checked:r.includes(e.id_cavo),onCheckedChange:a=>C(e.id_cavo,a),onClick:e=>e.stopPropagation()})}),e},[b,r,m,y,C]),E=e=>{let a=e.metri_posati||e.metratura_reale||0,i=e.stato_installazione||"Da installare",t=e.comanda_posa,s=e.comanda_partenza,r=e.comanda_arrivo,l=e.comanda_certificazione,n=t||s||r||l;return n&&"In corso"===i?`In corso (${n})`:"Installato"===i||a>0?"Installato":i},T=e=>{let a=e.metri_posati>0||e.metratura_reale>0,i=e.collegamento||e.collegamenti||0;if(!a)return"Non disponibile";switch(i){case 0:return"Collega";case 1:case 2:return"Completa collegamento";case 3:return"Scollega";default:return"Gestisci collegamenti"}},I=e=>{let a=e.metri_posati>0||e.metratura_reale>0,i=!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato;return a?i?"Genera PDF":"Certifica":"Non disponibile"},R=e=>{let a=e.id_bobina;if(!a||"N/A"===a)return(0,t.jsx)("span",{className:`inline-flex items-center px-2 py-1 text-xs font-medium ${h.mU.NEUTRAL.text_light}`,children:"-"});if("BOBINA_VUOTA"===a||"VUOTA"===a)return(0,t.jsxs)("button",{className:"cavi-table-button bobina",onClick:a=>{a.stopPropagation(),n?.(e,"modify_reel")},title:"Bobina Vuota - Clicca per modificare",children:[(0,t.jsx)("span",{children:"Vuota"}),(0,t.jsx)(eF.A,{className:"icon w-3 h-3 opacity-70"})]});let i=a,s=a.match(/_B(.+)$/);return s||(s=a.match(/_b(.+)$/))||(s=a.match(/c\d+_[bB](\d+)$/))?i=s[1]:(s=a.match(/(\d+)$/))&&(i=s[1]),(0,t.jsxs)("button",{className:"cavi-table-button bobina",onClick:a=>{a.stopPropagation(),n?.(e,"modify_reel")},title:`Bobina ${i} - Clicca per modificare`,children:[(0,t.jsx)("span",{children:i}),(0,t.jsx)(eF.A,{className:"icon w-3 h-3 opacity-70"})]})},O=e=>{let a=(e.metri_posati||e.metratura_reale||0)>0,i=e.stato_installazione||"Da installare",s=e.comanda_posa,r=e.comanda_partenza,l=e.comanda_arrivo,o=e.comanda_certificazione,c=s||r||l||o,d=i,m=(0,h.Tr)(i);c&&"In corso"===i&&(d=c,m=(0,h.Tr)("IN_CORSO")),a&&"Installato"!==i&&(d="Installato",m=(0,h.Tr)("INSTALLATO"));let x=e=>{switch(e.toLowerCase()){case"installato":return(0,t.jsx)(eM.A,{className:"icon w-3 h-3"});case"in corso":return(0,t.jsx)(eL.A,{className:"icon w-3 h-3"});case"da installare":return(0,t.jsx)(eP.A,{className:"icon w-3 h-3"});default:return c?(0,t.jsx)(eE,{className:"icon w-3 h-3"}):(0,t.jsx)(eP.A,{className:"icon w-3 h-3"})}};return"da installare"!==i.toLowerCase()||a?(0,t.jsxs)("span",{className:`inline-flex items-center gap-1 px-2 py-1 rounded text-xs font-medium ${m.text} ${m.bg} ${m.border}`,title:c?`Comanda attiva: ${c}`:`Stato: ${d}`,children:[x(d),(0,t.jsx)("span",{children:d})]}):(0,t.jsxs)("button",{className:"cavi-table-button stato",onClick:a=>{a.stopPropagation(),n?.(e,"insert_meters")},title:"Clicca per inserire metri posati",children:[x(d),(0,t.jsx)("span",{children:d})]})},$=e=>{let a=e.metri_posati>0||e.metratura_reale>0,i=e.collegamento||e.collegamenti||0;(0,h.Nj)();let s=(0,h.NM)();if(!a)return(0,t.jsxs)("span",{className:s.text,title:"Collegamento disponibile solo per cavi installati",children:[(0,t.jsx)(eD.A,{className:"w-3 h-3"}),(0,t.jsx)("span",{children:"Non disponibile"})]});let r=(a,i,s)=>(0,t.jsxs)("button",{className:"cavi-table-button collegamenti",onClick:a=>{a.stopPropagation(),n?.(e,i)},title:`Clicca per ${a.toLowerCase()}`,children:[(0,t.jsx)("span",{className:"icon text-sm mr-1",children:s}),(0,t.jsx)("span",{children:a})]});switch(i){case 0:return r("Collega","connect_cable","⚪⚪");case 1:return r("Completa collegamento","connect_arrival","\uD83D\uDFE2⚪");case 2:return r("Completa collegamento","connect_departure","⚪\uD83D\uDFE2");case 3:return r("Scollega","disconnect_cable","\uD83D\uDFE2\uD83D\uDFE2");default:return r("Gestisci collegamenti","manage_connections","⚙️")}},F=e=>{let a=e.metri_posati>0||e.metratura_reale>0,i=!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato,s=(0,h.NM)();return a?i?(0,t.jsxs)("button",{className:"cavi-table-button certificazioni",onClick:a=>{a.stopPropagation(),n?.(e,"generate_pdf")},title:"Certificato - Clicca per generare PDF",children:[(0,t.jsx)(eM.A,{className:"icon w-3 h-3"}),(0,t.jsx)("span",{children:"PDF"})]}):(0,t.jsxs)("button",{className:"cavi-table-button certificazioni",onClick:a=>{a.stopPropagation(),n?.(e,"create_certificate")},title:"Clicca per certificare il cavo",children:[(0,t.jsx)(ek.A,{className:"icon w-3 h-3"}),(0,t.jsx)("span",{children:"Certifica"})]}):(0,t.jsxs)("span",{className:s.text,title:"Certificazione disponibile solo per cavi installati",children:[(0,t.jsx)(eD.A,{className:"w-3 h-3"}),(0,t.jsx)("span",{children:"Non disponibile"})]})};return(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(ef,{cavi:e,onFilteredDataChange:e=>{d(e)},loading:a,selectionEnabled:b,onSelectionToggle:()=>{j(!b)},selectedCount:r.length,totalCount:c.length}),(0,t.jsx)(ep,{data:c,columns:k,loading:a,emptyMessage:"Nessun cavo disponibile",onFilteredDataChange:e=>{g(e)},renderRow:(e,a)=>{let i=r.includes(e.id_cavo);return(0,t.jsx)(v.Hj,{className:`
          ${i?"bg-blue-50 border-blue-200":"bg-white"}
          hover:bg-blue-50 hover:border-blue-200 hover:shadow-sm
          cursor-pointer border-b border-gray-200
          transition-all duration-200 ease-in-out
          ${i?"ring-1 ring-blue-300":""}
        `,onClick:()=>b&&C(e.id_cavo,!i),onContextMenu:a=>{a.preventDefault(),N({isOpen:!0,position:{x:a.clientX,y:a.clientY},cavo:e})},children:k.map(a=>(0,t.jsx)(v.nA,{className:`
              py-2 px-2 text-sm text-left
              ${i?"text-blue-900":"text-gray-900"}
              transition-colors duration-200
            `,style:{width:a.width,...a.cellStyle},onClick:e=>{["stato_installazione","collegamenti","certificato"].includes(a.field)&&e.stopPropagation()},children:a.renderCell?a.renderCell(e):e[a.field]||(0,t.jsx)("span",{className:"text-gray-400",children:"-"})},a.field))},e.id_cavo)}}),b&&r.length>0&&(0,t.jsx)("div",{className:"sticky bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-10",children:(0,t.jsxs)("div",{className:"flex items-center justify-between p-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)(u.E,{variant:"secondary",className:"bg-mariner-100 text-mariner-800",children:[r.length," cavi selezionati"]}),(0,t.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>y(!1),className:"text-xs",children:"Deseleziona tutto"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(x.$,{variant:"outline",size:"sm",onClick:()=>_(),className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDCCA"}),(0,t.jsx)("span",{children:"Esporta"})]}),(0,t.jsxs)(x.$,{variant:"outline",size:"sm",onClick:()=>z(),className:"flex items-center space-x-1 border-blue-200 text-blue-700 hover:bg-blue-50",children:[(0,t.jsx)(ek.A,{className:"w-3 h-3"}),(0,t.jsx)("span",{children:"Certifica"})]}),(0,t.jsxs)(x.$,{variant:"outline",size:"sm",onClick:()=>w(),className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDD04"}),(0,t.jsx)("span",{children:"Cambia Stato"})]}),(0,t.jsxs)(x.$,{variant:"outline",size:"sm",onClick:()=>A(),className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDCCB"}),(0,t.jsx)("span",{children:"Assegna Comanda"})]}),(0,t.jsxs)(x.$,{variant:"destructive",size:"sm",onClick:()=>S(),className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDDD1️"}),(0,t.jsx)("span",{children:"Elimina"})]})]})]})}),(0,t.jsx)(e$,{isOpen:f.isOpen,position:f.position,cavo:f.cavo,isSelected:!!f.cavo&&r.includes(f.cavo.id_cavo),hasMultipleSelection:r.length>1,totalSelectedCount:r.length,onAction:(e,a)=>{o?.(e,a)},onClose:()=>N({isOpen:!1,position:{x:0,y:0},cavo:null})})]})}var eB=i(53411),eG=i(23361),eV=i(43649),eq=i(45583);let eJ=({content:e,children:a,position:i="auto",delay:r=500,className:l="",disabled:n=!1,maxWidth:o=250})=>{let[c,d]=(0,s.useState)(!1),[m,x]=(0,s.useState)(null),u=(0,s.useRef)(null),p=(0,s.useRef)(null),h=(0,s.useRef)(null),v=()=>{if(!u.current)return null;let e=u.current.getBoundingClientRect(),a=window.innerWidth,t=window.innerHeight,s=i,r=0,l=0;if("auto"===i){let i=e.top,r=t-e.bottom;e.left;let l=a-e.right;s=i>40&&i>r?"top":r>40?"bottom":l>o?"right":"left"}switch(s){case"top":r=e.top-40-8,l=e.left+e.width/2-o/2;break;case"bottom":r=e.bottom+8,l=e.left+e.width/2-o/2;break;case"left":r=e.top+e.height/2-20,l=e.left-o-8;break;case"right":r=e.top+e.height/2-20,l=e.right+8}return l=Math.max(8,Math.min(l,a-o-8)),{top:r=Math.max(8,Math.min(r,t-40-8)),left:l,position:s}},g=()=>{n||(h.current&&clearTimeout(h.current),h.current=setTimeout(()=>{let e=v();e&&(x(e),d(!0))},r))},b=()=>{h.current&&(clearTimeout(h.current),h.current=null),d(!1),x(null)};(0,s.useEffect)(()=>()=>{h.current&&clearTimeout(h.current)},[]);let j=c&&m?(0,t.jsxs)("div",{ref:p,className:`fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg pointer-events-none transition-opacity duration-200 ${l}`,style:{top:m.top,left:m.left,maxWidth:o,wordWrap:"break-word",whiteSpace:"normal"},role:"tooltip","aria-hidden":!c,children:[e,(0,t.jsx)("div",{className:(e=>{let a="absolute w-0 h-0 border-solid";switch(e){case"top":return`${a} top-full left-1/2 transform -translate-x-1/2 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-gray-900`;case"bottom":return`${a} bottom-full left-1/2 transform -translate-x-1/2 border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-gray-900`;case"left":return`${a} left-full top-1/2 transform -translate-y-1/2 border-t-4 border-b-4 border-l-4 border-t-transparent border-b-transparent border-l-gray-900`;case"right":return`${a} right-full top-1/2 transform -translate-y-1/2 border-t-4 border-b-4 border-r-4 border-t-transparent border-b-transparent border-r-gray-900`;default:return a}})(m.position)})]}):null;return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{ref:u,onMouseEnter:g,onMouseLeave:b,onFocus:g,onBlur:b,className:"inline-block","aria-describedby":c?"tooltip":void 0,children:a}),"undefined"!=typeof document&&j&&(0,ey.createPortal)(j,document.body)]})},eW=({type:e,count:a,percentage:i,children:s})=>(0,t.jsx)(eJ,{content:(()=>{let t=`${a} cavi`,s=void 0!==i?` (${i.toFixed(1)}%)`:"";switch(e){case"total":return`Totale cavi nel progetto: ${t}`;case"installed":return`Cavi fisicamente installati: ${t}${s}`;case"in_progress":return`Cavi in corso di installazione: ${t}${s}`;case"to_install":return`Cavi ancora da installare: ${t}${s}`;case"connected":return`Cavi completamente collegati: ${t}${s}`;case"certified":return`Cavi certificati e collaudati: ${t}${s}`;default:return t}})(),delay:200,position:"bottom",children:s});function eZ({cavi:e,filteredCavi:a,className:i,revisioneCorrente:r}){let n=(0,s.useMemo)(()=>{let i=e.length,t=a.length,s=a.filter(e=>"Installato"===e.stato_installazione||e.metri_posati&&e.metri_posati>0||e.metratura_reale&&e.metratura_reale>0).length,r=a.filter(e=>"In corso"===e.stato_installazione).length,l=a.filter(e=>3===(e.collegamento||e.collegamenti||0)).length,n=a.filter(e=>{let a=e.collegamento||e.collegamenti||0;return 1===a||2===a}).length,o=a.filter(e=>0===(e.collegamento||e.collegamenti||0)&&(e.metri_posati>0||e.metratura_reale>0)).length,c=a.filter(e=>!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato).length,d=a.reduce((e,a)=>e+(a.metri_teorici||0),0),m=a.reduce((e,a)=>e+(a.metri_posati||a.metratura_reale||0),0),x=0===t?0:Math.round(100*(((s-l)*2+(l-c)*3.5+4*c)/(4*t)*100))/100;return{totalCavi:i,filteredCount:t,installati:s,inCorso:r,daInstallare:t-s-r,collegati:l,parzialmenteCollegati:n,nonCollegati:o,certificati:c,metriTotali:d,metriInstallati:m,percentualeInstallazione:x}},[e,a]);return(0,t.jsx)(l.Zp,{className:i,children:(0,t.jsxs)(l.Wu,{className:"p-1.5",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1.5",children:[(0,t.jsx)(eB.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,t.jsx)("span",{className:"text-xs font-semibold text-mariner-900",children:"Statistiche Cavi"})]}),(0,t.jsx)("div",{className:"flex items-center space-x-1",children:r&&(0,t.jsxs)(u.E,{variant:"outline",className:"text-xs font-medium py-0 px-1.5 h-5",children:["Rev. ",r]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2",children:[(0,t.jsx)(eW,{type:"total",count:n.totalCavi,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-mariner-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(eG.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-mariner-900 text-sm",children:n.filteredCount}),(0,t.jsxs)("div",{className:"text-xs text-mariner-600",children:["di ",n.totalCavi," cavi"]})]})]})}),(0,t.jsx)(eW,{type:"installed",count:n.installati,percentage:n.installati/n.filteredCount*100,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-green-50 px-1.5 py-1 rounded-lg","aria-label":`Cavi installati: ${n.installati} cavi`,children:[(0,t.jsx)(eM.A,{className:"h-3.5 w-3.5 text-green-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-green-700 text-sm",children:n.installati}),(0,t.jsx)("div",{className:"text-xs text-green-600",children:"installati"})]})]})}),(0,t.jsx)(eW,{type:"in_progress",count:n.inCorso,percentage:n.inCorso/n.filteredCount*100,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-yellow-50 px-1.5 py-1 rounded-lg","aria-label":`Cavi in corso: ${n.inCorso} cavi`,children:[(0,t.jsx)(eL.A,{className:"h-3.5 w-3.5 text-yellow-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-yellow-700 text-sm",children:n.inCorso}),(0,t.jsx)("div",{className:"text-xs text-yellow-600",children:"in corso"})]})]})}),(0,t.jsx)(eW,{type:"to_install",count:n.daInstallare,percentage:n.daInstallare/n.filteredCount*100,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-gray-50 px-1.5 py-1 rounded-lg","aria-label":`Cavi da installare: ${n.daInstallare} cavi`,children:[(0,t.jsx)(eV.A,{className:"h-3.5 w-3.5 text-gray-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-gray-700 text-sm",children:n.daInstallare}),(0,t.jsx)("div",{className:"text-xs text-gray-600",children:"da installare"})]})]})}),(0,t.jsx)(eW,{type:"connected",count:n.collegati,percentage:n.collegati/n.filteredCount*100,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-blue-50 px-1.5 py-1 rounded-lg","aria-label":`Cavi collegati: ${n.collegati} cavi`,children:[(0,t.jsx)(eq.A,{className:"h-3.5 w-3.5 text-blue-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-blue-700 text-sm",children:n.collegati}),(0,t.jsx)("div",{className:"text-xs text-blue-600",children:"collegati"})]})]})}),(0,t.jsx)(eW,{type:"certified",count:n.certificati,percentage:n.certificati/n.filteredCount*100,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-purple-50 px-1.5 py-1 rounded-lg","aria-label":`Cavi certificati: ${n.certificati} cavi`,children:[(0,t.jsx)(eS.A,{className:"h-3.5 w-3.5 text-purple-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-purple-700 text-sm",children:n.certificati}),(0,t.jsx)("div",{className:"text-xs text-purple-600",children:"certificati"})]})]})}),(0,t.jsx)(eW,{type:"total",count:n.metriInstallati,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-indigo-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)("div",{className:"h-3.5 w-3.5 flex items-center justify-center",children:(0,t.jsx)("div",{className:"h-2 w-2 bg-indigo-600 rounded-full"})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"font-bold text-indigo-700 text-sm",children:[n.metriInstallati.toLocaleString(),"m"]}),(0,t.jsxs)("div",{className:"text-xs text-indigo-600",children:["di ",n.metriTotali.toLocaleString(),"m"]})]})]})})]}),n.filteredCount>0&&(0,t.jsxs)("div",{className:"mt-2 bg-gray-50 p-2 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex justify-between text-xs font-medium text-gray-700 mb-1",children:[(0,t.jsx)("span",{children:"IAP - Indice Avanzamento Ponderato"}),(0,t.jsxs)("span",{className:`font-bold ${n.percentualeInstallazione>=80?"text-emerald-700":n.percentualeInstallazione>=50?"text-yellow-700":n.percentualeInstallazione>=25?"text-orange-700":"text-amber-700"}`,children:[n.percentualeInstallazione.toFixed(1),"%"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:`h-2 rounded-full transition-all duration-500 ease-in-out ${n.percentualeInstallazione>=80?"bg-gradient-to-r from-emerald-500 to-emerald-600":n.percentualeInstallazione>=50?"bg-gradient-to-r from-yellow-500 to-yellow-600":n.percentualeInstallazione>=25?"bg-gradient-to-r from-orange-500 to-orange-600":"bg-gradient-to-r from-amber-500 to-amber-600"}`,style:{width:`${Math.min(n.percentualeInstallazione,100)}%`}})}),(0,t.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-0.5",children:[(0,t.jsx)("span",{children:"Pesi: Posa(2.0) + Collegamento(1.5) + Certificazione(0.5)"}),(0,t.jsxs)("span",{children:[n.installati,"I + ",n.collegati,"C + ",n.certificati,"Cert"]})]})]})]})})}var eH=i(63503),eK=i(80013),eX=i(58869),eY=i(41862),eQ=i(29867);let e0=e=>0===(e=e||0)?{icon:"⚪",text:"Non collegato",color:"text-gray-600"}:1===e||2===e?{icon:"\uD83D\uDFE1",text:"Parzialmente collegato",color:"text-yellow-600"}:3===e?{icon:"\uD83D\uDFE2",text:"Completamente collegato",color:"text-green-600"}:{icon:"❓",text:`Sconosciuto (${e})`,color:"text-gray-600"},e1=(e,a)=>(e=e||0,"partenza"===a)?1&e?{icon:"\uD83D\uDFE2",text:"Collegato"}:{icon:"⚪",text:"Non collegato"}:2&e?{icon:"\uD83D\uDFE2",text:"Collegato"}:{icon:"⚪",text:"Non collegato"},e2=[{value:"cantiere",label:"Cantiere"},{value:"elettricista",label:"Elettricista"},{value:"tecnico",label:"Tecnico"},{value:"supervisore",label:"Supervisore"}];function e4({open:e,onClose:a,cavo:i,cantiere:r,onSuccess:l}){let[c,d]=(0,s.useState)(!1),[u,p]=(0,s.useState)(""),[h,v]=(0,s.useState)(""),{cantiere:g}=(0,o.A)(),{toast:j}=(0,eQ.dj)(),f=r||g,N=i?.collegamenti||0,y=(1&N)==1,C=(2&N)==2,_=(()=>{if(0===N)return{title:"Azioni di Collegamento",type:"collegamento",actions:[{id:"partenza",label:"Collega Partenza",icon:"⚡",variant:"default"},{id:"arrivo",label:"Collega Arrivo",icon:"⚡",variant:"default"},{id:"entrambi",label:"Collega Entrambi i Lati",icon:"⚡",variant:"default"}]};{if(3===N)return{title:"Azioni di Scollegamento",type:"scollegamento",actions:[{id:"scollega-partenza",label:"Scollega Partenza",icon:"⛔",variant:"destructive"},{id:"scollega-arrivo",label:"Scollega Arrivo",icon:"⛔",variant:"destructive"},{id:"scollega-entrambi",label:"Scollega Entrambi i Lati",icon:"⛔",variant:"destructive"}]};let e=[];return y?e.push({id:"scollega-partenza",label:"Scollega Partenza",icon:"⛔",variant:"destructive"}):e.push({id:"partenza",label:"Collega Partenza",icon:"⚡",variant:"default"}),C?e.push({id:"scollega-arrivo",label:"Scollega Arrivo",icon:"⛔",variant:"destructive"}):e.push({id:"arrivo",label:"Collega Arrivo",icon:"⚡",variant:"default"}),{title:"Azioni Disponibili",type:"misto",actions:e}}})(),w=async e=>{if(!h&&!e.startsWith("scollega"))return void p("Seleziona un responsabile per abilitare le azioni di collegamento");if(!i||!f){console.error("\uD83D\uDD0C CollegamentiDialogSimple: Mancano dati necessari",{cavo:i,cantiere:f}),p("Errore: dati del cavo o cantiere mancanti. Ricarica la pagina.");return}if(!localStorage.getItem("token")){p("Utente non autenticato. Effettua il login."),j({title:"Errore autenticazione",description:"Utente non autenticato. Effettua il login.",variant:"destructive"});return}d(!0),p("");try{switch(e){case"partenza":await m.At.collegaCavo(f.id_cantiere,i.id_cavo,"partenza",h),j({title:"Successo",description:"Lato partenza collegato con successo!"});break;case"arrivo":await m.At.collegaCavo(f.id_cantiere,i.id_cavo,"arrivo",h),j({title:"Successo",description:"Lato arrivo collegato con successo!"});break;case"entrambi":let t=[];y||(await m.At.collegaCavo(f.id_cantiere,i.id_cavo,"partenza",h),t.push("Lato partenza")),C||(await m.At.collegaCavo(f.id_cantiere,i.id_cavo,"arrivo",h),t.push("Lato arrivo")),t.length>0&&j({title:"Successo",description:`${t.join(" e ")} collegati con successo!`});break;case"scollega-partenza":await m.At.scollegaCavo(f.id_cantiere,i.id_cavo,"partenza"),j({title:"Successo",description:"Lato partenza scollegato con successo!"});break;case"scollega-arrivo":await m.At.scollegaCavo(f.id_cantiere,i.id_cavo,"arrivo"),j({title:"Successo",description:"Lato arrivo scollegato con successo!"});break;case"scollega-entrambi":await m.At.scollegaCavo(f.id_cantiere,i.id_cavo),j({title:"Successo",description:"Entrambi i lati scollegati con successo!"});break;default:throw Error(`Azione non riconosciuta: ${e}`)}l?.(),a()}catch(a){console.error("Errore collegamenti:",a);let e=a?.response?.data?.detail||a?.message||"Errore durante l'operazione";p(e),j({title:"Errore",description:e,variant:"destructive"})}finally{d(!1)}};return(0,t.jsx)(t.Fragment,{children:(0,t.jsx)(eH.lG,{open:e,onOpenChange:a,children:(0,t.jsxs)(eH.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(eH.c7,{children:[(0,t.jsxs)(eH.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(eq.A,{className:"h-5 w-5 text-blue-600"}),"Gestione Collegamenti - Cavo: ",i.id_cavo]}),(0,t.jsxs)(eH.rr,{children:["Gestisci i collegamenti del cavo ",i.id_cavo]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 font-medium text-sm text-gray-700",children:[(0,t.jsx)(eS.A,{className:"h-4 w-4"}),"Informazioni Cavo"]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Partenza:"}),(0,t.jsx)("span",{className:"font-medium text-gray-900",children:i.ubicazione_partenza||"N/A"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Arrivo:"}),(0,t.jsx)("span",{className:"font-medium text-gray-900",children:i.ubicazione_arrivo||"N/A"})]})]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 space-y-3",children:[(0,t.jsx)("div",{className:"font-medium text-sm text-gray-700",children:"Stato Attuale Collegamenti"}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,t.jsx)("span",{className:"text-2xl",children:e0(N).icon}),(0,t.jsx)("span",{className:`font-medium ${e0(N).color}`,children:e0(N).text})]}),(0,t.jsxs)("div",{className:"space-y-2 text-sm border-t pt-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"text-lg",children:e1(N,"partenza").icon}),(0,t.jsx)("span",{className:"text-gray-600",children:"Lato Partenza:"}),(0,t.jsx)("span",{className:"font-medium",children:e1(N,"partenza").text})]}),y&&i.responsabile_partenza&&(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["(Resp: ",i.responsabile_partenza,")"]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"text-lg",children:e1(N,"arrivo").icon}),(0,t.jsx)("span",{className:"text-gray-600",children:"Lato Arrivo:"}),(0,t.jsx)("span",{className:"font-medium",children:e1(N,"arrivo").text})]}),C&&i.responsabile_arrivo&&(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["(Resp: ",i.responsabile_arrivo,")"]})]})]})]}),u&&(0,t.jsxs)(n.Fc,{variant:"destructive",children:[(0,t.jsx)(eV.A,{className:"h-4 w-4"}),(0,t.jsx)(n.TN,{children:u})]}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 space-y-4",children:[(0,t.jsx)("div",{className:"font-medium text-sm text-gray-700",children:_.title}),"scollegamento"!==_.type&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(eK.J,{htmlFor:"responsabile",className:"flex items-center gap-2 text-sm",children:[(0,t.jsx)(eX.A,{className:"h-4 w-4"}),"Seleziona responsabile..."]}),(0,t.jsxs)(b.l6,{value:h,onValueChange:v,children:[(0,t.jsx)(b.bq,{className:"w-full",children:(0,t.jsx)(b.yv,{placeholder:"Seleziona responsabile..."})}),(0,t.jsx)(b.gC,{children:e2.map(e=>(0,t.jsx)(b.eb,{value:e.value,children:e.label},e.value))})]}),!h&&"scollegamento"!==_.type&&(0,t.jsxs)("div",{className:"flex items-center gap-2 text-xs text-amber-600",children:[(0,t.jsx)(eV.A,{className:"h-3 w-3"}),"Seleziona un responsabile per abilitare le azioni"]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{className:"text-sm text-gray-600",children:"Azioni Disponibili:"}),(0,t.jsx)("div",{className:"grid grid-cols-1 gap-2",children:_.actions.map(e=>(0,t.jsxs)(x.$,{onClick:()=>w(e.id),disabled:c||!h&&!e.id.startsWith("scollega"),variant:e.variant,className:"w-full justify-start",children:[c?(0,t.jsx)(eY.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,t.jsx)("span",{className:"mr-2",children:e.icon}),e.label]},e.id))})]})]})]}),(0,t.jsxs)(eH.Es,{className:"gap-2",children:[(0,t.jsx)(x.$,{variant:"outline",onClick:a,disabled:c,children:"Annulla"}),(0,t.jsxs)(x.$,{onClick:a,disabled:c,className:"bg-green-600 hover:bg-green-700 text-white",children:[(0,t.jsx)(eM.A,{className:"mr-2 h-4 w-4"}),"Chiudi"]})]})]})})})}let e5=(0,es.A)("circle-help",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var e3=i(17680);let e6=({icon:e,title:a,cableId:i,description:s})=>(0,t.jsxs)(eH.c7,{children:[(0,t.jsxs)(eH.L3,{className:"flex items-center gap-2",children:[e,(0,t.jsxs)("span",{className:"flex items-center gap-2",children:[a,(0,t.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-sm font-mono font-semibold",children:i})]})]}),s&&(0,t.jsx)(eH.rr,{className:"text-sm text-muted-foreground",children:s})]}),e8=({children:e,className:a="sm:max-w-md",onKeyDown:i,ariaLabelledBy:s,ariaDescribedBy:r})=>(0,t.jsx)(eH.Cf,{className:a,onKeyDown:i,"aria-labelledby":s,"aria-describedby":r,onPointerDownOutside:e=>e.preventDefault(),onEscapeKeyDown:e=>{i&&i(e)},children:e}),e7=({open:e,onClose:a,cavo:i,onConfirm:r})=>{let[l,o]=(0,s.useState)(!1),[c,d]=(0,s.useState)(!1),m=async()=>{if(i){o(!0);try{await r(i.id_cavo),a(),d(!1)}catch(e){console.error("Error disconnecting cable:",e)}finally{o(!1)}}},u=()=>{d(!1),a()};return i?(0,t.jsx)(eH.lG,{open:e,onOpenChange:u,children:(0,t.jsxs)(e8,{className:"sm:max-w-md",onKeyDown:e=>{"Escape"===e.key&&u()},ariaLabelledBy:"disconnect-modal-title",ariaDescribedBy:"disconnect-modal-description",children:[(0,t.jsx)(e6,{icon:(0,t.jsx)(eq.A,{className:"h-5 w-5 text-orange-500"}),title:"Gestione Collegamenti",cableId:i.id_cavo,description:"Gestisci le connessioni del cavo selezionato"}),c?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"py-4 text-center",children:[(0,t.jsx)("div",{className:"mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4",children:(0,t.jsx)(eV.A,{className:"h-6 w-6 text-red-600"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Conferma Scollegamento"}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 mb-4",children:["Sei veramente sicuro di voler scollegare completamente il cavo ",(0,t.jsx)("strong",{children:i.id_cavo}),"?"]}),(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,t.jsx)("p",{className:"text-sm text-red-800 font-medium",children:"⚠️ Questa azione \xe8 irreversibile"})})]}),(0,t.jsxs)(eH.Es,{className:"gap-2",children:[(0,t.jsx)(x.$,{variant:"outline",onClick:()=>d(!1),disabled:l,className:"flex-1",children:"No, Annulla"}),(0,t.jsx)(x.$,{variant:"destructive",onClick:m,disabled:l,className:"flex-1",children:l?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(eY.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Scollegando..."]}):"S\xec, Scollega"})]})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"py-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-500"}),(0,t.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-500"})]}),(0,t.jsxs)("span",{className:"text-sm font-medium text-green-700",children:["Completamente collegato",(0,t.jsx)(e5,{className:"inline h-4 w-4 ml-1 cursor-help",title:"Cavo collegato sia all'origine che alla destinazione"})]})]}),(0,t.jsx)("div",{className:"space-y-3",children:(0,t.jsxs)("div",{children:[(0,t.jsx)(eK.J,{htmlFor:"responsabile-collegamento",className:"text-sm font-medium",children:"Responsabile Collegamento"}),(0,t.jsxs)("select",{id:"responsabile-collegamento",className:"w-full mt-1 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500",defaultValue:"",children:[(0,t.jsx)("option",{value:"",disabled:!0,children:"Seleziona responsabile..."}),(0,t.jsx)("option",{value:"cantiere",children:"Cantiere"}),(0,t.jsx)("option",{value:"tecnico1",children:"Tecnico 1"}),(0,t.jsx)("option",{value:"tecnico2",children:"Tecnico 2"})]})]})})]}),(0,t.jsxs)(n.Fc,{className:"my-4 bg-orange-50 border-orange-200",children:[(0,t.jsx)(eV.A,{className:"h-4 w-4 text-orange-600"}),(0,t.jsxs)(n.TN,{className:"text-orange-800",children:[(0,t.jsx)("strong",{children:"Attenzione:"})," Lo scollegamento rimuover\xe0 tutte le connessioni attive del cavo. Questa azione potrebbe influenzare altri componenti collegati."]})]}),(0,t.jsxs)(eH.Es,{className:"gap-2",children:[(0,t.jsx)(x.$,{variant:"outline",onClick:u,disabled:l,className:"flex-1 hover:bg-gray-50",children:"Annulla"}),(0,t.jsxs)(x.$,{variant:"destructive",onClick:()=>{d(!0)},disabled:l,className:"flex-1 hover:bg-red-600",children:[(0,t.jsx)(eV.A,{className:"mr-2 h-4 w-4"}),"Scollega Completamente"]})]})]})]})}):null},e9=({open:e,onClose:a,cavo:i,onGenerate:r})=>{let[l,n]=(0,s.useState)(!1),[o,c]=(0,s.useState)({fileName:"",includeTestData:!0,format:"standard",emailRecipient:""}),[d,m]=(0,s.useState)({});(0,s.useEffect)(()=>{i&&e&&(c(e=>({...e,fileName:`Certificato_${i.id_cavo}_${new Date().toISOString().split("T")[0]}.pdf`})),m({}))},[i,e]);let u=()=>{let e={};return o.fileName.trim()?/^[a-zA-Z0-9_\-\s]+\.pdf$/i.test(o.fileName)||(e.fileName="Il nome del file deve terminare con .pdf e contenere solo caratteri validi"):e.fileName="Il nome del file \xe8 obbligatorio",o.emailRecipient&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(o.emailRecipient)&&(e.emailRecipient="Inserisci un indirizzo email valido"),m(e),0===Object.keys(e).length},h=async()=>{if(i&&u()){n(!0);try{await r(i.id_cavo,o),a()}catch(e){console.error("Error generating PDF:",e)}finally{n(!1)}}},v=o.fileName.trim()&&0===Object.keys(d).length;return i?(0,t.jsx)(eH.lG,{open:e,onOpenChange:a,children:(0,t.jsxs)(e8,{className:"sm:max-w-lg",onKeyDown:e=>{"Escape"===e.key&&a()},ariaLabelledBy:"pdf-modal-title",children:[(0,t.jsx)(e6,{icon:(0,t.jsx)(eR.A,{className:"h-5 w-5 text-blue-500"}),title:"Genera Certificato",cableId:i.id_cavo,description:"Configura le opzioni per la generazione del certificato PDF"}),(0,t.jsxs)("div",{className:"space-y-4 py-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"fileName",className:"text-sm font-medium",children:"Nome File *"}),(0,t.jsx)(g.p,{id:"fileName",value:o.fileName,onChange:e=>{c(a=>({...a,fileName:e.target.value})),d.fileName&&m(e=>({...e,fileName:""}))},onBlur:u,placeholder:"Certificato_C001_2025-06-29.pdf",className:d.fileName?"border-red-500 focus:ring-red-500":""}),d.fileName&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(eP.A,{className:"h-3 w-3"}),d.fileName]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(eK.J,{className:"text-sm font-medium",children:"Formato Certificato"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("input",{type:"radio",name:"format",value:"standard",checked:"standard"===o.format,onChange:e=>c(a=>({...a,format:e.target.value})),className:"text-blue-600 focus:ring-blue-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Standard"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Certificato con informazioni essenziali"})]})]}),(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("input",{type:"radio",name:"format",value:"detailed",checked:"detailed"===o.format,onChange:e=>c(a=>({...a,format:e.target.value})),className:"text-blue-600 focus:ring-blue-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Dettagliato"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Certificato con tutti i dati tecnici"})]})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-2 border rounded-md",children:[(0,t.jsx)(p.S,{id:"includeTestData",checked:o.includeTestData,onCheckedChange:e=>c(a=>({...a,includeTestData:e}))}),(0,t.jsxs)("div",{children:[(0,t.jsx)(eK.J,{htmlFor:"includeTestData",className:"text-sm font-medium cursor-pointer",children:"Includi Dati di Collaudo"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Aggiunge i risultati dei test al certificato"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"emailRecipient",className:"text-sm font-medium",children:"Email Destinatario (Opzionale)"}),(0,t.jsx)(g.p,{id:"emailRecipient",type:"email",value:o.emailRecipient,onChange:e=>{c(a=>({...a,emailRecipient:e.target.value})),d.emailRecipient&&m(e=>({...e,emailRecipient:""}))},onBlur:u,placeholder:"<EMAIL>",className:d.emailRecipient?"border-red-500 focus:ring-red-500":""}),d.emailRecipient&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(eP.A,{className:"h-3 w-3"}),d.emailRecipient]})]})]}),(0,t.jsxs)(eH.Es,{className:"gap-2",children:[(0,t.jsx)(x.$,{variant:"outline",onClick:a,disabled:l,className:"flex-1 hover:bg-gray-50",children:"Annulla"}),(0,t.jsx)(x.$,{onClick:h,disabled:l||!v,className:`flex-1 ${!v?"opacity-50 cursor-not-allowed":"hover:bg-blue-600"}`,children:l?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(eY.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Generando..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(eO.A,{className:"mr-2 h-4 w-4"}),"Genera PDF"]})})]})]})}):null},ae=({open:e,onClose:a,cavo:i,errorMessage:s,missingRequirements:r=[]})=>{let l=r.length>0?r:['Il cavo deve essere nello stato "Installato"',"Il cavo deve essere completamente collegato","Tutti i dati di collaudo devono essere presenti"];return i?(0,t.jsx)(eH.lG,{open:e,onOpenChange:a,children:(0,t.jsxs)(e8,{className:"sm:max-w-md",onKeyDown:e=>{"Escape"===e.key&&a()},ariaLabelledBy:"certification-error-title",children:[(0,t.jsx)(e6,{icon:(0,t.jsx)(eP.A,{className:"h-5 w-5 text-red-500"}),title:"Impossibile Certificare Cavo",cableId:i.id_cavo,description:"Il cavo non pu\xf2 essere certificato nel suo stato attuale"}),(0,t.jsxs)("div",{className:"py-4",children:[s&&(0,t.jsxs)(n.Fc,{className:"mb-4 bg-red-50 border-red-200",children:[(0,t.jsx)(eP.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)(n.TN,{className:"text-red-800",children:s})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"text-sm font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,t.jsx)(eV.A,{className:"h-4 w-4 text-amber-500"}),"Requisiti mancanti:"]}),(0,t.jsx)("ul",{className:"space-y-3",children:l.map((e,a)=>(0,t.jsxs)("li",{className:"flex items-start gap-3 p-2 bg-red-50 border border-red-200 rounded-md",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-5 h-5 bg-red-100 rounded-full flex items-center justify-center mt-0.5",children:(0,t.jsx)(eo.A,{className:"h-3 w-3 text-red-600"})}),(0,t.jsx)("span",{className:"text-sm text-red-800",children:e})]},a))})]}),(0,t.jsxs)(n.Fc,{className:"bg-blue-50 border-blue-200",children:[(0,t.jsx)(eM.A,{className:"h-4 w-4 text-blue-600"}),(0,t.jsxs)(n.TN,{className:"text-blue-800",children:[(0,t.jsx)("strong",{children:"Prossimi passi:"})," Completa tutti i requisiti sopra elencati per abilitare la certificazione del cavo."]})]})]})]}),(0,t.jsx)(eH.Es,{children:(0,t.jsxs)(x.$,{onClick:a,className:"w-full hover:bg-blue-600 focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)(eM.A,{className:"mr-2 h-4 w-4"}),"Ho Capito"]})})]})}):null},aa=({open:e,onClose:a,cavo:i,cantiereId:s=1,onCertify:r})=>{if(console.log("\uD83D\uDE80 CertificationModal render:",{open:e,cavo:i?.id_cavo}),!i)return null;let l=async e=>{if(e&&i)try{await r(i.id_cavo,e)}catch(e){console.error("Errore certificazione:",e);return}a()};return(0,t.jsx)(eH.lG,{open:e,onOpenChange:a,children:(0,t.jsxs)(eH.Cf,{className:"max-w-[98vw] w-[98vw] h-[98vh] min-w-[800px] p-0 overflow-hidden",children:[(0,t.jsxs)(eH.L3,{className:"sr-only",children:["Certificazione Cavo ",i.id_cavo]}),(0,t.jsx)(e3.A,{cantiereId:s,certificazione:null,strumenti:[],preselectedCavoId:i.id_cavo,onSuccess:l,onCancel:()=>{a()}})]})})},ai=({open:e,onClose:a,cavi:i,onCertify:r})=>{let[l,n]=(0,s.useState)(!1),[o,c]=(0,s.useState)(!1),[d,m]=(0,s.useState)({responsabile:"",dataCertificazione:new Date().toISOString().split("T")[0],esitoCertificazione:"CONFORME",note:""}),[u,p]=(0,s.useState)({});(0,s.useEffect)(()=>{e&&(m({responsabile:"",dataCertificazione:new Date().toISOString().split("T")[0],esitoCertificazione:"CONFORME",note:""}),p({}),n(!1))},[e]);let h=()=>{let e={};if(d.responsabile.trim()||(e.responsabile="Il responsabile \xe8 obbligatorio"),d.dataCertificazione){let a=new Date(d.dataCertificazione),i=new Date;i.setHours(0,0,0,0),a>i&&(e.dataCertificazione="La data non pu\xf2 essere futura")}else e.dataCertificazione="La data di certificazione \xe8 obbligatoria";return p(e),0===Object.keys(e).length},v=async()=>{if(h()){c(!0);try{let e=i.map(e=>e.id_cavo);await r(e,d),a()}catch(e){console.error("Error bulk certifying cables:",e)}finally{c(!1)}}},b=d.responsabile.trim()&&d.dataCertificazione&&0===Object.keys(u).length;return 0===i.length?null:(0,t.jsx)(eH.lG,{open:e,onOpenChange:a,children:(0,t.jsxs)(e8,{className:"sm:max-w-2xl",onKeyDown:e=>{"Escape"===e.key&&a()},ariaLabelledBy:"bulk-certification-modal-title",children:[(0,t.jsx)(e6,{icon:(0,t.jsx)(ek.A,{className:"h-5 w-5 text-blue-500"}),title:"Certificazione Multipla",cableId:`${i.length} cavi selezionati`,description:"Certifica tutti i cavi selezionati con gli stessi parametri"}),(0,t.jsxs)("div",{className:"space-y-4 py-4",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h4",{className:"text-sm font-semibold text-gray-900",children:"Cavi da certificare:"}),(0,t.jsx)("div",{className:"max-h-32 overflow-y-auto border border-gray-200 rounded-md p-3 bg-gray-50",children:(0,t.jsx)("div",{className:"grid grid-cols-4 gap-2 text-xs",children:i.map(e=>(0,t.jsxs)("div",{className:"flex items-center gap-1 p-1 bg-white rounded border",children:[(0,t.jsx)(eM.A,{className:"w-3 h-3 text-green-500"}),(0,t.jsx)("span",{className:"font-medium",children:e.id_cavo})]},e.id_cavo))})})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"responsabile",className:"text-sm font-medium",children:"Responsabile Certificazione *"}),(0,t.jsxs)("select",{id:"responsabile",value:d.responsabile,onChange:e=>{m(a=>({...a,responsabile:e.target.value})),u.responsabile&&p(e=>({...e,responsabile:""}))},className:`w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${u.responsabile?"border-red-500":"border-gray-300"}`,children:[(0,t.jsx)("option",{value:"",children:"Seleziona responsabile..."}),(0,t.jsx)("option",{value:"Tecnico 1",children:"Tecnico 1"}),(0,t.jsx)("option",{value:"Tecnico 2",children:"Tecnico 2"}),(0,t.jsx)("option",{value:"Supervisore",children:"Supervisore"}),(0,t.jsx)("option",{value:"Capo Cantiere",children:"Capo Cantiere"})]}),u.responsabile&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:u.responsabile})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"dataCertificazione",className:"text-sm font-medium",children:"Data Certificazione *"}),(0,t.jsx)(g.p,{id:"dataCertificazione",type:"date",value:d.dataCertificazione,onChange:e=>{m(a=>({...a,dataCertificazione:e.target.value})),u.dataCertificazione&&p(e=>({...e,dataCertificazione:""}))},className:u.dataCertificazione?"border-red-500":""}),u.dataCertificazione&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:u.dataCertificazione})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{className:"text-sm font-medium",children:"Esito Certificazione"}),(0,t.jsxs)("select",{value:d.esitoCertificazione,onChange:e=>m(a=>({...a,esitoCertificazione:e.target.value})),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,t.jsx)("option",{value:"CONFORME",children:"CONFORME"}),(0,t.jsx)("option",{value:"NON_CONFORME",children:"NON CONFORME"}),(0,t.jsx)("option",{value:"PARZIALMENTE_CONFORME",children:"PARZIALMENTE CONFORME"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"note",className:"text-sm font-medium",children:"Note (opzionale)"}),(0,t.jsx)("textarea",{id:"note",value:d.note,onChange:e=>m(a=>({...a,note:e.target.value})),placeholder:"Inserisci eventuali note sulla certificazione multipla...",rows:3,className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"})]})]}),(0,t.jsxs)(eH.Es,{className:"gap-2",children:[(0,t.jsx)(x.$,{variant:"outline",onClick:a,disabled:o,className:"hover:bg-gray-50",children:"Annulla"}),(0,t.jsx)(x.$,{onClick:v,disabled:o||!b,className:`${!b?"opacity-50 cursor-not-allowed bg-gray-400":"bg-blue-600 hover:bg-blue-700"}`,children:o?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(eY.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Certificando ",i.length," cavi..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(ek.A,{className:"mr-2 h-4 w-4"}),"Certifica ",i.length," Cavi"]})})]})]})})},at=({message:e,visible:a,onClose:i})=>((0,s.useEffect)(()=>{if(a){let e=setTimeout(()=>{i()},3e3);return()=>clearTimeout(e)}},[a,i]),a)?(0,t.jsx)("div",{className:"fixed top-4 right-4 z-50 animate-in slide-in-from-top-2",children:(0,t.jsxs)(n.Fc,{className:"bg-green-50 border-green-200 text-green-800 shadow-lg",children:[(0,t.jsx)(eM.A,{className:"h-4 w-4"}),(0,t.jsx)(n.TN,{className:"font-medium",children:e}),(0,t.jsx)(x.$,{variant:"ghost",size:"sm",className:"absolute top-2 right-2 h-6 w-6 p-0 hover:bg-green-100",onClick:i,children:(0,t.jsx)(eo.A,{className:"h-3 w-3"})})]})}):null;var as=i(49039);i(40510);let ar=(0,es.A)("calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]);function al({open:e,onClose:a,cavo:i,cantiere:r,onSuccess:l,onError:c}){let{cantiere:d}=(0,o.A)(),u=r||d,[p,h]=(0,s.useState)("assegna_nuova"),[v,b]=(0,s.useState)(""),[j,f]=(0,s.useState)([]),[N,y]=(0,s.useState)(!1),[C,_]=(0,s.useState)(!1),[w,z]=(0,s.useState)(""),[A,S]=(0,s.useState)(""),[k,E]=(0,s.useState)("compatibili"),T=(()=>{if(!i)return[];let e=j.filter(e=>{let a=e.tipologia===i.tipologia&&e.sezione===i.sezione,t=""===A||e.id_bobina.toLowerCase().includes(A.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(A.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(A.toLowerCase());return a&&t&&e.metri_residui>0});return console.log("\uD83D\uDD0D ModificaBobinaDialog: Filtro compatibili:",{cavoTipologia:i.tipologia,cavoSezione:i.sezione,totaleBobine:j.length,bobineCompatibili:e.length,searchText:A}),e})(),I=i?j.filter(e=>{let a=e.tipologia!==i.tipologia||e.sezione!==i.sezione,t=""===A||e.id_bobina.toLowerCase().includes(A.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(A.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(A.toLowerCase());return a&&t&&e.metri_residui>0}):[],R=()=>{h("assegna_nuova"),b(""),S(""),z(""),a()},O=async()=>{if(console.log("\uD83D\uDD04 ModificaBobinaDialog: Salvataggio:",{selectedOption:p,selectedBobina:v,cavoId:i?.id_cavo,cantiereId:u?.id_cantiere}),i)try{if(_(!0),z(""),"assegna_nuova"===p){if(!v)return void c("Selezionare una bobina");let e=await m.At.updateMetriPosati({id_cavo:i.id_cavo,metri_posati:i.metratura_reale||0,id_bobina:v,force_over:!0});e.success?(l(`Bobina aggiornata con successo per il cavo ${i.id_cavo}`),R()):c(e.message||"Errore durante l'aggiornamento della bobina")}else if("rimuovi_bobina"===p){let e=await m.At.updateMetriPosati({id_cavo:i.id_cavo,metri_posati:i.metratura_reale||0,id_bobina:"BOBINA_VUOTA",force_over:!1});e.success?(l(`Bobina rimossa dal cavo ${i.id_cavo}`),R()):c(e.message||"Errore durante la rimozione della bobina")}else if("annulla_installazione"===p){let e=await m.At.updateMetriPosati({id_cavo:i.id_cavo,metri_posati:0,id_bobina:"BOBINA_VUOTA",force_over:!1});e.success?(l(`Installazione annullata per il cavo ${i.id_cavo}`),R()):c(e.message||"Errore durante l'annullamento dell'installazione")}}catch(e){console.error("❌ ModificaBobinaDialog: Errore salvataggio:",e),c("Errore durante il salvataggio")}finally{_(!1)}};return i?(0,t.jsx)(t.Fragment,{children:(0,t.jsx)(eH.lG,{open:e,onOpenChange:R,children:(0,t.jsxs)(eH.Cf,{className:"max-w-4xl max-h-[90vh] flex flex-col",children:[(0,t.jsx)(eH.c7,{children:(0,t.jsxs)(eH.L3,{children:["Modifica Bobina Cavo ",i.id_cavo]})}),(0,t.jsxs)("div",{className:"flex-1 overflow-hidden space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(eS.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsx)("h3",{className:"font-medium text-gray-900",children:"Cavo Selezionato"})]}),(0,t.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-200",children:(0,t.jsxs)("div",{className:"text-sm font-medium text-blue-800",children:["Informazioni Cavo / Tipologia: ",i.tipologia||"N/A"," / Da: ",i.ubicazione_partenza||"N/A"," / Formazione: ",i.sezione||"N/A"," / A: ",i.ubicazione_arrivo||"N/A"," / Metri Posati: ",i.metratura_reale||0," m"]})})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Opzioni di modifica"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,t.jsx)("input",{type:"radio",name:"operazione",value:"assegna_nuova",checked:"assegna_nuova"===p,onChange:e=>h(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,t.jsx)("span",{className:"text-sm",children:"Cambia bobina"})]}),(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,t.jsx)("input",{type:"radio",name:"operazione",value:"rimuovi_bobina",checked:"rimuovi_bobina"===p,onChange:e=>h(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,t.jsx)("span",{className:"text-sm",children:"Bobina vuota"})]}),(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,t.jsx)("input",{type:"radio",name:"operazione",value:"annulla_installazione",checked:"annulla_installazione"===p,onChange:e=>h(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,t.jsx)("span",{className:"text-sm text-red-600",children:"Annulla posa"})]})]})]}),"assegna_nuova"===p&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Seleziona bobina"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(ev.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,t.jsx)(g.p,{placeholder:"Cerca bobina per ID, tipologia o numero...",value:A,onChange:e=>S(e.target.value),className:"pl-10"})]}),(0,t.jsxs)("div",{className:"flex space-x-1 border-b",children:[(0,t.jsx)("button",{onClick:()=>E("compatibili"),className:`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${"compatibili"===k?"border-green-500 text-green-600 bg-green-50":"border-transparent text-gray-500 hover:text-gray-700"}`,children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(eM.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["Bobine Compatibili (",T.length,")"]})]})}),(0,t.jsx)("button",{onClick:()=>E("incompatibili"),className:`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${"incompatibili"===k?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700"}`,children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(eP.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["Bobine Incompatibili (",I.length,")"]})]})})]}),(0,t.jsx)("div",{className:"border rounded-lg h-64 overflow-y-auto",children:N?(0,t.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(eY.A,{className:"h-4 w-4 animate-spin"}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Caricamento bobine..."})]})}):(0,t.jsx)("div",{className:"p-2",children:"compatibili"===k?0===T.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"text-gray-500 text-sm mb-2",children:"Nessuna bobina compatibile trovata"}),(0,t.jsxs)("div",{className:"text-xs text-gray-400",children:["Cercando bobine con tipologia ",(0,t.jsx)("strong",{children:i.tipologia})," e formazione ",(0,t.jsx)("strong",{children:i.sezione})]})]}):(0,t.jsx)("div",{className:"space-y-2",children:T.map(e=>(0,t.jsx)("div",{onClick:()=>b(e.id_bobina),className:`p-3 rounded-lg cursor-pointer transition-all duration-200 ${v===e.id_bobina?"bg-blue-100 border-2 border-blue-300 shadow-md":"hover:bg-gray-50 border border-gray-200 hover:border-gray-300"}`,children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,t.jsx)("div",{className:"font-medium text-sm text-gray-900",children:e.id_bobina}),e.stato_bobina&&(0,t.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${"Disponibile"===e.stato_bobina?"bg-green-100 text-green-800":"In uso"===e.stato_bobina?"bg-blue-100 text-blue-800":"bg-yellow-100 text-yellow-800"}`,children:e.stato_bobina})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 mb-1",children:[(0,t.jsx)("span",{className:"font-medium",children:e.tipologia})," • ",(0,t.jsx)("span",{children:e.sezione})]})]}),(0,t.jsxs)("div",{className:"text-right ml-3",children:[(0,t.jsxs)("div",{className:`text-sm font-medium ${e.metri_residui>0?"text-green-600":"text-gray-500"}`,children:[e.metri_residui,"m"]}),(0,t.jsx)("div",{className:"text-xs text-gray-400",children:e.metri_residui>0?"disponibili":"esaurita"})]})]})},e.id_bobina))}):0===I.length?(0,t.jsx)("div",{className:"text-center py-8 text-gray-500 text-sm",children:"Nessuna bobina incompatibile trovata"}):(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"p-3 bg-orange-50 border border-orange-200 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,t.jsx)(eP.A,{className:"h-4 w-4 text-orange-600 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{className:"text-sm text-orange-800",children:[(0,t.jsx)("div",{className:"font-medium mb-1",children:"Bobine Incompatibili"}),(0,t.jsx)("div",{className:"text-xs",children:"Selezionando una bobina incompatibile, le caratteristiche del cavo verranno aggiornate."})]})]})}),I.map(e=>(0,t.jsx)("div",{onClick:()=>b(e.id_bobina),className:`p-3 rounded-lg cursor-pointer transition-all duration-200 ${v===e.id_bobina?"bg-orange-100 border-2 border-orange-300 shadow-md":"hover:bg-gray-50 border border-gray-200 hover:border-gray-300"}`,children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,t.jsx)("div",{className:"font-medium text-sm text-gray-900",children:e.id_bobina}),(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800",children:"INCOMPATIBILE"})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 mb-1",children:[(0,t.jsx)("span",{className:"font-medium",children:e.tipologia})," • ",(0,t.jsx)("span",{children:e.sezione})]})]}),(0,t.jsxs)("div",{className:"text-right ml-3",children:[(0,t.jsxs)("div",{className:`text-sm font-medium ${e.metri_residui>0?"text-orange-600":"text-gray-500"}`,children:[e.metri_residui,"m"]}),(0,t.jsx)("div",{className:"text-xs text-gray-400",children:e.metri_residui>0?"disponibili":"esaurita"})]})]})},e.id_bobina))]})})})]})]}),w&&(0,t.jsxs)(n.Fc,{variant:"destructive",children:[(0,t.jsx)(eP.A,{className:"h-4 w-4"}),(0,t.jsx)(n.TN,{children:w})]}),(0,t.jsxs)(eH.Es,{className:"flex justify-end space-x-2",children:[(0,t.jsx)(x.$,{variant:"outline",onClick:R,disabled:C,children:"Annulla"}),(0,t.jsxs)(x.$,{onClick:O,disabled:C||"assegna_nuova"===p&&!v,children:[C&&(0,t.jsx)(eY.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salva"]})]})]})})}):null}function an({open:e,onClose:a,cavo:i,cantiere:r,onSuccess:l,onError:c}){let{cantiere:d}=(0,o.A)(),p=r||d,[h,v]=(0,s.useState)({metri_posati:"",id_bobina:""}),[b,j]=(0,s.useState)({}),[f,N]=(0,s.useState)({}),[y,C]=(0,s.useState)(!1),[_,w]=(0,s.useState)([]),[z,A]=(0,s.useState)(!1),[S,k]=(0,s.useState)(""),[E,T]=(0,s.useState)(!1),I=e=>{if(!e||"BOBINA_VUOTA"===e)return"VUOTA";if(e&&e.includes("_B"))return e.split("_B")[1];let a=_.find(a=>a.id_bobina===e);return a&&a.numero_bobina||e},R=i?_.filter(e=>{let a=e.tipologia===i.tipologia&&e.sezione===i.sezione,t=""===S||e.id_bobina.toLowerCase().includes(S.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(S.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(S.toLowerCase());return a&&t&&e.metri_residui>0}):[],O=i?_.filter(e=>{let a=e.tipologia!==i.tipologia||e.sezione!==i.sezione,t=""===S||e.id_bobina.toLowerCase().includes(S.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(S.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(S.toLowerCase());return a&&t&&e.metri_residui>0}):[],$=e=>{v(a=>({...a,id_bobina:e.id_bobina})),j(e=>{let a={...e};return delete a.id_bobina,a})},F=async()=>{if(console.log("\uD83D\uDCBE InserisciMetriDialog: Salvataggio metri:",{cavo:i?.id_cavo,metri_posati:h.metri_posati,id_bobina:h.id_bobina}),!i)return;if(!h.metri_posati||0>parseFloat(h.metri_posati))return void c("Inserire metri posati validi (≥ 0)");if(!h.id_bobina)return void c("Selezionare una bobina o BOBINA VUOTA");let e=parseFloat(h.metri_posati);if("BOBINA_VUOTA"!==h.id_bobina){let e=_.find(e=>e.id_bobina===h.id_bobina);e&&e.metri_residui}try{if(C(!0),!p)throw Error("Cantiere non selezionato");console.log("\uD83D\uDE80 InserisciMetriDialog: Chiamata API updateMetriPosati:",{cantiere:p.id_cantiere,cavo:i.id_cavo,metri:e,bobina:h.id_bobina,isBobinaVuota:"BOBINA_VUOTA"===h.id_bobina}),await m.At.updateMetriPosati(p.id_cantiere,i.id_cavo,e,h.id_bobina,!0),l(`Metri posati aggiornati con successo per il cavo ${i.id_cavo}: ${e}m`),a()}catch(e){c(e.response?.data?.detail||e.message||"Errore durante il salvataggio dei metri posati")}finally{C(!1)}},M=()=>{y||(v({metri_posati:"",id_bobina:""}),j({}),N({}),k(""),a())};return i?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(eH.lG,{open:e,onOpenChange:M,children:(0,t.jsxs)(eH.Cf,{className:"max-w-7xl h-[90vh] flex flex-col",children:[(0,t.jsxs)(eH.c7,{className:"flex-shrink-0",children:[(0,t.jsxs)(eH.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(ar,{className:"h-5 w-5"}),"Inserisci Metri Posati - ",i.id_cavo]}),(0,t.jsx)(eH.rr,{children:"Inserisci i metri effettivamente posati per il cavo e seleziona una bobina o usa BOBINA VUOTA"})]}),(0,t.jsxs)("div",{className:"flex-1 overflow-y-auto space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-4",children:[(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-200",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"Informazioni Cavo"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Tipologia:"})," ",i.tipologia||"N/A"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Da:"})," ",i.ubicazione_partenza||"N/A"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Formazione:"})," ",i.sezione||"N/A"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"A:"})," ",i.ubicazione_arrivo||"N/A"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Metri teorici:"})," ",i.metri_teorici||"N/A"," m"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Gi\xe0 posati:"})," ",i.metratura_reale||0," m"]})]})]})}),(0,t.jsx)("div",{className:"lg:col-span-1",children:(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-300 h-full",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"Metri da Installare"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"metri",className:"text-sm font-medium",children:"Metri Posati"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(g.p,{id:"metri",type:"number",value:h.metri_posati,onChange:e=>v(a=>({...a,metri_posati:e.target.value})),placeholder:"Inserisci metri posati",disabled:y,step:"0.1",min:"0",className:"text-lg font-bold text-center border-2 border-blue-400 focus:border-blue-600",autoFocus:!0}),(0,t.jsx)("span",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-lg font-bold text-blue-600",children:"m"})]}),b.metri_posati&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:b.metri_posati}),f.metri_posati&&(0,t.jsx)("p",{className:"text-sm text-amber-600",children:f.metri_posati})]})]})})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-800 text-lg",children:"Selezione Bobina"}),(0,t.jsx)("div",{className:"p-4 bg-gray-50 rounded-lg",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-12 gap-3 items-center",children:[(0,t.jsx)("div",{className:"sm:col-span-5",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(ev.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(g.p,{placeholder:"ID, tipologia, formazione...",value:S,onChange:e=>k(e.target.value),className:"pl-10",disabled:y}),S&&(0,t.jsx)(x.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0",onClick:()=>k(""),children:(0,t.jsx)(eo.A,{className:"h-4 w-4"})})]})}),(0,t.jsx)("div",{className:"sm:col-span-7",children:(0,t.jsxs)(x.$,{type:"button",variant:"BOBINA_VUOTA"===h.id_bobina?"default":"outline",className:`w-full h-10 font-bold flex items-center justify-center gap-2 ${"BOBINA_VUOTA"===h.id_bobina?"bg-green-600 hover:bg-green-700 text-white":"border-blue-400 text-blue-700 hover:bg-blue-50"}`,onClick:()=>{v(e=>({...e,id_bobina:"BOBINA_VUOTA"})),j(e=>{let a={...e};return delete a.id_bobina,a}),console.log("\uD83D\uDD04 InserisciMetriDialog: Usando BOBINA_VUOTA:",{saving:!1,metri_posati:h.metri_posati,id_bobina:"BOBINA_VUOTA",errorsCount:Object.keys(b).length})},disabled:y,children:["BOBINA_VUOTA"===h.id_bobina&&(0,t.jsx)(eM.A,{className:"h-5 w-5"}),"BOBINA VUOTA"]})})]})}),z?(0,t.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,t.jsx)(eY.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,t.jsx)("span",{children:"Caricamento bobine..."})]}):(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-medium text-green-700 mb-2 flex items-center gap-2",children:[(0,t.jsx)(eM.A,{className:"h-4 w-4"}),"Bobine Compatibili (",R.length,")"]}),(0,t.jsx)("div",{className:"max-h-72 overflow-y-auto border rounded-lg",children:0===R.length?(0,t.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina compatibile trovata"}):(0,t.jsx)("div",{className:"divide-y",children:R.map(e=>(0,t.jsx)("div",{className:`p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ${h.id_bobina===e.id_bobina?"bg-green-100 border-green-500 shadow-md":"border-gray-200 hover:bg-green-50 hover:border-green-300"}`,onClick:()=>$(e),children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[h.id_bobina===e.id_bobina&&(0,t.jsx)(eM.A,{className:"h-5 w-5 text-green-600 flex-shrink-0"}),(0,t.jsx)("div",{className:"font-bold text-base min-w-fit",children:I(e.id_bobina)}),(0,t.jsxs)("div",{className:"text-sm text-gray-600 truncate",children:[e.tipologia," - ",e.sezione]})]}),(0,t.jsxs)(u.E,{variant:"outline",className:"bg-green-100 text-green-800 border-green-300 font-medium min-w-fit",children:[e.metri_residui,"m"]})]})},e.id_bobina))})})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-medium text-amber-700 mb-2 flex items-center gap-2",children:[(0,t.jsx)(eV.A,{className:"h-4 w-4"}),"Bobine Incompatibili (",O.length,")"]}),(0,t.jsx)("div",{className:"max-h-72 overflow-y-auto border rounded-lg",children:0===O.length?(0,t.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina incompatibile trovata"}):(0,t.jsx)("div",{className:"divide-y",children:O.map(e=>(0,t.jsx)("div",{className:`p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ${h.id_bobina===e.id_bobina?"bg-amber-100 border-amber-500 shadow-md":"border-gray-200 hover:bg-amber-50 hover:border-amber-300"}`,onClick:()=>$(e),children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[h.id_bobina===e.id_bobina&&(0,t.jsx)(eM.A,{className:"h-5 w-5 text-amber-600 flex-shrink-0"}),(0,t.jsx)("div",{className:"font-bold text-base min-w-fit",children:I(e.id_bobina)}),(0,t.jsxs)("div",{className:"text-sm text-gray-600 truncate",children:[e.tipologia," - ",e.sezione]})]}),(0,t.jsxs)(u.E,{variant:"outline",className:"bg-amber-100 text-amber-800 border-amber-300 font-medium min-w-fit",children:[e.metri_residui,"m"]})]})},e.id_bobina))})})]})]}),0===_.length&&!z&&(0,t.jsxs)(n.Fc,{className:"border-amber-200 bg-amber-50",children:[(0,t.jsx)(eV.A,{className:"h-4 w-4 text-amber-600"}),(0,t.jsx)(n.TN,{className:"text-amber-800",children:"Non ci sono bobine disponibili. Puoi procedere con BOBINA VUOTA o aggiungere prima una nuova bobina."})]}),b.id_bobina&&(0,t.jsxs)(n.Fc,{variant:"destructive",children:[(0,t.jsx)(eP.A,{className:"h-4 w-4"}),(0,t.jsx)(n.TN,{children:b.id_bobina})]})]})]}),(0,t.jsxs)(eH.Es,{className:"flex-shrink-0 border-t pt-4 mt-4 flex justify-between items-center",children:[(0,t.jsx)("div",{children:"installato"===i.stato_installazione&&i.id_bobina&&(0,t.jsx)(x.$,{variant:"outline",onClick:()=>{T(!0)},disabled:y,className:"text-blue-600 border-blue-300 hover:bg-blue-50",children:"Modifica Bobina"})}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(x.$,{variant:"outline",onClick:M,disabled:y,children:"Annulla"}),(0,t.jsxs)(x.$,{onClick:F,disabled:y||!h.metri_posati||0>parseFloat(h.metri_posati)||!h.id_bobina,className:"bg-mariner-600 hover:bg-mariner-700 text-white disabled:bg-gray-400 disabled:text-gray-200",children:[y&&(0,t.jsx)(eY.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salva"]})]})]})]})}),(0,t.jsx)(al,{open:E,onClose:()=>T(!1),cavo:i,onSuccess:e=>{l(e),T(!1),a()},onError:c})]}):null}var ao=i(34729),ac=i(6727),ad=i(41312);function am({open:e,onClose:a,caviSelezionati:i,tipoComanda:r,onSuccess:l,onError:c}){let{cantiere:d}=(0,o.A)(),[u,p]=(0,s.useState)({tipo_comanda:r||"POSA",responsabile:"",note:""}),[h,v]=(0,s.useState)([]),[g,j]=(0,s.useState)(!1),[f,N]=(0,s.useState)(!1),[y,C]=(0,s.useState)(""),_=async()=>{if(d){if(!u.responsabile)return void C("Seleziona un responsabile per la comanda");if(0===i.length)return void C("Seleziona almeno un cavo per la comanda");try{j(!0),C("");let e={tipo_comanda:u.tipo_comanda,responsabile:u.responsabile,note:u.note||null},t=await m.CV.createComandaWithCavi(d.id_cantiere,e,i);l(`Comanda ${t.data.codice_comanda} creata con successo per ${i.length} cavi`),a()}catch(e){c(e.response?.data?.detail||e.message||"Errore durante la creazione della comanda")}finally{j(!1)}}};return(0,t.jsx)(eH.lG,{open:e,onOpenChange:a,children:(0,t.jsxs)(eH.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(eH.c7,{children:[(0,t.jsxs)(eH.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(ac.A,{className:"h-5 w-5"}),"Crea Nuova Comanda"]}),(0,t.jsxs)(eH.rr,{children:["Crea una nuova comanda per ",i.length," cavi selezionati"]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsxs)(eK.J,{className:"text-sm font-medium",children:["Cavi Selezionati (",i.length,")"]}),(0,t.jsx)("div",{className:"mt-2 max-h-32 overflow-y-auto",children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-1",children:[i.slice(0,10).map(e=>(0,t.jsx)("span",{className:"inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded",children:e},e)),i.length>10&&(0,t.jsxs)("span",{className:"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded",children:["+",i.length-10," altri..."]})]})})]}),y&&(0,t.jsxs)(n.Fc,{variant:"destructive",children:[(0,t.jsx)(eP.A,{className:"h-4 w-4"}),(0,t.jsx)(n.TN,{children:y})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"tipo",children:"Tipo Comanda *"}),(0,t.jsxs)(b.l6,{value:u.tipo_comanda,onValueChange:e=>p(a=>({...a,tipo_comanda:e})),children:[(0,t.jsx)(b.bq,{children:(0,t.jsx)(b.yv,{})}),(0,t.jsxs)(b.gC,{children:[(0,t.jsx)(b.eb,{value:"POSA",children:"\uD83D\uDD27 Posa Cavi"}),(0,t.jsx)(b.eb,{value:"COLLEGAMENTO_PARTENZA",children:"\uD83D\uDD0C Collegamento Partenza"}),(0,t.jsx)(b.eb,{value:"COLLEGAMENTO_ARRIVO",children:"⚡ Collegamento Arrivo"}),(0,t.jsx)(b.eb,{value:"CERTIFICAZIONE",children:"\uD83D\uDCCB Certificazione"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"responsabile",children:"Responsabile *"}),(0,t.jsxs)(b.l6,{value:u.responsabile,onValueChange:e=>p(a=>({...a,responsabile:e})),disabled:f,children:[(0,t.jsx)(b.bq,{children:(0,t.jsx)(b.yv,{placeholder:"Seleziona responsabile..."})}),(0,t.jsx)(b.gC,{children:h.map(e=>(0,t.jsx)(b.eb,{value:e.nome_responsabile,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(ad.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.nome_responsabile}),e.numero_telefono&&(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["- ",e.numero_telefono]})]})},e.id))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"note",children:"Note (opzionale)"}),(0,t.jsx)(ao.T,{id:"note",placeholder:"Inserisci eventuali note per la comanda...",value:u.note,onChange:e=>p(a=>({...a,note:e.target.value})),rows:3})]}),(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)(eK.J,{className:"text-sm font-medium",children:"Riepilogo Comanda"}),(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Tipo:"})," ",(e=>{switch(e){case"POSA":return"Posa Cavi";case"COLLEGAMENTO_PARTENZA":return"Collegamento Partenza";case"COLLEGAMENTO_ARRIVO":return"Collegamento Arrivo";case"CERTIFICAZIONE":return"Certificazione";default:return e}})(u.tipo_comanda)]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Responsabile:"})," ",u.responsabile||"Non selezionato"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Cavi:"})," ",i.length," selezionati"]}),u.note&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Note:"})," ",u.note]})]})]})]}),(0,t.jsxs)(eH.Es,{children:[(0,t.jsx)(x.$,{variant:"outline",onClick:a,disabled:g,children:"Annulla"}),(0,t.jsxs)(x.$,{onClick:_,disabled:g||!u.responsabile||0===i.length,children:[g?(0,t.jsx)(eY.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(ac.A,{className:"h-4 w-4 mr-2"}),"Crea Comanda"]})]})]})})}let ax=(0,es.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]),au=(0,es.A)("file-spreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]]);function ap({open:e,onClose:a,tipo:i,onSuccess:r,onError:l}){let{cantiere:c}=(0,o.A)(),[d,u]=(0,s.useState)(null),[p,h]=(0,s.useState)(""),[v,b]=(0,s.useState)(!1),[j,f]=(0,s.useState)(""),[N,y]=(0,s.useState)(0),C=(0,s.useRef)(null),_=async()=>{if(d&&c){if("cavi"===i&&!p.trim())return void f("Inserisci il codice revisione per l'importazione cavi");try{let e;if(b(!0),f(""),y(0),e="cavi"===i?await m.mg.importCavi(c.id_cantiere,d,p.trim()):await m.mg.importBobine(c.id_cantiere,d),y(100),e.data.success){let t=e.data.details,s=e.data.message;"cavi"===i&&t?.cavi_importati?s+=` (${t.cavi_importati} cavi importati)`:"bobine"===i&&t?.bobine_importate&&(s+=` (${t.bobine_importate} bobine importate)`),r(s),a()}else l(e.data.message||"Errore durante l'importazione")}catch(e){l(e.response?.data?.detail||e.message||"Errore durante l'importazione del file")}finally{b(!1),y(0)}}},w=()=>{v||(u(null),h(""),f(""),y(0),C.current&&(C.current.value=""),a())},z=()=>"cavi"===i?"Cavi":"Bobine";return(0,t.jsx)(eH.lG,{open:e,onOpenChange:w,children:(0,t.jsxs)(eH.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(eH.c7,{children:[(0,t.jsxs)(eH.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(ax,{className:"h-5 w-5"}),"Importa ",z()," da Excel"]}),(0,t.jsxs)(eH.rr,{children:["Carica un file Excel per importare ",z().toLowerCase()," nel cantiere"]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)(eK.J,{className:"text-sm font-medium",children:"Requisiti File"}),(0,t.jsx)("ul",{className:"mt-2 space-y-1 text-sm text-gray-600",children:("cavi"===i?["File Excel (.xlsx o .xls)","Colonne richieste: ID_CAVO, SISTEMA, UTILITY, TIPOLOGIA, ecc.","Prima riga deve contenere le intestazioni","Codice revisione obbligatorio per tracciabilit\xe0"]:["File Excel (.xlsx o .xls)","Colonne richieste: NUMERO_BOBINA, UTILITY, TIPOLOGIA, METRI_TOTALI, ecc.","Prima riga deve contenere le intestazioni","I metri residui saranno impostati uguali ai metri totali"]).map((e,a)=>(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)("span",{className:"text-blue-500 mt-0.5",children:"•"}),(0,t.jsx)("span",{children:e})]},a))})]}),j&&(0,t.jsxs)(n.Fc,{variant:"destructive",children:[(0,t.jsx)(eP.A,{className:"h-4 w-4"}),(0,t.jsx)(n.TN,{children:j})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"file",children:"File Excel *"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(g.p,{ref:C,id:"file",type:"file",accept:".xlsx,.xls",onChange:e=>{let a=e.target.files?.[0];if(a){if(!["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel"].includes(a.type)&&!a.name.toLowerCase().endsWith(".xlsx")&&!a.name.toLowerCase().endsWith(".xls"))return void f("Seleziona un file Excel valido (.xlsx o .xls)");u(a),f("")}},disabled:v,className:"flex-1"}),d&&(0,t.jsxs)("div",{className:"flex items-center gap-1 text-green-600",children:[(0,t.jsx)(eM.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm",children:"File selezionato"})]})]}),d&&(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,t.jsx)(au,{className:"h-4 w-4 inline mr-1"}),d.name," (",(d.size/1024/1024).toFixed(2)," MB)"]})]}),"cavi"===i&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"revisione",children:"Codice Revisione *"}),(0,t.jsx)(g.p,{id:"revisione",value:p,onChange:e=>h(e.target.value),placeholder:"es. REV001, V1.0, 2024-01",disabled:v}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Codice identificativo della revisione per tracciabilit\xe0 delle modifiche"})]}),v&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(eY.A,{className:"h-4 w-4 animate-spin"}),(0,t.jsx)("span",{className:"text-sm",children:"Caricamento in corso..."})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${N}%`}})})]}),d&&(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsx)(eK.J,{className:"text-sm font-medium",children:"Riepilogo Importazione"}),(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Tipo:"})," ",z()]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"File:"})," ",d.name]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Dimensione:"})," ",(d.size/1024/1024).toFixed(2)," MB"]}),"cavi"===i&&p&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Revisione:"})," ",p]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Cantiere:"})," ",c?.nome_cantiere]})]})]})]}),(0,t.jsxs)(eH.Es,{children:[(0,t.jsx)(x.$,{variant:"outline",onClick:w,disabled:v,children:"Annulla"}),(0,t.jsxs)(x.$,{onClick:_,disabled:v||!d||"cavi"===i&&!p.trim(),children:[v?(0,t.jsx)(eY.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(ax,{className:"h-4 w-4 mr-2"}),"Importa ",z()]})]})]})})}let ah={id_cavo:"",utility:"",sistema:"",colore_cavo:"",tipologia:"",sezione:"",ubicazione_partenza:"",utenza_partenza:"",descrizione_utenza_partenza:"",ubicazione_arrivo:"",utenza_arrivo:"",descrizione_utenza_arrivo:"",metri_teorici:""};function av({open:e,onClose:a,cantiere:i,onSuccess:r,onError:l}){let{cantiere:c}=(0,o.A)(),d=i||c,[u,p]=(0,s.useState)(ah),[h,v]=(0,s.useState)(!1),[b,j]=(0,s.useState)({}),f=(e,a)=>{p(i=>({...i,[e]:"id_cavo"===e?a.toUpperCase():a})),b[e]&&j(a=>{let i={...a};return delete i[e],i})},N=()=>{let e={};if(u.id_cavo.trim()||(e.id_cavo="ID Cavo \xe8 obbligatorio"),u.utility.trim()||(e.utility="Utility \xe8 obbligatoria"),u.metri_teorici.trim()){let a=parseFloat(u.metri_teorici);(isNaN(a)||a<=0)&&(e.metri_teorici="Metri Teorici deve essere un numero positivo")}else e.metri_teorici="Metri Teorici sono obbligatori";return j(e),0===Object.keys(e).length},y=async()=>{if(N()){if(!d?.id_cantiere)return void l("Cantiere non selezionato");try{v(!0);let e={...u,metri_teorici:parseFloat(u.metri_teorici),id_cantiere:d.id_cantiere};await m.At.createCavo(parseInt(d.id_cantiere),e),r(`Cavo ${u.id_cavo} aggiunto con successo`),C()}catch(e){l(e.response?.data?.detail||e.message||"Errore durante l'aggiunta del cavo")}finally{v(!1)}}},C=()=>{h||(p(ah),j({}),a())};return(0,t.jsx)(eH.lG,{open:e,onOpenChange:C,children:(0,t.jsxs)(eH.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(eH.c7,{children:[(0,t.jsxs)(eH.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(ez.A,{className:"h-5 w-5"}),"Aggiungi Nuovo Cavo"]}),(0,t.jsxs)(eH.rr,{children:["Inserisci i dati del nuovo cavo per il cantiere ",d?.nome_cantiere]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Informazioni Generali"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"id_cavo",children:"ID Cavo *"}),(0,t.jsx)(g.p,{id:"id_cavo",value:u.id_cavo,onChange:e=>f("id_cavo",e.target.value),placeholder:"Es. C001",className:b.id_cavo?"border-red-500":""}),b.id_cavo&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:b.id_cavo})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"utility",children:"Utility *"}),(0,t.jsx)(g.p,{id:"utility",value:u.utility,onChange:e=>f("utility",e.target.value),placeholder:"Es. ENEL",className:b.utility?"border-red-500":""}),b.utility&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:b.utility})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"sistema",children:"Sistema"}),(0,t.jsx)(g.p,{id:"sistema",value:u.sistema,onChange:e=>f("sistema",e.target.value),placeholder:"Es. MT"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Caratteristiche Tecniche"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"colore_cavo",children:"Colore Cavo"}),(0,t.jsx)(g.p,{id:"colore_cavo",value:u.colore_cavo,onChange:e=>f("colore_cavo",e.target.value),placeholder:"Es. Nero"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"tipologia",children:"Tipologia"}),(0,t.jsx)(g.p,{id:"tipologia",value:u.tipologia,onChange:e=>f("tipologia",e.target.value),placeholder:"Es. ARE4H5E"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"sezione",children:"Formazione"}),(0,t.jsx)(g.p,{id:"sezione",value:u.sezione,onChange:e=>f("sezione",e.target.value),placeholder:"Es. 3X240+120"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Partenza"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"ubicazione_partenza",children:"Ubicazione Partenza"}),(0,t.jsx)(g.p,{id:"ubicazione_partenza",value:u.ubicazione_partenza,onChange:e=>f("ubicazione_partenza",e.target.value)})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"utenza_partenza",children:"Utenza Partenza"}),(0,t.jsx)(g.p,{id:"utenza_partenza",value:u.utenza_partenza,onChange:e=>f("utenza_partenza",e.target.value)})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"descrizione_utenza_partenza",children:"Descrizione Utenza Partenza"}),(0,t.jsx)(g.p,{id:"descrizione_utenza_partenza",value:u.descrizione_utenza_partenza,onChange:e=>f("descrizione_utenza_partenza",e.target.value)})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Arrivo"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"ubicazione_arrivo",children:"Ubicazione Arrivo"}),(0,t.jsx)(g.p,{id:"ubicazione_arrivo",value:u.ubicazione_arrivo,onChange:e=>f("ubicazione_arrivo",e.target.value)})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"utenza_arrivo",children:"Utenza Arrivo"}),(0,t.jsx)(g.p,{id:"utenza_arrivo",value:u.utenza_arrivo,onChange:e=>f("utenza_arrivo",e.target.value)})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"descrizione_utenza_arrivo",children:"Descrizione Utenza Arrivo"}),(0,t.jsx)(g.p,{id:"descrizione_utenza_arrivo",value:u.descrizione_utenza_arrivo,onChange:e=>f("descrizione_utenza_arrivo",e.target.value)})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Metratura"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"metri_teorici",children:"Metri Teorici *"}),(0,t.jsx)(g.p,{id:"metri_teorici",type:"number",step:"0.01",min:"0",value:u.metri_teorici,onChange:e=>f("metri_teorici",e.target.value),placeholder:"Es. 100.50",className:b.metri_teorici?"border-red-500":""}),b.metri_teorici&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:b.metri_teorici})]})})]}),Object.keys(b).length>0&&(0,t.jsxs)(n.Fc,{variant:"destructive",children:[(0,t.jsx)(eP.A,{className:"h-4 w-4"}),(0,t.jsx)(n.TN,{children:"Correggere i campi evidenziati in rosso prima di salvare."})]})]}),(0,t.jsxs)(eH.Es,{children:[(0,t.jsx)(x.$,{variant:"outline",onClick:C,disabled:h,children:"Annulla"}),(0,t.jsxs)(x.$,{onClick:y,disabled:h,children:[h&&(0,t.jsx)(eY.A,{className:"mr-2 h-4 w-4 animate-spin"}),h?"Aggiungendo...":"Aggiungi Cavo"]})]})]})})}function ag({open:e,onClose:a,cavo:i,cantiere:r,onSuccess:l,onError:c}){let{cantiere:d}=(0,o.A)(),u=r||d,[p,h]=(0,s.useState)({id_cavo:"",utility:"",sistema:"",colore_cavo:"",tipologia:"",sezione:"",ubicazione_partenza:"",utenza_partenza:"",descrizione_utenza_partenza:"",ubicazione_arrivo:"",utenza_arrivo:"",descrizione_utenza_arrivo:"",metri_teorici:""}),[v,b]=(0,s.useState)(!1),[j,f]=(0,s.useState)({}),N=(e,a)=>{h(i=>({...i,[e]:"id_cavo"===e?a.toUpperCase():a})),j[e]&&f(a=>{let i={...a};return delete i[e],i})},y=()=>{let e={};if(p.id_cavo.trim()||(e.id_cavo="ID Cavo \xe8 obbligatorio"),p.utility.trim()||(e.utility="Utility \xe8 obbligatoria"),p.metri_teorici.trim()){let a=parseFloat(p.metri_teorici);(isNaN(a)||a<=0)&&(e.metri_teorici="Metri Teorici deve essere un numero positivo")}else e.metri_teorici="Metri Teorici sono obbligatori";return f(e),0===Object.keys(e).length},C=async()=>{if(y()){if(!i?.id_cavo)return void c("Cavo non selezionato");if(!u?.id_cantiere)return void c("Cantiere non selezionato");try{b(!0);let e={...p,metri_teorici:parseFloat(p.metri_teorici),id_cantiere:u.id_cantiere};await m.At.updateCavo(parseInt(u.id_cantiere),i.id_cavo,e),l(`Cavo ${p.id_cavo} modificato con successo`),_()}catch(e){c(e.response?.data?.detail||e.message||"Errore durante la modifica del cavo")}finally{b(!1)}}},_=()=>{v||(f({}),a())},w=i&&(i.metratura_reale>0||"da installare"!==i.stato_installazione);return i?(0,t.jsx)(eH.lG,{open:e,onOpenChange:_,children:(0,t.jsxs)(eH.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(eH.c7,{children:[(0,t.jsxs)(eH.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(e_.A,{className:"h-5 w-5"}),"Modifica Cavo: ",i.id_cavo]}),(0,t.jsxs)(eH.rr,{children:["Modifica i dati del cavo nel cantiere ",u?.nome_cantiere]})]}),w&&(0,t.jsxs)(n.Fc,{variant:"destructive",children:[(0,t.jsx)(eV.A,{className:"h-4 w-4"}),(0,t.jsxs)(n.TN,{children:[(0,t.jsx)("strong",{children:"Attenzione:"})," Questo cavo \xe8 gi\xe0 stato installato. La modifica potrebbe influire sui dati di installazione esistenti."]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Informazioni Generali"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"id_cavo",children:"ID Cavo *"}),(0,t.jsx)(g.p,{id:"id_cavo",value:p.id_cavo,onChange:e=>N("id_cavo",e.target.value),placeholder:"Es. C001",className:j.id_cavo?"border-red-500":""}),j.id_cavo&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:j.id_cavo})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"utility",children:"Utility *"}),(0,t.jsx)(g.p,{id:"utility",value:p.utility,onChange:e=>N("utility",e.target.value),placeholder:"Es. ENEL",className:j.utility?"border-red-500":""}),j.utility&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:j.utility})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"sistema",children:"Sistema"}),(0,t.jsx)(g.p,{id:"sistema",value:p.sistema,onChange:e=>N("sistema",e.target.value),placeholder:"Es. MT"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Caratteristiche Tecniche"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"colore_cavo",children:"Colore Cavo"}),(0,t.jsx)(g.p,{id:"colore_cavo",value:p.colore_cavo,onChange:e=>N("colore_cavo",e.target.value),placeholder:"Es. Nero"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"tipologia",children:"Tipologia"}),(0,t.jsx)(g.p,{id:"tipologia",value:p.tipologia,onChange:e=>N("tipologia",e.target.value),placeholder:"Es. ARE4H5E"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"sezione",children:"Formazione"}),(0,t.jsx)(g.p,{id:"sezione",value:p.sezione,onChange:e=>N("sezione",e.target.value),placeholder:"Es. 3X240+120"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Partenza"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"ubicazione_partenza",children:"Ubicazione Partenza"}),(0,t.jsx)(g.p,{id:"ubicazione_partenza",value:p.ubicazione_partenza,onChange:e=>N("ubicazione_partenza",e.target.value)})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"utenza_partenza",children:"Utenza Partenza"}),(0,t.jsx)(g.p,{id:"utenza_partenza",value:p.utenza_partenza,onChange:e=>N("utenza_partenza",e.target.value)})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"descrizione_utenza_partenza",children:"Descrizione Utenza Partenza"}),(0,t.jsx)(g.p,{id:"descrizione_utenza_partenza",value:p.descrizione_utenza_partenza,onChange:e=>N("descrizione_utenza_partenza",e.target.value)})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Arrivo"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"ubicazione_arrivo",children:"Ubicazione Arrivo"}),(0,t.jsx)(g.p,{id:"ubicazione_arrivo",value:p.ubicazione_arrivo,onChange:e=>N("ubicazione_arrivo",e.target.value)})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"utenza_arrivo",children:"Utenza Arrivo"}),(0,t.jsx)(g.p,{id:"utenza_arrivo",value:p.utenza_arrivo,onChange:e=>N("utenza_arrivo",e.target.value)})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"descrizione_utenza_arrivo",children:"Descrizione Utenza Arrivo"}),(0,t.jsx)(g.p,{id:"descrizione_utenza_arrivo",value:p.descrizione_utenza_arrivo,onChange:e=>N("descrizione_utenza_arrivo",e.target.value)})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Metratura"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eK.J,{htmlFor:"metri_teorici",children:"Metri Teorici *"}),(0,t.jsx)(g.p,{id:"metri_teorici",type:"number",step:"0.01",min:"0",value:p.metri_teorici,onChange:e=>N("metri_teorici",e.target.value),placeholder:"Es. 100.50",className:j.metri_teorici?"border-red-500":""}),j.metri_teorici&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:j.metri_teorici})]})})]}),Object.keys(j).length>0&&(0,t.jsxs)(n.Fc,{variant:"destructive",children:[(0,t.jsx)(eP.A,{className:"h-4 w-4"}),(0,t.jsx)(n.TN,{children:"Correggere i campi evidenziati in rosso prima di salvare."})]})]}),(0,t.jsxs)(eH.Es,{children:[(0,t.jsx)(x.$,{variant:"outline",onClick:_,disabled:v,children:"Annulla"}),(0,t.jsxs)(x.$,{onClick:C,disabled:v,children:[v&&(0,t.jsx)(eY.A,{className:"mr-2 h-4 w-4 animate-spin"}),v?"Salvando...":"Salva Modifiche"]})]})]})}):null}var ab=i(72942),aj=i(43),af=i(18853),aN=i(83721),ay="Radio",[aC,a_]=(0,N.A)(ay),[aw,az]=aC(ay),aA=s.forwardRef((e,a)=>{let{__scopeRadio:i,name:r,checked:l=!1,required:n,disabled:o,value:c="on",onCheck:d,form:m,...x}=e,[u,p]=s.useState(null),h=(0,f.s)(a,e=>p(e)),v=s.useRef(!1),g=!u||m||!!u.closest("form");return(0,t.jsxs)(aw,{scope:i,checked:l,disabled:o,children:[(0,t.jsx)(k.sG.button,{type:"button",role:"radio","aria-checked":l,"data-state":aT(l),"data-disabled":o?"":void 0,disabled:o,value:c,...x,ref:h,onClick:(0,j.m)(e.onClick,e=>{l||d?.(),g&&(v.current=e.isPropagationStopped(),v.current||e.stopPropagation())})}),g&&(0,t.jsx)(aE,{control:u,bubbles:!v.current,name:r,value:c,checked:l,required:n,disabled:o,form:m,style:{transform:"translateX(-100%)"}})]})});aA.displayName=ay;var aS="RadioIndicator",ak=s.forwardRef((e,a)=>{let{__scopeRadio:i,forceMount:s,...r}=e,l=az(aS,i);return(0,t.jsx)(S.C,{present:s||l.checked,children:(0,t.jsx)(k.sG.span,{"data-state":aT(l.checked),"data-disabled":l.disabled?"":void 0,...r,ref:a})})});ak.displayName=aS;var aE=s.forwardRef(({__scopeRadio:e,control:a,checked:i,bubbles:r=!0,...l},n)=>{let o=s.useRef(null),c=(0,f.s)(o,n),d=(0,aN.Z)(i),m=(0,af.X)(a);return s.useEffect(()=>{let e=o.current;if(!e)return;let a=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(d!==i&&a){let t=new Event("click",{bubbles:r});a.call(e,i),e.dispatchEvent(t)}},[d,i,r]),(0,t.jsx)(k.sG.input,{type:"radio","aria-hidden":!0,defaultChecked:i,...l,tabIndex:-1,ref:c,style:{...l.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function aT(e){return e?"checked":"unchecked"}aE.displayName="RadioBubbleInput";var aI=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],aR="RadioGroup",[aO,a$]=(0,N.A)(aR,[ab.RG,a_]),aF=(0,ab.RG)(),aM=a_(),[aL,aP]=aO(aR),aD=s.forwardRef((e,a)=>{let{__scopeRadioGroup:i,name:s,defaultValue:r,value:l,required:n=!1,disabled:o=!1,orientation:c,dir:d,loop:m=!0,onValueChange:x,...u}=e,p=aF(i),h=(0,aj.jH)(d),[v,g]=(0,T.i)({prop:l,defaultProp:r??null,onChange:x,caller:aR});return(0,t.jsx)(aL,{scope:i,name:s,required:n,disabled:o,value:v,onValueChange:g,children:(0,t.jsx)(ab.bL,{asChild:!0,...p,orientation:c,dir:h,loop:m,children:(0,t.jsx)(k.sG.div,{role:"radiogroup","aria-required":n,"aria-orientation":c,"data-disabled":o?"":void 0,dir:h,...u,ref:a})})})});aD.displayName=aR;var aU="RadioGroupItem",aB=s.forwardRef((e,a)=>{let{__scopeRadioGroup:i,disabled:r,...l}=e,n=aP(aU,i),o=n.disabled||r,c=aF(i),d=aM(i),m=s.useRef(null),x=(0,f.s)(a,m),u=n.value===l.value,p=s.useRef(!1);return s.useEffect(()=>{let e=e=>{aI.includes(e.key)&&(p.current=!0)},a=()=>p.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",a),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",a)}},[]),(0,t.jsx)(ab.q7,{asChild:!0,...c,focusable:!o,active:u,children:(0,t.jsx)(aA,{disabled:o,required:n.required,checked:u,...d,...l,name:n.name,ref:x,onCheck:()=>n.onValueChange(l.value),onKeyDown:(0,j.m)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,j.m)(l.onFocus,()=>{p.current&&m.current?.click()})})})});aB.displayName=aU;var aG=s.forwardRef((e,a)=>{let{__scopeRadioGroup:i,...s}=e,r=aM(i);return(0,t.jsx)(ak,{...r,...s,ref:a})});aG.displayName="RadioGroupIndicator";let aV=(0,es.A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]),aq=s.forwardRef(({className:e,...a},i)=>(0,t.jsx)(aD,{className:(0,ei.cn)("grid gap-2",e),...a,ref:i}));aq.displayName=aD.displayName;let aJ=s.forwardRef(({className:e,...a},i)=>(0,t.jsx)(aB,{ref:i,className:(0,ei.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...a,children:(0,t.jsx)(aG,{className:"flex items-center justify-center",children:(0,t.jsx)(aV,{className:"h-2.5 w-2.5 fill-current text-current"})})}));function aW({open:e,onClose:a,cavo:i,cantiere:r,onSuccess:l,onError:c}){let{cantiere:d}=(0,o.A)(),u=r||d,[p,h]=(0,s.useState)(!1),[v,g]=(0,s.useState)("spare"),b=async()=>{if(!i?.id_cavo)return void c("Cavo non selezionato");if(!u?.id_cantiere)return void c("Cantiere non selezionato");try{if(h(!0),console.log(`Iniziando operazione ${v} per cavo ${i.id_cavo}`),"spare"===v){console.log("Chiamando markAsSpare API...");let e=await m.At.markAsSpare(parseInt(u.id_cantiere),i.id_cavo,!0);console.log("Risultato markAsSpare:",e),l(`Cavo ${i.id_cavo} marcato come SPARE`)}else{console.log("Chiamando deleteCavo API...");let e=await m.At.deleteCavo(parseInt(u.id_cantiere),i.id_cavo);console.log("Risultato deleteCavo:",e),l(`Cavo ${i.id_cavo} eliminato definitivamente`)}j()}catch(a){console.error("Errore durante operazione:",a);let e=a.response?.data?.detail||a.message||"Errore durante l'eliminazione del cavo";console.error("Messaggio errore:",e),c(e)}finally{h(!1)}},j=()=>{p||(g("spare"),a())};if(!i)return null;let f=i.metratura_reale>0||i.stato_installazione&&"da installare"!==i.stato_installazione.toLowerCase(),N=!f,y={id:i.id_cavo,tipologia:i.tipologia||"N/A",sezione:i.sezione||"N/A",metri_teorici:i.metri_teorici||0,metri_reali:i.metratura_reale||0,stato:i.stato_installazione||"N/A",bobina:i.id_bobina||"N/A"};return(0,t.jsx)(eH.lG,{open:e,onOpenChange:j,children:(0,t.jsxs)(eH.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(eH.c7,{children:[(0,t.jsxs)(eH.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(ew.A,{className:"h-5 w-5"}),"Elimina Cavo: ",i.id_cavo]}),(0,t.jsxs)(eH.rr,{children:["Scegli come gestire l'eliminazione del cavo dal cantiere ",u?.nome_cantiere]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Informazioni Cavo"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"ID:"}),(0,t.jsx)("span",{className:"ml-2 font-medium",children:y.id})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Tipologia:"}),(0,t.jsx)("span",{className:"ml-2 font-medium",children:y.tipologia})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Formazione:"}),(0,t.jsx)("span",{className:"ml-2 font-medium",children:y.sezione})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Stato:"}),(0,t.jsx)("span",{className:"ml-2 font-medium",children:y.stato})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Metri Teorici:"}),(0,t.jsx)("span",{className:"ml-2 font-medium",children:y.metri_teorici})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Metri Installati:"}),(0,t.jsx)("span",{className:"ml-2 font-medium",children:y.metri_reali})]}),(0,t.jsxs)("div",{className:"col-span-2",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Bobina:"}),(0,t.jsx)("span",{className:"ml-2 font-medium",children:y.bobina})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Modalit\xe0 di Eliminazione"}),(0,t.jsxs)(aq,{value:v,onValueChange:e=>g(e),children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(aJ,{value:"spare",id:"spare"}),(0,t.jsxs)(eK.J,{htmlFor:"spare",className:"flex-1",children:[(0,t.jsx)("div",{className:"font-medium",children:"Marca come SPARE"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Il cavo viene marcato come spare/consumato ma rimane nel database per tracciabilit\xe0"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(aJ,{value:"permanent",id:"permanent",disabled:!N}),(0,t.jsxs)(eK.J,{htmlFor:"permanent",className:`flex-1 ${!N?"opacity-50":""}`,children:[(0,t.jsx)("div",{className:"font-medium",children:"Eliminazione Definitiva"}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:["Il cavo viene rimosso completamente dal database",!N&&" (non disponibile per cavi installati)"]})]})]})]})]}),f&&(0,t.jsxs)(n.Fc,{children:[(0,t.jsx)(eD.A,{className:"h-4 w-4"}),(0,t.jsxs)(n.TN,{children:[(0,t.jsx)("strong",{children:"Cavo Installato:"})," Questo cavo ha metri installati. Si consiglia di marcarlo come SPARE piuttosto che eliminarlo definitivamente."]})]}),"permanent"===v&&(0,t.jsxs)(n.Fc,{variant:"destructive",children:[(0,t.jsx)(eV.A,{className:"h-4 w-4"}),(0,t.jsxs)(n.TN,{children:[(0,t.jsx)("strong",{children:"Attenzione:"})," L'eliminazione definitiva \xe8 irreversibile. Tutti i dati del cavo verranno persi permanentemente."]})]}),"spare"===v&&(0,t.jsxs)(n.Fc,{children:[(0,t.jsx)(eD.A,{className:"h-4 w-4"}),(0,t.jsx)(n.TN,{children:"Il cavo verr\xe0 marcato come SPARE e non apparir\xe0 pi\xf9 nelle liste attive, ma rimarr\xe0 nel database per eventuali controlli futuri."})]})]}),(0,t.jsxs)(eH.Es,{children:[(0,t.jsx)(x.$,{variant:"outline",onClick:j,disabled:p,children:"Annulla"}),(0,t.jsxs)(x.$,{variant:"destructive",onClick:b,disabled:p,children:[p&&(0,t.jsx)(eY.A,{className:"mr-2 h-4 w-4 animate-spin"}),p?"Eliminando...":"spare"===v?"Marca come SPARE":"Elimina Definitivamente"]})]})]})})}aJ.displayName=aB.displayName;var aZ=i(61611);function aH({open:e,onClose:a,onSuccess:i,onError:r}){let{cantiere:l}=(0,o.A)(),[c,d]=(0,s.useState)({cavi:!0,bobine:!0,comande:!1,certificazioni:!1,responsabili:!1}),[u,h]=(0,s.useState)(!1),[v,g]=(0,s.useState)(""),b=(e,a)=>{d(i=>({...i,[e]:a}))},j=async()=>{if(l)try{h(!0);let e=await m.mg.exportCavi(l.id_cantiere),a=window.URL.createObjectURL(new Blob([e.data])),t=document.createElement("a");t.href=a,t.setAttribute("download",`cavi_${l.nome_cantiere}_${new Date().toISOString().split("T")[0]}.xlsx`),document.body.appendChild(t),t.click(),t.remove(),window.URL.revokeObjectURL(a),i("Export cavi completato con successo")}catch(e){r(e.response?.data?.detail||e.message||"Errore durante l'export dei cavi")}finally{h(!1)}},f=async()=>{if(l)try{h(!0);let e=await m.mg.exportBobine(l.id_cantiere),a=window.URL.createObjectURL(new Blob([e.data])),t=document.createElement("a");t.href=a,t.setAttribute("download",`bobine_${l.nome_cantiere}_${new Date().toISOString().split("T")[0]}.xlsx`),document.body.appendChild(t),t.click(),t.remove(),window.URL.revokeObjectURL(a),i("Export bobine completato con successo")}catch(e){r(e.response?.data?.detail||e.message||"Errore durante l'export delle bobine")}finally{h(!1)}},N=async()=>{if(l)try{h(!0),g("");let e=[];c.cavi&&e.push(j()),c.bobine&&e.push(f()),c.comande,c.certificazioni,c.responsabili,await Promise.all(e);let t=Object.values(c).filter(Boolean).length;i(`Export completato: ${t} file scaricati`),a()}catch(e){r(e.response?.data?.detail||e.message||"Errore durante l'export dei dati")}finally{h(!1)}},y=[{key:"cavi",label:"Cavi",description:"Esporta tutti i cavi del cantiere con stato, collegamenti e certificazioni",icon:(0,t.jsx)(aZ.A,{className:"h-4 w-4"}),available:!0},{key:"bobine",label:"Bobine",description:"Esporta tutte le bobine del parco cavi con metri residui e assegnazioni",icon:(0,t.jsx)(au,{className:"h-4 w-4"}),available:!0},{key:"comande",label:"Comande",description:"Esporta tutte le comande con cavi assegnati e responsabili",icon:(0,t.jsx)(au,{className:"h-4 w-4"}),available:!1},{key:"certificazioni",label:"Certificazioni",description:"Esporta tutte le certificazioni con esiti e responsabili",icon:(0,t.jsx)(au,{className:"h-4 w-4"}),available:!1},{key:"responsabili",label:"Responsabili",description:"Esporta tutti i responsabili con contatti e ruoli",icon:(0,t.jsx)(au,{className:"h-4 w-4"}),available:!1}];return(0,t.jsx)(eH.lG,{open:e,onOpenChange:a,children:(0,t.jsxs)(eH.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(eH.c7,{children:[(0,t.jsxs)(eH.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(eO.A,{className:"h-5 w-5"}),"Esporta Dati Cantiere"]}),(0,t.jsxs)(eH.rr,{children:["Seleziona i dati da esportare dal cantiere ",l?.nome_cantiere]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[v&&(0,t.jsxs)(n.Fc,{variant:"destructive",children:[(0,t.jsx)(eP.A,{className:"h-4 w-4"}),(0,t.jsx)(n.TN,{children:v})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(eK.J,{className:"text-sm font-medium",children:"Seleziona Dati da Esportare"}),y.map(e=>(0,t.jsxs)("div",{className:`flex items-start space-x-3 p-3 rounded-lg border ${e.available?"bg-white":"bg-gray-50"}`,children:[(0,t.jsx)(p.S,{id:e.key,checked:c[e.key],onCheckedChange:a=>b(e.key,a),disabled:!e.available||u}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,(0,t.jsxs)(eK.J,{htmlFor:e.key,className:`font-medium ${!e.available?"text-gray-500":""}`,children:[e.label,!e.available&&(0,t.jsx)("span",{className:"ml-2 text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded",children:"In sviluppo"})]})]}),(0,t.jsx)("p",{className:`text-sm mt-1 ${!e.available?"text-gray-400":"text-gray-600"}`,children:e.description})]})]},e.key))]}),(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)(eK.J,{className:"text-sm font-medium",children:"Informazioni Export"}),(0,t.jsxs)("ul",{className:"mt-2 space-y-1 text-sm text-gray-600",children:[(0,t.jsx)("li",{children:"• I file saranno scaricati in formato Excel (.xlsx)"}),(0,t.jsx)("li",{children:"• I nomi file includeranno data e nome cantiere"}),(0,t.jsx)("li",{children:"• I dati esportati riflettono lo stato attuale del database"}),(0,t.jsx)("li",{children:"• L'export non modifica i dati originali"})]})]}),Object.values(c).filter(Boolean).length>0&&(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsx)(eK.J,{className:"text-sm font-medium",children:"Riepilogo Export"}),(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Cantiere:"})," ",l?.nome_cantiere]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"File da scaricare:"})," ",Object.values(c).filter(Boolean).length]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Data export:"})," ",new Date().toLocaleDateString("it-IT")]})]})]})]}),(0,t.jsxs)(eH.Es,{children:[(0,t.jsx)(x.$,{variant:"outline",onClick:a,disabled:u,children:"Annulla"}),(0,t.jsxs)(x.$,{onClick:N,disabled:u||0===Object.values(c).filter(Boolean).length,children:[u?(0,t.jsx)(eY.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(eO.A,{className:"h-4 w-4 mr-2"}),"Esporta ",Object.values(c).filter(Boolean).length>0?`(${Object.values(c).filter(Boolean).length})`:""]})]})]})})}function aK(){let{user:e,isAuthenticated:a,isLoading:i}=(0,o.A)(),{cantiereId:x,cantiere:u,isValidCantiere:p,isLoading:h,error:v}=(0,c.jV)();(0,r.useRouter)();let g=({title:e,description:a,variant:i})=>{},[b,j]=(0,s.useState)([]),[f,N]=(0,s.useState)([]),[y,C]=(0,s.useState)(!0),[_,w]=(0,s.useState)(""),[z,A]=(0,s.useState)([]),[S,k]=(0,s.useState)(!1),[E,T]=(0,s.useState)([]),[I,R]=(0,s.useState)(""),[O,$]=(0,s.useState)({open:!1,cavo:null}),[F,M]=(0,s.useState)({open:!1,cavo:null}),[L,P]=(0,s.useState)(!1),[D,U]=(0,s.useState)({open:!1,cavo:null}),[B,G]=(0,s.useState)({open:!1,cavo:null}),[V,q]=(0,s.useState)({open:!1,cavo:null}),[J,W]=(0,s.useState)({open:!1,mode:null,cavo:null}),[Z,H]=(0,s.useState)({open:!1,cavo:null}),[K,X]=(0,s.useState)({open:!1,cavo:null}),[Y,Q]=(0,s.useState)({open:!1,cavo:null}),[ee,ea]=(0,s.useState)({open:!1,cavo:null}),[ei,et]=(0,s.useState)({open:!1,cavi:[]}),[es,er]=(0,s.useState)({open:!1,cavo:null,error:""}),[el,en]=(0,s.useState)({visible:!1,message:""}),[eo,ec]=(0,s.useState)({open:!1}),[ed,em]=(0,s.useState)({open:!1}),[ex,eu]=(0,s.useState)(!1),[ep,eh]=(0,s.useState)({totali:0,installati:0,collegati:0,certificati:0,percentualeInstallazione:0,percentualeCollegamento:0,percentualeCertificazione:0,metriTotali:0,metriInstallati:0,metriCollegati:0,metriCertificati:0}),ev=u||(x&&x>0?{id_cantiere:x,commessa:`Cantiere ${x}`}:null),eg=async()=>{try{C(!0),w("");try{let e=await m.At.getCavi(x),a=e.filter(e=>3!==e.modificato_manualmente),i=e.filter(e=>3===e.modificato_manualmente);j(a),N(i),eb(a)}catch(e){throw e}}catch(e){w(`Errore nel caricamento dei cavi: ${e.response?.data?.detail||e.message}`)}finally{C(!1)}},eb=e=>{let a=e.length,i=e.filter(e=>(e.metri_posati||e.metratura_reale||0)>0).length,t=e.filter(e=>3===(e.collegamento||e.collegamenti)).length,s=e.filter(e=>e.certificato).length,r=e.reduce((e,a)=>e+(a.metri_teorici||0),0),l=e.reduce((e,a)=>e+(a.metri_posati||0),0);eh({totali:a,installati:i,collegati:t,certificati:s,percentualeInstallazione:a>0?Math.round(i/a*100):0,percentualeCollegamento:a>0?Math.round(t/a*100):0,percentualeCertificazione:a>0?Math.round(s/a*100):0,metriTotali:r,metriInstallati:l,metriCollegati:e.filter(e=>3===e.collegamento).reduce((e,a)=>e+(a.metri_posati||0),0),metriCertificati:e.filter(e=>e.certificato).reduce((e,a)=>e+(a.metri_posati||0),0)})},ej=(e,a,i)=>{switch(a){case"insert_meters":W({open:!0,mode:"aggiungi_metri",cavo:e});break;case"modify_reel":W({open:!0,mode:"modifica_bobina",cavo:e});break;case"view_command":g({title:"Visualizza Comanda",description:`Apertura comanda ${label} per cavo ${e.id_cavo}`});break;case"connect_cable":case"connect_arrival":case"connect_departure":case"manage_connections":case"disconnect_cable":H({open:!0,cavo:e});break;case"create_certificate":console.log("\uD83D\uDE80 Opening certification modal for cavo:",e.id_cavo),ea({open:!0,cavo:e});break;case"bulk_certify":i&&i.length>0&&et({open:!0,cavi:i});break;case"generate_pdf":Q({open:!0,cavo:e})}},ef=(e,a)=>{switch(a){case"view_details":M({open:!0,cavo:e});break;case"edit":U({open:!0,cavo:e});break;case"delete":G({open:!0,cavo:e});break;case"add_new":P(!0);break;case"select":z.includes(e.id_cavo)?(A(z.filter(a=>a!==e.id_cavo)),g({title:"Cavo Deselezionato",description:`Cavo ${e.id_cavo} deselezionato`})):(A([...z,e.id_cavo]),g({title:"Cavo Selezionato",description:`Cavo ${e.id_cavo} selezionato`}));break;case"copy_id":navigator.clipboard.writeText(e.id_cavo),g({title:"ID Copiato",description:`ID cavo ${e.id_cavo} copiato negli appunti`});break;case"copy_details":let i=`ID: ${e.id_cavo}, Tipologia: ${e.tipologia}, Formazione: ${e.formazione||e.sezione}, Metri: ${e.metri_teorici}`;navigator.clipboard.writeText(i),g({title:"Dettagli Copiati",description:"Dettagli cavo copiati negli appunti"});break;case"add_to_command":g({title:"Aggiungi a Comanda",description:"Funzione aggiunta a comanda in sviluppo"});break;case"remove_from_command":g({title:"Rimuovi da Comanda",description:"Funzione rimozione da comanda in sviluppo"});break;case"create_command_posa":ec({open:!0,tipoComanda:"POSA"});break;case"create_command_collegamento_partenza":ec({open:!0,tipoComanda:"COLLEGAMENTO_PARTENZA"});break;case"create_command_collegamento_arrivo":ec({open:!0,tipoComanda:"COLLEGAMENTO_ARRIVO"});break;case"create_command_certificazione":ec({open:!0,tipoComanda:"CERTIFICAZIONE"});break;case"add_multiple_to_command":g({title:"Aggiungi Tutti a Comanda",description:"Funzione aggiunta multipla a comanda in sviluppo"});break;case"remove_multiple_from_commands":g({title:"Rimuovi Tutti dalle Comande",description:"Funzione rimozione multipla dalle comande in sviluppo"});break;default:g({title:"Azione non implementata",description:`Azione ${a} non ancora implementata`})}},eN=e=>{g({title:"Operazione completata",description:e}),eg()},ey=e=>{g({title:"Errore",description:e,variant:"destructive"})},eC=async e=>{try{if(console.log("\uD83D\uDD0D DEBUG handleUnifiedModalSave:",{cantiere:u,cantiereId:x,cantiereForDialog:ev,isValidCantiere:p,data:e}),!x||!p)throw Error("Cantiere non selezionato o non valido");let a="";"aggiungi_metri"===e.mode?(console.log("\uD83D\uDE80 UnifiedModal: Inserimento metri:",e),await m.At.updateMetriPosati(x,e.cableId,e.metersToInstall,"BOBINA_VUOTA"===e.bobbinId?"BOBINA_VUOTA":e.bobbinId,!0),a=`Metri posati aggiornati con successo per il cavo ${e.cableId}`):"modifica_bobina"===e.mode&&(console.log("\uD83D\uDE80 UnifiedModal: Modifica bobina:",e),"cambia_bobina"===e.editOption?(await m.At.updateMetriPosati(x,e.cableId,e.newLaidMeters,e.newBobbinId,!0),a=`Bobina aggiornata con successo per il cavo ${e.cableId}`):"bobina_vuota"===e.editOption?(await m.At.updateMetriPosati(x,e.cableId,e.newLaidMeters,"BOBINA_VUOTA",!1),a=`Bobina rimossa con successo per il cavo ${e.cableId}`):"annulla_posa"===e.editOption&&(await m.At.updateMetriPosati(x,e.cableId,0,"BOBINA_VUOTA",!1),a=`Posa annullata con successo per il cavo ${e.cableId}`)),await eg(),g({title:"Operazione completata",description:a,variant:"default"})}catch(e){throw console.error("Errore unified modal save:",e),g({title:"Errore",description:e.message||"Errore durante l'operazione",variant:"destructive"}),e}},e_=()=>{en({visible:!0,message:"Certificazione completata con successo"}),ea({open:!1,cavo:null}),eg()},ew=async(e,a)=>{try{if(!u?.id_cantiere)throw Error("Cantiere non selezionato");console.log("Certificando cavo:",e,a),await new Promise(e=>setTimeout(e,1e3)),e_()}catch(e){console.error("Errore certificazione:",e),er({open:!0,cavo:ee.cavo,error:e.message||"Errore durante la certificazione"}),ea({open:!1,cavo:null})}},ez=async(e,a)=>{try{if(!u?.id_cantiere)throw Error("Cantiere non selezionato");console.log("Certificando cavi multipli:",e,a),await new Promise(e=>setTimeout(e,2e3)),en({visible:!0,message:`${e.length} cavi certificati con successo`}),et({open:!1,cavi:[]}),eg()}catch(e){console.error("Errore certificazione multipla:",e),er({open:!0,cavo:null,error:e.message||"Errore durante la certificazione multipla"}),et({open:!1,cavi:[]})}};return y&&p?(0,t.jsxs)("div",{className:"flex items-center justify-center min-h-screen",children:[(0,t.jsx)(eY.A,{className:"h-8 w-8 animate-spin"}),(0,t.jsx)("span",{className:"ml-2",children:"Caricamento cavi..."})]}):(0,t.jsx)(d.u,{children:(0,t.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[_&&(0,t.jsxs)(n.Fc,{variant:"destructive",className:"mb-6",children:[(0,t.jsx)(eP.A,{className:"h-4 w-4"}),(0,t.jsx)(n.TN,{children:_})]}),(0,t.jsx)(eZ,{cavi:b,filteredCavi:E,revisioneCorrente:I,className:"mb-2"}),(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsx)(eU,{cavi:b,loading:y,selectionEnabled:S,selectedCavi:z,onSelectionChange:A,onStatusAction:ej,onContextMenuAction:ef})}),f.length>0&&(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsxs)(l.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(eS.A,{className:"h-5 w-5"}),(0,t.jsxs)("span",{children:["Cavi Spare (",f.length,")"]})]})}),(0,t.jsx)(l.Wu,{children:(0,t.jsx)(eU,{cavi:f,loading:y,selectionEnabled:!1,onStatusAction:ej,onContextMenuAction:ef})})]})}),(0,t.jsx)(as.B,{mode:J.mode||"aggiungi_metri",open:J.open,onClose:()=>W({open:!1,mode:null,cavo:null}),cavo:J.cavo,cantiere:ev,onSave:eC}),(0,t.jsx)(an,{open:O.open,onClose:()=>$({open:!1,cavo:null}),cavo:O.cavo,cantiere:ev,onSuccess:eN,onError:ey}),Z.cavo&&(0,t.jsx)(e4,{open:Z.open,onClose:()=>H({open:!1,cavo:null}),cavo:Z.cavo,cantiere:ev,onSuccess:()=>{g({title:"Successo",description:"Operazione completata con successo"}),eg()}}),(0,t.jsx)(e7,{open:K.open,onClose:()=>X({open:!1,cavo:null}),cavo:K.cavo,onConfirm:()=>{en({visible:!0,message:"Cavo scollegato con successo"}),X({open:!1,cavo:null}),eg()},onError:e=>{er({open:!0,cavo:K.cavo,error:e}),X({open:!1,cavo:null})}}),(0,t.jsx)(e9,{open:Y.open,onClose:()=>Q({open:!1,cavo:null}),cavo:Y.cavo,onSuccess:()=>{en({visible:!0,message:"PDF generato con successo"}),Q({open:!1,cavo:null})},onError:e=>{er({open:!0,cavo:Y.cavo,error:e}),Q({open:!1,cavo:null})}}),(0,t.jsx)(aa,{open:ee.open,onClose:()=>ea({open:!1,cavo:null}),cavo:ee.cavo,cantiereId:u?.id_cantiere,onCertify:ew}),(0,t.jsx)(ai,{open:ei.open,onClose:()=>et({open:!1,cavi:[]}),cavi:ei.cavi,onCertify:ez}),(0,t.jsx)(ae,{open:es.open,onClose:()=>er({open:!1,cavo:null,error:""}),cavo:es.cavo,error:es.error,onRetry:()=>{er({open:!1,cavo:null,error:""}),es.cavo&&ea({open:!0,cavo:es.cavo})}}),(0,t.jsx)(at,{visible:el.visible,message:el.message,onClose:()=>en({visible:!1,message:""})}),(0,t.jsx)(am,{open:eo.open,onClose:()=>ec({open:!1}),caviSelezionati:z,tipoComanda:eo.tipoComanda,onSuccess:eN,onError:ey}),(0,t.jsx)(ap,{open:ed.open,onClose:()=>em({open:!1}),tipo:ed.tipo||"cavi",onSuccess:eN,onError:ey}),(0,t.jsx)(aH,{open:ex,onClose:()=>eu(!1),onSuccess:eN,onError:ey}),(0,t.jsx)(av,{open:L,onClose:()=>P(!1),cantiere:u,onSuccess:e=>{eN(e),eg()},onError:ey}),(0,t.jsx)(ag,{open:D.open,onClose:()=>U({open:!1,cavo:null}),cavo:D.cavo,cantiere:u,onSuccess:e=>{eN(e),eg()},onError:ey}),(0,t.jsx)(aW,{open:B.open,onClose:()=>G({open:!1,cavo:null}),cavo:B.cavo,cantiere:u,onSuccess:e=>{eN(e),eg()},onError:ey}),F.open&&F.cavo&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold",children:["Dettagli Cavo ",F.cavo.id_cavo]}),(0,t.jsx)("button",{onClick:()=>M({open:!1,cavo:null}),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"ID Cavo:"})," ",(0,t.jsx)("span",{className:"font-bold",children:F.cavo.id_cavo})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Tipologia:"})," ",(0,t.jsx)("span",{className:"font-bold",children:F.cavo.tipologia||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Sezione:"})," ",(0,t.jsx)("span",{className:"font-bold",children:F.cavo.sezione||F.cavo.formazione||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Sistema:"})," ",(0,t.jsx)("span",{className:"font-bold",children:F.cavo.sistema||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Utility:"})," ",(0,t.jsx)("span",{className:"font-bold",children:F.cavo.utility||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Colore:"})," ",(0,t.jsx)("span",{className:"font-bold",children:F.cavo.colore_cavo||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Da:"})," ",(0,t.jsx)("span",{className:"font-bold",children:F.cavo.ubicazione_partenza||F.cavo.da||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"A:"})," ",(0,t.jsx)("span",{className:"font-bold",children:F.cavo.ubicazione_arrivo||F.cavo.a||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Metri Teorici:"})," ",(0,t.jsx)("span",{className:"font-bold",children:F.cavo.metri_teorici||0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Metri Posati:"})," ",(0,t.jsx)("span",{className:"font-bold",children:F.cavo.metri_posati||F.cavo.metratura_reale||0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Stato:"})," ",(0,t.jsx)("span",{className:"font-bold",children:F.cavo.stato_installazione||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Bobina:"})," ",(0,t.jsx)("span",{className:"font-bold",children:F.cavo.id_bobina||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Collegamenti:"})," ",(0,t.jsx)("span",{className:"font-bold",children:F.cavo.collegamento||F.cavo.collegamenti||0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Modificato Manualmente:"})," ",(0,t.jsx)("span",{className:"font-bold",children:F.cavo.modificato_manualmente||0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Responsabile Posa:"})," ",(0,t.jsx)("span",{className:"font-bold",children:F.cavo.responsabile_posa||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Timestamp:"})," ",(0,t.jsx)("span",{className:"font-bold",children:F.cavo.timestamp||"N/A"})]})]}),(0,t.jsx)("div",{className:"mt-6 flex justify-end",children:(0,t.jsx)("button",{onClick:()=>M({open:!1,cavo:null}),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Chiudi"})})]})})]})})}},94735:e=>{"use strict";e.exports=require("events")},98741:(e,a,i)=>{"use strict";i.r(a),i.d(a,{default:()=>t});let t=(0,i(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs_1\\\\src\\\\app\\\\cavi\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_1\\src\\app\\cavi\\layout.tsx","default")}};var a=require("../../webpack-runtime.js");a.C(e);var i=e=>a(a.s=e),t=a.X(0,[4447,6539,1658,7400,9464,4949,4951,3140,5653,9039],()=>i(19538));module.exports=t})();