#!/usr/bin/env python
# monitor_system.py - Sistema di monitoraggio per CABLYS
import requests
import socket
import time
import json
from datetime import datetime
from pathlib import Path

class CablysMonitor:
    def __init__(self, config_file="run_config.json"):
        self.config = self.load_config(config_file)
        
    def load_config(self, config_file):
        """Carica la configurazione"""
        try:
            config_path = Path(__file__).resolve().parent / config_file
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            return {
                "backend": {"port": 8001},
                "frontend": {"port": 3000}
            }
    
    def check_port(self, port, host="localhost"):
        """Verifica se una porta è in ascolto"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(2)
                result = s.connect_ex((host, port))
                return result == 0
        except:
            return False
    
    def check_http_endpoint(self, url, timeout=5):
        """Verifica se un endpoint HTTP risponde"""
        try:
            response = requests.get(url, timeout=timeout)
            return response.status_code == 200
        except:
            return False
    
    def check_backend(self):
        """Verifica lo stato del backend"""
        port = self.config.get("backend", {}).get("port", 8001)
        
        # Verifica porta
        port_open = self.check_port(port)
        if not port_open:
            return {"status": "down", "port": port, "message": "Porta non in ascolto"}
        
        # Verifica endpoint API
        api_url = f"http://localhost:{port}/"
        api_responding = self.check_http_endpoint(api_url)
        
        if api_responding:
            return {"status": "up", "port": port, "message": "Backend operativo"}
        else:
            return {"status": "partial", "port": port, "message": "Porta aperta ma API non risponde"}
    
    def check_frontend(self):
        """Verifica lo stato del frontend"""
        port = self.config.get("frontend", {}).get("port", 3000)
        
        # Verifica porta
        port_open = self.check_port(port)
        if not port_open:
            return {"status": "down", "port": port, "message": "Porta non in ascolto"}
        
        # Verifica endpoint frontend
        frontend_url = f"http://localhost:{port}/"
        frontend_responding = self.check_http_endpoint(frontend_url)
        
        if frontend_responding:
            return {"status": "up", "port": port, "message": "Frontend operativo"}
        else:
            return {"status": "partial", "port": port, "message": "Porta aperta ma frontend non risponde"}
    
    def check_database(self):
        """Verifica la connessione al database"""
        try:
            import psycopg2
            db_config = self.config.get("database", {})
            conn = psycopg2.connect(
                host=db_config.get("host", "localhost"),
                port=db_config.get("port", 5432),
                database=db_config.get("name", "cantieri"),
                user=db_config.get("user", "postgres"),
                password="Taranto",
                connect_timeout=5
            )
            conn.close()
            return {"status": "up", "message": "Database connesso"}
        except ImportError:
            return {"status": "unknown", "message": "psycopg2 non installato"}
        except Exception as e:
            return {"status": "down", "message": f"Errore connessione: {str(e)}"}
    
    def get_system_status(self):
        """Ottieni lo stato completo del sistema"""
        return {
            "timestamp": datetime.now().isoformat(),
            "backend": self.check_backend(),
            "frontend": self.check_frontend(),
            "database": self.check_database()
        }
    
    def print_status(self, status=None):
        """Stampa lo stato del sistema"""
        if status is None:
            status = self.get_system_status()
        
        print("\n" + "="*60)
        print(f"🔍 CABLYS System Monitor - {status['timestamp']}")
        print("="*60)
        
        # Backend
        backend = status['backend']
        backend_icon = "🟢" if backend['status'] == "up" else "🟡" if backend['status'] == "partial" else "🔴"
        print(f"{backend_icon} Backend (:{backend['port']}): {backend['message']}")
        
        # Frontend
        frontend = status['frontend']
        frontend_icon = "🟢" if frontend['status'] == "up" else "🟡" if frontend['status'] == "partial" else "🔴"
        print(f"{frontend_icon} Frontend (:{frontend['port']}): {frontend['message']}")
        
        # Database
        database = status['database']
        db_icon = "🟢" if database['status'] == "up" else "🟡" if database['status'] == "unknown" else "🔴"
        print(f"{db_icon} Database: {database['message']}")
        
        # Stato generale
        all_up = all(s['status'] == "up" for s in [backend, frontend, database] if s['status'] != "unknown")
        overall_icon = "🟢" if all_up else "🟡"
        overall_status = "Tutti i servizi operativi" if all_up else "Alcuni servizi hanno problemi"
        
        print(f"\n{overall_icon} Stato generale: {overall_status}")
        print("="*60 + "\n")
    
    def monitor_continuous(self, interval=30):
        """Monitoraggio continuo"""
        print(f"🔄 Avvio monitoraggio continuo (intervallo: {interval}s)")
        print("Premi Ctrl+C per interrompere\n")
        
        try:
            while True:
                self.print_status()
                time.sleep(interval)
        except KeyboardInterrupt:
            print("\n👋 Monitoraggio interrotto")

def main():
    """Funzione principale"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Monitor per sistema CABLYS")
    parser.add_argument("--continuous", "-c", action="store_true", 
                       help="Monitoraggio continuo")
    parser.add_argument("--interval", "-i", type=int, default=30,
                       help="Intervallo di monitoraggio in secondi (default: 30)")
    parser.add_argument("--config", default="run_config.json",
                       help="File di configurazione (default: run_config.json)")
    
    args = parser.parse_args()
    
    monitor = CablysMonitor(args.config)
    
    if args.continuous:
        monitor.monitor_continuous(args.interval)
    else:
        monitor.print_status()

if __name__ == "__main__":
    main()
