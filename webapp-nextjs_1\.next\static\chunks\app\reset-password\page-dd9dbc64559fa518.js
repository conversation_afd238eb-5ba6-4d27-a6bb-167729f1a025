(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4700],{3493:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(19946).A)("cable",[["path",{d:"M17 21v-2a1 1 0 0 1-1-1v-1a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v1a1 1 0 0 1-1 1",key:"10bnsj"}],["path",{d:"M19 15V6.5a1 1 0 0 0-7 0v11a1 1 0 0 1-7 0V9",key:"1eqmu1"}],["path",{d:"M21 21v-2h-4",key:"14zm7j"}],["path",{d:"M3 5h4V3",key:"z442eg"}],["path",{d:"M7 5a1 1 0 0 1 1 1v1a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a1 1 0 0 1 1-1V3",key:"ebdjd7"}]])},10495:(e,s,a)=>{Promise.resolve().then(a.bind(a,67712))},35695:(e,s,a)=>{"use strict";var r=a(18999);a.o(r,"useParams")&&a.d(s,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(s,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(s,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(s,{useSearchParams:function(){return r.useSearchParams}})},67712:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>k});var r=a(95155),t=a(12115),i=a(6874),n=a.n(i),l=a(3493),c=a(35169),o=a(30285),d=a(35695),m=a(62523),x=a(85057),h=a(66695),u=a(55365),p=a(24944),w=a(54861),j=a(32919),f=a(78749),N=a(92657),g=a(40646),b=a(1243),v=a(75525),y=a(59434);function P(){var e;let s=(0,d.useRouter)(),a=(0,d.useSearchParams)().get("token"),[i,n]=(0,t.useState)({newPassword:"",confirmPassword:""}),[l,c]=(0,t.useState)({new:!1,confirm:!1}),[P,k]=(0,t.useState)({score:0,feedback:[],isValid:!1}),[S,A]=(0,t.useState)(!1),[z,C]=(0,t.useState)(null),[E,T]=(0,t.useState)(null);(0,t.useEffect)(()=>{if(!a){C({type:"error",text:"Token di reset mancante o non valido"}),T(!1);return}T(!0)},[a]);let R=async e=>{if(!e)return void k({score:0,feedback:[],isValid:!1});try{let s=await fetch("/api/password/validate-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({password:e})});if(s.ok){let e=await s.json();k({score:e.strength_score,feedback:e.suggestions||[],isValid:e.is_valid})}}catch(e){}},V=(e,s)=>{n(a=>({...a,[e]:s})),"newPassword"===e&&R(s)},_=async e=>{e.preventDefault(),A(!0),C(null);try{if(!i.newPassword||!i.confirmPassword)throw Error("Tutti i campi sono obbligatori");if(i.newPassword!==i.confirmPassword)throw Error("Le password non corrispondono");if(!P.isValid)throw Error("La password non rispetta i requisiti di sicurezza");if(!a)throw Error("Token di reset non valido");let e=await fetch("/api/password/confirm-password-reset",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({token:a,new_password:i.newPassword,confirm_password:i.confirmPassword})}),r=await e.json();if(e.ok&&r.success)C({type:"success",text:r.message}),setTimeout(()=>{s.push("/login")},3e3);else throw Error(r.detail||r.message||"Errore durante il reset della password")}catch(e){C({type:"error",text:e instanceof Error?e.message:"Errore durante il reset della password"})}finally{A(!1)}},F=e=>{c(s=>({...s,[e]:!s[e]}))};return!1===E?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsxs)(h.Zp,{className:"w-full max-w-md mx-auto",children:[(0,r.jsxs)(h.aR,{className:"text-center",children:[(0,r.jsxs)(h.ZB,{className:"text-2xl flex items-center justify-center gap-2 text-red-600",children:[(0,r.jsx)(w.A,{className:"h-6 w-6"}),"Token Non Valido"]}),(0,r.jsx)(h.BT,{children:"Il link di reset password non \xe8 valido o \xe8 scaduto"})]}),(0,r.jsxs)(h.Wu,{className:"text-center",children:[(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Il token potrebbe essere scaduto o gi\xe0 utilizzato. Richiedi un nuovo link di reset password."}),(0,r.jsx)(o.$,{onClick:()=>s.push("/forgot-password"),className:"bg-mariner-600 hover:bg-mariner-700",children:"Richiedi Nuovo Reset"})]})]})}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsxs)(h.Zp,{className:"w-full max-w-md mx-auto",children:[(0,r.jsxs)(h.aR,{className:"space-y-1",children:[(0,r.jsxs)(h.ZB,{className:"text-2xl flex items-center gap-2",children:[(0,r.jsx)(j.A,{className:"h-5 w-5 text-mariner-600"}),"Reimposta Password"]}),(0,r.jsx)(h.BT,{children:"Inserisci la tua nuova password sicura"})]}),(0,r.jsxs)(h.Wu,{children:[(0,r.jsxs)("form",{onSubmit:_,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(x.J,{htmlFor:"newPassword",children:"Nuova Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(m.p,{id:"newPassword",type:l.new?"text":"password",value:i.newPassword,onChange:e=>V("newPassword",e.target.value),className:"pr-10",required:!0}),(0,r.jsx)(o.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>F("new"),children:l.new?(0,r.jsx)(f.A,{className:"h-4 w-4 text-gray-400"}):(0,r.jsx)(N.A,{className:"h-4 w-4 text-gray-400"})})]}),i.newPassword&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsx)("span",{children:"Forza password:"}),(0,r.jsx)("span",{className:(0,y.cn)("font-medium",P.score<2?"text-red-600":P.score<4?"text-yellow-600":"text-green-600"),children:(e=P.score)<2?"Debole":e<4?"Media":"Forte"})]}),(0,r.jsx)(p.k,{value:P.score/5*100,className:"h-2"}),P.feedback.length>0&&(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,r.jsx)("p",{className:"font-medium mb-1",children:"Suggerimenti:"}),(0,r.jsx)("ul",{className:"list-disc list-inside space-y-1",children:P.feedback.map((e,s)=>(0,r.jsx)("li",{children:e},s))})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(x.J,{htmlFor:"confirmPassword",children:"Conferma Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(m.p,{id:"confirmPassword",type:l.confirm?"text":"password",value:i.confirmPassword,onChange:e=>V("confirmPassword",e.target.value),className:"pr-10",required:!0}),(0,r.jsx)(o.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>F("confirm"),children:l.confirm?(0,r.jsx)(f.A,{className:"h-4 w-4 text-gray-400"}):(0,r.jsx)(N.A,{className:"h-4 w-4 text-gray-400"})})]}),i.confirmPassword&&(0,r.jsx)("div",{className:"flex items-center gap-2 text-sm",children:i.newPassword===i.confirmPassword?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(g.A,{className:"h-4 w-4 text-green-600"}),(0,r.jsx)("span",{className:"text-green-600",children:"Le password corrispondono"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(w.A,{className:"h-4 w-4 text-red-600"}),(0,r.jsx)("span",{className:"text-red-600",children:"Le password non corrispondono"})]})})]}),z&&(0,r.jsxs)(u.Fc,{className:(0,y.cn)("success"===z.type?"border-green-200 bg-green-50":"border-red-200 bg-red-50"),children:["success"===z.type?(0,r.jsx)(g.A,{className:"h-4 w-4 text-green-600"}):(0,r.jsx)(b.A,{className:"h-4 w-4 text-red-600"}),(0,r.jsxs)(u.TN,{className:(0,y.cn)("success"===z.type?"text-green-800":"text-red-800"),children:[z.text,"success"===z.type&&(0,r.jsx)("div",{className:"mt-2 text-sm",children:"Verrai reindirizzato al login tra pochi secondi..."})]})]}),(0,r.jsx)(o.$,{type:"submit",className:"w-full bg-mariner-600 hover:bg-mariner-700",disabled:S||!P.isValid||i.newPassword!==i.confirmPassword,children:S?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Aggiornamento..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Reimposta Password"]})})]}),(0,r.jsx)("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-start gap-2",children:[(0,r.jsx)(v.A,{className:"h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,r.jsx)("p",{className:"font-medium mb-1",children:"Sicurezza"}),(0,r.jsxs)("ul",{className:"space-y-1 text-xs",children:[(0,r.jsx)("li",{children:"• Usa una password unica che non hai mai utilizzato prima"}),(0,r.jsx)("li",{children:"• Combina lettere maiuscole, minuscole, numeri e simboli"}),(0,r.jsx)("li",{children:"• Evita informazioni personali facilmente indovinabili"}),(0,r.jsx)("li",{children:"• Considera l'uso di un gestore di password"})]})]})]})})]})]})})}function k(){return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-slate-100 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md space-y-6",children:[(0,r.jsxs)("div",{className:"text-center space-y-2",children:[(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center",children:(0,r.jsx)(l.A,{className:"w-8 h-8 text-white"})})}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-slate-900",children:"CABLYS"}),(0,r.jsx)("p",{className:"text-slate-600",children:"Reimposta Password"})]}),(0,r.jsx)(t.Suspense,{fallback:(0,r.jsx)("div",{className:"text-center",children:"Caricamento..."}),children:(0,r.jsx)(P,{})}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)(n(),{href:"/login",children:(0,r.jsxs)(o.$,{variant:"ghost",className:"text-slate-600 hover:text-slate-900",children:[(0,r.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"Torna al Login"]})})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[3455,2600,6127,8441,1684,7358],()=>s(10495)),_N_E=e.O()}]);