'use client'

import { useState, useEffect, useRef } from 'react'
import * as VisuallyHidden from '@radix-ui/react-visually-hidden'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle, Zap, CheckCircle, AlertTriangle, X } from 'lucide-react'
import { Cavo } from '@/types'
import { caviApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'
import { useToast } from '@/hooks/use-toast'

interface CollegamentiDialogProps {
  open: boolean
  onClose: () => void
  cavo: Cavo | null
  onSuccess?: () => void
  onError?: (error: string) => void
}

interface ConfirmDialogState {
  open: boolean
  type: 'partenza' | 'arrivo' | 'entrambi' | null
  title: string
  description: string
}

interface ConfirmDisconnectDialogProps {
  open: boolean
  onClose: () => void
  onConfirm: () => void
  title: string
  description: string
  isLoading: boolean
  isDangerous?: boolean
}

function ConfirmDisconnectDialog({
  open,
  onClose,
  onConfirm,
  title,
  description,
  isLoading,
  isDangerous = false
}: ConfirmDisconnectDialogProps) {
  const [showFinalConfirmation, setShowFinalConfirmation] = useState(false)

  useEffect(() => {
    if (!open) {
      setShowFinalConfirmation(false)
    }
  }, [open])

  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && open && !isLoading) {
        onClose()
      }
    }

    if (open) {
      document.addEventListener('keydown', handleEsc)
      return () => document.removeEventListener('keydown', handleEsc)
    }
  }, [open, onClose, isLoading])

  const handleInitialConfirm = () => {
    if (isDangerous) {
      setShowFinalConfirmation(true)
    } else {
      onConfirm()
    }
  }

  const handleFinalConfirm = () => {
    onConfirm()
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent
        className="sm:max-w-[400px]"
        aria-describedby="confirm-disconnect-description"
      >
        {!showFinalConfirmation ? (
          <>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2 text-orange-600">
                <AlertTriangle className="h-5 w-5" />
                {title}
              </DialogTitle>
              <DialogDescription id="confirm-disconnect-description">
                {description}
              </DialogDescription>
            </DialogHeader>

            <div className="py-4">
              <Alert className="border-orange-200 bg-orange-50">
                <AlertTriangle className="h-4 w-4 text-orange-600" />
                <AlertDescription className="text-orange-800">
                  <strong>Attenzione:</strong> Questa azione modificherà lo stato del collegamento del cavo.
                </AlertDescription>
              </Alert>
            </div>

            <DialogFooter className="gap-2">
              <Button
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
                className="flex-1 hover:bg-gray-50"
              >
                Annulla
              </Button>
              <Button
                variant="outline"
                onClick={handleInitialConfirm}
                disabled={isLoading}
                className="flex-1 bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100 hover:border-orange-300"
              >
                <AlertTriangle className="mr-2 h-4 w-4" />
                {isDangerous ? 'Procedi' : 'Conferma'}
              </Button>
            </DialogFooter>
          </>
        ) : (
          <>
            <DialogHeader>
              <DialogTitle className="text-center text-orange-600">
                Conferma Finale
              </DialogTitle>
            </DialogHeader>

            <div className="py-4 text-center">
              <div className="mx-auto w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-4">
                <AlertTriangle className="h-6 w-6 text-orange-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Sei veramente sicuro?
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                Questa azione scollegherà <strong>entrambi i lati</strong> del cavo.
              </p>
              <div className="bg-orange-50 border border-orange-200 rounded-md p-3">
                <p className="text-sm text-orange-800 font-medium">
                  ⚠️ Operazione irreversibile
                </p>
              </div>
            </div>

            <DialogFooter className="gap-2">
              <Button
                variant="outline"
                onClick={() => setShowFinalConfirmation(false)}
                disabled={isLoading}
                className="flex-1"
              >
                No, Annulla
              </Button>
              <Button
                variant="outline"
                onClick={handleFinalConfirm}
                disabled={isLoading}
                className="flex-1 bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100 hover:border-orange-300"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Scollegando...
                  </>
                ) : (
                  'Sì, Scollega'
                )}
              </Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  )
}

function CollegamentiDialog({
  open,
  onClose,
  cavo,
  onSuccess,
  onError
}: CollegamentiDialogProps) {
  const { cantiere } = useAuth()
  const { toast } = useToast()
  const [selectedResponsabile, setSelectedResponsabile] = useState('cantiere')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const [confirmDialog, setConfirmDialog] = useState<ConfirmDialogState>({
    open: false,
    type: null,
    title: '',
    description: ''
  })

  const dialogRef = useRef<HTMLDivElement>(null)
  const firstFocusableRef = useRef<HTMLButtonElement>(null)
  const lastFocusableRef = useRef<HTMLButtonElement>(null)

  const [screenReaderAnnouncement, setScreenReaderAnnouncement] = useState('')

  useEffect(() => {
    if (open && cavo) {
      setSelectedResponsabile('cantiere')
      setError('')
    }
  }, [open, cavo])

  useEffect(() => {
    if (open && dialogRef.current) {
      const focusableElements = dialogRef.current.querySelectorAll(
        'button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
      )

      if (focusableElements.length > 0) {
        (focusableElements[0] as HTMLElement).focus()
      }
    }
  }, [open])

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!open || loading || confirmDialog.open) return

      switch (e.key) {
        case 'Escape':
          onClose()
          break

        case 'Tab':
          const focusableElements = dialogRef.current?.querySelectorAll(
            'button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
          )

          if (focusableElements && focusableElements.length > 0) {
            const firstElement = focusableElements[0] as HTMLElement
            const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement

            if (e.shiftKey && document.activeElement === firstElement) {
              e.preventDefault()
              lastElement.focus()
            } else if (!e.shiftKey && document.activeElement === lastElement) {
              e.preventDefault()
              firstElement.focus()
            }
          }
          break

        case '1':
        case '2':
        case '3':
          if (!loading) {
            e.preventDefault()
            const num = parseInt(e.key)
            if (num === 1) handleCollegaPartenza()
            else if (num === 2) handleCollegaArrivo()
            else if (num === 3) handleCollegaEntrambi()
          }
          break
      }
    }

    if (open) {
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [open, loading, confirmDialog.open])

  const announceToScreenReader = (message: string) => {
    setScreenReaderAnnouncement(message)
    setTimeout(() => setScreenReaderAnnouncement(''), 1000)
  }

  const getStatoCollegamento = () => {
    if (!cavo) return { stato: 'non_collegato', descrizione: 'Non collegato' }
    
    const collegamento = cavo.collegamento || cavo.collegamenti || 0
    
    switch (collegamento) {
      case 1:
        return { stato: 'partenza', descrizione: '🟢⚪ Collegato lato partenza' }
      case 2:
        return { stato: 'arrivo', descrizione: '⚪🟢 Collegato lato arrivo' }
      case 3:
        return { stato: 'completo', descrizione: '🟢🟢 Completamente collegato' }
      default:
        return { stato: 'non_collegato', descrizione: '⚪⚪ Non collegato' }
    }
  }

  const handleCollegaPartenza = () => {
    if (!cavo) return

    const statoAttuale = getStatoCollegamento()

    if (statoAttuale.stato === 'partenza' || statoAttuale.stato === 'completo') {
      setConfirmDialog({
        open: true,
        type: 'partenza',
        title: 'Scollega lato partenza',
        description: `Vuoi scollegare il lato partenza del cavo ${cavo.id_cavo}?`
      })
    } else {
      executeCollegaPartenza()
    }
  }

  const executeCollegaPartenza = async () => {
    if (!cavo || !cantiere) return

    try {
      setLoading(true)
      setError('')
      announceToScreenReader('Collegamento in corso...')

      await caviApi.collegaCavo(
        cantiere.id_cantiere,
        cavo.id_cavo,
        'partenza',
        selectedResponsabile
      )

      const message = `Collegamento lato partenza completato per il cavo ${cavo.id_cavo}`
      announceToScreenReader(message)
      toast({
        title: "Successo",
        description: message,
      })

      if (onSuccess) onSuccess()
      onClose()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante il collegamento'
      setError(errorMessage)
      announceToScreenReader(`Errore: ${errorMessage}`)
      if (onError) onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleCollegaArrivo = () => {
    if (!cavo) return

    const statoAttuale = getStatoCollegamento()

    if (statoAttuale.stato === 'arrivo' || statoAttuale.stato === 'completo') {
      setConfirmDialog({
        open: true,
        type: 'arrivo',
        title: 'Scollega lato arrivo',
        description: `Vuoi scollegare il lato arrivo del cavo ${cavo.id_cavo}?`
      })
    } else {
      executeCollegaArrivo()
    }
  }

  const executeCollegaArrivo = async () => {
    if (!cavo || !cantiere) return

    try {
      setLoading(true)
      setError('')
      announceToScreenReader('Collegamento in corso...')

      await caviApi.collegaCavo(
        cantiere.id_cantiere,
        cavo.id_cavo,
        'arrivo',
        selectedResponsabile
      )

      const message = `Collegamento lato arrivo completato per il cavo ${cavo.id_cavo}`
      announceToScreenReader(message)
      toast({
        title: "Successo",
        description: message,
      })

      if (onSuccess) onSuccess()
      onClose()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante il collegamento'
      setError(errorMessage)
      announceToScreenReader(`Errore: ${errorMessage}`)
      if (onError) onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleCollegaEntrambi = () => {
    if (!cavo) return

    const statoAttuale = getStatoCollegamento()

    if (statoAttuale.stato === 'completo') {
      setConfirmDialog({
        open: true,
        type: 'entrambi',
        title: 'Scollega entrambi i lati',
        description: `Vuoi scollegare completamente il cavo ${cavo.id_cavo}? Questa operazione rimuoverà tutti i collegamenti.`
      })
    } else {
      executeCollegaEntrambi()
    }
  }

  const executeCollegaEntrambi = async () => {
    if (!cavo || !cantiere) return

    try {
      setLoading(true)
      setError('')
      announceToScreenReader('Collegamento entrambi i lati in corso...')

      await caviApi.collegaCavo(
        cantiere.id_cantiere,
        cavo.id_cavo,
        'entrambi',
        selectedResponsabile
      )

      const message = `Collegamento completo per il cavo ${cavo.id_cavo}`
      announceToScreenReader(message)
      toast({
        title: "Successo",
        description: message,
      })

      if (onSuccess) onSuccess()
      onClose()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante il collegamento'
      setError(errorMessage)
      announceToScreenReader(`Errore: ${errorMessage}`)
      if (onError) onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const executeDisconnect = async () => {
    if (!cavo || !cantiere || !confirmDialog.type) return

    try {
      setLoading(true)
      setError('')
      announceToScreenReader('Scollegamento in corso...')

      await caviApi.scollegaCavo(
        cantiere.id_cantiere,
        cavo.id_cavo,
        confirmDialog.type === 'entrambi' ? undefined : confirmDialog.type
      )

      const latoText = confirmDialog.type === 'entrambi' ? '' : ` lato ${confirmDialog.type}`
      const message = `Scollegamento${latoText} completato per il cavo ${cavo.id_cavo}`
      announceToScreenReader(message)
      toast({
        title: "Successo",
        description: message,
      })

      if (onSuccess) onSuccess()
      setConfirmDialog({ open: false, type: null, title: '', description: '' })
      onClose()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante lo scollegamento'
      setError(errorMessage)
      announceToScreenReader(`Errore: ${errorMessage}`)
      if (onError) onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  if (!cavo) return null

  const statoCollegamento = getStatoCollegamento()
  const isInstalled = (cavo.metri_posati || cavo.metratura_reale || 0) > 0

  return (
    <>
      <VisuallyHidden.Root>
        <div aria-live="polite" aria-atomic="true">
          {screenReaderAnnouncement}
        </div>
      </VisuallyHidden.Root>

      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent
          className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto"
          ref={dialogRef}
          aria-describedby="collegamenti-description"
        >
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-blue-600">
              <Zap className="h-5 w-5" />
              Gestione Collegamenti - {cavo.id_cavo}
            </DialogTitle>
            <DialogDescription id="collegamenti-description">
              Gestisci i collegamenti del cavo {cavo.id_cavo}. Usa i tasti 1, 2, 3 per azioni rapide.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Informazioni cavo */}
            <div className="p-4 bg-blue-50 rounded-lg border-2 border-blue-200">
              <div className="text-sm font-medium text-blue-800">
                Informazioni Cavo / Tipologia: {cavo.tipologia || 'N/A'} / Da: {cavo.ubicazione_partenza || 'N/A'} / Formazione: {cavo.sezione || 'N/A'} / A: {cavo.ubicazione_arrivo || 'N/A'} / Metri Posati: {cavo.metratura_reale || 0} m
              </div>
            </div>

            {/* Stato attuale con icone migliorate */}
            <div className="p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Stato Collegamento</Label>
                  <div className="mt-1 text-lg font-semibold flex items-center gap-2">
                    {statoCollegamento.stato === 'completo' && <CheckCircle className="h-5 w-5 text-green-600" />}
                    {statoCollegamento.stato === 'non_collegato' && <AlertCircle className="h-5 w-5 text-gray-400" />}
                    {(statoCollegamento.stato === 'partenza' || statoCollegamento.stato === 'arrivo') && <AlertTriangle className="h-5 w-5 text-orange-500" />}
                    {statoCollegamento.descrizione}
                  </div>
                </div>
              </div>
            </div>

            {!isInstalled && (
              <Alert className="border-orange-200 bg-orange-50">
                <AlertCircle className="h-4 w-4 text-orange-600" />
                <AlertDescription className="text-orange-800">
                  <strong>Attenzione:</strong> Il cavo deve essere installato prima di poter essere collegato.
                </AlertDescription>
              </Alert>
            )}

            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {isInstalled && (
              <>
                {/* Selezione responsabile semplificata */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium">Responsabile Collegamento</Label>
                  <div className="p-3 bg-gray-50 rounded-lg border">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="font-medium">Cantiere</span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      Collegamento eseguito dal responsabile del cantiere
                    </p>
                  </div>
                </div>

                {/* Azioni di collegamento con design migliorato */}
                <div className="space-y-4">
                  <Label className="text-sm font-medium">Azioni Disponibili</Label>

                  <div className="grid grid-cols-1 gap-3">
                    <Button
                      ref={firstFocusableRef}
                      onClick={handleCollegaPartenza}
                      disabled={loading}
                      className="w-full h-12 text-left justify-start bg-green-50 text-green-700 border-green-200 hover:bg-green-100 hover:border-green-300"
                      variant="outline"
                    >
                      <div className="flex items-center justify-between w-full">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <span className="text-sm font-bold text-green-700">1</span>
                          </div>
                          <div>
                            <div className="font-medium">
                              {statoCollegamento.stato === 'partenza' || statoCollegamento.stato === 'completo'
                                ? 'Scollega Partenza'
                                : 'Collega Partenza'}
                            </div>
                            <div className="text-xs text-green-600">
                              {statoCollegamento.stato === 'partenza' || statoCollegamento.stato === 'completo'
                                ? 'Rimuovi collegamento lato partenza'
                                : 'Connetti il lato partenza del cavo'}
                            </div>
                          </div>
                        </div>
                        {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Zap className="h-4 w-4" />}
                      </div>
                    </Button>

                    <Button
                      onClick={handleCollegaArrivo}
                      disabled={loading}
                      className="w-full h-12 text-left justify-start bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 hover:border-blue-300"
                      variant="outline"
                    >
                      <div className="flex items-center justify-between w-full">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <span className="text-sm font-bold text-blue-700">2</span>
                          </div>
                          <div>
                            <div className="font-medium">
                              {statoCollegamento.stato === 'arrivo' || statoCollegamento.stato === 'completo'
                                ? 'Scollega Arrivo'
                                : 'Collega Arrivo'}
                            </div>
                            <div className="text-xs text-blue-600">
                              {statoCollegamento.stato === 'arrivo' || statoCollegamento.stato === 'completo'
                                ? 'Rimuovi collegamento lato arrivo'
                                : 'Connetti il lato arrivo del cavo'}
                            </div>
                          </div>
                        </div>
                        {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Zap className="h-4 w-4" />}
                      </div>
                    </Button>

                    <Button
                      onClick={handleCollegaEntrambi}
                      disabled={loading}
                      className="w-full h-12 text-left justify-start bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100 hover:border-purple-300"
                      variant="outline"
                    >
                      <div className="flex items-center justify-between w-full">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                            <span className="text-sm font-bold text-purple-700">3</span>
                          </div>
                          <div>
                            <div className="font-medium">
                              {statoCollegamento.stato === 'completo'
                                ? 'Scollega Completamente'
                                : 'Collega Entrambi'}
                            </div>
                            <div className="text-xs text-purple-600">
                              {statoCollegamento.stato === 'completo'
                                ? 'Rimuovi tutti i collegamenti'
                                : 'Connetti entrambi i lati del cavo'}
                            </div>
                          </div>
                        </div>
                        {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Zap className="h-4 w-4" />}
                      </div>
                    </Button>
                  </div>
                </div>
              </>
            )}
          </div>

          <DialogFooter>
            <Button
              ref={lastFocusableRef}
              variant="outline"
              onClick={onClose}
              disabled={loading}
              className="hover:bg-gray-50"
            >
              <X className="mr-2 h-4 w-4" />
              Chiudi
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <ConfirmDisconnectDialog
        open={confirmDialog.open}
        onClose={() => setConfirmDialog({ open: false, type: null, title: '', description: '' })}
        onConfirm={executeDisconnect}
        title={confirmDialog.title}
        description={confirmDialog.description}
        isLoading={loading}
        isDangerous={confirmDialog.type === 'entrambi'}
      />
    </>
  )
}

export default CollegamentiDialog
