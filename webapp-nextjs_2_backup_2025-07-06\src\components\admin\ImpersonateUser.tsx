'use client'

import { useState, useEffect } from 'react'
import { useR<PERSON><PERSON> } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { PrimaryButton } from '@/components/ui/animated-button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { usersApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'
import { User } from '@/types'
import { Loader2, LogIn, AlertCircle } from 'lucide-react'

export default function ImpersonateUser() {
  const router = useRouter()
  const { impersonateUser } = useAuth()
  const [users, setUsers] = useState<User[]>([])
  const [selectedUserId, setSelectedUserId] = useState('')
  const [loading, setLoading] = useState(false)
  const [loadingUsers, setLoadingUsers] = useState(true)
  const [error, setError] = useState('')

  // Carica gli utenti all'avvio del componente
  useEffect(() => {
    loadUsers()
  }, [])

  const loadUsers = async () => {
    setLoadingUsers(true)
    try {
      const data = await usersApi.getUsers()
      // Filtra solo gli utenti non amministratori
      const nonAdminUsers = data.filter((user: User) => user.ruolo !== 'owner')
      setUsers(nonAdminUsers)
      setError('')
    } catch (err: any) {
      setError(err.response?.data?.detail || err.message || 'Errore durante il caricamento degli utenti')
    } finally {
      setLoadingUsers(false)
    }
  }

  // Gestisce l'accesso come utente selezionato
  const handleImpersonate = async () => {
    if (!selectedUserId) {
      setError('Seleziona un utente')
      return
    }

    setLoading(true)
    try {
      // Trova l'utente selezionato per ottenere il ruolo
      const selectedUser = users.find(user => user.id_utente === parseInt(selectedUserId))
      if (!selectedUser) {
        throw new Error('Utente non trovato')
      }

      // Utilizza la funzione impersonateUser dal contesto di autenticazione
      const userData = await impersonateUser(parseInt(selectedUserId))

      // Reindirizza in base al ruolo dell'utente impersonato
      // L'amministratore mantiene i suoi privilegi ma accede alle funzionalità dell'utente impersonato
      if (selectedUser.ruolo === 'user') {
        // Utente standard - vai alla pagina dei cantieri
        router.push('/cantieri')
      } else if (selectedUser.ruolo === 'cantieri_user') {
        // Utente cantiere - vai alla pagina di visualizzazione cavi
        router.push('/cavi')
      } else {
        // Fallback - vai alla homepage
        router.push('/')
      }
    } catch (err: any) {
      setError(err.response?.data?.detail || err.message || 'Errore durante l\'accesso come utente selezionato')
      setLoading(false)
    }
  }

  const getRuoloBadge = (ruolo: string) => {
    switch (ruolo) {
      case 'user':
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">User</span>
      case 'cantieri_user':
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Cantieri User</span>
      default:
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">{ruolo}</span>
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <LogIn className="h-5 w-5" />
          Accedi come Utente
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900">Informazioni sull'impersonificazione</h4>
              <p className="text-sm text-blue-700 mt-1">
                Questa funzionalità ti permette di accedere al sistema come se fossi un altro utente, 
                mantenendo i tuoi privilegi di amministratore. Utile per testare le funzionalità 
                o assistere gli utenti.
              </p>
            </div>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="user-select">Seleziona Utente</Label>
            {loadingUsers ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="ml-2">Caricamento utenti...</span>
              </div>
            ) : users.length === 0 ? (
              <div className="text-center py-8 text-slate-500">
                Nessun utente disponibile per l'impersonificazione
              </div>
            ) : (
              <Select value={selectedUserId} onValueChange={setSelectedUserId}>
                <SelectTrigger>
                  <SelectValue placeholder="Seleziona un utente da impersonare" />
                </SelectTrigger>
                <SelectContent>
                  {users.map((user) => (
                    <SelectItem key={user.id_utente} value={user.id_utente.toString()}>
                      <div className="flex items-center justify-between w-full">
                        <div className="flex flex-col">
                          <span className="font-medium">{user.username}</span>
                          <span className="text-sm text-slate-500">{user.ragione_sociale || 'N/A'}</span>
                        </div>
                        <div className="ml-4">
                          {getRuoloBadge(user.ruolo)}
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>

          {selectedUserId && (
            <div className="bg-slate-50 border border-slate-200 rounded-lg p-4">
              <h4 className="font-medium text-slate-900 mb-2">Utente Selezionato</h4>
              {(() => {
                const selectedUser = users.find(user => user.id_utente === parseInt(selectedUserId))
                if (!selectedUser) return null
                
                return (
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-slate-600">Username:</span>
                      <span className="font-medium">{selectedUser.username}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-600">Ragione Sociale:</span>
                      <span className="font-medium">{selectedUser.ragione_sociale || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-600">Ruolo:</span>
                      <span>{getRuoloBadge(selectedUser.ruolo)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-600">Email:</span>
                      <span className="font-medium">{selectedUser.email || 'N/A'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-600">Stato:</span>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        selectedUser.abilitato 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {selectedUser.abilitato ? 'Attivo' : 'Disabilitato'}
                      </span>
                    </div>
                  </div>
                )
              })()}
            </div>
          )}

          <PrimaryButton
            onClick={handleImpersonate}
            disabled={!selectedUserId || loading || loadingUsers}
            className="w-full"
            loading={loading}
            icon={<LogIn className="h-4 w-4" />}
            glow
          >
            {loading ? 'Accesso in corso...' : 'Accedi come Utente Selezionato'}
          </PrimaryButton>
        </div>
      </CardContent>
    </Card>
  )
}
