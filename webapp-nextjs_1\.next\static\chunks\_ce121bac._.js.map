{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_1/src/app/cantieri/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useRouter, useParams } from 'next/navigation'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { cantieriApi } from '@/lib/api'\nimport { Cantiere } from '@/types'\nimport {\n  ArrowLeft,\n  Loader2,\n  AlertCircle\n} from 'lucide-react'\n\nexport default function CantierePage() {\n  const { user, isAuthenticated, isLoading, selectCantiere } = useAuth()\n  const router = useRouter()\n  const params = useParams()\n  const cantiereId = parseInt(params.id as string)\n  \n  const [cantiere, setCantiere] = useState<Cantiere | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState('')\n\n  useEffect(() => {\n    if (!isLoading && !isAuthenticated) {\n      router.push('/login')\n    }\n  }, [isAuthenticated, isLoading, router])\n\n  useEffect(() => {\n    if (isAuthenticated && cantiereId) {\n      loadCantiere()\n    }\n  }, [isAuthenticated, cantiereId])\n\n  const loadCantiere = async () => {\n    try {\n      setLoading(true)\n      const data = await cantieriApi.getCantiere(cantiereId)\n      setCantiere(data)\n      \n      // Usa la funzione selectCantiere dal context per aggiornare tutto il sistema\n      selectCantiere(data)\n    } catch (error) {\n      setError('Errore nel caricamento del cantiere')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleBackToCantieri = () => {\n    router.push('/cantieri')\n  }\n\n  // Reindirizza automaticamente alla pagina di visualizzazione cavi come nella webapp originale\n  useEffect(() => {\n    if (cantiere && !loading) {\n      router.push('/cavi')\n    }\n  }, [cantiere, loading, router])\n\n  if (isLoading || loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <Loader2 className=\"h-8 w-8 animate-spin\" />\n      </div>\n    )\n  }\n\n  if (error || !cantiere) {\n    return (\n      <div className=\"max-w-[90%] mx-auto p-6\">\n        <div className=\"mb-4 p-4 border border-red-200 rounded-lg bg-red-50\">\n          <div className=\"flex items-center\">\n            <AlertCircle className=\"h-4 w-4 text-red-600 mr-2\" />\n            <span className=\"text-red-800\">{error || 'Cantiere non trovato'}</span>\n          </div>\n        </div>\n        <Button onClick={handleBackToCantieri}>\n          <ArrowLeft className=\"mr-2 h-4 w-4\" />\n          Torna alla Lista Cantieri\n        </Button>\n      </div>\n    )\n  }\n\n  // Mostra un loading mentre reindirizza\n  return (\n    <div className=\"flex items-center justify-center min-h-screen\">\n      <div className=\"text-center\">\n        <Loader2 className=\"h-8 w-8 animate-spin mx-auto mb-4\" />\n        <p className=\"text-muted-foreground\">Caricamento gestione cavi...</p>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAEA;AACA;AAEA;AAAA;AAAA;;;AAVA;;;;;;;AAgBe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACnE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,aAAa,SAAS,OAAO,EAAE;IAErC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,CAAC,aAAa,CAAC,iBAAiB;gBAClC,OAAO,IAAI,CAAC;YACd;QACF;iCAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,mBAAmB,YAAY;gBACjC;YACF;QACF;iCAAG;QAAC;QAAiB;KAAW;IAEhC,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,OAAO,MAAM,oHAAA,CAAA,cAAW,CAAC,WAAW,CAAC;YAC3C,YAAY;YAEZ,6EAA6E;YAC7E,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,8FAA8F;IAC9F,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,YAAY,CAAC,SAAS;gBACxB,OAAO,IAAI,CAAC;YACd;QACF;iCAAG;QAAC;QAAU;QAAS;KAAO;IAE9B,IAAI,aAAa,SAAS;QACxB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,oNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;;;;;;IAGzB;IAEA,IAAI,SAAS,CAAC,UAAU;QACtB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC;gCAAK,WAAU;0CAAgB,SAAS;;;;;;;;;;;;;;;;;8BAG7C,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAS;;sCACf,6LAAC,mNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;;IAK9C;IAEA,uCAAuC;IACvC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,oNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;8BACnB,6LAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;;;;;;AAI7C;GAlFwB;;QACuC,kIAAA,CAAA,UAAO;QACrD,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "file": "arrow-left.js", "sources": ["file:///C:/CMS/webapp-nextjs_1/node_modules/lucide-react/src/icons/arrow-left.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm12 19-7-7 7-7', key: '1l729n' }],\n  ['path', { d: 'M19 12H5', key: 'x3x0zl' }],\n];\n\n/**\n * @component @name ArrowLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIgMTktNy03IDctNyIgLz4KICA8cGF0aCBkPSJNMTkgMTJINSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowLeft = createLucideIcon('arrow-left', __iconNode);\n\nexport default ArrowLeft;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC/C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}