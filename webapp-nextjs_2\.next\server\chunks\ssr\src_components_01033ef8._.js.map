{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,8OAAC,wMAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm border-collapse\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;YAC9D,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Popover = PopoverPrimitive.Root\n\nconst PopoverTrigger = PopoverPrimitive.Trigger\n\nconst PopoverContent = React.forwardRef<\n  React.ElementRef<typeof PopoverPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\n  <PopoverPrimitive.Portal>\n    <PopoverPrimitive.Content\n      ref={ref}\n      align={align}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className\n      )}\n      {...props}\n    />\n  </PopoverPrimitive.Portal>\n))\nPopoverContent.displayName = PopoverPrimitive.Content.displayName\n\nexport { Popover, PopoverTrigger, PopoverContent }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,UAAU,mKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,mKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC5D,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,KAAK;YACL,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8aACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,mKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 605, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/common/FilterableTable.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useMemo } from 'react'\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from '@/components/ui/table'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport {\n  Popover,\n  PopoverContent,\n  PopoverTrigger,\n} from '@/components/ui/popover'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport {\n  Filter,\n  ChevronDown,\n  ChevronUp,\n  ArrowUpDown,\n  ArrowUp,\n  ArrowDown,\n  X,\n  ChevronLeft,\n  ChevronRight,\n  ChevronsLeft,\n  ChevronsRight\n} from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\nexport interface ColumnDef {\n  field: string\n  headerName: string\n  dataType?: 'text' | 'number' | 'date'\n  align?: 'left' | 'center' | 'right'\n  width?: number\n  disableFilter?: boolean\n  disableSort?: boolean\n  headerStyle?: React.CSSProperties\n  cellStyle?: React.CSSProperties\n  renderHeader?: () => React.ReactNode\n  renderCell?: (row: any) => React.ReactNode\n}\n\ninterface FilterableTableProps {\n  data: any[]\n  columns: ColumnDef[]\n  loading?: boolean\n  emptyMessage?: string\n  onFilteredDataChange?: (filteredData: any[]) => void\n  renderRow?: (row: any, index: number) => React.ReactNode\n  className?: string\n  pagination?: boolean\n  defaultRowsPerPage?: number\n}\n\ninterface SortConfig {\n  key: string | null\n  direction: 'asc' | 'desc' | null\n}\n\ninterface FilterConfig {\n  [key: string]: {\n    type: 'text' | 'select' | 'number'\n    value: string | string[]\n    operator?: 'contains' | 'equals' | 'gt' | 'lt' | 'gte' | 'lte'\n  }\n}\n\nexport default function FilterableTable({\n  data = [],\n  columns = [],\n  loading = false,\n  emptyMessage = 'Nessun dato disponibile',\n  onFilteredDataChange,\n  renderRow,\n  className,\n  pagination = true,\n  defaultRowsPerPage = 25\n}: FilterableTableProps) {\n  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: null, direction: null })\n  const [filters, setFilters] = useState<FilterConfig>({})\n  const [openFilters, setOpenFilters] = useState<{ [key: string]: boolean }>({})\n  const [currentPage, setCurrentPage] = useState(0)\n  const [rowsPerPage, setRowsPerPage] = useState(defaultRowsPerPage)\n\n  // Get unique values for select filters\n  const getUniqueValues = (field: string) => {\n    return [...new Set(data.map(item => item[field]).filter(Boolean))].sort()\n  }\n\n  // Apply filters and sorting\n  const filteredAndSortedData = useMemo(() => {\n    let filtered = [...data]\n\n    // Apply filters\n    Object.entries(filters).forEach(([field, filterConfig]) => {\n      if (!filterConfig.value || \n          (Array.isArray(filterConfig.value) && filterConfig.value.length === 0) ||\n          (typeof filterConfig.value === 'string' && filterConfig.value.trim() === '')) {\n        return\n      }\n\n      filtered = filtered.filter(item => {\n        const itemValue = item[field]\n        \n        if (filterConfig.type === 'select') {\n          const selectedValues = Array.isArray(filterConfig.value) ? filterConfig.value : [filterConfig.value]\n          return selectedValues.includes(itemValue)\n        }\n        \n        if (filterConfig.type === 'text') {\n          const searchValue = (filterConfig.value as string).toLowerCase()\n          const cellValue = String(itemValue || '').toLowerCase()\n          \n          if (filterConfig.operator === 'equals') {\n            return cellValue === searchValue\n          }\n          return cellValue.includes(searchValue)\n        }\n        \n        if (filterConfig.type === 'number') {\n          const numValue = parseFloat(itemValue)\n          const filterValue = parseFloat(filterConfig.value as string)\n          \n          if (isNaN(numValue) || isNaN(filterValue)) return false\n          \n          switch (filterConfig.operator) {\n            case 'equals': return numValue === filterValue\n            case 'gt': return numValue > filterValue\n            case 'lt': return numValue < filterValue\n            case 'gte': return numValue >= filterValue\n            case 'lte': return numValue <= filterValue\n            default: return numValue === filterValue\n          }\n        }\n        \n        return true\n      })\n    })\n\n    // Apply sorting\n    if (sortConfig.key && sortConfig.direction) {\n      filtered.sort((a, b) => {\n        const aValue = a[sortConfig.key!]\n        const bValue = b[sortConfig.key!]\n        \n        // Handle null/undefined values\n        if (aValue == null && bValue == null) return 0\n        if (aValue == null) return sortConfig.direction === 'asc' ? -1 : 1\n        if (bValue == null) return sortConfig.direction === 'asc' ? 1 : -1\n        \n        // Determine if values are numbers\n        const aNum = parseFloat(aValue)\n        const bNum = parseFloat(bValue)\n        const isNumeric = !isNaN(aNum) && !isNaN(bNum)\n        \n        let comparison = 0\n        if (isNumeric) {\n          comparison = aNum - bNum\n        } else {\n          comparison = String(aValue).localeCompare(String(bValue))\n        }\n        \n        return sortConfig.direction === 'asc' ? comparison : -comparison\n      })\n    }\n\n    return filtered\n  }, [data, filters, sortConfig])\n\n  // Calculate paginated data\n  const paginatedData = useMemo(() => {\n    if (!pagination) return filteredAndSortedData\n\n    const startIndex = currentPage * rowsPerPage\n    const endIndex = startIndex + rowsPerPage\n    return filteredAndSortedData.slice(startIndex, endIndex)\n  }, [filteredAndSortedData, currentPage, rowsPerPage, pagination])\n\n  // Reset page when filters change\n  useEffect(() => {\n    setCurrentPage(0)\n  }, [filters])\n\n  // Calculate pagination info\n  const totalPages = Math.ceil(filteredAndSortedData.length / rowsPerPage)\n  const startRow = currentPage * rowsPerPage + 1\n  const endRow = Math.min((currentPage + 1) * rowsPerPage, filteredAndSortedData.length)\n\n  // Notify parent of filtered data changes\n  useEffect(() => {\n    if (onFilteredDataChange) {\n      onFilteredDataChange(filteredAndSortedData)\n    }\n  }, [filteredAndSortedData, onFilteredDataChange])\n\n  const handleSort = (field: string) => {\n    const column = columns.find(col => col.field === field)\n    if (column?.disableSort) return\n\n    setSortConfig(prev => {\n      if (prev.key === field) {\n        if (prev.direction === 'asc') return { key: field, direction: 'desc' }\n        if (prev.direction === 'desc') return { key: null, direction: null }\n      }\n      return { key: field, direction: 'asc' }\n    })\n  }\n\n  const updateFilter = (field: string, filterConfig: Partial<FilterConfig[string]>) => {\n    setFilters(prev => ({\n      ...prev,\n      [field]: { ...prev[field], ...filterConfig }\n    }))\n  }\n\n  const clearFilter = (field: string) => {\n    setFilters(prev => {\n      const newFilters = { ...prev }\n      delete newFilters[field]\n      return newFilters\n    })\n  }\n\n  const clearAllFilters = () => {\n    setFilters({})\n  }\n\n  const getSortIcon = (field: string) => {\n    if (sortConfig.key !== field) return <ArrowUpDown className=\"h-3 w-3\" />\n    if (sortConfig.direction === 'asc') return <ArrowUp className=\"h-3 w-3\" />\n    if (sortConfig.direction === 'desc') return <ArrowDown className=\"h-3 w-3\" />\n    return <ArrowUpDown className=\"h-3 w-3\" />\n  }\n\n  const hasActiveFilters = Object.keys(filters).length > 0\n\n  if (loading) {\n    return (\n      <Card className={className}>\n        <CardContent className=\"p-6\">\n          <div className=\"text-center\">Caricamento...</div>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  return (\n    <div className={className}>\n      {/* Active filters display */}\n      {hasActiveFilters && (\n        <div className=\"mb-4 flex flex-wrap gap-2 items-center\">\n          <span className=\"text-sm text-muted-foreground\">Filtri attivi:</span>\n          {Object.entries(filters).map(([field, filterConfig]) => {\n            const column = columns.find(col => col.field === field)\n            if (!column) return null\n            \n            const displayValue = Array.isArray(filterConfig.value) \n              ? filterConfig.value.join(', ')\n              : String(filterConfig.value)\n            \n            return (\n              <Badge key={field} variant=\"secondary\" className=\"gap-1\">\n                {column.headerName}: {displayValue}\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  className=\"h-auto p-0 hover:bg-transparent\"\n                  onClick={() => clearFilter(field)}\n                >\n                  <X className=\"h-3 w-3\" />\n                </Button>\n              </Badge>\n            )\n          })}\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={clearAllFilters}\n            className=\"h-6 px-2 text-xs\"\n          >\n            Pulisci tutti\n          </Button>\n        </div>\n      )}\n\n      {/* Table */}\n      <Card>\n        <CardContent className=\"p-0\">\n          <Table>\n            <TableHeader>\n              <TableRow className=\"bg-mariner-50 hover:bg-mariner-50\">\n                {columns.map((column) => (\n                  <TableHead\n                    key={column.field}\n                    className={cn(\n                      \"font-semibold text-mariner-900 border-b border-mariner-200\",\n                      column.align === 'center' && \"text-center\",\n                      column.align === 'right' && \"text-right\"\n                    )}\n                    style={{ width: column.width, ...column.headerStyle }}\n                  >\n                    {column.renderHeader ? (\n                      column.renderHeader()\n                    ) : (\n                      <div className=\"relative group\">\n                        <div className=\"flex items-center justify-between w-full\">\n                          <span className=\"truncate\">{column.headerName}</span>\n\n                          {/* Compact icons container - only visible on hover */}\n                          <div className=\"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\n                            {/* Sort button */}\n                            {!column.disableSort && (\n                              <Button\n                                variant=\"ghost\"\n                                size=\"sm\"\n                                className=\"h-4 w-4 p-0 hover:bg-mariner-100\"\n                                onClick={() => handleSort(column.field)}\n                              >\n                                {getSortIcon(column.field)}\n                              </Button>\n                            )}\n\n                            {/* Filter button */}\n                            {!column.disableFilter && (\n                              <Popover\n                                open={openFilters[column.field]}\n                                onOpenChange={(open) => setOpenFilters(prev => ({ ...prev, [column.field]: open }))}\n                              >\n                                <PopoverTrigger asChild>\n                                  <Button\n                                    variant=\"ghost\"\n                                    size=\"sm\"\n                                    className={cn(\n                                      \"h-4 w-4 p-0 hover:bg-mariner-100\",\n                                      filters[column.field] && \"text-mariner-600 opacity-100\"\n                                    )}\n                                  >\n                                    <Filter className=\"h-2.5 w-2.5\" />\n                                  </Button>\n                                </PopoverTrigger>\n                                <PopoverContent className=\"w-64\" align=\"start\">\n                                  <FilterContent\n                                    column={column}\n                                    data={data}\n                                    currentFilter={filters[column.field]}\n                                    onFilterChange={(filterConfig) => updateFilter(column.field, filterConfig)}\n                                    onClearFilter={() => clearFilter(column.field)}\n                                    getUniqueValues={() => getUniqueValues(column.field)}\n                                  />\n                                </PopoverContent>\n                              </Popover>\n                            )}\n                          </div>\n                        </div>\n\n                        {/* Active filter indicator */}\n                        {filters[column.field] && (\n                          <div className=\"absolute -top-1 -right-1 h-2 w-2 bg-mariner-600 rounded-full\"></div>\n                        )}\n                      </div>\n                    )}\n                  </TableHead>\n                ))}\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {paginatedData.length > 0 ? (\n                paginatedData.map((row, index) => (\n                  renderRow ? (\n                    renderRow(row, currentPage * rowsPerPage + index)\n                  ) : (\n                    <TableRow\n                      key={index}\n                      className=\"hover:bg-mariner-50 border-b border-mariner-100\"\n                    >\n                      {columns.map((column) => (\n                        <TableCell\n                          key={column.field}\n                          className={cn(\n                            \"py-2 px-4\",\n                            column.align === 'center' && \"text-center\",\n                            column.align === 'right' && \"text-right\"\n                          )}\n                          style={column.cellStyle}\n                        >\n                          {column.renderCell ? column.renderCell(row) : row[column.field]}\n                        </TableCell>\n                      ))}\n                    </TableRow>\n                  )\n                ))\n              ) : (\n                <TableRow>\n                  <TableCell colSpan={columns.length} className=\"text-center py-8 text-muted-foreground\">\n                    {emptyMessage}\n                  </TableCell>\n                </TableRow>\n              )}\n            </TableBody>\n          </Table>\n        </CardContent>\n      </Card>\n\n      {/* Pagination Controls */}\n      {pagination && filteredAndSortedData.length > 0 && (\n        <div className=\"flex items-center justify-between mt-4\">\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-sm text-muted-foreground\">\n              Righe per pagina:\n            </span>\n            <Select\n              value={rowsPerPage.toString()}\n              onValueChange={(value) => {\n                setRowsPerPage(Number(value))\n                setCurrentPage(0)\n              }}\n            >\n              <SelectTrigger className=\"w-20\">\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"10\">10</SelectItem>\n                <SelectItem value=\"25\">25</SelectItem>\n                <SelectItem value=\"50\">50</SelectItem>\n                <SelectItem value=\"100\">100</SelectItem>\n                <SelectItem value={filteredAndSortedData.length.toString()}>Tutto</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-sm text-muted-foreground\">\n              {filteredAndSortedData.length > 0 ? `${startRow}-${endRow} di ${filteredAndSortedData.length}` : '0 di 0'}\n            </span>\n\n            <div className=\"flex items-center space-x-1\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setCurrentPage(0)}\n                disabled={currentPage === 0}\n                className=\"h-8 w-8 p-0\"\n              >\n                <ChevronsLeft className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setCurrentPage(prev => Math.max(0, prev - 1))}\n                disabled={currentPage === 0}\n                className=\"h-8 w-8 p-0\"\n              >\n                <ChevronLeft className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setCurrentPage(prev => Math.min(totalPages - 1, prev + 1))}\n                disabled={currentPage >= totalPages - 1}\n                className=\"h-8 w-8 p-0\"\n              >\n                <ChevronRight className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setCurrentPage(totalPages - 1)}\n                disabled={currentPage >= totalPages - 1}\n                className=\"h-8 w-8 p-0\"\n              >\n                <ChevronsRight className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n\n// Filter content component\ninterface FilterContentProps {\n  column: ColumnDef\n  data: any[]\n  currentFilter?: FilterConfig[string]\n  onFilterChange: (filterConfig: Partial<FilterConfig[string]>) => void\n  onClearFilter: () => void\n  getUniqueValues: () => any[]\n}\n\nfunction FilterContent({\n  column,\n  currentFilter,\n  onFilterChange,\n  onClearFilter,\n  getUniqueValues\n}: FilterContentProps) {\n  const [localValue, setLocalValue] = useState(currentFilter?.value || '')\n  const [operator, setOperator] = useState(currentFilter?.operator || 'contains')\n\n  const uniqueValues = getUniqueValues()\n  const isSelectType = column.dataType !== 'number' && uniqueValues.length <= 20\n  const isNumberType = column.dataType === 'number'\n\n  const applyFilter = () => {\n    if (isSelectType) {\n      onFilterChange({\n        type: 'select',\n        value: Array.isArray(localValue) ? localValue : [localValue]\n      })\n    } else if (isNumberType) {\n      onFilterChange({\n        type: 'number',\n        value: localValue as string,\n        operator\n      })\n    } else {\n      onFilterChange({\n        type: 'text',\n        value: localValue as string,\n        operator\n      })\n    }\n  }\n\n  return (\n    <div className=\"space-y-3\">\n      <div className=\"font-medium text-sm\">Filtra {column.headerName}</div>\n      \n      {isSelectType ? (\n        <div className=\"space-y-2 max-h-48 overflow-y-auto\">\n          {uniqueValues.map(value => (\n            <div key={value} className=\"flex items-center space-x-2\">\n              <Checkbox\n                id={`filter-${value}`}\n                checked={Array.isArray(localValue) ? localValue.includes(value) : localValue === value}\n                onCheckedChange={(checked) => {\n                  if (Array.isArray(localValue)) {\n                    setLocalValue(checked \n                      ? [...localValue, value]\n                      : localValue.filter(v => v !== value)\n                    )\n                  } else {\n                    setLocalValue(checked ? [value] : [])\n                  }\n                }}\n              />\n              <label htmlFor={`filter-${value}`} className=\"text-sm\">\n                {value}\n              </label>\n            </div>\n          ))}\n        </div>\n      ) : (\n        <div className=\"space-y-2\">\n          {isNumberType && (\n            <Select value={operator} onValueChange={setOperator}>\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"equals\">Uguale a</SelectItem>\n                <SelectItem value=\"gt\">Maggiore di</SelectItem>\n                <SelectItem value=\"lt\">Minore di</SelectItem>\n                <SelectItem value=\"gte\">Maggiore o uguale</SelectItem>\n                <SelectItem value=\"lte\">Minore o uguale</SelectItem>\n              </SelectContent>\n            </Select>\n          )}\n          \n          {!isNumberType && (\n            <Select value={operator} onValueChange={setOperator}>\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"contains\">Contiene</SelectItem>\n                <SelectItem value=\"equals\">Uguale a</SelectItem>\n              </SelectContent>\n            </Select>\n          )}\n          \n          <Input\n            placeholder={`Cerca ${column.headerName.toLowerCase()}...`}\n            value={localValue as string}\n            onChange={(e) => setLocalValue(e.target.value)}\n            onKeyDown={(e) => e.key === 'Enter' && applyFilter()}\n          />\n        </div>\n      )}\n      \n      <div className=\"flex gap-2\">\n        <Button size=\"sm\" onClick={applyFilter}>\n          Applica\n        </Button>\n        <Button size=\"sm\" variant=\"outline\" onClick={onClearFilter}>\n          Pulisci\n        </Button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AAOA;AAKA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAzCA;;;;;;;;;;;;;AAkFe,SAAS,gBAAgB,EACtC,OAAO,EAAE,EACT,UAAU,EAAE,EACZ,UAAU,KAAK,EACf,eAAe,yBAAyB,EACxC,oBAAoB,EACpB,SAAS,EACT,SAAS,EACT,aAAa,IAAI,EACjB,qBAAqB,EAAE,EACF;IACrB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;QAAE,KAAK;QAAM,WAAW;IAAK;IACtF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,CAAC;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B,CAAC;IAC5E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,uCAAuC;IACvC,MAAM,kBAAkB,CAAC;QACvB,OAAO;eAAI,IAAI,IAAI,KAAK,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC;SAAU,CAAC,IAAI;IACzE;IAEA,4BAA4B;IAC5B,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACpC,IAAI,WAAW;eAAI;SAAK;QAExB,gBAAgB;QAChB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,OAAO,aAAa;YACpD,IAAI,CAAC,aAAa,KAAK,IAClB,MAAM,OAAO,CAAC,aAAa,KAAK,KAAK,aAAa,KAAK,CAAC,MAAM,KAAK,KACnE,OAAO,aAAa,KAAK,KAAK,YAAY,aAAa,KAAK,CAAC,IAAI,OAAO,IAAK;gBAChF;YACF;YAEA,WAAW,SAAS,MAAM,CAAC,CAAA;gBACzB,MAAM,YAAY,IAAI,CAAC,MAAM;gBAE7B,IAAI,aAAa,IAAI,KAAK,UAAU;oBAClC,MAAM,iBAAiB,MAAM,OAAO,CAAC,aAAa,KAAK,IAAI,aAAa,KAAK,GAAG;wBAAC,aAAa,KAAK;qBAAC;oBACpG,OAAO,eAAe,QAAQ,CAAC;gBACjC;gBAEA,IAAI,aAAa,IAAI,KAAK,QAAQ;oBAChC,MAAM,cAAc,AAAC,aAAa,KAAK,CAAY,WAAW;oBAC9D,MAAM,YAAY,OAAO,aAAa,IAAI,WAAW;oBAErD,IAAI,aAAa,QAAQ,KAAK,UAAU;wBACtC,OAAO,cAAc;oBACvB;oBACA,OAAO,UAAU,QAAQ,CAAC;gBAC5B;gBAEA,IAAI,aAAa,IAAI,KAAK,UAAU;oBAClC,MAAM,WAAW,WAAW;oBAC5B,MAAM,cAAc,WAAW,aAAa,KAAK;oBAEjD,IAAI,MAAM,aAAa,MAAM,cAAc,OAAO;oBAElD,OAAQ,aAAa,QAAQ;wBAC3B,KAAK;4BAAU,OAAO,aAAa;wBACnC,KAAK;4BAAM,OAAO,WAAW;wBAC7B,KAAK;4BAAM,OAAO,WAAW;wBAC7B,KAAK;4BAAO,OAAO,YAAY;wBAC/B,KAAK;4BAAO,OAAO,YAAY;wBAC/B;4BAAS,OAAO,aAAa;oBAC/B;gBACF;gBAEA,OAAO;YACT;QACF;QAEA,gBAAgB;QAChB,IAAI,WAAW,GAAG,IAAI,WAAW,SAAS,EAAE;YAC1C,SAAS,IAAI,CAAC,CAAC,GAAG;gBAChB,MAAM,SAAS,CAAC,CAAC,WAAW,GAAG,CAAE;gBACjC,MAAM,SAAS,CAAC,CAAC,WAAW,GAAG,CAAE;gBAEjC,+BAA+B;gBAC/B,IAAI,UAAU,QAAQ,UAAU,MAAM,OAAO;gBAC7C,IAAI,UAAU,MAAM,OAAO,WAAW,SAAS,KAAK,QAAQ,CAAC,IAAI;gBACjE,IAAI,UAAU,MAAM,OAAO,WAAW,SAAS,KAAK,QAAQ,IAAI,CAAC;gBAEjE,kCAAkC;gBAClC,MAAM,OAAO,WAAW;gBACxB,MAAM,OAAO,WAAW;gBACxB,MAAM,YAAY,CAAC,MAAM,SAAS,CAAC,MAAM;gBAEzC,IAAI,aAAa;gBACjB,IAAI,WAAW;oBACb,aAAa,OAAO;gBACtB,OAAO;oBACL,aAAa,OAAO,QAAQ,aAAa,CAAC,OAAO;gBACnD;gBAEA,OAAO,WAAW,SAAS,KAAK,QAAQ,aAAa,CAAC;YACxD;QACF;QAEA,OAAO;IACT,GAAG;QAAC;QAAM;QAAS;KAAW;IAE9B,2BAA2B;IAC3B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,IAAI,CAAC,YAAY,OAAO;QAExB,MAAM,aAAa,cAAc;QACjC,MAAM,WAAW,aAAa;QAC9B,OAAO,sBAAsB,KAAK,CAAC,YAAY;IACjD,GAAG;QAAC;QAAuB;QAAa;QAAa;KAAW;IAEhE,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe;IACjB,GAAG;QAAC;KAAQ;IAEZ,4BAA4B;IAC5B,MAAM,aAAa,KAAK,IAAI,CAAC,sBAAsB,MAAM,GAAG;IAC5D,MAAM,WAAW,cAAc,cAAc;IAC7C,MAAM,SAAS,KAAK,GAAG,CAAC,CAAC,cAAc,CAAC,IAAI,aAAa,sBAAsB,MAAM;IAErF,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,sBAAsB;YACxB,qBAAqB;QACvB;IACF,GAAG;QAAC;QAAuB;KAAqB;IAEhD,MAAM,aAAa,CAAC;QAClB,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;QACjD,IAAI,QAAQ,aAAa;QAEzB,cAAc,CAAA;YACZ,IAAI,KAAK,GAAG,KAAK,OAAO;gBACtB,IAAI,KAAK,SAAS,KAAK,OAAO,OAAO;oBAAE,KAAK;oBAAO,WAAW;gBAAO;gBACrE,IAAI,KAAK,SAAS,KAAK,QAAQ,OAAO;oBAAE,KAAK;oBAAM,WAAW;gBAAK;YACrE;YACA,OAAO;gBAAE,KAAK;gBAAO,WAAW;YAAM;QACxC;IACF;IAEA,MAAM,eAAe,CAAC,OAAe;QACnC,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;oBAAE,GAAG,IAAI,CAAC,MAAM;oBAAE,GAAG,YAAY;gBAAC;YAC7C,CAAC;IACH;IAEA,MAAM,cAAc,CAAC;QACnB,WAAW,CAAA;YACT,MAAM,aAAa;gBAAE,GAAG,IAAI;YAAC;YAC7B,OAAO,UAAU,CAAC,MAAM;YACxB,OAAO;QACT;IACF;IAEA,MAAM,kBAAkB;QACtB,WAAW,CAAC;IACd;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,WAAW,GAAG,KAAK,OAAO,qBAAO,8OAAC,wNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QAC5D,IAAI,WAAW,SAAS,KAAK,OAAO,qBAAO,8OAAC,4MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QAC9D,IAAI,WAAW,SAAS,KAAK,QAAQ,qBAAO,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QACjE,qBAAO,8OAAC,wNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;IAChC;IAEA,MAAM,mBAAmB,OAAO,IAAI,CAAC,SAAS,MAAM,GAAG;IAEvD,IAAI,SAAS;QACX,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;sBACf,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;8BAAc;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,8OAAC;QAAI,WAAW;;YAEb,kCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAgC;;;;;;oBAC/C,OAAO,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,OAAO,aAAa;wBACjD,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;wBACjD,IAAI,CAAC,QAAQ,OAAO;wBAEpB,MAAM,eAAe,MAAM,OAAO,CAAC,aAAa,KAAK,IACjD,aAAa,KAAK,CAAC,IAAI,CAAC,QACxB,OAAO,aAAa,KAAK;wBAE7B,qBACE,8OAAC,iIAAA,CAAA,QAAK;4BAAa,SAAQ;4BAAY,WAAU;;gCAC9C,OAAO,UAAU;gCAAC;gCAAG;8CACtB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,YAAY;8CAE3B,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;2BARL;;;;;oBAYhB;kCACA,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAOL,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC,iIAAA,CAAA,QAAK;;0CACJ,8OAAC,iIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;oCAAC,WAAU;8CACjB,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,iIAAA,CAAA,YAAS;4CAER,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA,OAAO,KAAK,KAAK,YAAY,eAC7B,OAAO,KAAK,KAAK,WAAW;4CAE9B,OAAO;gDAAE,OAAO,OAAO,KAAK;gDAAE,GAAG,OAAO,WAAW;4CAAC;sDAEnD,OAAO,YAAY,GAClB,OAAO,YAAY,mBAEnB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAY,OAAO,UAAU;;;;;;0EAG7C,8OAAC;gEAAI,WAAU;;oEAEZ,CAAC,OAAO,WAAW,kBAClB,8OAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS,IAAM,WAAW,OAAO,KAAK;kFAErC,YAAY,OAAO,KAAK;;;;;;oEAK5B,CAAC,OAAO,aAAa,kBACpB,8OAAC,mIAAA,CAAA,UAAO;wEACN,MAAM,WAAW,CAAC,OAAO,KAAK,CAAC;wEAC/B,cAAc,CAAC,OAAS,eAAe,CAAA,OAAQ,CAAC;oFAAE,GAAG,IAAI;oFAAE,CAAC,OAAO,KAAK,CAAC,EAAE;gFAAK,CAAC;;0FAEjF,8OAAC,mIAAA,CAAA,iBAAc;gFAAC,OAAO;0FACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oFACL,SAAQ;oFACR,MAAK;oFACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oCACA,OAAO,CAAC,OAAO,KAAK,CAAC,IAAI;8FAG3B,cAAA,8OAAC,sMAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;;;;;;;;;;;0FAGtB,8OAAC,mIAAA,CAAA,iBAAc;gFAAC,WAAU;gFAAO,OAAM;0FACrC,cAAA,8OAAC;oFACC,QAAQ;oFACR,MAAM;oFACN,eAAe,OAAO,CAAC,OAAO,KAAK,CAAC;oFACpC,gBAAgB,CAAC,eAAiB,aAAa,OAAO,KAAK,EAAE;oFAC7D,eAAe,IAAM,YAAY,OAAO,KAAK;oFAC7C,iBAAiB,IAAM,gBAAgB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oDAS9D,OAAO,CAAC,OAAO,KAAK,CAAC,kBACpB,8OAAC;wDAAI,WAAU;;;;;;;;;;;;2CAhEhB,OAAO,KAAK;;;;;;;;;;;;;;;0CAwEzB,8OAAC,iIAAA,CAAA,YAAS;0CACP,cAAc,MAAM,GAAG,IACtB,cAAc,GAAG,CAAC,CAAC,KAAK,QACtB,YACE,UAAU,KAAK,cAAc,cAAc,uBAE3C,8OAAC,iIAAA,CAAA,WAAQ;wCAEP,WAAU;kDAET,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,iIAAA,CAAA,YAAS;gDAER,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,OAAO,KAAK,KAAK,YAAY,eAC7B,OAAO,KAAK,KAAK,WAAW;gDAE9B,OAAO,OAAO,SAAS;0DAEtB,OAAO,UAAU,GAAG,OAAO,UAAU,CAAC,OAAO,GAAG,CAAC,OAAO,KAAK,CAAC;+CAR1D,OAAO,KAAK;;;;;uCALhB;;;;8DAoBX,8OAAC,iIAAA,CAAA,WAAQ;8CACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;wCAAC,SAAS,QAAQ,MAAM;wCAAE,WAAU;kDAC3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUd,cAAc,sBAAsB,MAAM,GAAG,mBAC5C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAgC;;;;;;0CAGhD,8OAAC,kIAAA,CAAA,SAAM;gCACL,OAAO,YAAY,QAAQ;gCAC3B,eAAe,CAAC;oCACd,eAAe,OAAO;oCACtB,eAAe;gCACjB;;kDAEA,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kDAEd,8OAAC,kIAAA,CAAA,gBAAa;;0DACZ,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;0DACvB,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;0DACvB,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;0DACvB,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;0DACxB,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAO,sBAAsB,MAAM,CAAC,QAAQ;0DAAI;;;;;;;;;;;;;;;;;;;;;;;;kCAKlE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CACb,sBAAsB,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC,EAAE,OAAO,IAAI,EAAE,sBAAsB,MAAM,EAAE,GAAG;;;;;;0CAGnG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe;wCAC9B,UAAU,gBAAgB;wCAC1B,WAAU;kDAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;kDAE1B,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;wCACzD,UAAU,gBAAgB;wCAC1B,WAAU;kDAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAEzB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,aAAa,GAAG,OAAO;wCACtE,UAAU,eAAe,aAAa;wCACtC,WAAU;kDAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;kDAE1B,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe,aAAa;wCAC3C,UAAU,eAAe,aAAa;wCACtC,WAAU;kDAEV,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzC;AAYA,SAAS,cAAc,EACrB,MAAM,EACN,aAAa,EACb,cAAc,EACd,aAAa,EACb,eAAe,EACI;IACnB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,SAAS;IACrE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,YAAY;IAEpE,MAAM,eAAe;IACrB,MAAM,eAAe,OAAO,QAAQ,KAAK,YAAY,aAAa,MAAM,IAAI;IAC5E,MAAM,eAAe,OAAO,QAAQ,KAAK;IAEzC,MAAM,cAAc;QAClB,IAAI,cAAc;YAChB,eAAe;gBACb,MAAM;gBACN,OAAO,MAAM,OAAO,CAAC,cAAc,aAAa;oBAAC;iBAAW;YAC9D;QACF,OAAO,IAAI,cAAc;YACvB,eAAe;gBACb,MAAM;gBACN,OAAO;gBACP;YACF;QACF,OAAO;YACL,eAAe;gBACb,MAAM;gBACN,OAAO;gBACP;YACF;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;oBAAsB;oBAAQ,OAAO,UAAU;;;;;;;YAE7D,6BACC,8OAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAA,sBAChB,8OAAC;wBAAgB,WAAU;;0CACzB,8OAAC,oIAAA,CAAA,WAAQ;gCACP,IAAI,CAAC,OAAO,EAAE,OAAO;gCACrB,SAAS,MAAM,OAAO,CAAC,cAAc,WAAW,QAAQ,CAAC,SAAS,eAAe;gCACjF,iBAAiB,CAAC;oCAChB,IAAI,MAAM,OAAO,CAAC,aAAa;wCAC7B,cAAc,UACV;+CAAI;4CAAY;yCAAM,GACtB,WAAW,MAAM,CAAC,CAAA,IAAK,MAAM;oCAEnC,OAAO;wCACL,cAAc,UAAU;4CAAC;yCAAM,GAAG,EAAE;oCACtC;gCACF;;;;;;0CAEF,8OAAC;gCAAM,SAAS,CAAC,OAAO,EAAE,OAAO;gCAAE,WAAU;0CAC1C;;;;;;;uBAhBK;;;;;;;;;qCAsBd,8OAAC;gBAAI,WAAU;;oBACZ,8BACC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAU,eAAe;;0CACtC,8OAAC,kIAAA,CAAA,gBAAa;0CACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0CAEd,8OAAC,kIAAA,CAAA,gBAAa;;kDACZ,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAS;;;;;;kDAC3B,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAK;;;;;;kDACvB,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAK;;;;;;kDACvB,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;kDACxB,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;;;;;;;;;;;;;oBAK7B,CAAC,8BACA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAU,eAAe;;0CACtC,8OAAC,kIAAA,CAAA,gBAAa;0CACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0CAEd,8OAAC,kIAAA,CAAA,gBAAa;;kDACZ,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAW;;;;;;kDAC7B,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAS;;;;;;;;;;;;;;;;;;kCAKjC,8OAAC,iIAAA,CAAA,QAAK;wBACJ,aAAa,CAAC,MAAM,EAAE,OAAO,UAAU,CAAC,WAAW,GAAG,GAAG,CAAC;wBAC1D,OAAO;wBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC7C,WAAW,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;;;;;;;0BAK7C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAK,SAAS;kCAAa;;;;;;kCAGxC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAK,SAAQ;wBAAU,SAAS;kCAAe;;;;;;;;;;;;;;;;;;AAMpE", "debugId": null}}, {"offset": {"line": 1579, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1607, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/cavi/SmartCaviFilter.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useCallback, useEffect, useMemo } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Card, CardContent } from '@/components/ui/card'\nimport {\n  Search,\n  X,\n  CheckSquare,\n  Square,\n  Filter,\n  ChevronDown,\n  ChevronUp,\n  Zap,\n  Package,\n  Clock,\n  CheckCircle,\n  AlertTriangle,\n  Settings\n} from 'lucide-react'\nimport { Cavo } from '@/types'\nimport {\n  Tooltip,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from '@/components/ui/tooltip'\n\ninterface SmartCaviFilterProps {\n  cavi: Cavo[]\n  onFilteredDataChange?: (filteredCavi: Cavo[]) => void\n  loading?: boolean\n  selectionEnabled?: boolean\n  onSelectionToggle?: () => void\n}\n\nexport default function SmartCaviFilter({\n  cavi = [],\n  onFilteredDataChange,\n  loading = false,\n  selectionEnabled = false,\n  onSelectionToggle\n}: SmartCaviFilterProps) {\n  const [searchText, setSearchText] = useState('')\n  const [searchType, setSearchType] = useState<'contains' | 'equals'>('contains')\n  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false)\n\n  // Filtri avanzati\n  const [statusFilter, setStatusFilter] = useState<string>('all')\n  const [connectionFilter, setConnectionFilter] = useState<string>('all')\n  const [certificationFilter, setCertificationFilter] = useState<string>('all')\n  const [systemFilter, setSystemFilter] = useState<string>('all')\n  const [meterRangeMin, setMeterRangeMin] = useState<string>('')\n  const [meterRangeMax, setMeterRangeMax] = useState<string>('')\n\n  // Normalize string for search\n  const normalizeString = useCallback((str: string | null | undefined): string => {\n    if (!str) return ''\n    return str.toString().toLowerCase().trim()\n  }, [])\n\n  // Extract cable info for advanced search\n  const getCavoInfo = useCallback((idCavo: string) => {\n    const match = idCavo.match(/^([A-Z]+)(\\d+)([A-Z]*)$/)\n    if (match) {\n      return {\n        prefix: match[1],\n        number: match[2],\n        suffix: match[3] || ''\n      }\n    }\n    return { prefix: '', number: idCavo, suffix: '' }\n  }, [])\n\n  // Check if a cable matches a search term\n  const cavoMatchesTerm = useCallback((cavo: Cavo, term: string, exactMatch: boolean): boolean => {\n    const normalizedTerm = normalizeString(term)\n    \n    if (!normalizedTerm) return true\n\n    // Basic cable info\n    const cavoId = normalizeString(cavo.id_cavo)\n    const { prefix: cavoPrefix, number: cavoNumber, suffix: cavoSuffix } = getCavoInfo(cavo.id_cavo || '')\n    \n    // Cable properties\n    const tipologia = normalizeString(cavo.tipologia)\n    const formazione = normalizeString(cavo.formazione || cavo.sezione)\n    const utility = normalizeString(cavo.utility)\n    const sistema = normalizeString(cavo.sistema)\n    \n    // Locations\n    const ubicazionePartenza = normalizeString(cavo.da || cavo.ubicazione_partenza)\n    const ubicazioneArrivo = normalizeString(cavo.a || cavo.ubicazione_arrivo)\n    const utenzaPartenza = normalizeString(cavo.utenza_partenza)\n    const utenzaArrivo = normalizeString(cavo.utenza_arrivo)\n    \n    // Reel info\n    const bobina = normalizeString(cavo.id_bobina)\n    const bobinaDisplay = cavo.id_bobina === 'BOBINA_VUOTA' ? 'bobina vuota' :\n                         cavo.id_bobina === null ? '' :\n                         normalizeString(cavo.id_bobina)\n\n    // All text fields to search\n    const textFields = [\n      cavoId, cavoPrefix, cavoNumber, cavoSuffix, tipologia, formazione, utility, sistema,\n      ubicazionePartenza, ubicazioneArrivo, utenzaPartenza, utenzaArrivo,\n      bobina, bobinaDisplay\n    ]\n\n    // Numeric fields for range search\n    const numericFields = [\n      { value: cavo.metri_teorici, name: 'metri_teorici' },\n      { value: cavo.metratura_reale || cavo.metri_posati, name: 'metratura_reale' },\n      { value: parseFloat(formazione), name: 'formazione' }\n    ]\n\n    // Check for range queries (e.g., \">100\", \"<=50\")\n    const rangeMatch = normalizedTerm.match(/^([><=]+)(\\d+(?:\\.\\d+)?)$/)\n    if (rangeMatch) {\n      const operator = rangeMatch[1]\n      const value = parseFloat(rangeMatch[2])\n      \n      return numericFields.some(field => {\n        if (field.value == null || isNaN(field.value)) return false\n        \n        switch (operator) {\n          case '>': return field.value > value\n          case '>=': return field.value >= value\n          case '<': return field.value < value\n          case '<=': return field.value <= value\n          case '=': return field.value === value\n          default: return false\n        }\n      })\n    }\n\n    // Check for exact numeric match\n    const numericTerm = parseFloat(normalizedTerm)\n    if (!isNaN(numericTerm)) {\n      const numericMatch = numericFields.some(field => \n        field.value != null && !isNaN(field.value) && field.value === numericTerm\n      )\n      if (numericMatch) return true\n    }\n\n    // Text search\n    if (exactMatch) {\n      return textFields.some(field => field === normalizedTerm)\n    } else {\n      return textFields.some(field => field.includes(normalizedTerm))\n    }\n  }, [normalizeString, getCavoInfo])\n\n  // Funzione per applicare filtri avanzati\n  const applyAdvancedFilters = useCallback((caviToFilter: Cavo[]) => {\n    let filtered = caviToFilter\n\n    // Filtro stato installazione\n    if (statusFilter !== 'all') {\n      filtered = filtered.filter(cavo => {\n        switch (statusFilter) {\n          case 'installati':\n            return cavo.stato_installazione === 'Installato' ||\n                   (cavo.metri_posati && cavo.metri_posati > 0) ||\n                   (cavo.metratura_reale && cavo.metratura_reale > 0)\n          case 'in_corso':\n            return cavo.stato_installazione === 'In corso'\n          case 'da_installare':\n            return cavo.stato_installazione !== 'Installato' &&\n                   cavo.stato_installazione !== 'In corso' &&\n                   !(cavo.metri_posati && cavo.metri_posati > 0) &&\n                   !(cavo.metratura_reale && cavo.metratura_reale > 0)\n          default:\n            return true\n        }\n      })\n    }\n\n    // Filtro collegamento\n    if (connectionFilter !== 'all') {\n      filtered = filtered.filter(cavo => {\n        const collegamento = cavo.collegamento || cavo.collegamenti || 0\n        switch (connectionFilter) {\n          case 'collegati':\n            return collegamento === 3\n          case 'parziali':\n            return collegamento === 1 || collegamento === 2\n          case 'non_collegati':\n            return collegamento === 0\n          default:\n            return true\n        }\n      })\n    }\n\n    // Filtro certificazione\n    if (certificationFilter !== 'all') {\n      filtered = filtered.filter(cavo => {\n        const isCertified = cavo.certificato === true ||\n                           cavo.certificato === 'SI' ||\n                           cavo.certificato === 'CERTIFICATO'\n        return certificationFilter === 'certificati' ? isCertified : !isCertified\n      })\n    }\n\n    // Filtro sistema\n    if (systemFilter !== 'all') {\n      filtered = filtered.filter(cavo =>\n        normalizeString(cavo.sistema) === normalizeString(systemFilter)\n      )\n    }\n\n    // Filtro range metri\n    if (meterRangeMin || meterRangeMax) {\n      filtered = filtered.filter(cavo => {\n        const metri = cavo.metri_posati || cavo.metratura_reale || 0\n        const min = meterRangeMin ? parseFloat(meterRangeMin) : 0\n        const max = meterRangeMax ? parseFloat(meterRangeMax) : Infinity\n        return metri >= min && metri <= max\n      })\n    }\n\n    return filtered\n  }, [statusFilter, connectionFilter, certificationFilter, systemFilter, meterRangeMin, meterRangeMax, normalizeString])\n\n  // Apply filter\n  const applyFilter = useCallback(() => {\n    let filtered = cavi\n\n    // Prima applica la ricerca testuale\n    if (searchText.trim()) {\n      const searchTerms = searchText.split(',')\n        .map(term => term.trim())\n        .filter(term => term.length > 0)\n\n      if (searchType === 'equals') {\n        if (searchTerms.length === 1) {\n          filtered = filtered.filter(cavo => cavoMatchesTerm(cavo, searchTerms[0], true))\n        } else {\n          filtered = filtered.filter(cavo =>\n            searchTerms.every(term => cavoMatchesTerm(cavo, term, true))\n          )\n        }\n      } else {\n        filtered = filtered.filter(cavo =>\n          searchTerms.some(term => cavoMatchesTerm(cavo, term, false))\n        )\n      }\n    }\n\n    // Poi applica i filtri avanzati\n    filtered = applyAdvancedFilters(filtered)\n\n    onFilteredDataChange?.(filtered)\n  }, [searchText, searchType, cavi, cavoMatchesTerm, applyAdvancedFilters])\n\n  // Calcola valori unici per i filtri\n  const uniqueValues = useMemo(() => {\n    const systems = [...new Set(cavi.map(c => c.sistema).filter(Boolean))].sort()\n    return { systems }\n  }, [cavi])\n\n  // Funzioni per gestire i filtri\n  const clearAllFilters = () => {\n    setSearchText('')\n    setStatusFilter('all')\n    setConnectionFilter('all')\n    setCertificationFilter('all')\n    setSystemFilter('all')\n    setMeterRangeMin('')\n    setMeterRangeMax('')\n  }\n\n  const hasActiveFilters = searchText.trim() ||\n                          statusFilter !== 'all' ||\n                          connectionFilter !== 'all' ||\n                          certificationFilter !== 'all' ||\n                          systemFilter !== 'all' ||\n                          meterRangeMin ||\n                          meterRangeMax\n\n  // Apply filter when dependencies change\n  useEffect(() => {\n    applyFilter()\n  }, [searchText, searchType, cavi, statusFilter, connectionFilter, certificationFilter, systemFilter, meterRangeMin, meterRangeMax])\n\n  const handleSearchTextChange = (value: string) => {\n    setSearchText(value)\n  }\n\n  const clearFilter = () => {\n    setSearchText('')\n    setSearchType('contains')\n  }\n\n  return (\n    <Card className=\"mb-4 shadow-sm border-slate-200\">\n      <CardContent className=\"p-4\">\n        {/* Header con controlli principali */}\n        <div className=\"flex items-center gap-3 mb-4\">\n          {/* Search input principale */}\n          <div className=\"flex-1 relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400\" />\n            <Input\n              placeholder=\"Ricerca intelligente cavi (ID, sistema, metri, stato...)...\"\n              value={searchText}\n              onChange={(e) => handleSearchTextChange(e.target.value)}\n              disabled={loading}\n              className=\"pl-10 pr-12 h-10 border-slate-300 focus:border-blue-500 focus:ring-blue-500\"\n            />\n            {searchText && (\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-slate-100\"\n                onClick={clearFilter}\n              >\n                <X className=\"h-3 w-3\" />\n              </Button>\n            )}\n          </div>\n\n          {/* Search type selector */}\n          <TooltipProvider>\n            <Tooltip>\n              <TooltipTrigger asChild>\n                <div className=\"w-36\">\n                  <Select value={searchType} onValueChange={(value: 'contains' | 'equals') => setSearchType(value)}>\n                    <SelectTrigger className=\"h-10 border-slate-300\">\n                      <SelectValue />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"contains\">Contiene</SelectItem>\n                      <SelectItem value=\"equals\">Uguale a</SelectItem>\n                    </SelectContent>\n                  </Select>\n                </div>\n              </TooltipTrigger>\n              <TooltipContent>\n                <p>Modalità di ricerca: contiene o corrispondenza esatta</p>\n              </TooltipContent>\n            </Tooltip>\n          </TooltipProvider>\n\n          {/* Filtri avanzati toggle */}\n          <TooltipProvider>\n            <Tooltip>\n              <TooltipTrigger asChild>\n                <Button\n                  variant={isAdvancedOpen ? \"default\" : \"outline\"}\n                  size=\"sm\"\n                  onClick={() => setIsAdvancedOpen(!isAdvancedOpen)}\n                  className=\"flex items-center gap-2 transition-all duration-200 hover:scale-105\"\n                >\n                  <Filter className=\"h-4 w-4\" />\n                  Filtri\n                  {isAdvancedOpen ? <ChevronUp className=\"h-4 w-4\" /> : <ChevronDown className=\"h-4 w-4\" />}\n                  {hasActiveFilters && (\n                    <Badge variant=\"secondary\" className=\"ml-1 bg-blue-100 text-blue-800 text-xs\">\n                      Attivi\n                    </Badge>\n                  )}\n                </Button>\n              </TooltipTrigger>\n              <TooltipContent>\n                <p>Apri filtri avanzati per ricerca dettagliata</p>\n              </TooltipContent>\n            </Tooltip>\n          </TooltipProvider>\n\n          {/* Clear all filters - matching webapp vecchia style */}\n          {hasActiveFilters && (\n            <TooltipProvider>\n              <Tooltip>\n                <TooltipTrigger asChild>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={clearAllFilters}\n                    disabled={loading}\n                    className=\"flex items-center gap-2 transition-all duration-200\"\n                  >\n                    <X className=\"h-4 w-4\" />\n                    Pulisci\n                  </Button>\n                </TooltipTrigger>\n                <TooltipContent>\n                  <p>Rimuovi tutti i filtri attivi</p>\n                </TooltipContent>\n              </Tooltip>\n            </TooltipProvider>\n          )}\n\n          {/* Selection toggle button - matching webapp vecchia exactly */}\n          {onSelectionToggle && (\n            <TooltipProvider>\n              <Tooltip>\n                <TooltipTrigger asChild>\n                  <Button\n                    variant={selectionEnabled ? \"default\" : \"outline\"}\n                    size=\"sm\"\n                    onClick={onSelectionToggle}\n                    className=\"flex items-center gap-2 transition-all duration-200 hover:scale-105 font-normal hover:font-bold\"\n                  >\n                    {selectionEnabled ? <CheckSquare className=\"h-4 w-4\" /> : <Square className=\"h-4 w-4\" />}\n                    {selectionEnabled ? 'Disabilita Selezione' : 'Abilita Selezione'}\n                  </Button>\n                </TooltipTrigger>\n                <TooltipContent>\n                  <p>{selectionEnabled ? 'Disabilita la selezione multipla' : 'Abilita la selezione multipla'}</p>\n                </TooltipContent>\n              </Tooltip>\n            </TooltipProvider>\n          )}\n        </div>\n\n        {/* Filtri Avanzati - Sezione Espandibile */}\n        {isAdvancedOpen && (\n          <div className=\"mt-4 pt-4 border-t border-slate-200\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n\n              {/* Filtro Stato Installazione */}\n              <div className=\"space-y-2\">\n                <Label className=\"text-sm font-medium text-slate-700 flex items-center gap-2\">\n                  <CheckCircle className=\"w-4 h-4 text-green-600\" />\n                  Stato Installazione\n                </Label>\n                <Select value={statusFilter} onValueChange={setStatusFilter}>\n                  <SelectTrigger className=\"h-9 border-slate-300\">\n                    <SelectValue placeholder=\"Tutti gli stati\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"all\">Tutti gli stati</SelectItem>\n                    <SelectItem value=\"installati\">\n                      <div className=\"flex items-center gap-2\">\n                        <div className=\"w-2 h-2 bg-green-500 rounded-full\" />\n                        Installati\n                      </div>\n                    </SelectItem>\n                    <SelectItem value=\"in_corso\">\n                      <div className=\"flex items-center gap-2\">\n                        <div className=\"w-2 h-2 bg-yellow-500 rounded-full\" />\n                        In Corso\n                      </div>\n                    </SelectItem>\n                    <SelectItem value=\"da_installare\">\n                      <div className=\"flex items-center gap-2\">\n                        <div className=\"w-2 h-2 bg-gray-500 rounded-full\" />\n                        Da Installare\n                      </div>\n                    </SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n\n              {/* Filtro Collegamento */}\n              <div className=\"space-y-2\">\n                <Label className=\"text-sm font-medium text-slate-700 flex items-center gap-2\">\n                  <Zap className=\"w-4 h-4 text-blue-600\" />\n                  Collegamento\n                </Label>\n                <Select value={connectionFilter} onValueChange={setConnectionFilter}>\n                  <SelectTrigger className=\"h-9 border-slate-300\">\n                    <SelectValue placeholder=\"Tutti i collegamenti\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"all\">Tutti i collegamenti</SelectItem>\n                    <SelectItem value=\"collegati\">\n                      <div className=\"flex items-center gap-2\">\n                        <div className=\"w-2 h-2 bg-blue-500 rounded-full\" />\n                        Collegati (3/3)\n                      </div>\n                    </SelectItem>\n                    <SelectItem value=\"parziali\">\n                      <div className=\"flex items-center gap-2\">\n                        <div className=\"w-2 h-2 bg-yellow-500 rounded-full\" />\n                        Parziali (1-2/3)\n                      </div>\n                    </SelectItem>\n                    <SelectItem value=\"non_collegati\">\n                      <div className=\"flex items-center gap-2\">\n                        <div className=\"w-2 h-2 bg-gray-500 rounded-full\" />\n                        Non Collegati (0/3)\n                      </div>\n                    </SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n\n              {/* Filtro Certificazione */}\n              <div className=\"space-y-2\">\n                <Label className=\"text-sm font-medium text-slate-700 flex items-center gap-2\">\n                  <Package className=\"w-4 h-4 text-purple-600\" />\n                  Certificazione\n                </Label>\n                <Select value={certificationFilter} onValueChange={setCertificationFilter}>\n                  <SelectTrigger className=\"h-9 border-slate-300\">\n                    <SelectValue placeholder=\"Tutte le certificazioni\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"all\">Tutte le certificazioni</SelectItem>\n                    <SelectItem value=\"certificati\">\n                      <div className=\"flex items-center gap-2\">\n                        <div className=\"w-2 h-2 bg-purple-500 rounded-full\" />\n                        Certificati\n                      </div>\n                    </SelectItem>\n                    <SelectItem value=\"non_certificati\">\n                      <div className=\"flex items-center gap-2\">\n                        <div className=\"w-2 h-2 bg-gray-500 rounded-full\" />\n                        Non Certificati\n                      </div>\n                    </SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n\n              {/* Filtro Sistema */}\n              <div className=\"space-y-2\">\n                <Label className=\"text-sm font-medium text-slate-700 flex items-center gap-2\">\n                  <Settings className=\"w-4 h-4 text-indigo-600\" />\n                  Sistema\n                </Label>\n                <Select value={systemFilter} onValueChange={setSystemFilter}>\n                  <SelectTrigger className=\"h-9 border-slate-300\">\n                    <SelectValue placeholder=\"Tutti i sistemi\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"all\">Tutti i sistemi</SelectItem>\n                    {uniqueValues.systems.map(system => (\n                      <SelectItem key={system} value={system}>\n                        {system}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n            </div>\n\n            {/* Range Metri */}\n            <div className=\"mt-4 grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label className=\"text-sm font-medium text-slate-700\">Range Metri Installati</Label>\n                <div className=\"flex items-center gap-2\">\n                  <Input\n                    type=\"number\"\n                    placeholder=\"Min\"\n                    value={meterRangeMin}\n                    onChange={(e) => setMeterRangeMin(e.target.value)}\n                    className=\"h-9 border-slate-300\"\n                  />\n                  <span className=\"text-slate-500\">-</span>\n                  <Input\n                    type=\"number\"\n                    placeholder=\"Max\"\n                    value={meterRangeMax}\n                    onChange={(e) => setMeterRangeMax(e.target.value)}\n                    className=\"h-9 border-slate-300\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Filtri attivi summary */}\n            {hasActiveFilters && (\n              <div className=\"mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center gap-2\">\n                    <Filter className=\"w-4 h-4 text-blue-600\" />\n                    <span className=\"text-sm font-medium text-blue-800\">Filtri Attivi:</span>\n                  </div>\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={clearAllFilters}\n                    className=\"text-blue-600 hover:text-blue-800 hover:bg-blue-100\"\n                  >\n                    <X className=\"w-4 h-4 mr-1\" />\n                    Rimuovi Tutti\n                  </Button>\n                </div>\n                <div className=\"flex flex-wrap gap-2 mt-2\">\n                  {searchText && (\n                    <Badge variant=\"secondary\" className=\"bg-blue-100 text-blue-800\">\n                      Ricerca: \"{searchText}\"\n                    </Badge>\n                  )}\n                  {statusFilter !== 'all' && (\n                    <Badge variant=\"secondary\" className=\"bg-green-100 text-green-800\">\n                      Stato: {statusFilter}\n                    </Badge>\n                  )}\n                  {connectionFilter !== 'all' && (\n                    <Badge variant=\"secondary\" className=\"bg-blue-100 text-blue-800\">\n                      Collegamento: {connectionFilter}\n                    </Badge>\n                  )}\n                  {certificationFilter !== 'all' && (\n                    <Badge variant=\"secondary\" className=\"bg-purple-100 text-purple-800\">\n                      Certificazione: {certificationFilter}\n                    </Badge>\n                  )}\n                  {systemFilter !== 'all' && (\n                    <Badge variant=\"secondary\" className=\"bg-indigo-100 text-indigo-800\">\n                      Sistema: {systemFilter}\n                    </Badge>\n                  )}\n                  {(meterRangeMin || meterRangeMax) && (\n                    <Badge variant=\"secondary\" className=\"bg-orange-100 text-orange-800\">\n                      Metri: {meterRangeMin || '0'} - {meterRangeMax || '∞'}\n                    </Badge>\n                  )}\n                </div>\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Search help text */}\n        {searchText && !isAdvancedOpen && (\n          <div className=\"mt-3 p-2 bg-slate-50 rounded-lg border border-slate-200\">\n            <div className=\"text-xs text-slate-600 flex items-center gap-2\">\n              <span>💡</span>\n              <span>Suggerimenti: Usa virgole per ricerche multiple • Operatori: &gt;100, &lt;=50 per numeri</span>\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AA/BA;;;;;;;;;;;AA8Ce,SAAS,gBAAgB,EACtC,OAAO,EAAE,EACT,oBAAoB,EACpB,UAAU,KAAK,EACf,mBAAmB,KAAK,EACxB,iBAAiB,EACI;IACrB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IACpE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,kBAAkB;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACvE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3D,8BAA8B;IAC9B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,IAAI,CAAC,KAAK,OAAO;QACjB,OAAO,IAAI,QAAQ,GAAG,WAAW,GAAG,IAAI;IAC1C,GAAG,EAAE;IAEL,yCAAyC;IACzC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,MAAM,QAAQ,OAAO,KAAK,CAAC;QAC3B,IAAI,OAAO;YACT,OAAO;gBACL,QAAQ,KAAK,CAAC,EAAE;gBAChB,QAAQ,KAAK,CAAC,EAAE;gBAChB,QAAQ,KAAK,CAAC,EAAE,IAAI;YACtB;QACF;QACA,OAAO;YAAE,QAAQ;YAAI,QAAQ;YAAQ,QAAQ;QAAG;IAClD,GAAG,EAAE;IAEL,yCAAyC;IACzC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,MAAY,MAAc;QAC7D,MAAM,iBAAiB,gBAAgB;QAEvC,IAAI,CAAC,gBAAgB,OAAO;QAE5B,mBAAmB;QACnB,MAAM,SAAS,gBAAgB,KAAK,OAAO;QAC3C,MAAM,EAAE,QAAQ,UAAU,EAAE,QAAQ,UAAU,EAAE,QAAQ,UAAU,EAAE,GAAG,YAAY,KAAK,OAAO,IAAI;QAEnG,mBAAmB;QACnB,MAAM,YAAY,gBAAgB,KAAK,SAAS;QAChD,MAAM,aAAa,gBAAgB,KAAK,UAAU,IAAI,KAAK,OAAO;QAClE,MAAM,UAAU,gBAAgB,KAAK,OAAO;QAC5C,MAAM,UAAU,gBAAgB,KAAK,OAAO;QAE5C,YAAY;QACZ,MAAM,qBAAqB,gBAAgB,KAAK,EAAE,IAAI,KAAK,mBAAmB;QAC9E,MAAM,mBAAmB,gBAAgB,KAAK,CAAC,IAAI,KAAK,iBAAiB;QACzE,MAAM,iBAAiB,gBAAgB,KAAK,eAAe;QAC3D,MAAM,eAAe,gBAAgB,KAAK,aAAa;QAEvD,YAAY;QACZ,MAAM,SAAS,gBAAgB,KAAK,SAAS;QAC7C,MAAM,gBAAgB,KAAK,SAAS,KAAK,iBAAiB,iBACrC,KAAK,SAAS,KAAK,OAAO,KAC1B,gBAAgB,KAAK,SAAS;QAEnD,4BAA4B;QAC5B,MAAM,aAAa;YACjB;YAAQ;YAAY;YAAY;YAAY;YAAW;YAAY;YAAS;YAC5E;YAAoB;YAAkB;YAAgB;YACtD;YAAQ;SACT;QAED,kCAAkC;QAClC,MAAM,gBAAgB;YACpB;gBAAE,OAAO,KAAK,aAAa;gBAAE,MAAM;YAAgB;YACnD;gBAAE,OAAO,KAAK,eAAe,IAAI,KAAK,YAAY;gBAAE,MAAM;YAAkB;YAC5E;gBAAE,OAAO,WAAW;gBAAa,MAAM;YAAa;SACrD;QAED,iDAAiD;QACjD,MAAM,aAAa,eAAe,KAAK,CAAC;QACxC,IAAI,YAAY;YACd,MAAM,WAAW,UAAU,CAAC,EAAE;YAC9B,MAAM,QAAQ,WAAW,UAAU,CAAC,EAAE;YAEtC,OAAO,cAAc,IAAI,CAAC,CAAA;gBACxB,IAAI,MAAM,KAAK,IAAI,QAAQ,MAAM,MAAM,KAAK,GAAG,OAAO;gBAEtD,OAAQ;oBACN,KAAK;wBAAK,OAAO,MAAM,KAAK,GAAG;oBAC/B,KAAK;wBAAM,OAAO,MAAM,KAAK,IAAI;oBACjC,KAAK;wBAAK,OAAO,MAAM,KAAK,GAAG;oBAC/B,KAAK;wBAAM,OAAO,MAAM,KAAK,IAAI;oBACjC,KAAK;wBAAK,OAAO,MAAM,KAAK,KAAK;oBACjC;wBAAS,OAAO;gBAClB;YACF;QACF;QAEA,gCAAgC;QAChC,MAAM,cAAc,WAAW;QAC/B,IAAI,CAAC,MAAM,cAAc;YACvB,MAAM,eAAe,cAAc,IAAI,CAAC,CAAA,QACtC,MAAM,KAAK,IAAI,QAAQ,CAAC,MAAM,MAAM,KAAK,KAAK,MAAM,KAAK,KAAK;YAEhE,IAAI,cAAc,OAAO;QAC3B;QAEA,cAAc;QACd,IAAI,YAAY;YACd,OAAO,WAAW,IAAI,CAAC,CAAA,QAAS,UAAU;QAC5C,OAAO;YACL,OAAO,WAAW,IAAI,CAAC,CAAA,QAAS,MAAM,QAAQ,CAAC;QACjD;IACF,GAAG;QAAC;QAAiB;KAAY;IAEjC,yCAAyC;IACzC,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxC,IAAI,WAAW;QAEf,6BAA6B;QAC7B,IAAI,iBAAiB,OAAO;YAC1B,WAAW,SAAS,MAAM,CAAC,CAAA;gBACzB,OAAQ;oBACN,KAAK;wBACH,OAAO,KAAK,mBAAmB,KAAK,gBAC5B,KAAK,YAAY,IAAI,KAAK,YAAY,GAAG,KACzC,KAAK,eAAe,IAAI,KAAK,eAAe,GAAG;oBACzD,KAAK;wBACH,OAAO,KAAK,mBAAmB,KAAK;oBACtC,KAAK;wBACH,OAAO,KAAK,mBAAmB,KAAK,gBAC7B,KAAK,mBAAmB,KAAK,cAC7B,CAAC,CAAC,KAAK,YAAY,IAAI,KAAK,YAAY,GAAG,CAAC,KAC5C,CAAC,CAAC,KAAK,eAAe,IAAI,KAAK,eAAe,GAAG,CAAC;oBAC3D;wBACE,OAAO;gBACX;YACF;QACF;QAEA,sBAAsB;QACtB,IAAI,qBAAqB,OAAO;YAC9B,WAAW,SAAS,MAAM,CAAC,CAAA;gBACzB,MAAM,eAAe,KAAK,YAAY,IAAI,KAAK,YAAY,IAAI;gBAC/D,OAAQ;oBACN,KAAK;wBACH,OAAO,iBAAiB;oBAC1B,KAAK;wBACH,OAAO,iBAAiB,KAAK,iBAAiB;oBAChD,KAAK;wBACH,OAAO,iBAAiB;oBAC1B;wBACE,OAAO;gBACX;YACF;QACF;QAEA,wBAAwB;QACxB,IAAI,wBAAwB,OAAO;YACjC,WAAW,SAAS,MAAM,CAAC,CAAA;gBACzB,MAAM,cAAc,KAAK,WAAW,KAAK,QACtB,KAAK,WAAW,KAAK,QACrB,KAAK,WAAW,KAAK;gBACxC,OAAO,wBAAwB,gBAAgB,cAAc,CAAC;YAChE;QACF;QAEA,iBAAiB;QACjB,IAAI,iBAAiB,OAAO;YAC1B,WAAW,SAAS,MAAM,CAAC,CAAA,OACzB,gBAAgB,KAAK,OAAO,MAAM,gBAAgB;QAEtD;QAEA,qBAAqB;QACrB,IAAI,iBAAiB,eAAe;YAClC,WAAW,SAAS,MAAM,CAAC,CAAA;gBACzB,MAAM,QAAQ,KAAK,YAAY,IAAI,KAAK,eAAe,IAAI;gBAC3D,MAAM,MAAM,gBAAgB,WAAW,iBAAiB;gBACxD,MAAM,MAAM,gBAAgB,WAAW,iBAAiB;gBACxD,OAAO,SAAS,OAAO,SAAS;YAClC;QACF;QAEA,OAAO;IACT,GAAG;QAAC;QAAc;QAAkB;QAAqB;QAAc;QAAe;QAAe;KAAgB;IAErH,eAAe;IACf,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI,WAAW;QAEf,oCAAoC;QACpC,IAAI,WAAW,IAAI,IAAI;YACrB,MAAM,cAAc,WAAW,KAAK,CAAC,KAClC,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IACrB,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;YAEhC,IAAI,eAAe,UAAU;gBAC3B,IAAI,YAAY,MAAM,KAAK,GAAG;oBAC5B,WAAW,SAAS,MAAM,CAAC,CAAA,OAAQ,gBAAgB,MAAM,WAAW,CAAC,EAAE,EAAE;gBAC3E,OAAO;oBACL,WAAW,SAAS,MAAM,CAAC,CAAA,OACzB,YAAY,KAAK,CAAC,CAAA,OAAQ,gBAAgB,MAAM,MAAM;gBAE1D;YACF,OAAO;gBACL,WAAW,SAAS,MAAM,CAAC,CAAA,OACzB,YAAY,IAAI,CAAC,CAAA,OAAQ,gBAAgB,MAAM,MAAM;YAEzD;QACF;QAEA,gCAAgC;QAChC,WAAW,qBAAqB;QAEhC,uBAAuB;IACzB,GAAG;QAAC;QAAY;QAAY;QAAM;QAAiB;KAAqB;IAExE,oCAAoC;IACpC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC3B,MAAM,UAAU;eAAI,IAAI,IAAI,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,MAAM,CAAC;SAAU,CAAC,IAAI;QAC3E,OAAO;YAAE;QAAQ;IACnB,GAAG;QAAC;KAAK;IAET,gCAAgC;IAChC,MAAM,kBAAkB;QACtB,cAAc;QACd,gBAAgB;QAChB,oBAAoB;QACpB,uBAAuB;QACvB,gBAAgB;QAChB,iBAAiB;QACjB,iBAAiB;IACnB;IAEA,MAAM,mBAAmB,WAAW,IAAI,MAChB,iBAAiB,SACjB,qBAAqB,SACrB,wBAAwB,SACxB,iBAAiB,SACjB,iBACA;IAExB,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAY;QAAY;QAAM;QAAc;QAAkB;QAAqB;QAAc;QAAe;KAAc;IAElI,MAAM,yBAAyB,CAAC;QAC9B,cAAc;IAChB;IAEA,MAAM,cAAc;QAClB,cAAc;QACd,cAAc;IAChB;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;;8BAErB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC,iIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,uBAAuB,EAAE,MAAM,CAAC,KAAK;oCACtD,UAAU;oCACV,WAAU;;;;;;gCAEX,4BACC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;8CAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAMnB,8OAAC,mIAAA,CAAA,kBAAe;sCACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;kDACN,8OAAC,mIAAA,CAAA,iBAAc;wCAAC,OAAO;kDACrB,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAY,eAAe,CAAC,QAAiC,cAAc;;kEACxF,8OAAC,kIAAA,CAAA,gBAAa;wDAAC,WAAU;kEACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAW;;;;;;0EAC7B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAKnC,8OAAC,mIAAA,CAAA,iBAAc;kDACb,cAAA,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;sCAMT,8OAAC,mIAAA,CAAA,kBAAe;sCACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;kDACN,8OAAC,mIAAA,CAAA,iBAAc;wCAAC,OAAO;kDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,iBAAiB,YAAY;4CACtC,MAAK;4CACL,SAAS,IAAM,kBAAkB,CAAC;4CAClC,WAAU;;8DAEV,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAY;gDAE7B,+BAAiB,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;yEAAe,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDAC5E,kCACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAAyC;;;;;;;;;;;;;;;;;kDAMpF,8OAAC,mIAAA,CAAA,iBAAc;kDACb,cAAA,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;wBAMR,kCACC,8OAAC,mIAAA,CAAA,kBAAe;sCACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;kDACN,8OAAC,mIAAA,CAAA,iBAAc;wCAAC,OAAO;kDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,UAAU;4CACV,WAAU;;8DAEV,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;kDAI7B,8OAAC,mIAAA,CAAA,iBAAc;kDACb,cAAA,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;wBAOV,mCACC,8OAAC,mIAAA,CAAA,kBAAe;sCACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;kDACN,8OAAC,mIAAA,CAAA,iBAAc;wCAAC,OAAO;kDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,mBAAmB,YAAY;4CACxC,MAAK;4CACL,SAAS;4CACT,WAAU;;gDAET,iCAAmB,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;yEAAe,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAC3E,mBAAmB,yBAAyB;;;;;;;;;;;;kDAGjD,8OAAC,mIAAA,CAAA,iBAAc;kDACb,cAAA,8OAAC;sDAAG,mBAAmB,qCAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQrE,gCACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CAGb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDAA2B;;;;;;;sDAGpD,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAc,eAAe;;8DAC1C,8OAAC,kIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,kIAAA,CAAA,gBAAa;;sEACZ,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;sEACxB,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAChB,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;;;;;oEAAsC;;;;;;;;;;;;sEAIzD,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAChB,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;;;;;oEAAuC;;;;;;;;;;;;sEAI1D,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAChB,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;;;;;oEAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAS9D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAA0B;;;;;;;sDAG3C,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAkB,eAAe;;8DAC9C,8OAAC,kIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,kIAAA,CAAA,gBAAa;;sEACZ,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;sEACxB,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAChB,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;;;;;oEAAqC;;;;;;;;;;;;sEAIxD,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAChB,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;;;;;oEAAuC;;;;;;;;;;;;sEAI1D,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAChB,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;;;;;oEAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAS9D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,8OAAC,wMAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAA4B;;;;;;;sDAGjD,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAqB,eAAe;;8DACjD,8OAAC,kIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,kIAAA,CAAA,gBAAa;;sEACZ,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;sEACxB,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAChB,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;;;;;oEAAuC;;;;;;;;;;;;sEAI1D,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAChB,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;;;;;oEAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAS9D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAA4B;;;;;;;sDAGlD,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAc,eAAe;;8DAC1C,8OAAC,kIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,kIAAA,CAAA,gBAAa;;sEACZ,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;wDACvB,aAAa,OAAO,CAAC,GAAG,CAAC,CAAA,uBACxB,8OAAC,kIAAA,CAAA,aAAU;gEAAc,OAAO;0EAC7B;+DADc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAU3B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,WAAU;kDAAqC;;;;;;kDACtD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAChD,WAAU;;;;;;0DAEZ,8OAAC;gDAAK,WAAU;0DAAiB;;;;;;0DACjC,8OAAC,iIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAChD,WAAU;;;;;;;;;;;;;;;;;;;;;;;wBAOjB,kCACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAK,WAAU;8DAAoC;;;;;;;;;;;;sDAEtD,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;8CAIlC,8OAAC;oCAAI,WAAU;;wCACZ,4BACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;;gDAA4B;gDACpD;gDAAW;;;;;;;wCAGzB,iBAAiB,uBAChB,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;;gDAA8B;gDACzD;;;;;;;wCAGX,qBAAqB,uBACpB,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;;gDAA4B;gDAChD;;;;;;;wCAGlB,wBAAwB,uBACvB,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;;gDAAgC;gDAClD;;;;;;;wCAGpB,iBAAiB,uBAChB,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;;gDAAgC;gDACzD;;;;;;;wCAGb,CAAC,iBAAiB,aAAa,mBAC9B,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;;gDAAgC;gDAC3D,iBAAiB;gDAAI;gDAAI,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;gBAU/D,cAAc,CAAC,gCACd,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}, {"offset": {"line": 2972, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/common/TruncatedText.tsx"], "sourcesContent": ["import React, { useState } from 'react'\n\ninterface TruncatedTextProps {\n  text: string\n  maxLength?: number\n  className?: string\n}\n\nexport default function TruncatedText({\n  text,\n  maxLength = 20,\n  className = \"\"\n}: TruncatedTextProps) {\n  const [showTooltip, setShowTooltip] = useState(false)\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })\n\n  if (!text) return <span className=\"text-gray-400\">-</span>\n\n  const shouldTruncate = text.length > maxLength\n  const displayText = shouldTruncate ? `${text.substring(0, maxLength)}...` : text\n\n  if (!shouldTruncate) {\n    return <span className={className}>{text}</span>\n  }\n\n  const handleMouseEnter = (e: React.MouseEvent) => {\n    setMousePosition({ x: e.clientX, y: e.clientY })\n    setShowTooltip(true)\n  }\n\n  const handleMouseMove = (e: React.MouseEvent) => {\n    setMousePosition({ x: e.clientX, y: e.clientY })\n  }\n\n  return (\n    <div className=\"relative inline-block\">\n      <span\n        className={`cursor-help ${className}`}\n        style={{\n          textOverflow: 'ellipsis',\n          whiteSpace: 'nowrap',\n          overflow: 'hidden',\n          maxWidth: '100%',\n          display: 'inline-block'\n        }}\n        onMouseEnter={handleMouseEnter}\n        onMouseMove={handleMouseMove}\n        onMouseLeave={() => setShowTooltip(false)}\n        title={text} // Fallback browser tooltip\n      >\n        {displayText}\n      </span>\n\n      {/* Custom tooltip */}\n      {showTooltip && (\n        <div\n          className=\"fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg pointer-events-none\"\n          style={{\n            top: mousePosition.y - 40,\n            left: mousePosition.x - 150,\n            maxWidth: '300px',\n            wordWrap: 'break-word',\n            whiteSpace: 'normal'\n          }}\n        >\n          {text}\n          {/* Arrow */}\n          <div\n            className=\"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0\"\n            style={{\n              borderLeft: '5px solid transparent',\n              borderRight: '5px solid transparent',\n              borderTop: '5px solid #1f2937'\n            }}\n          />\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAQe,SAAS,cAAc,EACpC,IAAI,EACJ,YAAY,EAAE,EACd,YAAY,EAAE,EACK;IACnB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAEhE,IAAI,CAAC,MAAM,qBAAO,8OAAC;QAAK,WAAU;kBAAgB;;;;;;IAElD,MAAM,iBAAiB,KAAK,MAAM,GAAG;IACrC,MAAM,cAAc,iBAAiB,GAAG,KAAK,SAAS,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG;IAE5E,IAAI,CAAC,gBAAgB;QACnB,qBAAO,8OAAC;YAAK,WAAW;sBAAY;;;;;;IACtC;IAEA,MAAM,mBAAmB,CAAC;QACxB,iBAAiB;YAAE,GAAG,EAAE,OAAO;YAAE,GAAG,EAAE,OAAO;QAAC;QAC9C,eAAe;IACjB;IAEA,MAAM,kBAAkB,CAAC;QACvB,iBAAiB;YAAE,GAAG,EAAE,OAAO;YAAE,GAAG,EAAE,OAAO;QAAC;IAChD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,WAAW,CAAC,YAAY,EAAE,WAAW;gBACrC,OAAO;oBACL,cAAc;oBACd,YAAY;oBACZ,UAAU;oBACV,UAAU;oBACV,SAAS;gBACX;gBACA,cAAc;gBACd,aAAa;gBACb,cAAc,IAAM,eAAe;gBACnC,OAAO;0BAEN;;;;;;YAIF,6BACC,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,KAAK,cAAc,CAAC,GAAG;oBACvB,MAAM,cAAc,CAAC,GAAG;oBACxB,UAAU;oBACV,UAAU;oBACV,YAAY;gBACd;;oBAEC;kCAED,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,YAAY;4BACZ,aAAa;4BACb,WAAW;wBACb;;;;;;;;;;;;;;;;;;AAMZ", "debugId": null}}, {"offset": {"line": 3082, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3256, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3284, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\n\nimport { cn } from \"@/lib/utils\"\nimport { buttonVariants } from \"@/components/ui/button\"\n\nconst AlertDialog = AlertDialogPrimitive.Root\n\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger\n\nconst AlertDialogPortal = AlertDialogPrimitive.Portal\n\nconst AlertDialogOverlay = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName\n\nconst AlertDialogContent = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPortal>\n    <AlertDialogOverlay />\n    <AlertDialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    />\n  </AlertDialogPortal>\n))\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName\n\nconst AlertDialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogHeader.displayName = \"AlertDialogHeader\"\n\nconst AlertDialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogFooter.displayName = \"AlertDialogFooter\"\n\nconst AlertDialogTitle = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold\", className)}\n    {...props}\n  />\n))\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName\n\nconst AlertDialogDescription = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nAlertDialogDescription.displayName =\n  AlertDialogPrimitive.Description.displayName\n\nconst AlertDialogAction = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Action\n    ref={ref}\n    className={cn(buttonVariants(), className)}\n    {...props}\n  />\n))\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName\n\nconst AlertDialogCancel = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Cancel\n    ref={ref}\n    className={cn(\n      buttonVariants({ variant: \"outline\" }),\n      \"mt-2 sm:mt-0\",\n      className\n    )}\n    {...props}\n  />\n))\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName\n\nexport {\n  AlertDialog,\n  AlertDialogPortal,\n  AlertDialogOverlay,\n  AlertDialogTrigger,\n  AlertDialogContent,\n  AlertDialogHeader,\n  AlertDialogFooter,\n  AlertDialogTitle,\n  AlertDialogDescription,\n  AlertDialogAction,\n  AlertDialogCancel,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAQA,MAAM,cAAc,2KAAA,CAAA,OAAyB;AAE7C,MAAM,qBAAqB,2KAAA,CAAA,UAA4B;AAEvD,MAAM,oBAAoB,2KAAA,CAAA,SAA2B;AAErD,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,UAA4B;QAC3B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;AAGT,mBAAmB,WAAW,GAAG,2KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,2KAAA,CAAA,UAA4B;gBAC3B,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIf,mBAAmB,WAAW,GAAG,2KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,QAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,2KAAA,CAAA,QAA0B,CAAC,WAAW;AAErE,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,cAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAChC,2KAAA,CAAA,cAAgC,CAAC,WAAW;AAE9C,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,2KAAA,CAAA,SAA2B,CAAC,WAAW;AAEvE,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IACpC,gBACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,2KAAA,CAAA,SAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3411, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/ui/confirmation-dialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  AlertDialog,\n  AlertDialogAction,\n  AlertDialogCancel,\n  AlertDialogContent,\n  AlertDialogDescription,\n  AlertDialogFooter,\n  AlertDialogHeader,\n  AlertDialogTitle,\n} from '@/components/ui/alert-dialog'\nimport { Button } from '@/components/ui/button'\nimport { Loader2, AlertTriangle, CheckCircle, XCircle, Info } from 'lucide-react'\n\nexport type ConfirmationVariant = 'danger' | 'warning' | 'info' | 'success'\n\ninterface ConfirmationDialogProps {\n  isOpen: boolean\n  onClose: () => void\n  onConfirm: () => void | Promise<void>\n  title: string\n  description: string\n  confirmText?: string\n  cancelText?: string\n  variant?: ConfirmationVariant\n  isLoading?: boolean\n  icon?: React.ReactNode\n}\n\nconst variantConfig = {\n  danger: {\n    icon: XCircle,\n    iconColor: 'text-red-600',\n    buttonClass: 'bg-red-600 hover:bg-red-700 focus:ring-red-500',\n    borderClass: 'border-l-red-500'\n  },\n  warning: {\n    icon: AlertTriangle,\n    iconColor: 'text-orange-600',\n    buttonClass: 'bg-orange-600 hover:bg-orange-700 focus:ring-orange-500',\n    borderClass: 'border-l-orange-500'\n  },\n  info: {\n    icon: Info,\n    iconColor: 'text-blue-600',\n    buttonClass: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500',\n    borderClass: 'border-l-blue-500'\n  },\n  success: {\n    icon: CheckCircle,\n    iconColor: 'text-green-600',\n    buttonClass: 'bg-green-600 hover:bg-green-700 focus:ring-green-500',\n    borderClass: 'border-l-green-500'\n  }\n}\n\nexport default function ConfirmationDialog({\n  isOpen,\n  onClose,\n  onConfirm,\n  title,\n  description,\n  confirmText = 'Conferma',\n  cancelText = 'Annulla',\n  variant = 'info',\n  isLoading = false,\n  icon\n}: ConfirmationDialogProps) {\n  const [internalLoading, setInternalLoading] = useState(false)\n  const config = variantConfig[variant]\n  const IconComponent = icon || config.icon\n\n  // Gestione ESC key\n  useEffect(() => {\n    const handleEscape = (event: KeyboardEvent) => {\n      if (event.key === 'Escape' && isOpen && !isLoading && !internalLoading) {\n        onClose()\n      }\n    }\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape)\n      // Blocca lo scroll del body quando il modal è aperto\n      document.body.style.overflow = 'hidden'\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape)\n      document.body.style.overflow = 'unset'\n    }\n  }, [isOpen, isLoading, internalLoading, onClose])\n\n  const handleConfirm = async () => {\n    try {\n      setInternalLoading(true)\n      const result = onConfirm()\n      \n      // Se onConfirm restituisce una Promise, aspettiamo\n      if (result instanceof Promise) {\n        await result\n      }\n      \n      // Piccolo delay per mostrare il feedback\n      await new Promise(resolve => setTimeout(resolve, 300))\n    } catch (error) {\n      console.error('Errore durante conferma:', error)\n      // In caso di errore, non chiudiamo il dialog per permettere all'utente di riprovare\n      setInternalLoading(false)\n      return\n    } finally {\n      setInternalLoading(false)\n    }\n  }\n\n  const loading = isLoading || internalLoading\n\n  return (\n    <AlertDialog open={isOpen} onOpenChange={onClose}>\n      <AlertDialogContent className={`border-l-4 ${config.borderClass} max-w-md`}>\n        <AlertDialogHeader>\n          <AlertDialogTitle className=\"flex items-center space-x-3\">\n            <IconComponent className={`w-6 h-6 ${config.iconColor} flex-shrink-0`} />\n            <span className=\"text-lg font-semibold text-slate-900\">{title}</span>\n          </AlertDialogTitle>\n          <AlertDialogDescription className=\"text-base text-slate-600 leading-relaxed mt-2\">\n            {description}\n          </AlertDialogDescription>\n        </AlertDialogHeader>\n        \n        <AlertDialogFooter className=\"gap-3 mt-6\">\n          <AlertDialogCancel \n            onClick={onClose}\n            disabled={loading}\n            className=\"px-6 py-2 border-slate-300 text-slate-700 hover:bg-slate-50 focus:ring-slate-500\"\n          >\n            {cancelText}\n          </AlertDialogCancel>\n          \n          <AlertDialogAction\n            onClick={handleConfirm}\n            disabled={loading}\n            className={`px-6 py-2 text-white font-medium transition-all duration-200 ${config.buttonClass} disabled:opacity-50 disabled:cursor-not-allowed`}\n          >\n            {loading ? (\n              <>\n                <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                Elaborazione...\n              </>\n            ) : (\n              confirmText\n            )}\n          </AlertDialogAction>\n        </AlertDialogFooter>\n      </AlertDialogContent>\n    </AlertDialog>\n  )\n}\n\n// Hook per gestire facilmente i dialog di conferma\nexport function useConfirmationDialog() {\n  const [dialogState, setDialogState] = useState<{\n    isOpen: boolean\n    title: string\n    description: string\n    confirmText?: string\n    cancelText?: string\n    variant?: ConfirmationVariant\n    onConfirm?: () => void | Promise<void>\n    icon?: React.ReactNode\n  }>({\n    isOpen: false,\n    title: '',\n    description: ''\n  })\n\n  const showConfirmation = (config: Omit<typeof dialogState, 'isOpen'>) => {\n    setDialogState({\n      ...config,\n      isOpen: true\n    })\n  }\n\n  const hideConfirmation = () => {\n    setDialogState(prev => ({ ...prev, isOpen: false }))\n  }\n\n  const handleConfirm = async () => {\n    if (dialogState.onConfirm) {\n      await dialogState.onConfirm()\n    }\n    hideConfirmation()\n  }\n\n  return {\n    dialogState,\n    showConfirmation,\n    hideConfirmation,\n    handleConfirm,\n    ConfirmationDialog: () => (\n      <ConfirmationDialog\n        isOpen={dialogState.isOpen}\n        onClose={hideConfirmation}\n        onConfirm={handleConfirm}\n        title={dialogState.title}\n        description={dialogState.description}\n        confirmText={dialogState.confirmText}\n        cancelText={dialogState.cancelText}\n        variant={dialogState.variant}\n        icon={dialogState.icon}\n      />\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAWA;AAAA;AAAA;AAAA;AAAA;AAdA;;;;;AA+BA,MAAM,gBAAgB;IACpB,QAAQ;QACN,MAAM,4MAAA,CAAA,UAAO;QACb,WAAW;QACX,aAAa;QACb,aAAa;IACf;IACA,SAAS;QACP,MAAM,wNAAA,CAAA,gBAAa;QACnB,WAAW;QACX,aAAa;QACb,aAAa;IACf;IACA,MAAM;QACJ,MAAM,kMAAA,CAAA,OAAI;QACV,WAAW;QACX,aAAa;QACb,aAAa;IACf;IACA,SAAS;QACP,MAAM,2NAAA,CAAA,cAAW;QACjB,WAAW;QACX,aAAa;QACb,aAAa;IACf;AACF;AAEe,SAAS,mBAAmB,EACzC,MAAM,EACN,OAAO,EACP,SAAS,EACT,KAAK,EACL,WAAW,EACX,cAAc,UAAU,EACxB,aAAa,SAAS,EACtB,UAAU,MAAM,EAChB,YAAY,KAAK,EACjB,IAAI,EACoB;IACxB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,SAAS,aAAa,CAAC,QAAQ;IACrC,MAAM,gBAAgB,QAAQ,OAAO,IAAI;IAEzC,mBAAmB;IACnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,CAAC;YACpB,IAAI,MAAM,GAAG,KAAK,YAAY,UAAU,CAAC,aAAa,CAAC,iBAAiB;gBACtE;YACF;QACF;QAEA,IAAI,QAAQ;YACV,SAAS,gBAAgB,CAAC,WAAW;YACrC,qDAAqD;YACrD,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;YACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;QAAQ;QAAW;QAAiB;KAAQ;IAEhD,MAAM,gBAAgB;QACpB,IAAI;YACF,mBAAmB;YACnB,MAAM,SAAS;YAEf,mDAAmD;YACnD,IAAI,kBAAkB,SAAS;gBAC7B,MAAM;YACR;YAEA,yCAAyC;YACzC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,oFAAoF;YACpF,mBAAmB;YACnB;QACF,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,MAAM,UAAU,aAAa;IAE7B,qBACE,8OAAC,2IAAA,CAAA,cAAW;QAAC,MAAM;QAAQ,cAAc;kBACvC,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;YAAC,WAAW,CAAC,WAAW,EAAE,OAAO,WAAW,CAAC,SAAS,CAAC;;8BACxE,8OAAC,2IAAA,CAAA,oBAAiB;;sCAChB,8OAAC,2IAAA,CAAA,mBAAgB;4BAAC,WAAU;;8CAC1B,8OAAC;oCAAc,WAAW,CAAC,QAAQ,EAAE,OAAO,SAAS,CAAC,cAAc,CAAC;;;;;;8CACrE,8OAAC;oCAAK,WAAU;8CAAwC;;;;;;;;;;;;sCAE1D,8OAAC,2IAAA,CAAA,yBAAsB;4BAAC,WAAU;sCAC/B;;;;;;;;;;;;8BAIL,8OAAC,2IAAA,CAAA,oBAAiB;oBAAC,WAAU;;sCAC3B,8OAAC,2IAAA,CAAA,oBAAiB;4BAChB,SAAS;4BACT,UAAU;4BACV,WAAU;sCAET;;;;;;sCAGH,8OAAC,2IAAA,CAAA,oBAAiB;4BAChB,SAAS;4BACT,UAAU;4BACV,WAAW,CAAC,6DAA6D,EAAE,OAAO,WAAW,CAAC,gDAAgD,CAAC;sCAE9I,wBACC;;kDACE,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;+CAInD;;;;;;;;;;;;;;;;;;;;;;;AAOd;AAGO,SAAS;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAS1C;QACD,QAAQ;QACR,OAAO;QACP,aAAa;IACf;IAEA,MAAM,mBAAmB,CAAC;QACxB,eAAe;YACb,GAAG,MAAM;YACT,QAAQ;QACV;IACF;IAEA,MAAM,mBAAmB;QACvB,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,QAAQ;YAAM,CAAC;IACpD;IAEA,MAAM,gBAAgB;QACpB,IAAI,YAAY,SAAS,EAAE;YACzB,MAAM,YAAY,SAAS;QAC7B;QACA;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA,oBAAoB,kBAClB,8OAAC;gBACC,QAAQ,YAAY,MAAM;gBAC1B,SAAS;gBACT,WAAW;gBACX,OAAO,YAAY,KAAK;gBACxB,aAAa,YAAY,WAAW;gBACpC,aAAa,YAAY,WAAW;gBACpC,YAAY,YAAY,UAAU;gBAClC,SAAS,YAAY,OAAO;gBAC5B,MAAM,YAAY,IAAI;;;;;;IAG5B;AACF", "debugId": null}}, {"offset": {"line": 3650, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/cavi/CaviActionDialogs.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport {\n  <PERSON>alog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { \n  Loader2, \n  FileText, \n  Download, \n  Unplug, \n  AlertTriangle,\n  X,\n  Settings,\n  Mail\n} from 'lucide-react'\nimport { Cavo } from '@/types'\nimport ConfirmationDialog from '@/components/ui/confirmation-dialog'\n\ninterface DisconnectCableDialogProps {\n  isOpen: boolean\n  onClose: () => void\n  onConfirm: (cavoId: string) => Promise<void>\n  cavo: Cavo | null\n}\n\nexport function DisconnectCableDialog({\n  isOpen,\n  onClose,\n  onConfirm,\n  cavo\n}: DisconnectCableDialogProps) {\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleConfirm = async () => {\n    if (!cavo) return\n    \n    setIsLoading(true)\n    try {\n      await onConfirm(cavo.id_cavo)\n      onClose()\n    } catch (error) {\n      console.error('Errore durante scollegamento:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  if (!cavo) return null\n\n  return (\n    <ConfirmationDialog\n      isOpen={isOpen}\n      onClose={onClose}\n      onConfirm={handleConfirm}\n      title=\"Conferma Scollegamento Cavo\"\n      description={`Sei sicuro di voler scollegare il cavo ${cavo.id_cavo}? Questa azione potrebbe influenzare lo stato di altri componenti e dovrà essere ricollegato manualmente.`}\n      confirmText=\"Scollega\"\n      cancelText=\"Annulla\"\n      variant=\"warning\"\n      isLoading={isLoading}\n      icon={<Unplug className=\"w-6 h-6\" />}\n    />\n  )\n}\n\ninterface GeneratePDFDialogProps {\n  isOpen: boolean\n  onClose: () => void\n  onGenerate: (config: PDFGenerationConfig) => Promise<void>\n  cavo: Cavo | null\n}\n\nexport interface PDFGenerationConfig {\n  cavoId: string\n  fileName: string\n  format: 'standard' | 'detailed' | 'summary'\n  includeTestData: boolean\n  includePhotos: boolean\n  emailRecipient?: string\n  notes?: string\n}\n\nexport function GeneratePDFDialog({\n  isOpen,\n  onClose,\n  onGenerate,\n  cavo\n}: GeneratePDFDialogProps) {\n  const [isLoading, setIsLoading] = useState(false)\n  const [config, setConfig] = useState<PDFGenerationConfig>({\n    cavoId: '',\n    fileName: '',\n    format: 'standard',\n    includeTestData: true,\n    includePhotos: false,\n    emailRecipient: '',\n    notes: ''\n  })\n\n  // Aggiorna config quando cambia il cavo\n  useState(() => {\n    if (cavo) {\n      setConfig(prev => ({\n        ...prev,\n        cavoId: cavo.id_cavo,\n        fileName: `Certificato_${cavo.id_cavo}_${new Date().toISOString().split('T')[0]}.pdf`\n      }))\n    }\n  }, [cavo])\n\n  const handleGenerate = async () => {\n    if (!cavo) return\n    \n    setIsLoading(true)\n    try {\n      await onGenerate(config)\n      onClose()\n    } catch (error) {\n      console.error('Errore durante generazione PDF:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  if (!cavo) return null\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-2xl border-l-4 border-l-blue-500\">\n        <DialogHeader className=\"bg-gradient-to-r from-blue-50 to-transparent p-6 -m-6 mb-4\">\n          <DialogTitle className=\"flex items-center gap-3 text-xl\">\n            <FileText className=\"w-6 h-6 text-blue-600\" />\n            Genera Certificato per Cavo {cavo.id_cavo}\n          </DialogTitle>\n          <DialogDescription className=\"text-base text-slate-600 mt-2\">\n            Configura le opzioni per la generazione del certificato PDF\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-6\">\n          {/* Informazioni Cavo */}\n          <div className=\"bg-slate-50 p-4 rounded-lg border\">\n            <h4 className=\"font-medium text-slate-900 mb-2\">Informazioni Cavo</h4>\n            <div className=\"grid grid-cols-2 gap-4 text-sm\">\n              <div>\n                <span className=\"text-slate-600\">ID:</span>\n                <span className=\"ml-2 font-mono\">{cavo.id_cavo}</span>\n              </div>\n              <div>\n                <span className=\"text-slate-600\">Sistema:</span>\n                <span className=\"ml-2\">{cavo.sistema || 'N/A'}</span>\n              </div>\n              <div>\n                <span className=\"text-slate-600\">Tipologia:</span>\n                <span className=\"ml-2\">{cavo.tipologia || 'N/A'}</span>\n              </div>\n              <div>\n                <span className=\"text-slate-600\">Stato:</span>\n                <span className=\"ml-2\">{cavo.stato || 'N/A'}</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Configurazione PDF */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"fileName\">Nome File *</Label>\n              <Input\n                id=\"fileName\"\n                value={config.fileName}\n                onChange={(e) => setConfig(prev => ({ ...prev, fileName: e.target.value }))}\n                placeholder=\"Certificato_C001_2024-01-01.pdf\"\n                className=\"font-mono text-sm\"\n              />\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"format\">Formato Certificato</Label>\n              <Select \n                value={config.format} \n                onValueChange={(value: any) => setConfig(prev => ({ ...prev, format: value }))}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"standard\">📄 Standard CEI 64-8</SelectItem>\n                  <SelectItem value=\"detailed\">📋 Dettagliato con Misure</SelectItem>\n                  <SelectItem value=\"summary\">📝 Riassunto Esecutivo</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"emailRecipient\">Email Destinatario (Opzionale)</Label>\n              <div className=\"relative\">\n                <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400\" />\n                <Input\n                  id=\"emailRecipient\"\n                  type=\"email\"\n                  value={config.emailRecipient}\n                  onChange={(e) => setConfig(prev => ({ ...prev, emailRecipient: e.target.value }))}\n                  placeholder=\"<EMAIL>\"\n                  className=\"pl-10\"\n                />\n              </div>\n            </div>\n\n            <div className=\"space-y-3\">\n              <Label>Opzioni Aggiuntive</Label>\n              <div className=\"space-y-2\">\n                <label className=\"flex items-center space-x-2 cursor-pointer\">\n                  <input\n                    type=\"checkbox\"\n                    checked={config.includeTestData}\n                    onChange={(e) => setConfig(prev => ({ ...prev, includeTestData: e.target.checked }))}\n                    className=\"rounded border-slate-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                  <span className=\"text-sm text-slate-700\">Includi Dati di Collaudo</span>\n                </label>\n                <label className=\"flex items-center space-x-2 cursor-pointer\">\n                  <input\n                    type=\"checkbox\"\n                    checked={config.includePhotos}\n                    onChange={(e) => setConfig(prev => ({ ...prev, includePhotos: e.target.checked }))}\n                    className=\"rounded border-slate-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                  <span className=\"text-sm text-slate-700\">Includi Foto Installazione</span>\n                </label>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"notes\">Note Aggiuntive</Label>\n            <Textarea\n              id=\"notes\"\n              value={config.notes}\n              onChange={(e) => setConfig(prev => ({ ...prev, notes: e.target.value }))}\n              placeholder=\"Note o commenti da includere nel certificato...\"\n              rows={3}\n              className=\"resize-none\"\n            />\n          </div>\n        </div>\n\n        <DialogFooter className=\"gap-3 pt-6 border-t\">\n          <Button\n            variant=\"outline\"\n            onClick={onClose}\n            disabled={isLoading}\n            className=\"px-6\"\n          >\n            <X className=\"w-4 h-4 mr-2\" />\n            Annulla\n          </Button>\n          \n          <Button\n            onClick={handleGenerate}\n            disabled={isLoading || !config.fileName.trim()}\n            className=\"px-6 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700\"\n          >\n            {isLoading ? (\n              <>\n                <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                Generazione...\n              </>\n            ) : (\n              <>\n                <Download className=\"w-4 h-4 mr-2\" />\n                Genera PDF\n              </>\n            )}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n\ninterface CertificationErrorDialogProps {\n  isOpen: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  missingRequirements: string[]\n}\n\nexport function CertificationErrorDialog({\n  isOpen,\n  onClose,\n  cavo,\n  missingRequirements\n}: CertificationErrorDialogProps) {\n  if (!cavo) return null\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-md border-l-4 border-l-red-500\">\n        <DialogHeader className=\"bg-gradient-to-r from-red-50 to-transparent p-6 -m-6 mb-4\">\n          <DialogTitle className=\"flex items-center gap-3 text-xl\">\n            <AlertTriangle className=\"w-6 h-6 text-red-600\" />\n            Impossibile Certificare Cavo\n          </DialogTitle>\n          <DialogDescription className=\"text-base text-slate-600 mt-2\">\n            Il cavo {cavo.id_cavo} non può essere certificato nel suo stato attuale\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          <Alert variant=\"destructive\" className=\"border-red-200 bg-red-50\">\n            <AlertTriangle className=\"h-4 w-4\" />\n            <AlertDescription className=\"font-medium\">\n              Requisiti mancanti per la certificazione:\n            </AlertDescription>\n          </Alert>\n\n          <ul className=\"space-y-2\">\n            {missingRequirements.map((requirement, index) => (\n              <li key={index} className=\"flex items-center gap-2 text-sm text-slate-700\">\n                <X className=\"w-4 h-4 text-red-500 flex-shrink-0\" />\n                {requirement}\n              </li>\n            ))}\n          </ul>\n\n          <div className=\"bg-blue-50 p-4 rounded-lg border border-blue-200\">\n            <h4 className=\"font-medium text-blue-900 mb-2\">💡 Suggerimento</h4>\n            <p className=\"text-sm text-blue-800\">\n              Completa tutti i requisiti sopra elencati prima di procedere con la certificazione. \n              Puoi utilizzare le azioni disponibili nella tabella per aggiornare lo stato del cavo.\n            </p>\n          </div>\n        </div>\n\n        <DialogFooter className=\"pt-6 border-t\">\n          <Button\n            onClick={onClose}\n            className=\"px-6 bg-slate-600 hover:bg-slate-700\"\n          >\n            <Settings className=\"w-4 h-4 mr-2\" />\n            Ho Capito\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AAOA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAlCA;;;;;;;;;;;;AA2CO,SAAS,sBAAsB,EACpC,MAAM,EACN,OAAO,EACP,SAAS,EACT,IAAI,EACuB;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,gBAAgB;QACpB,IAAI,CAAC,MAAM;QAEX,aAAa;QACb,IAAI;YACF,MAAM,UAAU,KAAK,OAAO;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC,kJAAA,CAAA,UAAkB;QACjB,QAAQ;QACR,SAAS;QACT,WAAW;QACX,OAAM;QACN,aAAa,CAAC,uCAAuC,EAAE,KAAK,OAAO,CAAC,yGAAyG,CAAC;QAC9K,aAAY;QACZ,YAAW;QACX,SAAQ;QACR,WAAW;QACX,oBAAM,8OAAC,sMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;;;;;;AAG9B;AAmBO,SAAS,kBAAkB,EAChC,MAAM,EACN,OAAO,EACP,UAAU,EACV,IAAI,EACmB;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;QACxD,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,iBAAiB;QACjB,eAAe;QACf,gBAAgB;QAChB,OAAO;IACT;IAEA,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACP,IAAI,MAAM;YACR,UAAU,CAAA,OAAQ,CAAC;oBACjB,GAAG,IAAI;oBACP,QAAQ,KAAK,OAAO;oBACpB,UAAU,CAAC,YAAY,EAAE,KAAK,OAAO,CAAC,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;gBACvF,CAAC;QACH;IACF,GAAG;QAAC;KAAK;IAET,MAAM,iBAAiB;QACrB,IAAI,CAAC,MAAM;QAEX,aAAa;QACb,IAAI;YACF,MAAM,WAAW;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAA0B;gCACjB,KAAK,OAAO;;;;;;;sCAE3C,8OAAC,kIAAA,CAAA,oBAAiB;4BAAC,WAAU;sCAAgC;;;;;;;;;;;;8BAK/D,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAkC;;;;;;8CAChD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAiB;;;;;;8DACjC,8OAAC;oDAAK,WAAU;8DAAkB,KAAK,OAAO;;;;;;;;;;;;sDAEhD,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAiB;;;;;;8DACjC,8OAAC;oDAAK,WAAU;8DAAQ,KAAK,OAAO,IAAI;;;;;;;;;;;;sDAE1C,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAiB;;;;;;8DACjC,8OAAC;oDAAK,WAAU;8DAAQ,KAAK,SAAS,IAAI;;;;;;;;;;;;sDAE5C,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAiB;;;;;;8DACjC,8OAAC;oDAAK,WAAU;8DAAQ,KAAK,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;sCAM5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,OAAO,QAAQ;4CACtB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CACzE,aAAY;4CACZ,WAAU;;;;;;;;;;;;8CAId,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAS;;;;;;sDACxB,8OAAC,kIAAA,CAAA,SAAM;4CACL,OAAO,OAAO,MAAM;4CACpB,eAAe,CAAC,QAAe,UAAU,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,QAAQ;oDAAM,CAAC;;8DAE5E,8OAAC,kIAAA,CAAA,gBAAa;8DACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;8DAEd,8OAAC,kIAAA,CAAA,gBAAa;;sEACZ,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAW;;;;;;sEAC7B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAW;;;;;;sEAC7B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAU;;;;;;;;;;;;;;;;;;;;;;;;8CAKlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAiB;;;;;;sDAChC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,OAAO,cAAc;oDAC5B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDAC/E,aAAY;oDACZ,WAAU;;;;;;;;;;;;;;;;;;8CAKhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;4DACC,MAAK;4DACL,SAAS,OAAO,eAAe;4DAC/B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,iBAAiB,EAAE,MAAM,CAAC,OAAO;oEAAC,CAAC;4DAClF,WAAU;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEAAyB;;;;;;;;;;;;8DAE3C,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;4DACC,MAAK;4DACL,SAAS,OAAO,aAAa;4DAC7B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,eAAe,EAAE,MAAM,CAAC,OAAO;oEAAC,CAAC;4DAChF,WAAU;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMjD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAQ;;;;;;8CACvB,8OAAC,oIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,OAAO,OAAO,KAAK;oCACnB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACtE,aAAY;oCACZ,MAAM;oCACN,WAAU;;;;;;;;;;;;;;;;;;8BAKhB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;4BACV,WAAU;;8CAEV,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAIhC,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,aAAa,CAAC,OAAO,QAAQ,CAAC,IAAI;4BAC5C,WAAU;sCAET,0BACC;;kDACE,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;6DAInD;;kDACE,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AASrD;AASO,SAAS,yBAAyB,EACvC,MAAM,EACN,OAAO,EACP,IAAI,EACJ,mBAAmB,EACW;IAC9B,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAyB;;;;;;;sCAGpD,8OAAC,kIAAA,CAAA,oBAAiB;4BAAC,WAAU;;gCAAgC;gCAClD,KAAK,OAAO;gCAAC;;;;;;;;;;;;;8BAI1B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAc,WAAU;;8CACrC,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC,iIAAA,CAAA,mBAAgB;oCAAC,WAAU;8CAAc;;;;;;;;;;;;sCAK5C,8OAAC;4BAAG,WAAU;sCACX,oBAAoB,GAAG,CAAC,CAAC,aAAa,sBACrC,8OAAC;oCAAe,WAAU;;sDACxB,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;wCACZ;;mCAFM;;;;;;;;;;sCAOb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;8BAOzC,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;8BACtB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,WAAU;;0CAEV,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 4454, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/cavi/CaviTable.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useMemo, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { getCavoColorClasses } from '@/utils/softColors'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { TableRow, TableCell } from '@/components/ui/table'\nimport { Cavo } from '@/types'\nimport FilterableTable, { ColumnDef } from '@/components/common/FilterableTable'\nimport SmartCaviFilter from './SmartCaviFilter'\nimport TruncatedText from '@/components/common/TruncatedText'\nimport {\n  MoreHorizontal,\n  Cable,\n  Settings,\n  Zap,\n  CheckCircle,\n  AlertCircle,\n  Clock,\n  Package,\n  Unplug,\n  FileText,\n  Download,\n  X,\n  ArrowUpDown,\n  ArrowUp,\n  ArrowDown,\n  Eye,\n  Edit,\n  Trash2,\n  ChevronLeft,\n  ChevronRight,\n  ChevronsLeft,\n  ChevronsRight,\n  <PERSON>,\n  Filter,\n  Info,\n  Link,\n  Unlink,\n  ChevronDown,\n  Plus\n} from 'lucide-react'\nimport {\n  DisconnectCableDialog,\n  GeneratePDFDialog,\n  CertificationErrorDialog,\n  PDFGenerationConfig\n} from './CaviActionDialogs'\nimport { useToastActions } from '@/components/ui/toast-notification'\nimport {\n  Tooltip,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from '@/components/ui/tooltip'\n\ninterface CaviTableProps {\n  cavi: Cavo[]\n  loading?: boolean\n  selectionEnabled?: boolean\n  selectedCavi?: string[]\n  onSelectionChange?: (selectedIds: string[]) => void\n  onStatusAction?: (cavo: Cavo, action: string) => void\n  onContextMenuAction?: (cavo: Cavo, action: string) => void\n  onDisconnectCable?: (cavoId: string) => Promise<void>\n  onGeneratePDF?: (config: PDFGenerationConfig) => Promise<void>\n  onCertifyCable?: (cavoId: string) => Promise<void>\n}\n\ntype SortField = 'id_cavo' | 'sistema' | 'metri_teorici' | 'metri_posati' | 'stato'\ntype SortDirection = 'asc' | 'desc'\n\ninterface DialogState {\n  disconnect: { isOpen: boolean; cavo: Cavo | null }\n  generatePDF: { isOpen: boolean; cavo: Cavo | null }\n  certificationError: { isOpen: boolean; cavo: Cavo | null; missingRequirements: string[] }\n}\n\nexport default function CaviTable({\n  cavi = [],\n  loading = false,\n  selectionEnabled = false,\n  selectedCavi = [],\n  onSelectionChange,\n  onStatusAction,\n  onContextMenuAction,\n  onDisconnectCable,\n  onGeneratePDF,\n  onCertifyCable\n}: CaviTableProps) {\n  const [smartFilteredCavi, setSmartFilteredCavi] = useState(cavi)\n  const [filteredCavi, setFilteredCavi] = useState(cavi)\n  const [internalSelectionEnabled, setInternalSelectionEnabled] = useState(selectionEnabled)\n  const [sortField, setSortField] = useState<SortField>('id_cavo')\n  const [sortDirection, setSortDirection] = useState<SortDirection>('asc')\n  const [dialogs, setDialogs] = useState<DialogState>({\n    disconnect: { isOpen: false, cavo: null },\n    generatePDF: { isOpen: false, cavo: null },\n    certificationError: { isOpen: false, cavo: null, missingRequirements: [] }\n  })\n\n  // Stati per la paginazione\n  const [currentPage, setCurrentPage] = useState(1)\n  const [itemsPerPage, setItemsPerPage] = useState(25)\n  const [showBulkActions, setShowBulkActions] = useState(false)\n\n  const toast = useToastActions()\n\n  // Funzioni per gestire i dialog\n  const openDialog = (type: keyof DialogState, cavo: Cavo, missingRequirements: string[] = []) => {\n    setDialogs(prev => ({\n      ...prev,\n      [type]: { isOpen: true, cavo, missingRequirements }\n    }))\n  }\n\n  const closeDialog = (type: keyof DialogState) => {\n    setDialogs(prev => ({\n      ...prev,\n      [type]: { isOpen: false, cavo: null, missingRequirements: [] }\n    }))\n  }\n\n  // Funzione per ordinamento\n  const handleSort = (field: SortField) => {\n    if (sortField === field) {\n      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc')\n    } else {\n      setSortField(field)\n      setSortDirection('asc')\n    }\n  }\n\n  // Ordinamento dei cavi\n  const sortedCavi = useMemo(() => {\n    const sorted = [...smartFilteredCavi].sort((a, b) => {\n      let aValue: any = a[sortField]\n      let bValue: any = b[sortField]\n\n      // Gestione valori numerici\n      if (sortField === 'metri_teorici' || sortField === 'metri_posati') {\n        aValue = parseFloat(aValue) || 0\n        bValue = parseFloat(bValue) || 0\n      }\n\n      // Gestione stringhe\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase()\n      }\n      if (typeof bValue === 'string') {\n        bValue = bValue.toLowerCase()\n      }\n\n      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1\n      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1\n      return 0\n    })\n    return sorted\n  }, [smartFilteredCavi, sortField, sortDirection])\n\n  // Aggiorna i cavi quando cambiano i cavi originali\n  useEffect(() => {\n    setSmartFilteredCavi(cavi)\n  }, [cavi])\n\n  // Calcolo paginazione\n  const paginatedData = useMemo(() => {\n    const startIndex = (currentPage - 1) * itemsPerPage\n    const endIndex = startIndex + itemsPerPage\n    const paginatedCavi = sortedCavi.slice(startIndex, endIndex)\n\n    const totalPages = Math.ceil(sortedCavi.length / itemsPerPage)\n    const hasNextPage = currentPage < totalPages\n    const hasPrevPage = currentPage > 1\n\n    return {\n      cavi: paginatedCavi,\n      totalItems: sortedCavi.length,\n      totalPages,\n      hasNextPage,\n      hasPrevPage,\n      startIndex: startIndex + 1,\n      endIndex: Math.min(endIndex, sortedCavi.length)\n    }\n  }, [sortedCavi, currentPage, itemsPerPage])\n\n  // Aggiorna filteredCavi quando cambiano i cavi paginati\n  useEffect(() => {\n    setFilteredCavi(paginatedData.cavi)\n  }, [paginatedData.cavi])\n\n  // Reset pagina quando cambiano i filtri\n  useEffect(() => {\n    setCurrentPage(1)\n  }, [smartFilteredCavi.length])\n\n  // Gestione filtri intelligenti\n  const handleSmartFilterChange = (filtered: Cavo[]) => {\n    setSmartFilteredCavi(filtered)\n  }\n\n  // Gestione filtri tabella\n  const handleTableFilterChange = (filtered: Cavo[]) => {\n    setFilteredCavi(filtered)\n  }\n\n  // Gestione azioni sui cavi\n  const handleDisconnectCable = async (cavoId: string) => {\n    try {\n      if (onDisconnectCable) {\n        await onDisconnectCable(cavoId)\n        toast.cavoDisconnected(cavoId)\n      }\n    } catch (error) {\n      toast.error('Errore Scollegamento', 'Impossibile scollegare il cavo. Riprova.')\n    }\n  }\n\n  const handleGeneratePDF = async (config: PDFGenerationConfig) => {\n    try {\n      if (onGeneratePDF) {\n        await onGeneratePDF(config)\n        toast.pdfGenerated(config.fileName, config.cavoId)\n      }\n    } catch (error) {\n      toast.error('Errore Generazione PDF', 'Impossibile generare il certificato. Riprova.')\n    }\n  }\n\n  const handleCertifyCable = async (cavo: Cavo) => {\n    // Verifica requisiti per certificazione\n    const missingRequirements: string[] = []\n\n    if (!cavo.metri_posati || parseFloat(cavo.metri_posati) === 0) {\n      missingRequirements.push('Metri posati non inseriti')\n    }\n\n    if (cavo.stato !== 'Collegato') {\n      missingRequirements.push('Cavo non collegato')\n    }\n\n    if (!cavo.data_installazione) {\n      missingRequirements.push('Data installazione mancante')\n    }\n\n    if (missingRequirements.length > 0) {\n      openDialog('certificationError', cavo, missingRequirements)\n      return\n    }\n\n    try {\n      if (onCertifyCable) {\n        toast.actionInProgress('Certificazione', cavo.id_cavo)\n        await onCertifyCable(cavo.id_cavo)\n        toast.success('Cavo Certificato', `Il cavo ${cavo.id_cavo} è stato certificato con successo.`)\n      }\n    } catch (error) {\n      toast.certificationError(cavo.id_cavo, 'Errore durante il processo di certificazione')\n    }\n  }\n\n  const handleSelectionToggle = () => {\n    setInternalSelectionEnabled(!internalSelectionEnabled)\n    setShowBulkActions(!internalSelectionEnabled && selectedCavi.length > 0)\n  }\n\n  // Funzioni per la paginazione\n  const goToPage = (page: number) => {\n    setCurrentPage(Math.max(1, Math.min(page, paginatedData.totalPages)))\n  }\n\n  const goToFirstPage = () => goToPage(1)\n  const goToLastPage = () => goToPage(paginatedData.totalPages)\n  const goToPrevPage = () => goToPage(currentPage - 1)\n  const goToNextPage = () => goToPage(currentPage + 1)\n\n  // Funzioni per selezione multipla\n  const selectAllVisible = () => {\n    const visibleIds = paginatedData.cavi.map(c => c.id_cavo)\n    const newSelection = [...new Set([...selectedCavi, ...visibleIds])]\n    onSelectionChange?.(newSelection)\n  }\n\n  const deselectAllVisible = () => {\n    const visibleIds = new Set(paginatedData.cavi.map(c => c.id_cavo))\n    const newSelection = selectedCavi.filter(id => !visibleIds.has(id))\n    onSelectionChange?.(newSelection)\n  }\n\n  const selectAll = () => {\n    const allIds = sortedCavi.map(c => c.id_cavo)\n    onSelectionChange?.(allIds)\n  }\n\n  const deselectAll = () => {\n    onSelectionChange?.([])\n  }\n\n  // Verifica se tutti i cavi visibili sono selezionati\n  const allVisibleSelected = paginatedData.cavi.length > 0 &&\n    paginatedData.cavi.every(cavo => selectedCavi.includes(cavo.id_cavo))\n\n  const someVisibleSelected = paginatedData.cavi.some(cavo => selectedCavi.includes(cavo.id_cavo))\n\n  // Componente per header ordinabile\n  const SortableHeader = ({ field, children, className = \"\" }: {\n    field: SortField;\n    children: React.ReactNode;\n    className?: string\n  }) => (\n    <TooltipProvider>\n      <Tooltip>\n        <TooltipTrigger asChild>\n          <button\n            onClick={() => handleSort(field)}\n            className={`flex items-center gap-2 font-medium text-left hover:text-blue-600 transition-colors ${className}`}\n          >\n            {children}\n            {sortField === field ? (\n              sortDirection === 'asc' ? (\n                <ArrowUp className=\"w-4 h-4 text-blue-600\" />\n              ) : (\n                <ArrowDown className=\"w-4 h-4 text-blue-600\" />\n              )\n            ) : (\n              <ArrowUpDown className=\"w-4 h-4 text-slate-400\" />\n            )}\n          </button>\n        </TooltipTrigger>\n        <TooltipContent>\n          <p>Clicca per ordinare per {children}</p>\n        </TooltipContent>\n      </Tooltip>\n    </TooltipProvider>\n  )\n\n  // Componente per pulsanti azione con tooltip\n  const ActionButton = ({\n    onClick,\n    icon: Icon,\n    tooltip,\n    variant = \"default\",\n    disabled = false,\n    className = \"\"\n  }: {\n    onClick: () => void;\n    icon: any;\n    tooltip: string;\n    variant?: \"default\" | \"danger\" | \"success\" | \"warning\";\n    disabled?: boolean;\n    className?: string;\n  }) => {\n    const variantClasses = {\n      default: \"bg-blue-600 hover:bg-blue-700 text-white\",\n      danger: \"bg-red-600 hover:bg-red-700 text-white\",\n      success: \"bg-green-600 hover:bg-green-700 text-white\",\n      warning: \"bg-orange-600 hover:bg-orange-700 text-white\"\n    }\n\n    return (\n      <TooltipProvider>\n        <Tooltip>\n          <TooltipTrigger asChild>\n            <Button\n              size=\"sm\"\n              onClick={onClick}\n              disabled={disabled}\n              className={`h-8 w-8 p-0 ${variantClasses[variant]} ${className} transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100`}\n            >\n              <Icon className=\"w-4 h-4\" />\n            </Button>\n          </TooltipTrigger>\n          <TooltipContent>\n            <p>{tooltip}</p>\n          </TooltipContent>\n        </Tooltip>\n      </TooltipProvider>\n    )\n  }\n\n  // Gestione selezione\n  const handleSelectAll = (checked: boolean) => {\n    if (onSelectionChange) {\n      onSelectionChange(checked ? filteredCavi.map(c => c.id_cavo) : [])\n    }\n  }\n\n  const handleSelectCavo = (cavoId: string, checked: boolean) => {\n    if (onSelectionChange) {\n      const newSelection = checked\n        ? [...selectedCavi, cavoId]\n        : selectedCavi.filter(id => id !== cavoId)\n      onSelectionChange(newSelection)\n    }\n  }\n\n  // Bulk action handlers\n  const handleBulkExport = async () => {\n    try {\n      const response = await fetch('/api/cavi/export', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          selectedIds: selectedCavi,\n          cantiereId: 1 // TODO: Get from context\n        })\n      })\n\n      if (response.ok) {\n        const blob = await response.blob()\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = `cavi_export_${new Date().toISOString().split('T')[0]}.csv`\n        document.body.appendChild(a)\n        a.click()\n        window.URL.revokeObjectURL(url)\n        document.body.removeChild(a)\n      } else {\n        const error = await response.json()\n        alert(`Errore durante l'esportazione: ${error.error}`)\n      }\n    } catch (error) {\n      alert('Errore durante l\\'esportazione')\n    }\n  }\n\n  const handleBulkStatusChange = async () => {\n    const newStatus = prompt('Inserisci il nuovo stato (Da installare, In corso, Installato):')\n    if (!newStatus) return\n\n    try {\n      const response = await fetch('/api/cavi/bulk-status', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          selectedIds: selectedCavi,\n          cantiereId: 1, // TODO: Get from context\n          newStatus\n        })\n      })\n\n      const result = await response.json()\n      if (result.success) {\n        alert(result.message)\n        // TODO: Refresh data\n      } else {\n        alert(`Errore: ${result.error}`)\n      }\n    } catch (error) {\n      alert('Errore durante il cambio stato')\n    }\n  }\n\n  const handleBulkAssignCommand = () => {\n    // TODO: Implementare modal per selezione comanda\n    alert(`Assegnazione comanda per ${selectedCavi.length} cavi`)\n  }\n\n  const handleBulkDelete = async () => {\n    if (!confirm(`Sei sicuro di voler eliminare ${selectedCavi.length} cavi?`)) {\n      return\n    }\n\n    try {\n      const response = await fetch('/api/cavi/bulk-delete', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          selectedIds: selectedCavi,\n          cantiereId: 1 // TODO: Get from context\n        })\n      })\n\n      const result = await response.json()\n      if (result.success) {\n        alert(result.message)\n        // TODO: Refresh data\n      } else {\n        alert(`Errore: ${result.error}`)\n      }\n    } catch (error) {\n      alert('Errore durante l\\'eliminazione')\n    }\n  }\n\n  // Define columns matching original webapp structure\n  const columns: ColumnDef[] = useMemo(() => {\n    const baseColumns: ColumnDef[] = [\n      {\n        field: 'id_cavo',\n        headerName: 'ID',\n        dataType: 'text',\n        width: 70,\n        align: 'left',\n        renderHeader: () => (\n          <SortableHeader field=\"id_cavo\">ID</SortableHeader>\n        ),\n        renderCell: (row: Cavo) => (\n          <span className=\"font-semibold text-mariner-900\">{row.id_cavo}</span>\n        )\n      },\n      {\n        field: 'sistema',\n        headerName: 'Sistema',\n        dataType: 'text',\n        width: 80,\n        renderHeader: () => (\n          <SortableHeader field=\"sistema\">Sistema</SortableHeader>\n        ),\n        renderCell: (row: Cavo) => (\n          <TruncatedText text={row.sistema || ''} maxLength={8} />\n        )\n      },\n      {\n        field: 'utility',\n        headerName: 'Utility',\n        dataType: 'text',\n        width: 80,\n        renderCell: (row: Cavo) => (\n          <TruncatedText text={row.utility || ''} maxLength={8} />\n        )\n      },\n      {\n        field: 'tipologia',\n        headerName: 'Tipologia',\n        dataType: 'text',\n        width: 100,\n        renderCell: (row: Cavo) => (\n          <TruncatedText text={row.tipologia || ''} maxLength={12} />\n        )\n      },\n      {\n        field: 'formazione',\n        headerName: 'Form.',\n        dataType: 'text',\n        align: 'left',\n        width: 60,\n        renderCell: (row: Cavo) => row.formazione || row.sezione\n      },\n      {\n        field: 'metri_teorici',\n        headerName: 'M.Teor.',\n        dataType: 'number',\n        align: 'left',\n        width: 70,\n        renderHeader: () => (\n          <SortableHeader field=\"metri_teorici\">M.Teor.</SortableHeader>\n        ),\n        renderCell: (row: Cavo) => row.metri_teorici ? row.metri_teorici.toFixed(1) : '0'\n      },\n      {\n        field: 'metri_posati',\n        headerName: 'M.Reali',\n        dataType: 'number',\n        align: 'left',\n        width: 70,\n        renderHeader: () => (\n          <SortableHeader field=\"metri_posati\">M.Reali</SortableHeader>\n        ),\n        renderCell: (row: Cavo) => {\n          const metri = row.metri_posati || row.metratura_reale || 0\n          return metri ? metri.toFixed(1) : '0'\n        }\n      },\n      {\n        field: 'ubicazione_partenza',\n        headerName: 'Da',\n        dataType: 'text',\n        width: 140,\n        renderCell: (row: Cavo) => (\n          <TruncatedText\n            text={row.da || row.ubicazione_partenza || ''}\n            maxLength={18}\n          />\n        )\n      },\n      {\n        field: 'ubicazione_arrivo',\n        headerName: 'A',\n        dataType: 'text',\n        width: 140,\n        renderCell: (row: Cavo) => (\n          <TruncatedText\n            text={row.a || row.ubicazione_arrivo || ''}\n            maxLength={18}\n          />\n        )\n      },\n      {\n        field: 'id_bobina',\n        headerName: 'Bobina',\n        dataType: 'text',\n        width: 100,\n        align: 'center',\n        renderCell: (row: Cavo) => getBobinaButton(row)\n      },\n      {\n        field: 'stato_installazione',\n        headerName: 'Stato',\n        dataType: 'text',\n        align: 'left',\n        width: 130,\n        disableFilter: true,\n        renderHeader: () => (\n          <SortableHeader field=\"stato\">Stato</SortableHeader>\n        ),\n        renderCell: (row: Cavo) => getStatusButton(row)\n      },\n      {\n        field: 'collegamenti',\n        headerName: 'Collegamenti',\n        dataType: 'text',\n        align: 'left',\n        width: 160,\n        disableFilter: true,\n        disableSort: true,\n        renderCell: (row: Cavo) => getConnectionButton(row)\n      },\n      {\n        field: 'certificato',\n        headerName: 'Certificato',\n        dataType: 'text',\n        align: 'left',\n        width: 170,\n        disableFilter: true,\n        disableSort: true,\n        renderCell: (row: Cavo) => getCertificationButton(row)\n      },\n\n    ]\n\n    // Add selection column if enabled\n    if (internalSelectionEnabled) {\n      baseColumns.unshift({\n        field: 'selection',\n        headerName: '',\n        disableFilter: true,\n        disableSort: true,\n        width: 50,\n        align: 'left',\n        renderHeader: () => (\n          <Checkbox\n            checked={selectedCavi.length === filteredCavi.length && filteredCavi.length > 0}\n            onCheckedChange={handleSelectAll}\n          />\n        ),\n        renderCell: (row: Cavo) => (\n          <Checkbox\n            checked={selectedCavi.includes(row.id_cavo)}\n            onCheckedChange={(checked) => handleSelectCavo(row.id_cavo, checked as boolean)}\n            onClick={(e) => e.stopPropagation()}\n          />\n        )\n      })\n    }\n\n    return baseColumns\n  }, [internalSelectionEnabled, selectedCavi, filteredCavi, handleSelectAll, handleSelectCavo])\n\n  // Custom row renderer for selection and context menu\n  const renderRow = (row: Cavo, index: number) => {\n    const isSelected = selectedCavi.includes(row.id_cavo)\n\n    return (\n      <TableRow\n        key={row.id_cavo}\n        className={`\n          ${isSelected ? 'bg-blue-50 border-blue-200' : 'bg-white'}\n          hover:bg-blue-50 hover:border-blue-200 hover:shadow-sm\n          cursor-pointer border-b border-gray-200\n          transition-all duration-200 ease-in-out\n          ${isSelected ? 'ring-1 ring-blue-300' : ''}\n        `}\n        onClick={() => internalSelectionEnabled && handleSelectCavo(row.id_cavo, !isSelected)}\n        onContextMenu={(e) => {\n          e.preventDefault()\n          onContextMenuAction?.(row, 'context_menu')\n        }}\n      >\n        {columns.map((column) => (\n          <TableCell\n            key={column.field}\n            className={`\n              py-2 px-2 text-sm text-left\n              ${isSelected ? 'text-blue-900' : 'text-gray-900'}\n              transition-colors duration-200\n            `}\n            style={{ width: column.width, ...column.cellStyle }}\n            onClick={(e) => {\n              // Prevent row click for action columns\n              if (['stato_installazione', 'collegamenti', 'certificato'].includes(column.field)) {\n                e.stopPropagation()\n              }\n            }}\n          >\n            {column.renderCell ? column.renderCell(row) : (row[column.field] || <span className=\"text-gray-400\">-</span>)}\n          </TableCell>\n        ))}\n      </TableRow>\n    )\n  }\n\n  // Funzione per la colonna Bobina (Indicativa - Azione Implicita)\n  const getBobinaButton = (cavo: Cavo) => {\n    const idBobina = cavo.id_bobina\n\n    if (!idBobina || idBobina === 'N/A') {\n      return <span className=\"text-gray-400\">-</span>\n    }\n\n    if (idBobina === 'BOBINA_VUOTA') {\n      return (\n        <Badge\n          variant=\"outline\"\n          className=\"text-xs px-2 py-0.5 text-gray-500 border-gray-300 bg-gray-50\"\n        >\n          Vuota\n        </Badge>\n      )\n    }\n\n    // Estrai il numero della bobina usando i pattern esistenti\n    let displayValue = idBobina\n    let match = idBobina.match(/_B(.+)$/)\n    if (match) {\n      displayValue = match[1]\n    } else {\n      match = idBobina.match(/_b(.+)$/)\n      if (match) {\n        displayValue = match[1]\n      } else {\n        match = idBobina.match(/c\\d+_[bB](\\d+)$/)\n        if (match) {\n          displayValue = match[1]\n        } else {\n          match = idBobina.match(/(\\d+)$/)\n          if (match) {\n            displayValue = match[1]\n          }\n        }\n      }\n    }\n\n    return (\n      <TooltipProvider>\n        <Tooltip>\n          <TooltipTrigger asChild>\n            <Badge\n              variant=\"outline\"\n              className=\"cursor-pointer hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 hover:shadow-sm transition-all duration-200 px-2 py-1 font-medium text-slate-700 border-slate-300 bg-white flex items-center gap-1\"\n              onClick={(e) => {\n                e.stopPropagation()\n                // Determina l'azione basata sullo stato del cavo\n                const metriInstallati = cavo.metri_posati || cavo.metratura_reale || 0\n                const isInstalled = metriInstallati > 0\n\n                if (isInstalled) {\n                  // Se il cavo è installato, permetti modifica bobina\n                  onStatusAction?.(cavo, 'modify_reel')\n                } else {\n                  // Se il cavo non è installato, permetti inserimento metri\n                  onStatusAction?.(cavo, 'insert_meters')\n                }\n              }}\n            >\n              <span>{displayValue}</span>\n              <ChevronDown className=\"h-3 w-3 opacity-60\" />\n            </Badge>\n          </TooltipTrigger>\n          <TooltipContent>\n            <p>\n              {(() => {\n                const metriInstallati = cavo.metri_posati || cavo.metratura_reale || 0\n                const isInstalled = metriInstallati > 0\n                return isInstalled\n                  ? \"Clicca per modificare bobina\"\n                  : \"Clicca per inserire metri posati\"\n              })()}\n            </p>\n          </TooltipContent>\n        </Tooltip>\n      </TooltipProvider>\n    )\n  }\n\n  // Funzioni di utilità per lo stato\n  const getStatusBadge = (cavo: Cavo) => {\n    // Verifica se il cavo è assegnato a una comanda\n    const comandaPosa = cavo.comanda_posa\n    const comandaPartenza = cavo.comanda_partenza\n    const comandaArrivo = cavo.comanda_arrivo\n    const comandaCertificazione = cavo.comanda_certificazione\n\n    // Trova la comanda attiva (priorità: posa > partenza > arrivo > certificazione)\n    const comandaAttiva = comandaPosa || comandaPartenza || comandaArrivo || comandaCertificazione\n\n    // Se c'è una comanda attiva e lo stato è \"In corso\", mostra il codice comanda\n    if (comandaAttiva && cavo.stato_installazione === 'In corso') {\n      const colorClasses = getCavoColorClasses('IN_CORSO')\n      return (\n        <Badge\n          className={`cursor-pointer ${colorClasses.badge} ${colorClasses.hover}`}\n          onClick={() => onStatusAction?.(cavo, 'view_command', comandaAttiva)}\n        >\n          {comandaAttiva}\n        </Badge>\n      )\n    }\n\n    // Logica normale per gli altri stati\n    const stato = cavo.stato_installazione || 'Da installare'\n    const colorClasses = getCavoColorClasses(stato)\n\n    return (\n      <Badge className={colorClasses.badge}>\n        {stato}\n      </Badge>\n    )\n  }\n\n  const getStatusButton = (cavo: Cavo) => {\n    // Verifica se il cavo è installato controllando metri_posati o metratura_reale\n    const metriInstallati = cavo.metri_posati || cavo.metratura_reale || 0\n    const isInstalled = metriInstallati > 0\n\n    // Verifica se il cavo è assegnato a una comanda attiva\n    const comandaPosa = cavo.comanda_posa\n    const comandaPartenza = cavo.comanda_partenza\n    const comandaArrivo = cavo.comanda_arrivo\n    const comandaCertificazione = cavo.comanda_certificazione\n    const comandaAttiva = comandaPosa || comandaPartenza || comandaArrivo || comandaCertificazione\n\n    // Se c'è una comanda attiva e lo stato è \"In corso\", mostra il codice comanda (questo rimane cliccabile)\n    if (comandaAttiva && cavo.stato_installazione === 'In corso') {\n      return (\n        <TooltipProvider>\n          <Tooltip>\n            <TooltipTrigger asChild>\n              <Badge\n                className=\"bg-blue-500 text-white cursor-pointer hover:bg-blue-600 transition-colors duration-200 px-3 py-1 font-medium\"\n                onClick={(e) => {\n                  e.stopPropagation()\n                  onStatusAction?.(cavo, 'view_command', comandaAttiva)\n                }}\n              >\n                {comandaAttiva}\n              </Badge>\n            </TooltipTrigger>\n            <TooltipContent>\n              <p>Visualizza dettagli comanda</p>\n            </TooltipContent>\n          </Tooltip>\n        </TooltipProvider>\n      )\n    }\n\n    // Determina lo stato del cavo - BADGE PURAMENTE INDICATIVI (non cliccabili)\n    const stato = cavo.stato_installazione || 'Da installare'\n\n    if (stato === 'Installato' || isInstalled) {\n      return (\n        <Badge className=\"bg-green-100 text-green-700 px-3 py-1 font-medium border border-green-200\">\n          Installato\n        </Badge>\n      )\n    } else if (stato === 'In corso') {\n      return (\n        <Badge className=\"bg-yellow-100 text-yellow-700 px-3 py-1 font-medium border border-yellow-200\">\n          In corso\n        </Badge>\n      )\n    } else {\n      return (\n        <Badge\n          variant=\"outline\"\n          className=\"text-gray-600 px-3 py-1 font-medium border-gray-300 bg-gray-50\"\n        >\n          Da installare\n        </Badge>\n      )\n    }\n  }\n\n  const getConnectionButton = (cavo: Cavo) => {\n    // Verifica se il cavo è installato o in corso di installazione\n    const metriInstallati = cavo.metri_posati || cavo.metratura_reale || 0\n    const statoInstallazione = cavo.stato_installazione || 'Da installare'\n    const isInstalled = metriInstallati > 0 || statoInstallazione === 'Installato' || statoInstallazione === 'In corso'\n    const collegamento = cavo.collegamento || cavo.collegamenti || 0\n\n    // \"Non disponibile\" - Design distinto per elementi non interattivi\n    if (!isInstalled) {\n      return (\n        <TooltipProvider>\n          <Tooltip>\n            <TooltipTrigger asChild>\n              <span className=\"text-gray-400 text-sm px-2 py-1 flex items-center gap-1\">\n                <Info className=\"h-3 w-3\" />\n                Non disponibile\n              </span>\n            </TooltipTrigger>\n            <TooltipContent>\n              <p>Il collegamento non può essere gestito perché il cavo non è installato</p>\n            </TooltipContent>\n          </Tooltip>\n        </TooltipProvider>\n      )\n    }\n\n    // Pulsanti di azione - Stile outline con hover states chiari\n    let label, icon, actionType, buttonClass\n\n    switch (collegamento) {\n      case 0:\n        label = \"Collega\"\n        icon = Link\n        actionType = \"connect_cable\"\n        buttonClass = \"border-blue-300 text-blue-700 hover:bg-blue-50 hover:border-blue-400\"\n        break\n      case 1:\n        label = \"Completa Arrivo\"\n        icon = Link\n        actionType = \"connect_arrival\"\n        buttonClass = \"border-yellow-300 text-yellow-700 hover:bg-yellow-50 hover:border-yellow-400\"\n        break\n      case 2:\n        label = \"Completa Partenza\"\n        icon = Link\n        actionType = \"connect_departure\"\n        buttonClass = \"border-yellow-300 text-yellow-700 hover:bg-yellow-50 hover:border-yellow-400\"\n        break\n      case 3:\n        label = \"Scollega\"\n        icon = Unlink\n        actionType = \"disconnect_cable\"\n        buttonClass = \"border-red-300 text-red-700 hover:bg-red-50 hover:border-red-400\"\n        break\n      default:\n        label = \"Gestisci\"\n        icon = Settings\n        actionType = \"manage_connections\"\n        buttonClass = \"border-blue-300 text-blue-700 hover:bg-blue-50 hover:border-blue-400\"\n        break\n    }\n\n    const IconComponent = icon\n\n    return (\n      <Button\n        variant=\"outline\"\n        size=\"sm\"\n        className={`h-7 px-2 text-xs font-medium transition-colors duration-200 ${buttonClass}`}\n        onClick={(e) => {\n          e.stopPropagation()\n          if (actionType === 'disconnect_cable') {\n            openDialog('disconnect', cavo)\n          } else {\n            onStatusAction?.(cavo, actionType)\n          }\n        }}\n      >\n        <IconComponent className=\"h-3 w-3 mr-1\" />\n        {label}\n      </Button>\n    )\n  }\n\n  const getCertificationButton = (cavo: Cavo) => {\n    // Verifica se il cavo è installato o in corso di installazione\n    const metriInstallati = cavo.metri_posati || cavo.metratura_reale || 0\n    const statoInstallazione = cavo.stato_installazione || 'Da installare'\n    const isInstalled = metriInstallati > 0 || statoInstallazione === 'Installato' || statoInstallazione === 'In corso'\n    const isCertified = cavo.certificato === true || cavo.certificato === 'SI' || cavo.certificato === 'CERTIFICATO'\n    const isRejected = cavo.certificato === false || cavo.certificato === 'NO' || cavo.certificato === 'RIFIUTATO'\n\n    // \"Non disponibile\" - Design distinto per elementi non interattivi\n    if (!isInstalled) {\n      return (\n        <TooltipProvider>\n          <Tooltip>\n            <TooltipTrigger asChild>\n              <span className=\"text-gray-400 text-sm px-2 py-1 flex items-center gap-1\">\n                <Info className=\"h-3 w-3\" />\n                Non disponibile\n              </span>\n            </TooltipTrigger>\n            <TooltipContent>\n              <p>La certificazione non può essere gestita perché il cavo non è installato</p>\n            </TooltipContent>\n          </Tooltip>\n        </TooltipProvider>\n      )\n    }\n\n    // Certificato - Badge di stato + pulsante download\n    if (isCertified) {\n      return (\n        <div className=\"flex items-center gap-2\">\n          <Badge className=\"bg-green-100 text-green-700 px-2 py-1 text-xs font-medium border border-green-200\">\n            <CheckCircle className=\"h-3 w-3 mr-1\" />\n            Certificato\n          </Badge>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            className=\"h-6 w-6 p-0 border-blue-300 text-blue-700 hover:bg-blue-50 hover:border-blue-400\"\n            onClick={(e) => {\n              e.stopPropagation()\n              openDialog('generatePDF', cavo)\n            }}\n          >\n            <Download className=\"h-3 w-3\" />\n          </Button>\n        </div>\n      )\n    }\n\n    // Non Certificato/Rifiutato - Badge di stato (non cliccabile)\n    if (isRejected) {\n      return (\n        <Badge className=\"bg-red-100 text-red-700 px-2 py-1 text-xs font-medium border border-red-200\">\n          <X className=\"h-3 w-3 mr-1\" />\n          Non Certificato\n        </Badge>\n      )\n    }\n\n    // Azione \"Certifica\" - Pulsante chiaro\n    return (\n      <Button\n        variant=\"outline\"\n        size=\"sm\"\n        className=\"h-7 px-2 text-xs font-medium border-purple-300 text-purple-700 hover:bg-purple-50 hover:border-purple-400 transition-colors duration-200\"\n        onClick={(e) => {\n          e.stopPropagation()\n          handleCertifyCable(cavo)\n        }}\n      >\n        <CheckCircle className=\"h-3 w-3 mr-1\" />\n        Certifica\n      </Button>\n    )\n  }\n\n  return (\n    <div className=\"relative\">\n      {/* Smart Filter */}\n      <SmartCaviFilter\n        cavi={cavi}\n        onFilteredDataChange={handleSmartFilterChange}\n        loading={loading}\n        selectionEnabled={internalSelectionEnabled}\n        onSelectionToggle={handleSelectionToggle}\n      />\n\n      {/* Controlli Tabella e Paginazione */}\n      <div className=\"mb-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 bg-slate-50 p-4 rounded-lg border border-slate-200\">\n        {/* Info e controlli selezione */}\n        <div className=\"flex items-center gap-4\">\n          <div className=\"text-sm text-slate-600\">\n            Mostrando <span className=\"font-semibold\">{paginatedData.startIndex}</span> - <span className=\"font-semibold\">{paginatedData.endIndex}</span> di <span className=\"font-semibold\">{paginatedData.totalItems}</span> cavi\n          </div>\n\n          {internalSelectionEnabled && (\n            <div className=\"flex items-center gap-2\">\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger asChild>\n                    <div\n                      onClick={allVisibleSelected ? deselectAllVisible : selectAllVisible}\n                      className=\"flex items-center gap-2 px-3 py-2 text-sm font-medium border border-slate-200 rounded-md hover:bg-slate-50 hover:border-slate-300 transition-all duration-200 cursor-pointer\"\n                    >\n                      <Checkbox\n                        checked={allVisibleSelected}\n                        ref={(el) => {\n                          if (el) el.indeterminate = someVisibleSelected && !allVisibleSelected\n                        }}\n                      />\n                      Pagina\n                    </div>\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>{allVisibleSelected ? 'Deseleziona tutti i cavi visibili' : 'Seleziona tutti i cavi visibili'}</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger asChild>\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={selectedCavi.length === sortedCavi.length ? deselectAll : selectAll}\n                      className=\"flex items-center gap-2\"\n                    >\n                      <Users className=\"w-4 h-4\" />\n                      Tutti ({sortedCavi.length})\n                    </Button>\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>{selectedCavi.length === sortedCavi.length ? 'Deseleziona tutti i cavi' : 'Seleziona tutti i cavi filtrati'}</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n\n              {selectedCavi.length > 0 && (\n                <Badge variant=\"secondary\" className=\"bg-blue-100 text-blue-800\">\n                  {selectedCavi.length} selezionati\n                </Badge>\n              )}\n            </div>\n          )}\n        </div>\n\n        {/* Controlli per pagina */}\n        <div className=\"flex items-center gap-2\">\n          <span className=\"text-sm text-slate-600\">Righe per pagina:</span>\n          <select\n            value={itemsPerPage}\n            onChange={(e) => {\n              setItemsPerPage(Number(e.target.value))\n              setCurrentPage(1)\n            }}\n            className=\"border border-slate-300 rounded px-2 py-1 text-sm\"\n          >\n            <option value={10}>10</option>\n            <option value={25}>25</option>\n            <option value={50}>50</option>\n            <option value={100}>100</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Filterable Table */}\n      <FilterableTable\n        data={smartFilteredCavi}\n        columns={columns}\n        loading={loading}\n        emptyMessage=\"Nessun cavo disponibile\"\n        onFilteredDataChange={handleTableFilterChange}\n        renderRow={renderRow}\n      />\n\n      {/* Controlli Paginazione */}\n      {paginatedData.totalPages > 1 && (\n        <div className=\"mt-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 bg-white p-4 rounded-lg border border-slate-200\">\n          <div className=\"text-sm text-slate-600\">\n            Pagina <span className=\"font-semibold\">{currentPage}</span> di <span className=\"font-semibold\">{paginatedData.totalPages}</span>\n          </div>\n\n          <div className=\"flex items-center gap-2\">\n            <TooltipProvider>\n              <Tooltip>\n                <TooltipTrigger asChild>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={goToFirstPage}\n                    disabled={!paginatedData.hasPrevPage}\n                    className=\"p-2\"\n                  >\n                    <ChevronsLeft className=\"w-4 h-4\" />\n                  </Button>\n                </TooltipTrigger>\n                <TooltipContent>\n                  <p>Prima pagina</p>\n                </TooltipContent>\n              </Tooltip>\n            </TooltipProvider>\n\n            <TooltipProvider>\n              <Tooltip>\n                <TooltipTrigger asChild>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={goToPrevPage}\n                    disabled={!paginatedData.hasPrevPage}\n                    className=\"p-2\"\n                  >\n                    <ChevronLeft className=\"w-4 h-4\" />\n                  </Button>\n                </TooltipTrigger>\n                <TooltipContent>\n                  <p>Pagina precedente</p>\n                </TooltipContent>\n              </Tooltip>\n            </TooltipProvider>\n\n            {/* Numeri di pagina */}\n            <div className=\"flex items-center gap-1\">\n              {Array.from({ length: Math.min(5, paginatedData.totalPages) }, (_, i) => {\n                let pageNum: number\n                if (paginatedData.totalPages <= 5) {\n                  pageNum = i + 1\n                } else if (currentPage <= 3) {\n                  pageNum = i + 1\n                } else if (currentPage >= paginatedData.totalPages - 2) {\n                  pageNum = paginatedData.totalPages - 4 + i\n                } else {\n                  pageNum = currentPage - 2 + i\n                }\n\n                return (\n                  <Button\n                    key={pageNum}\n                    variant={currentPage === pageNum ? \"default\" : \"outline\"}\n                    size=\"sm\"\n                    onClick={() => goToPage(pageNum)}\n                    className=\"w-8 h-8 p-0\"\n                  >\n                    {pageNum}\n                  </Button>\n                )\n              })}\n            </div>\n\n            <TooltipProvider>\n              <Tooltip>\n                <TooltipTrigger asChild>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={goToNextPage}\n                    disabled={!paginatedData.hasNextPage}\n                    className=\"p-2\"\n                  >\n                    <ChevronRight className=\"w-4 h-4\" />\n                  </Button>\n                </TooltipTrigger>\n                <TooltipContent>\n                  <p>Pagina successiva</p>\n                </TooltipContent>\n              </Tooltip>\n            </TooltipProvider>\n\n            <TooltipProvider>\n              <Tooltip>\n                <TooltipTrigger asChild>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={goToLastPage}\n                    disabled={!paginatedData.hasNextPage}\n                    className=\"p-2\"\n                  >\n                    <ChevronsRight className=\"w-4 h-4\" />\n                  </Button>\n                </TooltipTrigger>\n                <TooltipContent>\n                  <p>Ultima pagina</p>\n                </TooltipContent>\n              </Tooltip>\n            </TooltipProvider>\n          </div>\n        </div>\n      )}\n\n      {/* Barra Azioni Bulk - appare solo quando ci sono elementi selezionati */}\n      {internalSelectionEnabled && selectedCavi.length > 0 && (\n        <div className=\"fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-white border border-slate-300 rounded-lg shadow-xl z-50 p-4 min-w-[600px]\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"flex items-center gap-2\">\n                <div className=\"w-3 h-3 bg-blue-500 rounded-full animate-pulse\"></div>\n                <Badge variant=\"secondary\" className=\"bg-blue-100 text-blue-800 font-semibold\">\n                  {selectedCavi.length} cavi selezionati\n                </Badge>\n              </div>\n\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger asChild>\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={deselectAll}\n                      className=\"text-slate-600 hover:text-slate-800\"\n                    >\n                      <X className=\"w-4 h-4 mr-1\" />\n                      Deseleziona tutto\n                    </Button>\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>Rimuovi la selezione da tutti i cavi</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n            </div>\n\n            <div className=\"flex items-center gap-2\">\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger asChild>\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => {\n                        // Logica per esportazione bulk\n                        toast.actionInProgress('Esportazione', `${selectedCavi.length} cavi`)\n                      }}\n                      className=\"flex items-center gap-2 hover:bg-green-50 hover:border-green-300\"\n                    >\n                      <Download className=\"w-4 h-4\" />\n                      Esporta\n                    </Button>\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>Esporta i cavi selezionati in Excel</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger asChild>\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => {\n                        // Logica per generazione PDF bulk\n                        toast.actionInProgress('Generazione PDF', `${selectedCavi.length} cavi`)\n                      }}\n                      className=\"flex items-center gap-2 hover:bg-blue-50 hover:border-blue-300\"\n                    >\n                      <FileText className=\"w-4 h-4\" />\n                      PDF Bulk\n                    </Button>\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>Genera PDF per tutti i cavi selezionati</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger asChild>\n                    <Button\n                      size=\"sm\"\n                      onClick={() => {\n                        // Logica per cambio stato bulk\n                        toast.actionInProgress('Aggiornamento Stato', `${selectedCavi.length} cavi`)\n                      }}\n                      className=\"flex items-center gap-2 hover:bg-yellow-50 hover:border-yellow-300\"\n                    >\n                      <Settings className=\"w-4 h-4\" />\n                      Stato\n                    </Button>\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>Cambia stato per tutti i cavi selezionati</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger asChild>\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => {\n                        // Logica per assegnazione comanda bulk\n                        toast.actionInProgress('Assegnazione Comanda', `${selectedCavi.length} cavi`)\n                      }}\n                      className=\"flex items-center gap-2 hover:bg-purple-50 hover:border-purple-300\"\n                    >\n                      <Package className=\"w-4 h-4\" />\n                      Comanda\n                    </Button>\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>Assegna comanda a tutti i cavi selezionati</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n\n              <div className=\"w-px h-6 bg-slate-300\"></div>\n\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger asChild>\n                    <Button\n                      variant=\"destructive\"\n                      size=\"sm\"\n                      onClick={() => {\n                        // Logica per eliminazione bulk con conferma\n                        if (confirm(`Sei sicuro di voler eliminare ${selectedCavi.length} cavi selezionati?`)) {\n                          toast.actionInProgress('Eliminazione', `${selectedCavi.length} cavi`)\n                        }\n                      }}\n                      className=\"flex items-center gap-2\"\n                    >\n                      <Trash2 className=\"w-4 h-4\" />\n                      Elimina\n                    </Button>\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>Elimina tutti i cavi selezionati (azione irreversibile)</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Dialog Components */}\n      <DisconnectCableDialog\n        isOpen={dialogs.disconnect.isOpen}\n        onClose={() => closeDialog('disconnect')}\n        onConfirm={handleDisconnectCable}\n        cavo={dialogs.disconnect.cavo}\n      />\n\n      <GeneratePDFDialog\n        isOpen={dialogs.generatePDF.isOpen}\n        onClose={() => closeDialog('generatePDF')}\n        onGenerate={handleGeneratePDF}\n        cavo={dialogs.generatePDF.cavo}\n      />\n\n      <CertificationErrorDialog\n        isOpen={dialogs.certificationError.isOpen}\n        onClose={() => closeDialog('certificationError')}\n        cavo={dialogs.certificationError.cavo}\n        missingRequirements={dialogs.certificationError.missingRequirements}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA+BA;AAMA;AACA;AAnDA;;;;;;;;;;;;;;;AAgFe,SAAS,UAAU,EAChC,OAAO,EAAE,EACT,UAAU,KAAK,EACf,mBAAmB,KAAK,EACxB,eAAe,EAAE,EACjB,iBAAiB,EACjB,cAAc,EACd,mBAAmB,EACnB,iBAAiB,EACjB,aAAa,EACb,cAAc,EACC;IACf,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;IACtD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QAClD,YAAY;YAAE,QAAQ;YAAO,MAAM;QAAK;QACxC,aAAa;YAAE,QAAQ;YAAO,MAAM;QAAK;QACzC,oBAAoB;YAAE,QAAQ;YAAO,MAAM;YAAM,qBAAqB,EAAE;QAAC;IAC3E;IAEA,2BAA2B;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,QAAQ,CAAA,GAAA,iJAAA,CAAA,kBAAe,AAAD;IAE5B,gCAAgC;IAChC,MAAM,aAAa,CAAC,MAAyB,MAAY,sBAAgC,EAAE;QACzF,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;oBAAE,QAAQ;oBAAM;oBAAM;gBAAoB;YACpD,CAAC;IACH;IAEA,MAAM,cAAc,CAAC;QACnB,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;oBAAE,QAAQ;oBAAO,MAAM;oBAAM,qBAAqB,EAAE;gBAAC;YAC/D,CAAC;IACH;IAEA,2BAA2B;IAC3B,MAAM,aAAa,CAAC;QAClB,IAAI,cAAc,OAAO;YACvB,iBAAiB,CAAA,OAAQ,SAAS,QAAQ,SAAS;QACrD,OAAO;YACL,aAAa;YACb,iBAAiB;QACnB;IACF;IAEA,uBAAuB;IACvB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACzB,MAAM,SAAS;eAAI;SAAkB,CAAC,IAAI,CAAC,CAAC,GAAG;YAC7C,IAAI,SAAc,CAAC,CAAC,UAAU;YAC9B,IAAI,SAAc,CAAC,CAAC,UAAU;YAE9B,2BAA2B;YAC3B,IAAI,cAAc,mBAAmB,cAAc,gBAAgB;gBACjE,SAAS,WAAW,WAAW;gBAC/B,SAAS,WAAW,WAAW;YACjC;YAEA,oBAAoB;YACpB,IAAI,OAAO,WAAW,UAAU;gBAC9B,SAAS,OAAO,WAAW;YAC7B;YACA,IAAI,OAAO,WAAW,UAAU;gBAC9B,SAAS,OAAO,WAAW;YAC7B;YAEA,IAAI,SAAS,QAAQ,OAAO,kBAAkB,QAAQ,CAAC,IAAI;YAC3D,IAAI,SAAS,QAAQ,OAAO,kBAAkB,QAAQ,IAAI,CAAC;YAC3D,OAAO;QACT;QACA,OAAO;IACT,GAAG;QAAC;QAAmB;QAAW;KAAc;IAEhD,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qBAAqB;IACvB,GAAG;QAAC;KAAK;IAET,sBAAsB;IACtB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,MAAM,aAAa,CAAC,cAAc,CAAC,IAAI;QACvC,MAAM,WAAW,aAAa;QAC9B,MAAM,gBAAgB,WAAW,KAAK,CAAC,YAAY;QAEnD,MAAM,aAAa,KAAK,IAAI,CAAC,WAAW,MAAM,GAAG;QACjD,MAAM,cAAc,cAAc;QAClC,MAAM,cAAc,cAAc;QAElC,OAAO;YACL,MAAM;YACN,YAAY,WAAW,MAAM;YAC7B;YACA;YACA;YACA,YAAY,aAAa;YACzB,UAAU,KAAK,GAAG,CAAC,UAAU,WAAW,MAAM;QAChD;IACF,GAAG;QAAC;QAAY;QAAa;KAAa;IAE1C,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gBAAgB,cAAc,IAAI;IACpC,GAAG;QAAC,cAAc,IAAI;KAAC;IAEvB,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe;IACjB,GAAG;QAAC,kBAAkB,MAAM;KAAC;IAE7B,+BAA+B;IAC/B,MAAM,0BAA0B,CAAC;QAC/B,qBAAqB;IACvB;IAEA,0BAA0B;IAC1B,MAAM,0BAA0B,CAAC;QAC/B,gBAAgB;IAClB;IAEA,2BAA2B;IAC3B,MAAM,wBAAwB,OAAO;QACnC,IAAI;YACF,IAAI,mBAAmB;gBACrB,MAAM,kBAAkB;gBACxB,MAAM,gBAAgB,CAAC;YACzB;QACF,EAAE,OAAO,OAAO;YACd,MAAM,KAAK,CAAC,wBAAwB;QACtC;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,IAAI,eAAe;gBACjB,MAAM,cAAc;gBACpB,MAAM,YAAY,CAAC,OAAO,QAAQ,EAAE,OAAO,MAAM;YACnD;QACF,EAAE,OAAO,OAAO;YACd,MAAM,KAAK,CAAC,0BAA0B;QACxC;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,wCAAwC;QACxC,MAAM,sBAAgC,EAAE;QAExC,IAAI,CAAC,KAAK,YAAY,IAAI,WAAW,KAAK,YAAY,MAAM,GAAG;YAC7D,oBAAoB,IAAI,CAAC;QAC3B;QAEA,IAAI,KAAK,KAAK,KAAK,aAAa;YAC9B,oBAAoB,IAAI,CAAC;QAC3B;QAEA,IAAI,CAAC,KAAK,kBAAkB,EAAE;YAC5B,oBAAoB,IAAI,CAAC;QAC3B;QAEA,IAAI,oBAAoB,MAAM,GAAG,GAAG;YAClC,WAAW,sBAAsB,MAAM;YACvC;QACF;QAEA,IAAI;YACF,IAAI,gBAAgB;gBAClB,MAAM,gBAAgB,CAAC,kBAAkB,KAAK,OAAO;gBACrD,MAAM,eAAe,KAAK,OAAO;gBACjC,MAAM,OAAO,CAAC,oBAAoB,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC,kCAAkC,CAAC;YAC/F;QACF,EAAE,OAAO,OAAO;YACd,MAAM,kBAAkB,CAAC,KAAK,OAAO,EAAE;QACzC;IACF;IAEA,MAAM,wBAAwB;QAC5B,4BAA4B,CAAC;QAC7B,mBAAmB,CAAC,4BAA4B,aAAa,MAAM,GAAG;IACxE;IAEA,8BAA8B;IAC9B,MAAM,WAAW,CAAC;QAChB,eAAe,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,cAAc,UAAU;IACpE;IAEA,MAAM,gBAAgB,IAAM,SAAS;IACrC,MAAM,eAAe,IAAM,SAAS,cAAc,UAAU;IAC5D,MAAM,eAAe,IAAM,SAAS,cAAc;IAClD,MAAM,eAAe,IAAM,SAAS,cAAc;IAElD,kCAAkC;IAClC,MAAM,mBAAmB;QACvB,MAAM,aAAa,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO;QACxD,MAAM,eAAe;eAAI,IAAI,IAAI;mBAAI;mBAAiB;aAAW;SAAE;QACnE,oBAAoB;IACtB;IAEA,MAAM,qBAAqB;QACzB,MAAM,aAAa,IAAI,IAAI,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO;QAChE,MAAM,eAAe,aAAa,MAAM,CAAC,CAAA,KAAM,CAAC,WAAW,GAAG,CAAC;QAC/D,oBAAoB;IACtB;IAEA,MAAM,YAAY;QAChB,MAAM,SAAS,WAAW,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO;QAC5C,oBAAoB;IACtB;IAEA,MAAM,cAAc;QAClB,oBAAoB,EAAE;IACxB;IAEA,qDAAqD;IACrD,MAAM,qBAAqB,cAAc,IAAI,CAAC,MAAM,GAAG,KACrD,cAAc,IAAI,CAAC,KAAK,CAAC,CAAA,OAAQ,aAAa,QAAQ,CAAC,KAAK,OAAO;IAErE,MAAM,sBAAsB,cAAc,IAAI,CAAC,IAAI,CAAC,CAAA,OAAQ,aAAa,QAAQ,CAAC,KAAK,OAAO;IAE9F,mCAAmC;IACnC,MAAM,iBAAiB,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,EAIxD,iBACC,8OAAC,mIAAA,CAAA,kBAAe;sBACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;kCACN,8OAAC,mIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,8OAAC;4BACC,SAAS,IAAM,WAAW;4BAC1B,WAAW,CAAC,oFAAoF,EAAE,WAAW;;gCAE5G;gCACA,cAAc,QACb,kBAAkB,sBAChB,8OAAC,4MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAEnB,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;yDAGvB,8OAAC,wNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAI7B,8OAAC,mIAAA,CAAA,iBAAc;kCACb,cAAA,8OAAC;;gCAAE;gCAAyB;;;;;;;;;;;;;;;;;;;;;;;IAMpC,6CAA6C;IAC7C,MAAM,eAAe,CAAC,EACpB,OAAO,EACP,MAAM,IAAI,EACV,OAAO,EACP,UAAU,SAAS,EACnB,WAAW,KAAK,EAChB,YAAY,EAAE,EAQf;QACC,MAAM,iBAAiB;YACrB,SAAS;YACT,QAAQ;YACR,SAAS;YACT,SAAS;QACX;QAEA,qBACE,8OAAC,mIAAA,CAAA,kBAAe;sBACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;kCACN,8OAAC,mIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAS;4BACT,UAAU;4BACV,WAAW,CAAC,YAAY,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU,qHAAqH,CAAC;sCAErL,cAAA,8OAAC;gCAAK,WAAU;;;;;;;;;;;;;;;;kCAGpB,8OAAC,mIAAA,CAAA,iBAAc;kCACb,cAAA,8OAAC;sCAAG;;;;;;;;;;;;;;;;;;;;;;IAKd;IAEA,qBAAqB;IACrB,MAAM,kBAAkB,CAAC;QACvB,IAAI,mBAAmB;YACrB,kBAAkB,UAAU,aAAa,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,IAAI,EAAE;QACnE;IACF;IAEA,MAAM,mBAAmB,CAAC,QAAgB;QACxC,IAAI,mBAAmB;YACrB,MAAM,eAAe,UACjB;mBAAI;gBAAc;aAAO,GACzB,aAAa,MAAM,CAAC,CAAA,KAAM,OAAO;YACrC,kBAAkB;QACpB;IACF;IAEA,uBAAuB;IACvB,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,aAAa;oBACb,YAAY,EAAE,yBAAyB;gBACzC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;gBACvC,MAAM,IAAI,SAAS,aAAa,CAAC;gBACjC,EAAE,IAAI,GAAG;gBACT,EAAE,QAAQ,GAAG,CAAC,YAAY,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;gBACxE,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,EAAE,KAAK;gBACP,OAAO,GAAG,CAAC,eAAe,CAAC;gBAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,CAAC,+BAA+B,EAAE,MAAM,KAAK,EAAE;YACvD;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,yBAAyB;QAC7B,MAAM,YAAY,OAAO;QACzB,IAAI,CAAC,WAAW;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,aAAa;oBACb,YAAY;oBACZ;gBACF;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,OAAO,OAAO;YACpB,qBAAqB;YACvB,OAAO;gBACL,MAAM,CAAC,QAAQ,EAAE,OAAO,KAAK,EAAE;YACjC;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,0BAA0B;QAC9B,iDAAiD;QACjD,MAAM,CAAC,yBAAyB,EAAE,aAAa,MAAM,CAAC,KAAK,CAAC;IAC9D;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,QAAQ,CAAC,8BAA8B,EAAE,aAAa,MAAM,CAAC,MAAM,CAAC,GAAG;YAC1E;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,aAAa;oBACb,YAAY,EAAE,yBAAyB;gBACzC;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,OAAO,OAAO;YACpB,qBAAqB;YACvB,OAAO;gBACL,MAAM,CAAC,QAAQ,EAAE,OAAO,KAAK,EAAE;YACjC;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,oDAAoD;IACpD,MAAM,UAAuB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACnC,MAAM,cAA2B;YAC/B;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,cAAc,kBACZ,8OAAC;wBAAe,OAAM;kCAAU;;;;;;gBAElC,YAAY,CAAC,oBACX,8OAAC;wBAAK,WAAU;kCAAkC,IAAI,OAAO;;;;;;YAEjE;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,cAAc,kBACZ,8OAAC;wBAAe,OAAM;kCAAU;;;;;;gBAElC,YAAY,CAAC,oBACX,8OAAC,6IAAA,CAAA,UAAa;wBAAC,MAAM,IAAI,OAAO,IAAI;wBAAI,WAAW;;;;;;YAEvD;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,YAAY,CAAC,oBACX,8OAAC,6IAAA,CAAA,UAAa;wBAAC,MAAM,IAAI,OAAO,IAAI;wBAAI,WAAW;;;;;;YAEvD;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,YAAY,CAAC,oBACX,8OAAC,6IAAA,CAAA,UAAa;wBAAC,MAAM,IAAI,SAAS,IAAI;wBAAI,WAAW;;;;;;YAEzD;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,YAAY,CAAC,MAAc,IAAI,UAAU,IAAI,IAAI,OAAO;YAC1D;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,cAAc,kBACZ,8OAAC;wBAAe,OAAM;kCAAgB;;;;;;gBAExC,YAAY,CAAC,MAAc,IAAI,aAAa,GAAG,IAAI,aAAa,CAAC,OAAO,CAAC,KAAK;YAChF;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,cAAc,kBACZ,8OAAC;wBAAe,OAAM;kCAAe;;;;;;gBAEvC,YAAY,CAAC;oBACX,MAAM,QAAQ,IAAI,YAAY,IAAI,IAAI,eAAe,IAAI;oBACzD,OAAO,QAAQ,MAAM,OAAO,CAAC,KAAK;gBACpC;YACF;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,YAAY,CAAC,oBACX,8OAAC,6IAAA,CAAA,UAAa;wBACZ,MAAM,IAAI,EAAE,IAAI,IAAI,mBAAmB,IAAI;wBAC3C,WAAW;;;;;;YAGjB;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,YAAY,CAAC,oBACX,8OAAC,6IAAA,CAAA,UAAa;wBACZ,MAAM,IAAI,CAAC,IAAI,IAAI,iBAAiB,IAAI;wBACxC,WAAW;;;;;;YAGjB;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,YAAY,CAAC,MAAc,gBAAgB;YAC7C;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,eAAe;gBACf,cAAc,kBACZ,8OAAC;wBAAe,OAAM;kCAAQ;;;;;;gBAEhC,YAAY,CAAC,MAAc,gBAAgB;YAC7C;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,eAAe;gBACf,aAAa;gBACb,YAAY,CAAC,MAAc,oBAAoB;YACjD;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,eAAe;gBACf,aAAa;gBACb,YAAY,CAAC,MAAc,uBAAuB;YACpD;SAED;QAED,kCAAkC;QAClC,IAAI,0BAA0B;YAC5B,YAAY,OAAO,CAAC;gBAClB,OAAO;gBACP,YAAY;gBACZ,eAAe;gBACf,aAAa;gBACb,OAAO;gBACP,OAAO;gBACP,cAAc,kBACZ,8OAAC,oIAAA,CAAA,WAAQ;wBACP,SAAS,aAAa,MAAM,KAAK,aAAa,MAAM,IAAI,aAAa,MAAM,GAAG;wBAC9E,iBAAiB;;;;;;gBAGrB,YAAY,CAAC,oBACX,8OAAC,oIAAA,CAAA,WAAQ;wBACP,SAAS,aAAa,QAAQ,CAAC,IAAI,OAAO;wBAC1C,iBAAiB,CAAC,UAAY,iBAAiB,IAAI,OAAO,EAAE;wBAC5D,SAAS,CAAC,IAAM,EAAE,eAAe;;;;;;YAGvC;QACF;QAEA,OAAO;IACT,GAAG;QAAC;QAA0B;QAAc;QAAc;QAAiB;KAAiB;IAE5F,qDAAqD;IACrD,MAAM,YAAY,CAAC,KAAW;QAC5B,MAAM,aAAa,aAAa,QAAQ,CAAC,IAAI,OAAO;QAEpD,qBACE,8OAAC,iIAAA,CAAA,WAAQ;YAEP,WAAW,CAAC;UACV,EAAE,aAAa,+BAA+B,WAAW;;;;UAIzD,EAAE,aAAa,yBAAyB,GAAG;QAC7C,CAAC;YACD,SAAS,IAAM,4BAA4B,iBAAiB,IAAI,OAAO,EAAE,CAAC;YAC1E,eAAe,CAAC;gBACd,EAAE,cAAc;gBAChB,sBAAsB,KAAK;YAC7B;sBAEC,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,iIAAA,CAAA,YAAS;oBAER,WAAW,CAAC;;cAEV,EAAE,aAAa,kBAAkB,gBAAgB;;YAEnD,CAAC;oBACD,OAAO;wBAAE,OAAO,OAAO,KAAK;wBAAE,GAAG,OAAO,SAAS;oBAAC;oBAClD,SAAS,CAAC;wBACR,uCAAuC;wBACvC,IAAI;4BAAC;4BAAuB;4BAAgB;yBAAc,CAAC,QAAQ,CAAC,OAAO,KAAK,GAAG;4BACjF,EAAE,eAAe;wBACnB;oBACF;8BAEC,OAAO,UAAU,GAAG,OAAO,UAAU,CAAC,OAAQ,GAAG,CAAC,OAAO,KAAK,CAAC,kBAAI,8OAAC;wBAAK,WAAU;kCAAgB;;;;;;mBAd/F,OAAO,KAAK;;;;;WAhBhB,IAAI,OAAO;;;;;IAmCtB;IAEA,iEAAiE;IACjE,MAAM,kBAAkB,CAAC;QACvB,MAAM,WAAW,KAAK,SAAS;QAE/B,IAAI,CAAC,YAAY,aAAa,OAAO;YACnC,qBAAO,8OAAC;gBAAK,WAAU;0BAAgB;;;;;;QACzC;QAEA,IAAI,aAAa,gBAAgB;YAC/B,qBACE,8OAAC,iIAAA,CAAA,QAAK;gBACJ,SAAQ;gBACR,WAAU;0BACX;;;;;;QAIL;QAEA,2DAA2D;QAC3D,IAAI,eAAe;QACnB,IAAI,QAAQ,SAAS,KAAK,CAAC;QAC3B,IAAI,OAAO;YACT,eAAe,KAAK,CAAC,EAAE;QACzB,OAAO;YACL,QAAQ,SAAS,KAAK,CAAC;YACvB,IAAI,OAAO;gBACT,eAAe,KAAK,CAAC,EAAE;YACzB,OAAO;gBACL,QAAQ,SAAS,KAAK,CAAC;gBACvB,IAAI,OAAO;oBACT,eAAe,KAAK,CAAC,EAAE;gBACzB,OAAO;oBACL,QAAQ,SAAS,KAAK,CAAC;oBACvB,IAAI,OAAO;wBACT,eAAe,KAAK,CAAC,EAAE;oBACzB;gBACF;YACF;QACF;QAEA,qBACE,8OAAC,mIAAA,CAAA,kBAAe;sBACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;kCACN,8OAAC,mIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,8OAAC,iIAAA,CAAA,QAAK;4BACJ,SAAQ;4BACR,WAAU;4BACV,SAAS,CAAC;gCACR,EAAE,eAAe;gCACjB,iDAAiD;gCACjD,MAAM,kBAAkB,KAAK,YAAY,IAAI,KAAK,eAAe,IAAI;gCACrE,MAAM,cAAc,kBAAkB;gCAEtC,IAAI,aAAa;oCACf,oDAAoD;oCACpD,iBAAiB,MAAM;gCACzB,OAAO;oCACL,0DAA0D;oCAC1D,iBAAiB,MAAM;gCACzB;4BACF;;8CAEA,8OAAC;8CAAM;;;;;;8CACP,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG3B,8OAAC,mIAAA,CAAA,iBAAc;kCACb,cAAA,8OAAC;sCACE,CAAC;gCACA,MAAM,kBAAkB,KAAK,YAAY,IAAI,KAAK,eAAe,IAAI;gCACrE,MAAM,cAAc,kBAAkB;gCACtC,OAAO,cACH,iCACA;4BACN,CAAC;;;;;;;;;;;;;;;;;;;;;;IAMb;IAEA,mCAAmC;IACnC,MAAM,iBAAiB,CAAC;QACtB,gDAAgD;QAChD,MAAM,cAAc,KAAK,YAAY;QACrC,MAAM,kBAAkB,KAAK,gBAAgB;QAC7C,MAAM,gBAAgB,KAAK,cAAc;QACzC,MAAM,wBAAwB,KAAK,sBAAsB;QAEzD,gFAAgF;QAChF,MAAM,gBAAgB,eAAe,mBAAmB,iBAAiB;QAEzE,8EAA8E;QAC9E,IAAI,iBAAiB,KAAK,mBAAmB,KAAK,YAAY;YAC5D,MAAM,eAAe,CAAA,GAAA,0HAAA,CAAA,sBAAmB,AAAD,EAAE;YACzC,qBACE,8OAAC,iIAAA,CAAA,QAAK;gBACJ,WAAW,CAAC,eAAe,EAAE,aAAa,KAAK,CAAC,CAAC,EAAE,aAAa,KAAK,EAAE;gBACvE,SAAS,IAAM,iBAAiB,MAAM,gBAAgB;0BAErD;;;;;;QAGP;QAEA,qCAAqC;QACrC,MAAM,QAAQ,KAAK,mBAAmB,IAAI;QAC1C,MAAM,eAAe,CAAA,GAAA,0HAAA,CAAA,sBAAmB,AAAD,EAAE;QAEzC,qBACE,8OAAC,iIAAA,CAAA,QAAK;YAAC,WAAW,aAAa,KAAK;sBACjC;;;;;;IAGP;IAEA,MAAM,kBAAkB,CAAC;QACvB,+EAA+E;QAC/E,MAAM,kBAAkB,KAAK,YAAY,IAAI,KAAK,eAAe,IAAI;QACrE,MAAM,cAAc,kBAAkB;QAEtC,uDAAuD;QACvD,MAAM,cAAc,KAAK,YAAY;QACrC,MAAM,kBAAkB,KAAK,gBAAgB;QAC7C,MAAM,gBAAgB,KAAK,cAAc;QACzC,MAAM,wBAAwB,KAAK,sBAAsB;QACzD,MAAM,gBAAgB,eAAe,mBAAmB,iBAAiB;QAEzE,yGAAyG;QACzG,IAAI,iBAAiB,KAAK,mBAAmB,KAAK,YAAY;YAC5D,qBACE,8OAAC,mIAAA,CAAA,kBAAe;0BACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;sCACN,8OAAC,mIAAA,CAAA,iBAAc;4BAAC,OAAO;sCACrB,cAAA,8OAAC,iIAAA,CAAA,QAAK;gCACJ,WAAU;gCACV,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB,iBAAiB,MAAM,gBAAgB;gCACzC;0CAEC;;;;;;;;;;;sCAGL,8OAAC,mIAAA,CAAA,iBAAc;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;QAKb;QAEA,4EAA4E;QAC5E,MAAM,QAAQ,KAAK,mBAAmB,IAAI;QAE1C,IAAI,UAAU,gBAAgB,aAAa;YACzC,qBACE,8OAAC,iIAAA,CAAA,QAAK;gBAAC,WAAU;0BAA4E;;;;;;QAIjG,OAAO,IAAI,UAAU,YAAY;YAC/B,qBACE,8OAAC,iIAAA,CAAA,QAAK;gBAAC,WAAU;0BAA+E;;;;;;QAIpG,OAAO;YACL,qBACE,8OAAC,iIAAA,CAAA,QAAK;gBACJ,SAAQ;gBACR,WAAU;0BACX;;;;;;QAIL;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,+DAA+D;QAC/D,MAAM,kBAAkB,KAAK,YAAY,IAAI,KAAK,eAAe,IAAI;QACrE,MAAM,qBAAqB,KAAK,mBAAmB,IAAI;QACvD,MAAM,cAAc,kBAAkB,KAAK,uBAAuB,gBAAgB,uBAAuB;QACzG,MAAM,eAAe,KAAK,YAAY,IAAI,KAAK,YAAY,IAAI;QAE/D,mEAAmE;QACnE,IAAI,CAAC,aAAa;YAChB,qBACE,8OAAC,mIAAA,CAAA,kBAAe;0BACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;sCACN,8OAAC,mIAAA,CAAA,iBAAc;4BAAC,OAAO;sCACrB,cAAA,8OAAC;gCAAK,WAAU;;kDACd,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;sCAIhC,8OAAC,mIAAA,CAAA,iBAAc;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;QAKb;QAEA,6DAA6D;QAC7D,IAAI,OAAO,MAAM,YAAY;QAE7B,OAAQ;YACN,KAAK;gBACH,QAAQ;gBACR,OAAO,kMAAA,CAAA,OAAI;gBACX,aAAa;gBACb,cAAc;gBACd;YACF,KAAK;gBACH,QAAQ;gBACR,OAAO,kMAAA,CAAA,OAAI;gBACX,aAAa;gBACb,cAAc;gBACd;YACF,KAAK;gBACH,QAAQ;gBACR,OAAO,kMAAA,CAAA,OAAI;gBACX,aAAa;gBACb,cAAc;gBACd;YACF,KAAK;gBACH,QAAQ;gBACR,OAAO,sMAAA,CAAA,SAAM;gBACb,aAAa;gBACb,cAAc;gBACd;YACF;gBACE,QAAQ;gBACR,OAAO,0MAAA,CAAA,WAAQ;gBACf,aAAa;gBACb,cAAc;gBACd;QACJ;QAEA,MAAM,gBAAgB;QAEtB,qBACE,8OAAC,kIAAA,CAAA,SAAM;YACL,SAAQ;YACR,MAAK;YACL,WAAW,CAAC,4DAA4D,EAAE,aAAa;YACvF,SAAS,CAAC;gBACR,EAAE,eAAe;gBACjB,IAAI,eAAe,oBAAoB;oBACrC,WAAW,cAAc;gBAC3B,OAAO;oBACL,iBAAiB,MAAM;gBACzB;YACF;;8BAEA,8OAAC;oBAAc,WAAU;;;;;;gBACxB;;;;;;;IAGP;IAEA,MAAM,yBAAyB,CAAC;QAC9B,+DAA+D;QAC/D,MAAM,kBAAkB,KAAK,YAAY,IAAI,KAAK,eAAe,IAAI;QACrE,MAAM,qBAAqB,KAAK,mBAAmB,IAAI;QACvD,MAAM,cAAc,kBAAkB,KAAK,uBAAuB,gBAAgB,uBAAuB;QACzG,MAAM,cAAc,KAAK,WAAW,KAAK,QAAQ,KAAK,WAAW,KAAK,QAAQ,KAAK,WAAW,KAAK;QACnG,MAAM,aAAa,KAAK,WAAW,KAAK,SAAS,KAAK,WAAW,KAAK,QAAQ,KAAK,WAAW,KAAK;QAEnG,mEAAmE;QACnE,IAAI,CAAC,aAAa;YAChB,qBACE,8OAAC,mIAAA,CAAA,kBAAe;0BACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;sCACN,8OAAC,mIAAA,CAAA,iBAAc;4BAAC,OAAO;sCACrB,cAAA,8OAAC;gCAAK,WAAU;;kDACd,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;sCAIhC,8OAAC,mIAAA,CAAA,iBAAc;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;QAKb;QAEA,mDAAmD;QACnD,IAAI,aAAa;YACf,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iIAAA,CAAA,QAAK;wBAAC,WAAU;;0CACf,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAG1C,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,CAAC;4BACR,EAAE,eAAe;4BACjB,WAAW,eAAe;wBAC5B;kCAEA,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;;;;;;QAI5B;QAEA,8DAA8D;QAC9D,IAAI,YAAY;YACd,qBACE,8OAAC,iIAAA,CAAA,QAAK;gBAAC,WAAU;;kCACf,8OAAC,4LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;QAIpC;QAEA,uCAAuC;QACvC,qBACE,8OAAC,kIAAA,CAAA,SAAM;YACL,SAAQ;YACR,MAAK;YACL,WAAU;YACV,SAAS,CAAC;gBACR,EAAE,eAAe;gBACjB,mBAAmB;YACrB;;8BAEA,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;gBAAiB;;;;;;;IAI9C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,6IAAA,CAAA,UAAe;gBACd,MAAM;gBACN,sBAAsB;gBACtB,SAAS;gBACT,kBAAkB;gBAClB,mBAAmB;;;;;;0BAIrB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCAAyB;kDAC5B,8OAAC;wCAAK,WAAU;kDAAiB,cAAc,UAAU;;;;;;oCAAQ;kDAAG,8OAAC;wCAAK,WAAU;kDAAiB,cAAc,QAAQ;;;;;;oCAAQ;kDAAI,8OAAC;wCAAK,WAAU;kDAAiB,cAAc,UAAU;;;;;;oCAAQ;;;;;;;4BAGnN,0CACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,mIAAA,CAAA,kBAAe;kDACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;8DACN,8OAAC,mIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,8OAAC;wDACC,SAAS,qBAAqB,qBAAqB;wDACnD,WAAU;;0EAEV,8OAAC,oIAAA,CAAA,WAAQ;gEACP,SAAS;gEACT,KAAK,CAAC;oEACJ,IAAI,IAAI,GAAG,aAAa,GAAG,uBAAuB,CAAC;gEACrD;;;;;;4DACA;;;;;;;;;;;;8DAIN,8OAAC,mIAAA,CAAA,iBAAc;8DACb,cAAA,8OAAC;kEAAG,qBAAqB,sCAAsC;;;;;;;;;;;;;;;;;;;;;;kDAKrE,8OAAC,mIAAA,CAAA,kBAAe;kDACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;8DACN,8OAAC,mIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,aAAa,MAAM,KAAK,WAAW,MAAM,GAAG,cAAc;wDACnE,WAAU;;0EAEV,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAY;4DACrB,WAAW,MAAM;4DAAC;;;;;;;;;;;;8DAG9B,8OAAC,mIAAA,CAAA,iBAAc;8DACb,cAAA,8OAAC;kEAAG,aAAa,MAAM,KAAK,WAAW,MAAM,GAAG,6BAA6B;;;;;;;;;;;;;;;;;;;;;;oCAKlF,aAAa,MAAM,GAAG,mBACrB,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;;4CAClC,aAAa,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;kCAQ/B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAyB;;;;;;0CACzC,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC;oCACT,gBAAgB,OAAO,EAAE,MAAM,CAAC,KAAK;oCACrC,eAAe;gCACjB;gCACA,WAAU;;kDAEV,8OAAC;wCAAO,OAAO;kDAAI;;;;;;kDACnB,8OAAC;wCAAO,OAAO;kDAAI;;;;;;kDACnB,8OAAC;wCAAO,OAAO;kDAAI;;;;;;kDACnB,8OAAC;wCAAO,OAAO;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAM1B,8OAAC,+IAAA,CAAA,UAAe;gBACd,MAAM;gBACN,SAAS;gBACT,SAAS;gBACT,cAAa;gBACb,sBAAsB;gBACtB,WAAW;;;;;;YAIZ,cAAc,UAAU,GAAG,mBAC1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BAAyB;0CAC/B,8OAAC;gCAAK,WAAU;0CAAiB;;;;;;4BAAmB;0CAAI,8OAAC;gCAAK,WAAU;0CAAiB,cAAc,UAAU;;;;;;;;;;;;kCAG1H,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,mIAAA,CAAA,kBAAe;0CACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;sDACN,8OAAC,mIAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC,cAAc,WAAW;gDACpC,WAAU;0DAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAG5B,8OAAC,mIAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;0CAKT,8OAAC,mIAAA,CAAA,kBAAe;0CACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;sDACN,8OAAC,mIAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC,cAAc,WAAW;gDACpC,WAAU;0DAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAG3B,8OAAC,mIAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;0CAMT,8OAAC;gCAAI,WAAU;0CACZ,MAAM,IAAI,CAAC;oCAAE,QAAQ,KAAK,GAAG,CAAC,GAAG,cAAc,UAAU;gCAAE,GAAG,CAAC,GAAG;oCACjE,IAAI;oCACJ,IAAI,cAAc,UAAU,IAAI,GAAG;wCACjC,UAAU,IAAI;oCAChB,OAAO,IAAI,eAAe,GAAG;wCAC3B,UAAU,IAAI;oCAChB,OAAO,IAAI,eAAe,cAAc,UAAU,GAAG,GAAG;wCACtD,UAAU,cAAc,UAAU,GAAG,IAAI;oCAC3C,OAAO;wCACL,UAAU,cAAc,IAAI;oCAC9B;oCAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;wCAEL,SAAS,gBAAgB,UAAU,YAAY;wCAC/C,MAAK;wCACL,SAAS,IAAM,SAAS;wCACxB,WAAU;kDAET;uCANI;;;;;gCASX;;;;;;0CAGF,8OAAC,mIAAA,CAAA,kBAAe;0CACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;sDACN,8OAAC,mIAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC,cAAc,WAAW;gDACpC,WAAU;0DAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAG5B,8OAAC,mIAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;0CAKT,8OAAC,mIAAA,CAAA,kBAAe;0CACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;sDACN,8OAAC,mIAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC,cAAc,WAAW;gDACpC,WAAU;0DAEV,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAG7B,8OAAC,mIAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASd,4BAA4B,aAAa,MAAM,GAAG,mBACjD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;;gDAClC,aAAa,MAAM;gDAAC;;;;;;;;;;;;;8CAIzB,8OAAC,mIAAA,CAAA,kBAAe;8CACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;0DACN,8OAAC,mIAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;;sEAEV,8OAAC,4LAAA,CAAA,IAAC;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;0DAIlC,8OAAC,mIAAA,CAAA,iBAAc;0DACb,cAAA,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMX,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,mIAAA,CAAA,kBAAe;8CACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;0DACN,8OAAC,mIAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;wDACP,+BAA+B;wDAC/B,MAAM,gBAAgB,CAAC,gBAAgB,GAAG,aAAa,MAAM,CAAC,KAAK,CAAC;oDACtE;oDACA,WAAU;;sEAEV,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;0DAIpC,8OAAC,mIAAA,CAAA,iBAAc;0DACb,cAAA,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;8CAKT,8OAAC,mIAAA,CAAA,kBAAe;8CACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;0DACN,8OAAC,mIAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;wDACP,kCAAkC;wDAClC,MAAM,gBAAgB,CAAC,mBAAmB,GAAG,aAAa,MAAM,CAAC,KAAK,CAAC;oDACzE;oDACA,WAAU;;sEAEV,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;0DAIpC,8OAAC,mIAAA,CAAA,iBAAc;0DACb,cAAA,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;8CAKT,8OAAC,mIAAA,CAAA,kBAAe;8CACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;0DACN,8OAAC,mIAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS;wDACP,+BAA+B;wDAC/B,MAAM,gBAAgB,CAAC,uBAAuB,GAAG,aAAa,MAAM,CAAC,KAAK,CAAC;oDAC7E;oDACA,WAAU;;sEAEV,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;0DAIpC,8OAAC,mIAAA,CAAA,iBAAc;0DACb,cAAA,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;8CAKT,8OAAC,mIAAA,CAAA,kBAAe;8CACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;0DACN,8OAAC,mIAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;wDACP,uCAAuC;wDACvC,MAAM,gBAAgB,CAAC,wBAAwB,GAAG,aAAa,MAAM,CAAC,KAAK,CAAC;oDAC9E;oDACA,WAAU;;sEAEV,8OAAC,wMAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;0DAInC,8OAAC,mIAAA,CAAA,iBAAc;0DACb,cAAA,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;8CAKT,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC,mIAAA,CAAA,kBAAe;8CACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;0DACN,8OAAC,mIAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;wDACP,4CAA4C;wDAC5C,IAAI,QAAQ,CAAC,8BAA8B,EAAE,aAAa,MAAM,CAAC,kBAAkB,CAAC,GAAG;4DACrF,MAAM,gBAAgB,CAAC,gBAAgB,GAAG,aAAa,MAAM,CAAC,KAAK,CAAC;wDACtE;oDACF;oDACA,WAAU;;sEAEV,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;0DAIlC,8OAAC,mIAAA,CAAA,iBAAc;0DACb,cAAA,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUjB,8OAAC,+IAAA,CAAA,wBAAqB;gBACpB,QAAQ,QAAQ,UAAU,CAAC,MAAM;gBACjC,SAAS,IAAM,YAAY;gBAC3B,WAAW;gBACX,MAAM,QAAQ,UAAU,CAAC,IAAI;;;;;;0BAG/B,8OAAC,+IAAA,CAAA,oBAAiB;gBAChB,QAAQ,QAAQ,WAAW,CAAC,MAAM;gBAClC,SAAS,IAAM,YAAY;gBAC3B,YAAY;gBACZ,MAAM,QAAQ,WAAW,CAAC,IAAI;;;;;;0BAGhC,8OAAC,+IAAA,CAAA,2BAAwB;gBACvB,QAAQ,QAAQ,kBAAkB,CAAC,MAAM;gBACzC,SAAS,IAAM,YAAY;gBAC3B,MAAM,QAAQ,kBAAkB,CAAC,IAAI;gBACrC,qBAAqB,QAAQ,kBAAkB,CAAC,mBAAmB;;;;;;;;;;;;AAI3E", "debugId": null}}, {"offset": {"line": 6773, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/cavi/CaviStatistics.tsx"], "sourcesContent": ["'use client'\n\nimport { useMemo, useState } from 'react'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  Cable,\n  CheckCircle,\n  Clock,\n  AlertTriangle,\n  Zap,\n  Package,\n  BarChart3\n} from 'lucide-react'\nimport { Cavo } from '@/types'\n\n\n\ninterface CaviStatisticsProps {\n  cavi: Cavo[]\n  filteredCavi: Cavo[]\n  className?: string\n  revisioneCorrente?: string\n}\n\nexport default function CaviStatistics({\n  cavi,\n  filteredCavi,\n  className,\n  revisioneCorrente\n}: CaviStatisticsProps) {\n  const stats = useMemo(() => {\n    const totalCavi = cavi.length\n    const filteredCount = filteredCavi.length\n    \n    // Installation status\n    const installati = filteredCavi.filter(c => \n      c.stato_installazione === 'Installato' || \n      (c.metri_posati && c.metri_posati > 0) ||\n      (c.metratura_reale && c.metratura_reale > 0)\n    ).length\n    \n    const inCorso = filteredCavi.filter(c => \n      c.stato_installazione === 'In corso'\n    ).length\n    \n    const daInstallare = filteredCount - installati - inCorso\n    \n    // Connection status\n    const collegati = filteredCavi.filter(c => {\n      const collegamento = c.collegamento || c.collegamenti || 0\n      return collegamento === 3 // Both sides connected\n    }).length\n    \n    const parzialmenteCollegati = filteredCavi.filter(c => {\n      const collegamento = c.collegamento || c.collegamenti || 0\n      return collegamento === 1 || collegamento === 2 // One side connected\n    }).length\n    \n    const nonCollegati = filteredCavi.filter(c => {\n      const collegamento = c.collegamento || c.collegamenti || 0\n      return collegamento === 0 && (c.metri_posati > 0 || c.metratura_reale > 0)\n    }).length\n    \n    // Certification status\n    const certificati = filteredCavi.filter(c => \n      c.certificato === true || \n      c.certificato === 'SI' || \n      c.certificato === 'CERTIFICATO'\n    ).length\n    \n    // Meters calculation\n    const metriTotali = filteredCavi.reduce((sum, c) => sum + (c.metri_teorici || 0), 0)\n    const metriInstallati = filteredCavi.reduce((sum, c) => {\n      const metri = c.metri_posati || c.metratura_reale || 0\n      return sum + metri\n    }, 0)\n\n    // Calcolo IAP (Indice di Avanzamento Ponderato) come nella webapp originale\n    const calculateIAP = (nTot: number, nInst: number, nColl: number, nCert: number): number => {\n      // Pesi per le fasi del progetto\n      const Wp = 2.0  // Peso fase Posa\n      const Wc = 1.5  // Peso fase Collegamento\n      const Wz = 0.5  // Peso fase Certificazione\n\n      // Se non ci sono cavi, ritorna 0\n      if (nTot === 0) return 0\n\n      // Calcolo del numeratore (Sforzo Completato)\n      const sforzoSoloInstallati = (nInst - nColl) * Wp\n      const sforzoSoloCollegati = (nColl - nCert) * (Wp + Wc)\n      const sforzoCertificati = nCert * (Wp + Wc + Wz)\n      const numeratore = sforzoSoloInstallati + sforzoSoloCollegati + sforzoCertificati\n\n      // Calcolo del denominatore (Sforzo Massimo Previsto)\n      const denominatore = nTot * (Wp + Wc + Wz)\n\n      // Calcolo finale dell'IAP in percentuale\n      const iap = (numeratore / denominatore) * 100\n\n      return Math.round(iap * 100) / 100 // Arrotonda a 2 decimali\n    }\n\n    const percentualeInstallazione = calculateIAP(filteredCount, installati, collegati, certificati)\n    \n    return {\n      totalCavi,\n      filteredCount,\n      installati,\n      inCorso,\n      daInstallare,\n      collegati,\n      parzialmenteCollegati,\n      nonCollegati,\n      certificati,\n      metriTotali,\n      metriInstallati,\n      percentualeInstallazione\n    }\n  }, [cavi, filteredCavi])\n\n  // Componente KPI interattivo\n\n\n  return (\n    <Card className={className}>\n      <CardContent className=\"p-1.5\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between mb-1\">\n          <div className=\"flex items-center space-x-1.5\">\n            <BarChart3 className=\"h-3.5 w-3.5 text-mariner-600\" />\n            <span className=\"text-xs font-semibold text-mariner-900\">Statistiche Cavi</span>\n            {revisioneCorrente && (\n              <Badge variant=\"outline\" className=\"text-xs bg-blue-50 text-blue-700 border-blue-200\">\n                Rev. {revisioneCorrente}\n              </Badge>\n            )}\n          </div>\n        </div>\n\n        {/* Statistics distributed across full width */}\n        <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2\">\n\n          {/* Total cavi */}\n          <div className=\"flex items-center space-x-1.5 bg-mariner-50 px-1.5 py-1 rounded-lg\">\n            <Cable className=\"h-3.5 w-3.5 text-mariner-600\" />\n            <div>\n              <div className=\"font-bold text-mariner-900 text-sm\">{stats.filteredCount}</div>\n              <div className=\"text-xs text-mariner-600\">di {stats.totalCavi} cavi</div>\n            </div>\n          </div>\n\n          {/* Installati */}\n          <div className=\"flex items-center space-x-1.5 bg-green-50 px-1.5 py-1 rounded-lg\">\n            <CheckCircle className=\"h-3.5 w-3.5 text-green-600\" />\n            <div>\n              <div className=\"font-bold text-green-700 text-sm\">{stats.installati}</div>\n              <div className=\"text-xs text-green-600\">installati</div>\n            </div>\n          </div>\n\n          {/* In corso */}\n          <div className=\"flex items-center space-x-1.5 bg-yellow-50 px-1.5 py-1 rounded-lg\">\n            <Clock className=\"h-3.5 w-3.5 text-yellow-600\" />\n            <div>\n              <div className=\"font-bold text-yellow-700 text-sm\">{stats.inCorso}</div>\n              <div className=\"text-xs text-yellow-600\">in corso</div>\n            </div>\n          </div>\n\n          {/* Da installare */}\n          <div className=\"flex items-center space-x-1.5 bg-gray-50 px-1.5 py-1 rounded-lg\">\n            <AlertTriangle className=\"h-3.5 w-3.5 text-gray-600\" />\n            <div>\n              <div className=\"font-bold text-gray-700 text-sm\">{stats.daInstallare}</div>\n              <div className=\"text-xs text-gray-600\">da installare</div>\n            </div>\n          </div>\n\n          {/* Collegati */}\n          <div className=\"flex items-center space-x-1.5 bg-blue-50 px-1.5 py-1 rounded-lg\">\n            <Zap className=\"h-3.5 w-3.5 text-blue-600\" />\n            <div>\n              <div className=\"font-bold text-blue-700 text-sm\">{stats.collegati}</div>\n              <div className=\"text-xs text-blue-600\">collegati</div>\n            </div>\n          </div>\n\n          {/* Certificati */}\n          <div className=\"flex items-center space-x-1.5 bg-purple-50 px-1.5 py-1 rounded-lg\">\n            <Package className=\"h-3.5 w-3.5 text-purple-600\" />\n            <div>\n              <div className=\"font-bold text-purple-700 text-sm\">{stats.certificati}</div>\n              <div className=\"text-xs text-purple-600\">certificati</div>\n            </div>\n          </div>\n\n          {/* Meters progress */}\n          <div className=\"flex items-center space-x-1.5 bg-indigo-50 px-1.5 py-1 rounded-lg\">\n            <div className=\"h-3.5 w-3.5 flex items-center justify-center\">\n              <div className=\"h-2 w-2 bg-indigo-600 rounded-full\"></div>\n            </div>\n            <div>\n              <div className=\"font-bold text-indigo-700 text-sm\">{stats.metriInstallati.toLocaleString()}m</div>\n              <div className=\"text-xs text-indigo-600\">di {stats.metriTotali.toLocaleString()}m</div>\n            </div>\n          </div>\n\n        </div>\n\n        {/* Progress bar - Colori morbidi */}\n        {stats.filteredCount > 0 && (\n          <div className=\"mt-2 bg-gray-50 p-2 rounded-lg\">\n            <div className=\"flex justify-between text-xs font-medium text-gray-700 mb-1\">\n              <span>Avanzamento Complessivo Cavi</span>\n              <span className={`font-bold ${\n                stats.percentualeInstallazione >= 80 ? 'text-amber-700' :\n                stats.percentualeInstallazione >= 60 ? 'text-orange-700' :\n                stats.percentualeInstallazione >= 40 ? 'text-yellow-700' : 'text-emerald-700'\n              }`}>\n                {stats.percentualeInstallazione.toFixed(1)}%\n              </span>\n            </div>\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <div\n                className={`h-2 rounded-full transition-all duration-500 ease-in-out ${\n                  stats.percentualeInstallazione >= 80 ? 'bg-gradient-to-r from-amber-500 to-amber-600' :\n                  stats.percentualeInstallazione >= 60 ? 'bg-gradient-to-r from-orange-500 to-orange-600' :\n                  stats.percentualeInstallazione >= 40 ? 'bg-gradient-to-r from-yellow-500 to-yellow-600' :\n                  'bg-gradient-to-r from-emerald-500 to-emerald-600'\n                }`}\n                style={{ width: `${Math.min(stats.percentualeInstallazione, 100)}%` }}\n              />\n            </div>\n            <div className=\"flex justify-between text-xs text-gray-500 mt-0.5\">\n              <span>Metri installati vs totali disponibili</span>\n              <span>{(stats.metriTotali - stats.metriInstallati).toLocaleString()}m rimanenti</span>\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAyBe,SAAS,eAAe,EACrC,IAAI,EACJ,YAAY,EACZ,SAAS,EACT,iBAAiB,EACG;IACpB,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACpB,MAAM,YAAY,KAAK,MAAM;QAC7B,MAAM,gBAAgB,aAAa,MAAM;QAEzC,sBAAsB;QACtB,MAAM,aAAa,aAAa,MAAM,CAAC,CAAA,IACrC,EAAE,mBAAmB,KAAK,gBACzB,EAAE,YAAY,IAAI,EAAE,YAAY,GAAG,KACnC,EAAE,eAAe,IAAI,EAAE,eAAe,GAAG,GAC1C,MAAM;QAER,MAAM,UAAU,aAAa,MAAM,CAAC,CAAA,IAClC,EAAE,mBAAmB,KAAK,YAC1B,MAAM;QAER,MAAM,eAAe,gBAAgB,aAAa;QAElD,oBAAoB;QACpB,MAAM,YAAY,aAAa,MAAM,CAAC,CAAA;YACpC,MAAM,eAAe,EAAE,YAAY,IAAI,EAAE,YAAY,IAAI;YACzD,OAAO,iBAAiB,EAAE,uBAAuB;;QACnD,GAAG,MAAM;QAET,MAAM,wBAAwB,aAAa,MAAM,CAAC,CAAA;YAChD,MAAM,eAAe,EAAE,YAAY,IAAI,EAAE,YAAY,IAAI;YACzD,OAAO,iBAAiB,KAAK,iBAAiB,EAAE,qBAAqB;;QACvE,GAAG,MAAM;QAET,MAAM,eAAe,aAAa,MAAM,CAAC,CAAA;YACvC,MAAM,eAAe,EAAE,YAAY,IAAI,EAAE,YAAY,IAAI;YACzD,OAAO,iBAAiB,KAAK,CAAC,EAAE,YAAY,GAAG,KAAK,EAAE,eAAe,GAAG,CAAC;QAC3E,GAAG,MAAM;QAET,uBAAuB;QACvB,MAAM,cAAc,aAAa,MAAM,CAAC,CAAA,IACtC,EAAE,WAAW,KAAK,QAClB,EAAE,WAAW,KAAK,QAClB,EAAE,WAAW,KAAK,eAClB,MAAM;QAER,qBAAqB;QACrB,MAAM,cAAc,aAAa,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,aAAa,IAAI,CAAC,GAAG;QAClF,MAAM,kBAAkB,aAAa,MAAM,CAAC,CAAC,KAAK;YAChD,MAAM,QAAQ,EAAE,YAAY,IAAI,EAAE,eAAe,IAAI;YACrD,OAAO,MAAM;QACf,GAAG;QAEH,4EAA4E;QAC5E,MAAM,eAAe,CAAC,MAAc,OAAe,OAAe;YAChE,gCAAgC;YAChC,MAAM,KAAK,IAAK,iBAAiB;;YACjC,MAAM,KAAK,IAAK,yBAAyB;;YACzC,MAAM,KAAK,IAAK,2BAA2B;;YAE3C,iCAAiC;YACjC,IAAI,SAAS,GAAG,OAAO;YAEvB,6CAA6C;YAC7C,MAAM,uBAAuB,CAAC,QAAQ,KAAK,IAAI;YAC/C,MAAM,sBAAsB,CAAC,QAAQ,KAAK,IAAI,CAAC,KAAK,EAAE;YACtD,MAAM,oBAAoB,QAAQ,CAAC,KAAK,KAAK,EAAE;YAC/C,MAAM,aAAa,uBAAuB,sBAAsB;YAEhE,qDAAqD;YACrD,MAAM,eAAe,OAAO,CAAC,KAAK,KAAK,EAAE;YAEzC,yCAAyC;YACzC,MAAM,MAAM,AAAC,aAAa,eAAgB;YAE1C,OAAO,KAAK,KAAK,CAAC,MAAM,OAAO,IAAI,yBAAyB;;QAC9D;QAEA,MAAM,2BAA2B,aAAa,eAAe,YAAY,WAAW;QAEpF,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;IACF,GAAG;QAAC;QAAM;KAAa;IAEvB,6BAA6B;IAG7B,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW;kBACf,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;;8BAErB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,8OAAC;gCAAK,WAAU;0CAAyC;;;;;;4BACxD,mCACC,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;;oCAAmD;oCAC9E;;;;;;;;;;;;;;;;;;8BAOd,8OAAC;oBAAI,WAAU;;sCAGb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAsC,MAAM,aAAa;;;;;;sDACxE,8OAAC;4CAAI,WAAU;;gDAA2B;gDAAI,MAAM,SAAS;gDAAC;;;;;;;;;;;;;;;;;;;sCAKlE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAoC,MAAM,UAAU;;;;;;sDACnE,8OAAC;4CAAI,WAAU;sDAAyB;;;;;;;;;;;;;;;;;;sCAK5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAqC,MAAM,OAAO;;;;;;sDACjE,8OAAC;4CAAI,WAAU;sDAA0B;;;;;;;;;;;;;;;;;;sCAK7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAmC,MAAM,YAAY;;;;;;sDACpE,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAK3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAmC,MAAM,SAAS;;;;;;sDACjE,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAK3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAqC,MAAM,WAAW;;;;;;sDACrE,8OAAC;4CAAI,WAAU;sDAA0B;;;;;;;;;;;;;;;;;;sCAK7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;;8CAEjB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;;gDAAqC,MAAM,eAAe,CAAC,cAAc;gDAAG;;;;;;;sDAC3F,8OAAC;4CAAI,WAAU;;gDAA0B;gDAAI,MAAM,WAAW,CAAC,cAAc;gDAAG;;;;;;;;;;;;;;;;;;;;;;;;;gBAOrF,MAAM,aAAa,GAAG,mBACrB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAK;;;;;;8CACN,8OAAC;oCAAK,WAAW,CAAC,UAAU,EAC1B,MAAM,wBAAwB,IAAI,KAAK,mBACvC,MAAM,wBAAwB,IAAI,KAAK,oBACvC,MAAM,wBAAwB,IAAI,KAAK,oBAAoB,oBAC3D;;wCACC,MAAM,wBAAwB,CAAC,OAAO,CAAC;wCAAG;;;;;;;;;;;;;sCAG/C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAW,CAAC,yDAAyD,EACnE,MAAM,wBAAwB,IAAI,KAAK,iDACvC,MAAM,wBAAwB,IAAI,KAAK,mDACvC,MAAM,wBAAwB,IAAI,KAAK,mDACvC,oDACA;gCACF,OAAO;oCAAE,OAAO,GAAG,KAAK,GAAG,CAAC,MAAM,wBAAwB,EAAE,KAAK,CAAC,CAAC;gCAAC;;;;;;;;;;;sCAGxE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAK;;;;;;8CACN,8OAAC;;wCAAM,CAAC,MAAM,WAAW,GAAG,MAAM,eAAe,EAAE,cAAc;wCAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlF", "debugId": null}}, {"offset": {"line": 7328, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/cavi/InserisciMetriDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  <PERSON>alogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Badge } from '@/components/ui/badge'\nimport { Loader2, AlertCircle, Calculator, Search, CheckCircle, AlertTriangle, X } from 'lucide-react'\nimport { Cavo } from '@/types'\nimport { parcoCaviApi, caviApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useToast } from '@/hooks/use-toast'\n\nimport ModificaBobinaDialog from './ModificaBobinaDialog'\n\ninterface Bobina {\n  id_bobina: string\n  numero_bobina?: string\n  tipologia: string\n  formazione: string\n  metri_residui: number\n  fornitore?: string\n  stato_bobina?: string\n}\n\ninterface FormData {\n  metri_posati: string\n  id_bobina: string\n}\n\ninterface FormErrors {\n  metri_posati?: string\n  id_bobina?: string\n}\n\ninterface FormWarnings {\n  metri_posati?: string\n}\n\ninterface InserisciMetriDialogProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  cantiere?: { id_cantiere: number; commessa: string } | null\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\nexport default function InserisciMetriDialog({\n  open,\n  onClose,\n  cavo,\n  cantiere: cantiereProp,\n  onSuccess,\n  onError\n}: InserisciMetriDialogProps) {\n  const { cantiere: cantiereAuth } = useAuth()\n\n  // Usa il cantiere passato come prop o quello dall'auth come fallback\n  const cantiere = cantiereProp || cantiereAuth\n\n  // Stati per il form\n  const [formData, setFormData] = useState<FormData>({\n    metri_posati: '',\n    id_bobina: ''\n  })\n\n  // Debug formData changes\n  useEffect(() => {\n    console.log('📊 InserisciMetriDialog: FormData aggiornato:', {\n      hasMetri: !!formData.metri_posati,\n      hasBobina: !!formData.id_bobina,\n      metri_posati: formData.metri_posati,\n      id_bobina: formData.id_bobina\n    })\n  }, [formData])\n  const [formErrors, setFormErrors] = useState<FormErrors>({})\n  const [formWarnings, setFormWarnings] = useState<FormWarnings>({})\n  const [saving, setSaving] = useState(false)\n\n  // Stati per bobine\n  const [bobine, setBobine] = useState<Bobina[]>([])\n  const [bobineLoading, setBobineLoading] = useState(false)\n\n  // Stati per la ricerca delle bobine\n  const [searchText, setSearchText] = useState('')\n\n  // Stati per dialoghi\n  const [showModificaBobinaDialog, setShowModificaBobinaDialog] = useState(false)\n\n  // Carica bobine quando si apre il dialog\n  useEffect(() => {\n    if (open && cantiere) {\n      loadBobine()\n    }\n  }, [open, cantiere])\n\n  // Reset form quando si apre il dialog\n  useEffect(() => {\n    if (open && cavo) {\n      setFormData({\n        metri_posati: '',\n        id_bobina: ''\n      })\n      setFormErrors({})\n      setFormWarnings({})\n      setSearchText('')\n    }\n  }, [open, cavo])\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = (idBobina: string) => {\n    if (!idBobina || idBobina === 'BOBINA_VUOTA') return 'VUOTA'\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1]\n    }\n    // Cerca nella lista bobine per ottenere il numero_bobina\n    const bobina = bobine.find(b => b.id_bobina === idBobina)\n    return bobina ? bobina.numero_bobina || idBobina : idBobina\n  }\n\n  // Filtra le bobine compatibili\n  const getBobineCompatibili = () => {\n    if (!cavo) return []\n\n    return bobine.filter(bobina => {\n      const isCompatible = bobina.tipologia === cavo.tipologia &&\n                          bobina.sezione === cavo.sezione\n      const matchesSearch = searchText === '' ||\n                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase()) ||\n                           (bobina.tipologia && bobina.tipologia.toLowerCase().includes(searchText.toLowerCase())) ||\n                           (bobina.numero_bobina && bobina.numero_bobina.toLowerCase().includes(searchText.toLowerCase()))\n      return isCompatible && matchesSearch && bobina.metri_residui > 0\n    })\n  }\n\n  // Filtra le bobine incompatibili\n  const getBobineIncompatibili = () => {\n    if (!cavo) return []\n\n    return bobine.filter(bobina => {\n      const isIncompatible = bobina.tipologia !== cavo.tipologia ||\n                            bobina.sezione !== cavo.sezione\n      const matchesSearch = searchText === '' ||\n                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase()) ||\n                           (bobina.tipologia && bobina.tipologia.toLowerCase().includes(searchText.toLowerCase())) ||\n                           (bobina.numero_bobina && bobina.numero_bobina.toLowerCase().includes(searchText.toLowerCase()))\n      return isIncompatible && matchesSearch && bobina.metri_residui > 0\n    })\n  }\n\n  // Filtra bobine in base alla ricerca\n  const bobineFiltrate = bobine.filter(bobina => {\n    if (!searchText) return true\n    const searchLower = searchText.toLowerCase()\n    return (\n      bobina.id_bobina.toLowerCase().includes(searchLower) ||\n      bobina.tipologia.toLowerCase().includes(searchLower) ||\n      bobina.formazione.toLowerCase().includes(searchLower) ||\n      getBobinaNumber(bobina.id_bobina).toLowerCase().includes(searchLower)\n    )\n  })\n\n  // Usa le funzioni di filtro per evitare duplicazioni\n  const bobineCompatibili = getBobineCompatibili()\n  const bobineIncompatibili = getBobineIncompatibili()\n\n  // Gestisce la selezione di una bobina\n  const handleBobinaSelect = (bobina: Bobina) => {\n    console.log('🎯 Bobina selezionata:', {\n      id: bobina.id_bobina,\n      numero: getBobinaNumber(bobina.id_bobina),\n      tipologia: bobina.tipologia,\n      formazione: bobina.formazione,\n      metri_residui: bobina.metri_residui\n    })\n\n    setFormData(prev => ({ ...prev, id_bobina: bobina.id_bobina }))\n    setFormErrors(prev => ({ ...prev, id_bobina: undefined }))\n  }\n\n  // Gestisce la selezione di BOBINA VUOTA\n  // Permette di posare il cavo senza assegnare una bobina specifica\n  // Il cavo potrà essere collegato a una bobina in seguito tramite ModificaBobinaDialog\n  const handleBobinaVuotaSelect = () => {\n    console.log('🎯 BOBINA VUOTA selezionata - cavo sarà posato senza bobina specifica')\n    setFormData(prev => ({ ...prev, id_bobina: 'BOBINA_VUOTA' }))\n\n    // Reset completo degli errori - rimuovi la chiave invece di impostarla vuota\n    setFormErrors(prev => {\n      const newErrors = { ...prev }\n      delete newErrors.id_bobina  // Rimuovi completamente la chiave\n      return newErrors\n    })\n  }\n\n  // Carica bobine quando si apre il dialog\n  useEffect(() => {\n    if (open && cavo) {\n\n      if (cantiere) {\n        loadBobine()\n      } else {\n      }\n\n      setFormData({\n        metri_posati: '0', // Default a 0 come nell'originale\n        id_bobina: ''\n      })\n      setFormErrors({})\n      setFormWarnings({})\n      setSearchText('')\n    }\n  }, [open, cavo, cantiere])\n\n  // Validazione real-time dei metri posati\n  useEffect(() => {\n    if (formData.metri_posati && cavo) {\n      validateMetriPosati(parseFloat(formData.metri_posati))\n    } else {\n      setFormErrors(prev => ({ ...prev, metri_posati: undefined }))\n      setFormWarnings(prev => ({ ...prev, metri_posati: undefined }))\n    }\n  }, [formData.metri_posati, cavo])\n\n  const validateMetriPosati = (metri: number) => {\n    if (!cavo) return\n\n    let errors: FormErrors = { ...formErrors }\n    let warnings: FormWarnings = { ...formWarnings }\n\n    // Rimuovi errori/warning precedenti per metri_posati\n    delete errors.metri_posati\n    delete warnings.metri_posati\n\n    // NESSUN ERRORE BLOCCANTE - Solo warning informativi\n    // I warning non impediscono il salvataggio\n\n    if (metri > (cavo.metri_teorici || 0) * 1.1) {\n      warnings.metri_posati = `Attenzione: i metri posati superano del 10% i metri teorici (${cavo.metri_teorici}m)`\n    } else if (metri > (cavo.metri_teorici || 0)) {\n      warnings.metri_posati = 'Metratura superiore ai metri teorici'\n    }\n\n    setFormErrors(errors)\n    setFormWarnings(warnings)\n  }\n\n  const loadBobine = async () => {\n    console.log({\n      cavo: !!cavo,\n      cantiere: !!cantiere,\n      cavoId: cavo?.id_cavo,\n      cantiereId: cantiere?.id_cantiere\n    })\n\n    if (!cavo || !cantiere) {\n      return\n    }\n\n    try {\n      setBobineLoading(true)\n\n      // Carica tutte le bobine disponibili\n      const response = await parcoCaviApi.getBobine(cantiere.id_cantiere)\n\n      // Gestisce diversi formati di risposta\n      let bobineData = []\n      if (Array.isArray(response)) {\n        bobineData = response\n      } else if (response && Array.isArray(response.data)) {\n        bobineData = response.data\n      } else if (response && response.bobine && Array.isArray(response.bobine)) {\n        bobineData = response.bobine\n      } else {\n        throw new Error('Formato risposta API non valido')\n      }\n\n      // Filtra solo per stato (disponibile o in uso) e metri residui > 0\n      const bobineUtilizzabili = bobineData.filter((bobina: Bobina) =>\n        bobina.stato_bobina !== 'Terminata' &&\n        bobina.stato_bobina !== 'Over' &&\n        bobina.metri_residui > 0\n      )\n\n      if (cavo) {\n        console.log({\n          tipologia: cavo.tipologia,\n          sezione: cavo.sezione\n        })\n\n        // Separa bobine compatibili e incompatibili\n        const bobineCompatibili = bobineUtilizzabili.filter(bobina => {\n          const isCompatible = bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione\n          return isCompatible\n        })\n        const bobineNonCompatibili = bobineUtilizzabili.filter(bobina =>\n          !(bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione)\n        )\n\n        // Ordina entrambi gli array per metri residui (decrescente)\n        bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui)\n        bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui)\n\n        // Concatena gli array: prima le compatibili, poi le non compatibili\n        const bobineOrdinate = [...bobineCompatibili, ...bobineNonCompatibili]\n        setBobine(bobineOrdinate)\n      } else {\n        // Se non c'è un cavo selezionato, mostra tutte le bobine ordinate per metri residui\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui)\n        setBobine(bobineUtilizzabili)\n      }\n    } catch (error: any) {\n      console.log({\n        message: error.message,\n        response: error.response,\n        status: error.response?.status,\n        data: error.response?.data\n      })\n\n      // Non mostrare errore se è solo un problema di rete, permetti di usare BOBINA_VUOTA\n      if (error.response?.status !== 404) {\n        onError('Errore nel caricamento delle bobine. Puoi comunque usare BOBINA VUOTA.')\n      }\n      setBobine([])\n    } finally {\n      setBobineLoading(false)\n    }\n  }\n\n\n\n  const handleSave = async () => {\n    console.log({\n      cavo: cavo?.id_cavo,\n      metri_posati: formData.metri_posati,\n      id_bobina: formData.id_bobina\n    })\n\n    if (!cavo) {\n      return\n    }\n\n    // Validazioni di base (solo controlli essenziali)\n    if (!formData.metri_posati || parseFloat(formData.metri_posati) < 0) {\n      onError('Inserire metri posati validi (≥ 0)')\n      return\n    }\n\n    if (!formData.id_bobina) {\n      onError('Selezionare una bobina o BOBINA VUOTA')\n      return\n    }\n\n    const metri = parseFloat(formData.metri_posati)\n\n    // Gestione stato OVER per bobine reali (NON BLOCCANTE)\n    if (formData.id_bobina !== 'BOBINA_VUOTA') {\n      const bobina = bobine.find(b => b.id_bobina === formData.id_bobina)\n      if (bobina && metri > bobina.metri_residui) {\n        // OVER state - salva comunque ma avvisa\n        // Il salvataggio continua - lo stato OVER viene gestito dal backend\n      }\n    }\n\n    try {\n      setSaving(true)\n\n      if (!cantiere) {\n        throw new Error('Cantiere non selezionato')\n      }\n\n      // Aggiorna metri posati tramite API\n      console.log({\n        cantiere: cantiere.id_cantiere,\n        cavo: cavo.id_cavo,\n        metri: metri,\n        bobina: formData.id_bobina,  // Mostra il valore reale che viene passato\n        isBobinaVuota: formData.id_bobina === 'BOBINA_VUOTA'\n      })\n\n      await caviApi.updateMetriPosati(\n        cantiere.id_cantiere,\n        cavo.id_cavo,\n        metri,\n        formData.id_bobina,  // Passa sempre il valore, incluso 'BOBINA_VUOTA'\n        true  // force_over: true per permettere bobine incompatibili e OVER state\n      )\n\n      onSuccess(`Metri posati aggiornati con successo per il cavo ${cavo.id_cavo}: ${metri}m`)\n      onClose()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante il salvataggio dei metri posati'\n      onError(errorMessage)\n    } finally {\n      setSaving(false)\n    }\n  }\n\n  const handleClose = () => {\n    if (!saving) {\n      setFormData({ metri_posati: '', id_bobina: '' })\n      setFormErrors({})\n      setFormWarnings({})\n      setSearchText('')\n      onClose()\n    }\n  }\n\n  if (!cavo) return null\n\n  return (\n    <>\n      <Dialog open={open} onOpenChange={handleClose}>\n        <DialogContent className=\"max-w-7xl h-[90vh] flex flex-col\">\n          <DialogHeader className=\"flex-shrink-0\">\n            <DialogTitle className=\"flex items-center gap-2\">\n              <Calculator className=\"h-5 w-5\" />\n              Inserisci Metri Posati - {cavo.id_cavo}\n            </DialogTitle>\n            <DialogDescription>\n              Inserisci i metri effettivamente posati per il cavo e seleziona una bobina o usa BOBINA VUOTA\n            </DialogDescription>\n          </DialogHeader>\n\n          <div className=\"flex-1 overflow-y-auto space-y-6\">\n            {/* Sezione informazioni cavo e metri posati */}\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-4\">\n              {/* Informazioni cavo - 2/3 della larghezza */}\n              <div className=\"lg:col-span-2\">\n                <div className=\"p-4 bg-blue-50 rounded-lg border-2 border-blue-200\">\n                  <h3 className=\"font-semibold text-blue-800 mb-3\">Informazioni Cavo</h3>\n                  <div className=\"grid grid-cols-2 gap-3 text-sm\">\n                    <div><strong>Tipologia:</strong> {cavo.tipologia || 'N/A'}</div>\n                    <div><strong>Da:</strong> {cavo.ubicazione_partenza || 'N/A'}</div>\n                    <div><strong>Formazione:</strong> {cavo.sezione || 'N/A'}</div>\n                    <div><strong>A:</strong> {cavo.ubicazione_arrivo || 'N/A'}</div>\n                    <div><strong>Metri teorici:</strong> {cavo.metri_teorici || 'N/A'} m</div>\n                    <div><strong>Già posati:</strong> {cavo.metratura_reale || 0} m</div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Campo metri posati - 1/3 della larghezza */}\n              <div className=\"lg:col-span-1\">\n                <div className=\"p-4 bg-blue-50 rounded-lg border-2 border-blue-300 h-full\">\n                  <h3 className=\"font-semibold text-blue-800 mb-3\">Metri da Installare</h3>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"metri\" className=\"text-sm font-medium\">\n                      Metri Posati\n                    </Label>\n                    <div className=\"relative\">\n                      <Input\n                        id=\"metri\"\n                        type=\"number\"\n                        value={formData.metri_posati}\n                        onChange={(e) => setFormData(prev => ({ ...prev, metri_posati: e.target.value }))}\n                        placeholder=\"Inserisci metri posati\"\n                        disabled={saving}\n                        step=\"0.1\"\n                        min=\"0\"\n                        className=\"text-lg font-bold text-center border-2 border-blue-400 focus:border-blue-600\"\n                        autoFocus\n                      />\n                      <span className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-lg font-bold text-blue-600\">\n                        m\n                      </span>\n                    </div>\n                    {formErrors.metri_posati && (\n                      <p className=\"text-sm text-red-600\">{formErrors.metri_posati}</p>\n                    )}\n                    {formWarnings.metri_posati && (\n                      <p className=\"text-sm text-amber-600\">{formWarnings.metri_posati}</p>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Selezione bobina */}\n            <div className=\"space-y-4\">\n              <h3 className=\"font-semibold text-blue-800 text-lg\">Selezione Bobina</h3>\n\n              {/* Controlli di ricerca e BOBINA VUOTA */}\n              <div className=\"p-4 bg-gray-50 rounded-lg\">\n                <div className=\"grid grid-cols-1 sm:grid-cols-12 gap-3 items-center\">\n                  {/* Campo di ricerca - 5 colonne */}\n                  <div className=\"sm:col-span-5\">\n                    <div className=\"relative\">\n                      <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                      <Input\n                        placeholder=\"ID, tipologia, formazione...\"\n                        value={searchText}\n                        onChange={(e) => setSearchText(e.target.value)}\n                        className=\"pl-10\"\n                        disabled={saving}\n                      />\n                      {searchText && (\n                        <Button\n                          type=\"button\"\n                          variant=\"ghost\"\n                          size=\"sm\"\n                          className=\"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0\"\n                          onClick={() => setSearchText('')}\n                        >\n                          <X className=\"h-4 w-4\" />\n                        </Button>\n                      )}\n                    </div>\n                  </div>\n\n                  {/* Pulsante BOBINA VUOTA - 7 colonne */}\n                  <div className=\"sm:col-span-7\">\n                    <Button\n                      type=\"button\"\n                      variant={formData.id_bobina === 'BOBINA_VUOTA' ? 'default' : 'outline'}\n                      className={`w-full h-10 font-bold flex items-center justify-center gap-2 ${\n                        formData.id_bobina === 'BOBINA_VUOTA'\n                          ? 'bg-green-600 hover:bg-green-700 text-white'\n                          : 'border-blue-400 text-blue-700 hover:bg-blue-50'\n                      }`}\n                      onClick={handleBobinaVuotaSelect}\n                      disabled={saving}\n                    >\n                      {formData.id_bobina === 'BOBINA_VUOTA' && (\n                        <CheckCircle className=\"h-5 w-5\" />\n                      )}\n                      BOBINA VUOTA\n                    </Button>\n                  </div>\n                </div>\n\n                {/* Messaggio informativo per BOBINA VUOTA */}\n                {formData.id_bobina === 'BOBINA_VUOTA' && (\n                  <div className=\"mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg\">\n                    <div className=\"flex items-start gap-2\">\n                      <CheckCircle className=\"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0\" />\n                      <div className=\"text-sm text-blue-800\">\n                        <p className=\"font-medium\">Bobina Vuota Selezionata</p>\n                        <p className=\"mt-1\">\n                          Il cavo sarà posato senza assegnazione di bobina specifica.\n                          Potrai collegarlo a una bobina in seguito tramite la funzione \"Modifica Bobina\".\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n\n              {bobineLoading ? (\n                <div className=\"flex items-center justify-center p-8\">\n                  <Loader2 className=\"h-6 w-6 animate-spin mr-2\" />\n                  <span>Caricamento bobine...</span>\n                </div>\n              ) : (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  {/* Bobine compatibili */}\n                  <div>\n                    <h4 className=\"font-medium text-green-700 mb-2 flex items-center gap-2\">\n                      <CheckCircle className=\"h-4 w-4\" />\n                      Bobine Compatibili ({bobineCompatibili.length})\n                    </h4>\n                    <div className=\"max-h-72 overflow-y-auto border rounded-lg\">\n                      {bobineCompatibili.length === 0 ? (\n                        <div className=\"p-4 text-center text-gray-500\">\n                          Nessuna bobina compatibile trovata\n                        </div>\n                      ) : (\n                        <div className=\"divide-y\">\n                          {bobineCompatibili.map((bobina) => (\n                            <div\n                              key={bobina.id_bobina}\n                              className={`p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ${\n                                formData.id_bobina === bobina.id_bobina\n                                  ? 'bg-green-100 border-green-500 shadow-md'\n                                  : 'border-gray-200 hover:bg-green-50 hover:border-green-300'\n                              }`}\n                              onClick={() => handleBobinaSelect(bobina)}\n                            >\n                              <div className=\"flex justify-between items-center\">\n                                <div className=\"flex items-center gap-3 flex-1 min-w-0\">\n                                  {formData.id_bobina === bobina.id_bobina && (\n                                    <CheckCircle className=\"h-5 w-5 text-green-600 flex-shrink-0\" />\n                                  )}\n                                  <div className=\"font-bold text-base min-w-fit\">\n                                    {getBobinaNumber(bobina.id_bobina)}\n                                  </div>\n                                  <div className=\"text-sm text-gray-600 truncate\">\n                                    {bobina.tipologia} - {bobina.sezione}\n                                  </div>\n                                </div>\n                                <Badge variant=\"outline\" className=\"bg-green-100 text-green-800 border-green-300 font-medium min-w-fit\">\n                                  {bobina.metri_residui}m\n                                </Badge>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n\n                  {/* Bobine incompatibili */}\n                  <div>\n                    <h4 className=\"font-medium text-amber-700 mb-2 flex items-center gap-2\">\n                      <AlertTriangle className=\"h-4 w-4\" />\n                      Bobine Incompatibili ({bobineIncompatibili.length})\n                    </h4>\n                    <div className=\"max-h-72 overflow-y-auto border rounded-lg\">\n                      {bobineIncompatibili.length === 0 ? (\n                        <div className=\"p-4 text-center text-gray-500\">\n                          Nessuna bobina incompatibile trovata\n                        </div>\n                      ) : (\n                        <div className=\"divide-y\">\n                          {bobineIncompatibili.map((bobina) => (\n                            <div\n                              key={bobina.id_bobina}\n                              className={`p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ${\n                                formData.id_bobina === bobina.id_bobina\n                                  ? 'bg-amber-100 border-amber-500 shadow-md'\n                                  : 'border-gray-200 hover:bg-amber-50 hover:border-amber-300'\n                              }`}\n                              onClick={() => handleBobinaSelect(bobina)}\n                            >\n                              <div className=\"flex justify-between items-center\">\n                                <div className=\"flex items-center gap-3 flex-1 min-w-0\">\n                                  {formData.id_bobina === bobina.id_bobina && (\n                                    <CheckCircle className=\"h-5 w-5 text-amber-600 flex-shrink-0\" />\n                                  )}\n                                  <div className=\"font-bold text-base min-w-fit\">\n                                    {getBobinaNumber(bobina.id_bobina)}\n                                  </div>\n                                  <div className=\"text-sm text-gray-600 truncate\">\n                                    {bobina.tipologia} - {bobina.sezione}\n                                  </div>\n                                </div>\n                                <Badge variant=\"outline\" className=\"bg-amber-100 text-amber-800 border-amber-300 font-medium min-w-fit\">\n                                  {bobina.metri_residui}m\n                                </Badge>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {bobine.length === 0 && !bobineLoading && (\n                <Alert className=\"border-amber-200 bg-amber-50\">\n                  <AlertTriangle className=\"h-4 w-4 text-amber-600\" />\n                  <AlertDescription className=\"text-amber-800\">\n                    Non ci sono bobine disponibili. Puoi procedere con BOBINA VUOTA o aggiungere prima una nuova bobina.\n                  </AlertDescription>\n                </Alert>\n              )}\n\n              {formErrors.id_bobina && (\n                <Alert variant=\"destructive\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  <AlertDescription>{formErrors.id_bobina}</AlertDescription>\n                </Alert>\n              )}\n            </div>\n          </div>\n\n          <DialogFooter className=\"flex-shrink-0 border-t pt-4 mt-4 flex justify-between items-center\">\n            {/* Pulsante Modifica Bobina a sinistra (solo se cavo è installato) */}\n            <div>\n              {cavo.stato_installazione === 'installato' && cavo.id_bobina && (\n                <Button\n                  variant=\"outline\"\n                  onClick={() => {\n                    setShowModificaBobinaDialog(true)\n                  }}\n                  disabled={saving}\n                  className=\"text-blue-600 border-blue-300 hover:bg-blue-50\"\n                >\n                  Modifica Bobina\n                </Button>\n              )}\n            </div>\n\n            {/* Pulsanti principali a destra */}\n            <div className=\"flex gap-2\">\n              <Button variant=\"outline\" onClick={handleClose} disabled={saving}>\n                Annulla\n              </Button>\n              <Button\n                onClick={handleSave}\n                disabled={\n                  saving ||\n                  !formData.metri_posati ||\n                  parseFloat(formData.metri_posati) < 0 ||\n                  !formData.id_bobina\n                }\n                className=\"bg-mariner-600 hover:bg-mariner-700 text-white disabled:bg-gray-400 disabled:text-gray-200\"\n              >\n                {saving && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n                Salva\n              </Button>\n            </div>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* Dialog per modifica bobina */}\n      <ModificaBobinaDialog\n        open={showModificaBobinaDialog}\n        onClose={() => setShowModificaBobinaDialog(false)}\n        cavo={cavo}\n        onSuccess={(message) => {\n          onSuccess(message)\n          setShowModificaBobinaDialog(false)\n          onClose() // Chiudi anche il dialog principale dopo il successo\n        }}\n        onError={onError}\n      />\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAGA;AAtBA;;;;;;;;;;;;;AAyDe,SAAS,qBAAqB,EAC3C,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,UAAU,YAAY,EACtB,SAAS,EACT,OAAO,EACmB;IAC1B,MAAM,EAAE,UAAU,YAAY,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEzC,qEAAqE;IACrE,MAAM,WAAW,gBAAgB;IAEjC,oBAAoB;IACpB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,cAAc;QACd,WAAW;IACb;IAEA,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,iDAAiD;YAC3D,UAAU,CAAC,CAAC,SAAS,YAAY;YACjC,WAAW,CAAC,CAAC,SAAS,SAAS;YAC/B,cAAc,SAAS,YAAY;YACnC,WAAW,SAAS,SAAS;QAC/B;IACF,GAAG;QAAC;KAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,CAAC;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,CAAC;IAChE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,mBAAmB;IACnB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,oCAAoC;IACpC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBAAqB;IACrB,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzE,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,UAAU;YACpB;QACF;IACF,GAAG;QAAC;QAAM;KAAS;IAEnB,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,MAAM;YAChB,YAAY;gBACV,cAAc;gBACd,WAAW;YACb;YACA,cAAc,CAAC;YACf,gBAAgB,CAAC;YACjB,cAAc;QAChB;IACF,GAAG;QAAC;QAAM;KAAK;IAEf,gEAAgE;IAChE,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,YAAY,aAAa,gBAAgB,OAAO;QACrD,8DAA8D;QAC9D,IAAI,YAAY,SAAS,QAAQ,CAAC,OAAO;YACvC,OAAO,SAAS,KAAK,CAAC,KAAK,CAAC,EAAE;QAChC;QACA,yDAAyD;QACzD,MAAM,SAAS,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;QAChD,OAAO,SAAS,OAAO,aAAa,IAAI,WAAW;IACrD;IAEA,+BAA+B;IAC/B,MAAM,uBAAuB;QAC3B,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,OAAO,OAAO,MAAM,CAAC,CAAA;YACnB,MAAM,eAAe,OAAO,SAAS,KAAK,KAAK,SAAS,IACpC,OAAO,OAAO,KAAK,KAAK,OAAO;YACnD,MAAM,gBAAgB,eAAe,MAChB,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC7D,OAAO,SAAS,IAAI,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAClF,OAAO,aAAa,IAAI,OAAO,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;YAChH,OAAO,gBAAgB,iBAAiB,OAAO,aAAa,GAAG;QACjE;IACF;IAEA,iCAAiC;IACjC,MAAM,yBAAyB;QAC7B,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,OAAO,OAAO,MAAM,CAAC,CAAA;YACnB,MAAM,iBAAiB,OAAO,SAAS,KAAK,KAAK,SAAS,IACpC,OAAO,OAAO,KAAK,KAAK,OAAO;YACrD,MAAM,gBAAgB,eAAe,MAChB,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC7D,OAAO,SAAS,IAAI,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAClF,OAAO,aAAa,IAAI,OAAO,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;YAChH,OAAO,kBAAkB,iBAAiB,OAAO,aAAa,GAAG;QACnE;IACF;IAEA,qCAAqC;IACrC,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA;QACnC,IAAI,CAAC,YAAY,OAAO;QACxB,MAAM,cAAc,WAAW,WAAW;QAC1C,OACE,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACxC,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACxC,OAAO,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACzC,gBAAgB,OAAO,SAAS,EAAE,WAAW,GAAG,QAAQ,CAAC;IAE7D;IAEA,qDAAqD;IACrD,MAAM,oBAAoB;IAC1B,MAAM,sBAAsB;IAE5B,sCAAsC;IACtC,MAAM,qBAAqB,CAAC;QAC1B,QAAQ,GAAG,CAAC,0BAA0B;YACpC,IAAI,OAAO,SAAS;YACpB,QAAQ,gBAAgB,OAAO,SAAS;YACxC,WAAW,OAAO,SAAS;YAC3B,YAAY,OAAO,UAAU;YAC7B,eAAe,OAAO,aAAa;QACrC;QAEA,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,WAAW,OAAO,SAAS;YAAC,CAAC;QAC7D,cAAc,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,WAAW;YAAU,CAAC;IAC1D;IAEA,wCAAwC;IACxC,kEAAkE;IAClE,sFAAsF;IACtF,MAAM,0BAA0B;QAC9B,QAAQ,GAAG,CAAC;QACZ,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,WAAW;YAAe,CAAC;QAE3D,6EAA6E;QAC7E,cAAc,CAAA;YACZ,MAAM,YAAY;gBAAE,GAAG,IAAI;YAAC;YAC5B,OAAO,UAAU,SAAS,CAAE,kCAAkC;;YAC9D,OAAO;QACT;IACF;IAEA,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,MAAM;YAEhB,IAAI,UAAU;gBACZ;YACF,OAAO,CACP;YAEA,YAAY;gBACV,cAAc;gBACd,WAAW;YACb;YACA,cAAc,CAAC;YACf,gBAAgB,CAAC;YACjB,cAAc;QAChB;IACF,GAAG;QAAC;QAAM;QAAM;KAAS;IAEzB,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,YAAY,IAAI,MAAM;YACjC,oBAAoB,WAAW,SAAS,YAAY;QACtD,OAAO;YACL,cAAc,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,cAAc;gBAAU,CAAC;YAC3D,gBAAgB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,cAAc;gBAAU,CAAC;QAC/D;IACF,GAAG;QAAC,SAAS,YAAY;QAAE;KAAK;IAEhC,MAAM,sBAAsB,CAAC;QAC3B,IAAI,CAAC,MAAM;QAEX,IAAI,SAAqB;YAAE,GAAG,UAAU;QAAC;QACzC,IAAI,WAAyB;YAAE,GAAG,YAAY;QAAC;QAE/C,qDAAqD;QACrD,OAAO,OAAO,YAAY;QAC1B,OAAO,SAAS,YAAY;QAE5B,qDAAqD;QACrD,2CAA2C;QAE3C,IAAI,QAAQ,CAAC,KAAK,aAAa,IAAI,CAAC,IAAI,KAAK;YAC3C,SAAS,YAAY,GAAG,CAAC,6DAA6D,EAAE,KAAK,aAAa,CAAC,EAAE,CAAC;QAChH,OAAO,IAAI,QAAQ,CAAC,KAAK,aAAa,IAAI,CAAC,GAAG;YAC5C,SAAS,YAAY,GAAG;QAC1B;QAEA,cAAc;QACd,gBAAgB;IAClB;IAEA,MAAM,aAAa;QACjB,QAAQ,GAAG,CAAC;YACV,MAAM,CAAC,CAAC;YACR,UAAU,CAAC,CAAC;YACZ,QAAQ,MAAM;YACd,YAAY,UAAU;QACxB;QAEA,IAAI,CAAC,QAAQ,CAAC,UAAU;YACtB;QACF;QAEA,IAAI;YACF,iBAAiB;YAEjB,qCAAqC;YACrC,MAAM,WAAW,MAAM,iHAAA,CAAA,eAAY,CAAC,SAAS,CAAC,SAAS,WAAW;YAElE,uCAAuC;YACvC,IAAI,aAAa,EAAE;YACnB,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,aAAa;YACf,OAAO,IAAI,YAAY,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBACnD,aAAa,SAAS,IAAI;YAC5B,OAAO,IAAI,YAAY,SAAS,MAAM,IAAI,MAAM,OAAO,CAAC,SAAS,MAAM,GAAG;gBACxE,aAAa,SAAS,MAAM;YAC9B,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;YAEA,mEAAmE;YACnE,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAC,SAC5C,OAAO,YAAY,KAAK,eACxB,OAAO,YAAY,KAAK,UACxB,OAAO,aAAa,GAAG;YAGzB,IAAI,MAAM;gBACR,QAAQ,GAAG,CAAC;oBACV,WAAW,KAAK,SAAS;oBACzB,SAAS,KAAK,OAAO;gBACvB;gBAEA,4CAA4C;gBAC5C,MAAM,oBAAoB,mBAAmB,MAAM,CAAC,CAAA;oBAClD,MAAM,eAAe,OAAO,SAAS,KAAK,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,KAAK,OAAO;oBAC3F,OAAO;gBACT;gBACA,MAAM,uBAAuB,mBAAmB,MAAM,CAAC,CAAA,SACrD,CAAC,CAAC,OAAO,SAAS,KAAK,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,KAAK,OAAO;gBAG1E,4DAA4D;gBAC5D,kBAAkB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,aAAa,GAAG,EAAE,aAAa;gBAClE,qBAAqB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,aAAa,GAAG,EAAE,aAAa;gBAErE,oEAAoE;gBACpE,MAAM,iBAAiB;uBAAI;uBAAsB;iBAAqB;gBACtE,UAAU;YACZ,OAAO;gBACL,oFAAoF;gBACpF,mBAAmB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,aAAa,GAAG,EAAE,aAAa;gBACnE,UAAU;YACZ;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,GAAG,CAAC;gBACV,SAAS,MAAM,OAAO;gBACtB,UAAU,MAAM,QAAQ;gBACxB,QAAQ,MAAM,QAAQ,EAAE;gBACxB,MAAM,MAAM,QAAQ,EAAE;YACxB;YAEA,oFAAoF;YACpF,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,QAAQ;YACV;YACA,UAAU,EAAE;QACd,SAAU;YACR,iBAAiB;QACnB;IACF;IAIA,MAAM,aAAa;QACjB,QAAQ,GAAG,CAAC;YACV,MAAM,MAAM;YACZ,cAAc,SAAS,YAAY;YACnC,WAAW,SAAS,SAAS;QAC/B;QAEA,IAAI,CAAC,MAAM;YACT;QACF;QAEA,kDAAkD;QAClD,IAAI,CAAC,SAAS,YAAY,IAAI,WAAW,SAAS,YAAY,IAAI,GAAG;YACnE,QAAQ;YACR;QACF;QAEA,IAAI,CAAC,SAAS,SAAS,EAAE;YACvB,QAAQ;YACR;QACF;QAEA,MAAM,QAAQ,WAAW,SAAS,YAAY;QAE9C,uDAAuD;QACvD,IAAI,SAAS,SAAS,KAAK,gBAAgB;YACzC,MAAM,SAAS,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK,SAAS,SAAS;YAClE,IAAI,UAAU,QAAQ,OAAO,aAAa,EAAE;YAC1C,wCAAwC;YACxC,oEAAoE;YACtE;QACF;QAEA,IAAI;YACF,UAAU;YAEV,IAAI,CAAC,UAAU;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,oCAAoC;YACpC,QAAQ,GAAG,CAAC;gBACV,UAAU,SAAS,WAAW;gBAC9B,MAAM,KAAK,OAAO;gBAClB,OAAO;gBACP,QAAQ,SAAS,SAAS;gBAC1B,eAAe,SAAS,SAAS,KAAK;YACxC;YAEA,MAAM,iHAAA,CAAA,UAAO,CAAC,iBAAiB,CAC7B,SAAS,WAAW,EACpB,KAAK,OAAO,EACZ,OACA,SAAS,SAAS,EAClB,KAAM,oEAAoE;;YAG5E,UAAU,CAAC,iDAAiD,EAAE,KAAK,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;YACvF;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,QAAQ;YACX,YAAY;gBAAE,cAAc;gBAAI,WAAW;YAAG;YAC9C,cAAc,CAAC;YACf,gBAAgB,CAAC;YACjB,cAAc;YACd;QACF;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE;;0BACE,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAM,cAAc;0BAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,8OAAC,kIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC,8MAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAY;wCACR,KAAK,OAAO;;;;;;;8CAExC,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAKrB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFAAI,8OAAC;kFAAO;;;;;;oEAAmB;oEAAE,KAAK,SAAS,IAAI;;;;;;;0EACpD,8OAAC;;kFAAI,8OAAC;kFAAO;;;;;;oEAAY;oEAAE,KAAK,mBAAmB,IAAI;;;;;;;0EACvD,8OAAC;;kFAAI,8OAAC;kFAAO;;;;;;oEAAoB;oEAAE,KAAK,OAAO,IAAI;;;;;;;0EACnD,8OAAC;;kFAAI,8OAAC;kFAAO;;;;;;oEAAW;oEAAE,KAAK,iBAAiB,IAAI;;;;;;;0EACpD,8OAAC;;kFAAI,8OAAC;kFAAO;;;;;;oEAAuB;oEAAE,KAAK,aAAa,IAAI;oEAAM;;;;;;;0EAClE,8OAAC;;kFAAI,8OAAC;kFAAO;;;;;;oEAAoB;oEAAE,KAAK,eAAe,IAAI;oEAAE;;;;;;;;;;;;;;;;;;;;;;;;sDAMnE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAQ,WAAU;0EAAsB;;;;;;0EAGvD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEACJ,IAAG;wEACH,MAAK;wEACL,OAAO,SAAS,YAAY;wEAC5B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oFAAE,GAAG,IAAI;oFAAE,cAAc,EAAE,MAAM,CAAC,KAAK;gFAAC,CAAC;wEAC/E,aAAY;wEACZ,UAAU;wEACV,MAAK;wEACL,KAAI;wEACJ,WAAU;wEACV,SAAS;;;;;;kFAEX,8OAAC;wEAAK,WAAU;kFAAsF;;;;;;;;;;;;4DAIvG,WAAW,YAAY,kBACtB,8OAAC;gEAAE,WAAU;0EAAwB,WAAW,YAAY;;;;;;4DAE7D,aAAa,YAAY,kBACxB,8OAAC;gEAAE,WAAU;0EAA0B,aAAa,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQ1E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsC;;;;;;sDAGpD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEAEb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,sMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,8OAAC,iIAAA,CAAA,QAAK;wEACJ,aAAY;wEACZ,OAAO;wEACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wEAC7C,WAAU;wEACV,UAAU;;;;;;oEAEX,4BACC,8OAAC,kIAAA,CAAA,SAAM;wEACL,MAAK;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS,IAAM,cAAc;kFAE7B,cAAA,8OAAC,4LAAA,CAAA,IAAC;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sEAOrB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAS,SAAS,SAAS,KAAK,iBAAiB,YAAY;gEAC7D,WAAW,CAAC,6DAA6D,EACvE,SAAS,SAAS,KAAK,iBACnB,+CACA,kDACJ;gEACF,SAAS;gEACT,UAAU;;oEAET,SAAS,SAAS,KAAK,gCACtB,8OAAC,2NAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;oEACvB;;;;;;;;;;;;;;;;;;gDAOP,SAAS,SAAS,KAAK,gCACtB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;kFAAc;;;;;;kFAC3B,8OAAC;wEAAE,WAAU;kFAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCAU7B,8BACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,8OAAC;8DAAK;;;;;;;;;;;iEAGR,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAAY;gEACd,kBAAkB,MAAM;gEAAC;;;;;;;sEAEhD,8OAAC;4DAAI,WAAU;sEACZ,kBAAkB,MAAM,KAAK,kBAC5B,8OAAC;gEAAI,WAAU;0EAAgC;;;;;qFAI/C,8OAAC;gEAAI,WAAU;0EACZ,kBAAkB,GAAG,CAAC,CAAC,uBACtB,8OAAC;wEAEC,WAAW,CAAC,2DAA2D,EACrE,SAAS,SAAS,KAAK,OAAO,SAAS,GACnC,4CACA,4DACJ;wEACF,SAAS,IAAM,mBAAmB;kFAElC,cAAA,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;wFACZ,SAAS,SAAS,KAAK,OAAO,SAAS,kBACtC,8OAAC,2NAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;sGAEzB,8OAAC;4FAAI,WAAU;sGACZ,gBAAgB,OAAO,SAAS;;;;;;sGAEnC,8OAAC;4FAAI,WAAU;;gGACZ,OAAO,SAAS;gGAAC;gGAAI,OAAO,OAAO;;;;;;;;;;;;;8FAGxC,8OAAC,iIAAA,CAAA,QAAK;oFAAC,SAAQ;oFAAU,WAAU;;wFAChC,OAAO,aAAa;wFAAC;;;;;;;;;;;;;uEArBrB,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;8DAgCjC,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,wNAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;gEAAY;gEACd,oBAAoB,MAAM;gEAAC;;;;;;;sEAEpD,8OAAC;4DAAI,WAAU;sEACZ,oBAAoB,MAAM,KAAK,kBAC9B,8OAAC;gEAAI,WAAU;0EAAgC;;;;;qFAI/C,8OAAC;gEAAI,WAAU;0EACZ,oBAAoB,GAAG,CAAC,CAAC,uBACxB,8OAAC;wEAEC,WAAW,CAAC,2DAA2D,EACrE,SAAS,SAAS,KAAK,OAAO,SAAS,GACnC,4CACA,4DACJ;wEACF,SAAS,IAAM,mBAAmB;kFAElC,cAAA,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;wFACZ,SAAS,SAAS,KAAK,OAAO,SAAS,kBACtC,8OAAC,2NAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;sGAEzB,8OAAC;4FAAI,WAAU;sGACZ,gBAAgB,OAAO,SAAS;;;;;;sGAEnC,8OAAC;4FAAI,WAAU;;gGACZ,OAAO,SAAS;gGAAC;gGAAI,OAAO,OAAO;;;;;;;;;;;;;8FAGxC,8OAAC,iIAAA,CAAA,QAAK;oFAAC,SAAQ;oFAAU,WAAU;;wFAChC,OAAO,aAAa;wFAAC;;;;;;;;;;;;;uEArBrB,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;wCAiCpC,OAAO,MAAM,KAAK,KAAK,CAAC,+BACvB,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;8DACzB,8OAAC,iIAAA,CAAA,mBAAgB;oDAAC,WAAU;8DAAiB;;;;;;;;;;;;wCAMhD,WAAW,SAAS,kBACnB,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;;8DACb,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC,iIAAA,CAAA,mBAAgB;8DAAE,WAAW,SAAS;;;;;;;;;;;;;;;;;;;;;;;;sCAM/C,8OAAC,kIAAA,CAAA,eAAY;4BAAC,WAAU;;8CAEtB,8OAAC;8CACE,KAAK,mBAAmB,KAAK,gBAAgB,KAAK,SAAS,kBAC1D,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS;4CACP,4BAA4B;wCAC9B;wCACA,UAAU;wCACV,WAAU;kDACX;;;;;;;;;;;8CAOL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS;4CAAa,UAAU;sDAAQ;;;;;;sDAGlE,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UACE,UACA,CAAC,SAAS,YAAY,IACtB,WAAW,SAAS,YAAY,IAAI,KACpC,CAAC,SAAS,SAAS;4CAErB,WAAU;;gDAET,wBAAU,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASvE,8OAAC,kJAAA,CAAA,UAAoB;gBACnB,MAAM;gBACN,SAAS,IAAM,4BAA4B;gBAC3C,MAAM;gBACN,WAAW,CAAC;oBACV,UAAU;oBACV,4BAA4B;oBAC5B,UAAU,qDAAqD;;gBACjE;gBACA,SAAS;;;;;;;;AAIjB", "debugId": null}}, {"offset": {"line": 8541, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/cavi/CollegamentiDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport * as VisuallyHidden from '@radix-ui/react-visually-hidden'\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Zap, CheckCircle, AlertTriangle, X } from 'lucide-react'\nimport { Cavo } from '@/types'\nimport { caviApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useToastActions } from '@/components/ui/toast-notification'\n\ninterface CollegamentiDialogProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  onSuccess?: () => void\n  onError?: (error: string) => void\n}\n\ninterface ConfirmDialogState {\n  open: boolean\n  type: 'partenza' | 'arrivo' | 'entrambi' | null\n  title: string\n  description: string\n}\n\ninterface ConfirmDisconnectDialogProps {\n  open: boolean\n  onClose: () => void\n  onConfirm: () => void\n  title: string\n  description: string\n  isLoading: boolean\n  isDangerous?: boolean\n}\n\nfunction ConfirmDisconnectDialog({\n  open,\n  onClose,\n  onConfirm,\n  title,\n  description,\n  isLoading,\n  isDangerous = false\n}: ConfirmDisconnectDialogProps) {\n  const [showFinalConfirmation, setShowFinalConfirmation] = useState(false)\n\n  useEffect(() => {\n    if (!open) {\n      setShowFinalConfirmation(false)\n    }\n  }, [open])\n\n  useEffect(() => {\n    const handleEsc = (e: KeyboardEvent) => {\n      if (e.key === 'Escape' && open && !isLoading) {\n        onClose()\n      }\n    }\n\n    if (open) {\n      document.addEventListener('keydown', handleEsc)\n      return () => document.removeEventListener('keydown', handleEsc)\n    }\n  }, [open, onClose, isLoading])\n\n  const handleInitialConfirm = () => {\n    if (isDangerous) {\n      setShowFinalConfirmation(true)\n    } else {\n      onConfirm()\n    }\n  }\n\n  const handleFinalConfirm = () => {\n    onConfirm()\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <DialogContent\n        className=\"sm:max-w-[400px]\"\n        aria-describedby=\"confirm-disconnect-description\"\n      >\n        {!showFinalConfirmation ? (\n          <>\n            <DialogHeader>\n              <DialogTitle className=\"flex items-center gap-2 text-orange-600\">\n                <AlertTriangle className=\"h-5 w-5\" />\n                {title}\n              </DialogTitle>\n              <DialogDescription id=\"confirm-disconnect-description\">\n                {description}\n              </DialogDescription>\n            </DialogHeader>\n\n            <div className=\"py-4\">\n              <Alert className=\"border-orange-200 bg-orange-50\">\n                <AlertTriangle className=\"h-4 w-4 text-orange-600\" />\n                <AlertDescription className=\"text-orange-800\">\n                  <strong>Attenzione:</strong> Questa azione modificherà lo stato del collegamento del cavo.\n                </AlertDescription>\n              </Alert>\n            </div>\n\n            <DialogFooter className=\"gap-2\">\n              <Button\n                variant=\"outline\"\n                onClick={onClose}\n                disabled={isLoading}\n                className=\"flex-1 hover:bg-gray-50\"\n              >\n                Annulla\n              </Button>\n              <Button\n                variant=\"outline\"\n                onClick={handleInitialConfirm}\n                disabled={isLoading}\n                className=\"flex-1 bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100 hover:border-orange-300\"\n              >\n                <AlertTriangle className=\"mr-2 h-4 w-4\" />\n                {isDangerous ? 'Procedi' : 'Conferma'}\n              </Button>\n            </DialogFooter>\n          </>\n        ) : (\n          <>\n            <DialogHeader>\n              <DialogTitle className=\"text-center text-orange-600\">\n                Conferma Finale\n              </DialogTitle>\n            </DialogHeader>\n\n            <div className=\"py-4 text-center\">\n              <div className=\"mx-auto w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-4\">\n                <AlertTriangle className=\"h-6 w-6 text-orange-600\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                Sei veramente sicuro?\n              </h3>\n              <p className=\"text-sm text-gray-600 mb-4\">\n                Questa azione scollegherà <strong>entrambi i lati</strong> del cavo.\n              </p>\n              <div className=\"bg-orange-50 border border-orange-200 rounded-md p-3\">\n                <p className=\"text-sm text-orange-800 font-medium\">\n                  ⚠️ Operazione irreversibile\n                </p>\n              </div>\n            </div>\n\n            <DialogFooter className=\"gap-2\">\n              <Button\n                variant=\"outline\"\n                onClick={() => setShowFinalConfirmation(false)}\n                disabled={isLoading}\n                className=\"flex-1\"\n              >\n                No, Annulla\n              </Button>\n              <Button\n                variant=\"outline\"\n                onClick={handleFinalConfirm}\n                disabled={isLoading}\n                className=\"flex-1 bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100 hover:border-orange-300\"\n              >\n                {isLoading ? (\n                  <>\n                    <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                    Scollegando...\n                  </>\n                ) : (\n                  'Sì, Scollega'\n                )}\n              </Button>\n            </DialogFooter>\n          </>\n        )}\n      </DialogContent>\n    </Dialog>\n  )\n}\n\nfunction CollegamentiDialog({\n  open,\n  onClose,\n  cavo,\n  onSuccess,\n  onError\n}: CollegamentiDialogProps) {\n  const { cantiere } = useAuth()\n  const toast = useToastActions()\n  const [selectedResponsabile, setSelectedResponsabile] = useState('cantiere')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const [confirmDialog, setConfirmDialog] = useState<ConfirmDialogState>({\n    open: false,\n    type: null,\n    title: '',\n    description: ''\n  })\n\n  const dialogRef = useRef<HTMLDivElement>(null)\n  const firstFocusableRef = useRef<HTMLButtonElement>(null)\n  const lastFocusableRef = useRef<HTMLButtonElement>(null)\n\n  const [screenReaderAnnouncement, setScreenReaderAnnouncement] = useState('')\n\n  useEffect(() => {\n    if (open && cavo) {\n      setSelectedResponsabile('cantiere')\n      setError('')\n    }\n  }, [open, cavo])\n\n  useEffect(() => {\n    if (open && dialogRef.current) {\n      const focusableElements = dialogRef.current.querySelectorAll(\n        'button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex=\"-1\"])'\n      )\n\n      if (focusableElements.length > 0) {\n        (focusableElements[0] as HTMLElement).focus()\n      }\n    }\n  }, [open])\n\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if (!open || loading || confirmDialog.open) return\n\n      switch (e.key) {\n        case 'Escape':\n          onClose()\n          break\n\n        case 'Tab':\n          const focusableElements = dialogRef.current?.querySelectorAll(\n            'button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex=\"-1\"])'\n          )\n\n          if (focusableElements && focusableElements.length > 0) {\n            const firstElement = focusableElements[0] as HTMLElement\n            const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement\n\n            if (e.shiftKey && document.activeElement === firstElement) {\n              e.preventDefault()\n              lastElement.focus()\n            } else if (!e.shiftKey && document.activeElement === lastElement) {\n              e.preventDefault()\n              firstElement.focus()\n            }\n          }\n          break\n\n        case '1':\n        case '2':\n        case '3':\n          if (!loading) {\n            e.preventDefault()\n            const num = parseInt(e.key)\n            if (num === 1) handleCollegaPartenza()\n            else if (num === 2) handleCollegaArrivo()\n            else if (num === 3) handleCollegaEntrambi()\n          }\n          break\n      }\n    }\n\n    if (open) {\n      document.addEventListener('keydown', handleKeyDown)\n      return () => document.removeEventListener('keydown', handleKeyDown)\n    }\n  }, [open, loading, confirmDialog.open])\n\n  const announceToScreenReader = (message: string) => {\n    setScreenReaderAnnouncement(message)\n    setTimeout(() => setScreenReaderAnnouncement(''), 1000)\n  }\n\n  const getStatoCollegamento = () => {\n    if (!cavo) return { stato: 'non_collegato', descrizione: 'Non collegato' }\n    \n    const collegamento = cavo.collegamento || cavo.collegamenti || 0\n    \n    switch (collegamento) {\n      case 1:\n        return { stato: 'partenza', descrizione: '🟢⚪ Collegato lato partenza' }\n      case 2:\n        return { stato: 'arrivo', descrizione: '⚪🟢 Collegato lato arrivo' }\n      case 3:\n        return { stato: 'completo', descrizione: '🟢🟢 Completamente collegato' }\n      default:\n        return { stato: 'non_collegato', descrizione: '⚪⚪ Non collegato' }\n    }\n  }\n\n  const handleCollegaPartenza = () => {\n    if (!cavo) return\n\n    const statoAttuale = getStatoCollegamento()\n\n    if (statoAttuale.stato === 'partenza' || statoAttuale.stato === 'completo') {\n      setConfirmDialog({\n        open: true,\n        type: 'partenza',\n        title: 'Scollega lato partenza',\n        description: `Vuoi scollegare il lato partenza del cavo ${cavo.id_cavo}?`\n      })\n    } else {\n      executeCollegaPartenza()\n    }\n  }\n\n  const executeCollegaPartenza = async () => {\n    if (!cavo || !cantiere) return\n\n    try {\n      setLoading(true)\n      setError('')\n      announceToScreenReader('Collegamento in corso...')\n\n      await caviApi.collegaCavo(\n        cantiere.id_cantiere,\n        cavo.id_cavo,\n        'partenza',\n        selectedResponsabile\n      )\n\n      const message = `Collegamento lato partenza completato per il cavo ${cavo.id_cavo}`\n      announceToScreenReader(message)\n      toast.success(\"Successo\", message)\n\n      if (onSuccess) onSuccess()\n      onClose()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante il collegamento'\n      setError(errorMessage)\n      announceToScreenReader(`Errore: ${errorMessage}`)\n      if (onError) onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleCollegaArrivo = () => {\n    if (!cavo) return\n\n    const statoAttuale = getStatoCollegamento()\n\n    if (statoAttuale.stato === 'arrivo' || statoAttuale.stato === 'completo') {\n      setConfirmDialog({\n        open: true,\n        type: 'arrivo',\n        title: 'Scollega lato arrivo',\n        description: `Vuoi scollegare il lato arrivo del cavo ${cavo.id_cavo}?`\n      })\n    } else {\n      executeCollegaArrivo()\n    }\n  }\n\n  const executeCollegaArrivo = async () => {\n    if (!cavo || !cantiere) return\n\n    try {\n      setLoading(true)\n      setError('')\n      announceToScreenReader('Collegamento in corso...')\n\n      await caviApi.collegaCavo(\n        cantiere.id_cantiere,\n        cavo.id_cavo,\n        'arrivo',\n        selectedResponsabile\n      )\n\n      const message = `Collegamento lato arrivo completato per il cavo ${cavo.id_cavo}`\n      announceToScreenReader(message)\n      toast.success(\"Successo\", message)\n\n      if (onSuccess) onSuccess()\n      onClose()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante il collegamento'\n      setError(errorMessage)\n      announceToScreenReader(`Errore: ${errorMessage}`)\n      if (onError) onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleCollegaEntrambi = () => {\n    if (!cavo) return\n\n    const statoAttuale = getStatoCollegamento()\n\n    if (statoAttuale.stato === 'completo') {\n      setConfirmDialog({\n        open: true,\n        type: 'entrambi',\n        title: 'Scollega entrambi i lati',\n        description: `Vuoi scollegare completamente il cavo ${cavo.id_cavo}? Questa operazione rimuoverà tutti i collegamenti.`\n      })\n    } else {\n      executeCollegaEntrambi()\n    }\n  }\n\n  const executeCollegaEntrambi = async () => {\n    if (!cavo || !cantiere) return\n\n    try {\n      setLoading(true)\n      setError('')\n      announceToScreenReader('Collegamento entrambi i lati in corso...')\n\n      await caviApi.collegaCavo(\n        cantiere.id_cantiere,\n        cavo.id_cavo,\n        'entrambi',\n        selectedResponsabile\n      )\n\n      const message = `Collegamento completo per il cavo ${cavo.id_cavo}`\n      announceToScreenReader(message)\n      toast.success(\"Successo\", message)\n\n      if (onSuccess) onSuccess()\n      onClose()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante il collegamento'\n      setError(errorMessage)\n      announceToScreenReader(`Errore: ${errorMessage}`)\n      if (onError) onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const executeDisconnect = async () => {\n    if (!cavo || !cantiere || !confirmDialog.type) return\n\n    try {\n      setLoading(true)\n      setError('')\n      announceToScreenReader('Scollegamento in corso...')\n\n      await caviApi.scollegaCavo(\n        cantiere.id_cantiere,\n        cavo.id_cavo,\n        confirmDialog.type === 'entrambi' ? undefined : confirmDialog.type\n      )\n\n      const latoText = confirmDialog.type === 'entrambi' ? '' : ` lato ${confirmDialog.type}`\n      const message = `Scollegamento${latoText} completato per il cavo ${cavo.id_cavo}`\n      announceToScreenReader(message)\n      toast.success(\"Successo\", message)\n\n      if (onSuccess) onSuccess()\n      setConfirmDialog({ open: false, type: null, title: '', description: '' })\n      onClose()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante lo scollegamento'\n      setError(errorMessage)\n      announceToScreenReader(`Errore: ${errorMessage}`)\n      if (onError) onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (!cavo) return null\n\n  const statoCollegamento = getStatoCollegamento()\n  const isInstalled = (cavo.metri_posati || cavo.metratura_reale || 0) > 0\n\n  return (\n    <>\n      <VisuallyHidden.Root>\n        <div aria-live=\"polite\" aria-atomic=\"true\">\n          {screenReaderAnnouncement}\n        </div>\n      </VisuallyHidden.Root>\n\n      <Dialog open={open} onOpenChange={onClose}>\n        <DialogContent\n          className=\"sm:max-w-[600px] max-h-[90vh] overflow-y-auto\"\n          ref={dialogRef}\n          aria-describedby=\"collegamenti-description\"\n        >\n          <DialogHeader>\n            <DialogTitle className=\"flex items-center gap-2 text-blue-600\">\n              <Zap className=\"h-5 w-5\" />\n              Gestione Collegamenti - {cavo.id_cavo}\n            </DialogTitle>\n            <DialogDescription id=\"collegamenti-description\">\n              Gestisci i collegamenti del cavo {cavo.id_cavo}. Usa i tasti 1, 2, 3 per azioni rapide.\n            </DialogDescription>\n          </DialogHeader>\n\n          <div className=\"space-y-6\">\n            {/* Informazioni cavo */}\n            <div className=\"p-4 bg-blue-50 rounded-lg border-2 border-blue-200\">\n              <div className=\"text-sm font-medium text-blue-800\">\n                Informazioni Cavo / Tipologia: {cavo.tipologia || 'N/A'} / Da: {cavo.ubicazione_partenza || 'N/A'} / Formazione: {cavo.sezione || 'N/A'} / A: {cavo.ubicazione_arrivo || 'N/A'} / Metri Posati: {cavo.metratura_reale || 0} m\n              </div>\n            </div>\n\n            {/* Stato attuale con icone migliorate */}\n            <div className=\"p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <Label className=\"text-sm font-medium text-gray-700\">Stato Collegamento</Label>\n                  <div className=\"mt-1 text-lg font-semibold flex items-center gap-2\">\n                    {statoCollegamento.stato === 'completo' && <CheckCircle className=\"h-5 w-5 text-green-600\" />}\n                    {statoCollegamento.stato === 'non_collegato' && <AlertCircle className=\"h-5 w-5 text-gray-400\" />}\n                    {(statoCollegamento.stato === 'partenza' || statoCollegamento.stato === 'arrivo') && <AlertTriangle className=\"h-5 w-5 text-orange-500\" />}\n                    {statoCollegamento.descrizione}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {!isInstalled && (\n              <Alert className=\"border-orange-200 bg-orange-50\">\n                <AlertCircle className=\"h-4 w-4 text-orange-600\" />\n                <AlertDescription className=\"text-orange-800\">\n                  <strong>Attenzione:</strong> Il cavo deve essere installato prima di poter essere collegato.\n                </AlertDescription>\n              </Alert>\n            )}\n\n            {error && (\n              <Alert variant=\"destructive\">\n                <AlertCircle className=\"h-4 w-4\" />\n                <AlertDescription>{error}</AlertDescription>\n              </Alert>\n            )}\n\n            {isInstalled && (\n              <>\n                {/* Selezione responsabile semplificata */}\n                <div className=\"space-y-3\">\n                  <Label className=\"text-sm font-medium\">Responsabile Collegamento</Label>\n                  <div className=\"p-3 bg-gray-50 rounded-lg border\">\n                    <div className=\"flex items-center gap-2\">\n                      <CheckCircle className=\"h-4 w-4 text-green-600\" />\n                      <span className=\"font-medium\">Cantiere</span>\n                    </div>\n                    <p className=\"text-sm text-gray-600 mt-1\">\n                      Collegamento eseguito dal responsabile del cantiere\n                    </p>\n                  </div>\n                </div>\n\n                {/* Azioni di collegamento con design migliorato */}\n                <div className=\"space-y-4\">\n                  <Label className=\"text-sm font-medium\">Azioni Disponibili</Label>\n\n                  <div className=\"grid grid-cols-1 gap-3\">\n                    <Button\n                      ref={firstFocusableRef}\n                      onClick={handleCollegaPartenza}\n                      disabled={loading}\n                      className=\"w-full h-12 text-left justify-start bg-green-50 text-green-700 border-green-200 hover:bg-green-100 hover:border-green-300\"\n                      variant=\"outline\"\n                    >\n                      <div className=\"flex items-center justify-between w-full\">\n                        <div className=\"flex items-center gap-3\">\n                          <div className=\"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\">\n                            <span className=\"text-sm font-bold text-green-700\">1</span>\n                          </div>\n                          <div>\n                            <div className=\"font-medium\">\n                              {statoCollegamento.stato === 'partenza' || statoCollegamento.stato === 'completo'\n                                ? 'Scollega Partenza'\n                                : 'Collega Partenza'}\n                            </div>\n                            <div className=\"text-xs text-green-600\">\n                              {statoCollegamento.stato === 'partenza' || statoCollegamento.stato === 'completo'\n                                ? 'Rimuovi collegamento lato partenza'\n                                : 'Connetti il lato partenza del cavo'}\n                            </div>\n                          </div>\n                        </div>\n                        {loading ? <Loader2 className=\"h-4 w-4 animate-spin\" /> : <Zap className=\"h-4 w-4\" />}\n                      </div>\n                    </Button>\n\n                    <Button\n                      onClick={handleCollegaArrivo}\n                      disabled={loading}\n                      className=\"w-full h-12 text-left justify-start bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 hover:border-blue-300\"\n                      variant=\"outline\"\n                    >\n                      <div className=\"flex items-center justify-between w-full\">\n                        <div className=\"flex items-center gap-3\">\n                          <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\n                            <span className=\"text-sm font-bold text-blue-700\">2</span>\n                          </div>\n                          <div>\n                            <div className=\"font-medium\">\n                              {statoCollegamento.stato === 'arrivo' || statoCollegamento.stato === 'completo'\n                                ? 'Scollega Arrivo'\n                                : 'Collega Arrivo'}\n                            </div>\n                            <div className=\"text-xs text-blue-600\">\n                              {statoCollegamento.stato === 'arrivo' || statoCollegamento.stato === 'completo'\n                                ? 'Rimuovi collegamento lato arrivo'\n                                : 'Connetti il lato arrivo del cavo'}\n                            </div>\n                          </div>\n                        </div>\n                        {loading ? <Loader2 className=\"h-4 w-4 animate-spin\" /> : <Zap className=\"h-4 w-4\" />}\n                      </div>\n                    </Button>\n\n                    <Button\n                      onClick={handleCollegaEntrambi}\n                      disabled={loading}\n                      className=\"w-full h-12 text-left justify-start bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100 hover:border-purple-300\"\n                      variant=\"outline\"\n                    >\n                      <div className=\"flex items-center justify-between w-full\">\n                        <div className=\"flex items-center gap-3\">\n                          <div className=\"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center\">\n                            <span className=\"text-sm font-bold text-purple-700\">3</span>\n                          </div>\n                          <div>\n                            <div className=\"font-medium\">\n                              {statoCollegamento.stato === 'completo'\n                                ? 'Scollega Completamente'\n                                : 'Collega Entrambi'}\n                            </div>\n                            <div className=\"text-xs text-purple-600\">\n                              {statoCollegamento.stato === 'completo'\n                                ? 'Rimuovi tutti i collegamenti'\n                                : 'Connetti entrambi i lati del cavo'}\n                            </div>\n                          </div>\n                        </div>\n                        {loading ? <Loader2 className=\"h-4 w-4 animate-spin\" /> : <Zap className=\"h-4 w-4\" />}\n                      </div>\n                    </Button>\n                  </div>\n                </div>\n              </>\n            )}\n          </div>\n\n          <DialogFooter>\n            <Button\n              ref={lastFocusableRef}\n              variant=\"outline\"\n              onClick={onClose}\n              disabled={loading}\n              className=\"hover:bg-gray-50\"\n            >\n              <X className=\"mr-2 h-4 w-4\" />\n              Chiudi\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      <ConfirmDisconnectDialog\n        open={confirmDialog.open}\n        onClose={() => setConfirmDialog({ open: false, type: null, title: '', description: '' })}\n        onConfirm={executeDisconnect}\n        title={confirmDialog.title}\n        description={confirmDialog.description}\n        isLoading={loading}\n        isDangerous={confirmDialog.type === 'entrambi'}\n      />\n    </>\n  )\n}\n\nexport default CollegamentiDialog\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAQA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AApBA;;;;;;;;;;;;AA+CA,SAAS,wBAAwB,EAC/B,IAAI,EACJ,OAAO,EACP,SAAS,EACT,KAAK,EACL,WAAW,EACX,SAAS,EACT,cAAc,KAAK,EACU;IAC7B,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,MAAM;YACT,yBAAyB;QAC3B;IACF,GAAG;QAAC;KAAK;IAET,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,CAAC;YACjB,IAAI,EAAE,GAAG,KAAK,YAAY,QAAQ,CAAC,WAAW;gBAC5C;YACF;QACF;QAEA,IAAI,MAAM;YACR,SAAS,gBAAgB,CAAC,WAAW;YACrC,OAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;QACvD;IACF,GAAG;QAAC;QAAM;QAAS;KAAU;IAE7B,MAAM,uBAAuB;QAC3B,IAAI,aAAa;YACf,yBAAyB;QAC3B,OAAO;YACL;QACF;IACF;IAEA,MAAM,qBAAqB;QACzB;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YACZ,WAAU;YACV,oBAAiB;sBAEhB,CAAC,sCACA;;kCACE,8OAAC,kIAAA,CAAA,eAAY;;0CACX,8OAAC,kIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCACxB;;;;;;;0CAEH,8OAAC,kIAAA,CAAA,oBAAiB;gCAAC,IAAG;0CACnB;;;;;;;;;;;;kCAIL,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC,iIAAA,CAAA,mBAAgB;oCAAC,WAAU;;sDAC1B,8OAAC;sDAAO;;;;;;wCAAoB;;;;;;;;;;;;;;;;;;kCAKlC,8OAAC,kIAAA,CAAA,eAAY;wBAAC,WAAU;;0CACtB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU;gCACV,WAAU;0CACX;;;;;;0CAGD,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU;gCACV,WAAU;;kDAEV,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCACxB,cAAc,YAAY;;;;;;;;;;;;;;6CAKjC;;kCACE,8OAAC,kIAAA,CAAA,eAAY;kCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;sCAA8B;;;;;;;;;;;kCAKvD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;;;;;;0CAE3B,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,8OAAC;gCAAE,WAAU;;oCAA6B;kDACd,8OAAC;kDAAO;;;;;;oCAAwB;;;;;;;0CAE5D,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAsC;;;;;;;;;;;;;;;;;kCAMvD,8OAAC,kIAAA,CAAA,eAAY;wBAAC,WAAU;;0CACtB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS,IAAM,yBAAyB;gCACxC,UAAU;gCACV,WAAU;0CACX;;;;;;0CAGD,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,0BACC;;sDACE,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAA8B;;mDAInD;;;;;;;;;;;;;;;;;;;;;;;;AASlB;AAEA,SAAS,mBAAmB,EAC1B,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,SAAS,EACT,OAAO,EACiB;IACxB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,QAAQ,CAAA,GAAA,iJAAA,CAAA,kBAAe,AAAD;IAC5B,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;QACrE,MAAM;QACN,MAAM;QACN,OAAO;QACP,aAAa;IACf;IAEA,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IACpD,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAEnD,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,MAAM;YAChB,wBAAwB;YACxB,SAAS;QACX;IACF,GAAG;QAAC;QAAM;KAAK;IAEf,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,UAAU,OAAO,EAAE;YAC7B,MAAM,oBAAoB,UAAU,OAAO,CAAC,gBAAgB,CAC1D;YAGF,IAAI,kBAAkB,MAAM,GAAG,GAAG;gBAC/B,iBAAiB,CAAC,EAAE,CAAiB,KAAK;YAC7C;QACF;IACF,GAAG;QAAC;KAAK;IAET,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,IAAI,CAAC,QAAQ,WAAW,cAAc,IAAI,EAAE;YAE5C,OAAQ,EAAE,GAAG;gBACX,KAAK;oBACH;oBACA;gBAEF,KAAK;oBACH,MAAM,oBAAoB,UAAU,OAAO,EAAE,iBAC3C;oBAGF,IAAI,qBAAqB,kBAAkB,MAAM,GAAG,GAAG;wBACrD,MAAM,eAAe,iBAAiB,CAAC,EAAE;wBACzC,MAAM,cAAc,iBAAiB,CAAC,kBAAkB,MAAM,GAAG,EAAE;wBAEnE,IAAI,EAAE,QAAQ,IAAI,SAAS,aAAa,KAAK,cAAc;4BACzD,EAAE,cAAc;4BAChB,YAAY,KAAK;wBACnB,OAAO,IAAI,CAAC,EAAE,QAAQ,IAAI,SAAS,aAAa,KAAK,aAAa;4BAChE,EAAE,cAAc;4BAChB,aAAa,KAAK;wBACpB;oBACF;oBACA;gBAEF,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH,IAAI,CAAC,SAAS;wBACZ,EAAE,cAAc;wBAChB,MAAM,MAAM,SAAS,EAAE,GAAG;wBAC1B,IAAI,QAAQ,GAAG;6BACV,IAAI,QAAQ,GAAG;6BACf,IAAI,QAAQ,GAAG;oBACtB;oBACA;YACJ;QACF;QAEA,IAAI,MAAM;YACR,SAAS,gBAAgB,CAAC,WAAW;YACrC,OAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;QACvD;IACF,GAAG;QAAC;QAAM;QAAS,cAAc,IAAI;KAAC;IAEtC,MAAM,yBAAyB,CAAC;QAC9B,4BAA4B;QAC5B,WAAW,IAAM,4BAA4B,KAAK;IACpD;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,MAAM,OAAO;YAAE,OAAO;YAAiB,aAAa;QAAgB;QAEzE,MAAM,eAAe,KAAK,YAAY,IAAI,KAAK,YAAY,IAAI;QAE/D,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,OAAO;oBAAY,aAAa;gBAA8B;YACzE,KAAK;gBACH,OAAO;oBAAE,OAAO;oBAAU,aAAa;gBAA4B;YACrE,KAAK;gBACH,OAAO;oBAAE,OAAO;oBAAY,aAAa;gBAA+B;YAC1E;gBACE,OAAO;oBAAE,OAAO;oBAAiB,aAAa;gBAAmB;QACrE;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,MAAM;QAEX,MAAM,eAAe;QAErB,IAAI,aAAa,KAAK,KAAK,cAAc,aAAa,KAAK,KAAK,YAAY;YAC1E,iBAAiB;gBACf,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,aAAa,CAAC,0CAA0C,EAAE,KAAK,OAAO,CAAC,CAAC,CAAC;YAC3E;QACF,OAAO;YACL;QACF;IACF;IAEA,MAAM,yBAAyB;QAC7B,IAAI,CAAC,QAAQ,CAAC,UAAU;QAExB,IAAI;YACF,WAAW;YACX,SAAS;YACT,uBAAuB;YAEvB,MAAM,iHAAA,CAAA,UAAO,CAAC,WAAW,CACvB,SAAS,WAAW,EACpB,KAAK,OAAO,EACZ,YACA;YAGF,MAAM,UAAU,CAAC,kDAAkD,EAAE,KAAK,OAAO,EAAE;YACnF,uBAAuB;YACvB,MAAM,OAAO,CAAC,YAAY;YAE1B,IAAI,WAAW;YACf;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,SAAS;YACT,uBAAuB,CAAC,QAAQ,EAAE,cAAc;YAChD,IAAI,SAAS,QAAQ;QACvB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,MAAM;QAEX,MAAM,eAAe;QAErB,IAAI,aAAa,KAAK,KAAK,YAAY,aAAa,KAAK,KAAK,YAAY;YACxE,iBAAiB;gBACf,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,aAAa,CAAC,wCAAwC,EAAE,KAAK,OAAO,CAAC,CAAC,CAAC;YACzE;QACF,OAAO;YACL;QACF;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,QAAQ,CAAC,UAAU;QAExB,IAAI;YACF,WAAW;YACX,SAAS;YACT,uBAAuB;YAEvB,MAAM,iHAAA,CAAA,UAAO,CAAC,WAAW,CACvB,SAAS,WAAW,EACpB,KAAK,OAAO,EACZ,UACA;YAGF,MAAM,UAAU,CAAC,gDAAgD,EAAE,KAAK,OAAO,EAAE;YACjF,uBAAuB;YACvB,MAAM,OAAO,CAAC,YAAY;YAE1B,IAAI,WAAW;YACf;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,SAAS;YACT,uBAAuB,CAAC,QAAQ,EAAE,cAAc;YAChD,IAAI,SAAS,QAAQ;QACvB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,MAAM;QAEX,MAAM,eAAe;QAErB,IAAI,aAAa,KAAK,KAAK,YAAY;YACrC,iBAAiB;gBACf,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,aAAa,CAAC,sCAAsC,EAAE,KAAK,OAAO,CAAC,mDAAmD,CAAC;YACzH;QACF,OAAO;YACL;QACF;IACF;IAEA,MAAM,yBAAyB;QAC7B,IAAI,CAAC,QAAQ,CAAC,UAAU;QAExB,IAAI;YACF,WAAW;YACX,SAAS;YACT,uBAAuB;YAEvB,MAAM,iHAAA,CAAA,UAAO,CAAC,WAAW,CACvB,SAAS,WAAW,EACpB,KAAK,OAAO,EACZ,YACA;YAGF,MAAM,UAAU,CAAC,kCAAkC,EAAE,KAAK,OAAO,EAAE;YACnE,uBAAuB;YACvB,MAAM,OAAO,CAAC,YAAY;YAE1B,IAAI,WAAW;YACf;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,SAAS;YACT,uBAAuB,CAAC,QAAQ,EAAE,cAAc;YAChD,IAAI,SAAS,QAAQ;QACvB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,cAAc,IAAI,EAAE;QAE/C,IAAI;YACF,WAAW;YACX,SAAS;YACT,uBAAuB;YAEvB,MAAM,iHAAA,CAAA,UAAO,CAAC,YAAY,CACxB,SAAS,WAAW,EACpB,KAAK,OAAO,EACZ,cAAc,IAAI,KAAK,aAAa,YAAY,cAAc,IAAI;YAGpE,MAAM,WAAW,cAAc,IAAI,KAAK,aAAa,KAAK,CAAC,MAAM,EAAE,cAAc,IAAI,EAAE;YACvF,MAAM,UAAU,CAAC,aAAa,EAAE,SAAS,wBAAwB,EAAE,KAAK,OAAO,EAAE;YACjF,uBAAuB;YACvB,MAAM,OAAO,CAAC,YAAY;YAE1B,IAAI,WAAW;YACf,iBAAiB;gBAAE,MAAM;gBAAO,MAAM;gBAAM,OAAO;gBAAI,aAAa;YAAG;YACvE;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,SAAS;YACT,uBAAuB,CAAC,QAAQ,EAAE,cAAc;YAChD,IAAI,SAAS,QAAQ;QACvB,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,oBAAoB;IAC1B,MAAM,cAAc,CAAC,KAAK,YAAY,IAAI,KAAK,eAAe,IAAI,CAAC,IAAI;IAEvE,qBACE;;0BACE,8OAAC,8KAAA,CAAA,OAAmB;0BAClB,cAAA,8OAAC;oBAAI,aAAU;oBAAS,eAAY;8BACjC;;;;;;;;;;;0BAIL,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAM,cAAc;0BAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBACZ,WAAU;oBACV,KAAK;oBACL,oBAAiB;;sCAEjB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAY;wCACF,KAAK,OAAO;;;;;;;8CAEvC,8OAAC,kIAAA,CAAA,oBAAiB;oCAAC,IAAG;;wCAA2B;wCACb,KAAK,OAAO;wCAAC;;;;;;;;;;;;;sCAInD,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;4CAAoC;4CACjB,KAAK,SAAS,IAAI;4CAAM;4CAAQ,KAAK,mBAAmB,IAAI;4CAAM;4CAAgB,KAAK,OAAO,IAAI;4CAAM;4CAAO,KAAK,iBAAiB,IAAI;4CAAM;4CAAkB,KAAK,eAAe,IAAI;4CAAE;;;;;;;;;;;;8CAK/N,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;8DAAoC;;;;;;8DACrD,8OAAC;oDAAI,WAAU;;wDACZ,kBAAkB,KAAK,KAAK,4BAAc,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDACjE,kBAAkB,KAAK,KAAK,iCAAmB,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDACtE,CAAC,kBAAkB,KAAK,KAAK,cAAc,kBAAkB,KAAK,KAAK,QAAQ,mBAAK,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;wDAC7G,kBAAkB,WAAW;;;;;;;;;;;;;;;;;;;;;;;gCAMrC,CAAC,6BACA,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;;sDACf,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC,iIAAA,CAAA,mBAAgB;4CAAC,WAAU;;8DAC1B,8OAAC;8DAAO;;;;;;gDAAoB;;;;;;;;;;;;;gCAKjC,uBACC,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;;sDACb,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC,iIAAA,CAAA,mBAAgB;sDAAE;;;;;;;;;;;;gCAItB,6BACC;;sDAEE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;8DAAsB;;;;;;8DACvC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;8EACvB,8OAAC;oEAAK,WAAU;8EAAc;;;;;;;;;;;;sEAEhC,8OAAC;4DAAE,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;sDAO9C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;8DAAsB;;;;;;8DAEvC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,KAAK;4DACL,SAAS;4DACT,UAAU;4DACV,WAAU;4DACV,SAAQ;sEAER,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC;oFAAK,WAAU;8FAAmC;;;;;;;;;;;0FAErD,8OAAC;;kGACC,8OAAC;wFAAI,WAAU;kGACZ,kBAAkB,KAAK,KAAK,cAAc,kBAAkB,KAAK,KAAK,aACnE,sBACA;;;;;;kGAEN,8OAAC;wFAAI,WAAU;kGACZ,kBAAkB,KAAK,KAAK,cAAc,kBAAkB,KAAK,KAAK,aACnE,uCACA;;;;;;;;;;;;;;;;;;oEAIT,wBAAU,8OAAC,iNAAA,CAAA,UAAO;wEAAC,WAAU;;;;;6FAA4B,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;;;;;;;sEAI7E,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS;4DACT,UAAU;4DACV,WAAU;4DACV,SAAQ;sEAER,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC;oFAAK,WAAU;8FAAkC;;;;;;;;;;;0FAEpD,8OAAC;;kGACC,8OAAC;wFAAI,WAAU;kGACZ,kBAAkB,KAAK,KAAK,YAAY,kBAAkB,KAAK,KAAK,aACjE,oBACA;;;;;;kGAEN,8OAAC;wFAAI,WAAU;kGACZ,kBAAkB,KAAK,KAAK,YAAY,kBAAkB,KAAK,KAAK,aACjE,qCACA;;;;;;;;;;;;;;;;;;oEAIT,wBAAU,8OAAC,iNAAA,CAAA,UAAO;wEAAC,WAAU;;;;;6FAA4B,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;;;;;;;sEAI7E,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS;4DACT,UAAU;4DACV,WAAU;4DACV,SAAQ;sEAER,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC;oFAAK,WAAU;8FAAoC;;;;;;;;;;;0FAEtD,8OAAC;;kGACC,8OAAC;wFAAI,WAAU;kGACZ,kBAAkB,KAAK,KAAK,aACzB,2BACA;;;;;;kGAEN,8OAAC;wFAAI,WAAU;kGACZ,kBAAkB,KAAK,KAAK,aACzB,iCACA;;;;;;;;;;;;;;;;;;oEAIT,wBAAU,8OAAC,iNAAA,CAAA,UAAO;wEAAC,WAAU;;;;;6FAA4B,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASvF,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,KAAK;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU;gCACV,WAAU;;kDAEV,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;0BAOtC,8OAAC;gBACC,MAAM,cAAc,IAAI;gBACxB,SAAS,IAAM,iBAAiB;wBAAE,MAAM;wBAAO,MAAM;wBAAM,OAAO;wBAAI,aAAa;oBAAG;gBACtF,WAAW;gBACX,OAAO,cAAc,KAAK;gBAC1B,aAAa,cAAc,WAAW;gBACtC,WAAW;gBACX,aAAa,cAAc,IAAI,KAAK;;;;;;;;AAI5C;uCAEe", "debugId": null}}, {"offset": {"line": 9721, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/cavi/CertificazioneDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Award, FileText, Download, CheckCircle, X } from 'lucide-react'\nimport { Cavo } from '@/types'\nimport { certificazioniApi, responsabiliApi, caviApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useToast } from '@/hooks/use-toast'\n\ninterface CertificazioneDialogProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\ninterface Responsabile {\n  id: number\n  nome_responsabile: string\n  numero_telefono?: string\n  mail?: string\n}\n\nexport default function CertificazioneDialog({\n  open,\n  onClose,\n  cavo,\n  onSuccess,\n  onError\n}: CertificazioneDialogProps) {\n  const { cantiere } = useAuth()\n  const { toast } = useToast()\n  const [formData, setFormData] = useState({\n    responsabile_certificazione: '',\n    data_certificazione: new Date().toISOString().split('T')[0],\n    esito_certificazione: 'CONFORME',\n    note_certificazione: ''\n  })\n  const [responsabili, setResponsabili] = useState<Responsabile[]>([])\n  const [loading, setLoading] = useState(false)\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false)\n  const [error, setError] = useState('')\n  const [screenReaderAnnouncement, setScreenReaderAnnouncement] = useState('')\n\n  // Funzione per annunci screen reader\n  const announceToScreenReader = (message: string) => {\n    setScreenReaderAnnouncement(message)\n    setTimeout(() => setScreenReaderAnnouncement(''), 1000)\n  }\n\n  // Reset form quando si apre il dialog\n  useEffect(() => {\n    if (open && cavo) {\n      setFormData({\n        responsabile_certificazione: '',\n        data_certificazione: new Date().toISOString().split('T')[0],\n        esito_certificazione: 'CONFORME',\n        note_certificazione: ''\n      })\n      setError('')\n      loadResponsabili()\n    }\n  }, [open, cavo])\n\n  const loadResponsabili = async () => {\n    if (!cantiere) return\n\n    try {\n      setLoadingResponsabili(true)\n      const response = await responsabiliApi.getResponsabili(cantiere.id_cantiere)\n      setResponsabili(response.data)\n    } catch (error) {\n      setResponsabili([])\n    } finally {\n      setLoadingResponsabili(false)\n    }\n  }\n\n  const isCavoCollegato = () => {\n    if (!cavo) return false\n    const collegamento = cavo.collegamento || cavo.collegamenti || 0\n    return collegamento === 3 // Completamente collegato\n  }\n\n  const isCavoCertificato = () => {\n    if (!cavo) return false\n    return cavo.certificato === true || cavo.certificato === 'SI' || cavo.certificato === 'CERTIFICATO'\n  }\n\n  // Funzione per collegare automaticamente il cavo durante la certificazione\n  const autoCollegaCavo = async () => {\n    if (!cavo || !cantiere) return false\n\n    try {\n      announceToScreenReader('Collegamento automatico in corso...')\n      \n      // Collega entrambi i lati a \"cantiere\"\n      await caviApi.collegaCavo(\n        cantiere.id_cantiere,\n        cavo.id_cavo,\n        'entrambi',\n        'cantiere'\n      )\n\n      announceToScreenReader('Cavo collegato automaticamente')\n      return true\n    } catch (error) {\n      console.error('Errore nel collegamento automatico:', error)\n      return false\n    }\n  }\n\n  const handleCertifica = async () => {\n    if (!cavo || !cantiere) return\n\n    if (!formData.responsabile_certificazione) {\n      setError('Seleziona un responsabile per la certificazione')\n      announceToScreenReader('Errore: Seleziona un responsabile per la certificazione')\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError('')\n      announceToScreenReader('Certificazione in corso...')\n\n      // Se il cavo non è collegato, prova a collegarlo automaticamente\n      const isCollegato = isCavoCollegato()\n      if (!isCollegato) {\n        announceToScreenReader('Collegamento automatico del cavo...')\n        const collegamentoRiuscito = await autoCollegaCavo()\n        if (!collegamentoRiuscito) {\n          setError('Impossibile collegare automaticamente il cavo. Collegalo manualmente prima di certificare.')\n          announceToScreenReader('Errore: Impossibile collegare automaticamente il cavo')\n          return\n        }\n      }\n\n      const certificazioneData = {\n        id_cavo: cavo.id_cavo,\n        responsabile_certificazione: formData.responsabile_certificazione,\n        data_certificazione: formData.data_certificazione,\n        esito_certificazione: formData.esito_certificazione,\n        note_certificazione: formData.note_certificazione || null\n      }\n\n      await certificazioniApi.createCertificazione(cantiere.id_cantiere, certificazioneData)\n\n      const message = `Certificazione completata per il cavo ${cavo.id_cavo}`\n      announceToScreenReader(message)\n      toast({\n        title: \"Successo\",\n        description: message,\n      })\n      \n      if (onSuccess) onSuccess(message)\n      onClose()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la certificazione'\n      setError(errorMessage)\n      announceToScreenReader(`Errore: ${errorMessage}`)\n      if (onError) onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleGeneraPDF = async () => {\n    if (!cavo || !cantiere) return\n\n    try {\n      setLoading(true)\n      setError('')\n      announceToScreenReader('Generazione PDF in corso...')\n\n      const response = await certificazioniApi.generatePDF(cantiere.id_cantiere, cavo.id_cavo)\n      \n      // Crea un link per il download\n      const url = window.URL.createObjectURL(new Blob([response.data]))\n      const link = document.createElement('a')\n      link.href = url\n      link.setAttribute('download', `certificato_${cavo.id_cavo}.pdf`)\n      document.body.appendChild(link)\n      link.click()\n      link.remove()\n      window.URL.revokeObjectURL(url)\n\n      const message = `PDF certificato generato per il cavo ${cavo.id_cavo}`\n      announceToScreenReader(message)\n      toast({\n        title: \"Successo\",\n        description: message,\n      })\n      \n      if (onSuccess) onSuccess(message)\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la generazione del PDF'\n      setError(errorMessage)\n      announceToScreenReader(`Errore: ${errorMessage}`)\n      if (onError) onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (!cavo) return null\n\n  // Controlli di validazione allineati con webapp originale CEI 64-8\n  const isInstalled = cavo.stato_installazione === 'Installato' ||\n                     cavo.stato_installazione === 'INSTALLATO' ||\n                     cavo.stato_installazione === 'POSATO' ||\n                     (cavo.metri_posati || cavo.metratura_reale || 0) > 0\n\n  const isCollegato = isCavoCollegato()\n  const hasResponsabili = cavo.responsabile_partenza && cavo.responsabile_arrivo\n  const isCompletelyConnected = isCollegato && hasResponsabili\n  const isCertificato = isCavoCertificato()\n\n  // Per la certificazione CEI 64-8 basta che sia posato (il collegamento può essere gestito durante la certificazione)\n  const puoEssereCertificato = isInstalled\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      {/* Screen reader announcements */}\n      <div \n        aria-live=\"polite\" \n        aria-atomic=\"true\" \n        className=\"sr-only\"\n      >\n        {screenReaderAnnouncement}\n      </div>\n      \n      <DialogContent \n        className=\"sm:max-w-[700px] max-h-[90vh] overflow-y-auto\"\n        aria-describedby=\"certificazione-description\"\n      >\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2 text-blue-600\">\n            <Award className=\"h-5 w-5\" />\n            Gestione Certificazione - {cavo.id_cavo}\n          </DialogTitle>\n          <DialogDescription id=\"certificazione-description\">\n            Certifica il cavo {cavo.id_cavo} secondo normativa CEI 64-8 o genera il PDF del certificato\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-6\">\n          {/* Informazioni cavo */}\n          <div className=\"p-4 bg-blue-50 rounded-lg border-2 border-blue-200\">\n            <div className=\"text-sm font-medium text-blue-800\">\n              Informazioni Cavo / Tipologia: {cavo.tipologia || 'N/A'} / Da: {cavo.ubicazione_partenza || 'N/A'} / Formazione: {cavo.sezione || 'N/A'} / A: {cavo.ubicazione_arrivo || 'N/A'} / Metri Posati: {cavo.metratura_reale || 0} m\n            </div>\n          </div>\n\n          {/* Stato attuale con icone migliorate */}\n          <div className=\"p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border\">\n            <Label className=\"text-sm font-medium text-gray-700\">Stato Cavo</Label>\n            <div className=\"mt-2 space-y-2\">\n              <div className=\"flex items-center gap-2\">\n                {isInstalled ? (\n                  <CheckCircle className=\"w-4 h-4 text-green-600\" />\n                ) : (\n                  <AlertCircle className=\"w-4 h-4 text-red-500\" />\n                )}\n                <span className=\"text-sm font-medium\">\n                  {isInstalled ? 'Installato/Posato' : 'Non installato'}\n                </span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                {isCollegato ? (\n                  <CheckCircle className=\"w-4 h-4 text-green-600\" />\n                ) : (\n                  <AlertCircle className=\"w-4 h-4 text-orange-500\" />\n                )}\n                <span className=\"text-sm font-medium\">\n                  {isCollegato ? 'Collegato' : 'Non collegato'}\n                </span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                {isCertificato ? (\n                  <CheckCircle className=\"w-4 h-4 text-green-600\" />\n                ) : (\n                  <AlertCircle className=\"w-4 h-4 text-gray-400\" />\n                )}\n                <span className=\"text-sm font-medium\">\n                  {isCertificato ? 'Certificato' : 'Non certificato'}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          {/* Alert di validazione */}\n          {!puoEssereCertificato && (\n            <Alert className=\"border-red-200 bg-red-50\">\n              <AlertCircle className=\"h-4 w-4 text-red-600\" />\n              <AlertDescription className=\"text-red-800\">\n                <strong>ATTENZIONE:</strong> Il cavo deve essere installato/posato prima di poter essere certificato secondo CEI 64-8.\n              </AlertDescription>\n            </Alert>\n          )}\n\n          {puoEssereCertificato && !isCompletelyConnected && (\n            <Alert className=\"border-amber-200 bg-amber-50\">\n              <AlertCircle className=\"h-4 w-4 text-amber-600\" />\n              <AlertDescription className=\"text-amber-800\">\n                <strong>ATTENZIONE:</strong> Il cavo non risulta completamente collegato.\n                Durante la certificazione sarà possibile collegarlo automaticamente a \"cantiere\" su entrambi i lati.\n              </AlertDescription>\n            </Alert>\n          )}\n\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          {isCertificato ? (\n            // Cavo già certificato - mostra opzione per generare PDF\n            <div className=\"space-y-4\">\n              <Alert className=\"border-green-200 bg-green-50\">\n                <Award className=\"h-4 w-4 text-green-600\" />\n                <AlertDescription className=\"text-green-800\">\n                  <strong>CERTIFICATO:</strong> Questo cavo è già stato certificato. Puoi generare il PDF del certificato.\n                </AlertDescription>\n              </Alert>\n\n              <Button\n                onClick={handleGeneraPDF}\n                disabled={loading}\n                className=\"w-full bg-green-600 hover:bg-green-700 text-white\"\n                size=\"lg\"\n              >\n                {loading ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <Download className=\"h-4 w-4 mr-2\" />}\n                Genera PDF Certificato\n              </Button>\n            </div>\n          ) : (\n            // Cavo non certificato - mostra form per certificazione (CEI 64-8: basta che sia posato)\n            puoEssereCertificato && (\n              <div className=\"space-y-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border-2 border-blue-200\">\n                <div className=\"text-sm font-medium text-blue-800 mb-4\">\n                  📋 Dati Certificazione CEI 64-8\n                </div>\n\n                {/* Responsabile */}\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"responsabile\" className=\"text-sm font-medium text-gray-700\">\n                    Responsabile Certificazione *\n                  </Label>\n                  <Select\n                    value={formData.responsabile_certificazione}\n                    onValueChange={(value) => setFormData(prev => ({ ...prev, responsabile_certificazione: value }))}\n                    disabled={loadingResponsabili}\n                  >\n                    <SelectTrigger className=\"border-2 border-gray-300 focus:border-blue-500\">\n                      <SelectValue placeholder=\"Seleziona responsabile...\" />\n                    </SelectTrigger>\n                    <SelectContent>\n                      {responsabili.map((resp) => (\n                        <SelectItem key={resp.id} value={resp.nome_responsabile}>\n                          {resp.nome_responsabile}\n                          {resp.numero_telefono && ` - ${resp.numero_telefono}`}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                </div>\n\n                {/* Data certificazione */}\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"data\" className=\"text-sm font-medium text-gray-700\">\n                    Data Certificazione\n                  </Label>\n                  <Input\n                    id=\"data\"\n                    type=\"date\"\n                    value={formData.data_certificazione}\n                    onChange={(e) => setFormData(prev => ({ ...prev, data_certificazione: e.target.value }))}\n                    className=\"border-2 border-gray-300 focus:border-blue-500\"\n                  />\n                </div>\n\n                {/* Esito */}\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"esito\" className=\"text-sm font-medium text-gray-700\">\n                    Esito Certificazione\n                  </Label>\n                  <Select\n                    value={formData.esito_certificazione}\n                    onValueChange={(value) => setFormData(prev => ({ ...prev, esito_certificazione: value }))}\n                  >\n                    <SelectTrigger className=\"border-2 border-gray-300 focus:border-blue-500\">\n                      <SelectValue />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"CONFORME\">✅ CONFORME</SelectItem>\n                      <SelectItem value=\"NON_CONFORME\">❌ NON CONFORME</SelectItem>\n                      <SelectItem value=\"CONFORME_CON_RISERVA\">⚠️ CONFORME CON RISERVA</SelectItem>\n                    </SelectContent>\n                  </Select>\n                </div>\n\n                {/* Note */}\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"note\" className=\"text-sm font-medium text-gray-700\">\n                    Note (opzionale)\n                  </Label>\n                  <Textarea\n                    id=\"note\"\n                    placeholder=\"Inserisci eventuali note sulla certificazione...\"\n                    value={formData.note_certificazione}\n                    onChange={(e) => setFormData(prev => ({ ...prev, note_certificazione: e.target.value }))}\n                    rows={3}\n                    className=\"border-2 border-gray-300 focus:border-blue-500\"\n                  />\n                </div>\n\n                <Button\n                  onClick={handleCertifica}\n                  disabled={loading || !formData.responsabile_certificazione}\n                  className=\"w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3\"\n                  size=\"lg\"\n                >\n                  {loading ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <Award className=\"h-4 w-4 mr-2\" />}\n                  Certifica Cavo CEI 64-8\n                </Button>\n              </div>\n            )\n          )}\n        </div>\n\n        <DialogFooter className=\"border-t pt-4\">\n          <Button\n            variant=\"outline\"\n            onClick={onClose}\n            disabled={loading}\n            className=\"flex items-center gap-2\"\n          >\n            <X className=\"h-4 w-4\" />\n            Chiudi\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AAOA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AA3BA;;;;;;;;;;;;;;AA4Ce,SAAS,qBAAqB,EAC3C,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,SAAS,EACT,OAAO,EACmB;IAC1B,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,6BAA6B;QAC7B,qBAAqB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC3D,sBAAsB;QACtB,qBAAqB;IACvB;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzE,qCAAqC;IACrC,MAAM,yBAAyB,CAAC;QAC9B,4BAA4B;QAC5B,WAAW,IAAM,4BAA4B,KAAK;IACpD;IAEA,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,MAAM;YAChB,YAAY;gBACV,6BAA6B;gBAC7B,qBAAqB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC3D,sBAAsB;gBACtB,qBAAqB;YACvB;YACA,SAAS;YACT;QACF;IACF,GAAG;QAAC;QAAM;KAAK;IAEf,MAAM,mBAAmB;QACvB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,uBAAuB;YACvB,MAAM,WAAW,MAAM,iHAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,SAAS,WAAW;YAC3E,gBAAgB,SAAS,IAAI;QAC/B,EAAE,OAAO,OAAO;YACd,gBAAgB,EAAE;QACpB,SAAU;YACR,uBAAuB;QACzB;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM,OAAO;QAClB,MAAM,eAAe,KAAK,YAAY,IAAI,KAAK,YAAY,IAAI;QAC/D,OAAO,iBAAiB,EAAE,0BAA0B;;IACtD;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,KAAK,WAAW,KAAK,QAAQ,KAAK,WAAW,KAAK,QAAQ,KAAK,WAAW,KAAK;IACxF;IAEA,2EAA2E;IAC3E,MAAM,kBAAkB;QACtB,IAAI,CAAC,QAAQ,CAAC,UAAU,OAAO;QAE/B,IAAI;YACF,uBAAuB;YAEvB,uCAAuC;YACvC,MAAM,iHAAA,CAAA,UAAO,CAAC,WAAW,CACvB,SAAS,WAAW,EACpB,KAAK,OAAO,EACZ,YACA;YAGF,uBAAuB;YACvB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,OAAO;QACT;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,QAAQ,CAAC,UAAU;QAExB,IAAI,CAAC,SAAS,2BAA2B,EAAE;YACzC,SAAS;YACT,uBAAuB;YACvB;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YACT,uBAAuB;YAEvB,iEAAiE;YACjE,MAAM,cAAc;YACpB,IAAI,CAAC,aAAa;gBAChB,uBAAuB;gBACvB,MAAM,uBAAuB,MAAM;gBACnC,IAAI,CAAC,sBAAsB;oBACzB,SAAS;oBACT,uBAAuB;oBACvB;gBACF;YACF;YAEA,MAAM,qBAAqB;gBACzB,SAAS,KAAK,OAAO;gBACrB,6BAA6B,SAAS,2BAA2B;gBACjE,qBAAqB,SAAS,mBAAmB;gBACjD,sBAAsB,SAAS,oBAAoB;gBACnD,qBAAqB,SAAS,mBAAmB,IAAI;YACvD;YAEA,MAAM,iHAAA,CAAA,oBAAiB,CAAC,oBAAoB,CAAC,SAAS,WAAW,EAAE;YAEnE,MAAM,UAAU,CAAC,sCAAsC,EAAE,KAAK,OAAO,EAAE;YACvE,uBAAuB;YACvB,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA,IAAI,WAAW,UAAU;YACzB;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,SAAS;YACT,uBAAuB,CAAC,QAAQ,EAAE,cAAc;YAChD,IAAI,SAAS,QAAQ;QACvB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,QAAQ,CAAC,UAAU;QAExB,IAAI;YACF,WAAW;YACX,SAAS;YACT,uBAAuB;YAEvB,MAAM,WAAW,MAAM,iHAAA,CAAA,oBAAiB,CAAC,WAAW,CAAC,SAAS,WAAW,EAAE,KAAK,OAAO;YAEvF,+BAA+B;YAC/B,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC,IAAI,KAAK;gBAAC,SAAS,IAAI;aAAC;YAC/D,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,YAAY,CAAC,YAAY,CAAC,YAAY,EAAE,KAAK,OAAO,CAAC,IAAI,CAAC;YAC/D,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,KAAK,MAAM;YACX,OAAO,GAAG,CAAC,eAAe,CAAC;YAE3B,MAAM,UAAU,CAAC,qCAAqC,EAAE,KAAK,OAAO,EAAE;YACtE,uBAAuB;YACvB,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA,IAAI,WAAW,UAAU;QAC3B,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,SAAS;YACT,uBAAuB,CAAC,QAAQ,EAAE,cAAc;YAChD,IAAI,SAAS,QAAQ;QACvB,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,mEAAmE;IACnE,MAAM,cAAc,KAAK,mBAAmB,KAAK,gBAC9B,KAAK,mBAAmB,KAAK,gBAC7B,KAAK,mBAAmB,KAAK,YAC7B,CAAC,KAAK,YAAY,IAAI,KAAK,eAAe,IAAI,CAAC,IAAI;IAEtE,MAAM,cAAc;IACpB,MAAM,kBAAkB,KAAK,qBAAqB,IAAI,KAAK,mBAAmB;IAC9E,MAAM,wBAAwB,eAAe;IAC7C,MAAM,gBAAgB;IAEtB,qHAAqH;IACrH,MAAM,uBAAuB;IAE7B,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;;0BAEhC,8OAAC;gBACC,aAAU;gBACV,eAAY;gBACZ,WAAU;0BAET;;;;;;0BAGH,8OAAC,kIAAA,CAAA,gBAAa;gBACZ,WAAU;gBACV,oBAAiB;;kCAEjB,8OAAC,kIAAA,CAAA,eAAY;;0CACX,8OAAC,kIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAY;oCACF,KAAK,OAAO;;;;;;;0CAEzC,8OAAC,kIAAA,CAAA,oBAAiB;gCAAC,IAAG;;oCAA6B;oCAC9B,KAAK,OAAO;oCAAC;;;;;;;;;;;;;kCAIpC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;wCAAoC;wCACjB,KAAK,SAAS,IAAI;wCAAM;wCAAQ,KAAK,mBAAmB,IAAI;wCAAM;wCAAgB,KAAK,OAAO,IAAI;wCAAM;wCAAO,KAAK,iBAAiB,IAAI;wCAAM;wCAAkB,KAAK,eAAe,IAAI;wCAAE;;;;;;;;;;;;0CAK/N,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,WAAU;kDAAoC;;;;;;kDACrD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;oDACZ,4BACC,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;6EAEvB,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEAEzB,8OAAC;wDAAK,WAAU;kEACb,cAAc,sBAAsB;;;;;;;;;;;;0DAGzC,8OAAC;gDAAI,WAAU;;oDACZ,4BACC,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;6EAEvB,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEAEzB,8OAAC;wDAAK,WAAU;kEACb,cAAc,cAAc;;;;;;;;;;;;0DAGjC,8OAAC;gDAAI,WAAU;;oDACZ,8BACC,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;6EAEvB,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEAEzB,8OAAC;wDAAK,WAAU;kEACb,gBAAgB,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;4BAOxC,CAAC,sCACA,8OAAC,iIAAA,CAAA,QAAK;gCAAC,WAAU;;kDACf,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC,iIAAA,CAAA,mBAAgB;wCAAC,WAAU;;0DAC1B,8OAAC;0DAAO;;;;;;4CAAoB;;;;;;;;;;;;;4BAKjC,wBAAwB,CAAC,uCACxB,8OAAC,iIAAA,CAAA,QAAK;gCAAC,WAAU;;kDACf,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC,iIAAA,CAAA,mBAAgB;wCAAC,WAAU;;0DAC1B,8OAAC;0DAAO;;;;;;4CAAoB;;;;;;;;;;;;;4BAMjC,uBACC,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;;kDACb,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC,iIAAA,CAAA,mBAAgB;kDAAE;;;;;;;;;;;;4BAItB,gBACC,yDAAyD;0CACzD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,WAAU;;0DACf,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC,iIAAA,CAAA,mBAAgB;gDAAC,WAAU;;kEAC1B,8OAAC;kEAAO;;;;;;oDAAqB;;;;;;;;;;;;;kDAIjC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU;wCACV,WAAU;wCACV,MAAK;;4CAEJ,wBAAU,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAAiC,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAkB;;;;;;;;;;;;uCAKzG,yFAAyF;4BACzF,sCACE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAyC;;;;;;kDAKxD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAe,WAAU;0DAAoC;;;;;;0DAG5E,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,SAAS,2BAA2B;gDAC3C,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,6BAA6B;wDAAM,CAAC;gDAC9F,UAAU;;kEAEV,8OAAC,kIAAA,CAAA,gBAAa;wDAAC,WAAU;kEACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,8OAAC,kIAAA,CAAA,gBAAa;kEACX,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,kIAAA,CAAA,aAAU;gEAAe,OAAO,KAAK,iBAAiB;;oEACpD,KAAK,iBAAiB;oEACtB,KAAK,eAAe,IAAI,CAAC,GAAG,EAAE,KAAK,eAAe,EAAE;;+DAFtC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;kDAUhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAO,WAAU;0DAAoC;;;;;;0DAGpE,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,mBAAmB;gDACnC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,qBAAqB,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDACtF,WAAU;;;;;;;;;;;;kDAKd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAQ,WAAU;0DAAoC;;;;;;0DAGrE,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,SAAS,oBAAoB;gDACpC,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,sBAAsB;wDAAM,CAAC;;kEAEvF,8OAAC,kIAAA,CAAA,gBAAa;wDAAC,WAAU;kEACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAW;;;;;;0EAC7B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAe;;;;;;0EACjC,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAuB;;;;;;;;;;;;;;;;;;;;;;;;kDAM/C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAO,WAAU;0DAAoC;;;;;;0DAGpE,8OAAC,oIAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,aAAY;gDACZ,OAAO,SAAS,mBAAmB;gDACnC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,qBAAqB,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDACtF,MAAM;gDACN,WAAU;;;;;;;;;;;;kDAId,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,WAAW,CAAC,SAAS,2BAA2B;wCAC1D,WAAU;wCACV,MAAK;;4CAEJ,wBAAU,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAAiC,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAkB;;;;;;;;;;;;;;;;;;;kCAQ5G,8OAAC,kIAAA,CAAA,eAAY;wBAAC,WAAU;kCACtB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;4BACV,WAAU;;8CAEV,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;;;;;;;;;;;AAOrC", "debugId": null}}, {"offset": {"line": 10575, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/cavi/CreaComandaDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, ClipboardList, Users } from 'lucide-react'\nimport { comandeApi, responsabiliApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface CreaComandaDialogProps {\n  open: boolean\n  onClose: () => void\n  caviSelezionati: string[]\n  tipoComanda?: 'POSA' | 'COLLEGAMENTO_PARTENZA' | 'COLLEGAMENTO_ARRIVO' | 'CERTIFICAZIONE'\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\ninterface Responsabile {\n  id: number\n  nome_responsabile: string\n  numero_telefono?: string\n  mail?: string\n}\n\nexport default function CreaComandaDialog({\n  open,\n  onClose,\n  caviSelezionati,\n  tipoComanda,\n  onSuccess,\n  onError\n}: CreaComandaDialogProps) {\n  const { cantiere } = useAuth()\n  const [formData, setFormData] = useState({\n    tipo_comanda: tipoComanda || 'POSA',\n    responsabile: '',\n    note: ''\n  })\n  const [responsabili, setResponsabili] = useState<Responsabile[]>([])\n  const [loading, setLoading] = useState(false)\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false)\n  const [error, setError] = useState('')\n\n  // Reset form quando si apre il dialog\n  useEffect(() => {\n    if (open) {\n      setFormData({\n        tipo_comanda: tipoComanda || 'POSA',\n        responsabile: '',\n        note: ''\n      })\n      setError('')\n      loadResponsabili()\n    }\n  }, [open, tipoComanda])\n\n  const loadResponsabili = async () => {\n    if (!cantiere) return\n\n    try {\n      setLoadingResponsabili(true)\n      const response = await responsabiliApi.getResponsabili(cantiere.id_cantiere)\n      setResponsabili(response.data)\n    } catch (error) {\n      setResponsabili([])\n    } finally {\n      setLoadingResponsabili(false)\n    }\n  }\n\n  const getTipoComandaLabel = (tipo: string) => {\n    switch (tipo) {\n      case 'POSA':\n        return 'Posa Cavi'\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'Collegamento Partenza'\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'Collegamento Arrivo'\n      case 'CERTIFICAZIONE':\n        return 'Certificazione'\n      default:\n        return tipo\n    }\n  }\n\n  const handleCreaComanda = async () => {\n    if (!cantiere) return\n\n    if (!formData.responsabile) {\n      setError('Seleziona un responsabile per la comanda')\n      return\n    }\n\n    if (caviSelezionati.length === 0) {\n      setError('Seleziona almeno un cavo per la comanda')\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError('')\n\n      // Crea la comanda con i cavi assegnati\n      const comandaData = {\n        tipo_comanda: formData.tipo_comanda,\n        responsabile: formData.responsabile,\n        note: formData.note || null\n      }\n\n      const response = await comandeApi.createComandaWithCavi(\n        cantiere.id_cantiere,\n        comandaData,\n        caviSelezionati\n      )\n\n      onSuccess(`Comanda ${response.data.codice_comanda} creata con successo per ${caviSelezionati.length} cavi`)\n      onClose()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la creazione della comanda'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <DialogContent className=\"sm:max-w-[600px]\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <ClipboardList className=\"h-5 w-5\" />\n            Crea Nuova Comanda\n          </DialogTitle>\n          <DialogDescription>\n            Crea una nuova comanda per {caviSelezionati.length} cavi selezionati\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          {/* Cavi selezionati */}\n          <div className=\"p-4 bg-gray-50 rounded-lg\">\n            <Label className=\"text-sm font-medium\">Cavi Selezionati ({caviSelezionati.length})</Label>\n            <div className=\"mt-2 max-h-32 overflow-y-auto\">\n              <div className=\"flex flex-wrap gap-1\">\n                {caviSelezionati.slice(0, 10).map((cavoId) => (\n                  <span\n                    key={cavoId}\n                    className=\"inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded\"\n                  >\n                    {cavoId}\n                  </span>\n                ))}\n                {caviSelezionati.length > 10 && (\n                  <span className=\"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded\">\n                    +{caviSelezionati.length - 10} altri...\n                  </span>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          {/* Tipo comanda */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"tipo\">Tipo Comanda *</Label>\n            <Select\n              value={formData.tipo_comanda}\n              onValueChange={(value) => setFormData(prev => ({ ...prev, tipo_comanda: value }))}\n            >\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"POSA\">🔧 Posa Cavi</SelectItem>\n                <SelectItem value=\"COLLEGAMENTO_PARTENZA\">🔌 Collegamento Partenza</SelectItem>\n                <SelectItem value=\"COLLEGAMENTO_ARRIVO\">⚡ Collegamento Arrivo</SelectItem>\n                <SelectItem value=\"CERTIFICAZIONE\">📋 Certificazione</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Responsabile */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"responsabile\">Responsabile *</Label>\n            <Select\n              value={formData.responsabile}\n              onValueChange={(value) => setFormData(prev => ({ ...prev, responsabile: value }))}\n              disabled={loadingResponsabili}\n            >\n              <SelectTrigger>\n                <SelectValue placeholder=\"Seleziona responsabile...\" />\n              </SelectTrigger>\n              <SelectContent>\n                {responsabili.map((resp) => (\n                  <SelectItem key={resp.id} value={resp.nome_responsabile}>\n                    <div className=\"flex items-center gap-2\">\n                      <Users className=\"h-4 w-4\" />\n                      <span>{resp.nome_responsabile}</span>\n                      {resp.numero_telefono && (\n                        <span className=\"text-xs text-gray-500\">- {resp.numero_telefono}</span>\n                      )}\n                    </div>\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Note */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"note\">Note (opzionale)</Label>\n            <Textarea\n              id=\"note\"\n              placeholder=\"Inserisci eventuali note per la comanda...\"\n              value={formData.note}\n              onChange={(e) => setFormData(prev => ({ ...prev, note: e.target.value }))}\n              rows={3}\n            />\n          </div>\n\n          {/* Riepilogo */}\n          <div className=\"p-4 bg-blue-50 rounded-lg\">\n            <Label className=\"text-sm font-medium\">Riepilogo Comanda</Label>\n            <div className=\"mt-2 space-y-1 text-sm\">\n              <div><strong>Tipo:</strong> {getTipoComandaLabel(formData.tipo_comanda)}</div>\n              <div><strong>Responsabile:</strong> {formData.responsabile || 'Non selezionato'}</div>\n              <div><strong>Cavi:</strong> {caviSelezionati.length} selezionati</div>\n              {formData.note && <div><strong>Note:</strong> {formData.note}</div>}\n            </div>\n          </div>\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={onClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button\n            onClick={handleCreaComanda}\n            disabled={loading || !formData.responsabile || caviSelezionati.length === 0}\n          >\n            {loading ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <ClipboardList className=\"h-4 w-4 mr-2\" />}\n            Crea Comanda\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AAEA;AACA;AAOA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAzBA;;;;;;;;;;;;AA2Ce,SAAS,kBAAkB,EACxC,IAAI,EACJ,OAAO,EACP,eAAe,EACf,WAAW,EACX,SAAS,EACT,OAAO,EACgB;IACvB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,cAAc,eAAe;QAC7B,cAAc;QACd,MAAM;IACR;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,YAAY;gBACV,cAAc,eAAe;gBAC7B,cAAc;gBACd,MAAM;YACR;YACA,SAAS;YACT;QACF;IACF,GAAG;QAAC;QAAM;KAAY;IAEtB,MAAM,mBAAmB;QACvB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,uBAAuB;YACvB,MAAM,WAAW,MAAM,iHAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,SAAS,WAAW;YAC3E,gBAAgB,SAAS,IAAI;QAC/B,EAAE,OAAO,OAAO;YACd,gBAAgB,EAAE;QACpB,SAAU;YACR,uBAAuB;QACzB;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,UAAU;QAEf,IAAI,CAAC,SAAS,YAAY,EAAE;YAC1B,SAAS;YACT;QACF;QAEA,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,SAAS;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,uCAAuC;YACvC,MAAM,cAAc;gBAClB,cAAc,SAAS,YAAY;gBACnC,cAAc,SAAS,YAAY;gBACnC,MAAM,SAAS,IAAI,IAAI;YACzB;YAEA,MAAM,WAAW,MAAM,iHAAA,CAAA,aAAU,CAAC,qBAAqB,CACrD,SAAS,WAAW,EACpB,aACA;YAGF,UAAU,CAAC,QAAQ,EAAE,SAAS,IAAI,CAAC,cAAc,CAAC,yBAAyB,EAAE,gBAAgB,MAAM,CAAC,KAAK,CAAC;YAC1G;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGvC,8OAAC,kIAAA,CAAA,oBAAiB;;gCAAC;gCACW,gBAAgB,MAAM;gCAAC;;;;;;;;;;;;;8BAIvD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;;wCAAsB;wCAAmB,gBAAgB,MAAM;wCAAC;;;;;;;8CACjF,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;4CACZ,gBAAgB,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,uBACjC,8OAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;4CAMR,gBAAgB,MAAM,GAAG,oBACxB,8OAAC;gDAAK,WAAU;;oDAAmE;oDAC/E,gBAAgB,MAAM,GAAG;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;wBAOvC,uBACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;sCAKvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAO;;;;;;8CACtB,8OAAC,kIAAA,CAAA,SAAM;oCACL,OAAO,SAAS,YAAY;oCAC5B,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,cAAc;4CAAM,CAAC;;sDAE/E,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;sDAEd,8OAAC,kIAAA,CAAA,gBAAa;;8DACZ,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAO;;;;;;8DACzB,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAwB;;;;;;8DAC1C,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAsB;;;;;;8DACxC,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAiB;;;;;;;;;;;;;;;;;;;;;;;;sCAMzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAe;;;;;;8CAC9B,8OAAC,kIAAA,CAAA,SAAM;oCACL,OAAO,SAAS,YAAY;oCAC5B,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,cAAc;4CAAM,CAAC;oCAC/E,UAAU;;sDAEV,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,8OAAC,kIAAA,CAAA,gBAAa;sDACX,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,kIAAA,CAAA,aAAU;oDAAe,OAAO,KAAK,iBAAiB;8DACrD,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;0EAAM,KAAK,iBAAiB;;;;;;4DAC5B,KAAK,eAAe,kBACnB,8OAAC;gEAAK,WAAU;;oEAAwB;oEAAG,KAAK,eAAe;;;;;;;;;;;;;mDALpD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;sCAehC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAO;;;;;;8CACtB,8OAAC,oIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,aAAY;oCACZ,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACvE,MAAM;;;;;;;;;;;;sCAKV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAc;gDAAE,oBAAoB,SAAS,YAAY;;;;;;;sDACtE,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAsB;gDAAE,SAAS,YAAY,IAAI;;;;;;;sDAC9D,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAc;gDAAE,gBAAgB,MAAM;gDAAC;;;;;;;wCACnD,SAAS,IAAI,kBAAI,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAc;gDAAE,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;8BAKlE,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAS,UAAU;sCAAS;;;;;;sCAG/D,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,WAAW,CAAC,SAAS,YAAY,IAAI,gBAAgB,MAAM,KAAK;;gCAEzE,wBAAU,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAAiC,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAkB;;;;;;;;;;;;;;;;;;;;;;;;AAOtH", "debugId": null}}, {"offset": {"line": 11174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/cavi/ImportExcelDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef } from 'react'\nimport {\n  <PERSON><PERSON>,\n  <PERSON>alogContent,\n  DialogDescription,\n  DialogFooter,\n  <PERSON>alogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Upload, FileSpreadsheet, CheckCircle } from 'lucide-react'\nimport { excelApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface ImportExcelDialogProps {\n  open: boolean\n  onClose: () => void\n  tipo: 'cavi' | 'bobine'\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\nexport default function ImportExcelDialog({\n  open,\n  onClose,\n  tipo,\n  onSuccess,\n  onError\n}: ImportExcelDialogProps) {\n  const { cantiere } = useAuth()\n  const [file, setFile] = useState<File | null>(null)\n  const [revisione, setRevisione] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [uploadProgress, setUploadProgress] = useState(0)\n  const fileInputRef = useRef<HTMLInputElement>(null)\n\n  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const selectedFile = event.target.files?.[0]\n    if (selectedFile) {\n      // Verifica che sia un file Excel\n      const validTypes = [\n        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n        'application/vnd.ms-excel'\n      ]\n      \n      if (!validTypes.includes(selectedFile.type) && \n          !selectedFile.name.toLowerCase().endsWith('.xlsx') && \n          !selectedFile.name.toLowerCase().endsWith('.xls')) {\n        setError('Seleziona un file Excel valido (.xlsx o .xls)')\n        return\n      }\n\n      setFile(selectedFile)\n      setError('')\n    }\n  }\n\n  const handleImport = async () => {\n    if (!file || !cantiere) return\n\n    if (tipo === 'cavi' && !revisione.trim()) {\n      setError('Inserisci il codice revisione per l\\'importazione cavi')\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError('')\n      setUploadProgress(0)\n\n      let response\n      if (tipo === 'cavi') {\n        response = await excelApi.importCavi(cantiere.id_cantiere, file, revisione.trim())\n      } else {\n        response = await excelApi.importBobine(cantiere.id_cantiere, file)\n      }\n\n      setUploadProgress(100)\n\n      if (response.data.success) {\n        const details = response.data.details\n        let message = response.data.message\n        \n        if (tipo === 'cavi' && details?.cavi_importati) {\n          message += ` (${details.cavi_importati} cavi importati)`\n        } else if (tipo === 'bobine' && details?.bobine_importate) {\n          message += ` (${details.bobine_importate} bobine importate)`\n        }\n\n        onSuccess(message)\n        onClose()\n      } else {\n        onError(response.data.message || 'Errore durante l\\'importazione')\n      }\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\\'importazione del file'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n      setUploadProgress(0)\n    }\n  }\n\n  const handleClose = () => {\n    if (!loading) {\n      setFile(null)\n      setRevisione('')\n      setError('')\n      setUploadProgress(0)\n      if (fileInputRef.current) {\n        fileInputRef.current.value = ''\n      }\n      onClose()\n    }\n  }\n\n  const getTipoLabel = () => {\n    return tipo === 'cavi' ? 'Cavi' : 'Bobine'\n  }\n\n  const getFileRequirements = () => {\n    if (tipo === 'cavi') {\n      return [\n        'File Excel (.xlsx o .xls)',\n        'Colonne richieste: ID_CAVO, SISTEMA, UTILITY, TIPOLOGIA, ecc.',\n        'Prima riga deve contenere le intestazioni',\n        'Codice revisione obbligatorio per tracciabilità'\n      ]\n    } else {\n      return [\n        'File Excel (.xlsx o .xls)',\n        'Colonne richieste: NUMERO_BOBINA, UTILITY, TIPOLOGIA, METRI_TOTALI, ecc.',\n        'Prima riga deve contenere le intestazioni',\n        'I metri residui saranno impostati uguali ai metri totali'\n      ]\n    }\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent className=\"sm:max-w-[600px]\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Upload className=\"h-5 w-5\" />\n            Importa {getTipoLabel()} da Excel\n          </DialogTitle>\n          <DialogDescription>\n            Carica un file Excel per importare {getTipoLabel().toLowerCase()} nel cantiere\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          {/* Requisiti file */}\n          <div className=\"p-4 bg-blue-50 rounded-lg\">\n            <Label className=\"text-sm font-medium\">Requisiti File</Label>\n            <ul className=\"mt-2 space-y-1 text-sm text-gray-600\">\n              {getFileRequirements().map((req, index) => (\n                <li key={index} className=\"flex items-start gap-2\">\n                  <span className=\"text-blue-500 mt-0.5\">•</span>\n                  <span>{req}</span>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          {/* Selezione file */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"file\">File Excel *</Label>\n            <div className=\"flex items-center gap-2\">\n              <Input\n                ref={fileInputRef}\n                id=\"file\"\n                type=\"file\"\n                accept=\".xlsx,.xls\"\n                onChange={handleFileSelect}\n                disabled={loading}\n                className=\"flex-1\"\n              />\n              {file && (\n                <div className=\"flex items-center gap-1 text-green-600\">\n                  <CheckCircle className=\"h-4 w-4\" />\n                  <span className=\"text-sm\">File selezionato</span>\n                </div>\n              )}\n            </div>\n            {file && (\n              <div className=\"text-sm text-gray-600\">\n                <FileSpreadsheet className=\"h-4 w-4 inline mr-1\" />\n                {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)\n              </div>\n            )}\n          </div>\n\n          {/* Revisione (solo per cavi) */}\n          {tipo === 'cavi' && (\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"revisione\">Codice Revisione *</Label>\n              <Input\n                id=\"revisione\"\n                value={revisione}\n                onChange={(e) => setRevisione(e.target.value)}\n                placeholder=\"es. REV001, V1.0, 2024-01\"\n                disabled={loading}\n              />\n              <p className=\"text-sm text-gray-500\">\n                Codice identificativo della revisione per tracciabilità delle modifiche\n              </p>\n            </div>\n          )}\n\n          {/* Progress bar */}\n          {loading && (\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center gap-2\">\n                <Loader2 className=\"h-4 w-4 animate-spin\" />\n                <span className=\"text-sm\">Caricamento in corso...</span>\n              </div>\n              <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                <div \n                  className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                  style={{ width: `${uploadProgress}%` }}\n                ></div>\n              </div>\n            </div>\n          )}\n\n          {/* Riepilogo */}\n          {file && (\n            <div className=\"p-4 bg-gray-50 rounded-lg\">\n              <Label className=\"text-sm font-medium\">Riepilogo Importazione</Label>\n              <div className=\"mt-2 space-y-1 text-sm\">\n                <div><strong>Tipo:</strong> {getTipoLabel()}</div>\n                <div><strong>File:</strong> {file.name}</div>\n                <div><strong>Dimensione:</strong> {(file.size / 1024 / 1024).toFixed(2)} MB</div>\n                {tipo === 'cavi' && revisione && (\n                  <div><strong>Revisione:</strong> {revisione}</div>\n                )}\n                <div><strong>Cantiere:</strong> {cantiere?.nome_cantiere}</div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={handleClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button\n            onClick={handleImport}\n            disabled={loading || !file || (tipo === 'cavi' && !revisione.trim())}\n          >\n            {loading ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <Upload className=\"h-4 w-4 mr-2\" />}\n            Importa {getTipoLabel()}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAjBA;;;;;;;;;;;AA2Be,SAAS,kBAAkB,EACxC,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,SAAS,EACT,OAAO,EACgB;IACvB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,mBAAmB,CAAC;QACxB,MAAM,eAAe,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAC5C,IAAI,cAAc;YAChB,iCAAiC;YACjC,MAAM,aAAa;gBACjB;gBACA;aACD;YAED,IAAI,CAAC,WAAW,QAAQ,CAAC,aAAa,IAAI,KACtC,CAAC,aAAa,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAC1C,CAAC,aAAa,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS;gBACrD,SAAS;gBACT;YACF;YAEA,QAAQ;YACR,SAAS;QACX;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,QAAQ,CAAC,UAAU;QAExB,IAAI,SAAS,UAAU,CAAC,UAAU,IAAI,IAAI;YACxC,SAAS;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YACT,kBAAkB;YAElB,IAAI;YACJ,IAAI,SAAS,QAAQ;gBACnB,WAAW,MAAM,iHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,SAAS,WAAW,EAAE,MAAM,UAAU,IAAI;YACjF,OAAO;gBACL,WAAW,MAAM,iHAAA,CAAA,WAAQ,CAAC,YAAY,CAAC,SAAS,WAAW,EAAE;YAC/D;YAEA,kBAAkB;YAElB,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACzB,MAAM,UAAU,SAAS,IAAI,CAAC,OAAO;gBACrC,IAAI,UAAU,SAAS,IAAI,CAAC,OAAO;gBAEnC,IAAI,SAAS,UAAU,SAAS,gBAAgB;oBAC9C,WAAW,CAAC,EAAE,EAAE,QAAQ,cAAc,CAAC,gBAAgB,CAAC;gBAC1D,OAAO,IAAI,SAAS,YAAY,SAAS,kBAAkB;oBACzD,WAAW,CAAC,EAAE,EAAE,QAAQ,gBAAgB,CAAC,kBAAkB,CAAC;gBAC9D;gBAEA,UAAU;gBACV;YACF,OAAO;gBACL,QAAQ,SAAS,IAAI,CAAC,OAAO,IAAI;YACnC;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;YACX,kBAAkB;QACpB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ,QAAQ;YACR,aAAa;YACb,SAAS;YACT,kBAAkB;YAClB,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,KAAK,GAAG;YAC/B;YACA;QACF;IACF;IAEA,MAAM,eAAe;QACnB,OAAO,SAAS,SAAS,SAAS;IACpC;IAEA,MAAM,sBAAsB;QAC1B,IAAI,SAAS,QAAQ;YACnB,OAAO;gBACL;gBACA;gBACA;gBACA;aACD;QACH,OAAO;YACL,OAAO;gBACL;gBACA;gBACA;gBACA;aACD;QACH;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;gCACrB;gCAAe;;;;;;;sCAE1B,8OAAC,kIAAA,CAAA,oBAAiB;;gCAAC;gCACmB,eAAe,WAAW;gCAAG;;;;;;;;;;;;;8BAIrE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,8OAAC;oCAAG,WAAU;8CACX,sBAAsB,GAAG,CAAC,CAAC,KAAK,sBAC/B,8OAAC;4CAAe,WAAU;;8DACxB,8OAAC;oDAAK,WAAU;8DAAuB;;;;;;8DACvC,8OAAC;8DAAM;;;;;;;2CAFA;;;;;;;;;;;;;;;;wBAQd,uBACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;sCAKvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAO;;;;;;8CACtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CACJ,KAAK;4CACL,IAAG;4CACH,MAAK;4CACL,QAAO;4CACP,UAAU;4CACV,UAAU;4CACV,WAAU;;;;;;wCAEX,sBACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;gCAI/B,sBACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4NAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;wCAC1B,KAAK,IAAI;wCAAC;wCAAG,CAAC,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;wCAAG;;;;;;;;;;;;;wBAMxD,SAAS,wBACR,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAY;;;;;;8CAC3B,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oCAC5C,aAAY;oCACZ,UAAU;;;;;;8CAEZ,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;wBAOxC,yBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;8CAE5B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,GAAG,eAAe,CAAC,CAAC;wCAAC;;;;;;;;;;;;;;;;;wBAO5C,sBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAc;gDAAE;;;;;;;sDAC7B,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAc;gDAAE,KAAK,IAAI;;;;;;;sDACtC,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAoB;gDAAE,CAAC,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;gDAAG;;;;;;;wCACvE,SAAS,UAAU,2BAClB,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAmB;gDAAE;;;;;;;sDAEpC,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAkB;gDAAE,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;8BAMnD,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAa,UAAU;sCAAS;;;;;;sCAGnE,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,WAAW,CAAC,QAAS,SAAS,UAAU,CAAC,UAAU,IAAI;;gCAEhE,wBAAU,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAAiC,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAkB;gCAC1F;;;;;;;;;;;;;;;;;;;;;;;;AAMrB", "debugId": null}}, {"offset": {"line": 11762, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/cavi/ExportDataDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Download, FileSpreadsheet, Database } from 'lucide-react'\nimport { excelApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface ExportDataDialogProps {\n  open: boolean\n  onClose: () => void\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\nexport default function ExportDataDialog({\n  open,\n  onClose,\n  onSuccess,\n  onError\n}: ExportDataDialogProps) {\n  const { cantiere } = useAuth()\n  const [selectedExports, setSelectedExports] = useState({\n    cavi: true,\n    bobine: true,\n    comande: false,\n    certificazioni: false,\n    responsabili: false\n  })\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const handleExportChange = (exportType: string, checked: boolean) => {\n    setSelectedExports(prev => ({\n      ...prev,\n      [exportType]: checked\n    }))\n  }\n\n  const handleExportCavi = async () => {\n    if (!cantiere) return\n\n    try {\n      setLoading(true)\n      const response = await excelApi.exportCavi(cantiere.id_cantiere)\n      \n      // Crea un link per il download\n      const url = window.URL.createObjectURL(new Blob([response.data]))\n      const link = document.createElement('a')\n      link.href = url\n      link.setAttribute('download', `cavi_${cantiere.nome_cantiere}_${new Date().toISOString().split('T')[0]}.xlsx`)\n      document.body.appendChild(link)\n      link.click()\n      link.remove()\n      window.URL.revokeObjectURL(url)\n\n      onSuccess('Export cavi completato con successo')\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\\'export dei cavi'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleExportBobine = async () => {\n    if (!cantiere) return\n\n    try {\n      setLoading(true)\n      const response = await excelApi.exportBobine(cantiere.id_cantiere)\n      \n      // Crea un link per il download\n      const url = window.URL.createObjectURL(new Blob([response.data]))\n      const link = document.createElement('a')\n      link.href = url\n      link.setAttribute('download', `bobine_${cantiere.nome_cantiere}_${new Date().toISOString().split('T')[0]}.xlsx`)\n      document.body.appendChild(link)\n      link.click()\n      link.remove()\n      window.URL.revokeObjectURL(url)\n\n      onSuccess('Export bobine completato con successo')\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\\'export delle bobine'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleExportAll = async () => {\n    if (!cantiere) return\n\n    try {\n      setLoading(true)\n      setError('')\n\n      const exports = []\n\n      if (selectedExports.cavi) {\n        exports.push(handleExportCavi())\n      }\n\n      if (selectedExports.bobine) {\n        exports.push(handleExportBobine())\n      }\n\n      // TODO: Implementare export per comande, certificazioni, responsabili\n      if (selectedExports.comande) {\n      }\n\n      if (selectedExports.certificazioni) {\n      }\n\n      if (selectedExports.responsabili) {\n      }\n\n      await Promise.all(exports)\n\n      const exportCount = Object.values(selectedExports).filter(Boolean).length\n      onSuccess(`Export completato: ${exportCount} file scaricati`)\n      onClose()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\\'export dei dati'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const getSelectedCount = () => {\n    return Object.values(selectedExports).filter(Boolean).length\n  }\n\n  const exportOptions = [\n    {\n      key: 'cavi',\n      label: 'Cavi',\n      description: 'Esporta tutti i cavi del cantiere con stato, collegamenti e certificazioni',\n      icon: <Database className=\"h-4 w-4\" />,\n      available: true\n    },\n    {\n      key: 'bobine',\n      label: 'Bobine',\n      description: 'Esporta tutte le bobine del parco cavi con metri residui e assegnazioni',\n      icon: <FileSpreadsheet className=\"h-4 w-4\" />,\n      available: true\n    },\n    {\n      key: 'comande',\n      label: 'Comande',\n      description: 'Esporta tutte le comande con cavi assegnati e responsabili',\n      icon: <FileSpreadsheet className=\"h-4 w-4\" />,\n      available: false\n    },\n    {\n      key: 'certificazioni',\n      label: 'Certificazioni',\n      description: 'Esporta tutte le certificazioni con esiti e responsabili',\n      icon: <FileSpreadsheet className=\"h-4 w-4\" />,\n      available: false\n    },\n    {\n      key: 'responsabili',\n      label: 'Responsabili',\n      description: 'Esporta tutti i responsabili con contatti e ruoli',\n      icon: <FileSpreadsheet className=\"h-4 w-4\" />,\n      available: false\n    }\n  ]\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <DialogContent className=\"sm:max-w-[600px]\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Download className=\"h-5 w-5\" />\n            Esporta Dati Cantiere\n          </DialogTitle>\n          <DialogDescription>\n            Seleziona i dati da esportare dal cantiere {cantiere?.nome_cantiere}\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          {/* Opzioni di export */}\n          <div className=\"space-y-3\">\n            <Label className=\"text-sm font-medium\">Seleziona Dati da Esportare</Label>\n            \n            {exportOptions.map((option) => (\n              <div\n                key={option.key}\n                className={`flex items-start space-x-3 p-3 rounded-lg border ${\n                  option.available ? 'bg-white' : 'bg-gray-50'\n                }`}\n              >\n                <Checkbox\n                  id={option.key}\n                  checked={selectedExports[option.key as keyof typeof selectedExports]}\n                  onCheckedChange={(checked) => handleExportChange(option.key, checked as boolean)}\n                  disabled={!option.available || loading}\n                />\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center gap-2\">\n                    {option.icon}\n                    <Label\n                      htmlFor={option.key}\n                      className={`font-medium ${!option.available ? 'text-gray-500' : ''}`}\n                    >\n                      {option.label}\n                      {!option.available && (\n                        <span className=\"ml-2 text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded\">\n                          In sviluppo\n                        </span>\n                      )}\n                    </Label>\n                  </div>\n                  <p className={`text-sm mt-1 ${!option.available ? 'text-gray-400' : 'text-gray-600'}`}>\n                    {option.description}\n                  </p>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* Informazioni export */}\n          <div className=\"p-4 bg-blue-50 rounded-lg\">\n            <Label className=\"text-sm font-medium\">Informazioni Export</Label>\n            <ul className=\"mt-2 space-y-1 text-sm text-gray-600\">\n              <li>• I file saranno scaricati in formato Excel (.xlsx)</li>\n              <li>• I nomi file includeranno data e nome cantiere</li>\n              <li>• I dati esportati riflettono lo stato attuale del database</li>\n              <li>• L'export non modifica i dati originali</li>\n            </ul>\n          </div>\n\n          {/* Riepilogo */}\n          {getSelectedCount() > 0 && (\n            <div className=\"p-4 bg-gray-50 rounded-lg\">\n              <Label className=\"text-sm font-medium\">Riepilogo Export</Label>\n              <div className=\"mt-2 space-y-1 text-sm\">\n                <div><strong>Cantiere:</strong> {cantiere?.nome_cantiere}</div>\n                <div><strong>File da scaricare:</strong> {getSelectedCount()}</div>\n                <div><strong>Data export:</strong> {new Date().toLocaleDateString('it-IT')}</div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={onClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button\n            onClick={handleExportAll}\n            disabled={loading || getSelectedCount() === 0}\n          >\n            {loading ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <Download className=\"h-4 w-4 mr-2\" />}\n            Esporta {getSelectedCount() > 0 ? `(${getSelectedCount()})` : ''}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAjBA;;;;;;;;;;;AA0Be,SAAS,iBAAiB,EACvC,IAAI,EACJ,OAAO,EACP,SAAS,EACT,OAAO,EACe;IACtB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrD,MAAM;QACN,QAAQ;QACR,SAAS;QACT,gBAAgB;QAChB,cAAc;IAChB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,qBAAqB,CAAC,YAAoB;QAC9C,mBAAmB,CAAA,OAAQ,CAAC;gBAC1B,GAAG,IAAI;gBACP,CAAC,WAAW,EAAE;YAChB,CAAC;IACH;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,iHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,SAAS,WAAW;YAE/D,+BAA+B;YAC/B,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC,IAAI,KAAK;gBAAC,SAAS,IAAI;aAAC;YAC/D,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,YAAY,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,aAAa,CAAC,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;YAC7G,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,KAAK,MAAM;YACX,OAAO,GAAG,CAAC,eAAe,CAAC;YAE3B,UAAU;QACZ,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,iHAAA,CAAA,WAAQ,CAAC,YAAY,CAAC,SAAS,WAAW;YAEjE,+BAA+B;YAC/B,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC,IAAI,KAAK;gBAAC,SAAS,IAAI;aAAC;YAC/D,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,YAAY,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,aAAa,CAAC,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;YAC/G,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,KAAK,MAAM;YACX,OAAO,GAAG,CAAC,eAAe,CAAC;YAE3B,UAAU;QACZ,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,UAAU,EAAE;YAElB,IAAI,gBAAgB,IAAI,EAAE;gBACxB,QAAQ,IAAI,CAAC;YACf;YAEA,IAAI,gBAAgB,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC;YACf;YAEA,sEAAsE;YACtE,IAAI,gBAAgB,OAAO,EAAE,CAC7B;YAEA,IAAI,gBAAgB,cAAc,EAAE,CACpC;YAEA,IAAI,gBAAgB,YAAY,EAAE,CAClC;YAEA,MAAM,QAAQ,GAAG,CAAC;YAElB,MAAM,cAAc,OAAO,MAAM,CAAC,iBAAiB,MAAM,CAAC,SAAS,MAAM;YACzE,UAAU,CAAC,mBAAmB,EAAE,YAAY,eAAe,CAAC;YAC5D;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,OAAO,OAAO,MAAM,CAAC,iBAAiB,MAAM,CAAC,SAAS,MAAM;IAC9D;IAEA,MAAM,gBAAgB;QACpB;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,WAAW;QACb;QACA;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,4NAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,WAAW;QACb;QACA;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,4NAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,WAAW;QACb;QACA;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,4NAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,WAAW;QACb;QACA;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,4NAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,WAAW;QACb;KACD;IAED,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGlC,8OAAC,kIAAA,CAAA,oBAAiB;;gCAAC;gCAC2B,UAAU;;;;;;;;;;;;;8BAI1D,8OAAC;oBAAI,WAAU;;wBACZ,uBACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;sCAKvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;gCAEtC,cAAc,GAAG,CAAC,CAAC,uBAClB,8OAAC;wCAEC,WAAW,CAAC,iDAAiD,EAC3D,OAAO,SAAS,GAAG,aAAa,cAChC;;0DAEF,8OAAC,oIAAA,CAAA,WAAQ;gDACP,IAAI,OAAO,GAAG;gDACd,SAAS,eAAe,CAAC,OAAO,GAAG,CAAiC;gDACpE,iBAAiB,CAAC,UAAY,mBAAmB,OAAO,GAAG,EAAE;gDAC7D,UAAU,CAAC,OAAO,SAAS,IAAI;;;;;;0DAEjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;4DACZ,OAAO,IAAI;0EACZ,8OAAC,iIAAA,CAAA,QAAK;gEACJ,SAAS,OAAO,GAAG;gEACnB,WAAW,CAAC,YAAY,EAAE,CAAC,OAAO,SAAS,GAAG,kBAAkB,IAAI;;oEAEnE,OAAO,KAAK;oEACZ,CAAC,OAAO,SAAS,kBAChB,8OAAC;wEAAK,WAAU;kFAA2D;;;;;;;;;;;;;;;;;;kEAMjF,8OAAC;wDAAE,WAAW,CAAC,aAAa,EAAE,CAAC,OAAO,SAAS,GAAG,kBAAkB,iBAAiB;kEAClF,OAAO,WAAW;;;;;;;;;;;;;uCA3BlB,OAAO,GAAG;;;;;;;;;;;sCAmCrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;wBAKP,qBAAqB,mBACpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAkB;gDAAE,UAAU;;;;;;;sDAC3C,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAA2B;gDAAE;;;;;;;sDAC1C,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAqB;gDAAE,IAAI,OAAO,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;8BAM1E,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAS,UAAU;sCAAS;;;;;;sCAG/D,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,WAAW,uBAAuB;;gCAE3C,wBAAU,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAAiC,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAkB;gCAC5F,qBAAqB,IAAI,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC,GAAG;;;;;;;;;;;;;;;;;;;;;;;;AAM1E", "debugId": null}}]}