(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__dc15d093._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/src/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "middleware": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
;
// Rate limiting store (in produzione usare Redis)
const rateLimitStore = new Map();
// Rate limiting middleware
const checkRateLimit = (request, maxRequests, windowMs)=>{
    const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
    const key = `${ip}-${request.nextUrl.pathname}`;
    const now = Date.now();
    const record = rateLimitStore.get(key);
    if (!record || now > record.resetTime) {
        rateLimitStore.set(key, {
            count: 1,
            resetTime: now + windowMs
        });
        return true;
    }
    if (record.count >= maxRequests) {
        return false;
    }
    record.count++;
    return true;
};
// Security headers avanzati
const securityHeaders = {
    'X-XSS-Protection': '1; mode=block',
    'X-Frame-Options': 'DENY',
    'X-Content-Type-Options': 'nosniff',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'geolocation=(), microphone=(), camera=()',
    'Content-Security-Policy': [
        "default-src 'self'",
        "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
        "style-src 'self' 'unsafe-inline'",
        "img-src 'self' data: blob:",
        "connect-src 'self' http://localhost:8001",
        "frame-ancestors 'none'"
    ].join('; ')
};
function middleware(request) {
    const response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    // In modalità sviluppo, pulisci il rate limiting store ogni tanto
    if ("TURBOPACK compile-time truthy", 1) {
        const now = Date.now();
        // Pulisci entries scadute ogni 30 secondi
        if (Math.random() < 0.1) {
            for (const [key, record] of rateLimitStore.entries()){
                if (now > record.resetTime) {
                    rateLimitStore.delete(key);
                }
            }
        }
    }
    // Applica security headers avanzati
    Object.entries(securityHeaders).forEach(([key, value])=>{
        response.headers.set(key, value);
    });
    // Rate limiting per API routes
    if (request.nextUrl.pathname.startsWith('/api/')) {
        if (!checkRateLimit(request, 100, 60000)) {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"]('Rate limit exceeded', {
                status: 429,
                headers: {
                    'Retry-After': '60'
                }
            });
        }
    }
    // Rate limiting per login (aumentato per testing)
    if (request.nextUrl.pathname === '/api/auth/login') {
        if (!checkRateLimit(request, 50, 60000)) {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"]('Too many login attempts', {
                status: 429,
                headers: {
                    'Retry-After': '60'
                }
            });
        }
    }
    // Blocca User-Agent sospetti
    const userAgent = request.headers.get('user-agent') || '';
    const suspiciousAgents = [
        'sqlmap',
        'nikto',
        'nmap',
        'burpsuite'
    ];
    if (suspiciousAgents.some((agent)=>userAgent.toLowerCase().includes(agent))) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"]('Forbidden', {
            status: 403
        });
    }
    // Blocca payload sospetti
    const url = request.nextUrl.toString();
    if (/[<>\"']|union|select|insert|javascript:/gi.test(url)) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"]('Bad Request', {
            status: 400
        });
    }
    // Log accessi sensibili
    if (request.nextUrl.pathname.startsWith('/admin')) {
        const ip = request.ip || 'unknown';
    }
    return response;
}
const config = {
    matcher: [
        /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */ '/((?!api|_next/static|_next/image|favicon.ico|public).*)'
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__dc15d093._.js.map