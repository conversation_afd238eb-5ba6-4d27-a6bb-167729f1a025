(()=>{var e={};e.id=6222,e.ids=[6222],e.modules={2461:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>P});var s=t(60687),r=t(43210),n=t(16189),i=t(44493),o=t(29523),l=t(89667),c=t(80013),d=t(6211),m=t(63503),x=t(63213),u=t(62185),h=t(41862),p=t(99270),g=t(96474),f=t(93613),b=t(17313),v=t(70615),j=t(12597),w=t(13861),N=t(53411),y=t(63143);let _=(0,t(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var C=t(64021),z=t(84027),A=t(5336);function P(){let{user:e,isAuthenticated:a,isLoading:t}=(0,x.A)(),P=(0,n.useRouter)(),[k,S]=(0,r.useState)([]),[M,E]=(0,r.useState)(!0),[q,$]=(0,r.useState)(""),[I,F]=(0,r.useState)({}),[J,L]=(0,r.useState)(!1),[G,D]=(0,r.useState)(""),[R,O]=(0,r.useState)(!1),[T,B]=(0,r.useState)(!1),[V,Z]=(0,r.useState)(!1),[H,U]=(0,r.useState)(!1),[X,W]=(0,r.useState)(null),[K,Y]=(0,r.useState)({commessa:"",descrizione:"",nome_cliente:"",indirizzo_cantiere:"",citta_cantiere:"",nazione_cantiere:"",password_cantiere:"",codice_univoco:""}),[Q,ee]=(0,r.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[ea,et]=(0,r.useState)("change"),[es,er]=(0,r.useState)(""),[en,ei]=(0,r.useState)(!1),[eo,el]=(0,r.useState)(!1),[ec,ed]=(0,r.useState)({}),[em,ex]=(0,r.useState)({}),eu=async()=>{try{E(!0);let e=await u._I.getCantieri();S(e),await eh(e)}catch(e){$("Errore nel caricamento dei cantieri")}finally{E(!1)}},eh=async e=>{try{L(!0);let a=e.map(async e=>{try{let a=await u._I.getCantiereStatistics(e.id_cantiere);return{id:e.id_cantiere,stats:a}}catch(a){return console.error(`Errore nel caricamento statistiche cantiere ${e.id_cantiere}:`,a),{id:e.id_cantiere,stats:{percentuale_avanzamento:0}}}}),t=(await Promise.all(a)).reduce((e,{id:a,stats:t})=>(e[a]=t,e),{});F(t)}catch(e){console.error("Errore nel caricamento delle statistiche:",e)}finally{L(!1)}},ep=async()=>{try{await u._I.createCantiere(K),O(!1),Y({commessa:"",descrizione:"",nome_cliente:"",indirizzo_cantiere:"",citta_cantiere:"",nazione_cantiere:"",password_cantiere:"",codice_univoco:""}),eu()}catch(e){$("Errore nella creazione del cantiere")}},eg=async()=>{if(X)try{await u._I.updateCantiere(X.id_cantiere,K),B(!1),W(null),eu()}catch(e){$("Errore nella modifica del cantiere")}},ef=e=>{localStorage.setItem("selectedCantiereId",e.id_cantiere.toString()),localStorage.setItem("selectedCantiereName",e.commessa),P.push(`/cantieri/${e.id_cantiere}`)},eb=e=>{W(e),Y({commessa:e.commessa||"",descrizione:e.descrizione||"",nome_cliente:e.nome_cliente||"",indirizzo_cantiere:e.indirizzo_cantiere||"",citta_cantiere:e.citta_cantiere||"",nazione_cantiere:e.nazione_cantiere||"",password_cantiere:e.password_cantiere||"",codice_univoco:e.codice_univoco||""}),B(!0)},ev=async()=>{if(X){if(Q.newPassword!==Q.confirmPassword)return void $("Le password non coincidono");if(!Q.currentPassword)return void $("Inserisci la password attuale per confermare il cambio");if(!Q.newPassword||Q.newPassword.length<6)return void $("La nuova password deve essere di almeno 6 caratteri");try{E(!0),$("");let e=await fetch(`http://localhost:8001/api/cantieri/${X.id_cantiere}/change-password`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`},body:JSON.stringify({password_attuale:Q.currentPassword,password_nuova:Q.newPassword,conferma_password:Q.confirmPassword})});if(!e.ok){let a=await e.json();throw Error(a.detail||"Errore nel cambio password")}let a=await e.json();if(a.success)ee({currentPassword:"",newPassword:"",confirmPassword:""}),Z(!1),$(""),alert(a.message||"Password cambiata con successo");else throw Error(a.message||"Errore nel cambio password")}catch(e){$(e instanceof Error?e.message:"Errore nel cambio password")}finally{E(!1)}}},ej=async e=>{try{await navigator.clipboard.writeText(e)}catch(e){}},ew=async e=>{let a=e.id_cantiere;if(ec[a])ed(e=>({...e,[a]:!1})),ex(e=>({...e,[a]:""}));else if(em[a])ed(e=>({...e,[a]:!0}));else try{el(!0);let e=localStorage.getItem("token")||localStorage.getItem("access_token"),t=await fetch(`http://localhost:8001/api/cantieri/${a}/view-password`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e}`}});if(!t.ok){let e=await t.json();throw Error(e.detail||"Errore nel recupero password")}let s=await t.json();ex(e=>({...e,[a]:s.password_cantiere})),ed(e=>({...e,[a]:!0}))}catch(e){$(e instanceof Error?e.message:"Errore nel recupero password")}finally{el(!1)}},eN=k.filter(e=>e.commessa.toLowerCase().includes(G.toLowerCase())||e.descrizione?.toLowerCase().includes(G.toLowerCase())||e.nome_cliente?.toLowerCase().includes(G.toLowerCase()));return t?(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsx)(h.A,{className:"h-8 w-8 animate-spin"})}):(0,s.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("div",{className:"flex items-center gap-4",children:(0,s.jsxs)("div",{className:"relative w-80",children:[(0,s.jsx)(p.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(l.p,{placeholder:"Cerca per commessa, descrizione o cliente...",value:G,onChange:e=>D(e.target.value),className:"pl-8 w-full"})]})}),(0,s.jsxs)(m.lG,{open:R,onOpenChange:O,children:[(0,s.jsx)(m.zM,{asChild:!0,children:(0,s.jsxs)(o.$,{className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:[(0,s.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"Nuovo Cantiere"]})}),(0,s.jsxs)(m.Cf,{className:"sm:max-w-[425px]",children:[(0,s.jsxs)(m.c7,{children:[(0,s.jsx)(m.L3,{children:"Crea Nuovo Cantiere"}),(0,s.jsx)(m.rr,{children:"Inserisci i dettagli del nuovo cantiere"})]}),(0,s.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(c.J,{htmlFor:"commessa",className:"text-right",children:"Commessa"}),(0,s.jsx)(l.p,{id:"commessa",value:K.commessa,onChange:e=>Y({...K,commessa:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(c.J,{htmlFor:"descrizione",className:"text-right",children:"Descrizione"}),(0,s.jsx)(l.p,{id:"descrizione",value:K.descrizione,onChange:e=>Y({...K,descrizione:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(c.J,{htmlFor:"nome_cliente",className:"text-right",children:"Cliente"}),(0,s.jsx)(l.p,{id:"nome_cliente",value:K.nome_cliente,onChange:e=>Y({...K,nome_cliente:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(c.J,{htmlFor:"password_cantiere",className:"text-right",children:"Password"}),(0,s.jsx)(l.p,{id:"password_cantiere",type:"password",value:K.password_cantiere,onChange:e=>Y({...K,password_cantiere:e.target.value}),className:"col-span-3"})]})]}),(0,s.jsx)(m.Es,{children:(0,s.jsx)(o.$,{onClick:ep,className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:"Crea Cantiere"})})]})]})]}),q&&(0,s.jsx)("div",{className:"mb-4 p-4 border border-red-200 rounded-lg bg-red-50",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(f.A,{className:"h-4 w-4 text-red-600 mr-2"}),(0,s.jsx)("span",{className:"text-red-800",children:q})]})}),M?(0,s.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,s.jsx)(h.A,{className:"h-8 w-8 animate-spin"})}):0===eN.length?(0,s.jsx)(i.Zp,{children:(0,s.jsxs)(i.Wu,{className:"flex flex-col items-center justify-center py-8",children:[(0,s.jsx)(b.A,{className:"h-12 w-12 text-muted-foreground mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Nessun cantiere trovato"}),(0,s.jsx)("p",{className:"text-muted-foreground text-center mb-4",children:G?"Nessun cantiere corrisponde ai criteri di ricerca":"Crea il tuo primo cantiere per iniziare"}),!G&&(0,s.jsxs)(o.$,{onClick:()=>O(!0),className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:[(0,s.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"Crea Primo Cantiere"]})]})}):(0,s.jsx)(i.Zp,{children:(0,s.jsxs)(d.XI,{children:[(0,s.jsx)(d.A0,{children:(0,s.jsxs)(d.Hj,{className:"border-b border-gray-200",children:[(0,s.jsx)(d.nd,{className:"font-semibold text-gray-700",children:"Commessa"}),(0,s.jsx)(d.nd,{className:"font-semibold text-gray-700",children:"Descrizione"}),(0,s.jsx)(d.nd,{className:"font-semibold text-gray-700",children:"Cliente"}),(0,s.jsx)(d.nd,{className:"font-semibold text-gray-700",children:"Data Creazione"}),(0,s.jsx)(d.nd,{className:"font-semibold text-gray-700",children:"Codice Accesso"}),(0,s.jsx)(d.nd,{className:"font-semibold text-gray-700",children:"Password Cantiere"}),(0,s.jsx)(d.nd,{className:"font-semibold text-gray-700 w-32",children:"Avanzamento"}),(0,s.jsx)(d.nd,{className:"font-semibold text-gray-700 text-center",children:"Progresso %"}),(0,s.jsx)(d.nd,{className:"text-center font-semibold text-gray-700 w-48",children:"Azioni"})]})}),(0,s.jsx)(d.BF,{children:eN.map(e=>(0,s.jsxs)(d.Hj,{className:"hover:bg-gray-50/50 transition-colors",children:[(0,s.jsx)(d.nA,{className:"font-semibold text-gray-900 py-4",children:e.commessa}),(0,s.jsx)(d.nA,{className:"text-gray-700 py-4",children:e.descrizione}),(0,s.jsx)(d.nA,{className:"text-gray-700 py-4",children:e.nome_cliente}),(0,s.jsx)(d.nA,{className:"text-gray-600 py-4",children:new Date(e.data_creazione).toLocaleDateString()}),(0,s.jsx)(d.nA,{className:"py-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("code",{className:"text-sm bg-blue-50 text-blue-700 px-3 py-1.5 rounded-md font-mono border border-blue-200",children:e.codice_univoco}),(0,s.jsx)(o.$,{size:"sm",variant:"ghost",className:"h-7 w-7 p-0 text-gray-400 hover:bg-gray-50 hover:text-gray-600 transition-colors",title:"Copia codice",onClick:()=>ej(e.codice_univoco),children:(0,s.jsx)(v.A,{className:"h-3 w-3"})})]})}),(0,s.jsx)(d.nA,{className:"py-4",children:(0,s.jsx)("div",{className:"flex items-center gap-3",children:(0,s.jsx)("div",{className:"flex items-center gap-2",children:ec[e.id_cantiere]&&em[e.id_cantiere]?(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("code",{className:"text-sm bg-green-50 text-green-700 px-2 py-1 rounded border border-green-200 font-mono",children:em[e.id_cantiere]}),(0,s.jsx)(o.$,{size:"sm",variant:"ghost",className:"h-7 w-7 p-0 text-green-600 hover:bg-green-50 hover:text-green-700 transition-colors",title:"Nascondi password",onClick:()=>ew(e),children:(0,s.jsx)(j.A,{className:"h-4 w-4"})})]}):(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,s.jsx)("span",{className:"text-sm text-gray-600 font-medium",children:"Configurata"})]}),(0,s.jsx)(o.$,{size:"sm",variant:"ghost",className:"h-7 w-7 p-0 text-blue-600 hover:bg-blue-50 hover:text-blue-700 transition-colors",title:"Mostra password",onClick:()=>ew(e),disabled:eo,children:eo?(0,s.jsx)(h.A,{className:"h-4 w-4 animate-spin"}):(0,s.jsx)(w.A,{className:"h-4 w-4"})})]})})})}),(0,s.jsx)(d.nA,{className:"py-4",children:(0,s.jsx)("div",{className:"flex items-center gap-2",children:J?(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 animate-spin text-gray-400"}),(0,s.jsx)("span",{className:"text-sm text-gray-500",children:"Caricamento..."})]}):(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"flex-1 min-w-[120px]",children:(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 shadow-inner",children:(0,s.jsx)("div",{className:`h-3 rounded-full transition-all duration-500 ease-out shadow-sm ${(I[e.id_cantiere]?.percentuale_avanzamento||0)>=90?"bg-gradient-to-r from-green-500 to-green-600":(I[e.id_cantiere]?.percentuale_avanzamento||0)>=75?"bg-gradient-to-r from-blue-500 to-blue-600":(I[e.id_cantiere]?.percentuale_avanzamento||0)>=50?"bg-gradient-to-r from-yellow-500 to-yellow-600":(I[e.id_cantiere]?.percentuale_avanzamento||0)>=25?"bg-gradient-to-r from-orange-500 to-orange-600":"bg-gradient-to-r from-red-500 to-red-600"}`,style:{width:`${Math.min(I[e.id_cantiere]?.percentuale_avanzamento||0,100)}%`}})})})})})}),(0,s.jsx)(d.nA,{className:"py-4 text-center",children:J?(0,s.jsx)(h.A,{className:"h-4 w-4 animate-spin text-gray-400 mx-auto"}):(0,s.jsxs)("div",{className:"flex items-center justify-center gap-1",children:[(0,s.jsx)(N.A,{className:"h-4 w-4 text-gray-500"}),(0,s.jsxs)("span",{className:`text-sm font-semibold ${(I[e.id_cantiere]?.percentuale_avanzamento||0)>=90?"text-green-700":(I[e.id_cantiere]?.percentuale_avanzamento||0)>=75?"text-blue-700":(I[e.id_cantiere]?.percentuale_avanzamento||0)>=50?"text-yellow-700":(I[e.id_cantiere]?.percentuale_avanzamento||0)>=25?"text-orange-700":"text-red-700"}`,children:[(I[e.id_cantiere]?.percentuale_avanzamento||0).toFixed(1),"%"]})]})}),(0,s.jsx)(d.nA,{className:"text-center py-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsxs)(o.$,{size:"sm",variant:"outline",onClick:()=>eb(e),className:"h-9 px-3 text-gray-600 border-gray-200 hover:bg-gray-50 hover:text-gray-700 hover:border-gray-300 transition-all duration-200 ease-in-out",title:"Modifica dati cantiere",children:[(0,s.jsx)(y.A,{className:"h-4 w-4 mr-1.5"}),"Modifica"]}),(0,s.jsxs)(o.$,{size:"sm",onClick:()=>ef(e),className:"h-9 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium transition-colors duration-200 ease-in-out",title:"Accedi al cantiere",children:[(0,s.jsx)(_,{className:"h-4 w-4 mr-1.5"}),"Accedi"]})]})})]},e.id_cantiere))})]})}),(0,s.jsx)(m.lG,{open:T,onOpenChange:B,children:(0,s.jsxs)(m.Cf,{className:"sm:max-w-[425px]",children:[(0,s.jsxs)(m.c7,{children:[(0,s.jsx)(m.L3,{children:"Modifica Cantiere"}),(0,s.jsx)(m.rr,{children:"Modifica i dettagli del cantiere selezionato"})]}),(0,s.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(c.J,{htmlFor:"edit-commessa",className:"text-right",children:"Commessa"}),(0,s.jsx)(l.p,{id:"edit-commessa",value:K.commessa,onChange:e=>Y({...K,commessa:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(c.J,{htmlFor:"edit-descrizione",className:"text-right",children:"Descrizione"}),(0,s.jsx)(l.p,{id:"edit-descrizione",value:K.descrizione,onChange:e=>Y({...K,descrizione:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(c.J,{htmlFor:"edit-nome_cliente",className:"text-right",children:"Cliente"}),(0,s.jsx)(l.p,{id:"edit-nome_cliente",value:K.nome_cliente,onChange:e=>Y({...K,nome_cliente:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(c.J,{htmlFor:"edit-indirizzo_cantiere",className:"text-right",children:"Indirizzo"}),(0,s.jsx)(l.p,{id:"edit-indirizzo_cantiere",value:K.indirizzo_cantiere,onChange:e=>Y({...K,indirizzo_cantiere:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(c.J,{htmlFor:"edit-citta_cantiere",className:"text-right",children:"Citt\xe0"}),(0,s.jsx)(l.p,{id:"edit-citta_cantiere",value:K.citta_cantiere,onChange:e=>Y({...K,citta_cantiere:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(c.J,{htmlFor:"edit-nazione_cantiere",className:"text-right",children:"Nazione"}),(0,s.jsx)(l.p,{id:"edit-nazione_cantiere",value:K.nazione_cantiere,onChange:e=>Y({...K,nazione_cantiere:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(c.J,{className:"text-right",children:"Password"}),(0,s.jsx)("div",{className:"col-span-3",children:(0,s.jsxs)(o.$,{type:"button",variant:"outline",onClick:()=>{Z(!0)},className:"w-full h-10 justify-start text-left bg-gray-50 hover:bg-gray-100 border-gray-200 hover:border-gray-300 transition-colors",children:[(0,s.jsx)(C.A,{className:"h-4 w-4 mr-2 text-gray-500"}),(0,s.jsx)("span",{className:"text-gray-700",children:"Modifica Password"})]})})]})]}),(0,s.jsxs)(m.Es,{children:[(0,s.jsx)(o.$,{onClick:()=>B(!1),className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:"Annulla"}),(0,s.jsx)(o.$,{onClick:eg,className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:"Salva Modifiche"})]})]})}),(0,s.jsx)(m.lG,{open:V,onOpenChange:e=>{Z(e),e||(ee({currentPassword:"",newPassword:"",confirmPassword:""}),$(""))},children:(0,s.jsxs)(m.Cf,{className:"sm:max-w-[600px]",children:[(0,s.jsxs)(m.c7,{children:[(0,s.jsxs)(m.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(C.A,{className:"h-5 w-5"}),"Gestione Password - ",X?.commessa]}),(0,s.jsx)(m.rr,{children:"Modifica la password di accesso al cantiere"})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-medium flex items-center gap-2",children:[(0,s.jsx)(z.A,{className:"h-5 w-5"}),"Cambia Password"]}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Inserisci la password attuale e la nuova password"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"current-password-change",children:"Password Attuale"}),(0,s.jsx)(l.p,{id:"current-password-change",type:"password",placeholder:"Password attuale per conferma",value:Q.currentPassword,onChange:e=>ee({...Q,currentPassword:e.target.value})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"new-password",children:"Nuova Password"}),(0,s.jsx)(l.p,{id:"new-password",type:"password",placeholder:"Inserisci la nuova password",value:Q.newPassword,onChange:e=>ee({...Q,newPassword:e.target.value})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"confirm-password",children:"Conferma Nuova Password"}),(0,s.jsx)(l.p,{id:"confirm-password",type:"password",placeholder:"Conferma la nuova password",value:Q.confirmPassword,onChange:e=>ee({...Q,confirmPassword:e.target.value})})]}),(0,s.jsxs)(o.$,{onClick:ev,disabled:M||!Q.currentPassword||!Q.newPassword||!Q.confirmPassword,className:"w-full relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:[M?(0,s.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,s.jsx)(z.A,{className:"mr-2 h-4 w-4"}),"Cambia Password"]})]})]}),q&&(0,s.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(f.A,{className:"h-5 w-5 text-red-600"}),(0,s.jsx)("span",{className:"font-medium text-red-800",children:"Errore"})]}),(0,s.jsx)("p",{className:"text-sm text-red-700 mt-1",children:q})]})]}),(0,s.jsx)(m.Es,{children:(0,s.jsx)(o.$,{variant:"outline",onClick:()=>Z(!1),children:"Chiudi"})})]})}),(0,s.jsx)(m.lG,{open:H,onOpenChange:e=>{U(e),e||(er(""),W(null),$(""))},children:(0,s.jsxs)(m.Cf,{className:"sm:max-w-[500px]",children:[(0,s.jsxs)(m.c7,{children:[(0,s.jsxs)(m.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(w.A,{className:"h-5 w-5 text-green-600"}),"Password Cantiere - ",X?.commessa]}),(0,s.jsxs)(m.rr,{children:["Password per l'accesso al cantiere con codice: ",(0,s.jsx)("code",{className:"bg-muted px-2 py-1 rounded",children:X?.codice_univoco})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[es&&(0,s.jsxs)("div",{className:"p-4 bg-green-50 border border-green-200 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,s.jsx)(A.A,{className:"h-5 w-5 text-green-600"}),(0,s.jsx)("span",{className:"font-medium text-green-800",children:"Password del Cantiere"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("code",{className:"flex-1 text-lg font-mono bg-white p-3 rounded border border-green-300 text-center",children:es}),(0,s.jsx)(o.$,{size:"sm",variant:"outline",onClick:()=>ej(es),className:"text-green-600 hover:bg-green-50 border-green-300",title:"Copia password",children:(0,s.jsx)(v.A,{className:"h-4 w-4"})})]}),(0,s.jsxs)("p",{className:"text-sm text-green-700 mt-2",children:["Utilizza questa password insieme al codice univoco ",(0,s.jsx)("strong",{children:X?.codice_univoco})," per accedere al cantiere."]})]}),q&&(0,s.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(f.A,{className:"h-5 w-5 text-red-600"}),(0,s.jsx)("span",{className:"font-medium text-red-800",children:"Errore"})]}),(0,s.jsx)("p",{className:"text-sm text-red-700 mt-1",children:q})]})]}),(0,s.jsx)(m.Es,{children:(0,s.jsx)(o.$,{variant:"outline",onClick:()=>U(!1),children:"Chiudi"})})]})})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,a,t)=>{"use strict";t.d(a,{A0:()=>i,BF:()=>o,Hj:()=>l,XI:()=>n,nA:()=>d,nd:()=>c});var s=t(60687);t(43210);var r=t(4780);function n({className:e,...a}){return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm border-collapse",e),...a})})}function i({className:e,...a}){return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",e),...a})}function o({className:e,...a}){return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",e),...a})}function l({className:e,...a}){return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("data-[state=selected]:bg-muted border-b",e),...a})}function c({className:e,...a}){return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...a})}function d({className:e,...a}){return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...a})}},7e3:(e,a,t)=>{Promise.resolve().then(t.bind(t,38921))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11367:(e,a,t)=>{Promise.resolve().then(t.bind(t,68529))},12412:e=>{"use strict";e.exports=require("assert")},12597:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34570:(e,a,t)=>{"use strict";t.d(a,{A:()=>l});var s=t(60687),r=t(43210),n=t(16189),i=t(63213),o=t(41862);function l({children:e,requiredRole:a,requiresUser:t=!1,requiresCantiere:l=!1,redirectTo:c="/login"}){let{user:d,cantiere:m,isAuthenticated:x,isLoading:u}=(0,i.A)();(0,n.useRouter)();let[h,p]=(0,r.useState)(!0);return u||h?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,s.jsx)(o.A,{className:"h-8 w-8 animate-spin text-blue-600"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Verifica autenticazione..."})]})}):(0,s.jsx)(s.Fragment,{children:e})}},36607:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>n});var s=t(60687),r=t(34570);function n({children:e}){return(0,s.jsx)(r.A,{requiresUser:!0,children:e})}},38921:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs_1\\\\src\\\\app\\\\cantieri\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_1\\src\\app\\cantieri\\layout.tsx","default")},41848:(e,a,t)=>{Promise.resolve().then(t.bind(t,36607))},44493:(e,a,t)=>{"use strict";t.d(a,{BT:()=>l,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>i});var s=t(60687);t(43210);var r=t(4780);function n({className:e,...a}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...a})}function i({className:e,...a}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...a})}function o({className:e,...a}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...a})}function l({className:e,...a}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...a})}function c({className:e,...a}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...a})}},50687:(e,a,t)=>{Promise.resolve().then(t.bind(t,2461))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},63503:(e,a,t)=>{"use strict";t.d(a,{Cf:()=>m,Es:()=>u,L3:()=>h,c7:()=>x,lG:()=>o,rr:()=>p,zM:()=>l});var s=t(60687);t(43210);var r=t(26134),n=t(11860),i=t(4780);function o({...e}){return(0,s.jsx)(r.bL,{"data-slot":"dialog",...e})}function l({...e}){return(0,s.jsx)(r.l9,{"data-slot":"dialog-trigger",...e})}function c({...e}){return(0,s.jsx)(r.ZL,{"data-slot":"dialog-portal",...e})}function d({className:e,...a}){return(0,s.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...a})}function m({className:e,children:a,showCloseButton:t=!0,...o}){return(0,s.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,s.jsx)(d,{}),(0,s.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...o,children:[a,t&&(0,s.jsxs)(r.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(n.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x({className:e,...a}){return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",e),...a})}function u({className:e,...a}){return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...a})}function h({className:e,...a}){return(0,s.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",e),...a})}function p({className:e,...a}){return(0,s.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...a})}},64021:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},68529:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs_1\\\\src\\\\app\\\\cantieri\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_1\\src\\app\\cantieri\\page.tsx","default")},70440:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>r});var s=t(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},70615:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},74075:e=>{"use strict";e.exports=require("zlib")},78148:(e,a,t)=>{"use strict";t.d(a,{b:()=>o});var s=t(43210),r=t(14163),n=t(60687),i=s.forwardRef((e,a)=>(0,n.jsx)(r.sG.label,{...e,ref:a,onMouseDown:a=>{a.target.closest("button, input, select, textarea")||(e.onMouseDown?.(a),!a.defaultPrevented&&a.detail>1&&a.preventDefault())}}));i.displayName="Label";var o=i},79551:e=>{"use strict";e.exports=require("url")},80013:(e,a,t)=>{"use strict";t.d(a,{J:()=>i});var s=t(60687);t(43210);var r=t(78148),n=t(4780);function i({className:e,...a}){return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...a})}},81630:e=>{"use strict";e.exports=require("http")},83254:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c});var s=t(65239),r=t(48088),n=t(88170),i=t.n(n),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(a,l);let c={children:["",{children:["cantieri",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,68529)),"C:\\CMS\\webapp-nextjs_1\\src\\app\\cantieri\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,38921)),"C:\\CMS\\webapp-nextjs_1\\src\\app\\cantieri\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\CMS\\webapp-nextjs_1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\CMS\\webapp-nextjs_1\\src\\app\\cantieri\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/cantieri/page",pathname:"/cantieri",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},83997:e=>{"use strict";e.exports=require("tty")},84027:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},89667:(e,a,t)=>{"use strict";t.d(a,{p:()=>i});var s=t(60687),r=t(43210),n=t(4780);let i=r.forwardRef(({className:e,type:a,...t},r)=>(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),ref:r,...t}));i.displayName="Input"},94735:e=>{"use strict";e.exports=require("events")},96474:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},99270:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var a=require("../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),s=a.X(0,[4447,6539,1658,4951],()=>t(83254));module.exports=s})();