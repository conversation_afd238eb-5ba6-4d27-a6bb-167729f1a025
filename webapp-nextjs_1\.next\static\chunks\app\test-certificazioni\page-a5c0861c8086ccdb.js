(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1669],{381:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(19946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},17770:(e,t,a)=>{Promise.resolve().then(a.bind(a,97811))},25731:(e,t,a)=>{"use strict";a.d(t,{AR:()=>p,At:()=>o,CV:()=>d,Fw:()=>l,ZQ:()=>r,_I:()=>f,dG:()=>b,km:()=>u,kw:()=>m,l9:()=>g,mg:()=>v,om:()=>h,ug:()=>x});let i=a(23464).A.create({baseURL:"http://localhost:3000",timeout:3e4,headers:{"Content-Type":"application/json"}}),c=()=>localStorage.getItem("token")||localStorage.getItem("access_token"),n=()=>{["token","access_token","user_data","cantiere_data","selectedCantiereId","selectedCantiereName","isImpersonating","impersonatedUser"].forEach(e=>{localStorage.removeItem(e)})};i.interceptors.request.use(e=>{let t=c();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),i.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&(console.log("\uD83D\uDEA8 API: Token non valido o scaduto, pulizia dati e reindirizzamento"),n(),window.location.pathname.includes("/login")||(window.location.href="/login")),Promise.reject(e)});let s={get:async(e,t)=>(await i.get(e,t)).data,post:async(e,t,a)=>(await i.post(e,t,a)).data,put:async(e,t,a)=>(await i.put(e,t,a)).data,delete:async(e,t)=>(await i.delete(e,t)).data},r={login:async e=>(await i.post("/api/auth/login",{username:e.username,password:e.password})).data,loginCantiere:e=>s.post("/api/auth/login/cantiere",{codice_univoco:e.codice_cantiere,password:e.password_cantiere}),verifyToken:()=>s.post("/api/auth/test-token"),logout:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),window.location.href="/login"}},o={getCavi:(e,t)=>s.get("/api/cavi/".concat(e),{params:t}),getCavo:(e,t)=>s.get("/api/cavi/".concat(e,"/").concat(t)),checkCavo:(e,t)=>s.get("/api/cavi/".concat(e,"/check/").concat(t)),createCavo:(e,t)=>s.post("/api/cavi/".concat(e),t),updateCavo:(e,t,a)=>s.put("/api/cavi/".concat(e,"/").concat(t),a),deleteCavo:(e,t,a)=>s.delete("/api/cavi/".concat(e,"/").concat(t),{data:a}),updateMetriPosati:(e,t,a,i,c)=>s.post("/api/cavi/".concat(e,"/").concat(t,"/metri-posati"),{metri_posati:a,id_bobina:i,force_over:c||!1}),updateBobina:(e,t,a,i)=>s.post("/api/cavi/".concat(e,"/").concat(t,"/bobina"),{id_bobina:a,force_over:i||!1}),cancelInstallation:(e,t)=>s.post("/api/cavi/".concat(e,"/").concat(t,"/cancel-installation")),collegaCavo:(e,t,a,i)=>s.post("/api/cavi/".concat(e,"/").concat(t,"/collegamento"),{lato:a,responsabile:i}),scollegaCavo:(e,t,a)=>{let i={};return a&&(i.data={lato:a}),s.delete("/api/cavi/".concat(e,"/").concat(t,"/collegamento"),i)},markAsSpare:function(e,t,a){let i=!(arguments.length>3)||void 0===arguments[3]||arguments[3];return a?s.post("/api/cavi/".concat(e,"/").concat(t,"/mark-as-spare"),{force:i}):s.post("/api/cavi/".concat(e,"/").concat(t,"/reactivate-spare"),{})},debugCavi:e=>s.get("/api/cavi/debug/".concat(e)),debugCaviRaw:e=>s.get("/api/cavi/debug/raw/".concat(e))},l={getBobine:(e,t)=>s.get("/api/parco-cavi/".concat(e),{params:t}),getBobina:(e,t)=>s.get("/api/parco-cavi/".concat(e,"/").concat(t)),getBobineCompatibili:(e,t)=>s.get("/api/parco-cavi/".concat(e,"/compatibili"),{params:t}),createBobina:(e,t)=>s.post("/api/parco-cavi/".concat(e),t),updateBobina:(e,t,a)=>s.put("/api/parco-cavi/".concat(e,"/").concat(t),a),deleteBobina:(e,t)=>s.delete("/api/parco-cavi/".concat(e,"/").concat(t)),isFirstBobinaInsertion:e=>s.get("/api/parco-cavi/".concat(e,"/is-first-insertion")),updateBobina:(e,t,a)=>s.put("/api/parco-cavi/".concat(e,"/").concat(t),a),deleteBobina:(e,t)=>s.delete("/api/parco-cavi/".concat(e,"/").concat(t)),checkDisponibilita:(e,t,a)=>s.get("/api/parco-cavi/".concat(e,"/").concat(t,"/disponibilita"),{params:{metri_richiesti:a}})},d={getComande:e=>s.get("/api/comande/cantiere/".concat(e)),getComanda:(e,t)=>s.get("/api/comande/".concat(t)),getCaviComanda:e=>s.get("/api/comande/".concat(e,"/cavi")),createComanda:(e,t)=>s.post("/api/comande/cantiere/".concat(e),t),createComandaWithCavi:(e,t,a)=>s.post("/api/comande/cantiere/".concat(e,"/crea-con-cavi"),t,{params:{lista_id_cavi:a}}),updateDatiComanda:(e,t,a)=>s.put("/api/comande/".concat(e,"/").concat(t),a),updateComanda:(e,t,a)=>s.put("/api/comande/cantiere/".concat(e,"/").concat(t),a),deleteComanda:(e,t)=>s.delete("/api/comande/cantiere/".concat(e,"/").concat(t)),assegnaCavi:(e,t,a)=>s.post("/api/comande/cantiere/".concat(e,"/").concat(t,"/assegna-cavi"),{cavi_ids:a}),rimuoviCavi:(e,t,a)=>s.delete("/api/comande/cantiere/".concat(e,"/").concat(t,"/rimuovi-cavi"),{data:{cavi_ids:a}}),getStatistiche:e=>s.get("/api/comande/cantiere/".concat(e,"/statistiche")),cambiaStato:(e,t,a)=>s.put("/api/comande/cantiere/".concat(e,"/").concat(t,"/stato"),{nuovo_stato:a})},p={getResponsabili:e=>s.get("/api/responsabili/cantiere/".concat(e)),createResponsabile:(e,t)=>s.post("/api/responsabili/".concat(e),t),updateResponsabile:(e,t,a)=>s.put("/api/responsabili/".concat(e,"/").concat(t),a),deleteResponsabile:(e,t)=>s.delete("/api/responsabili/".concat(e,"/").concat(t))},u={getCertificazioni:(e,t)=>s.get("/api/cantieri/".concat(e,"/certificazioni"),{params:t?{filtro_cavo:t}:{}}),createCertificazione:(e,t)=>s.post("/api/cantieri/".concat(e,"/certificazioni"),t),getCertificazione:(e,t)=>s.get("/api/cantieri/".concat(e,"/certificazioni/").concat(t)),updateCertificazione:(e,t,a)=>s.put("/api/cantieri/".concat(e,"/certificazioni/").concat(t),a),deleteCertificazione:(e,t)=>s.delete("/api/cantieri/".concat(e,"/certificazioni/").concat(t)),generatePDF:(e,t)=>s.get("/api/cantieri/".concat(e,"/certificazioni/").concat(t,"/pdf"),{responseType:"blob"}),getStatistiche:e=>s.get("/api/cantieri/".concat(e,"/certificazioni/statistiche")),exportCertificazioni:(e,t)=>s.get("/api/cantieri/".concat(e,"/certificazioni/export"),{params:t,responseType:"blob"}),generateReport:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"completo";return s.get("/api/cantieri/".concat(e,"/certificazioni/report/").concat(t))},bulkDelete:(e,t)=>s.post("/api/cantieri/".concat(e,"/certificazioni/bulk-delete"),{ids:t}),generateBulkPdf:(e,t)=>s.post("/api/cantieri/".concat(e,"/certificazioni/bulk-pdf"),{ids:t},{responseType:"blob"}),validateCertificazione:(e,t)=>s.post("/api/cantieri/".concat(e,"/certificazioni/validate"),t)},m={getStrumenti:e=>s.get("/api/cantieri/".concat(e,"/strumenti")),createStrumento:(e,t)=>s.post("/api/cantieri/".concat(e,"/strumenti"),t),updateStrumento:(e,t,a)=>s.put("/api/cantieri/".concat(e,"/strumenti/").concat(t),a),deleteStrumento:(e,t)=>s.delete("/api/cantieri/".concat(e,"/strumenti/").concat(t))},g={getRapporti:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:100;return s.get("/api/cantieri/".concat(e,"/rapporti"),{params:{skip:t,limit:a}})},createRapporto:(e,t)=>s.post("/api/cantieri/".concat(e,"/rapporti"),t),getRapporto:(e,t)=>s.get("/api/cantieri/".concat(e,"/rapporti/").concat(t)),updateRapporto:(e,t,a)=>s.put("/api/cantieri/".concat(e,"/rapporti/").concat(t),a),deleteRapporto:(e,t)=>s.delete("/api/cantieri/".concat(e,"/rapporti/").concat(t)),aggiornaStatistiche:(e,t)=>s.post("/api/cantieri/".concat(e,"/rapporti/").concat(t,"/aggiorna-statistiche"))},h={getNonConformita:e=>s.get("/api/cantieri/".concat(e,"/non-conformita")),createNonConformita:(e,t)=>s.post("/api/cantieri/".concat(e,"/non-conformita"),t),updateNonConformita:(e,t,a)=>s.put("/api/cantieri/".concat(e,"/non-conformita/").concat(t),a),deleteNonConformita:(e,t)=>s.delete("/api/cantieri/".concat(e,"/non-conformita/").concat(t))},v={importCavi:(e,t,a)=>{let i=new FormData;return i.append("file",t),i.append("revisione",a),s.post("/api/excel/".concat(e,"/import-cavi"),i,{headers:{"Content-Type":"multipart/form-data"}})},importBobine:(e,t)=>{let a=new FormData;return a.append("file",t),s.post("/api/excel/".concat(e,"/import-parco-bobine"),a,{headers:{"Content-Type":"multipart/form-data"}})},exportCavi:e=>s.get("/api/excel/".concat(e,"/export-cavi"),{responseType:"blob"}),exportBobine:e=>s.get("/api/excel/".concat(e,"/export-parco-bobine"),{responseType:"blob"})},x={getReportAvanzamento:e=>s.get("/api/reports/".concat(e,"/avanzamento")),getReportBOQ:e=>s.get("/api/reports/".concat(e,"/boq")),getReportUtilizzoBobine:e=>s.get("/api/reports/".concat(e,"/storico-bobine")),getReportProgress:e=>s.get("/api/reports/".concat(e,"/progress")),getReportPosaPeriodo:(e,t,a)=>{let i=new URLSearchParams;t&&i.append("data_inizio",t),a&&i.append("data_fine",a);let c=i.toString();return s.get("/api/reports/".concat(e,"/posa-periodo").concat(c?"?".concat(c):""))}},f={getCantieri:()=>s.get("/api/cantieri"),getCantiere:e=>s.get("/api/cantieri/".concat(e)),createCantiere:e=>s.post("/api/cantieri",e),updateCantiere:(e,t)=>s.put("/api/cantieri/".concat(e),t),getCantiereStatistics:e=>s.get("/api/cantieri/".concat(e,"/statistics")),getWeatherData:e=>s.get("/api/cantieri/".concat(e,"/weather"))},b={getUsers:()=>s.get("/api/users"),getUser:e=>s.get("/api/users/".concat(e)),createUser:e=>s.post("/api/users",e),updateUser:(e,t)=>s.put("/api/users/".concat(e),t),deleteUser:e=>s.delete("/api/users/".concat(e)),toggleUserStatus:e=>s.get("/api/users/toggle/".concat(e)),checkExpiredUsers:()=>s.get("/api/users/check-expired"),impersonateUser:e=>s.post("/api/auth/impersonate",{user_id:e}),getDatabaseData:()=>s.get("/api/users/db-raw"),resetDatabase:()=>s.post("/api/admin/reset-database")}},26126:(e,t,a)=>{"use strict";a.d(t,{E:()=>o});var i=a(95155);a(12115);var c=a(99708),n=a(74466),s=a(59434);let r=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:a,asChild:n=!1,...o}=e,l=n?c.DX:"span";return(0,i.jsx)(l,{"data-slot":"badge",className:(0,s.cn)(r({variant:a}),t),...o})}},30285:(e,t,a)=>{"use strict";a.d(t,{$:()=>o,r:()=>r});var i=a(95155);a(12115);var c=a(99708),n=a(74466),s=a(59434);let r=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:a,size:n,asChild:o=!1,...l}=e,d=o?c.DX:"button";return(0,i.jsx)(d,{"data-slot":"button",className:(0,s.cn)(r({variant:a,size:n,className:t})),...l})}},38164:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(19946).A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},40646:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},50589:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(19946).A)("cloud",[["path",{d:"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z",key:"p7xjir"}]])},51154:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},57434:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},59434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n});var i=a(52596),c=a(39688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,c.QP)((0,i.$)(t))}},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>o,Wu:()=>l,ZB:()=>r,Zp:()=>n,aR:()=>s});var i=a(95155);a(12115);var c=a(59434);function n(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"card",className:(0,c.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function s(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"card-header",className:(0,c.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function r(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"card-title",className:(0,c.cn)("leading-none font-semibold",t),...a})}function o(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"card-description",className:(0,c.cn)("text-muted-foreground text-sm",t),...a})}function l(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"card-content",className:(0,c.cn)("px-6",t),...a})}},75021:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(19946).A)("test-tube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5c-1.4 0-2.5-1.1-2.5-2.5V2",key:"125lnx"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]])},85339:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},97811:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>x});var i=a(95155),c=a(12115),n=a(66695),s=a(30285),r=a(26126),o=a(40646),l=a(85339),d=a(75021),p=a(51154),u=a(57434),m=a(50589),g=a(38164),h=a(381),v=a(25731);function x(){let[e,t]=(0,c.useState)(!1),[a,x]=(0,c.useState)([]),[f]=(0,c.useState)(1),b=async()=>{var e,a,i,c,n,s,r,o,l,d;t(!0),x([]);let p=[];try{let e=await v._I.getWeatherData(f);p.push({test:"Dati Meteorologici",status:"success",message:"Temperatura: ".concat(e.data.temperature,"\xb0C, Umidit\xe0: ").concat(e.data.humidity,"%"),data:e.data})}catch(t){p.push({test:"Dati Meteorologici",status:"error",message:(null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||t.message})}try{let e=await v.At.getCavi(f),t=e.filter(e=>3>(e.collegamenti||0));p.push({test:"Caricamento Cavi",status:"success",message:"".concat(e.length," cavi totali, ").concat(t.length," non completamente collegati"),data:{totali:e.length,nonCollegati:t.length}})}catch(e){p.push({test:"Caricamento Cavi",status:"error",message:(null==(c=e.response)||null==(i=c.data)?void 0:i.detail)||e.message})}try{let e=await v.kw.getStrumenti(f),t=e.filter(e=>"ATTIVO"===e.stato);p.push({test:"Caricamento Strumenti",status:"success",message:"".concat(e.length," strumenti totali, ").concat(t.length," attivi"),data:{totali:e.length,attivi:t.length}})}catch(e){p.push({test:"Caricamento Strumenti",status:"error",message:(null==(s=e.response)||null==(n=s.data)?void 0:n.detail)||e.message})}try{let e=await v.km.getCertificazioni(f),t=e.filter(e=>"CONFORME"===e.stato_certificato);p.push({test:"Caricamento Certificazioni",status:"success",message:"".concat(e.length," certificazioni totali, ").concat(t.length," conformi"),data:{totali:e.length,conformi:t.length}})}catch(e){p.push({test:"Caricamento Certificazioni",status:"error",message:(null==(o=e.response)||null==(r=o.data)?void 0:r.detail)||e.message})}try{let e=(await v.At.getCavi(f)).find(e=>3>(e.collegamenti||0));e?p.push({test:"Collegamento Automatico",status:"info",message:"Cavo ".concat(e.id_cavo," disponibile per test collegamento (collegamenti: ").concat(e.collegamenti||0,"/3)"),data:{cavoId:e.id_cavo,collegamenti:e.collegamenti||0}}):p.push({test:"Collegamento Automatico",status:"warning",message:"Nessun cavo disponibile per test collegamento (tutti gi\xe0 collegati)"})}catch(e){p.push({test:"Collegamento Automatico",status:"error",message:(null==(d=e.response)||null==(l=d.data)?void 0:l.detail)||e.message})}x(p),t(!1)},j=e=>{switch(e){case"success":return(0,i.jsx)(o.A,{className:"h-5 w-5 text-green-600"});case"error":return(0,i.jsx)(l.A,{className:"h-5 w-5 text-red-600"});case"warning":return(0,i.jsx)(l.A,{className:"h-5 w-5 text-yellow-600"});case"info":return(0,i.jsx)(d.A,{className:"h-5 w-5 text-blue-600"});default:return(0,i.jsx)(p.A,{className:"h-5 w-5 animate-spin"})}},y=e=>{switch(e){case"success":return(0,i.jsx)(r.E,{className:"bg-green-100 text-green-800 border-green-200",children:"Successo"});case"error":return(0,i.jsx)(r.E,{className:"bg-red-100 text-red-800 border-red-200",children:"Errore"});case"warning":return(0,i.jsx)(r.E,{className:"bg-yellow-100 text-yellow-800 border-yellow-200",children:"Attenzione"});case"info":return(0,i.jsx)(r.E,{className:"bg-blue-100 text-blue-800 border-blue-200",children:"Info"});default:return(0,i.jsx)(r.E,{variant:"outline",children:"In corso"})}};return(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("h1",{className:"text-3xl font-bold text-slate-900 flex items-center gap-3",children:[(0,i.jsx)(d.A,{className:"h-8 w-8 text-blue-600"}),"Test Sistema Certificazioni"]}),(0,i.jsx)("p",{className:"text-slate-600 mt-1",children:"Verifica funzionalit\xe0 e automazioni CEI 64-8"})]}),(0,i.jsx)(s.$,{onClick:b,disabled:e,children:e?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(p.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Test in corso..."]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Avvia Test"]})})]}),(0,i.jsxs)(n.Zp,{children:[(0,i.jsxs)(n.aR,{children:[(0,i.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(u.A,{className:"h-5 w-5"}),"Funzionalit\xe0 Implementate"]}),(0,i.jsx)(n.BT,{children:"Sistema certificazioni CEI 64-8 con automazioni"})]}),(0,i.jsx)(n.Wu,{children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(m.A,{className:"h-4 w-4 text-blue-500"}),(0,i.jsx)("span",{className:"font-medium",children:"Dati Meteorologici Automatici"})]}),(0,i.jsx)("p",{className:"text-sm text-slate-600 ml-6",children:"Temperatura e umidit\xe0 rilevate automaticamente per il cantiere"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(g.A,{className:"h-4 w-4 text-green-500"}),(0,i.jsx)("span",{className:"font-medium",children:"Collegamento Automatico Cavi"})]}),(0,i.jsx)("p",{className:"text-sm text-slate-600 ml-6",children:"Collegamento automatico durante certificazione CEI 64-8"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(o.A,{className:"h-4 w-4 text-purple-500"}),(0,i.jsx)("span",{className:"font-medium",children:"Validazione Automatica"})]}),(0,i.jsx)("p",{className:"text-sm text-slate-600 ml-6",children:"Determinazione automatica conformit\xe0 in base ai valori"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(h.A,{className:"h-4 w-4 text-orange-500"}),(0,i.jsx)("span",{className:"font-medium",children:"Gestione Strumenti"})]}),(0,i.jsx)("p",{className:"text-sm text-slate-600 ml-6",children:"Tracking calibrazione e notifiche scadenze"})]})]})})]}),a.length>0&&(0,i.jsxs)(n.Zp,{children:[(0,i.jsxs)(n.aR,{children:[(0,i.jsx)(n.ZB,{children:"Risultati Test"}),(0,i.jsx)(n.BT,{children:"Verifica delle funzionalit\xe0 del sistema"})]}),(0,i.jsx)(n.Wu,{children:(0,i.jsx)("div",{className:"space-y-4",children:a.map((e,t)=>(0,i.jsxs)("div",{className:"flex items-start gap-3 p-3 rounded-lg border",children:[j(e.status),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("h4",{className:"font-medium",children:e.test}),y(e.status)]}),(0,i.jsx)("p",{className:"text-sm text-slate-600 mt-1",children:e.message}),e.data&&(0,i.jsx)("pre",{className:"text-xs bg-slate-100 p-2 rounded mt-2 overflow-auto",children:JSON.stringify(e.data,null,2)})]})]},t))})})]}),(0,i.jsxs)(n.Zp,{children:[(0,i.jsxs)(n.aR,{children:[(0,i.jsx)(n.ZB,{children:"Accesso al Sistema"}),(0,i.jsx)(n.BT,{children:"Link per testare l'interfaccia completa"})]}),(0,i.jsx)(n.Wu,{children:(0,i.jsxs)("div",{className:"flex gap-4",children:[(0,i.jsx)(s.$,{asChild:!0,children:(0,i.jsxs)("a",{href:"/certificazioni",target:"_blank",children:[(0,i.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Sistema Certificazioni"]})}),(0,i.jsx)(s.$,{variant:"outline",asChild:!0,children:(0,i.jsxs)("a",{href:"/cavi",target:"_blank",children:[(0,i.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Gestione Cavi"]})})]})})]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3464,3455,8441,1684,7358],()=>t(17770)),_N_E=e.O()}]);