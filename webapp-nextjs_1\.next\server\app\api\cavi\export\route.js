(()=>{var e={};e.id=8102,e.ids=[8102],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},90518:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>d,routeModule:()=>u,serverHooks:()=>x,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>l});var a={};r.r(a),r.d(a,{POST:()=>p});var o=r(96559),i=r(48088),s=r(37719),n=r(32190);async function p(e){try{let{selectedIds:t,cantiereId:r}=await e.json();if(!t||!Array.isArray(t)||0===t.length)return n.NextResponse.json({error:"Nessun cavo selezionato per l'esportazione"},{status:400});if(!r)return n.NextResponse.json({error:"ID cantiere mancante"},{status:400});let a=function(e){let t=e.map(e=>[e,"Sistema A","Utility B","Tipologia C","4x16","100.0","95.5","Partenza A","Arrivo B","123","Installato","Collegato","Certificato"]);return["ID Cavo,Sistema,Utility,Tipologia,Formazione,Metri Teorici,Metri Reali,Ubicazione Partenza,Ubicazione Arrivo,ID Bobina,Stato Installazione,Collegamenti,Certificato",...t.map(e=>e.join(","))].join("\n")}(t);return new n.NextResponse(a,{status:200,headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="cavi_export_${new Date().toISOString().split("T")[0]}.csv"`}})}catch(e){return n.NextResponse.json({error:"Errore interno del server"},{status:500})}}let u=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/cavi/export/route",pathname:"/api/cavi/export",filename:"route",bundlePath:"app/api/cavi/export/route"},resolvedPagePath:"C:\\CMS\\webapp-nextjs_1\\src\\app\\api\\cavi\\export\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:c,workUnitAsyncStorage:l,serverHooks:x}=u;function d(){return(0,s.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:l})}},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580],()=>r(90518));module.exports=a})();