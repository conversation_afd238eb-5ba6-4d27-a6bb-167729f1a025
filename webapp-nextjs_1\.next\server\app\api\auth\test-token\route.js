(()=>{var e={};e.id=7767,e.ids=[7767],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34522:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>l,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>c});var s={};r.r(s),r.d(s,{POST:()=>u});var o=r(96559),a=r(48088),n=r(37719),i=r(32190);async function u(e){try{let t=e.headers.get("authorization");if(!t||!t.startsWith("Bearer "))return i.NextResponse.json({detail:"Token di autorizzazione mancante"},{status:401});let r="http://localhost:8001";console.log("\uD83D\uDD04 Auth API: Proxying test-token request to backend:",`${r}/api/auth/test-token`);let s=await fetch(`${r}/api/auth/test-token`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:t}});console.log("\uD83D\uDCE1 Auth API: Backend response status:",s.status);let o=await s.json();return console.log("\uD83D\uDCE1 Auth API: Backend response data:",o),i.NextResponse.json(o,{status:s.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("❌ Auth API: Test token error:",e),i.NextResponse.json({detail:"Errore interno del server"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/auth/test-token/route",pathname:"/api/auth/test-token",filename:"route",bundlePath:"app/api/auth/test-token/route"},resolvedPagePath:"C:\\CMS\\webapp-nextjs_1\\src\\app\\api\\auth\\test-token\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:c,serverHooks:l}=p;function h(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:c})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(34522));module.exports=s})();