{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Nai6Gg2iDNwuYA9HGBXTaidaLgp3TuN421CBgdpfMQw=", "__NEXT_PREVIEW_MODE_ID": "1016a8ef4a7dbcbfbc0353a920468648", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "56b6d636d7674a34fb31626f2e3087eecd983fae8ebe9643394f8439c9bd216f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "192fdf306cf008877b50c3cd4258ea52554f063d4f54412321356659dd7bc9d2"}}}, "sortedMiddleware": ["/"], "functions": {}}