{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Nai6Gg2iDNwuYA9HGBXTaidaLgp3TuN421CBgdpfMQw=", "__NEXT_PREVIEW_MODE_ID": "c9f9888399ea9c0b5b5c6692ecf31867", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b99331a5f14718a0939cbadb3e1645ca335983c5099cc9761db352c294091528", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9346ed3450db0c67356ba455de1ad8656b2a92d7419f4732c708b435125f4946"}}}, "sortedMiddleware": ["/"], "functions": {}}