{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "cYSsmFYrlpuVHFkFQexBs5v1kdv/7ugk/03t8Z8E4Hs=", "__NEXT_PREVIEW_MODE_ID": "2ce16009f8faed0d14199daba2305580", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "55524b325ee6832ada5a88f183da6184d5495bc7a94e5b7e545f40bb943f8d21", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "07d99c91d14bc9cfb33c3238d89ee89e5b904c2da876c8ad2519913b869250d9"}}}, "sortedMiddleware": ["/"], "functions": {}}