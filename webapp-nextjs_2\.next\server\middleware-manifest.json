{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "cYSsmFYrlpuVHFkFQexBs5v1kdv/7ugk/03t8Z8E4Hs=", "__NEXT_PREVIEW_MODE_ID": "dda9ca8e4ad610e95c042938ab755f32", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d4c38a5f95b86ea6e98088868dea683d9685815254095b17c06965bbcf393454", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ad41256d08b1e01e9600f59623ed8ffcd266ea7d8cacd8c1ee8a89ef4bc637b6"}}}, "sortedMiddleware": ["/"], "functions": {}}