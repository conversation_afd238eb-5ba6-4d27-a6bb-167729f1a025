"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8034],{37328:(e,t,r)=>{function n(e,t,r){if(!t.has(e))throw TypeError("attempted to "+r+" private field on non-instance");return t.get(e)}function l(e,t){var r=n(e,t,"get");return r.get?r.get.call(e):r.value}function o(e,t,r){var l=n(e,t,"set");if(l.set)l.set.call(e,r);else{if(!l.writable)throw TypeError("attempted to set read only private field");l.value=r}return r}r.d(t,{N:()=>p});var a,i=r(12115),u=r(46081),c=r(6101),f=r(99708),s=r(95155);function p(e){let t=e+"CollectionProvider",[r,n]=(0,u.A)(t),[l,o]=r(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:r}=e,n=i.useRef(null),o=i.useRef(new Map).current;return(0,s.jsx)(l,{scope:t,itemMap:o,collectionRef:n,children:r})};a.displayName=t;let p=e+"CollectionSlot",d=(0,f.TL)(p),m=i.forwardRef((e,t)=>{let{scope:r,children:n}=e,l=o(p,r),a=(0,c.s)(t,l.collectionRef);return(0,s.jsx)(d,{ref:a,children:n})});m.displayName=p;let v=e+"CollectionItemSlot",y="data-radix-collection-item",w=(0,f.TL)(v),C=i.forwardRef((e,t)=>{let{scope:r,children:n,...l}=e,a=i.useRef(null),u=(0,c.s)(t,a),f=o(v,r);return i.useEffect(()=>(f.itemMap.set(a,{ref:a,...l}),()=>void f.itemMap.delete(a))),(0,s.jsx)(w,{...{[y]:""},ref:u,children:n})});return C.displayName=v,[{Provider:a,Slot:m,ItemSlot:C},function(t){let r=o(e+"CollectionConsumer",t);return i.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(y,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}var d=new WeakMap;function m(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=v(t),l=n>=0?n:r+n;return l<0||l>=r?-1:l}(e,t);return -1===r?void 0:e[r]}function v(e){return e!=e||0===e?0:Math.trunc(e)}a=new WeakMap},94315:(e,t,r)=>{r.d(t,{jH:()=>o});var n=r(12115);r(95155);var l=n.createContext(void 0);function o(e){let t=n.useContext(l);return e||t||"ltr"}}}]);