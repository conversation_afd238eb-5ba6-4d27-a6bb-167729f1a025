(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3986],{34428:(e,a,i)=>{Promise.resolve().then(i.bind(i,64388))},47262:(e,a,i)=>{"use strict";i.d(a,{S:()=>n});var t=i(95155);i(12115);var s=i(76981),l=i(5196),r=i(59434);function n(e){let{className:a,...i}=e;return(0,t.jsx)(s.bL,{"data-slot":"checkbox",className:(0,r.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...i,children:(0,t.jsx)(s.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,t.jsx)(l.A,{className:"size-3.5"})})})}},54165:(e,a,i)=>{"use strict";i.d(a,{Cf:()=>m,Es:()=>u,L3:()=>p,c7:()=>x,lG:()=>n,rr:()=>h,zM:()=>o});var t=i(95155);i(12115);var s=i(15452),l=i(54416),r=i(59434);function n(e){let{...a}=e;return(0,t.jsx)(s.bL,{"data-slot":"dialog",...a})}function o(e){let{...a}=e;return(0,t.jsx)(s.l9,{"data-slot":"dialog-trigger",...a})}function c(e){let{...a}=e;return(0,t.jsx)(s.ZL,{"data-slot":"dialog-portal",...a})}function d(e){let{className:a,...i}=e;return(0,t.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,r.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...i})}function m(e){let{className:a,children:i,showCloseButton:n=!0,...o}=e;return(0,t.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,t.jsx)(d,{}),(0,t.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,r.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...o,children:[i,n&&(0,t.jsxs)(s.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(l.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x(e){let{className:a,...i}=e;return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,r.cn)("flex flex-col gap-2 text-center sm:text-left",a),...i})}function u(e){let{className:a,...i}=e;return(0,t.jsx)("div",{"data-slot":"dialog-footer",className:(0,r.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...i})}function p(e){let{className:a,...i}=e;return(0,t.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,r.cn)("text-lg leading-none font-semibold",a),...i})}function h(e){let{className:a,...i}=e;return(0,t.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,r.cn)("text-muted-foreground text-sm",a),...i})}},55365:(e,a,i)=>{"use strict";i.d(a,{Fc:()=>o,TN:()=>c});var t=i(95155),s=i(12115),l=i(74466),r=i(59434);let n=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=s.forwardRef((e,a)=>{let{className:i,variant:s,...l}=e;return(0,t.jsx)("div",{ref:a,role:"alert",className:(0,r.cn)(n({variant:s}),i),...l})});o.displayName="Alert",s.forwardRef((e,a)=>{let{className:i,...s}=e;return(0,t.jsx)("h5",{ref:a,className:(0,r.cn)("mb-1 font-medium leading-none tracking-tight",i),...s})}).displayName="AlertTitle";let c=s.forwardRef((e,a)=>{let{className:i,...s}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.cn)("text-sm [&_p]:leading-relaxed",i),...s})});c.displayName="AlertDescription"},64388:(e,a,i)=>{"use strict";i.r(a),i.d(a,{default:()=>eh});var t=i(95155),s=i(12115),l=i(35695),r=i(66695),n=i(30285),o=i(55365),c=i(40283),d=i(25731),m=i(26126),x=i(47262);i(63743);var u=i(85127),p=i(62523),h=i(59409),b=i(20547),g=i(59434);let v=b.bL,f=b.l9,j=s.forwardRef((e,a)=>{let{className:i,align:s="center",sideOffset:l=4,...r}=e;return(0,t.jsx)(b.ZL,{children:(0,t.jsx)(b.UC,{ref:a,align:s,sideOffset:l,className:(0,g.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",i),...r})})});j.displayName=b.UC.displayName;var N=i(21492),y=i(39881),_=i(58832),C=i(54416),w=i(66932),A=i(52278),z=i(42355),S=i(13052),E=i(12767);function k(e){let{data:a=[],columns:i=[],loading:l=!1,emptyMessage:o="Nessun dato disponibile",onFilteredDataChange:c,renderRow:d,className:x,pagination:p=!0,defaultRowsPerPage:b=25}=e,[k,O]=(0,s.useState)({key:null,direction:null}),[T,F]=(0,s.useState)({}),[D,L]=(0,s.useState)({}),[B,R]=(0,s.useState)(0),[M,P]=(0,s.useState)(b),U=e=>[...new Set(a.map(a=>a[e]).filter(Boolean))].sort(),V=(0,s.useMemo)(()=>{let e=[...a];return Object.entries(T).forEach(a=>{let[i,t]=a;!t.value||Array.isArray(t.value)&&0===t.value.length||"string"==typeof t.value&&""===t.value.trim()||(e=e.filter(e=>{let a=e[i];if("select"===t.type)return(Array.isArray(t.value)?t.value:[t.value]).includes(a);if("text"===t.type){let e=t.value.toLowerCase(),i=String(a||"").toLowerCase();return"equals"===t.operator?i===e:i.includes(e)}if("number"===t.type){let e=parseFloat(a),i=parseFloat(t.value);if(isNaN(e)||isNaN(i))return!1;switch(t.operator){case"equals":default:return e===i;case"gt":return e>i;case"lt":return e<i;case"gte":return e>=i;case"lte":return e<=i}}return!0}))}),k.key&&k.direction&&e.sort((e,a)=>{let i=e[k.key],t=a[k.key];if(null==i&&null==t)return 0;if(null==i)return"asc"===k.direction?-1:1;if(null==t)return"asc"===k.direction?1:-1;let s=parseFloat(i),l=parseFloat(t),r=!isNaN(s)&&!isNaN(l),n=0;return n=r?s-l:String(i).localeCompare(String(t)),"asc"===k.direction?n:-n}),e},[a,T,k]),$=(0,s.useMemo)(()=>{if(!p)return V;let e=B*M,a=e+M;return V.slice(e,a)},[V,B,M,p]);(0,s.useEffect)(()=>{R(0)},[T]);let J=Math.ceil(V.length/M),G=B*M+1,q=Math.min((B+1)*M,V.length);(0,s.useEffect)(()=>{c&&c(V)},[V,c]);let Z=e=>{let a=i.find(a=>a.field===e);null!=a&&a.disableSort||O(a=>{if(a.key===e){if("asc"===a.direction)return{key:e,direction:"desc"};if("desc"===a.direction)return{key:null,direction:null}}return{key:e,direction:"asc"}})},W=(e,a)=>{F(i=>({...i,[e]:{...i[e],...a}}))},H=e=>{F(a=>{let i={...a};return delete i[e],i})},Y=e=>k.key!==e?(0,t.jsx)(N.A,{className:"h-3 w-3"}):"asc"===k.direction?(0,t.jsx)(y.A,{className:"h-3 w-3"}):"desc"===k.direction?(0,t.jsx)(_.A,{className:"h-3 w-3"}):(0,t.jsx)(N.A,{className:"h-3 w-3"}),Q=Object.keys(T).length>0;return l?(0,t.jsx)(r.Zp,{className:x,children:(0,t.jsx)(r.Wu,{className:"p-6",children:(0,t.jsx)("div",{className:"text-center",children:"Caricamento..."})})}):(0,t.jsxs)("div",{className:x,children:[Q&&(0,t.jsxs)("div",{className:"mb-4 flex flex-wrap gap-2 items-center",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Filtri attivi:"}),Object.entries(T).map(e=>{let[a,s]=e,l=i.find(e=>e.field===a);if(!l)return null;let r=Array.isArray(s.value)?s.value.join(", "):String(s.value);return(0,t.jsxs)(m.E,{variant:"secondary",className:"gap-1",children:[l.headerName,": ",r,(0,t.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-auto p-0 hover:bg-transparent",onClick:()=>H(a),children:(0,t.jsx)(C.A,{className:"h-3 w-3"})})]},a)}),(0,t.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>{F({})},className:"h-6 px-2 text-xs",children:"Pulisci tutti"})]}),(0,t.jsx)(r.Zp,{children:(0,t.jsx)(r.Wu,{className:"p-0",children:(0,t.jsxs)(u.XI,{children:[(0,t.jsx)(u.A0,{children:(0,t.jsx)(u.Hj,{className:"bg-mariner-50 hover:bg-mariner-50",children:i.map(e=>(0,t.jsx)(u.nd,{className:(0,g.cn)("font-semibold text-mariner-900 border-b border-mariner-200","center"===e.align&&"text-center","right"===e.align&&"text-right"),style:{width:e.width,...e.headerStyle},children:e.renderHeader?e.renderHeader():(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,t.jsx)("span",{className:"truncate",children:e.headerName}),(0,t.jsxs)("div",{className:"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:[!e.disableSort&&(0,t.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-4 w-4 p-0 hover:bg-mariner-100",onClick:()=>Z(e.field),children:Y(e.field)}),!e.disableFilter&&(0,t.jsxs)(v,{open:D[e.field],onOpenChange:a=>L(i=>({...i,[e.field]:a})),children:[(0,t.jsx)(f,{asChild:!0,children:(0,t.jsx)(n.$,{variant:"ghost",size:"sm",className:(0,g.cn)("h-4 w-4 p-0 hover:bg-mariner-100",T[e.field]&&"text-mariner-600 opacity-100"),children:(0,t.jsx)(w.A,{className:"h-2.5 w-2.5"})})}),(0,t.jsx)(j,{className:"w-64",align:"start",children:(0,t.jsx)(I,{column:e,data:a,currentFilter:T[e.field],onFilterChange:a=>W(e.field,a),onClearFilter:()=>H(e.field),getUniqueValues:()=>U(e.field)})})]})]})]}),T[e.field]&&(0,t.jsx)("div",{className:"absolute -top-1 -right-1 h-2 w-2 bg-mariner-600 rounded-full"})]})},e.field))})}),(0,t.jsx)(u.BF,{children:$.length>0?$.map((e,a)=>d?d(e,B*M+a):(0,t.jsx)(u.Hj,{className:"hover:bg-mariner-50 border-b border-mariner-100",children:i.map(a=>(0,t.jsx)(u.nA,{className:(0,g.cn)("py-2 px-4","center"===a.align&&"text-center","right"===a.align&&"text-right"),style:a.cellStyle,children:a.renderCell?a.renderCell(e):e[a.field]},a.field))},a)):(0,t.jsx)(u.Hj,{children:(0,t.jsx)(u.nA,{colSpan:i.length,className:"text-center py-8 text-muted-foreground",children:o})})})]})})}),p&&V.length>0&&(0,t.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Righe per pagina:"}),(0,t.jsxs)(h.l6,{value:M.toString(),onValueChange:e=>{P(Number(e)),R(0)},children:[(0,t.jsx)(h.bq,{className:"w-20",children:(0,t.jsx)(h.yv,{})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"10",children:"10"}),(0,t.jsx)(h.eb,{value:"25",children:"25"}),(0,t.jsx)(h.eb,{value:"50",children:"50"}),(0,t.jsx)(h.eb,{value:"100",children:"100"}),(0,t.jsx)(h.eb,{value:V.length.toString(),children:"Tutto"})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:V.length>0?"".concat(G,"-").concat(q," di ").concat(V.length):"0 di 0"}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>R(0),disabled:0===B,className:"h-8 w-8 p-0",children:(0,t.jsx)(A.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>R(e=>Math.max(0,e-1)),disabled:0===B,className:"h-8 w-8 p-0",children:(0,t.jsx)(z.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>R(e=>Math.min(J-1,e+1)),disabled:B>=J-1,className:"h-8 w-8 p-0",children:(0,t.jsx)(S.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>R(J-1),disabled:B>=J-1,className:"h-8 w-8 p-0",children:(0,t.jsx)(E.A,{className:"h-4 w-4"})})]})]})]})]})}function I(e){let{column:a,currentFilter:i,onFilterChange:l,onClearFilter:r,getUniqueValues:o}=e,[c,d]=(0,s.useState)((null==i?void 0:i.value)||""),[m,u]=(0,s.useState)((null==i?void 0:i.operator)||"contains"),b=o(),g="number"!==a.dataType&&b.length<=20,v="number"===a.dataType,f=()=>{g?l({type:"select",value:Array.isArray(c)?c:[c]}):v?l({type:"number",value:c,operator:m}):l({type:"text",value:c,operator:m})};return(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"font-medium text-sm",children:["Filtra ",a.headerName]}),g?(0,t.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:b.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(x.S,{id:"filter-".concat(e),checked:Array.isArray(c)?c.includes(e):c===e,onCheckedChange:a=>{Array.isArray(c)?d(a?[...c,e]:c.filter(a=>a!==e)):d(a?[e]:[])}}),(0,t.jsx)("label",{htmlFor:"filter-".concat(e),className:"text-sm",children:e})]},e))}):(0,t.jsxs)("div",{className:"space-y-2",children:[v&&(0,t.jsxs)(h.l6,{value:m,onValueChange:u,children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"equals",children:"Uguale a"}),(0,t.jsx)(h.eb,{value:"gt",children:"Maggiore di"}),(0,t.jsx)(h.eb,{value:"lt",children:"Minore di"}),(0,t.jsx)(h.eb,{value:"gte",children:"Maggiore o uguale"}),(0,t.jsx)(h.eb,{value:"lte",children:"Minore o uguale"})]})]}),!v&&(0,t.jsxs)(h.l6,{value:m,onValueChange:u,children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"contains",children:"Contiene"}),(0,t.jsx)(h.eb,{value:"equals",children:"Uguale a"})]})]}),(0,t.jsx)(p.p,{placeholder:"Cerca ".concat(a.headerName.toLowerCase(),"..."),value:c,onChange:e=>d(e.target.value),onKeyDown:e=>"Enter"===e.key&&f()})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(n.$,{size:"sm",onClick:f,children:"Applica"}),(0,t.jsx)(n.$,{size:"sm",variant:"outline",onClick:r,children:"Pulisci"})]})]})}var O=i(47924),T=i(19145),F=i(18979);function D(e){let{cavi:a=[],onFilteredDataChange:i,loading:l=!1,selectionEnabled:o=!1,onSelectionToggle:c}=e,[d,m]=(0,s.useState)(""),[x,u]=(0,s.useState)("contains"),b=e=>e?e.toString().toLowerCase().trim():"",g=e=>{let a=e.match(/^([A-Z]+)(\d+)([A-Z]*)$/);return a?{prefix:a[1],number:a[2],suffix:a[3]||""}:{prefix:"",number:e,suffix:""}},v=(0,s.useCallback)((e,a,i)=>{let t=b(a);if(!t)return!0;let s=b(e.id_cavo),{prefix:l,number:r,suffix:n}=g(e.id_cavo||""),o=b(e.tipologia),c=b(e.formazione||e.sezione),d=b(e.utility),m=b(e.sistema),x=b(e.da||e.ubicazione_partenza),u=b(e.a||e.ubicazione_arrivo),p=b(e.utenza_partenza),h=b(e.utenza_arrivo),v=[s,l,r,n,o,c,d,m,x,u,p,h,b(e.id_bobina),"BOBINA_VUOTA"===e.id_bobina?"bobina vuota":null===e.id_bobina?"":b(e.id_bobina)],f=[{value:e.metri_teorici,name:"metri_teorici"},{value:e.metratura_reale||e.metri_posati,name:"metratura_reale"},{value:parseFloat(c),name:"formazione"}],j=t.match(/^([><=]+)(\d+(?:\.\d+)?)$/);if(j){let e=j[1],a=parseFloat(j[2]);return f.some(i=>{if(null==i.value||isNaN(i.value))return!1;switch(e){case">":return i.value>a;case">=":return i.value>=a;case"<":return i.value<a;case"<=":return i.value<=a;case"=":return i.value===a;default:return!1}})}let N=parseFloat(t);return!!(!isNaN(N)&&f.some(e=>null!=e.value&&!isNaN(e.value)&&e.value===N))||(i?v.some(e=>e===t):v.some(e=>e.includes(t)))},[]),f=(0,s.useCallback)(()=>{if(!d.trim()){null==i||i(a);return}let e=d.split(",").map(e=>e.trim()).filter(e=>e.length>0),t=[];t="equals"===x?1===e.length?a.filter(a=>v(a,e[0],!0)):a.filter(a=>e.every(e=>v(a,e,!0))):a.filter(a=>e.some(e=>v(a,e,!1))),null==i||i(t)},[d,x,a,i,v]);(0,s.useEffect)(()=>{f()},[f]);let j=e=>{m(e)},N=()=>{m(""),u("contains")};return(0,t.jsx)(r.Zp,{className:"mb-1",children:(0,t.jsxs)(r.Wu,{className:"p-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsxs)("div",{className:"flex-1 relative",children:[(0,t.jsx)(O.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(p.p,{placeholder:"Ricerca intelligente cavi...",value:d,onChange:e=>j(e.target.value),disabled:l,className:"pl-10 pr-10 h-8"}),d&&(0,t.jsx)(n.$,{variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-5 w-5 p-0",onClick:N,children:(0,t.jsx)(C.A,{className:"h-2.5 w-2.5"})})]}),(0,t.jsx)("div",{className:"w-32",children:(0,t.jsxs)(h.l6,{value:x,onValueChange:e=>u(e),children:[(0,t.jsx)(h.bq,{className:"h-8",children:(0,t.jsx)(h.yv,{})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"contains",children:"Contiene"}),(0,t.jsx)(h.eb,{value:"equals",children:"Uguale a"})]})]})}),d&&(0,t.jsx)(n.$,{variant:"outline",size:"sm",onClick:N,disabled:l,className:"transition-all duration-200 hover:scale-105",children:"Pulisci"}),c&&(0,t.jsxs)(n.$,{variant:o?"default":"outline",size:"sm",onClick:c,className:"flex items-center gap-2 transition-all duration-200 hover:scale-105",children:[o?(0,t.jsx)(T.A,{className:"h-4 w-4"}):(0,t.jsx)(F.A,{className:"h-4 w-4"}),o?"Disabilita Selezione":"Abilita Selezione"]})]}),d&&(0,t.jsx)("div",{className:"mt-0.5 text-xs text-muted-foreground",children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDCA1"}),(0,t.jsx)("span",{children:"• Virgole per multipli"}),(0,t.jsx)("span",{children:"• >100, <=50 per numeri"})]})})]})})}function L(e){let{text:a,maxLength:i=20,className:l=""}=e,[r,n]=(0,s.useState)(!1),[o,c]=(0,s.useState)({x:0,y:0});if(!a)return(0,t.jsx)("span",{className:"text-gray-400",children:"-"});let d=a.length>i,m=d?"".concat(a.substring(0,i),"..."):a;return d?(0,t.jsxs)("div",{className:"relative inline-block",children:[(0,t.jsx)("span",{className:"cursor-help ".concat(l),style:{textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden",maxWidth:"100%",display:"inline-block"},onMouseEnter:e=>{c({x:e.clientX,y:e.clientY}),n(!0)},onMouseMove:e=>{c({x:e.clientX,y:e.clientY})},onMouseLeave:()=>n(!1),title:a,children:m}),r&&(0,t.jsxs)("div",{className:"fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg pointer-events-none",style:{top:o.y-40,left:o.x-150,maxWidth:"300px",wordWrap:"break-word",whiteSpace:"normal"},children:[a,(0,t.jsx)("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0",style:{borderLeft:"5px solid transparent",borderRight:"5px solid transparent",borderTop:"5px solid #1f2937"}})]})]}):(0,t.jsx)("span",{className:l,children:a})}function B(e){let{cavi:a=[],loading:i=!1,selectionEnabled:l=!1,selectedCavi:r=[],onSelectionChange:o,onStatusAction:c,onContextMenuAction:d}=e,[p,h]=(0,s.useState)(a),[b,g]=(0,s.useState)(a),[v,f]=(0,s.useState)(l);(0,s.useEffect)(()=>{h(a),g(a)},[a]);let j=e=>{o&&o(e?b.map(e=>e.id_cavo):[])},N=(e,a)=>{o&&o(a?[...r,e]:r.filter(a=>a!==e))},y=async()=>{try{let e=await fetch("/api/cavi/export",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({selectedIds:r,cantiereId:1})});if(e.ok){let a=await e.blob(),i=window.URL.createObjectURL(a),t=document.createElement("a");t.href=i,t.download="cavi_export_".concat(new Date().toISOString().split("T")[0],".csv"),document.body.appendChild(t),t.click(),window.URL.revokeObjectURL(i),document.body.removeChild(t)}else{let a=await e.json();alert("Errore durante l'esportazione: ".concat(a.error))}}catch(e){alert("Errore durante l'esportazione")}},_=async()=>{let e=prompt("Inserisci il nuovo stato (Da installare, In corso, Installato):");if(e)try{let a=await fetch("/api/cavi/bulk-status",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({selectedIds:r,cantiereId:1,newStatus:e})}),i=await a.json();i.success?alert(i.message):alert("Errore: ".concat(i.error))}catch(e){alert("Errore durante il cambio stato")}},C=()=>{alert("Assegnazione comanda per ".concat(r.length," cavi"))},w=async()=>{if(confirm("Sei sicuro di voler eliminare ".concat(r.length," cavi?")))try{let e=await fetch("/api/cavi/bulk-delete",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({selectedIds:r,cantiereId:1})}),a=await e.json();a.success?alert(a.message):alert("Errore: ".concat(a.error))}catch(e){alert("Errore durante l'eliminazione")}},A=(0,s.useMemo)(()=>{let e=[{field:"id_cavo",headerName:"ID",dataType:"text",width:70,align:"left",renderCell:e=>(0,t.jsx)("span",{className:"font-semibold text-mariner-900",children:e.id_cavo})},{field:"sistema",headerName:"Sistema",dataType:"text",width:80,renderCell:e=>(0,t.jsx)(L,{text:e.sistema||"",maxLength:8})},{field:"utility",headerName:"Utility",dataType:"text",width:80,renderCell:e=>(0,t.jsx)(L,{text:e.utility||"",maxLength:8})},{field:"tipologia",headerName:"Tipologia",dataType:"text",width:100,renderCell:e=>(0,t.jsx)(L,{text:e.tipologia||"",maxLength:12})},{field:"formazione",headerName:"Form.",dataType:"text",align:"left",width:60,renderCell:e=>e.formazione||e.sezione},{field:"metri_teorici",headerName:"M.Teor.",dataType:"number",align:"left",width:70,renderCell:e=>e.metri_teorici?e.metri_teorici.toFixed(1):"0"},{field:"metri_posati",headerName:"M.Reali",dataType:"number",align:"left",width:70,renderCell:e=>{let a=e.metri_posati||e.metratura_reale||0;return a?a.toFixed(1):"0"}},{field:"ubicazione_partenza",headerName:"Da",dataType:"text",width:140,renderCell:e=>(0,t.jsx)(L,{text:e.da||e.ubicazione_partenza||"",maxLength:18})},{field:"ubicazione_arrivo",headerName:"A",dataType:"text",width:140,renderCell:e=>(0,t.jsx)(L,{text:e.a||e.ubicazione_arrivo||"",maxLength:18})},{field:"id_bobina",headerName:"Bobina",dataType:"text",width:80,align:"center",renderCell:e=>{let a=e.id_bobina;if(a,!a||"N/A"===a)return(0,t.jsx)("span",{className:"text-gray-400",children:"-"});if("BOBINA_VUOTA"===a)return(0,t.jsx)(m.E,{variant:"outline",className:"text-xs px-2 py-0.5 text-orange-600 border-orange-300 bg-orange-50",children:"Vuota"});let i=a.match(/_B(.+)$/);return i||(i=a.match(/_b(.+)$/))||(i=a.match(/c\d+_[bB](\d+)$/))||(i=a.match(/(\d+)$/))?(0,t.jsx)("span",{className:"font-medium",children:i[1]}):(0,t.jsx)("span",{className:"font-medium text-xs",children:a})}},{field:"stato_installazione",headerName:"Stato",dataType:"text",align:"left",width:120,disableFilter:!0,disableSort:!0,renderCell:e=>z(e)},{field:"collegamenti",headerName:"Collegamenti",dataType:"text",align:"left",width:180,disableFilter:!0,disableSort:!0,renderCell:e=>S(e)},{field:"certificato",headerName:"Certificato",dataType:"text",align:"left",width:130,disableFilter:!0,disableSort:!0,renderCell:e=>E(e)}];return v&&e.unshift({field:"selection",headerName:"",disableFilter:!0,disableSort:!0,width:50,align:"left",renderHeader:()=>(0,t.jsx)(x.S,{checked:r.length===b.length&&b.length>0,onCheckedChange:j}),renderCell:e=>(0,t.jsx)(x.S,{checked:r.includes(e.id_cavo),onCheckedChange:a=>N(e.id_cavo,a),onClick:e=>e.stopPropagation()})}),e},[v,r,b,j,N]),z=e=>{let a=e.metri_posati||e.metratura_reale||0,i=e.comanda_posa,s=e.comanda_partenza,l=e.comanda_arrivo,r=e.comanda_certificazione,n=i||s||l||r;if(n&&"In corso"===e.stato_installazione)return(0,t.jsx)(m.E,{className:"bg-blue-600 text-white cursor-pointer hover:bg-blue-700 hover:scale-105 transition-all duration-200 hover:shadow-md px-3 py-1 font-semibold",onClick:a=>{a.stopPropagation(),null==c||c(e,"view_command",n)},children:n});let o=e.stato_installazione||"Da installare";return"Installato"===o||a>0?(0,t.jsx)(m.E,{className:"bg-green-100 text-green-800 cursor-pointer hover:bg-green-200 hover:text-green-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-green-300",onClick:a=>{a.stopPropagation(),null==c||c(e,"modify_reel")},children:"Installato"}):"In corso"===o?(0,t.jsx)(m.E,{className:"bg-yellow-100 text-yellow-800 cursor-pointer hover:bg-yellow-200 hover:text-yellow-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-yellow-300",onClick:a=>{a.stopPropagation(),null==c||c(e,"insert_meters")},children:"In corso"}):(0,t.jsx)(m.E,{variant:"outline",className:"text-gray-700 cursor-pointer hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium",onClick:a=>{a.stopPropagation(),null==c||c(e,"insert_meters")},children:"Da installare"})},S=e=>{let a,i,s,l=e.metri_posati>0||e.metratura_reale>0,r=e.collegamento||e.collegamenti||0;if(!l)return(0,t.jsx)(m.E,{variant:"outline",className:"text-gray-400 cursor-not-allowed bg-gray-50 border-gray-200 px-3 py-1",children:"✕ Non disponibile"});switch(r){case 0:a="⚪⚪ Collega",i="connect_cable",s="bg-gray-100 text-gray-700 cursor-pointer hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-gray-300";break;case 1:a="\uD83D\uDFE2⚪ Completa",i="connect_arrival",s="bg-yellow-100 text-yellow-800 cursor-pointer hover:bg-yellow-200 hover:text-yellow-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-yellow-300";break;case 2:a="⚪\uD83D\uDFE2 Completa",i="connect_departure",s="bg-yellow-100 text-yellow-800 cursor-pointer hover:bg-yellow-200 hover:text-yellow-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-yellow-300";break;case 3:a="\uD83D\uDFE2\uD83D\uDFE2 Scollega",i="disconnect_cable",s="bg-green-100 text-green-800 cursor-pointer hover:bg-green-200 hover:text-green-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-green-300";break;default:a="Gestisci",i="manage_connections",s="bg-blue-100 text-blue-800 cursor-pointer hover:bg-blue-200 hover:text-blue-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-blue-300"}return(0,t.jsx)(m.E,{className:s,onClick:a=>{a.stopPropagation(),null==c||c(e,i)},children:a})},E=e=>{let a=e.metri_posati>0||e.metratura_reale>0,i=!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato;return a?i?(0,t.jsx)(m.E,{className:"bg-green-600 text-white cursor-pointer hover:bg-green-700 transition-all duration-200 hover:scale-105 px-3 py-1 font-semibold",onClick:a=>{a.stopPropagation(),null==c||c(e,"generate_pdf")},children:"✓ Genera PDF"}):(0,t.jsx)(m.E,{variant:"outline",className:"text-purple-700 cursor-pointer hover:bg-purple-50 hover:text-purple-800 hover:border-purple-300 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border-purple-200",onClick:a=>{a.stopPropagation(),null==c||c(e,"create_certificate")},children:"\uD83D\uDD50 Certifica"}):(0,t.jsx)(m.E,{variant:"outline",className:"text-gray-400 cursor-not-allowed bg-gray-50 border-gray-200 px-3 py-1",children:"✕ Non disponibile"})};return(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(D,{cavi:a,onFilteredDataChange:e=>{h(e)},loading:i,selectionEnabled:v,onSelectionToggle:()=>{f(!v)}}),(0,t.jsx)(k,{data:p,columns:A,loading:i,emptyMessage:"Nessun cavo disponibile",onFilteredDataChange:e=>{g(e)},renderRow:(e,a)=>{let i=r.includes(e.id_cavo);return(0,t.jsx)(u.Hj,{className:"\n          ".concat(i?"bg-blue-50 border-blue-200":"bg-white","\n          hover:bg-blue-50 hover:border-blue-200 hover:shadow-sm\n          cursor-pointer border-b border-gray-200\n          transition-all duration-200 ease-in-out\n          ").concat(i?"ring-1 ring-blue-300":"","\n        "),onClick:()=>v&&N(e.id_cavo,!i),onContextMenu:a=>{a.preventDefault(),null==d||d(e,"context_menu")},children:A.map(a=>(0,t.jsx)(u.nA,{className:"\n              py-2 px-2 text-sm text-left\n              ".concat(i?"text-blue-900":"text-gray-900","\n              transition-colors duration-200\n            "),style:{width:a.width,...a.cellStyle},onClick:e=>{["stato_installazione","collegamenti","certificato"].includes(a.field)&&e.stopPropagation()},children:a.renderCell?a.renderCell(e):e[a.field]||(0,t.jsx)("span",{className:"text-gray-400",children:"-"})},a.field))},e.id_cavo)}}),v&&r.length>0&&(0,t.jsx)("div",{className:"sticky bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-10",children:(0,t.jsxs)("div",{className:"flex items-center justify-between p-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)(m.E,{variant:"secondary",className:"bg-mariner-100 text-mariner-800",children:[r.length," cavi selezionati"]}),(0,t.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>j(!1),className:"text-xs",children:"Deseleziona tutto"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>y(),className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDCCA"}),(0,t.jsx)("span",{children:"Esporta"})]}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>_(),className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDD04"}),(0,t.jsx)("span",{children:"Cambia Stato"})]}),(0,t.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>C(),className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDCCB"}),(0,t.jsx)("span",{children:"Assegna Comanda"})]}),(0,t.jsxs)(n.$,{variant:"destructive",size:"sm",onClick:()=>w(),className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDDD1️"}),(0,t.jsx)("span",{children:"Elimina"})]})]})]})})]})}var R=i(72713),M=i(3493),P=i(40646),U=i(14186),V=i(1243),$=i(71539),J=i(37108);function G(e){let{cavi:a,filteredCavi:i,className:l,revisioneCorrente:n}=e,o=(0,s.useMemo)(()=>{let e=a.length,t=i.length,s=i.filter(e=>"Installato"===e.stato_installazione||e.metri_posati&&e.metri_posati>0||e.metratura_reale&&e.metratura_reale>0).length,l=i.filter(e=>"In corso"===e.stato_installazione).length,r=i.filter(e=>3===(e.collegamento||e.collegamenti||0)).length,n=i.filter(e=>{let a=e.collegamento||e.collegamenti||0;return 1===a||2===a}).length,o=i.filter(e=>0===(e.collegamento||e.collegamenti||0)&&(e.metri_posati>0||e.metratura_reale>0)).length,c=i.filter(e=>!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato).length,d=i.reduce((e,a)=>e+(a.metri_teorici||0),0),m=i.reduce((e,a)=>e+(a.metri_posati||a.metratura_reale||0),0),x=0===t?0:Math.round(100*(((s-r)*2+(r-c)*3.5+4*c)/(4*t)*100))/100;return{totalCavi:e,filteredCount:t,installati:s,inCorso:l,daInstallare:t-s-l,collegati:r,parzialmenteCollegati:n,nonCollegati:o,certificati:c,metriTotali:d,metriInstallati:m,percentualeInstallazione:x}},[a,i]);return(0,t.jsx)(r.Zp,{className:l,children:(0,t.jsxs)(r.Wu,{className:"p-1.5",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1.5",children:[(0,t.jsx)(R.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,t.jsx)("span",{className:"text-xs font-semibold text-mariner-900",children:"Statistiche Cavi"})]}),n&&(0,t.jsxs)(m.E,{variant:"outline",className:"text-xs font-medium py-0 px-1.5 h-5",children:["Rev. ",n]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-mariner-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(M.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-mariner-900 text-sm",children:o.filteredCount}),(0,t.jsxs)("div",{className:"text-xs text-mariner-600",children:["di ",o.totalCavi," cavi"]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-green-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(P.A,{className:"h-3.5 w-3.5 text-green-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-green-700 text-sm",children:o.installati}),(0,t.jsx)("div",{className:"text-xs text-green-600",children:"installati"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-yellow-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(U.A,{className:"h-3.5 w-3.5 text-yellow-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-yellow-700 text-sm",children:o.inCorso}),(0,t.jsx)("div",{className:"text-xs text-yellow-600",children:"in corso"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-gray-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(V.A,{className:"h-3.5 w-3.5 text-gray-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-gray-700 text-sm",children:o.daInstallare}),(0,t.jsx)("div",{className:"text-xs text-gray-600",children:"da installare"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-blue-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)($.A,{className:"h-3.5 w-3.5 text-blue-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-blue-700 text-sm",children:o.collegati}),(0,t.jsx)("div",{className:"text-xs text-blue-600",children:"collegati"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-purple-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(J.A,{className:"h-3.5 w-3.5 text-purple-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-purple-700 text-sm",children:o.certificati}),(0,t.jsx)("div",{className:"text-xs text-purple-600",children:"certificati"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-indigo-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)("div",{className:"h-3.5 w-3.5 flex items-center justify-center",children:(0,t.jsx)("div",{className:"h-2 w-2 bg-indigo-600 rounded-full"})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"font-bold text-indigo-700 text-sm",children:[o.metriInstallati.toLocaleString(),"m"]}),(0,t.jsxs)("div",{className:"text-xs text-indigo-600",children:["di ",o.metriTotali.toLocaleString(),"m"]})]})]})]}),o.filteredCount>0&&(0,t.jsxs)("div",{className:"mt-2 bg-gray-50 p-2 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex justify-between text-xs font-medium text-gray-700 mb-1",children:[(0,t.jsx)("span",{children:"IAP - Indice Avanzamento Ponderato"}),(0,t.jsxs)("span",{className:"font-bold ".concat(o.percentualeInstallazione>=80?"text-emerald-700":o.percentualeInstallazione>=50?"text-yellow-700":o.percentualeInstallazione>=25?"text-orange-700":"text-amber-700"),children:[o.percentualeInstallazione.toFixed(1),"%"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"h-2 rounded-full transition-all duration-500 ease-in-out ".concat(o.percentualeInstallazione>=80?"bg-gradient-to-r from-emerald-500 to-emerald-600":o.percentualeInstallazione>=50?"bg-gradient-to-r from-yellow-500 to-yellow-600":o.percentualeInstallazione>=25?"bg-gradient-to-r from-orange-500 to-orange-600":"bg-gradient-to-r from-amber-500 to-amber-600"),style:{width:"".concat(Math.min(o.percentualeInstallazione,100),"%")}})}),(0,t.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-0.5",children:[(0,t.jsx)("span",{children:"Pesi: Posa(2.0) + Collegamento(1.5) + Certificazione(0.5)"}),(0,t.jsxs)("span",{children:[o.installati,"I + ",o.collegati,"C + ",o.certificati,"Cert"]})]})]})]})})}var q=i(54165),Z=i(85057),W=i(6740),H=i(51154),Y=i(85339);function Q(e){let{open:a,onClose:i,cavo:l,cantiere:r,onSuccess:x,onError:u}=e,{cantiere:h}=(0,c.A)(),b=r||h,[g,v]=(0,s.useState)("assegna_nuova"),[f,j]=(0,s.useState)(""),[N,y]=(0,s.useState)([]),[_,C]=(0,s.useState)(!1),[w,A]=(0,s.useState)(!1),[z,S]=(0,s.useState)(""),[E,k]=(0,s.useState)(""),[I,T]=(0,s.useState)("compatibili");(0,s.useEffect)(()=>{a&&(v("assegna_nuova"),j(""),k(""),T("compatibili"),S(""),(null==b?void 0:b.id_cantiere)&&F())},[a,null==b?void 0:b.id_cantiere]);let F=async()=>{if(!(null==b?void 0:b.id_cantiere))return void S("Cantiere non disponibile");try{C(!0),S(""),console.log("\uD83D\uDD04 ModificaBobinaDialog: Caricamento bobine per cantiere:",b.id_cantiere);let e=await d.Fw.getBobine(b.id_cantiere),a=[];if(Array.isArray(e))a=e;else if(e&&Array.isArray(e.data))a=e.data;else if(e&&e.bobine&&Array.isArray(e.bobine))a=e.bobine;else throw Error("Formato risposta API non valido");let i=a.filter(e=>"Terminata"!==e.stato_bobina&&"Over"!==e.stato_bobina&&e.metri_residui>0);y(i),console.log("✅ ModificaBobinaDialog: Bobine caricate:",i.length),console.log("\uD83D\uDCCB ModificaBobinaDialog: Dettaglio bobine:",i.map(e=>({id:e.id_bobina,tipologia:e.tipologia,formazione:e.formazione,metri_residui:e.metri_residui})))}catch(e){console.error("❌ ModificaBobinaDialog: Errore caricamento bobine:",e),S("Errore nel caricamento delle bobine: ".concat(e.message)),y([])}finally{C(!1)}},D=e=>{if(!e||"BOBINA_VUOTA"===e)return"VUOTA";if(e&&e.includes("_B"))return e.split("_B")[1];let a=N.find(a=>a.id_bobina===e);return a&&a.numero_bobina||e},L=(()=>{if(!l)return[];let e=N.filter(e=>{let a=e.tipologia===l.tipologia&&e.formazione===l.sezione,i=""===E||e.id_bobina.toLowerCase().includes(E.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(E.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(E.toLowerCase());return a&&i&&e.metri_residui>0});return console.log("\uD83D\uDD0D ModificaBobinaDialog: Filtro compatibili:",{cavoTipologia:l.tipologia,cavoSezione:l.sezione,totaleBobine:N.length,bobineCompatibili:e.length,searchText:E}),e})(),B=l?N.filter(e=>{let a=e.tipologia!==l.tipologia||e.formazione!==l.sezione,i=""===E||e.id_bobina.toLowerCase().includes(E.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(E.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(E.toLowerCase());return a&&i&&e.metri_residui>0}):[];(0,s.useEffect)(()=>{a&&l&&(F(),v("assegna_nuova"),j(""),k(""),S(""))},[a,l,b]);let R=()=>{v("assegna_nuova"),j(""),k(""),S(""),i()},M=async()=>{if(console.log("\uD83D\uDD04 ModificaBobinaDialog: Salvataggio:",{selectedOption:g,selectedBobina:f,cavoId:null==l?void 0:l.id_cavo,cantiereId:null==b?void 0:b.id_cantiere}),l)try{if(A(!0),S(""),"assegna_nuova"===g){if(!f)return void u("Selezionare una bobina");let e=await d.At.updateMetriPosati(null==b?void 0:b.id_cantiere,l.id_cavo,l.metratura_reale||0,f,!0);console.log("✅ ModificaBobinaDialog: Bobina aggiornata:",e),x("Bobina aggiornata con successo per il cavo ".concat(l.id_cavo)),R()}else if("rimuovi_bobina"===g){let e=await d.At.updateMetriPosati(null==b?void 0:b.id_cantiere,l.id_cavo,l.metratura_reale||0,"BOBINA_VUOTA",!0);console.log("✅ ModificaBobinaDialog: Bobina rimossa:",e),x("Bobina rimossa con successo dal cavo ".concat(l.id_cavo)),R()}}catch(i){var e,a;console.error("❌ ModificaBobinaDialog: Errore salvataggio:",i),u((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante la modifica della bobina")}finally{A(!1)}};return l?(0,t.jsx)(t.Fragment,{children:(0,t.jsx)(q.lG,{open:a,onOpenChange:R,children:(0,t.jsxs)(q.Cf,{className:"max-w-4xl max-h-[90vh] flex flex-col",children:[(0,t.jsx)(q.c7,{children:(0,t.jsxs)(q.L3,{children:["Modifica Bobina Cavo ",l.id_cavo]})}),(0,t.jsxs)("div",{className:"flex-1 overflow-hidden space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(J.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsx)("h3",{className:"font-medium text-gray-900",children:"Cavo Selezionato"})]}),(0,t.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-200",children:(0,t.jsxs)("div",{className:"text-sm font-medium text-blue-800",children:["Informazioni Cavo / Tipologia: ",l.tipologia||"N/A"," / Da: ",l.ubicazione_partenza||"N/A"," / Formazione: ",l.sezione||"N/A"," / A: ",l.ubicazione_arrivo||"N/A"," / Metri Posati: ",l.metratura_reale||0," m"]})})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Opzioni di modifica"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,t.jsx)("input",{type:"radio",name:"operazione",value:"assegna_nuova",checked:"assegna_nuova"===g,onChange:e=>v(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,t.jsx)("span",{className:"text-sm",children:"Cambia bobina"})]}),(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,t.jsx)("input",{type:"radio",name:"operazione",value:"rimuovi_bobina",checked:"rimuovi_bobina"===g,onChange:e=>v(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,t.jsx)("span",{className:"text-sm",children:"Bobina vuota"})]})]})]}),"assegna_nuova"===g&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Seleziona bobina"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(O.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,t.jsx)(p.p,{placeholder:"Cerca bobina per ID, tipologia o numero...",value:E,onChange:e=>k(e.target.value),className:"pl-10"})]}),(0,t.jsxs)("div",{className:"flex space-x-1 border-b",children:[(0,t.jsx)("button",{onClick:()=>T("compatibili"),className:"px-4 py-2 text-sm font-medium border-b-2 transition-colors ".concat("compatibili"===I?"border-green-500 text-green-600 bg-green-50":"border-transparent text-gray-500 hover:text-gray-700"),children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(P.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["Bobine Compatibili (",L.length,")"]})]})}),(0,t.jsx)("button",{onClick:()=>T("incompatibili"),className:"px-4 py-2 text-sm font-medium border-b-2 transition-colors ".concat("incompatibili"===I?"border-amber-500 text-amber-600 bg-amber-50":"border-transparent text-gray-500 hover:text-gray-700"),children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(V.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["Bobine Incompatibili (",B.length,")"]})]})})]}),(0,t.jsx)("div",{className:"max-h-64 overflow-y-auto border rounded-lg",children:_?(0,t.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,t.jsx)(H.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,t.jsx)("span",{children:"Caricamento bobine..."})]}):(0,t.jsx)("div",{className:"divide-y",children:"compatibili"===I?0===L.length?(0,t.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina compatibile trovata"}):L.map(e=>(0,t.jsx)("div",{className:"p-3 cursor-pointer transition-all ".concat(f===e.id_bobina?"bg-green-100 border-l-4 border-green-500":"hover:bg-green-50"),onClick:()=>j(e.id_bobina),children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[f===e.id_bobina&&(0,t.jsx)(P.A,{className:"h-5 w-5 text-green-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium text-sm",children:D(e.id_bobina)}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[e.tipologia," - ",e.formazione]})]})]}),(0,t.jsxs)(m.E,{variant:"outline",className:"bg-green-100 text-green-800 border-green-300",children:[e.metri_residui,"m"]})]})},e.id_bobina)):0===B.length?(0,t.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina incompatibile trovata"}):B.map(e=>(0,t.jsx)("div",{className:"p-3 cursor-pointer transition-all ".concat(f===e.id_bobina?"bg-amber-100 border-l-4 border-amber-500":"hover:bg-amber-50"),onClick:()=>j(e.id_bobina),children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[f===e.id_bobina&&(0,t.jsx)(P.A,{className:"h-5 w-5 text-amber-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium text-sm",children:D(e.id_bobina)}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[e.tipologia," - ",e.formazione]})]})]}),(0,t.jsxs)(m.E,{variant:"outline",className:"bg-amber-100 text-amber-800 border-amber-300",children:[e.metri_residui,"m"]})]})},e.id_bobina))})})]}),z&&(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(Y.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:z})]})]}),(0,t.jsxs)(q.Es,{children:[(0,t.jsx)(n.$,{variant:"outline",onClick:R,disabled:w,children:"Annulla"}),(0,t.jsxs)(n.$,{onClick:M,disabled:w,children:[w&&(0,t.jsx)(H.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salva"]})]})]})})}):null}function X(e){let{open:a,onClose:i,cavo:l,cantiere:r,onSuccess:x,onError:u}=e,{cantiere:h}=(0,c.A)(),b=r||h,[g,v]=(0,s.useState)({metri_posati:"",id_bobina:""});(0,s.useEffect)(()=>{console.log("\uD83D\uDCCA InserisciMetriDialog: FormData aggiornato:",{hasMetri:!!g.metri_posati,hasBobina:!!g.id_bobina,metri_posati:g.metri_posati,id_bobina:g.id_bobina})},[g]);let[f,j]=(0,s.useState)({}),[N,y]=(0,s.useState)({}),[_,w]=(0,s.useState)(!1),[A,z]=(0,s.useState)([]),[S,E]=(0,s.useState)(!1),[k,I]=(0,s.useState)(""),[T,F]=(0,s.useState)(!1);(0,s.useEffect)(()=>{a&&b&&U()},[a,b]),(0,s.useEffect)(()=>{a&&l&&(v({metri_posati:"",id_bobina:""}),j({}),y({}),I(""))},[a,l]);let D=e=>{if(!e||"BOBINA_VUOTA"===e)return"VUOTA";if(e&&e.includes("_B"))return e.split("_B")[1];let a=A.find(a=>a.id_bobina===e);return a&&a.numero_bobina||e};A.filter(e=>{if(!k)return!0;let a=k.toLowerCase();return e.id_bobina.toLowerCase().includes(a)||e.tipologia.toLowerCase().includes(a)||e.formazione.toLowerCase().includes(a)||D(e.id_bobina).toLowerCase().includes(a)});let L=l?A.filter(e=>{let a=e.tipologia===l.tipologia&&e.sezione===l.sezione,i=""===k||e.id_bobina.toLowerCase().includes(k.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(k.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(k.toLowerCase());return a&&i&&e.metri_residui>0}):[],B=l?A.filter(e=>{let a=e.tipologia!==l.tipologia||e.sezione!==l.sezione,i=""===k||e.id_bobina.toLowerCase().includes(k.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(k.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(k.toLowerCase());return a&&i&&e.metri_residui>0}):[],R=e=>{console.log("\uD83C\uDFAF Bobina selezionata:",{id:e.id_bobina,numero:D(e.id_bobina),tipologia:e.tipologia,formazione:e.formazione,metri_residui:e.metri_residui}),v(a=>({...a,id_bobina:e.id_bobina})),j(e=>({...e,id_bobina:void 0}))};(0,s.useEffect)(()=>{a&&l&&(b&&U(),v({metri_posati:"0",id_bobina:""}),j({}),y({}),I(""))},[a,l,b]),(0,s.useEffect)(()=>{g.metri_posati&&l?M(parseFloat(g.metri_posati)):(j(e=>({...e,metri_posati:void 0})),y(e=>({...e,metri_posati:void 0})))},[g.metri_posati,l]);let M=e=>{if(!l)return;let a={...f},i={...N};delete a.metri_posati,delete i.metri_posati,e>1.1*(l.metri_teorici||0)?i.metri_posati="Attenzione: i metri posati superano del 10% i metri teorici (".concat(l.metri_teorici,"m)"):e>(l.metri_teorici||0)&&(i.metri_posati="Metratura superiore ai metri teorici"),j(a),y(i)},U=async()=>{if(console.log({cavo:!!l,cantiere:!!b,cavoId:null==l?void 0:l.id_cavo,cantiereId:null==b?void 0:b.id_cantiere}),l&&b)try{E(!0);let e=await d.Fw.getBobine(b.id_cantiere),a=[];if(Array.isArray(e))a=e;else if(e&&Array.isArray(e.data))a=e.data;else if(e&&e.bobine&&Array.isArray(e.bobine))a=e.bobine;else throw Error("Formato risposta API non valido");let i=a.filter(e=>"Terminata"!==e.stato_bobina&&"Over"!==e.stato_bobina&&e.metri_residui>0);if(l){console.log({tipologia:l.tipologia,sezione:l.sezione});let e=i.filter(e=>e.tipologia===l.tipologia&&e.sezione===l.sezione),a=i.filter(e=>e.tipologia!==l.tipologia||e.sezione!==l.sezione);e.sort((e,a)=>a.metri_residui-e.metri_residui),a.sort((e,a)=>a.metri_residui-e.metri_residui);let t=[...e,...a];z(t)}else i.sort((e,a)=>a.metri_residui-e.metri_residui),z(i)}catch(t){var e,a,i;console.log({message:t.message,response:t.response,status:null==(e=t.response)?void 0:e.status,data:null==(a=t.response)?void 0:a.data}),(null==(i=t.response)?void 0:i.status)!==404&&u("Errore nel caricamento delle bobine. Puoi comunque usare BOBINA VUOTA."),z([])}finally{E(!1)}},$=async()=>{if(console.log({cavo:null==l?void 0:l.id_cavo,metri_posati:g.metri_posati,id_bobina:g.id_bobina}),!l)return;if(!g.metri_posati||0>parseFloat(g.metri_posati))return void u("Inserire metri posati validi (≥ 0)");if(!g.id_bobina)return void u("Selezionare una bobina o BOBINA VUOTA");let e=parseFloat(g.metri_posati);if("BOBINA_VUOTA"!==g.id_bobina){let e=A.find(e=>e.id_bobina===g.id_bobina);e&&e.metri_residui}try{if(w(!0),!b)throw Error("Cantiere non selezionato");console.log({cantiere:b.id_cantiere,cavo:l.id_cavo,metri:e,bobina:g.id_bobina,isBobinaVuota:"BOBINA_VUOTA"===g.id_bobina}),await d.At.updateMetriPosati(b.id_cantiere,l.id_cavo,e,g.id_bobina,!0),x("Metri posati aggiornati con successo per il cavo ".concat(l.id_cavo,": ").concat(e,"m")),i()}catch(e){var a,t;u((null==(t=e.response)||null==(a=t.data)?void 0:a.detail)||e.message||"Errore durante il salvataggio dei metri posati")}finally{w(!1)}},J=()=>{_||(v({metri_posati:"",id_bobina:""}),j({}),y({}),I(""),i())};return l?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(q.lG,{open:a,onOpenChange:J,children:(0,t.jsxs)(q.Cf,{className:"max-w-7xl h-[90vh] flex flex-col",children:[(0,t.jsxs)(q.c7,{className:"flex-shrink-0",children:[(0,t.jsxs)(q.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(W.A,{className:"h-5 w-5"}),"Inserisci Metri Posati - ",l.id_cavo]}),(0,t.jsx)(q.rr,{children:"Inserisci i metri effettivamente posati per il cavo e seleziona una bobina o usa BOBINA VUOTA"})]}),(0,t.jsxs)("div",{className:"flex-1 overflow-y-auto space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-4",children:[(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-200",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"Informazioni Cavo"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Tipologia:"})," ",l.tipologia||"N/A"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Da:"})," ",l.ubicazione_partenza||"N/A"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Formazione:"})," ",l.sezione||"N/A"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"A:"})," ",l.ubicazione_arrivo||"N/A"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Metri teorici:"})," ",l.metri_teorici||"N/A"," m"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Gi\xe0 posati:"})," ",l.metratura_reale||0," m"]})]})]})}),(0,t.jsx)("div",{className:"lg:col-span-1",children:(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-300 h-full",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"Metri da Installare"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(Z.J,{htmlFor:"metri",className:"text-sm font-medium",children:"Metri Posati"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(p.p,{id:"metri",type:"number",value:g.metri_posati,onChange:e=>v(a=>({...a,metri_posati:e.target.value})),placeholder:"Inserisci metri posati",disabled:_,step:"0.1",min:"0",className:"text-lg font-bold text-center border-2 border-blue-400 focus:border-blue-600",autoFocus:!0}),(0,t.jsx)("span",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-lg font-bold text-blue-600",children:"m"})]}),f.metri_posati&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:f.metri_posati}),N.metri_posati&&(0,t.jsx)("p",{className:"text-sm text-amber-600",children:N.metri_posati})]})]})})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-800 text-lg",children:"Selezione Bobina"}),(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-12 gap-3 items-center",children:[(0,t.jsx)("div",{className:"sm:col-span-5",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(O.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(p.p,{placeholder:"ID, tipologia, formazione...",value:k,onChange:e=>I(e.target.value),className:"pl-10",disabled:_}),k&&(0,t.jsx)(n.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0",onClick:()=>I(""),children:(0,t.jsx)(C.A,{className:"h-4 w-4"})})]})}),(0,t.jsx)("div",{className:"sm:col-span-7",children:(0,t.jsxs)(n.$,{type:"button",variant:"BOBINA_VUOTA"===g.id_bobina?"default":"outline",className:"w-full h-10 font-bold flex items-center justify-center gap-2 ".concat("BOBINA_VUOTA"===g.id_bobina?"bg-green-600 hover:bg-green-700 text-white":"border-blue-400 text-blue-700 hover:bg-blue-50"),onClick:()=>{console.log("\uD83C\uDFAF BOBINA VUOTA selezionata - cavo sar\xe0 posato senza bobina specifica"),v(e=>({...e,id_bobina:"BOBINA_VUOTA"})),j(e=>{let a={...e};return delete a.id_bobina,a})},disabled:_,children:["BOBINA_VUOTA"===g.id_bobina&&(0,t.jsx)(P.A,{className:"h-5 w-5"}),"BOBINA VUOTA"]})})]}),"BOBINA_VUOTA"===g.id_bobina&&(0,t.jsx)("div",{className:"mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-start gap-2",children:[(0,t.jsx)(P.A,{className:"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,t.jsx)("p",{className:"font-medium",children:"Bobina Vuota Selezionata"}),(0,t.jsx)("p",{className:"mt-1",children:'Il cavo sar\xe0 posato senza assegnazione di bobina specifica. Potrai collegarlo a una bobina in seguito tramite la funzione "Modifica Bobina".'})]})]})})]}),S?(0,t.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,t.jsx)(H.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,t.jsx)("span",{children:"Caricamento bobine..."})]}):(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-medium text-green-700 mb-2 flex items-center gap-2",children:[(0,t.jsx)(P.A,{className:"h-4 w-4"}),"Bobine Compatibili (",L.length,")"]}),(0,t.jsx)("div",{className:"max-h-72 overflow-y-auto border rounded-lg",children:0===L.length?(0,t.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina compatibile trovata"}):(0,t.jsx)("div",{className:"divide-y",children:L.map(e=>(0,t.jsx)("div",{className:"p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ".concat(g.id_bobina===e.id_bobina?"bg-green-100 border-green-500 shadow-md":"border-gray-200 hover:bg-green-50 hover:border-green-300"),onClick:()=>R(e),children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[g.id_bobina===e.id_bobina&&(0,t.jsx)(P.A,{className:"h-5 w-5 text-green-600 flex-shrink-0"}),(0,t.jsx)("div",{className:"font-bold text-base min-w-fit",children:D(e.id_bobina)}),(0,t.jsxs)("div",{className:"text-sm text-gray-600 truncate",children:[e.tipologia," - ",e.sezione]})]}),(0,t.jsxs)(m.E,{variant:"outline",className:"bg-green-100 text-green-800 border-green-300 font-medium min-w-fit",children:[e.metri_residui,"m"]})]})},e.id_bobina))})})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-medium text-amber-700 mb-2 flex items-center gap-2",children:[(0,t.jsx)(V.A,{className:"h-4 w-4"}),"Bobine Incompatibili (",B.length,")"]}),(0,t.jsx)("div",{className:"max-h-72 overflow-y-auto border rounded-lg",children:0===B.length?(0,t.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina incompatibile trovata"}):(0,t.jsx)("div",{className:"divide-y",children:B.map(e=>(0,t.jsx)("div",{className:"p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ".concat(g.id_bobina===e.id_bobina?"bg-amber-100 border-amber-500 shadow-md":"border-gray-200 hover:bg-amber-50 hover:border-amber-300"),onClick:()=>R(e),children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[g.id_bobina===e.id_bobina&&(0,t.jsx)(P.A,{className:"h-5 w-5 text-amber-600 flex-shrink-0"}),(0,t.jsx)("div",{className:"font-bold text-base min-w-fit",children:D(e.id_bobina)}),(0,t.jsxs)("div",{className:"text-sm text-gray-600 truncate",children:[e.tipologia," - ",e.sezione]})]}),(0,t.jsxs)(m.E,{variant:"outline",className:"bg-amber-100 text-amber-800 border-amber-300 font-medium min-w-fit",children:[e.metri_residui,"m"]})]})},e.id_bobina))})})]})]}),0===A.length&&!S&&(0,t.jsxs)(o.Fc,{className:"border-amber-200 bg-amber-50",children:[(0,t.jsx)(V.A,{className:"h-4 w-4 text-amber-600"}),(0,t.jsx)(o.TN,{className:"text-amber-800",children:"Non ci sono bobine disponibili. Puoi procedere con BOBINA VUOTA o aggiungere prima una nuova bobina."})]}),f.id_bobina&&(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(Y.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:f.id_bobina})]})]})]}),(0,t.jsxs)(q.Es,{className:"flex-shrink-0 border-t pt-4 mt-4 flex justify-between items-center",children:[(0,t.jsx)("div",{children:"installato"===l.stato_installazione&&l.id_bobina&&(0,t.jsx)(n.$,{variant:"outline",onClick:()=>{F(!0)},disabled:_,className:"text-blue-600 border-blue-300 hover:bg-blue-50",children:"Modifica Bobina"})}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(n.$,{variant:"outline",onClick:J,disabled:_,children:"Annulla"}),(0,t.jsxs)(n.$,{onClick:$,disabled:_||!g.metri_posati||0>parseFloat(g.metri_posati)||!g.id_bobina,className:"bg-mariner-600 hover:bg-mariner-700 text-white disabled:bg-gray-400 disabled:text-gray-200",children:[_&&(0,t.jsx)(H.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salva"]})]})]})]})}),(0,t.jsx)(Q,{open:T,onClose:()=>F(!1),cavo:l,onSuccess:e=>{x(e),F(!1),i()},onError:u})]}):null}var K=i(2564),ee=i(87481);function ea(e){let{open:a,onClose:i,onConfirm:l,title:r,description:c,isLoading:d,isDangerous:m=!1}=e,[x,u]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{a||u(!1)},[a]),(0,s.useEffect)(()=>{let e=e=>{"Escape"===e.key&&a&&!d&&i()};if(a)return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[a,i,d]),(0,t.jsx)(q.lG,{open:a,onOpenChange:i,children:(0,t.jsx)(q.Cf,{className:"sm:max-w-[400px]","aria-describedby":"confirm-disconnect-description",children:x?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(q.c7,{children:(0,t.jsx)(q.L3,{className:"text-center text-orange-600",children:"Conferma Finale"})}),(0,t.jsxs)("div",{className:"py-4 text-center",children:[(0,t.jsx)("div",{className:"mx-auto w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-4",children:(0,t.jsx)(V.A,{className:"h-6 w-6 text-orange-600"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Sei veramente sicuro?"}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 mb-4",children:["Questa azione scollegher\xe0 ",(0,t.jsx)("strong",{children:"entrambi i lati"})," del cavo."]}),(0,t.jsx)("div",{className:"bg-orange-50 border border-orange-200 rounded-md p-3",children:(0,t.jsx)("p",{className:"text-sm text-orange-800 font-medium",children:"⚠️ Operazione irreversibile"})})]}),(0,t.jsxs)(q.Es,{className:"gap-2",children:[(0,t.jsx)(n.$,{variant:"outline",onClick:()=>u(!1),disabled:d,className:"flex-1",children:"No, Annulla"}),(0,t.jsx)(n.$,{variant:"outline",onClick:()=>{l()},disabled:d,className:"flex-1 bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100 hover:border-orange-300",children:d?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(H.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Scollegando..."]}):"S\xec, Scollega"})]})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(q.c7,{children:[(0,t.jsxs)(q.L3,{className:"flex items-center gap-2 text-orange-600",children:[(0,t.jsx)(V.A,{className:"h-5 w-5"}),r]}),(0,t.jsx)(q.rr,{id:"confirm-disconnect-description",children:c})]}),(0,t.jsx)("div",{className:"py-4",children:(0,t.jsxs)(o.Fc,{className:"border-orange-200 bg-orange-50",children:[(0,t.jsx)(V.A,{className:"h-4 w-4 text-orange-600"}),(0,t.jsxs)(o.TN,{className:"text-orange-800",children:[(0,t.jsx)("strong",{children:"Attenzione:"})," Questa azione modificher\xe0 lo stato del collegamento del cavo."]})]})}),(0,t.jsxs)(q.Es,{className:"gap-2",children:[(0,t.jsx)(n.$,{variant:"outline",onClick:i,disabled:d,className:"flex-1 hover:bg-gray-50",children:"Annulla"}),(0,t.jsxs)(n.$,{variant:"outline",onClick:()=>{m?u(!0):l()},disabled:d,className:"flex-1 bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100 hover:border-orange-300",children:[(0,t.jsx)(V.A,{className:"mr-2 h-4 w-4"}),m?"Procedi":"Conferma"]})]})]})})})}let ei=function(e){let{open:a,onClose:i,cavo:l,onSuccess:r,onError:m}=e,{cantiere:x}=(0,c.A)(),{toast:u}=(0,ee.dj)(),[p,h]=(0,s.useState)("cantiere"),[b,g]=(0,s.useState)(!1),[v,f]=(0,s.useState)(""),[j,N]=(0,s.useState)({open:!1,type:null,title:"",description:""}),y=(0,s.useRef)(null),_=(0,s.useRef)(null),w=(0,s.useRef)(null),[A,z]=(0,s.useState)("");(0,s.useEffect)(()=>{a&&l&&(h("cantiere"),f(""))},[a,l]),(0,s.useEffect)(()=>{if(a&&y.current){let e=y.current.querySelectorAll('button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])');e.length>0&&e[0].focus()}},[a]),(0,s.useEffect)(()=>{let e=e=>{if(a&&!b&&!j.open)switch(e.key){case"Escape":i();break;case"Tab":var t;let s=null==(t=y.current)?void 0:t.querySelectorAll('button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])');if(s&&s.length>0){let a=s[0],i=s[s.length-1];e.shiftKey&&document.activeElement===a?(e.preventDefault(),i.focus()):e.shiftKey||document.activeElement!==i||(e.preventDefault(),a.focus())}break;case"1":case"2":case"3":if(!b){e.preventDefault();let a=parseInt(e.key);1===a?k():2===a?O():3===a&&F()}}};if(a)return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[a,b,j.open]);let S=e=>{z(e),setTimeout(()=>z(""),1e3)},E=()=>{if(!l)return{stato:"non_collegato",descrizione:"Non collegato"};switch(l.collegamento||l.collegamenti||0){case 1:return{stato:"partenza",descrizione:"\uD83D\uDFE2⚪ Collegato lato partenza"};case 2:return{stato:"arrivo",descrizione:"⚪\uD83D\uDFE2 Collegato lato arrivo"};case 3:return{stato:"completo",descrizione:"\uD83D\uDFE2\uD83D\uDFE2 Completamente collegato"};default:return{stato:"non_collegato",descrizione:"⚪⚪ Non collegato"}}},k=()=>{if(!l)return;let e=E();"partenza"===e.stato||"completo"===e.stato?N({open:!0,type:"partenza",title:"Scollega lato partenza",description:"Vuoi scollegare il lato partenza del cavo ".concat(l.id_cavo,"?")}):I()},I=async()=>{if(l&&x)try{g(!0),f(""),S("Collegamento in corso..."),await d.At.collegaCavo(x.id_cantiere,l.id_cavo,"partenza",p);let e="Collegamento lato partenza completato per il cavo ".concat(l.id_cavo);S(e),u({title:"Successo",description:e}),r&&r(),i()}catch(t){var e,a;let i=(null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||t.message||"Errore durante il collegamento";f(i),S("Errore: ".concat(i)),m&&m(i)}finally{g(!1)}},O=()=>{if(!l)return;let e=E();"arrivo"===e.stato||"completo"===e.stato?N({open:!0,type:"arrivo",title:"Scollega lato arrivo",description:"Vuoi scollegare il lato arrivo del cavo ".concat(l.id_cavo,"?")}):T()},T=async()=>{if(l&&x)try{g(!0),f(""),S("Collegamento in corso..."),await d.At.collegaCavo(x.id_cantiere,l.id_cavo,"arrivo",p);let e="Collegamento lato arrivo completato per il cavo ".concat(l.id_cavo);S(e),u({title:"Successo",description:e}),r&&r(),i()}catch(t){var e,a;let i=(null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||t.message||"Errore durante il collegamento";f(i),S("Errore: ".concat(i)),m&&m(i)}finally{g(!1)}},F=()=>{l&&("completo"===E().stato?N({open:!0,type:"entrambi",title:"Scollega entrambi i lati",description:"Vuoi scollegare completamente il cavo ".concat(l.id_cavo,"? Questa operazione rimuover\xe0 tutti i collegamenti.")}):D())},D=async()=>{if(l&&x)try{g(!0),f(""),S("Collegamento entrambi i lati in corso..."),await d.At.collegaCavo(x.id_cantiere,l.id_cavo,"entrambi",p);let e="Collegamento completo per il cavo ".concat(l.id_cavo);S(e),u({title:"Successo",description:e}),r&&r(),i()}catch(t){var e,a;let i=(null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||t.message||"Errore durante il collegamento";f(i),S("Errore: ".concat(i)),m&&m(i)}finally{g(!1)}},L=async()=>{if(l&&x&&j.type)try{g(!0),f(""),S("Scollegamento in corso..."),await d.At.scollegaCavo(x.id_cantiere,l.id_cavo,"entrambi"===j.type?void 0:j.type);let e="entrambi"===j.type?"":" lato ".concat(j.type),a="Scollegamento".concat(e," completato per il cavo ").concat(l.id_cavo);S(a),u({title:"Successo",description:a}),r&&r(),N({open:!1,type:null,title:"",description:""}),i()}catch(t){var e,a;let i=(null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||t.message||"Errore durante lo scollegamento";f(i),S("Errore: ".concat(i)),m&&m(i)}finally{g(!1)}};if(!l)return null;let B=E(),R=(l.metri_posati||l.metratura_reale||0)>0;return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(K.bL,{children:(0,t.jsx)("div",{"aria-live":"polite","aria-atomic":"true",children:A})}),(0,t.jsx)(q.lG,{open:a,onOpenChange:i,children:(0,t.jsxs)(q.Cf,{className:"sm:max-w-[600px] max-h-[90vh] overflow-y-auto",ref:y,"aria-describedby":"collegamenti-description",children:[(0,t.jsxs)(q.c7,{children:[(0,t.jsxs)(q.L3,{className:"flex items-center gap-2 text-blue-600",children:[(0,t.jsx)($.A,{className:"h-5 w-5"}),"Gestione Collegamenti - ",l.id_cavo]}),(0,t.jsxs)(q.rr,{id:"collegamenti-description",children:["Gestisci i collegamenti del cavo ",l.id_cavo,". Usa i tasti 1, 2, 3 per azioni rapide."]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-200",children:(0,t.jsxs)("div",{className:"text-sm font-medium text-blue-800",children:["Informazioni Cavo / Tipologia: ",l.tipologia||"N/A"," / Da: ",l.ubicazione_partenza||"N/A"," / Formazione: ",l.sezione||"N/A"," / A: ",l.ubicazione_arrivo||"N/A"," / Metri Posati: ",l.metratura_reale||0," m"]})}),(0,t.jsx)("div",{className:"p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border",children:(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsxs)("div",{children:[(0,t.jsx)(Z.J,{className:"text-sm font-medium text-gray-700",children:"Stato Collegamento"}),(0,t.jsxs)("div",{className:"mt-1 text-lg font-semibold flex items-center gap-2",children:["completo"===B.stato&&(0,t.jsx)(P.A,{className:"h-5 w-5 text-green-600"}),"non_collegato"===B.stato&&(0,t.jsx)(Y.A,{className:"h-5 w-5 text-gray-400"}),("partenza"===B.stato||"arrivo"===B.stato)&&(0,t.jsx)(V.A,{className:"h-5 w-5 text-orange-500"}),B.descrizione]})]})})}),!R&&(0,t.jsxs)(o.Fc,{className:"border-orange-200 bg-orange-50",children:[(0,t.jsx)(Y.A,{className:"h-4 w-4 text-orange-600"}),(0,t.jsxs)(o.TN,{className:"text-orange-800",children:[(0,t.jsx)("strong",{children:"Attenzione:"})," Il cavo deve essere installato prima di poter essere collegato."]})]}),v&&(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(Y.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:v})]}),R&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(Z.J,{className:"text-sm font-medium",children:"Responsabile Collegamento"}),(0,t.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg border",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(P.A,{className:"h-4 w-4 text-green-600"}),(0,t.jsx)("span",{className:"font-medium",children:"Cantiere"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Collegamento eseguito dal responsabile del cantiere"})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(Z.J,{className:"text-sm font-medium",children:"Azioni Disponibili"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-3",children:[(0,t.jsx)(n.$,{ref:_,onClick:k,disabled:b,className:"w-full h-12 text-left justify-start bg-green-50 text-green-700 border-green-200 hover:bg-green-100 hover:border-green-300",variant:"outline",children:(0,t.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-sm font-bold text-green-700",children:"1"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:"partenza"===B.stato||"completo"===B.stato?"Scollega Partenza":"Collega Partenza"}),(0,t.jsx)("div",{className:"text-xs text-green-600",children:"partenza"===B.stato||"completo"===B.stato?"Rimuovi collegamento lato partenza":"Connetti il lato partenza del cavo"})]})]}),b?(0,t.jsx)(H.A,{className:"h-4 w-4 animate-spin"}):(0,t.jsx)($.A,{className:"h-4 w-4"})]})}),(0,t.jsx)(n.$,{onClick:O,disabled:b,className:"w-full h-12 text-left justify-start bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 hover:border-blue-300",variant:"outline",children:(0,t.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-sm font-bold text-blue-700",children:"2"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:"arrivo"===B.stato||"completo"===B.stato?"Scollega Arrivo":"Collega Arrivo"}),(0,t.jsx)("div",{className:"text-xs text-blue-600",children:"arrivo"===B.stato||"completo"===B.stato?"Rimuovi collegamento lato arrivo":"Connetti il lato arrivo del cavo"})]})]}),b?(0,t.jsx)(H.A,{className:"h-4 w-4 animate-spin"}):(0,t.jsx)($.A,{className:"h-4 w-4"})]})}),(0,t.jsx)(n.$,{onClick:F,disabled:b,className:"w-full h-12 text-left justify-start bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100 hover:border-purple-300",variant:"outline",children:(0,t.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-sm font-bold text-purple-700",children:"3"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:"completo"===B.stato?"Scollega Completamente":"Collega Entrambi"}),(0,t.jsx)("div",{className:"text-xs text-purple-600",children:"completo"===B.stato?"Rimuovi tutti i collegamenti":"Connetti entrambi i lati del cavo"})]})]}),b?(0,t.jsx)(H.A,{className:"h-4 w-4 animate-spin"}):(0,t.jsx)($.A,{className:"h-4 w-4"})]})})]})]})]})]}),(0,t.jsx)(q.Es,{children:(0,t.jsxs)(n.$,{ref:w,variant:"outline",onClick:i,disabled:b,className:"hover:bg-gray-50",children:[(0,t.jsx)(C.A,{className:"mr-2 h-4 w-4"}),"Chiudi"]})})]})}),(0,t.jsx)(ea,{open:j.open,onClose:()=>N({open:!1,type:null,title:"",description:""}),onConfirm:L,title:j.title,description:j.description,isLoading:b,isDangerous:"entrambi"===j.type})]})};var et=i(88539),es=i(69037),el=i(91788);function er(e){let{open:a,onClose:i,cavo:l,onSuccess:r,onError:m}=e,{cantiere:x}=(0,c.A)(),{toast:u}=(0,ee.dj)(),[b,g]=(0,s.useState)({responsabile_certificazione:"",data_certificazione:new Date().toISOString().split("T")[0],esito_certificazione:"CONFORME",note_certificazione:""}),[v,f]=(0,s.useState)([]),[j,N]=(0,s.useState)(!1),[y,_]=(0,s.useState)(!1),[w,A]=(0,s.useState)(""),[z,S]=(0,s.useState)(""),E=e=>{S(e),setTimeout(()=>S(""),1e3)};(0,s.useEffect)(()=>{a&&l&&(g({responsabile_certificazione:"",data_certificazione:new Date().toISOString().split("T")[0],esito_certificazione:"CONFORME",note_certificazione:""}),A(""),k())},[a,l]);let k=async()=>{if(x)try{_(!0);let e=await d.AR.getResponsabili(x.id_cantiere);f(e.data)}catch(e){f([])}finally{_(!1)}},I=()=>!!l&&3===(l.collegamento||l.collegamenti||0),O=async()=>{if(!l||!x)return!1;try{return E("Collegamento automatico in corso..."),await d.At.collegaCavo(x.id_cantiere,l.id_cavo,"entrambi","cantiere"),E("Cavo collegato automaticamente"),!0}catch(e){return console.error("Errore nel collegamento automatico:",e),!1}},T=async()=>{if(l&&x){if(!b.responsabile_certificazione){A("Seleziona un responsabile per la certificazione"),E("Errore: Seleziona un responsabile per la certificazione");return}try{if(N(!0),A(""),E("Certificazione in corso..."),!I()&&(E("Collegamento automatico del cavo..."),!await O())){A("Impossibile collegare automaticamente il cavo. Collegalo manualmente prima di certificare."),E("Errore: Impossibile collegare automaticamente il cavo");return}let e={id_cavo:l.id_cavo,responsabile_certificazione:b.responsabile_certificazione,data_certificazione:b.data_certificazione,esito_certificazione:b.esito_certificazione,note_certificazione:b.note_certificazione||null};await d.km.createCertificazione(x.id_cantiere,e);let a="Certificazione completata per il cavo ".concat(l.id_cavo);E(a),u({title:"Successo",description:a}),r&&r(a),i()}catch(t){var e,a;let i=(null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||t.message||"Errore durante la certificazione";A(i),E("Errore: ".concat(i)),m&&m(i)}finally{N(!1)}}},F=async()=>{if(l&&x)try{N(!0),A(""),E("Generazione PDF in corso...");let e=await d.km.generatePDF(x.id_cantiere,l.id_cavo),a=window.URL.createObjectURL(new Blob([e.data])),i=document.createElement("a");i.href=a,i.setAttribute("download","certificato_".concat(l.id_cavo,".pdf")),document.body.appendChild(i),i.click(),i.remove(),window.URL.revokeObjectURL(a);let t="PDF certificato generato per il cavo ".concat(l.id_cavo);E(t),u({title:"Successo",description:t}),r&&r(t)}catch(t){var e,a;let i=(null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||t.message||"Errore durante la generazione del PDF";A(i),E("Errore: ".concat(i)),m&&m(i)}finally{N(!1)}};if(!l)return null;let D="Installato"===l.stato_installazione||"INSTALLATO"===l.stato_installazione||"POSATO"===l.stato_installazione||(l.metri_posati||l.metratura_reale||0)>0,L=I(),B=l.responsabile_partenza&&l.responsabile_arrivo,R=!!l&&(!0===l.certificato||"SI"===l.certificato||"CERTIFICATO"===l.certificato);return(0,t.jsxs)(q.lG,{open:a,onOpenChange:i,children:[(0,t.jsx)("div",{"aria-live":"polite","aria-atomic":"true",className:"sr-only",children:z}),(0,t.jsxs)(q.Cf,{className:"sm:max-w-[700px] max-h-[90vh] overflow-y-auto","aria-describedby":"certificazione-description",children:[(0,t.jsxs)(q.c7,{children:[(0,t.jsxs)(q.L3,{className:"flex items-center gap-2 text-blue-600",children:[(0,t.jsx)(es.A,{className:"h-5 w-5"}),"Gestione Certificazione - ",l.id_cavo]}),(0,t.jsxs)(q.rr,{id:"certificazione-description",children:["Certifica il cavo ",l.id_cavo," secondo normativa CEI 64-8 o genera il PDF del certificato"]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-200",children:(0,t.jsxs)("div",{className:"text-sm font-medium text-blue-800",children:["Informazioni Cavo / Tipologia: ",l.tipologia||"N/A"," / Da: ",l.ubicazione_partenza||"N/A"," / Formazione: ",l.sezione||"N/A"," / A: ",l.ubicazione_arrivo||"N/A"," / Metri Posati: ",l.metratura_reale||0," m"]})}),(0,t.jsxs)("div",{className:"p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border",children:[(0,t.jsx)(Z.J,{className:"text-sm font-medium text-gray-700",children:"Stato Cavo"}),(0,t.jsxs)("div",{className:"mt-2 space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[D?(0,t.jsx)(P.A,{className:"w-4 h-4 text-green-600"}):(0,t.jsx)(Y.A,{className:"w-4 h-4 text-red-500"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:D?"Installato/Posato":"Non installato"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[L?(0,t.jsx)(P.A,{className:"w-4 h-4 text-green-600"}):(0,t.jsx)(Y.A,{className:"w-4 h-4 text-orange-500"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:L?"Collegato":"Non collegato"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[R?(0,t.jsx)(P.A,{className:"w-4 h-4 text-green-600"}):(0,t.jsx)(Y.A,{className:"w-4 h-4 text-gray-400"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:R?"Certificato":"Non certificato"})]})]})]}),!D&&(0,t.jsxs)(o.Fc,{className:"border-red-200 bg-red-50",children:[(0,t.jsx)(Y.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsxs)(o.TN,{className:"text-red-800",children:[(0,t.jsx)("strong",{children:"ATTENZIONE:"})," Il cavo deve essere installato/posato prima di poter essere certificato secondo CEI 64-8."]})]}),D&&!(L&&B)&&(0,t.jsxs)(o.Fc,{className:"border-amber-200 bg-amber-50",children:[(0,t.jsx)(Y.A,{className:"h-4 w-4 text-amber-600"}),(0,t.jsxs)(o.TN,{className:"text-amber-800",children:[(0,t.jsx)("strong",{children:"ATTENZIONE:"}),' Il cavo non risulta completamente collegato. Durante la certificazione sar\xe0 possibile collegarlo automaticamente a "cantiere" su entrambi i lati.']})]}),w&&(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(Y.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:w})]}),R?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)(o.Fc,{className:"border-green-200 bg-green-50",children:[(0,t.jsx)(es.A,{className:"h-4 w-4 text-green-600"}),(0,t.jsxs)(o.TN,{className:"text-green-800",children:[(0,t.jsx)("strong",{children:"CERTIFICATO:"})," Questo cavo \xe8 gi\xe0 stato certificato. Puoi generare il PDF del certificato."]})]}),(0,t.jsxs)(n.$,{onClick:F,disabled:j,className:"w-full bg-green-600 hover:bg-green-700 text-white",size:"lg",children:[j?(0,t.jsx)(H.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(el.A,{className:"h-4 w-4 mr-2"}),"Genera PDF Certificato"]})]}):D&&(0,t.jsxs)("div",{className:"space-y-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border-2 border-blue-200",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-blue-800 mb-4",children:"\uD83D\uDCCB Dati Certificazione CEI 64-8"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(Z.J,{htmlFor:"responsabile",className:"text-sm font-medium text-gray-700",children:"Responsabile Certificazione *"}),(0,t.jsxs)(h.l6,{value:b.responsabile_certificazione,onValueChange:e=>g(a=>({...a,responsabile_certificazione:e})),disabled:y,children:[(0,t.jsx)(h.bq,{className:"border-2 border-gray-300 focus:border-blue-500",children:(0,t.jsx)(h.yv,{placeholder:"Seleziona responsabile..."})}),(0,t.jsx)(h.gC,{children:v.map(e=>(0,t.jsxs)(h.eb,{value:e.nome_responsabile,children:[e.nome_responsabile,e.numero_telefono&&" - ".concat(e.numero_telefono)]},e.id))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(Z.J,{htmlFor:"data",className:"text-sm font-medium text-gray-700",children:"Data Certificazione"}),(0,t.jsx)(p.p,{id:"data",type:"date",value:b.data_certificazione,onChange:e=>g(a=>({...a,data_certificazione:e.target.value})),className:"border-2 border-gray-300 focus:border-blue-500"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(Z.J,{htmlFor:"esito",className:"text-sm font-medium text-gray-700",children:"Esito Certificazione"}),(0,t.jsxs)(h.l6,{value:b.esito_certificazione,onValueChange:e=>g(a=>({...a,esito_certificazione:e})),children:[(0,t.jsx)(h.bq,{className:"border-2 border-gray-300 focus:border-blue-500",children:(0,t.jsx)(h.yv,{})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"CONFORME",children:"✅ CONFORME"}),(0,t.jsx)(h.eb,{value:"NON_CONFORME",children:"❌ NON CONFORME"}),(0,t.jsx)(h.eb,{value:"CONFORME_CON_RISERVA",children:"⚠️ CONFORME CON RISERVA"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(Z.J,{htmlFor:"note",className:"text-sm font-medium text-gray-700",children:"Note (opzionale)"}),(0,t.jsx)(et.T,{id:"note",placeholder:"Inserisci eventuali note sulla certificazione...",value:b.note_certificazione,onChange:e=>g(a=>({...a,note_certificazione:e.target.value})),rows:3,className:"border-2 border-gray-300 focus:border-blue-500"})]}),(0,t.jsxs)(n.$,{onClick:T,disabled:j||!b.responsabile_certificazione,className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3",size:"lg",children:[j?(0,t.jsx)(H.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(es.A,{className:"h-4 w-4 mr-2"}),"Certifica Cavo CEI 64-8"]})]})]}),(0,t.jsx)(q.Es,{className:"border-t pt-4",children:(0,t.jsxs)(n.$,{variant:"outline",onClick:i,disabled:j,className:"flex items-center gap-2",children:[(0,t.jsx)(C.A,{className:"h-4 w-4"}),"Chiudi"]})})]})]})}var en=i(25273),eo=i(17580);function ec(e){let{open:a,onClose:i,caviSelezionati:l,tipoComanda:r,onSuccess:m,onError:x}=e,{cantiere:u}=(0,c.A)(),[p,b]=(0,s.useState)({tipo_comanda:r||"POSA",responsabile:"",note:""}),[g,v]=(0,s.useState)([]),[f,j]=(0,s.useState)(!1),[N,y]=(0,s.useState)(!1),[_,C]=(0,s.useState)("");(0,s.useEffect)(()=>{a&&(b({tipo_comanda:r||"POSA",responsabile:"",note:""}),C(""),w())},[a,r]);let w=async()=>{if(u)try{y(!0);let e=await d.AR.getResponsabili(u.id_cantiere);v(e.data)}catch(e){v([])}finally{y(!1)}},A=async()=>{if(u){if(!p.responsabile)return void C("Seleziona un responsabile per la comanda");if(0===l.length)return void C("Seleziona almeno un cavo per la comanda");try{j(!0),C("");let e={tipo_comanda:p.tipo_comanda,responsabile:p.responsabile,note:p.note||null},a=await d.CV.createComandaWithCavi(u.id_cantiere,e,l);m("Comanda ".concat(a.data.codice_comanda," creata con successo per ").concat(l.length," cavi")),i()}catch(i){var e,a;x((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante la creazione della comanda")}finally{j(!1)}}};return(0,t.jsx)(q.lG,{open:a,onOpenChange:i,children:(0,t.jsxs)(q.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(q.c7,{children:[(0,t.jsxs)(q.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(en.A,{className:"h-5 w-5"}),"Crea Nuova Comanda"]}),(0,t.jsxs)(q.rr,{children:["Crea una nuova comanda per ",l.length," cavi selezionati"]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsxs)(Z.J,{className:"text-sm font-medium",children:["Cavi Selezionati (",l.length,")"]}),(0,t.jsx)("div",{className:"mt-2 max-h-32 overflow-y-auto",children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-1",children:[l.slice(0,10).map(e=>(0,t.jsx)("span",{className:"inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded",children:e},e)),l.length>10&&(0,t.jsxs)("span",{className:"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded",children:["+",l.length-10," altri..."]})]})})]}),_&&(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(Y.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:_})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(Z.J,{htmlFor:"tipo",children:"Tipo Comanda *"}),(0,t.jsxs)(h.l6,{value:p.tipo_comanda,onValueChange:e=>b(a=>({...a,tipo_comanda:e})),children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"POSA",children:"\uD83D\uDD27 Posa Cavi"}),(0,t.jsx)(h.eb,{value:"COLLEGAMENTO_PARTENZA",children:"\uD83D\uDD0C Collegamento Partenza"}),(0,t.jsx)(h.eb,{value:"COLLEGAMENTO_ARRIVO",children:"⚡ Collegamento Arrivo"}),(0,t.jsx)(h.eb,{value:"CERTIFICAZIONE",children:"\uD83D\uDCCB Certificazione"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(Z.J,{htmlFor:"responsabile",children:"Responsabile *"}),(0,t.jsxs)(h.l6,{value:p.responsabile,onValueChange:e=>b(a=>({...a,responsabile:e})),disabled:N,children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{placeholder:"Seleziona responsabile..."})}),(0,t.jsx)(h.gC,{children:g.map(e=>(0,t.jsx)(h.eb,{value:e.nome_responsabile,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(eo.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.nome_responsabile}),e.numero_telefono&&(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["- ",e.numero_telefono]})]})},e.id))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(Z.J,{htmlFor:"note",children:"Note (opzionale)"}),(0,t.jsx)(et.T,{id:"note",placeholder:"Inserisci eventuali note per la comanda...",value:p.note,onChange:e=>b(a=>({...a,note:e.target.value})),rows:3})]}),(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)(Z.J,{className:"text-sm font-medium",children:"Riepilogo Comanda"}),(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Tipo:"})," ",(e=>{switch(e){case"POSA":return"Posa Cavi";case"COLLEGAMENTO_PARTENZA":return"Collegamento Partenza";case"COLLEGAMENTO_ARRIVO":return"Collegamento Arrivo";case"CERTIFICAZIONE":return"Certificazione";default:return e}})(p.tipo_comanda)]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Responsabile:"})," ",p.responsabile||"Non selezionato"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Cavi:"})," ",l.length," selezionati"]}),p.note&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Note:"})," ",p.note]})]})]})]}),(0,t.jsxs)(q.Es,{children:[(0,t.jsx)(n.$,{variant:"outline",onClick:i,disabled:f,children:"Annulla"}),(0,t.jsxs)(n.$,{onClick:A,disabled:f||!p.responsabile||0===l.length,children:[f?(0,t.jsx)(H.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(en.A,{className:"h-4 w-4 mr-2"}),"Crea Comanda"]})]})]})})}var ed=i(29869),em=i(64261);function ex(e){let{open:a,onClose:i,tipo:l,onSuccess:r,onError:m}=e,{cantiere:x}=(0,c.A)(),[u,h]=(0,s.useState)(null),[b,g]=(0,s.useState)(""),[v,f]=(0,s.useState)(!1),[j,N]=(0,s.useState)(""),[y,_]=(0,s.useState)(0),C=(0,s.useRef)(null),w=async()=>{if(u&&x){if("cavi"===l&&!b.trim())return void N("Inserisci il codice revisione per l'importazione cavi");try{let e;if(f(!0),N(""),_(0),e="cavi"===l?await d.mg.importCavi(x.id_cantiere,u,b.trim()):await d.mg.importBobine(x.id_cantiere,u),_(100),e.data.success){let a=e.data.details,t=e.data.message;"cavi"===l&&(null==a?void 0:a.cavi_importati)?t+=" (".concat(a.cavi_importati," cavi importati)"):"bobine"===l&&(null==a?void 0:a.bobine_importate)&&(t+=" (".concat(a.bobine_importate," bobine importate)")),r(t),i()}else m(e.data.message||"Errore durante l'importazione")}catch(i){var e,a;m((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante l'importazione del file")}finally{f(!1),_(0)}}},A=()=>{v||(h(null),g(""),N(""),_(0),C.current&&(C.current.value=""),i())},z=()=>"cavi"===l?"Cavi":"Bobine";return(0,t.jsx)(q.lG,{open:a,onOpenChange:A,children:(0,t.jsxs)(q.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(q.c7,{children:[(0,t.jsxs)(q.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(ed.A,{className:"h-5 w-5"}),"Importa ",z()," da Excel"]}),(0,t.jsxs)(q.rr,{children:["Carica un file Excel per importare ",z().toLowerCase()," nel cantiere"]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)(Z.J,{className:"text-sm font-medium",children:"Requisiti File"}),(0,t.jsx)("ul",{className:"mt-2 space-y-1 text-sm text-gray-600",children:("cavi"===l?["File Excel (.xlsx o .xls)","Colonne richieste: ID_CAVO, SISTEMA, UTILITY, TIPOLOGIA, ecc.","Prima riga deve contenere le intestazioni","Codice revisione obbligatorio per tracciabilit\xe0"]:["File Excel (.xlsx o .xls)","Colonne richieste: NUMERO_BOBINA, UTILITY, TIPOLOGIA, METRI_TOTALI, ecc.","Prima riga deve contenere le intestazioni","I metri residui saranno impostati uguali ai metri totali"]).map((e,a)=>(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)("span",{className:"text-blue-500 mt-0.5",children:"•"}),(0,t.jsx)("span",{children:e})]},a))})]}),j&&(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(Y.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:j})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(Z.J,{htmlFor:"file",children:"File Excel *"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(p.p,{ref:C,id:"file",type:"file",accept:".xlsx,.xls",onChange:e=>{var a;let i=null==(a=e.target.files)?void 0:a[0];if(i){if(!["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel"].includes(i.type)&&!i.name.toLowerCase().endsWith(".xlsx")&&!i.name.toLowerCase().endsWith(".xls"))return void N("Seleziona un file Excel valido (.xlsx o .xls)");h(i),N("")}},disabled:v,className:"flex-1"}),u&&(0,t.jsxs)("div",{className:"flex items-center gap-1 text-green-600",children:[(0,t.jsx)(P.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm",children:"File selezionato"})]})]}),u&&(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,t.jsx)(em.A,{className:"h-4 w-4 inline mr-1"}),u.name," (",(u.size/1024/1024).toFixed(2)," MB)"]})]}),"cavi"===l&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(Z.J,{htmlFor:"revisione",children:"Codice Revisione *"}),(0,t.jsx)(p.p,{id:"revisione",value:b,onChange:e=>g(e.target.value),placeholder:"es. REV001, V1.0, 2024-01",disabled:v}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Codice identificativo della revisione per tracciabilit\xe0 delle modifiche"})]}),v&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(H.A,{className:"h-4 w-4 animate-spin"}),(0,t.jsx)("span",{className:"text-sm",children:"Caricamento in corso..."})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(y,"%")}})})]}),u&&(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsx)(Z.J,{className:"text-sm font-medium",children:"Riepilogo Importazione"}),(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Tipo:"})," ",z()]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"File:"})," ",u.name]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Dimensione:"})," ",(u.size/1024/1024).toFixed(2)," MB"]}),"cavi"===l&&b&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Revisione:"})," ",b]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Cantiere:"})," ",null==x?void 0:x.nome_cantiere]})]})]})]}),(0,t.jsxs)(q.Es,{children:[(0,t.jsx)(n.$,{variant:"outline",onClick:A,disabled:v,children:"Annulla"}),(0,t.jsxs)(n.$,{onClick:w,disabled:v||!u||"cavi"===l&&!b.trim(),children:[v?(0,t.jsx)(H.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(ed.A,{className:"h-4 w-4 mr-2"}),"Importa ",z()]})]})]})})}var eu=i(54213);function ep(e){let{open:a,onClose:i,onSuccess:l,onError:r}=e,{cantiere:m}=(0,c.A)(),[u,p]=(0,s.useState)({cavi:!0,bobine:!0,comande:!1,certificazioni:!1,responsabili:!1}),[h,b]=(0,s.useState)(!1),[g,v]=(0,s.useState)(""),f=(e,a)=>{p(i=>({...i,[e]:a}))},j=async()=>{if(m)try{b(!0);let e=await d.mg.exportCavi(m.id_cantiere),a=window.URL.createObjectURL(new Blob([e.data])),i=document.createElement("a");i.href=a,i.setAttribute("download","cavi_".concat(m.nome_cantiere,"_").concat(new Date().toISOString().split("T")[0],".xlsx")),document.body.appendChild(i),i.click(),i.remove(),window.URL.revokeObjectURL(a),l("Export cavi completato con successo")}catch(i){var e,a;r((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante l'export dei cavi")}finally{b(!1)}},N=async()=>{if(m)try{b(!0);let e=await d.mg.exportBobine(m.id_cantiere),a=window.URL.createObjectURL(new Blob([e.data])),i=document.createElement("a");i.href=a,i.setAttribute("download","bobine_".concat(m.nome_cantiere,"_").concat(new Date().toISOString().split("T")[0],".xlsx")),document.body.appendChild(i),i.click(),i.remove(),window.URL.revokeObjectURL(a),l("Export bobine completato con successo")}catch(i){var e,a;r((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante l'export delle bobine")}finally{b(!1)}},y=async()=>{if(m)try{b(!0),v("");let e=[];u.cavi&&e.push(j()),u.bobine&&e.push(N()),u.comande,u.certificazioni,u.responsabili,await Promise.all(e);let a=Object.values(u).filter(Boolean).length;l("Export completato: ".concat(a," file scaricati")),i()}catch(i){var e,a;r((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante l'export dei dati")}finally{b(!1)}},_=[{key:"cavi",label:"Cavi",description:"Esporta tutti i cavi del cantiere con stato, collegamenti e certificazioni",icon:(0,t.jsx)(eu.A,{className:"h-4 w-4"}),available:!0},{key:"bobine",label:"Bobine",description:"Esporta tutte le bobine del parco cavi con metri residui e assegnazioni",icon:(0,t.jsx)(em.A,{className:"h-4 w-4"}),available:!0},{key:"comande",label:"Comande",description:"Esporta tutte le comande con cavi assegnati e responsabili",icon:(0,t.jsx)(em.A,{className:"h-4 w-4"}),available:!1},{key:"certificazioni",label:"Certificazioni",description:"Esporta tutte le certificazioni con esiti e responsabili",icon:(0,t.jsx)(em.A,{className:"h-4 w-4"}),available:!1},{key:"responsabili",label:"Responsabili",description:"Esporta tutti i responsabili con contatti e ruoli",icon:(0,t.jsx)(em.A,{className:"h-4 w-4"}),available:!1}];return(0,t.jsx)(q.lG,{open:a,onOpenChange:i,children:(0,t.jsxs)(q.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(q.c7,{children:[(0,t.jsxs)(q.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(el.A,{className:"h-5 w-5"}),"Esporta Dati Cantiere"]}),(0,t.jsxs)(q.rr,{children:["Seleziona i dati da esportare dal cantiere ",null==m?void 0:m.nome_cantiere]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[g&&(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(Y.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:g})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(Z.J,{className:"text-sm font-medium",children:"Seleziona Dati da Esportare"}),_.map(e=>(0,t.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg border ".concat(e.available?"bg-white":"bg-gray-50"),children:[(0,t.jsx)(x.S,{id:e.key,checked:u[e.key],onCheckedChange:a=>f(e.key,a),disabled:!e.available||h}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,(0,t.jsxs)(Z.J,{htmlFor:e.key,className:"font-medium ".concat(e.available?"":"text-gray-500"),children:[e.label,!e.available&&(0,t.jsx)("span",{className:"ml-2 text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded",children:"In sviluppo"})]})]}),(0,t.jsx)("p",{className:"text-sm mt-1 ".concat(e.available?"text-gray-600":"text-gray-400"),children:e.description})]})]},e.key))]}),(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)(Z.J,{className:"text-sm font-medium",children:"Informazioni Export"}),(0,t.jsxs)("ul",{className:"mt-2 space-y-1 text-sm text-gray-600",children:[(0,t.jsx)("li",{children:"• I file saranno scaricati in formato Excel (.xlsx)"}),(0,t.jsx)("li",{children:"• I nomi file includeranno data e nome cantiere"}),(0,t.jsx)("li",{children:"• I dati esportati riflettono lo stato attuale del database"}),(0,t.jsx)("li",{children:"• L'export non modifica i dati originali"})]})]}),Object.values(u).filter(Boolean).length>0&&(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsx)(Z.J,{className:"text-sm font-medium",children:"Riepilogo Export"}),(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Cantiere:"})," ",null==m?void 0:m.nome_cantiere]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"File da scaricare:"})," ",Object.values(u).filter(Boolean).length]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Data export:"})," ",new Date().toLocaleDateString("it-IT")]})]})]})]}),(0,t.jsxs)(q.Es,{children:[(0,t.jsx)(n.$,{variant:"outline",onClick:i,disabled:h,children:"Annulla"}),(0,t.jsxs)(n.$,{onClick:y,disabled:h||0===Object.values(u).filter(Boolean).length,children:[h?(0,t.jsx)(H.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(el.A,{className:"h-4 w-4 mr-2"}),"Esporta ",Object.values(u).filter(Boolean).length>0?"(".concat(Object.values(u).filter(Boolean).length,")"):""]})]})]})})}function eh(){let{user:e,cantiere:a,isAuthenticated:i,isLoading:m}=(0,c.A)(),x=(0,l.useRouter)();console.log({cantiereFromAuth:a,user:e,userRole:null==e?void 0:e.ruolo});let u=e=>{let{title:a,description:i,variant:t}=e},[p,h]=(0,s.useState)([]),[b,g]=(0,s.useState)([]),[v,f]=(0,s.useState)(!0),[j,N]=(0,s.useState)(""),[y,_]=(0,s.useState)([]),[C,w]=(0,s.useState)(!0),[A,z]=(0,s.useState)([]),[S,E]=(0,s.useState)("");(0,s.useEffect)(()=>{z(p)},[p]);let[k,I]=(0,s.useState)({open:!1,cavo:null}),[O,T]=(0,s.useState)({open:!1,cavo:null}),[F,D]=(0,s.useState)({open:!1,cavo:null}),[L,R]=(0,s.useState)({open:!1,cavo:null}),[M,P]=(0,s.useState)({open:!1}),[U,V]=(0,s.useState)({open:!1}),[$,q]=(0,s.useState)(!1),[Z,W]=(0,s.useState)({totali:0,installati:0,collegati:0,certificati:0,percentualeInstallazione:0,percentualeCollegamento:0,percentualeCertificazione:0,metriTotali:0,metriInstallati:0,metriCollegati:0,metriCertificati:0}),[K,ee]=(0,s.useState)(0);(0,s.useEffect)(()=>{m||i||x.push("/login")},[i,m,x]),(0,s.useEffect)(()=>{{let e=(null==a?void 0:a.id_cantiere)||parseInt(localStorage.getItem("selectedCantiereId")||"0");ee(e),console.log({cantiereFromAuth:null==a?void 0:a.id_cantiere,storedId:e,localStorage:localStorage.getItem("selectedCantiereId")})}},[a]);let ea=a||(K>0?{id_cantiere:K,commessa:"Cantiere ".concat(K)}:null);(0,s.useEffect)(()=>{K&&K>0&&(es(),et())},[K]);let et=async()=>{try{let e=await fetch("http://localhost:8001/api/cavi/".concat(K,"/revisione-corrente"),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}});if(e.ok){let a=await e.json();E(a.revisione_corrente||"00")}else E("00")}catch(e){E("00")}},es=async()=>{try{f(!0),N("");try{let e=await d.At.getCavi(K),a=e.filter(e=>!e.spare),i=e.filter(e=>e.spare);h(a),g(i),el(a)}catch(e){try{let e=await fetch("http://localhost:8001/api/cavi/debug/".concat(K)),a=await e.json();if(a.cavi&&Array.isArray(a.cavi)){let e=a.cavi.filter(e=>!e.spare),i=a.cavi.filter(e=>e.spare);h(e),g(i),el(e),N("⚠️ Dati caricati tramite endpoint debug (problema autenticazione)")}else throw Error("Formato dati debug non valido")}catch(a){throw e}}}catch(i){var e,a;N("Errore nel caricamento dei cavi: ".concat((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message))}finally{f(!1)}},el=e=>{let a=e.length,i=e.filter(e=>(e.metri_posati||e.metratura_reale||0)>0).length,t=e.filter(e=>3===(e.collegamento||e.collegamenti)).length,s=e.filter(e=>e.certificato).length,l=e.reduce((e,a)=>e+(a.metri_teorici||0),0),r=e.reduce((e,a)=>e+(a.metri_posati||0),0);W({totali:a,installati:i,collegati:t,certificati:s,percentualeInstallazione:a>0?Math.round(i/a*100):0,percentualeCollegamento:a>0?Math.round(t/a*100):0,percentualeCertificazione:a>0?Math.round(s/a*100):0,metriTotali:l,metriInstallati:r,metriCollegati:e.filter(e=>3===e.collegamento).reduce((e,a)=>e+(a.metri_posati||0),0),metriCertificati:e.filter(e=>e.certificato).reduce((e,a)=>e+(a.metri_posati||0),0)})},en=(e,a,i)=>{switch(a){case"insert_meters":I({open:!0,cavo:e});break;case"modify_reel":T({open:!0,cavo:e});break;case"view_command":u({title:"Visualizza Comanda",description:"Apertura comanda ".concat(i," per cavo ").concat(e.id_cavo)});break;case"connect_cable":case"connect_arrival":case"connect_departure":case"disconnect_cable":case"manage_connections":D({open:!0,cavo:e});break;case"create_certificate":case"generate_pdf":R({open:!0,cavo:e})}},eo=(e,a)=>{switch(a){case"view_details":u({title:"Visualizza Dettagli",description:"Apertura dettagli per cavo ".concat(e.id_cavo)});break;case"edit":u({title:"Modifica Cavo",description:"Funzione modifica cavo in sviluppo"});break;case"delete":u({title:"Elimina Cavo",description:"Funzione eliminazione cavo in sviluppo",variant:"destructive"});break;case"add_new":u({title:"Aggiungi Nuovo Cavo",description:"Funzione aggiunta nuovo cavo in sviluppo"});break;case"select":y.includes(e.id_cavo)?(_(y.filter(a=>a!==e.id_cavo)),u({title:"Cavo Deselezionato",description:"Cavo ".concat(e.id_cavo," deselezionato")})):(_([...y,e.id_cavo]),u({title:"Cavo Selezionato",description:"Cavo ".concat(e.id_cavo," selezionato")}));break;case"copy_id":navigator.clipboard.writeText(e.id_cavo),u({title:"ID Copiato",description:"ID cavo ".concat(e.id_cavo," copiato negli appunti")});break;case"copy_details":let i="ID: ".concat(e.id_cavo,", Tipologia: ").concat(e.tipologia,", Formazione: ").concat(e.formazione||e.sezione,", Metri: ").concat(e.metri_teorici);navigator.clipboard.writeText(i),u({title:"Dettagli Copiati",description:"Dettagli cavo copiati negli appunti"});break;case"add_to_command":u({title:"Aggiungi a Comanda",description:"Funzione aggiunta a comanda in sviluppo"});break;case"remove_from_command":u({title:"Rimuovi da Comanda",description:"Funzione rimozione da comanda in sviluppo"});break;case"create_command_posa":P({open:!0,tipoComanda:"POSA"});break;case"create_command_collegamento_partenza":P({open:!0,tipoComanda:"COLLEGAMENTO_PARTENZA"});break;case"create_command_collegamento_arrivo":P({open:!0,tipoComanda:"COLLEGAMENTO_ARRIVO"});break;case"create_command_certificazione":P({open:!0,tipoComanda:"CERTIFICAZIONE"});break;case"add_multiple_to_command":u({title:"Aggiungi Tutti a Comanda",description:"Funzione aggiunta multipla a comanda in sviluppo"});break;case"remove_multiple_from_commands":u({title:"Rimuovi Tutti dalle Comande",description:"Funzione rimozione multipla dalle comande in sviluppo"});break;default:u({title:"Azione non implementata",description:"Azione ".concat(a," non ancora implementata")})}},ed=e=>{u({title:"Operazione completata",description:e}),es()},em=e=>{u({title:"Errore",description:e,variant:"destructive"})};return m||v?(0,t.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,t.jsx)(H.A,{className:"h-8 w-8 animate-spin"})}):K?j?(0,t.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(Y.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:j})]}),(0,t.jsx)(n.$,{onClick:es,className:"mt-4",children:"Riprova"})]}):(0,t.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,t.jsx)(G,{cavi:p,filteredCavi:A,revisioneCorrente:S,className:"mb-2"}),(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsx)(B,{cavi:p,loading:v,selectionEnabled:C,selectedCavi:y,onSelectionChange:_,onStatusAction:en,onContextMenuAction:eo})}),b.length>0&&(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsxs)(r.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(J.A,{className:"h-5 w-5"}),(0,t.jsxs)("span",{children:["Cavi Spare (",b.length,")"]})]})}),(0,t.jsx)(r.Wu,{children:(0,t.jsx)(B,{cavi:b,loading:v,selectionEnabled:!1,onStatusAction:en,onContextMenuAction:eo})})]})}),!1,(0,t.jsx)(X,{open:k.open,onClose:()=>I({open:!1,cavo:null}),cavo:k.cavo,cantiere:ea,onSuccess:ed,onError:em}),(0,t.jsx)(Q,{open:O.open,onClose:()=>T({open:!1,cavo:null}),cavo:O.cavo,cantiere:ea,onSuccess:ed,onError:em}),(0,t.jsx)(ei,{open:F.open,onClose:()=>D({open:!1,cavo:null}),cavo:F.cavo,onSuccess:ed,onError:em}),(0,t.jsx)(er,{open:L.open,onClose:()=>R({open:!1,cavo:null}),cavo:L.cavo,onSuccess:ed,onError:em}),(0,t.jsx)(ec,{open:M.open,onClose:()=>P({open:!1}),caviSelezionati:y,tipoComanda:M.tipoComanda,onSuccess:ed,onError:em}),(0,t.jsx)(ex,{open:U.open,onClose:()=>V({open:!1}),tipo:U.tipo||"cavi",onSuccess:ed,onError:em}),(0,t.jsx)(ep,{open:$,onClose:()=>q(!1),onSuccess:ed,onError:em})]}):(0,t.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,t.jsxs)(o.Fc,{children:[(0,t.jsx)(Y.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:"Nessun cantiere selezionato. Seleziona un cantiere per visualizzare i cavi."})]}),(0,t.jsxs)("div",{className:"mt-4 p-4 bg-gray-100 rounded",children:[(0,t.jsx)("h3",{className:"font-bold",children:"Debug Info:"}),(0,t.jsxs)("p",{children:["User: ",e?e.username:"Non autenticato"]}),(0,t.jsxs)("p",{children:["Cantiere context: ",a?a.commessa:"Nessuno"]}),(0,t.jsxs)("p",{children:["Token presente: ",localStorage.getItem("token")?"S\xec":"No"]})]})]})}},87481:(e,a,i)=>{"use strict";i.d(a,{dj:()=>x});var t=i(12115);let s=0,l=new Map,r=e=>{if(l.has(e))return;let a=setTimeout(()=>{l.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);l.set(e,a)},n=(e,a)=>{switch(a.type){case"ADD_TOAST":return{...e,toasts:[a.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===a.toast.id?{...e,...a.toast}:e)};case"DISMISS_TOAST":{let{toastId:i}=a;return i?r(i):e.toasts.forEach(e=>{r(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===i||void 0===i?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===a.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==a.toastId)}}},o=[],c={toasts:[]};function d(e){c=n(c,e),o.forEach(e=>{e(c)})}function m(e){let{...a}=e,i=(s=(s+1)%Number.MAX_VALUE).toString(),t=()=>d({type:"DISMISS_TOAST",toastId:i});return d({type:"ADD_TOAST",toast:{...a,id:i,open:!0,onOpenChange:e=>{e||t()}}}),{id:i,dismiss:t,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:i}})}}function x(){let[e,a]=(0,t.useState)(c);return(0,t.useEffect)(()=>(o.push(a),()=>{let e=o.indexOf(a);e>-1&&o.splice(e,1)}),[]),{...e,toast:m,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},88539:(e,a,i)=>{"use strict";i.d(a,{T:()=>r});var t=i(95155),s=i(12115),l=i(59434);let r=s.forwardRef((e,a)=>{let{className:i,...s}=e;return(0,t.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",i),ref:a,...s})});r.displayName="Textarea"}},e=>{var a=a=>e(e.s=a);e.O(0,[3464,3455,5585,546,9786,6112,1041,283,1642,8441,1684,7358],()=>a(34428)),_N_E=e.O()}]);