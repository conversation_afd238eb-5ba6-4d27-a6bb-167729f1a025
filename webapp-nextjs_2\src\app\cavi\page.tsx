'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useAuth } from '@/contexts/AuthContext'
import { caviApi } from '@/lib/api'
import { Cavo } from '@/types'
import CaviTable from '@/components/cavi/CaviTable'
import CaviStatistics from '@/components/cavi/CaviStatistics'
import InserisciMetriDialog from '@/components/cavi/InserisciMetriDialog'
import ModificaBobinaDialog from '@/components/cavi/ModificaBobinaDialog'
import CollegamentiDialog from '@/components/cavi/CollegamentiDialog'
import CertificazioneDialog from '@/components/cavi/CertificazioneDialog'
import CreaComandaDialog from '@/components/cavi/CreaComandaDialog'
import ImportExcelDialog from '@/components/cavi/ImportExcelDialog'
import ExportDataDialog from '@/components/cavi/ExportDataDialog'
import AggiungiCavoDialog from '@/components/cavi/AggiungiCavoDialog'
import ModificaCavoDialog from '@/components/cavi/ModificaCavoDialog'
import EliminaCavoDialog from '@/components/cavi/EliminaCavoDialog'
import { PDFGenerationConfig } from '@/components/cavi/CaviActionDialogs'
import { useToastActions } from '@/components/ui/toast-notification'
// import { useToast } from '@/hooks/use-toast'
import {
  Package,
  AlertCircle,
  Loader2
} from 'lucide-react'

interface DashboardStats {
  totali: number
  installati: number
  collegati: number
  certificati: number
  percentualeInstallazione: number
  percentualeCollegamento: number
  percentualeCertificazione: number
  metriTotali: number
  metriInstallati: number
  metriCollegati: number
  metriCertificati: number
}

export default function CaviPage() {
  const { user, cantiere, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const toast = useToastActions()




  const [cavi, setCavi] = useState<Cavo[]>([])
  const [caviSpare, setCaviSpare] = useState<Cavo[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [selectedCavi, setSelectedCavi] = useState<string[]>([])
  const [selectionEnabled, setSelectionEnabled] = useState(false)

  const [filteredCavi, setFilteredCavi] = useState<Cavo[]>([])
  const [revisioneCorrente, setRevisioneCorrente] = useState<string>('')



  // Update filtered cavi when main cavi change
  useEffect(() => {
    let filtered = cavi

    // Filtro per cantiere
    if (cantiere) {
      filtered = filtered.filter(cavo => cavo.cantiere === cantiere.nome)
    }

    setFilteredCavi(filtered)
  }, [cavi, cantiere])

  // Stati per i dialoghi
  const [inserisciMetriDialog, setInserisciMetriDialog] = useState<{
    open: boolean
    cavo: Cavo | null
  }>({ open: false, cavo: null })

  const [modificaBobinaDialog, setModificaBobinaDialog] = useState<{
    open: boolean
    cavo: Cavo | null
  }>({ open: false, cavo: null })

  const [collegamentiDialog, setCollegamentiDialog] = useState<{
    open: boolean
    cavo: Cavo | null
  }>({ open: false, cavo: null })

  const [certificazioneDialog, setCertificazioneDialog] = useState<{
    open: boolean
    cavo: Cavo | null
  }>({ open: false, cavo: null })

  const [creaComandaDialog, setCreaComandaDialog] = useState<{
    open: boolean
    tipoComanda?: 'POSA' | 'COLLEGAMENTO_PARTENZA' | 'COLLEGAMENTO_ARRIVO' | 'CERTIFICAZIONE'
  }>({ open: false })

  const [importExcelDialog, setImportExcelDialog] = useState<{
    open: boolean
    tipo?: 'cavi' | 'bobine'
  }>({ open: false })

  const [exportDataDialog, setExportDataDialog] = useState(false)

  // Stati per i nuovi dialog di gestione cavi
  const [aggiungiCavoDialog, setAggiungiCavoDialog] = useState(false)

  const [modificaCavoDialog, setModificaCavoDialog] = useState<{
    open: boolean
    cavo: Cavo | null
  }>({ open: false, cavo: null })

  const [eliminaCavoDialog, setEliminaCavoDialog] = useState<{
    open: boolean
    cavo: Cavo | null
  }>({ open: false, cavo: null })

  const [stats, setStats] = useState<DashboardStats>({
    totali: 0,
    installati: 0,
    collegati: 0,
    certificati: 0,
    percentualeInstallazione: 0,
    percentualeCollegamento: 0,
    percentualeCertificazione: 0,
    metriTotali: 0,
    metriInstallati: 0,
    metriCollegati: 0,
    metriCertificati: 0
  })

  // Get cantiere ID
  const [cantiereId, setCantiereId] = useState<number>(0)

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedId = cantiere?.id_cantiere || parseInt(localStorage.getItem('selectedCantiereId') || '0')
      setCantiereId(storedId)
    }
  }, [cantiere])

  // Crea oggetto cantiere per il dialog
  const cantiereForDialog = cantiere || (cantiereId > 0 ? {
    id_cantiere: cantiereId,
    commessa: `Cantiere ${cantiereId}`
  } : null)

  // Carica i cavi dal backend
  useEffect(() => {
    if (cantiereId && cantiereId > 0) {
      loadCavi()
      loadRevisioneCorrente()
    }
  }, [cantiereId])

  const loadRevisioneCorrente = async () => {
    try {
      const response = await fetch(`http://localhost:8001/api/cavi/${cantiereId}/revisione-corrente`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        setRevisioneCorrente(data.revisione_corrente || '00')
      } else {
        setRevisioneCorrente('00')
      }
    } catch (error) {
      setRevisioneCorrente('00')
    }
  }

  const loadCavi = async () => {
    try {
      setLoading(true)
      setError('')

      // Prima prova con l'API normale
      try {
        const data = await caviApi.getCavi(cantiereId)

        // Separa cavi attivi e spare
        const caviAttivi = data.filter((cavo: Cavo) => !cavo.spare)
        const caviSpareFiltered = data.filter((cavo: Cavo) => cavo.spare)

        setCavi(caviAttivi)
        setCaviSpare(caviSpareFiltered)

        // Calcola statistiche
        calculateStats(caviAttivi)

      } catch (apiError: any) {

        // Fallback: prova con endpoint debug (senza autenticazione)
        try {
          const response = await fetch(`http://localhost:8001/api/cavi/debug/${cantiereId}`)
          const debugData = await response.json()

          if (debugData.cavi && Array.isArray(debugData.cavi)) {
            const caviAttivi = debugData.cavi.filter((cavo: any) => !cavo.spare)
            const caviSpareFiltered = debugData.cavi.filter((cavo: any) => cavo.spare)

            setCavi(caviAttivi)
            setCaviSpare(caviSpareFiltered)
            calculateStats(caviAttivi)

            setError('⚠️ Dati caricati tramite endpoint debug (problema autenticazione)')
          } else {
            throw new Error('Formato dati debug non valido')
          }
        } catch (debugError) {
          throw apiError // Rilancia l'errore originale
        }
      }

    } catch (error: any) {
      setError(`Errore nel caricamento dei cavi: ${error.response?.data?.detail || error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const calculateStats = (caviData: Cavo[]) => {
    const totali = caviData.length
    const installati = caviData.filter(c => (c.metri_posati || c.metratura_reale || 0) > 0).length
    const collegati = caviData.filter(c => (c.collegamento || c.collegamenti) === 3).length // 3 = collegato
    const certificati = caviData.filter(c => c.certificato).length

    const metriTotali = caviData.reduce((sum, c) => sum + (c.metri_teorici || 0), 0)
    const metriInstallati = caviData.reduce((sum, c) => sum + (c.metri_posati || 0), 0)
    const metriCollegati = caviData.filter(c => c.collegamento === 3).reduce((sum, c) => sum + (c.metri_posati || 0), 0)
    const metriCertificati = caviData.filter(c => c.certificato).reduce((sum, c) => sum + (c.metri_posati || 0), 0)

    setStats({
      totali,
      installati,
      collegati,
      certificati,
      percentualeInstallazione: totali > 0 ? Math.round((installati / totali) * 100) : 0,
      percentualeCollegamento: totali > 0 ? Math.round((collegati / totali) * 100) : 0,
      percentualeCertificazione: totali > 0 ? Math.round((certificati / totali) * 100) : 0,
      metriTotali,
      metriInstallati,
      metriCollegati,
      metriCertificati
    })
  }

  // Nuove funzioni per gestire i popup avanzati
  const handleDisconnectCable = async (cavoId: string) => {
    try {
      // Simula API call per scollegamento
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Aggiorna lo stato locale
      setCavi(prev => prev.map(c =>
        c.id_cavo === cavoId
          ? { ...c, collegamento: 0, collegamenti: 0 }
          : c
      ))

      // Ricarica i dati
      await loadCavi()

      toast.success('Cavo Scollegato', `Il cavo ${cavoId} è stato scollegato con successo.`)
    } catch (error) {
      toast.error('Errore Scollegamento', 'Impossibile scollegare il cavo. Riprova.')
      throw error
    }
  }

  const handleGeneratePDF = async (config: PDFGenerationConfig) => {
    try {
      // Simula generazione PDF
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Simula download del file
      const blob = new Blob(['PDF Content'], { type: 'application/pdf' })
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = config.fileName
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      toast.success('PDF Generato', `Certificato per il cavo ${config.cavoId} generato con successo.`)
    } catch (error) {
      toast.error('Errore Generazione PDF', 'Impossibile generare il certificato. Riprova.')
      throw error
    }
  }

  const handleCertifyCable = async (cavoId: string) => {
    try {
      // Simula processo di certificazione
      await new Promise(resolve => setTimeout(resolve, 1500))

      // Aggiorna lo stato locale
      setCavi(prev => prev.map(c =>
        c.id_cavo === cavoId
          ? { ...c, certificato: true, data_certificazione: new Date().toISOString() }
          : c
      ))

      // Ricarica i dati
      await loadCavi()

      toast.success('Cavo Certificato', `Il cavo ${cavoId} è stato certificato con successo.`)
    } catch (error) {
      toast.error('Errore Certificazione', 'Impossibile certificare il cavo. Riprova.')
      throw error
    }
  }

  // Gestione azioni sui cavi
  const handleStatusAction = (cavo: Cavo, action: string, label?: string) => {

    switch (action) {
      case 'insert_meters':
        setInserisciMetriDialog({ open: true, cavo })
        break
      case 'modify_reel':
        setModificaBobinaDialog({ open: true, cavo })
        break
      case 'view_command':
        toast({
          title: "Visualizza Comanda",
          description: `Apertura comanda ${label} per cavo ${cavo.id_cavo}`,
        })
        break
      case 'connect_cable':
      case 'connect_arrival':
      case 'connect_departure':
      case 'disconnect_cable':
      case 'manage_connections':
        setCollegamentiDialog({ open: true, cavo })
        break
      case 'create_certificate':
      case 'generate_pdf':
        setCertificazioneDialog({ open: true, cavo })
        break
    }
  }

  const handleContextMenuAction = (cavo: Cavo, action: string) => {

    switch (action) {
      case 'view_details':
        toast({
          title: "Visualizza Dettagli",
          description: `Apertura dettagli per cavo ${cavo.id_cavo}`,
        })
        break
      case 'edit':
        setModificaCavoDialog({ open: true, cavo })
        break
      case 'delete':
        setEliminaCavoDialog({ open: true, cavo })
        break
      case 'add_new':
        setAggiungiCavoDialog(true)
        break
      case 'select':
        const isSelected = selectedCavi.includes(cavo.id_cavo)
        if (isSelected) {
          setSelectedCavi(selectedCavi.filter(id => id !== cavo.id_cavo))
          toast({
            title: "Cavo Deselezionato",
            description: `Cavo ${cavo.id_cavo} deselezionato`,
          })
        } else {
          setSelectedCavi([...selectedCavi, cavo.id_cavo])
          toast({
            title: "Cavo Selezionato",
            description: `Cavo ${cavo.id_cavo} selezionato`,
          })
        }
        break
      case 'copy_id':
        navigator.clipboard.writeText(cavo.id_cavo)
        toast({
          title: "ID Copiato",
          description: `ID cavo ${cavo.id_cavo} copiato negli appunti`,
        })
        break
      case 'copy_details':
        const details = `ID: ${cavo.id_cavo}, Tipologia: ${cavo.tipologia}, Formazione: ${cavo.formazione || cavo.sezione}, Metri: ${cavo.metri_teorici}`
        navigator.clipboard.writeText(details)
        toast({
          title: "Dettagli Copiati",
          description: "Dettagli cavo copiati negli appunti",
        })
        break
      case 'add_to_command':
        toast({
          title: "Aggiungi a Comanda",
          description: "Funzione aggiunta a comanda in sviluppo",
        })
        break
      case 'remove_from_command':
        toast({
          title: "Rimuovi da Comanda",
          description: "Funzione rimozione da comanda in sviluppo",
        })
        break
      case 'create_command_posa':
        setCreaComandaDialog({ open: true, tipoComanda: 'POSA' })
        break
      case 'create_command_collegamento_partenza':
        setCreaComandaDialog({ open: true, tipoComanda: 'COLLEGAMENTO_PARTENZA' })
        break
      case 'create_command_collegamento_arrivo':
        setCreaComandaDialog({ open: true, tipoComanda: 'COLLEGAMENTO_ARRIVO' })
        break
      case 'create_command_certificazione':
        setCreaComandaDialog({ open: true, tipoComanda: 'CERTIFICAZIONE' })
        break
      case 'add_multiple_to_command':
        toast({
          title: "Aggiungi Tutti a Comanda",
          description: "Funzione aggiunta multipla a comanda in sviluppo",
        })
        break
      case 'remove_multiple_from_commands':
        toast({
          title: "Rimuovi Tutti dalle Comande",
          description: "Funzione rimozione multipla dalle comande in sviluppo",
        })
        break
      default:
        toast({
          title: "Azione non implementata",
          description: `Azione ${action} non ancora implementata`,
        })
        break
    }
  }

  // Gestione successo/errore dialoghi
  const handleDialogSuccess = (message: string) => {
    toast({
      title: "Operazione completata",
      description: message,
    })
    // Ricarica i dati
    loadCavi()
  }

  const handleDialogError = (message: string) => {
    toast({
      title: "Errore",
      description: message,
      variant: "destructive"
    })
  }

  if (isLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!cantiereId) {
    return (
      <div className="max-w-[90%] mx-auto p-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Nessun cantiere selezionato. Seleziona un cantiere per visualizzare i cavi.
          </AlertDescription>
        </Alert>

      </div>
    )
  }

  if (error) {
    return (
      <div className="max-w-[90%] mx-auto p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <Button onClick={loadCavi} className="mt-4">
          Riprova
        </Button>
      </div>
    )
  }

  return (
    <div className="max-w-[90%] mx-auto p-6">

      {/* Statistics */}
      <CaviStatistics
        cavi={cavi}
        filteredCavi={filteredCavi}
        revisioneCorrente={revisioneCorrente}
        className="mb-2"
      />

      {/* Tabella Cavi Attivi */}
      <div className="mb-8">
        <CaviTable
          cavi={cavi}
          loading={loading}
          selectionEnabled={selectionEnabled}
          selectedCavi={selectedCavi}
          onSelectionChange={setSelectedCavi}
          onStatusAction={handleStatusAction}
          onContextMenuAction={handleContextMenuAction}
          onDisconnectCable={handleDisconnectCable}
          onGeneratePDF={handleGeneratePDF}
          onCertifyCable={handleCertifyCable}
        />
      </div>

      {/* Tabella Cavi Spare */}
      {caviSpare.length > 0 && (
        <div className="mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Package className="h-5 w-5" />
                <span>Cavi Spare ({caviSpare.length})</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <CaviTable
                cavi={caviSpare}
                loading={loading}
                selectionEnabled={false}
                onStatusAction={handleStatusAction}
                onContextMenuAction={handleContextMenuAction}
                onDisconnectCable={handleDisconnectCable}
                onGeneratePDF={handleGeneratePDF}
                onCertifyCable={handleCertifyCable}
              />
            </CardContent>
          </Card>
        </div>
      )}



      {/* Dialoghi */}
      <InserisciMetriDialog
        open={inserisciMetriDialog.open}
        onClose={() => setInserisciMetriDialog({ open: false, cavo: null })}
        cavo={inserisciMetriDialog.cavo}
        cantiere={cantiereForDialog}
        onSuccess={handleDialogSuccess}
        onError={handleDialogError}
      />

      <ModificaBobinaDialog
        open={modificaBobinaDialog.open}
        onClose={() => setModificaBobinaDialog({ open: false, cavo: null })}
        cavo={modificaBobinaDialog.cavo}
        cantiere={cantiereForDialog}
        onSuccess={handleDialogSuccess}
        onError={handleDialogError}
      />

      <CollegamentiDialog
        open={collegamentiDialog.open}
        onClose={() => setCollegamentiDialog({ open: false, cavo: null })}
        cavo={collegamentiDialog.cavo}
        onSuccess={handleDialogSuccess}
        onError={handleDialogError}
      />

      <CertificazioneDialog
        open={certificazioneDialog.open}
        onClose={() => setCertificazioneDialog({ open: false, cavo: null })}
        cavo={certificazioneDialog.cavo}
        onSuccess={handleDialogSuccess}
        onError={handleDialogError}
      />

      <CreaComandaDialog
        open={creaComandaDialog.open}
        onClose={() => setCreaComandaDialog({ open: false })}
        caviSelezionati={selectedCavi}
        tipoComanda={creaComandaDialog.tipoComanda}
        onSuccess={handleDialogSuccess}
        onError={handleDialogError}
      />

      <ImportExcelDialog
        open={importExcelDialog.open}
        onClose={() => setImportExcelDialog({ open: false })}
        tipo={importExcelDialog.tipo || 'cavi'}
        onSuccess={handleDialogSuccess}
        onError={handleDialogError}
      />

      <ExportDataDialog
        open={exportDataDialog}
        onClose={() => setExportDataDialog(false)}
        onSuccess={handleDialogSuccess}
        onError={handleDialogError}
      />

      {/* Dialog per gestione cavi */}
      <AggiungiCavoDialog
        open={aggiungiCavoDialog}
        onClose={() => setAggiungiCavoDialog(false)}
        cantiere={cantiere}
        onSuccess={(message) => {
          handleDialogSuccess(message)
          loadCavi() // Ricarica la lista cavi
        }}
        onError={handleDialogError}
      />

      <ModificaCavoDialog
        open={modificaCavoDialog.open}
        onClose={() => setModificaCavoDialog({ open: false, cavo: null })}
        cavo={modificaCavoDialog.cavo}
        cantiere={cantiere}
        onSuccess={(message) => {
          handleDialogSuccess(message)
          loadCavi() // Ricarica la lista cavi
        }}
        onError={handleDialogError}
      />

      <EliminaCavoDialog
        open={eliminaCavoDialog.open}
        onClose={() => setEliminaCavoDialog({ open: false, cavo: null })}
        cavo={eliminaCavoDialog.cavo}
        cantiere={cantiere}
        onSuccess={(message) => {
          handleDialogSuccess(message)
          loadCavi() // Ricarica la lista cavi
        }}
        onError={handleDialogError}
      />
    </div>
  )
}
