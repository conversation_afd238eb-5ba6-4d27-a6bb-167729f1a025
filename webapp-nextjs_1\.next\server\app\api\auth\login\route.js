(()=>{var e={};e.id=7758,e.ids=[7758],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29959:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>u,serverHooks:()=>c,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{POST:()=>p});var a=r(96559),o=r(48088),n=r(37719),i=r(32190);async function p(e){try{let t,r,s=e.headers.get("content-type");if(s?.includes("application/json")){let s=await e.json();t=s.username,r=s.password}else{let s=await e.formData();t=s.get("username"),r=s.get("password")}if(!t||!r)return i.NextResponse.json({detail:"Username e password sono richiesti"},{status:400});let a="http://localhost:8001",o=new FormData;o.append("username",t),o.append("password",r),console.log("\uD83D\uDD04 Auth API: Proxying login request to backend:",`${a}/api/auth/login`);let n=await fetch(`${a}/api/auth/login`,{method:"POST",body:o});console.log("\uD83D\uDCE1 Auth API: Backend response status:",n.status);let p=await n.json();return console.log("\uD83D\uDCE1 Auth API: Backend response data:",p),i.NextResponse.json(p,{status:n.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("❌ Auth API: Login error:",e),i.NextResponse.json({detail:"Errore interno del server"},{status:500})}}let u=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/login/route",pathname:"/api/auth/login",filename:"route",bundlePath:"app/api/auth/login/route"},resolvedPagePath:"C:\\CMS\\webapp-nextjs_1\\src\\app\\api\\auth\\login\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:c}=u;function h(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(29959));module.exports=s})();