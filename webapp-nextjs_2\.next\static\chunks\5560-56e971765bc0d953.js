"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5560],{1243:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},2564:(e,t,n)=>{n.d(t,{Qg:()=>l,bL:()=>f});var r=n(12115),i=n(63655),o=n(95155),l=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a=r.forwardRef((e,t)=>(0,o.jsx)(i.sG.span,{...e,ref:t,style:{...l,...e.style}}));a.displayName="VisuallyHidden";var f=a},3493:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("cable",[["path",{d:"M17 21v-2a1 1 0 0 1-1-1v-1a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v1a1 1 0 0 1-1 1",key:"10bnsj"}],["path",{d:"M19 15V6.5a1 1 0 0 0-7 0v11a1 1 0 0 1-7 0V9",key:"1eqmu1"}],["path",{d:"M21 21v-2h-4",key:"14zm7j"}],["path",{d:"M3 5h4V3",key:"z442eg"}],["path",{d:"M7 5a1 1 0 0 1 1 1v1a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a1 1 0 0 1 1-1V3",key:"ebdjd7"}]])},11275:(e,t,n)=>{n.d(t,{X:()=>o});var r=n(12115),i=n(52712);function o(e){let[t,n]=r.useState(void 0);return(0,i.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,i=t.blockSize}else r=e.offsetWidth,i=e.offsetHeight;n({width:r,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},14186:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},35152:(e,t,n)=>{n.d(t,{Mz:()=>eU,i3:()=>eK,UC:()=>eJ,bL:()=>eQ,Bk:()=>eM});var r=n(12115);let i=["top","right","bottom","left"],o=Math.min,l=Math.max,a=Math.round,f=Math.floor,s=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},u={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function g(e){return"y"===e?"height":"width"}function y(e){return["top","bottom"].includes(p(e))?"y":"x"}function w(e){return e.replace(/start|end/g,e=>u[e])}function v(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function x(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function b(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function A(e,t,n){let r,{reference:i,floating:o}=e,l=y(t),a=m(y(t)),f=g(a),s=p(t),c="y"===l,u=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,w=i[f]/2-o[f]/2;switch(s){case"top":r={x:u,y:i.y-o.height};break;case"bottom":r={x:u,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-o.width,y:d};break;default:r={x:i.x,y:i.y}}switch(h(t)){case"start":r[a]-=w*(n&&c?-1:1);break;case"end":r[a]+=w*(n&&c?-1:1)}return r}let R=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,a=o.filter(Boolean),f=await (null==l.isRTL?void 0:l.isRTL(t)),s=await l.getElementRects({reference:e,floating:t,strategy:i}),{x:c,y:u}=A(s,r,f),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:o,fn:m}=a[n],{x:g,y:y,data:w,reset:v}=await m({x:c,y:u,initialPlacement:r,placement:d,strategy:i,middlewareData:p,rects:s,platform:l,elements:{reference:e,floating:t}});c=null!=g?g:c,u=null!=y?y:u,p={...p,[o]:{...p[o],...w}},v&&h<=50&&(h++,"object"==typeof v&&(v.placement&&(d=v.placement),v.rects&&(s=!0===v.rects?await l.getElementRects({reference:e,floating:t,strategy:i}):v.rects),{x:c,y:u}=A(s,d,f)),n=-1)}return{x:c,y:u,placement:d,strategy:i,middlewareData:p}};async function k(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:o,rects:l,elements:a,strategy:f}=e,{boundary:s="clippingAncestors",rootBoundary:c="viewport",elementContext:u="floating",altBoundary:p=!1,padding:h=0}=d(t,e),m=x(h),g=a[p?"floating"===u?"reference":"floating":u],y=b(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(g)))||n?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:s,rootBoundary:c,strategy:f})),w="floating"===u?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,v=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),A=await (null==o.isElement?void 0:o.isElement(v))&&await (null==o.getScale?void 0:o.getScale(v))||{x:1,y:1},R=b(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:w,offsetParent:v,strategy:f}):w);return{top:(y.top-R.top+m.top)/A.y,bottom:(R.bottom-y.bottom+m.bottom)/A.y,left:(y.left-R.left+m.left)/A.x,right:(R.right-y.right+m.right)/A.x}}function L(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function S(e){return i.some(t=>e[t]>=0)}async function T(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=p(n),a=h(n),f="y"===y(n),s=["left","top"].includes(l)?-1:1,c=o&&f?-1:1,u=d(t,e),{mainAxis:m,crossAxis:g,alignmentAxis:w}="number"==typeof u?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return a&&"number"==typeof w&&(g="end"===a?-1*w:w),f?{x:g*c,y:m*s}:{x:m*s,y:g*c}}function E(){return"undefined"!=typeof window}function O(e){return H(e)?(e.nodeName||"").toLowerCase():"#document"}function C(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function P(e){var t;return null==(t=(H(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function H(e){return!!E()&&(e instanceof Node||e instanceof C(e).Node)}function M(e){return!!E()&&(e instanceof Element||e instanceof C(e).Element)}function j(e){return!!E()&&(e instanceof HTMLElement||e instanceof C(e).HTMLElement)}function D(e){return!!E()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof C(e).ShadowRoot)}function z(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=B(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function N(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function F(e){let t=V(),n=M(e)?B(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function V(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function W(e){return["html","body","#document"].includes(O(e))}function B(e){return C(e).getComputedStyle(e)}function _(e){return M(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function X(e){if("html"===O(e))return e;let t=e.assignedSlot||e.parentNode||D(e)&&e.host||P(e);return D(t)?t.host:t}function q(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=X(t);return W(n)?t.ownerDocument?t.ownerDocument.body:t.body:j(n)&&z(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),l=C(i);if(o){let e=I(l);return t.concat(l,l.visualViewport||[],z(i)?i:[],e&&n?q(e):[])}return t.concat(i,q(i,[],n))}function I(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Y(e){let t=B(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=j(e),o=i?e.offsetWidth:n,l=i?e.offsetHeight:r,f=a(n)!==o||a(r)!==l;return f&&(n=o,r=l),{width:n,height:r,$:f}}function G(e){return M(e)?e:e.contextElement}function $(e){let t=G(e);if(!j(t))return s(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=Y(t),l=(o?a(n.width):n.width)/r,f=(o?a(n.height):n.height)/i;return l&&Number.isFinite(l)||(l=1),f&&Number.isFinite(f)||(f=1),{x:l,y:f}}let Q=s(0);function U(e){let t=C(e);return V()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:Q}function J(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),l=G(e),a=s(1);t&&(r?M(r)&&(a=$(r)):a=$(e));let f=(void 0===(i=n)&&(i=!1),r&&(!i||r===C(l))&&i)?U(l):s(0),c=(o.left+f.x)/a.x,u=(o.top+f.y)/a.y,d=o.width/a.x,p=o.height/a.y;if(l){let e=C(l),t=r&&M(r)?C(r):r,n=e,i=I(n);for(;i&&r&&t!==n;){let e=$(i),t=i.getBoundingClientRect(),r=B(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,u*=e.y,d*=e.x,p*=e.y,c+=o,u+=l,i=I(n=C(i))}}return b({width:d,height:p,x:c,y:u})}function K(e,t){let n=_(e).scrollLeft;return t?t.left+n:J(P(e)).left+n}function Z(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:K(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=C(e),r=P(e),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,a=0,f=0;if(i){o=i.width,l=i.height;let e=V();(!e||e&&"fixed"===t)&&(a=i.offsetLeft,f=i.offsetTop)}return{width:o,height:l,x:a,y:f}}(e,n);else if("document"===t)r=function(e){let t=P(e),n=_(e),r=e.ownerDocument.body,i=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+K(e),f=-n.scrollTop;return"rtl"===B(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:a,y:f}}(P(e));else if(M(t))r=function(e,t){let n=J(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=j(e)?$(e):s(1),l=e.clientWidth*o.x,a=e.clientHeight*o.y;return{width:l,height:a,x:i*o.x,y:r*o.y}}(t,n);else{let n=U(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return b(r)}function et(e){return"static"===B(e).position}function en(e,t){if(!j(e)||"fixed"===B(e).position)return null;if(t)return t(e);let n=e.offsetParent;return P(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=C(e);if(N(e))return n;if(!j(e)){let t=X(e);for(;t&&!W(t);){if(M(t)&&!et(t))return t;t=X(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(O(r))&&et(r);)r=en(r,t);return r&&W(r)&&et(r)&&!F(r)?n:r||function(e){let t=X(e);for(;j(t)&&!W(t);){if(F(t))return t;if(N(t))break;t=X(t)}return null}(e)||n}let ei=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=j(t),i=P(t),o="fixed"===n,l=J(e,!0,o,t),a={scrollLeft:0,scrollTop:0},f=s(0);if(r||!r&&!o)if(("body"!==O(t)||z(i))&&(a=_(t)),r){let e=J(t,!0,o,t);f.x=e.x+t.clientLeft,f.y=e.y+t.clientTop}else i&&(f.x=K(i));o&&!r&&i&&(f.x=K(i));let c=!i||r||o?s(0):Z(i,a);return{x:l.left+a.scrollLeft-f.x-c.x,y:l.top+a.scrollTop-f.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eo={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,l=P(r),a=!!t&&N(t.floating);if(r===l||a&&o)return n;let f={scrollLeft:0,scrollTop:0},c=s(1),u=s(0),d=j(r);if((d||!d&&!o)&&(("body"!==O(r)||z(l))&&(f=_(r)),j(r))){let e=J(r);c=$(r),u.x=e.x+r.clientLeft,u.y=e.y+r.clientTop}let p=!l||d||o?s(0):Z(l,f,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-f.scrollLeft*c.x+u.x+p.x,y:n.y*c.y-f.scrollTop*c.y+u.y+p.y}},getDocumentElement:P,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,a=[..."clippingAncestors"===n?N(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=q(e,[],!1).filter(e=>M(e)&&"body"!==O(e)),i=null,o="fixed"===B(e).position,l=o?X(e):e;for(;M(l)&&!W(l);){let t=B(l),n=F(l);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&["absolute","fixed"].includes(i.position)||z(l)&&!n&&function e(t,n){let r=X(t);return!(r===n||!M(r)||W(r))&&("fixed"===B(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):i=t,l=X(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],f=a[0],s=a.reduce((e,n)=>{let r=ee(t,n,i);return e.top=l(r.top,e.top),e.right=o(r.right,e.right),e.bottom=o(r.bottom,e.bottom),e.left=l(r.left,e.left),e},ee(t,f,i));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:er,getElementRects:ei,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=Y(e);return{width:t,height:n}},getScale:$,isElement:M,isRTL:function(e){return"rtl"===B(e).direction}};function el(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ea=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:a,platform:f,elements:s,middlewareData:c}=t,{element:u,padding:p=0}=d(e,t)||{};if(null==u)return{};let w=x(p),v={x:n,y:r},b=m(y(i)),A=g(b),R=await f.getDimensions(u),k="y"===b,L=k?"clientHeight":"clientWidth",S=a.reference[A]+a.reference[b]-v[b]-a.floating[A],T=v[b]-a.reference[b],E=await (null==f.getOffsetParent?void 0:f.getOffsetParent(u)),O=E?E[L]:0;O&&await (null==f.isElement?void 0:f.isElement(E))||(O=s.floating[L]||a.floating[A]);let C=O/2-R[A]/2-1,P=o(w[k?"top":"left"],C),H=o(w[k?"bottom":"right"],C),M=O-R[A]-H,j=O/2-R[A]/2+(S/2-T/2),D=l(P,o(j,M)),z=!c.arrow&&null!=h(i)&&j!==D&&a.reference[A]/2-(j<P?P:H)-R[A]/2<0,N=z?j<P?j-P:j-M:0;return{[b]:v[b]+N,data:{[b]:D,centerOffset:j-D-N,...z&&{alignmentOffset:N}},reset:z}}}),ef=(e,t,n)=>{let r=new Map,i={platform:eo,...n},o={...i.platform,_c:r};return R(e,t,{...i,platform:o})};var es=n(47650),ec="undefined"!=typeof document?r.useLayoutEffect:function(){};function eu(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eu(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!eu(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ed(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ed(e);return Math.round(t*n)/n}function eh(e){let t=r.useRef(e);return ec(()=>{t.current=e}),t}let em=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ea({element:n.current,padding:r}).fn(t):{}:n?ea({element:n,padding:r}).fn(t):{}}}),eg=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:i,y:o,placement:l,middlewareData:a}=t,f=await T(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+f.x,y:o+f.y,data:{...f,placement:l}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:i}=t,{mainAxis:a=!0,crossAxis:f=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=d(e,t),u={x:n,y:r},h=await k(t,c),g=y(p(i)),w=m(g),v=u[w],x=u[g];if(a){let e="y"===w?"top":"left",t="y"===w?"bottom":"right",n=v+h[e],r=v-h[t];v=l(n,o(v,r))}if(f){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=x+h[e],r=x-h[t];x=l(n,o(x,r))}let b=s.fn({...t,[w]:v,[g]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[w]:a,[g]:f}}}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:i,rects:o,middlewareData:l}=t,{offset:a=0,mainAxis:f=!0,crossAxis:s=!0}=d(e,t),c={x:n,y:r},u=y(i),h=m(u),g=c[h],w=c[u],v=d(a,t),x="number"==typeof v?{mainAxis:v,crossAxis:0}:{mainAxis:0,crossAxis:0,...v};if(f){let e="y"===h?"height":"width",t=o.reference[h]-o.floating[e]+x.mainAxis,n=o.reference[h]+o.reference[e]-x.mainAxis;g<t?g=t:g>n&&(g=n)}if(s){var b,A;let e="y"===h?"width":"height",t=["top","left"].includes(p(i)),n=o.reference[u]-o.floating[e]+(t&&(null==(b=l.offset)?void 0:b[u])||0)+(t?0:x.crossAxis),r=o.reference[u]+o.reference[e]+(t?0:(null==(A=l.offset)?void 0:A[u])||0)-(t?x.crossAxis:0);w<n?w=n:w>r&&(w=r)}return{[h]:g,[u]:w}}}}(e),options:[e,t]}),ev=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,i,o,l;let{placement:a,middlewareData:f,rects:s,initialPlacement:c,platform:u,elements:x}=t,{mainAxis:b=!0,crossAxis:A=!0,fallbackPlacements:R,fallbackStrategy:L="bestFit",fallbackAxisSideDirection:S="none",flipAlignment:T=!0,...E}=d(e,t);if(null!=(n=f.arrow)&&n.alignmentOffset)return{};let O=p(a),C=y(c),P=p(c)===c,H=await (null==u.isRTL?void 0:u.isRTL(x.floating)),M=R||(P||!T?[v(c)]:function(e){let t=v(e);return[w(e),t,w(t)]}(c)),j="none"!==S;!R&&j&&M.push(...function(e,t,n,r){let i=h(e),o=function(e,t,n){let r=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":if(n)return t?i:r;return t?r:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(w)))),o}(c,T,S,H));let D=[c,...M],z=await k(t,E),N=[],F=(null==(r=f.flip)?void 0:r.overflows)||[];if(b&&N.push(z[O]),A){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),i=m(y(e)),o=g(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(l=v(l)),[l,v(l)]}(a,s,H);N.push(z[e[0]],z[e[1]])}if(F=[...F,{placement:a,overflows:N}],!N.every(e=>e<=0)){let e=((null==(i=f.flip)?void 0:i.index)||0)+1,t=D[e];if(t&&("alignment"!==A||C===y(t)||F.every(e=>e.overflows[0]>0&&y(e.placement)===C)))return{data:{index:e,overflows:F},reset:{placement:t}};let n=null==(o=F.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(L){case"bestFit":{let e=null==(l=F.filter(e=>{if(j){let t=y(e.placement);return t===C||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let i,a,{placement:f,rects:s,platform:c,elements:u}=t,{apply:m=()=>{},...g}=d(e,t),w=await k(t,g),v=p(f),x=h(f),b="y"===y(f),{width:A,height:R}=s.floating;"top"===v||"bottom"===v?(i=v,a=x===(await (null==c.isRTL?void 0:c.isRTL(u.floating))?"start":"end")?"left":"right"):(a=v,i="end"===x?"top":"bottom");let L=R-w.top-w.bottom,S=A-w.left-w.right,T=o(R-w[i],L),E=o(A-w[a],S),O=!t.middlewareData.shift,C=T,P=E;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(P=S),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(C=L),O&&!x){let e=l(w.left,0),t=l(w.right,0),n=l(w.top,0),r=l(w.bottom,0);b?P=A-2*(0!==e||0!==t?e+t:l(w.left,w.right)):C=R-2*(0!==n||0!==r?n+r:l(w.top,w.bottom))}await m({...t,availableWidth:P,availableHeight:C});let H=await c.getDimensions(u.floating);return A!==H.width||R!==H.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...i}=d(e,t);switch(r){case"referenceHidden":{let e=L(await k(t,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:S(e)}}}case"escaped":{let e=L(await k(t,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:S(e)}}}default:return{}}}}}(e),options:[e,t]}),eA=(e,t)=>({...em(e),options:[e,t]});var eR=n(63655),ek=n(95155),eL=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:i=5,...o}=e;return(0,ek.jsx)(eR.sG.svg,{...o,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,ek.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eL.displayName="Arrow";var eS=n(6101),eT=n(46081),eE=n(39033),eO=n(52712),eC=n(11275),eP="Popper",[eH,eM]=(0,eT.A)(eP),[ej,eD]=eH(eP),ez=e=>{let{__scopePopper:t,children:n}=e,[i,o]=r.useState(null);return(0,ek.jsx)(ej,{scope:t,anchor:i,onAnchorChange:o,children:n})};ez.displayName=eP;var eN="PopperAnchor",eF=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...o}=e,l=eD(eN,n),a=r.useRef(null),f=(0,eS.s)(t,a);return r.useEffect(()=>{l.onAnchorChange((null==i?void 0:i.current)||a.current)}),i?null:(0,ek.jsx)(eR.sG.div,{...o,ref:f})});eF.displayName=eN;var eV="PopperContent",[eW,eB]=eH(eV),e_=r.forwardRef((e,t)=>{var n,i,a,s,c,u,d,p;let{__scopePopper:h,side:m="bottom",sideOffset:g=0,align:y="center",alignOffset:w=0,arrowPadding:v=0,avoidCollisions:x=!0,collisionBoundary:b=[],collisionPadding:A=0,sticky:R="partial",hideWhenDetached:k=!1,updatePositionStrategy:L="optimized",onPlaced:S,...T}=e,E=eD(eV,h),[O,C]=r.useState(null),H=(0,eS.s)(t,e=>C(e)),[M,j]=r.useState(null),D=(0,eC.X)(M),z=null!=(d=null==D?void 0:D.width)?d:0,N=null!=(p=null==D?void 0:D.height)?p:0,F="number"==typeof A?A:{top:0,right:0,bottom:0,left:0,...A},V=Array.isArray(b)?b:[b],W=V.length>0,B={padding:F,boundary:V.filter(eY),altBoundary:W},{refs:_,floatingStyles:X,placement:I,isPositioned:Y,middlewareData:$}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:l,floating:a}={},transform:f=!0,whileElementsMounted:s,open:c}=e,[u,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(i);eu(p,i)||h(i);let[m,g]=r.useState(null),[y,w]=r.useState(null),v=r.useCallback(e=>{e!==R.current&&(R.current=e,g(e))},[]),x=r.useCallback(e=>{e!==k.current&&(k.current=e,w(e))},[]),b=l||m,A=a||y,R=r.useRef(null),k=r.useRef(null),L=r.useRef(u),S=null!=s,T=eh(s),E=eh(o),O=eh(c),C=r.useCallback(()=>{if(!R.current||!k.current)return;let e={placement:t,strategy:n,middleware:p};E.current&&(e.platform=E.current),ef(R.current,k.current,e).then(e=>{let t={...e,isPositioned:!1!==O.current};P.current&&!eu(L.current,t)&&(L.current=t,es.flushSync(()=>{d(t)}))})},[p,t,n,E,O]);ec(()=>{!1===c&&L.current.isPositioned&&(L.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[c]);let P=r.useRef(!1);ec(()=>(P.current=!0,()=>{P.current=!1}),[]),ec(()=>{if(b&&(R.current=b),A&&(k.current=A),b&&A){if(T.current)return T.current(b,A,C);C()}},[b,A,C,T,S]);let H=r.useMemo(()=>({reference:R,floating:k,setReference:v,setFloating:x}),[v,x]),M=r.useMemo(()=>({reference:b,floating:A}),[b,A]),j=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!M.floating)return e;let t=ep(M.floating,u.x),r=ep(M.floating,u.y);return f?{...e,transform:"translate("+t+"px, "+r+"px)",...ed(M.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,f,M.floating,u.x,u.y]);return r.useMemo(()=>({...u,update:C,refs:H,elements:M,floatingStyles:j}),[u,C,H,M,j])}({strategy:"fixed",placement:m+("center"!==y?"-"+y:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:s=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=G(e),h=a||s?[...p?q(p):[],...q(t)]:[];h.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),s&&e.addEventListener("resize",n)});let m=p&&u?function(e,t){let n,r=null,i=P(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function s(c,u){void 0===c&&(c=!1),void 0===u&&(u=1),a();let d=e.getBoundingClientRect(),{left:p,top:h,width:m,height:g}=d;if(c||t(),!m||!g)return;let y=f(h),w=f(i.clientWidth-(p+m)),v={rootMargin:-y+"px "+-w+"px "+-f(i.clientHeight-(h+g))+"px "+-f(p)+"px",threshold:l(0,o(1,u))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==u){if(!x)return s();r?s(!1,r):n=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==r||el(d,e.getBoundingClientRect())||s(),x=!1}try{r=new IntersectionObserver(b,{...v,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(b,v)}r.observe(e)}(!0),a}(p,n):null,g=-1,y=null;c&&(y=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&y&&(y.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),n()}),p&&!d&&y.observe(p),y.observe(t));let w=d?J(e):null;return d&&function t(){let r=J(e);w&&!el(w,r)&&n(),w=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{a&&e.removeEventListener("scroll",n),s&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=y)||e.disconnect(),y=null,d&&cancelAnimationFrame(i)}}(...t,{animationFrame:"always"===L})},elements:{reference:E.anchor},middleware:[eg({mainAxis:g+N,alignmentAxis:w}),x&&ey({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?ew():void 0,...B}),x&&ev({...B}),ex({...B,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:i}=e,{width:o,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(i,"px")),a.setProperty("--radix-popper-anchor-width","".concat(o,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),M&&eA({element:M,padding:v}),eG({arrowWidth:z,arrowHeight:N}),k&&eb({strategy:"referenceHidden",...B})]}),[Q,U]=e$(I),K=(0,eE.c)(S);(0,eO.N)(()=>{Y&&(null==K||K())},[Y,K]);let Z=null==(n=$.arrow)?void 0:n.x,ee=null==(i=$.arrow)?void 0:i.y,et=(null==(a=$.arrow)?void 0:a.centerOffset)!==0,[en,er]=r.useState();return(0,eO.N)(()=>{O&&er(window.getComputedStyle(O).zIndex)},[O]),(0,ek.jsx)("div",{ref:_.setFloating,"data-radix-popper-content-wrapper":"",style:{...X,transform:Y?X.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null==(s=$.transformOrigin)?void 0:s.x,null==(c=$.transformOrigin)?void 0:c.y].join(" "),...(null==(u=$.hide)?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,ek.jsx)(eW,{scope:h,placedSide:Q,onArrowChange:j,arrowX:Z,arrowY:ee,shouldHideArrow:et,children:(0,ek.jsx)(eR.sG.div,{"data-side":Q,"data-align":U,...T,ref:H,style:{...T.style,animation:Y?void 0:"none"}})})})});e_.displayName=eV;var eX="PopperArrow",eq={top:"bottom",right:"left",bottom:"top",left:"right"},eI=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,i=eB(eX,n),o=eq[i.placedSide];return(0,ek.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,ek.jsx)(eL,{...r,ref:t,style:{...r.style,display:"block"}})})});function eY(e){return null!==e}eI.displayName=eX;var eG=e=>({name:"transformOrigin",options:e,fn(t){var n,r,i,o,l;let{placement:a,rects:f,middlewareData:s}=t,c=(null==(n=s.arrow)?void 0:n.centerOffset)!==0,u=c?0:e.arrowWidth,d=c?0:e.arrowHeight,[p,h]=e$(a),m={start:"0%",center:"50%",end:"100%"}[h],g=(null!=(o=null==(r=s.arrow)?void 0:r.x)?o:0)+u/2,y=(null!=(l=null==(i=s.arrow)?void 0:i.y)?l:0)+d/2,w="",v="";return"bottom"===p?(w=c?m:"".concat(g,"px"),v="".concat(-d,"px")):"top"===p?(w=c?m:"".concat(g,"px"),v="".concat(f.floating.height+d,"px")):"right"===p?(w="".concat(-d,"px"),v=c?m:"".concat(y,"px")):"left"===p&&(w="".concat(f.floating.width+d,"px"),v=c?m:"".concat(y,"px")),{data:{x:w,y:v}}}});function e$(e){let[t,n="center"]=e.split("-");return[t,n]}var eQ=ez,eU=eF,eJ=e_,eK=eI},40646:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},66474:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])}}]);