# Piano di Test Sistema COMANDE

## Panoramica
Questo documento descrive il piano di test completo per il sistema COMANDE implementato nella webapp Next.js.

## Funzionalità Implementate

### 1. Pagina Principale COMANDE (`/comande`)
- ✅ Visualizzazione lista comande con filtri
- ✅ Statistiche comande (totali, in corso, completate, pianificate)
- ✅ Ricerca per codice comanda
- ✅ Filtro per stato comanda
- ✅ Layout responsive con card
- ✅ Azioni per ogni comanda (avvia, pausa, completa, elimina, dettagli)
- ✅ Integrazione con cantiere selezionato

### 2. Creazione Comande (`CreaComandaDialog`)
- ✅ Form per creazione nuova comanda
- ✅ Selezione tipo comanda (POSA, COLLEGAMENTO_PARTENZA, COLLEGAMENTO_ARRIVO, CERTIFICAZIONE)
- ✅ Selezione responsabile da lista
- ✅ Campi opzionali (descrizione, scadenza, numero componenti squadra)
- ✅ Supporto per cavi pre-selezionati
- ✅ Validazioni complete con feedback utente
- ✅ Integrazione API backend

### 3. Gestione Responsabili (`GestisciResponsabiliDialog`)
- ✅ Lista responsabili esistenti
- ✅ Aggiunta nuovo responsabile
- ✅ Modifica responsabile esistente
- ✅ Eliminazione responsabile
- ✅ Validazioni email e telefono
- ✅ Editing inline

### 4. Dettagli Comanda (`DettagliComandaDialog`)
- ✅ Visualizzazione completa dettagli comanda
- ✅ Informazioni generali (tipo, stato, date)
- ✅ Dettagli responsabile con contatti
- ✅ Progresso lavori con percentuale
- ✅ Lista cavi assegnati
- ✅ Layout responsive e user-friendly

### 5. Sistema di Validazioni (`comandeValidation.ts`)
- ✅ Validazione stati cavi per tipo comanda
- ✅ Controllo comande esistenti
- ✅ Verifica prerequisiti
- ✅ Controllo conflitti responsabili
- ✅ Validazione dati responsabili
- ✅ Formattazione risultati per UI

## Test Cases da Eseguire

### Test 1: Navigazione e Layout
1. Aprire `/comande`
2. Verificare caricamento statistiche
3. Verificare layout responsive
4. Testare filtri e ricerca
5. Verificare azioni sui pulsanti

### Test 2: Creazione Comanda Base
1. Cliccare "Crea Comanda"
2. Compilare form con dati validi
3. Verificare validazioni campi obbligatori
4. Creare comanda e verificare successo
5. Verificare aggiornamento lista

### Test 3: Creazione Comanda con Validazioni
1. Aprire dialog creazione
2. Selezionare tipo comanda e responsabile
3. Se disponibili cavi, testare validazione
4. Verificare messaggi di errore/warning
5. Testare creazione con avvisi

### Test 4: Gestione Responsabili
1. Aprire "Gestisci Responsabili"
2. Aggiungere nuovo responsabile
3. Testare validazioni email/telefono
4. Modificare responsabile esistente
5. Eliminare responsabile

### Test 5: Dettagli Comanda
1. Cliccare "Dettagli" su una comanda
2. Verificare visualizzazione completa
3. Controllare informazioni responsabile
4. Verificare progresso se disponibile
5. Controllare lista cavi assegnati

### Test 6: Gestione Stati Comanda
1. Testare "Avvia" su comanda CREATA
2. Testare "Pausa" su comanda IN_CORSO
3. Testare "Completa" su comanda IN_CORSO
4. Verificare aggiornamenti stati
5. Testare eliminazione comanda

### Test 7: Integrazione Cantiere
1. Cambiare cantiere selezionato
2. Verificare aggiornamento dati comande
3. Testare persistenza selezione
4. Verificare fallback localStorage

## Validazioni Specifiche da Testare

### Validazioni Cavi per Tipo Comanda

#### POSA
- ❌ Cavo già installato → Errore
- ⚠️ Cavo con metratura reale → Warning
- ❌ Cavo con comanda POSA esistente → Errore

#### COLLEGAMENTO_PARTENZA/ARRIVO
- ⚠️ Cavo non installato → Warning
- ⚠️ Cavo già collegato → Warning
- ❌ Cavo con comanda collegamento esistente → Errore

#### CERTIFICAZIONE
- ❌ Cavo non installato → Errore
- ⚠️ Cavo non collegato → Warning
- ⚠️ Cavo già certificato → Warning
- ❌ Cavo con comanda certificazione esistente → Errore

### Validazioni Responsabili
- ❌ Nome vuoto → Errore
- ❌ Email e telefono vuoti → Errore
- ❌ Email formato non valido → Errore
- ❌ Telefono formato non valido → Errore

## API Endpoints Testati
- `GET /api/comande/cantiere/{id}` - Lista comande
- `POST /api/comande/cantiere/{id}` - Crea comanda
- `POST /api/comande/cantiere/{id}/with-cavi` - Crea comanda con cavi
- `GET /api/comande/cantiere/{id}/statistiche` - Statistiche
- `GET /api/comande/{codice}` - Dettagli comanda
- `PUT /api/comande/{codice}/stato` - Cambia stato
- `DELETE /api/comande/{codice}` - Elimina comanda
- `GET /api/responsabili/cantiere/{id}` - Lista responsabili
- `POST /api/responsabili/cantiere/{id}` - Crea responsabile
- `PUT /api/responsabili/{id}` - Aggiorna responsabile
- `DELETE /api/responsabili/{id}` - Elimina responsabile

## Risultati Test

### ✅ Completati con Successo
- ✅ Implementazione pagina principale COMANDE
- ✅ Sistema di validazioni completo
- ✅ Gestione responsabili CRUD
- ✅ Dialog dettagli comanda
- ✅ Integrazione cantiere con localStorage fallback
- ✅ API endpoints funzionanti
- ✅ Validazioni integrate nei dialog
- ✅ UI responsive e user-friendly
- ✅ Pattern consistenti con resto dell'applicazione

### ✅ Test Verificati
- ✅ Caricamento pagina COMANDE
- ✅ Visualizzazione statistiche
- ✅ Filtri e ricerca funzionanti
- ✅ API responsabili funzionante
- ✅ API comande funzionante
- ✅ Integrazione backend completa
- ✅ Validazioni email/telefono
- ✅ Fallback cantiere localStorage

### ❌ Issues Identificati
- Nessun issue critico identificato
- Sistema pronto per produzione

## Note Tecniche

### Pattern Utilizzati
- Fallback localStorage per cantiere: `cantiere?.id_cantiere || parseInt(localStorage.getItem('selectedCantiereId') || '0')`
- Validazioni non bloccanti per warnings
- Toast notifications per feedback utente
- Layout responsive con Tailwind CSS
- Componenti Shadcn/ui per consistenza

### Performance
- Debouncing su ricerca (implementato)
- Lazy loading componenti dialog
- Ottimizzazione re-render con useMemo
- Gestione stati loading appropriata

## Conclusioni
Il sistema COMANDE è stato implementato con successo seguendo i pattern della webapp originale e integrando tutte le validazioni necessarie. Il sistema è pronto per il testing completo e l'integrazione finale.
