'use client'

import { useState, useMemo, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { getCavoColorClasses } from '@/utils/softColors'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { TableRow, TableCell } from '@/components/ui/table'
import { Cavo } from '@/types'
import FilterableTable, { ColumnDef } from '@/components/common/FilterableTable'
import SmartCaviFilter from './SmartCaviFilter'
import TruncatedText from '@/components/common/TruncatedText'
import {
  MoreHorizontal,
  Cable,
  Settings,
  Zap,
  CheckCircle,
  AlertCircle,
  Clock,
  Package,
  Unplug,
  FileText,
  Download,
  X,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Eye,
  Edit,
  Trash2,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  <PERSON>,
  Filter,
  Info,
  Link,
  Unlink,
  ChevronDown,
  Plus
} from 'lucide-react'
import {
  DisconnectCableDialog,
  GeneratePDFDialog,
  CertificationErrorDialog,
  PDFGenerationConfig
} from './CaviActionDialogs'
import { useToastActions } from '@/components/ui/toast-notification'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'

interface CaviTableProps {
  cavi: Cavo[]
  loading?: boolean
  selectionEnabled?: boolean
  selectedCavi?: string[]
  onSelectionChange?: (selectedIds: string[]) => void
  onStatusAction?: (cavo: Cavo, action: string) => void
  onContextMenuAction?: (cavo: Cavo, action: string) => void
  onDisconnectCable?: (cavoId: string) => Promise<void>
  onGeneratePDF?: (config: PDFGenerationConfig) => Promise<void>
  onCertifyCable?: (cavoId: string) => Promise<void>
}

type SortField = 'id_cavo' | 'sistema' | 'metri_teorici' | 'metri_posati' | 'stato'
type SortDirection = 'asc' | 'desc'

interface DialogState {
  disconnect: { isOpen: boolean; cavo: Cavo | null }
  generatePDF: { isOpen: boolean; cavo: Cavo | null }
  certificationError: { isOpen: boolean; cavo: Cavo | null; missingRequirements: string[] }
}

export default function CaviTable({
  cavi = [],
  loading = false,
  selectionEnabled = false,
  selectedCavi = [],
  onSelectionChange,
  onStatusAction,
  onContextMenuAction,
  onDisconnectCable,
  onGeneratePDF,
  onCertifyCable
}: CaviTableProps) {
  const [smartFilteredCavi, setSmartFilteredCavi] = useState(cavi)
  const [filteredCavi, setFilteredCavi] = useState(cavi)
  const [internalSelectionEnabled, setInternalSelectionEnabled] = useState(selectionEnabled)
  const [sortField, setSortField] = useState<SortField>('id_cavo')
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc')
  const [dialogs, setDialogs] = useState<DialogState>({
    disconnect: { isOpen: false, cavo: null },
    generatePDF: { isOpen: false, cavo: null },
    certificationError: { isOpen: false, cavo: null, missingRequirements: [] }
  })

  // Stati per la paginazione
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(25)
  const [showBulkActions, setShowBulkActions] = useState(false)

  const toast = useToastActions()

  // Funzioni per gestire i dialog
  const openDialog = (type: keyof DialogState, cavo: Cavo, missingRequirements: string[] = []) => {
    setDialogs(prev => ({
      ...prev,
      [type]: { isOpen: true, cavo, missingRequirements }
    }))
  }

  const closeDialog = (type: keyof DialogState) => {
    setDialogs(prev => ({
      ...prev,
      [type]: { isOpen: false, cavo: null, missingRequirements: [] }
    }))
  }

  // Funzione per ordinamento
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  // Ordinamento dei cavi
  const sortedCavi = useMemo(() => {
    const sorted = [...smartFilteredCavi].sort((a, b) => {
      let aValue: any = a[sortField]
      let bValue: any = b[sortField]

      // Gestione valori numerici
      if (sortField === 'metri_teorici' || sortField === 'metri_posati') {
        aValue = parseFloat(aValue) || 0
        bValue = parseFloat(bValue) || 0
      }

      // Gestione stringhe
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
      }
      if (typeof bValue === 'string') {
        bValue = bValue.toLowerCase()
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1
      return 0
    })
    return sorted
  }, [smartFilteredCavi, sortField, sortDirection])

  // Aggiorna i cavi quando cambiano i cavi originali
  useEffect(() => {
    setSmartFilteredCavi(cavi)
  }, [cavi])

  // Calcolo paginazione
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    const paginatedCavi = sortedCavi.slice(startIndex, endIndex)

    const totalPages = Math.ceil(sortedCavi.length / itemsPerPage)
    const hasNextPage = currentPage < totalPages
    const hasPrevPage = currentPage > 1

    return {
      cavi: paginatedCavi,
      totalItems: sortedCavi.length,
      totalPages,
      hasNextPage,
      hasPrevPage,
      startIndex: startIndex + 1,
      endIndex: Math.min(endIndex, sortedCavi.length)
    }
  }, [sortedCavi, currentPage, itemsPerPage])

  // Aggiorna filteredCavi quando cambiano i cavi paginati
  useEffect(() => {
    setFilteredCavi(paginatedData.cavi)
  }, [paginatedData.cavi])

  // Reset pagina quando cambiano i filtri
  useEffect(() => {
    setCurrentPage(1)
  }, [smartFilteredCavi.length])

  // Gestione filtri intelligenti
  const handleSmartFilterChange = (filtered: Cavo[]) => {
    setSmartFilteredCavi(filtered)
  }

  // Gestione filtri tabella
  const handleTableFilterChange = (filtered: Cavo[]) => {
    setFilteredCavi(filtered)
  }

  // Gestione azioni sui cavi
  const handleDisconnectCable = async (cavoId: string) => {
    try {
      if (onDisconnectCable) {
        await onDisconnectCable(cavoId)
        toast.cavoDisconnected(cavoId)
      }
    } catch (error) {
      toast.error('Errore Scollegamento', 'Impossibile scollegare il cavo. Riprova.')
    }
  }

  const handleGeneratePDF = async (config: PDFGenerationConfig) => {
    try {
      if (onGeneratePDF) {
        await onGeneratePDF(config)
        toast.pdfGenerated(config.fileName, config.cavoId)
      }
    } catch (error) {
      toast.error('Errore Generazione PDF', 'Impossibile generare il certificato. Riprova.')
    }
  }

  const handleCertifyCable = async (cavo: Cavo) => {
    // Verifica requisiti per certificazione
    const missingRequirements: string[] = []

    if (!cavo.metri_posati || parseFloat(cavo.metri_posati) === 0) {
      missingRequirements.push('Metri posati non inseriti')
    }

    if (cavo.stato !== 'Collegato') {
      missingRequirements.push('Cavo non collegato')
    }

    if (!cavo.data_installazione) {
      missingRequirements.push('Data installazione mancante')
    }

    if (missingRequirements.length > 0) {
      openDialog('certificationError', cavo, missingRequirements)
      return
    }

    try {
      if (onCertifyCable) {
        toast.actionInProgress('Certificazione', cavo.id_cavo)
        await onCertifyCable(cavo.id_cavo)
        toast.success('Cavo Certificato', `Il cavo ${cavo.id_cavo} è stato certificato con successo.`)
      }
    } catch (error) {
      toast.certificationError(cavo.id_cavo, 'Errore durante il processo di certificazione')
    }
  }

  const handleSelectionToggle = () => {
    setInternalSelectionEnabled(!internalSelectionEnabled)
    setShowBulkActions(!internalSelectionEnabled && selectedCavi.length > 0)
  }

  // Funzioni per la paginazione
  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, paginatedData.totalPages)))
  }

  const goToFirstPage = () => goToPage(1)
  const goToLastPage = () => goToPage(paginatedData.totalPages)
  const goToPrevPage = () => goToPage(currentPage - 1)
  const goToNextPage = () => goToPage(currentPage + 1)

  // Funzioni per selezione multipla
  const selectAllVisible = () => {
    const visibleIds = paginatedData.cavi.map(c => c.id_cavo)
    const newSelection = [...new Set([...selectedCavi, ...visibleIds])]
    onSelectionChange?.(newSelection)
  }

  const deselectAllVisible = () => {
    const visibleIds = new Set(paginatedData.cavi.map(c => c.id_cavo))
    const newSelection = selectedCavi.filter(id => !visibleIds.has(id))
    onSelectionChange?.(newSelection)
  }

  const selectAll = () => {
    const allIds = sortedCavi.map(c => c.id_cavo)
    onSelectionChange?.(allIds)
  }

  const deselectAll = () => {
    onSelectionChange?.([])
  }

  // Verifica se tutti i cavi visibili sono selezionati
  const allVisibleSelected = paginatedData.cavi.length > 0 &&
    paginatedData.cavi.every(cavo => selectedCavi.includes(cavo.id_cavo))

  const someVisibleSelected = paginatedData.cavi.some(cavo => selectedCavi.includes(cavo.id_cavo))

  // Componente per header ordinabile
  const SortableHeader = ({ field, children, className = "" }: {
    field: SortField;
    children: React.ReactNode;
    className?: string
  }) => (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <button
            onClick={() => handleSort(field)}
            className={`flex items-center gap-2 font-medium text-left hover:text-blue-600 transition-colors ${className}`}
          >
            {children}
            {sortField === field ? (
              sortDirection === 'asc' ? (
                <ArrowUp className="w-4 h-4 text-blue-600" />
              ) : (
                <ArrowDown className="w-4 h-4 text-blue-600" />
              )
            ) : (
              <ArrowUpDown className="w-4 h-4 text-slate-400" />
            )}
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Clicca per ordinare per {children}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )

  // Componente per pulsanti azione con tooltip
  const ActionButton = ({
    onClick,
    icon: Icon,
    tooltip,
    variant = "default",
    disabled = false,
    className = ""
  }: {
    onClick: () => void;
    icon: any;
    tooltip: string;
    variant?: "default" | "danger" | "success" | "warning";
    disabled?: boolean;
    className?: string;
  }) => {
    const variantClasses = {
      default: "bg-blue-600 hover:bg-blue-700 text-white",
      danger: "bg-red-600 hover:bg-red-700 text-white",
      success: "bg-green-600 hover:bg-green-700 text-white",
      warning: "bg-orange-600 hover:bg-orange-700 text-white"
    }

    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              size="sm"
              onClick={onClick}
              disabled={disabled}
              className={`h-8 w-8 p-0 ${variantClasses[variant]} ${className} transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100`}
            >
              <Icon className="w-4 h-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>{tooltip}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  }

  // Gestione selezione
  const handleSelectAll = (checked: boolean) => {
    if (onSelectionChange) {
      onSelectionChange(checked ? filteredCavi.map(c => c.id_cavo) : [])
    }
  }

  const handleSelectCavo = (cavoId: string, checked: boolean) => {
    if (onSelectionChange) {
      const newSelection = checked
        ? [...selectedCavi, cavoId]
        : selectedCavi.filter(id => id !== cavoId)
      onSelectionChange(newSelection)
    }
  }

  // Bulk action handlers
  const handleBulkExport = async () => {
    try {
      const response = await fetch('/api/cavi/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          selectedIds: selectedCavi,
          cantiereId: 1 // TODO: Get from context
        })
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `cavi_export_${new Date().toISOString().split('T')[0]}.csv`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        const error = await response.json()
        alert(`Errore durante l'esportazione: ${error.error}`)
      }
    } catch (error) {
      alert('Errore durante l\'esportazione')
    }
  }

  const handleBulkStatusChange = async () => {
    const newStatus = prompt('Inserisci il nuovo stato (Da installare, In corso, Installato):')
    if (!newStatus) return

    try {
      const response = await fetch('/api/cavi/bulk-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          selectedIds: selectedCavi,
          cantiereId: 1, // TODO: Get from context
          newStatus
        })
      })

      const result = await response.json()
      if (result.success) {
        alert(result.message)
        // TODO: Refresh data
      } else {
        alert(`Errore: ${result.error}`)
      }
    } catch (error) {
      alert('Errore durante il cambio stato')
    }
  }

  const handleBulkAssignCommand = () => {
    // TODO: Implementare modal per selezione comanda
    alert(`Assegnazione comanda per ${selectedCavi.length} cavi`)
  }

  const handleBulkDelete = async () => {
    if (!confirm(`Sei sicuro di voler eliminare ${selectedCavi.length} cavi?`)) {
      return
    }

    try {
      const response = await fetch('/api/cavi/bulk-delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          selectedIds: selectedCavi,
          cantiereId: 1 // TODO: Get from context
        })
      })

      const result = await response.json()
      if (result.success) {
        alert(result.message)
        // TODO: Refresh data
      } else {
        alert(`Errore: ${result.error}`)
      }
    } catch (error) {
      alert('Errore durante l\'eliminazione')
    }
  }

  // Define columns matching original webapp structure
  const columns: ColumnDef[] = useMemo(() => {
    const baseColumns: ColumnDef[] = [
      {
        field: 'id_cavo',
        headerName: 'ID',
        dataType: 'text',
        width: 70,
        align: 'left',
        renderHeader: () => (
          <SortableHeader field="id_cavo">ID</SortableHeader>
        ),
        renderCell: (row: Cavo) => (
          <span className="font-semibold text-mariner-900">{row.id_cavo}</span>
        )
      },
      {
        field: 'sistema',
        headerName: 'Sistema',
        dataType: 'text',
        width: 80,
        renderHeader: () => (
          <SortableHeader field="sistema">Sistema</SortableHeader>
        ),
        renderCell: (row: Cavo) => (
          <TruncatedText text={row.sistema || ''} maxLength={8} />
        )
      },
      {
        field: 'utility',
        headerName: 'Utility',
        dataType: 'text',
        width: 80,
        renderCell: (row: Cavo) => (
          <TruncatedText text={row.utility || ''} maxLength={8} />
        )
      },
      {
        field: 'tipologia',
        headerName: 'Tipologia',
        dataType: 'text',
        width: 100,
        renderCell: (row: Cavo) => (
          <TruncatedText text={row.tipologia || ''} maxLength={12} />
        )
      },
      {
        field: 'formazione',
        headerName: 'Form.',
        dataType: 'text',
        align: 'left',
        width: 60,
        renderCell: (row: Cavo) => row.formazione || row.sezione
      },
      {
        field: 'metri_teorici',
        headerName: 'M.Teor.',
        dataType: 'number',
        align: 'left',
        width: 70,
        renderHeader: () => (
          <SortableHeader field="metri_teorici">M.Teor.</SortableHeader>
        ),
        renderCell: (row: Cavo) => row.metri_teorici ? row.metri_teorici.toFixed(1) : '0'
      },
      {
        field: 'metri_posati',
        headerName: 'M.Reali',
        dataType: 'number',
        align: 'left',
        width: 70,
        renderHeader: () => (
          <SortableHeader field="metri_posati">M.Reali</SortableHeader>
        ),
        renderCell: (row: Cavo) => {
          const metri = row.metri_posati || row.metratura_reale || 0
          return metri ? metri.toFixed(1) : '0'
        }
      },
      {
        field: 'ubicazione_partenza',
        headerName: 'Da',
        dataType: 'text',
        width: 140,
        renderCell: (row: Cavo) => (
          <TruncatedText
            text={row.da || row.ubicazione_partenza || ''}
            maxLength={18}
          />
        )
      },
      {
        field: 'ubicazione_arrivo',
        headerName: 'A',
        dataType: 'text',
        width: 140,
        renderCell: (row: Cavo) => (
          <TruncatedText
            text={row.a || row.ubicazione_arrivo || ''}
            maxLength={18}
          />
        )
      },
      {
        field: 'id_bobina',
        headerName: 'Bobina',
        dataType: 'text',
        width: 100,
        align: 'center',
        renderCell: (row: Cavo) => getBobinaButton(row)
      },
      {
        field: 'stato_installazione',
        headerName: 'Stato',
        dataType: 'text',
        align: 'left',
        width: 130,
        disableFilter: true,
        renderHeader: () => (
          <SortableHeader field="stato">Stato</SortableHeader>
        ),
        renderCell: (row: Cavo) => getStatusButton(row)
      },
      {
        field: 'collegamenti',
        headerName: 'Collegamenti',
        dataType: 'text',
        align: 'left',
        width: 160,
        disableFilter: true,
        disableSort: true,
        renderCell: (row: Cavo) => getConnectionButton(row)
      },
      {
        field: 'certificato',
        headerName: 'Certificato',
        dataType: 'text',
        align: 'left',
        width: 170,
        disableFilter: true,
        disableSort: true,
        renderCell: (row: Cavo) => getCertificationButton(row)
      },

    ]

    // Add selection column if enabled
    if (internalSelectionEnabled) {
      baseColumns.unshift({
        field: 'selection',
        headerName: '',
        disableFilter: true,
        disableSort: true,
        width: 50,
        align: 'left',
        renderHeader: () => (
          <Checkbox
            checked={selectedCavi.length === filteredCavi.length && filteredCavi.length > 0}
            onCheckedChange={handleSelectAll}
          />
        ),
        renderCell: (row: Cavo) => (
          <Checkbox
            checked={selectedCavi.includes(row.id_cavo)}
            onCheckedChange={(checked) => handleSelectCavo(row.id_cavo, checked as boolean)}
            onClick={(e) => e.stopPropagation()}
          />
        )
      })
    }

    return baseColumns
  }, [internalSelectionEnabled, selectedCavi, filteredCavi, handleSelectAll, handleSelectCavo])

  // Custom row renderer for selection and context menu
  const renderRow = (row: Cavo, index: number) => {
    const isSelected = selectedCavi.includes(row.id_cavo)

    return (
      <TableRow
        key={row.id_cavo}
        className={`
          ${isSelected ? 'bg-blue-50 border-blue-200' : 'bg-white'}
          hover:bg-blue-50 hover:border-blue-200 hover:shadow-sm
          cursor-pointer border-b border-gray-200
          transition-all duration-200 ease-in-out
          ${isSelected ? 'ring-1 ring-blue-300' : ''}
        `}
        onClick={() => internalSelectionEnabled && handleSelectCavo(row.id_cavo, !isSelected)}
        onContextMenu={(e) => {
          e.preventDefault()
          onContextMenuAction?.(row, 'context_menu')
        }}
      >
        {columns.map((column) => (
          <TableCell
            key={column.field}
            className={`
              py-2 px-2 text-sm text-left
              ${isSelected ? 'text-blue-900' : 'text-gray-900'}
              transition-colors duration-200
            `}
            style={{ width: column.width, ...column.cellStyle }}
            onClick={(e) => {
              // Prevent row click for action columns
              if (['stato_installazione', 'collegamenti', 'certificato'].includes(column.field)) {
                e.stopPropagation()
              }
            }}
          >
            {column.renderCell ? column.renderCell(row) : (row[column.field] || <span className="text-gray-400">-</span>)}
          </TableCell>
        ))}
      </TableRow>
    )
  }

  // Funzione per la colonna Bobina (Indicativa - Azione Implicita)
  const getBobinaButton = (cavo: Cavo) => {
    const idBobina = cavo.id_bobina

    if (!idBobina || idBobina === 'N/A') {
      return <span className="text-gray-400">-</span>
    }

    if (idBobina === 'BOBINA_VUOTA') {
      return (
        <Badge
          variant="outline"
          className="text-xs px-2 py-0.5 text-gray-500 border-gray-300 bg-gray-50"
        >
          Vuota
        </Badge>
      )
    }

    // Estrai il numero della bobina usando i pattern esistenti
    let displayValue = idBobina
    let match = idBobina.match(/_B(.+)$/)
    if (match) {
      displayValue = match[1]
    } else {
      match = idBobina.match(/_b(.+)$/)
      if (match) {
        displayValue = match[1]
      } else {
        match = idBobina.match(/c\d+_[bB](\d+)$/)
        if (match) {
          displayValue = match[1]
        } else {
          match = idBobina.match(/(\d+)$/)
          if (match) {
            displayValue = match[1]
          }
        }
      }
    }

    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge
              variant="outline"
              className="cursor-pointer hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 hover:shadow-sm transition-all duration-200 px-2 py-1 font-medium text-slate-700 border-slate-300 bg-white flex items-center gap-1"
              onClick={(e) => {
                e.stopPropagation()
                onStatusAction?.(cavo, 'bobina_menu')
              }}
            >
              <span>{displayValue}</span>
              <ChevronDown className="h-3 w-3 opacity-60" />
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <p>Clicca per gestire bobina</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  }

  // Funzioni di utilità per lo stato
  const getStatusBadge = (cavo: Cavo) => {
    // Verifica se il cavo è assegnato a una comanda
    const comandaPosa = cavo.comanda_posa
    const comandaPartenza = cavo.comanda_partenza
    const comandaArrivo = cavo.comanda_arrivo
    const comandaCertificazione = cavo.comanda_certificazione

    // Trova la comanda attiva (priorità: posa > partenza > arrivo > certificazione)
    const comandaAttiva = comandaPosa || comandaPartenza || comandaArrivo || comandaCertificazione

    // Se c'è una comanda attiva e lo stato è "In corso", mostra il codice comanda
    if (comandaAttiva && cavo.stato_installazione === 'In corso') {
      const colorClasses = getCavoColorClasses('IN_CORSO')
      return (
        <Badge
          className={`cursor-pointer ${colorClasses.badge} ${colorClasses.hover}`}
          onClick={() => onStatusAction?.(cavo, 'view_command', comandaAttiva)}
        >
          {comandaAttiva}
        </Badge>
      )
    }

    // Logica normale per gli altri stati
    const stato = cavo.stato_installazione || 'Da installare'
    const colorClasses = getCavoColorClasses(stato)

    return (
      <Badge className={colorClasses.badge}>
        {stato}
      </Badge>
    )
  }

  const getStatusButton = (cavo: Cavo) => {
    // Verifica se il cavo è installato controllando metri_posati o metratura_reale
    const metriInstallati = cavo.metri_posati || cavo.metratura_reale || 0
    const isInstalled = metriInstallati > 0

    // Verifica se il cavo è assegnato a una comanda attiva
    const comandaPosa = cavo.comanda_posa
    const comandaPartenza = cavo.comanda_partenza
    const comandaArrivo = cavo.comanda_arrivo
    const comandaCertificazione = cavo.comanda_certificazione
    const comandaAttiva = comandaPosa || comandaPartenza || comandaArrivo || comandaCertificazione

    // Se c'è una comanda attiva e lo stato è "In corso", mostra il codice comanda (questo rimane cliccabile)
    if (comandaAttiva && cavo.stato_installazione === 'In corso') {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge
                className="bg-blue-500 text-white cursor-pointer hover:bg-blue-600 transition-colors duration-200 px-3 py-1 font-medium"
                onClick={(e) => {
                  e.stopPropagation()
                  onStatusAction?.(cavo, 'view_command', comandaAttiva)
                }}
              >
                {comandaAttiva}
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <p>Visualizza dettagli comanda</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )
    }

    // Determina lo stato del cavo - BADGE PURAMENTE INDICATIVI (non cliccabili)
    const stato = cavo.stato_installazione || 'Da installare'

    if (stato === 'Installato' || isInstalled) {
      return (
        <Badge className="bg-green-100 text-green-700 px-3 py-1 font-medium border border-green-200">
          Installato
        </Badge>
      )
    } else if (stato === 'In corso') {
      return (
        <Badge className="bg-yellow-100 text-yellow-700 px-3 py-1 font-medium border border-yellow-200">
          In corso
        </Badge>
      )
    } else {
      return (
        <Badge
          variant="outline"
          className="text-gray-600 px-3 py-1 font-medium border-gray-300 bg-gray-50"
        >
          Da installare
        </Badge>
      )
    }
  }

  const getConnectionButton = (cavo: Cavo) => {
    const isInstalled = cavo.metri_posati > 0 || cavo.metratura_reale > 0
    const collegamento = cavo.collegamento || cavo.collegamenti || 0

    // "Non disponibile" - Design distinto per elementi non interattivi
    if (!isInstalled) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <span className="text-gray-400 text-sm px-2 py-1 flex items-center gap-1">
                <Info className="h-3 w-3" />
                Non disponibile
              </span>
            </TooltipTrigger>
            <TooltipContent>
              <p>Il collegamento non può essere gestito perché il cavo non è installato</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )
    }

    // Pulsanti di azione - Stile outline con hover states chiari
    let label, icon, actionType, buttonClass

    switch (collegamento) {
      case 0:
        label = "Collega"
        icon = Link
        actionType = "connect_cable"
        buttonClass = "border-blue-300 text-blue-700 hover:bg-blue-50 hover:border-blue-400"
        break
      case 1:
        label = "Completa Arrivo"
        icon = Link
        actionType = "connect_arrival"
        buttonClass = "border-yellow-300 text-yellow-700 hover:bg-yellow-50 hover:border-yellow-400"
        break
      case 2:
        label = "Completa Partenza"
        icon = Link
        actionType = "connect_departure"
        buttonClass = "border-yellow-300 text-yellow-700 hover:bg-yellow-50 hover:border-yellow-400"
        break
      case 3:
        label = "Scollega"
        icon = Unlink
        actionType = "disconnect_cable"
        buttonClass = "border-red-300 text-red-700 hover:bg-red-50 hover:border-red-400"
        break
      default:
        label = "Gestisci"
        icon = Settings
        actionType = "manage_connections"
        buttonClass = "border-blue-300 text-blue-700 hover:bg-blue-50 hover:border-blue-400"
        break
    }

    const IconComponent = icon

    return (
      <Button
        variant="outline"
        size="sm"
        className={`h-7 px-2 text-xs font-medium transition-colors duration-200 ${buttonClass}`}
        onClick={(e) => {
          e.stopPropagation()
          if (actionType === 'disconnect_cable') {
            openDialog('disconnect', cavo)
          } else {
            onStatusAction?.(cavo, actionType)
          }
        }}
      >
        <IconComponent className="h-3 w-3 mr-1" />
        {label}
      </Button>
    )
  }

  const getCertificationButton = (cavo: Cavo) => {
    const isInstalled = cavo.metri_posati > 0 || cavo.metratura_reale > 0
    const isCertified = cavo.certificato === true || cavo.certificato === 'SI' || cavo.certificato === 'CERTIFICATO'
    const isRejected = cavo.certificato === false || cavo.certificato === 'NO' || cavo.certificato === 'RIFIUTATO'

    // "Non disponibile" - Design distinto per elementi non interattivi
    if (!isInstalled) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <span className="text-gray-400 text-sm px-2 py-1 flex items-center gap-1">
                <Info className="h-3 w-3" />
                Non disponibile
              </span>
            </TooltipTrigger>
            <TooltipContent>
              <p>La certificazione non può essere gestita perché il cavo non è installato</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )
    }

    // Certificato - Badge di stato + pulsante download
    if (isCertified) {
      return (
        <div className="flex items-center gap-2">
          <Badge className="bg-green-100 text-green-700 px-2 py-1 text-xs font-medium border border-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            Certificato
          </Badge>
          <Button
            variant="outline"
            size="sm"
            className="h-6 w-6 p-0 border-blue-300 text-blue-700 hover:bg-blue-50 hover:border-blue-400"
            onClick={(e) => {
              e.stopPropagation()
              openDialog('generatePDF', cavo)
            }}
          >
            <Download className="h-3 w-3" />
          </Button>
        </div>
      )
    }

    // Non Certificato/Rifiutato - Badge di stato (non cliccabile)
    if (isRejected) {
      return (
        <Badge className="bg-red-100 text-red-700 px-2 py-1 text-xs font-medium border border-red-200">
          <X className="h-3 w-3 mr-1" />
          Non Certificato
        </Badge>
      )
    }

    // Azione "Certifica" - Pulsante chiaro
    return (
      <Button
        variant="outline"
        size="sm"
        className="h-7 px-2 text-xs font-medium border-purple-300 text-purple-700 hover:bg-purple-50 hover:border-purple-400 transition-colors duration-200"
        onClick={(e) => {
          e.stopPropagation()
          handleCertifyCable(cavo)
        }}
      >
        <CheckCircle className="h-3 w-3 mr-1" />
        Certifica
      </Button>
    )
  }

  return (
    <div className="relative">
      {/* Smart Filter */}
      <SmartCaviFilter
        cavi={cavi}
        onFilteredDataChange={handleSmartFilterChange}
        loading={loading}
        selectionEnabled={internalSelectionEnabled}
        onSelectionToggle={handleSelectionToggle}
      />

      {/* Controlli Tabella e Paginazione */}
      <div className="mb-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 bg-slate-50 p-4 rounded-lg border border-slate-200">
        {/* Info e controlli selezione */}
        <div className="flex items-center gap-4">
          <div className="text-sm text-slate-600">
            Mostrando <span className="font-semibold">{paginatedData.startIndex}</span> - <span className="font-semibold">{paginatedData.endIndex}</span> di <span className="font-semibold">{paginatedData.totalItems}</span> cavi
          </div>

          {internalSelectionEnabled && (
            <div className="flex items-center gap-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div
                      onClick={allVisibleSelected ? deselectAllVisible : selectAllVisible}
                      className="flex items-center gap-2 px-3 py-2 text-sm font-medium border border-slate-200 rounded-md hover:bg-slate-50 hover:border-slate-300 transition-all duration-200 cursor-pointer"
                    >
                      <Checkbox
                        checked={allVisibleSelected}
                        ref={(el) => {
                          if (el) el.indeterminate = someVisibleSelected && !allVisibleSelected
                        }}
                      />
                      Pagina
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{allVisibleSelected ? 'Deseleziona tutti i cavi visibili' : 'Seleziona tutti i cavi visibili'}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={selectedCavi.length === sortedCavi.length ? deselectAll : selectAll}
                      className="flex items-center gap-2"
                    >
                      <Users className="w-4 h-4" />
                      Tutti ({sortedCavi.length})
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{selectedCavi.length === sortedCavi.length ? 'Deseleziona tutti i cavi' : 'Seleziona tutti i cavi filtrati'}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              {selectedCavi.length > 0 && (
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  {selectedCavi.length} selezionati
                </Badge>
              )}
            </div>
          )}
        </div>

        {/* Controlli per pagina */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-slate-600">Righe per pagina:</span>
          <select
            value={itemsPerPage}
            onChange={(e) => {
              setItemsPerPage(Number(e.target.value))
              setCurrentPage(1)
            }}
            className="border border-slate-300 rounded px-2 py-1 text-sm"
          >
            <option value={10}>10</option>
            <option value={25}>25</option>
            <option value={50}>50</option>
            <option value={100}>100</option>
          </select>
        </div>
      </div>

      {/* Filterable Table */}
      <FilterableTable
        data={smartFilteredCavi}
        columns={columns}
        loading={loading}
        emptyMessage="Nessun cavo disponibile"
        onFilteredDataChange={handleTableFilterChange}
        renderRow={renderRow}
      />

      {/* Controlli Paginazione */}
      {paginatedData.totalPages > 1 && (
        <div className="mt-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 bg-white p-4 rounded-lg border border-slate-200">
          <div className="text-sm text-slate-600">
            Pagina <span className="font-semibold">{currentPage}</span> di <span className="font-semibold">{paginatedData.totalPages}</span>
          </div>

          <div className="flex items-center gap-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={goToFirstPage}
                    disabled={!paginatedData.hasPrevPage}
                    className="p-2"
                  >
                    <ChevronsLeft className="w-4 h-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Prima pagina</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={goToPrevPage}
                    disabled={!paginatedData.hasPrevPage}
                    className="p-2"
                  >
                    <ChevronLeft className="w-4 h-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Pagina precedente</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {/* Numeri di pagina */}
            <div className="flex items-center gap-1">
              {Array.from({ length: Math.min(5, paginatedData.totalPages) }, (_, i) => {
                let pageNum: number
                if (paginatedData.totalPages <= 5) {
                  pageNum = i + 1
                } else if (currentPage <= 3) {
                  pageNum = i + 1
                } else if (currentPage >= paginatedData.totalPages - 2) {
                  pageNum = paginatedData.totalPages - 4 + i
                } else {
                  pageNum = currentPage - 2 + i
                }

                return (
                  <Button
                    key={pageNum}
                    variant={currentPage === pageNum ? "default" : "outline"}
                    size="sm"
                    onClick={() => goToPage(pageNum)}
                    className="w-8 h-8 p-0"
                  >
                    {pageNum}
                  </Button>
                )
              })}
            </div>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={goToNextPage}
                    disabled={!paginatedData.hasNextPage}
                    className="p-2"
                  >
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Pagina successiva</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={goToLastPage}
                    disabled={!paginatedData.hasNextPage}
                    className="p-2"
                  >
                    <ChevronsRight className="w-4 h-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Ultima pagina</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      )}

      {/* Barra Azioni Bulk - appare solo quando ci sono elementi selezionati */}
      {internalSelectionEnabled && selectedCavi.length > 0 && (
        <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-white border border-slate-300 rounded-lg shadow-xl z-50 p-4 min-w-[600px]">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                <Badge variant="secondary" className="bg-blue-100 text-blue-800 font-semibold">
                  {selectedCavi.length} cavi selezionati
                </Badge>
              </div>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={deselectAll}
                      className="text-slate-600 hover:text-slate-800"
                    >
                      <X className="w-4 h-4 mr-1" />
                      Deseleziona tutto
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Rimuovi la selezione da tutti i cavi</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>

            <div className="flex items-center gap-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        // Logica per esportazione bulk
                        toast.actionInProgress('Esportazione', `${selectedCavi.length} cavi`)
                      }}
                      className="flex items-center gap-2 hover:bg-green-50 hover:border-green-300"
                    >
                      <Download className="w-4 h-4" />
                      Esporta
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Esporta i cavi selezionati in Excel</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        // Logica per generazione PDF bulk
                        toast.actionInProgress('Generazione PDF', `${selectedCavi.length} cavi`)
                      }}
                      className="flex items-center gap-2 hover:bg-blue-50 hover:border-blue-300"
                    >
                      <FileText className="w-4 h-4" />
                      PDF Bulk
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Genera PDF per tutti i cavi selezionati</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="sm"
                      onClick={() => {
                        // Logica per cambio stato bulk
                        toast.actionInProgress('Aggiornamento Stato', `${selectedCavi.length} cavi`)
                      }}
                      className="flex items-center gap-2 hover:bg-yellow-50 hover:border-yellow-300"
                    >
                      <Settings className="w-4 h-4" />
                      Stato
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Cambia stato per tutti i cavi selezionati</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        // Logica per assegnazione comanda bulk
                        toast.actionInProgress('Assegnazione Comanda', `${selectedCavi.length} cavi`)
                      }}
                      className="flex items-center gap-2 hover:bg-purple-50 hover:border-purple-300"
                    >
                      <Package className="w-4 h-4" />
                      Comanda
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Assegna comanda a tutti i cavi selezionati</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <div className="w-px h-6 bg-slate-300"></div>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => {
                        // Logica per eliminazione bulk con conferma
                        if (confirm(`Sei sicuro di voler eliminare ${selectedCavi.length} cavi selezionati?`)) {
                          toast.actionInProgress('Eliminazione', `${selectedCavi.length} cavi`)
                        }
                      }}
                      className="flex items-center gap-2"
                    >
                      <Trash2 className="w-4 h-4" />
                      Elimina
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Elimina tutti i cavi selezionati (azione irreversibile)</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </div>
      )}

      {/* Dialog Components */}
      <DisconnectCableDialog
        isOpen={dialogs.disconnect.isOpen}
        onClose={() => closeDialog('disconnect')}
        onConfirm={handleDisconnectCable}
        cavo={dialogs.disconnect.cavo}
      />

      <GeneratePDFDialog
        isOpen={dialogs.generatePDF.isOpen}
        onClose={() => closeDialog('generatePDF')}
        onGenerate={handleGeneratePDF}
        cavo={dialogs.generatePDF.cavo}
      />

      <CertificationErrorDialog
        isOpen={dialogs.certificationError.isOpen}
        onClose={() => closeDialog('certificationError')}
        cavo={dialogs.certificationError.cavo}
        missingRequirements={dialogs.certificationError.missingRequirements}
      />
    </div>
  )
}
