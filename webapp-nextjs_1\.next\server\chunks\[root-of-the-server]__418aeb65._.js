module.exports = {

"[project]/.next-internal/server/app/api/cavi/[cantiereId]/[cavoId]/collegamento/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/api/cavi/[cantiereId]/[cavoId]/collegamento/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DELETE": (()=>DELETE),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
async function POST(request, { params }) {
    try {
        const { cantiereId, cavoId } = await params;
        const body = await request.json();
        // Validazione parametri
        if (!cantiereId || !cavoId) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Parametri cantiere ID e cavo ID richiesti'
            }, {
                status: 400
            });
        }
        if (!body.lato || ![
            'partenza',
            'arrivo'
        ].includes(body.lato)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Lato richiesto: "partenza" o "arrivo"'
            }, {
                status: 400
            });
        }
        // Ottieni il token di autenticazione dalla richiesta
        const authHeader = request.headers.get('authorization');
        if (!authHeader) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Token di autenticazione richiesto'
            }, {
                status: 401
            });
        }
        // Chiama l'API backend Python
        const backendUrl = process.env.BACKEND_URL || 'http://localhost:8000';
        const response = await fetch(`${backendUrl}/cavi/${cantiereId}/${cavoId}/collegamento`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': authHeader
            },
            body: JSON.stringify({
                lato: body.lato,
                responsabile: body.responsabile || 'cantiere'
            })
        });
        if (!response.ok) {
            const errorData = await response.json().catch(()=>({}));
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: errorData.detail || `Errore backend: ${response.status}`,
                detail: errorData.detail
            }, {
                status: response.status
            });
        }
        const data = await response.json();
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(data);
    } catch (error) {
        console.error('Errore nel collegamento cavo:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Errore interno del server',
            detail: error.message
        }, {
            status: 500
        });
    }
}
async function DELETE(request, { params }) {
    try {
        const { cantiereId, cavoId } = await params;
        // Leggi il body per ottenere il lato da scollegare
        let body = {};
        try {
            body = await request.json();
        } catch  {
            // Se non c'è body, scollega entrambi i lati
            body = {};
        }
        // Validazione parametri
        if (!cantiereId || !cavoId) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Parametri cantiere ID e cavo ID richiesti'
            }, {
                status: 400
            });
        }
        // Ottieni il token di autenticazione dalla richiesta
        const authHeader = request.headers.get('authorization');
        if (!authHeader) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Token di autenticazione richiesto'
            }, {
                status: 401
            });
        }
        // Chiama l'API backend Python
        const backendUrl = process.env.BACKEND_URL || 'http://localhost:8000';
        // Il backend Python richiede il lato nel path URL
        // Se non è specificato un lato, scolleghiamo entrambi i lati
        if (body.lato) {
            // Scollega un lato specifico
            const url = `${backendUrl}/cavi/${cantiereId}/${cavoId}/collegamento/${body.lato}`;
            const response = await fetch(url, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': authHeader
                }
            });
            if (!response.ok) {
                const errorData = await response.json().catch(()=>({}));
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: errorData.detail || `Errore backend: ${response.status}`,
                    detail: errorData.detail
                }, {
                    status: response.status
                });
            }
            const data = await response.json();
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(data);
        } else {
            // Scollega entrambi i lati (prima partenza, poi arrivo)
            let finalData = null;
            // Scollega partenza
            try {
                const urlPartenza = `${backendUrl}/cavi/${cantiereId}/${cavoId}/collegamento/partenza`;
                const responsePartenza = await fetch(urlPartenza, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': authHeader
                    }
                });
                if (responsePartenza.ok) {
                    finalData = await responsePartenza.json();
                }
            } catch (error) {
            // Ignora errori se il lato partenza non è collegato
            }
            // Scollega arrivo
            try {
                const urlArrivo = `${backendUrl}/cavi/${cantiereId}/${cavoId}/collegamento/arrivo`;
                const responseArrivo = await fetch(urlArrivo, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': authHeader
                    }
                });
                if (responseArrivo.ok) {
                    finalData = await responseArrivo.json();
                }
            } catch (error) {
            // Ignora errori se il lato arrivo non è collegato
            }
            if (finalData) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(finalData);
            } else {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Nessun collegamento da scollegare'
                }, {
                    status: 400
                });
            }
        }
    } catch (error) {
        console.error('Errore nello scollegamento cavo:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Errore interno del server',
            detail: error.message
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__418aeb65._.js.map