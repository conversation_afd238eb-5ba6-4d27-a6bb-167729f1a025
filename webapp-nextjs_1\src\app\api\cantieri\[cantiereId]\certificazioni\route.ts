import { NextRequest, NextResponse } from 'next/server'

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:8001'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ cantiereId: string }> }
) {
  try {
    const { cantiereId } = await params
    console.log('🔄 Certificazioni API: Proxying GET request to backend:', `${BACKEND_URL}/api/cantieri/${cantiereId}/certificazioni`)
    
    // Ottieni il token dall'header Authorization
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json({ error: 'Token di autorizzazione mancante' }, { status: 401 })
    }

    // Ottieni i parametri di query
    const { searchParams } = new URL(request.url)
    const queryString = searchParams.toString()
    const backendUrl = `${BACKEND_URL}/api/cantieri/${cantiereId}/certificazioni${queryString ? `?${queryString}` : ''}`

    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Authorization': authHeader,
        'Content-Type': 'application/json',
      },
    })

    console.log('📡 Certificazioni API: Backend response status:', response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ Certificazioni API: Backend error:', errorText)
      return NextResponse.json(
        { error: 'Errore dal backend', details: errorText },
        { status: response.status }
      )
    }

    const data = await response.json()
    console.log('📡 Certificazioni API: Backend response data:', data)

    return NextResponse.json(data)
  } catch (error) {
    console.error('❌ Certificazioni API: Error:', error)
    return NextResponse.json(
      { error: 'Errore interno del server', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ cantiereId: string }> }
) {
  try {
    const { cantiereId } = await params
    console.log('🔄 Certificazioni API: Proxying POST request to backend:', `${BACKEND_URL}/api/cantieri/${cantiereId}/certificazioni`)

    // Ottieni il token dall'header Authorization
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json({ error: 'Token di autorizzazione mancante' }, { status: 401 })
    }

    // Ottieni il body della richiesta
    const body = await request.json()
    console.log('📡 Certificazioni API: Request body:', JSON.stringify(body, null, 2))
    console.log('📡 Certificazioni API: Sending to backend URL:', `${BACKEND_URL}/api/cantieri/${cantiereId}/certificazioni`)

    const response = await fetch(`${BACKEND_URL}/api/cantieri/${cantiereId}/certificazioni`, {
      method: 'POST',
      headers: {
        'Authorization': authHeader,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    })

    console.log('📡 Certificazioni API: Backend response status:', response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ Certificazioni API: Backend error:', errorText)
      return NextResponse.json(
        { error: 'Errore dal backend', details: errorText },
        { status: response.status }
      )
    }

    const data = await response.json()
    console.log('📡 Certificazioni API: Backend response data:', data)

    return NextResponse.json(data)
  } catch (error) {
    console.error('❌ Certificazioni API: Error:', error)
    return NextResponse.json(
      { error: 'Errore interno del server', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
