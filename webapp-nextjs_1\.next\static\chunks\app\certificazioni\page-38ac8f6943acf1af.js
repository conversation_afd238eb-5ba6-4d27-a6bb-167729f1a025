(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9992],{16785:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},17313:(e,s,a)=>{"use strict";a.d(s,{Xi:()=>o,av:()=>d,j7:()=>c,tU:()=>r});var t=a(95155),i=a(12115),l=a(60704),n=a(59434);let r=l.bL,c=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,t.jsx)(l.B8,{ref:s,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...i})});c.displayName=l.B8.displayName;let o=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,t.jsx)(l.l9,{ref:s,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...i})});o.displayName=l.l9.displayName;let d=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,t.jsx)(l.UC,{ref:s,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...i})});d.displayName=l.UC.displayName},17522:(e,s,a)=>{"use strict";a.d(s,{jV:()=>l});var t=a(12115),i=a(40283);function l(){let{cantiere:e,isLoading:s}=(0,i.A)(),[a,l]=(0,t.useState)(null),[n,r]=(0,t.useState)(!0),[c,o]=(0,t.useState)(null),d=e=>{if(null==e)return!1;let s="string"==typeof e?parseInt(e,10):e;return!isNaN(s)&&!(s<=0)||(console.warn("\uD83C\uDFD7️ useCantiere: ID cantiere non valido:",e),!1)};return(0,t.useEffect)(()=>{if(s)return void console.log("\uD83C\uDFD7️ useCantiere: Autenticazione in corso...");r(!0),o(null);try{let s=null;if((null==e?void 0:e.id_cantiere)&&d(e.id_cantiere))s=e.id_cantiere,console.log("\uD83C\uDFD7️ useCantiere: Usando cantiere dal context (login cantiere):",s);else{let e=localStorage.getItem("cantiere_data");if(e)try{let a=JSON.parse(e);a.id_cantiere&&d(a.id_cantiere)&&(s=a.id_cantiere,console.log("\uD83C\uDFD7️ useCantiere: Usando cantiere da cantiere_data:",s))}catch(e){console.warn("\uD83C\uDFD7️ useCantiere: Errore parsing cantiere_data:",e)}if(!s){let e=localStorage.getItem("selectedCantiereId");e&&d(e)&&(s=parseInt(e,10),console.log("\uD83C\uDFD7️ useCantiere: Usando cantiere da selectedCantiereId:",s))}}s?(l(s),console.log("\uD83C\uDFD7️ useCantiere: Cantiere valido impostato:",s)):(console.warn("\uD83C\uDFD7️ useCantiere: Nessun cantiere valido trovato"),l(null),o("Nessun cantiere selezionato. Seleziona un cantiere per continuare."))}catch(e){console.error("\uD83C\uDFD7️ useCantiere: Errore nella gestione cantiere:",e),o("Errore nella gestione del cantiere selezionato."),l(null)}finally{r(!1)}},[e,s]),{cantiereId:a,cantiere:e,isValidCantiere:null!==a&&a>0,isLoading:n,error:c,validateCantiere:d,clearError:()=>o(null)}}},24944:(e,s,a)=>{"use strict";a.d(s,{k:()=>n});var t=a(95155);a(12115);var i=a(55863),l=a(59434);function n(e){let{className:s,value:a,...n}=e;return(0,t.jsx)(i.bL,{"data-slot":"progress",className:(0,l.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",s),...n,children:(0,t.jsx)(i.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(a||0),"%)")}})})}},33109:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},55863:(e,s,a)=>{"use strict";a.d(s,{C1:()=>b,bL:()=>g});var t=a(12115),i=a(46081),l=a(63655),n=a(95155),r="Progress",[c,o]=(0,i.A)(r),[d,x]=c(r),m=t.forwardRef((e,s)=>{var a,t,i,r;let{__scopeProgress:c,value:o=null,max:x,getValueLabel:m=u,...h}=e;(x||0===x)&&!N(x)&&console.error((a="".concat(x),t="Progress","Invalid prop `max` of value `".concat(a,"` supplied to `").concat(t,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let j=N(x)?x:100;null===o||f(o,j)||console.error((i="".concat(o),r="Progress","Invalid prop `value` of value `".concat(i,"` supplied to `").concat(r,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let g=f(o,j)?o:null,b=p(g)?m(g,j):void 0;return(0,n.jsx)(d,{scope:c,value:g,max:j,children:(0,n.jsx)(l.sG.div,{"aria-valuemax":j,"aria-valuemin":0,"aria-valuenow":p(g)?g:void 0,"aria-valuetext":b,role:"progressbar","data-state":v(g,j),"data-value":null!=g?g:void 0,"data-max":j,...h,ref:s})})});m.displayName=r;var h="ProgressIndicator",j=t.forwardRef((e,s)=>{var a;let{__scopeProgress:t,...i}=e,r=x(h,t);return(0,n.jsx)(l.sG.div,{"data-state":v(r.value,r.max),"data-value":null!=(a=r.value)?a:void 0,"data-max":r.max,...i,ref:s})});function u(e,s){return"".concat(Math.round(e/s*100),"%")}function v(e,s){return null==e?"indeterminate":e===s?"complete":"loading"}function p(e){return"number"==typeof e}function N(e){return p(e)&&!isNaN(e)&&e>0}function f(e,s){return p(e)&&!isNaN(e)&&e<=s&&e>=0}j.displayName=h;var g=m,b=j},60704:(e,s,a)=>{"use strict";a.d(s,{B8:()=>I,UC:()=>R,bL:()=>S,l9:()=>E});var t=a(12115),i=a(85185),l=a(46081),n=a(89196),r=a(28905),c=a(63655),o=a(94315),d=a(5845),x=a(61285),m=a(95155),h="Tabs",[j,u]=(0,l.A)(h,[n.RG]),v=(0,n.RG)(),[p,N]=j(h),f=t.forwardRef((e,s)=>{let{__scopeTabs:a,value:t,onValueChange:i,defaultValue:l,orientation:n="horizontal",dir:r,activationMode:j="automatic",...u}=e,v=(0,o.jH)(r),[N,f]=(0,d.i)({prop:t,onChange:i,defaultProp:null!=l?l:"",caller:h});return(0,m.jsx)(p,{scope:a,baseId:(0,x.B)(),value:N,onValueChange:f,orientation:n,dir:v,activationMode:j,children:(0,m.jsx)(c.sG.div,{dir:v,"data-orientation":n,...u,ref:s})})});f.displayName=h;var g="TabsList",b=t.forwardRef((e,s)=>{let{__scopeTabs:a,loop:t=!0,...i}=e,l=N(g,a),r=v(a);return(0,m.jsx)(n.bL,{asChild:!0,...r,orientation:l.orientation,dir:l.dir,loop:t,children:(0,m.jsx)(c.sG.div,{role:"tablist","aria-orientation":l.orientation,...i,ref:s})})});b.displayName=g;var w="TabsTrigger",z=t.forwardRef((e,s)=>{let{__scopeTabs:a,value:t,disabled:l=!1,...r}=e,o=N(w,a),d=v(a),x=A(o.baseId,t),h=C(o.baseId,t),j=t===o.value;return(0,m.jsx)(n.q7,{asChild:!0,...d,focusable:!l,active:j,children:(0,m.jsx)(c.sG.button,{type:"button",role:"tab","aria-selected":j,"aria-controls":h,"data-state":j?"active":"inactive","data-disabled":l?"":void 0,disabled:l,id:x,...r,ref:s,onMouseDown:(0,i.m)(e.onMouseDown,e=>{l||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(t)}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(t)}),onFocus:(0,i.m)(e.onFocus,()=>{let e="manual"!==o.activationMode;j||l||!e||o.onValueChange(t)})})})});z.displayName=w;var _="TabsContent",y=t.forwardRef((e,s)=>{let{__scopeTabs:a,value:i,forceMount:l,children:n,...o}=e,d=N(_,a),x=A(d.baseId,i),h=C(d.baseId,i),j=i===d.value,u=t.useRef(j);return t.useEffect(()=>{let e=requestAnimationFrame(()=>u.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,m.jsx)(r.C,{present:l||j,children:a=>{let{present:t}=a;return(0,m.jsx)(c.sG.div,{"data-state":j?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":x,hidden:!t,id:h,tabIndex:0,...o,ref:s,style:{...e.style,animationDuration:u.current?"0s":void 0},children:t&&n})}})});function A(e,s){return"".concat(e,"-trigger-").concat(s)}function C(e,s){return"".concat(e,"-content-").concat(s)}y.displayName=_;var S=f,I=b,E=z,R=y},64531:(e,s,a)=>{Promise.resolve().then(a.bind(a,73015))},73015:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>$});var t=a(95155),i=a(40283),l=a(17522),n=a(12115),r=a(66695),c=a(30285),o=a(26126),d=a(62523),x=a(17313),m=a(85127),h=a(25731),j=a(51154),u=a(69037),v=a(57434),p=a(381),N=a(72713),f=a(1243),g=a(47924),b=a(84616),w=a(40646),z=a(85339),_=a(14186),y=a(13717),A=a(62525),C=a(74782),S=a(55365),I=a(73155);function E(e){let{cantiereId:s,strumenti:a,onUpdate:i}=e,[l,x]=(0,n.useState)(""),[u,v]=(0,n.useState)(!1),[N,f]=(0,n.useState)(null),[C,E]=(0,n.useState)(!1),[R,T]=(0,n.useState)(""),D=e=>{f(e),v(!0)},Z=async e=>{if(confirm("Sei sicuro di voler eliminare questo strumento?"))try{E(!0),await h.kw.deleteStrumento(s,e),i()}catch(e){var a,t;T((null==(t=e.response)||null==(a=t.data)?void 0:a.detail)||"Errore durante l'eliminazione")}finally{E(!1)}},O=e=>{let s=new Date,a=Math.ceil((new Date(e.data_scadenza_calibrazione).getTime()-s.getTime())/864e5);return"FUORI_SERVIZIO"===e.stato_strumento?(0,t.jsx)(o.E,{className:"bg-gray-100 text-gray-800 border-gray-200",children:"Fuori Servizio"}):a<0?(0,t.jsx)(o.E,{className:"bg-red-100 text-red-800 border-red-200",children:"Scaduto"}):a<=30?(0,t.jsx)(o.E,{className:"bg-yellow-100 text-yellow-800 border-yellow-200",children:"In Scadenza"}):(0,t.jsx)(o.E,{className:"bg-green-100 text-green-800 border-green-200",children:"Attivo"})},F=e=>{let s=new Date,a=Math.ceil((new Date(e.data_scadenza_calibrazione).getTime()-s.getTime())/864e5);return"FUORI_SERVIZIO"===e.stato_strumento||a<0?(0,t.jsx)(z.A,{className:"h-4 w-4 text-red-500"}):a<=30?(0,t.jsx)(_.A,{className:"h-4 w-4 text-yellow-500"}):(0,t.jsx)(w.A,{className:"h-4 w-4 text-green-500"})},L=a.filter(e=>{var s,a,t,i;let n=l.toLowerCase();return(null==(s=e.nome)?void 0:s.toLowerCase().includes(n))||(null==(a=e.marca)?void 0:a.toLowerCase().includes(n))||(null==(t=e.modello)?void 0:t.toLowerCase().includes(n))||(null==(i=e.numero_serie)?void 0:i.toLowerCase().includes(n))}),B={totali:a.length,attivi:a.filter(e=>{let s=new Date,a=new Date(e.data_scadenza_calibrazione);return"ATTIVO"===e.stato_strumento&&a>s}).length,scaduti:a.filter(e=>{let s=new Date;return new Date(e.data_scadenza_calibrazione)<=s}).length,in_scadenza:a.filter(e=>{let s=new Date,a=Math.ceil((new Date(e.data_scadenza_calibrazione).getTime()-s.getTime())/864e5);return a>0&&a<=30}).length};return u?(0,t.jsx)(I.A,{cantiereId:s,strumento:N,onSuccess:()=>{v(!1),i()},onCancel:()=>v(!1)}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold text-slate-900 flex items-center gap-3",children:[(0,t.jsx)(p.A,{className:"h-6 w-6 text-blue-600"}),"Gestione Strumenti"]}),(0,t.jsx)("p",{className:"text-slate-600 mt-1",children:"Strumenti di misura e calibrazione"})]}),(0,t.jsxs)(c.$,{onClick:()=>{f(null),v(!0)},children:[(0,t.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Nuovo Strumento"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsx)(r.Zp,{children:(0,t.jsx)(r.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Totali"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:B.totali})]}),(0,t.jsx)(p.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,t.jsx)(r.Zp,{children:(0,t.jsx)(r.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Attivi"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-green-600",children:B.attivi})]}),(0,t.jsx)(w.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,t.jsx)(r.Zp,{children:(0,t.jsx)(r.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"In Scadenza"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:B.in_scadenza})]}),(0,t.jsx)(_.A,{className:"h-8 w-8 text-yellow-500"})]})})}),(0,t.jsx)(r.Zp,{children:(0,t.jsx)(r.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Scaduti"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-red-600",children:B.scaduti})]}),(0,t.jsx)(z.A,{className:"h-8 w-8 text-red-500"})]})})})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(g.A,{className:"h-5 w-5"}),"Ricerca Strumenti"]})}),(0,t.jsx)(r.Wu,{children:(0,t.jsx)(d.p,{placeholder:"Cerca per nome, marca, modello o numero serie...",value:l,onChange:e=>x(e.target.value)})})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsxs)(r.aR,{children:[(0,t.jsxs)(r.ZB,{children:["Elenco Strumenti (",L.length,")"]}),(0,t.jsx)(r.BT,{children:"Gestione strumenti di misura e certificazione"})]}),(0,t.jsx)(r.Wu,{children:(0,t.jsx)("div",{className:"rounded-md border",children:(0,t.jsxs)(m.XI,{children:[(0,t.jsx)(m.A0,{children:(0,t.jsxs)(m.Hj,{children:[(0,t.jsx)(m.nd,{children:"Nome"}),(0,t.jsx)(m.nd,{children:"Marca/Modello"}),(0,t.jsx)(m.nd,{children:"Numero Serie"}),(0,t.jsx)(m.nd,{children:"Tipo"}),(0,t.jsx)(m.nd,{children:"Calibrazione"}),(0,t.jsx)(m.nd,{children:"Scadenza"}),(0,t.jsx)(m.nd,{children:"Stato"}),(0,t.jsx)(m.nd,{children:"Azioni"})]})}),(0,t.jsx)(m.BF,{children:C?(0,t.jsx)(m.Hj,{children:(0,t.jsx)(m.nA,{colSpan:8,className:"text-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 animate-spin"}),"Caricamento..."]})})}):0===L.length?(0,t.jsx)(m.Hj,{children:(0,t.jsx)(m.nA,{colSpan:8,className:"text-center py-8 text-slate-500",children:"Nessuno strumento trovato"})}):L.map(e=>{let s=new Date,a=Math.ceil((new Date(e.data_scadenza_calibrazione).getTime()-s.getTime())/864e5);return(0,t.jsxs)(m.Hj,{children:[(0,t.jsx)(m.nA,{className:"font-medium",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[F(e),e.nome]})}),(0,t.jsx)(m.nA,{children:(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.marca}),(0,t.jsx)("div",{className:"text-sm text-slate-500",children:e.modello})]})}),(0,t.jsx)(m.nA,{className:"font-mono text-sm",children:e.numero_serie}),(0,t.jsx)(m.nA,{children:(0,t.jsx)(o.E,{variant:"outline",children:e.tipo_strumento||"N/A"})}),(0,t.jsx)(m.nA,{children:new Date(e.data_calibrazione).toLocaleDateString("it-IT")}),(0,t.jsx)(m.nA,{children:(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{children:new Date(e.data_scadenza_calibrazione).toLocaleDateString("it-IT")}),a>0&&a<=30&&(0,t.jsxs)("div",{className:"text-xs text-yellow-600",children:[a," giorni rimanenti"]}),a<0&&(0,t.jsxs)("div",{className:"text-xs text-red-600",children:["Scaduto da ",Math.abs(a)," giorni"]})]})}),(0,t.jsx)(m.nA,{children:O(e)}),(0,t.jsx)(m.nA,{children:(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>D(e),children:(0,t.jsx)(y.A,{className:"h-4 w-4"})}),(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>Z(e.id_strumento),children:(0,t.jsx)(A.A,{className:"h-4 w-4"})})]})})]},e.id_strumento)})})]})})})]}),R&&(0,t.jsxs)(S.Fc,{className:"border-red-200 bg-red-50",children:[(0,t.jsx)(z.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)(S.TN,{className:"text-red-800",children:R})]})]})}var R=a(53904),T=a(91788);function D(e){let{cantiereId:s,rapporti:a,onUpdate:i}=e,[l,x]=(0,n.useState)(""),[u,p]=(0,n.useState)(!1),[f,_]=(0,n.useState)(""),C=e=>{console.log("Modifica rapporto",e)},I=async e=>{if(confirm("Sei sicuro di voler eliminare questo rapporto?"))try{p(!0),await h.l9.deleteRapporto(s,e),i()}catch(e){var a,t;_((null==(t=e.response)||null==(a=t.data)?void 0:a.detail)||"Errore durante l'eliminazione")}finally{p(!1)}},E=async e=>{try{p(!0),await h.l9.aggiornaStatistiche(s,e),i()}catch(e){var a,t;_((null==(t=e.response)||null==(a=t.data)?void 0:a.detail)||"Errore durante l'aggiornamento")}finally{p(!1)}},D=e=>{switch(null==e?void 0:e.toLowerCase()){case"completato":return(0,t.jsx)(o.E,{className:"bg-green-100 text-green-800 border-green-200",children:"Completato"});case"approvato":return(0,t.jsx)(o.E,{className:"bg-blue-100 text-blue-800 border-blue-200",children:"Approvato"});case"bozza":return(0,t.jsx)(o.E,{className:"bg-gray-100 text-gray-800 border-gray-200",children:"Bozza"});default:return(0,t.jsx)(o.E,{className:"bg-gray-100 text-gray-800 border-gray-200",children:"Da Verificare"})}},Z=e=>{if(0===e.numero_cavi_totali)return(0,t.jsx)(o.E,{variant:"outline",children:"Nessun Cavo"});let s=e.numero_cavi_conformi/e.numero_cavi_totali*100;return 100===s?(0,t.jsx)(o.E,{className:"bg-green-100 text-green-800 border-green-200",children:"100% Conforme"}):s>=90?(0,t.jsxs)(o.E,{className:"bg-yellow-100 text-yellow-800 border-yellow-200",children:[s.toFixed(1),"% Conforme"]}):(0,t.jsxs)(o.E,{className:"bg-red-100 text-red-800 border-red-200",children:[s.toFixed(1),"% Conforme"]})},O=a.filter(e=>{var s,a,t;let i=l.toLowerCase();return(null==(s=e.numero_rapporto)?void 0:s.toLowerCase().includes(i))||(null==(a=e.nome_progetto)?void 0:a.toLowerCase().includes(i))||(null==(t=e.cliente_finale)?void 0:t.toLowerCase().includes(i))}),F={totali:a.length,completati:a.filter(e=>"COMPLETATO"===e.stato_rapporto).length,approvati:a.filter(e=>"APPROVATO"===e.stato_rapporto).length,bozze:a.filter(e=>"BOZZA"===e.stato_rapporto).length,cavi_totali:a.reduce((e,s)=>e+s.numero_cavi_totali,0),cavi_conformi:a.reduce((e,s)=>e+s.numero_cavi_conformi,0)};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold text-slate-900 flex items-center gap-3",children:[(0,t.jsx)(N.A,{className:"h-6 w-6 text-blue-600"}),"Rapporti Generali di Collaudo"]}),(0,t.jsx)("p",{className:"text-slate-600 mt-1",children:"Gestione rapporti generali CEI 64-8"})]}),(0,t.jsxs)(c.$,{onClick:()=>{console.log("Crea nuovo rapporto")},children:[(0,t.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Nuovo Rapporto"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,t.jsx)(r.Zp,{children:(0,t.jsx)(r.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Totali"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:F.totali})]}),(0,t.jsx)(v.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,t.jsx)(r.Zp,{children:(0,t.jsx)(r.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Completati"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-green-600",children:F.completati})]}),(0,t.jsx)(w.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,t.jsx)(r.Zp,{children:(0,t.jsx)(r.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Approvati"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:F.approvati})]}),(0,t.jsx)(N.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,t.jsx)(r.Zp,{children:(0,t.jsx)(r.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Cavi Totali"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:F.cavi_totali})]}),(0,t.jsx)(v.A,{className:"h-8 w-8 text-slate-500"})]})})}),(0,t.jsx)(r.Zp,{children:(0,t.jsx)(r.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Conformit\xe0"}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-green-600",children:[F.cavi_totali>0?(F.cavi_conformi/F.cavi_totali*100).toFixed(1):0,"%"]})]}),(0,t.jsx)(w.A,{className:"h-8 w-8 text-green-500"})]})})})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(g.A,{className:"h-5 w-5"}),"Ricerca Rapporti"]})}),(0,t.jsx)(r.Wu,{children:(0,t.jsx)(d.p,{placeholder:"Cerca per numero rapporto, progetto o cliente...",value:l,onChange:e=>x(e.target.value)})})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsxs)(r.aR,{children:[(0,t.jsxs)(r.ZB,{children:["Elenco Rapporti (",O.length,")"]}),(0,t.jsx)(r.BT,{children:"Gestione rapporti generali di collaudo"})]}),(0,t.jsx)(r.Wu,{children:(0,t.jsx)("div",{className:"rounded-md border",children:(0,t.jsxs)(m.XI,{children:[(0,t.jsx)(m.A0,{children:(0,t.jsxs)(m.Hj,{children:[(0,t.jsx)(m.nd,{children:"Numero Rapporto"}),(0,t.jsx)(m.nd,{children:"Progetto"}),(0,t.jsx)(m.nd,{children:"Cliente"}),(0,t.jsx)(m.nd,{children:"Data"}),(0,t.jsx)(m.nd,{children:"Cavi"}),(0,t.jsx)(m.nd,{children:"Conformit\xe0"}),(0,t.jsx)(m.nd,{children:"Stato"}),(0,t.jsx)(m.nd,{children:"Azioni"})]})}),(0,t.jsx)(m.BF,{children:u?(0,t.jsx)(m.Hj,{children:(0,t.jsx)(m.nA,{colSpan:8,className:"text-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 animate-spin"}),"Caricamento..."]})})}):0===O.length?(0,t.jsx)(m.Hj,{children:(0,t.jsx)(m.nA,{colSpan:8,className:"text-center py-8 text-slate-500",children:"Nessun rapporto trovato"})}):O.map(e=>(0,t.jsxs)(m.Hj,{children:[(0,t.jsx)(m.nA,{className:"font-medium",children:e.numero_rapporto}),(0,t.jsx)(m.nA,{children:(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.nome_progetto||"-"}),(0,t.jsx)("div",{className:"text-sm text-slate-500",children:e.societa_installatrice||"-"})]})}),(0,t.jsx)(m.nA,{children:e.cliente_finale||"-"}),(0,t.jsx)(m.nA,{children:new Date(e.data_rapporto).toLocaleDateString("it-IT")}),(0,t.jsx)(m.nA,{children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"font-medium",children:e.numero_cavi_totali}),(0,t.jsxs)("div",{className:"text-xs text-slate-500",children:[e.numero_cavi_conformi,"C / ",e.numero_cavi_non_conformi,"NC"]})]})}),(0,t.jsx)(m.nA,{children:Z(e)}),(0,t.jsx)(m.nA,{children:D(e.stato_rapporto)}),(0,t.jsx)(m.nA,{children:(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>C(e),title:"Visualizza/Modifica",children:(0,t.jsx)(y.A,{className:"h-4 w-4"})}),(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>E(e.id_rapporto),title:"Aggiorna Statistiche",children:(0,t.jsx)(R.A,{className:"h-4 w-4"})}),(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>{},title:"Genera PDF",children:(0,t.jsx)(T.A,{className:"h-4 w-4"})}),(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>I(e.id_rapporto),title:"Elimina",children:(0,t.jsx)(A.A,{className:"h-4 w-4"})})]})})]},e.id_rapporto))})]})})})]}),f&&(0,t.jsxs)(S.Fc,{className:"border-red-200 bg-red-50",children:[(0,t.jsx)(z.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)(S.TN,{className:"text-red-800",children:f})]})]})}var Z=a(24944),O=a(16785),F=a(33109);function L(e){let{stats:s,detailed:a=!1}=e,i=s.totali>0?s.conformi/s.totali*100:0,l=s.totali>0?(s.conformi+s.non_conformi+s.con_riserva)/s.totali*100:0;return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,t.jsx)(r.Zp,{children:(0,t.jsx)(r.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Totali"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:s.totali})]}),(0,t.jsx)(v.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,t.jsx)(r.Zp,{children:(0,t.jsx)(r.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Conformi"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-green-600",children:s.conformi}),(0,t.jsx)("p",{className:"text-xs text-slate-500",children:s.totali>0?"".concat((s.conformi/s.totali*100).toFixed(1),"%"):"0%"})]}),(0,t.jsx)(w.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,t.jsx)(r.Zp,{children:(0,t.jsx)(r.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Non Conformi"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-red-600",children:s.non_conformi}),(0,t.jsx)("p",{className:"text-xs text-slate-500",children:s.totali>0?"".concat((s.non_conformi/s.totali*100).toFixed(1),"%"):"0%"})]}),(0,t.jsx)(z.A,{className:"h-8 w-8 text-red-500"})]})})}),(0,t.jsx)(r.Zp,{children:(0,t.jsx)(r.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Bozze"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:s.bozze}),(0,t.jsx)("p",{className:"text-xs text-slate-500",children:s.totali>0?"".concat((s.bozze/s.totali*100).toFixed(1),"%"):"0%"})]}),(0,t.jsx)(_.A,{className:"h-8 w-8 text-yellow-500"})]})})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)(r.Zp,{children:[(0,t.jsxs)(r.aR,{children:[(0,t.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(O.A,{className:"h-5 w-5 text-blue-600"}),"Tasso di Conformit\xe0"]}),(0,t.jsx)(r.BT,{children:"Percentuale di certificazioni conformi sul totale"})]}),(0,t.jsx)(r.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Conformit\xe0"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[i>=95?(0,t.jsx)(w.A,{className:"h-5 w-5 text-green-600"}):i>=85?(0,t.jsx)(f.A,{className:"h-5 w-5 text-yellow-600"}):(0,t.jsx)(z.A,{className:"h-5 w-5 text-red-600"}),(0,t.jsxs)("span",{className:"text-lg font-bold ".concat(i>=95?"text-green-600":i>=85?"text-yellow-600":"text-red-600"),children:[i.toFixed(1),"%"]})]})]}),(0,t.jsx)(Z.k,{value:i,className:"h-2"}),(0,t.jsxs)("div",{className:"flex justify-between text-xs text-slate-500",children:[(0,t.jsx)("span",{children:"Target: 95%"}),(0,t.jsxs)("span",{children:[s.conformi," / ",s.totali]})]})]})})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsxs)(r.aR,{children:[(0,t.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(F.A,{className:"h-5 w-5 text-green-600"}),"Completamento Certificazioni"]}),(0,t.jsx)(r.BT,{children:"Percentuale di certificazioni completate (non bozze)"})]}),(0,t.jsx)(r.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Completamento"}),(0,t.jsxs)("span",{className:"text-lg font-bold text-blue-600",children:[l.toFixed(1),"%"]})]}),(0,t.jsx)(Z.k,{value:l,className:"h-2"}),(0,t.jsxs)("div",{className:"flex justify-between text-xs text-slate-500",children:[(0,t.jsxs)("span",{children:["Completate: ",s.conformi+s.non_conformi+s.con_riserva]}),(0,t.jsxs)("span",{children:["Bozze: ",s.bozze]})]})]})})]})]}),a&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsx)(r.ZB,{className:"text-lg",children:"Distribuzione Stati"})}),(0,t.jsx)(r.Wu,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),(0,t.jsx)("span",{className:"text-sm",children:"Conformi"})]}),(0,t.jsx)(o.E,{variant:"outline",className:"text-green-600 border-green-200",children:s.conformi})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-red-500 rounded-full"}),(0,t.jsx)("span",{className:"text-sm",children:"Non Conformi"})]}),(0,t.jsx)(o.E,{variant:"outline",className:"text-red-600 border-red-200",children:s.non_conformi})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-yellow-500 rounded-full"}),(0,t.jsx)("span",{className:"text-sm",children:"Con Riserva"})]}),(0,t.jsx)(o.E,{variant:"outline",className:"text-yellow-600 border-yellow-200",children:s.con_riserva})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-gray-500 rounded-full"}),(0,t.jsx)("span",{className:"text-sm",children:"Bozze"})]}),(0,t.jsx)(o.E,{variant:"outline",className:"text-gray-600 border-gray-200",children:s.bozze})]})]})})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsx)(r.ZB,{className:"text-lg",children:"Indicatori Qualit\xe0"})}),(0,t.jsx)(r.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,t.jsx)("span",{children:"Tasso Successo"}),(0,t.jsxs)("span",{className:"font-medium",children:[s.totali>0?((s.conformi+s.con_riserva)/s.totali*100).toFixed(1):0,"%"]})]}),(0,t.jsx)(Z.k,{value:s.totali>0?(s.conformi+s.con_riserva)/s.totali*100:0,className:"h-2"})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,t.jsx)("span",{children:"Tasso Fallimento"}),(0,t.jsxs)("span",{className:"font-medium text-red-600",children:[s.totali>0?(s.non_conformi/s.totali*100).toFixed(1):0,"%"]})]}),(0,t.jsx)(Z.k,{value:s.totali>0?s.non_conformi/s.totali*100:0,className:"h-2"})]})]})})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsxs)(r.ZB,{className:"text-lg flex items-center gap-2",children:[(0,t.jsx)(u.A,{className:"h-5 w-5 text-yellow-500"}),"Valutazione Complessiva"]})}),(0,t.jsx)(r.Wu,{children:(0,t.jsxs)("div",{className:"text-center space-y-2",children:[(0,t.jsx)("div",{className:"text-3xl font-bold",children:i>=95?"\uD83C\uDFC6":i>=85?"⭐":i>=70?"\uD83D\uDC4D":"⚠️"}),(0,t.jsx)("div",{className:"text-lg font-semibold",children:i>=95?"Eccellente":i>=85?"Buono":i>=70?"Sufficiente":"Da Migliorare"}),(0,t.jsx)("div",{className:"text-sm text-slate-600",children:i>=95?"Qualit\xe0 certificazioni ottimale":i>=85?"Qualit\xe0 certificazioni buona":i>=70?"Qualit\xe0 certificazioni accettabile":"Necessario miglioramento qualit\xe0"})]})})]})]}),(i<95||s.bozze>0)&&(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"h-5 w-5 text-yellow-600"}),"Raccomandazioni"]})}),(0,t.jsx)(r.Wu,{children:(0,t.jsxs)("div",{className:"space-y-2",children:[i<95&&(0,t.jsxs)("div",{className:"flex items-start gap-2 text-sm",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full mt-2"}),(0,t.jsxs)("span",{children:["Il tasso di conformit\xe0 \xe8 del ",i.toFixed(1),"%. Obiettivo raccomandato: 95%. Verificare procedure di test e qualit\xe0 installazioni."]})]}),s.bozze>0&&(0,t.jsxs)("div",{className:"flex items-start gap-2 text-sm",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2"}),(0,t.jsxs)("span",{children:["Ci sono ",s.bozze," certificazioni in bozza. Completare le certificazioni per avere dati accurati."]})]}),s.non_conformi>s.conformi&&(0,t.jsxs)("div",{className:"flex items-start gap-2 text-sm",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full mt-2"}),(0,t.jsx)("span",{children:"Il numero di certificazioni non conformi supera quelle conformi. Rivedere urgentemente le procedure di installazione e test."})]})]})})]})]})]})}var B=a(92657),k=a(85057),M=a(88539),W=a(59409),V=a(54416),U=a(4229);function G(e){let{cantiereId:s,nonConformita:a,onSuccess:i,onCancel:l}=e,[o,x]=(0,n.useState)({id_cavo:"",tipo_non_conformita:"",descrizione:"",severita:"MEDIA",stato:"APERTA",responsabile_rilevazione:"",data_rilevazione:new Date().toISOString().split("T")[0],azione_correttiva:"",responsabile_risoluzione:"",data_risoluzione:null,note:""}),[m,u]=(0,n.useState)([]),[v,p]=(0,n.useState)([]),[N,g]=(0,n.useState)(!1),[b,w]=(0,n.useState)(!1),[_,y]=(0,n.useState)(""),[A,C]=(0,n.useState)({}),I=!!a;(0,n.useEffect)(()=>{E(),a&&x({id_cavo:a.id_cavo||"",tipo_non_conformita:a.tipo_non_conformita||"",descrizione:a.descrizione||"",severita:a.severita||"MEDIA",stato:a.stato||"APERTA",responsabile_rilevazione:a.responsabile_rilevazione||"",data_rilevazione:a.data_rilevazione?new Date(a.data_rilevazione).toISOString().split("T")[0]:new Date().toISOString().split("T")[0],azione_correttiva:a.azione_correttiva||"",responsabile_risoluzione:a.responsabile_risoluzione||"",data_risoluzione:a.data_risoluzione?new Date(a.data_risoluzione).toISOString().split("T")[0]:null,note:a.note||""})},[a]);let E=async()=>{try{g(!0);let[e,a]=await Promise.all([h.At.getCavi(s),h.AR.getResponsabili(s)]);u(e),p(a)}catch(s){var e,a;y((null==(a=s.response)||null==(e=a.data)?void 0:e.detail)||"Errore durante il caricamento dei dati")}finally{g(!1)}},R=()=>{let e={};return o.id_cavo||(e.id_cavo="ID Cavo \xe8 obbligatorio"),o.tipo_non_conformita||(e.tipo_non_conformita="Tipo non conformit\xe0 \xe8 obbligatorio"),o.descrizione||(e.descrizione="Descrizione \xe8 obbligatoria"),o.responsabile_rilevazione||(e.responsabile_rilevazione="Responsabile rilevazione \xe8 obbligatorio"),C(e),0===Object.keys(e).length},T=async e=>{if(e.preventDefault(),R())try{w(!0),y("");let e={...o,data_risoluzione:o.data_risoluzione||null};I&&a?await h.om.updateNonConformita(s,a.id_non_conformita,e):await h.om.createNonConformita(s,e),i()}catch(e){var t,l;y((null==(l=e.response)||null==(t=l.data)?void 0:t.detail)||"Errore durante il salvataggio")}finally{w(!1)}},D=(e,s)=>{x(a=>({...a,[e]:s})),A[e]&&C(s=>{let a={...s};return delete a[e],a})};return N?(0,t.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 animate-spin"}),"Caricamento dati..."]})}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsxs)("div",{children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold text-slate-900 flex items-center gap-3",children:[(0,t.jsx)(f.A,{className:"h-6 w-6 text-red-600"}),I?"Modifica Non Conformit\xe0":"Nuova Non Conformit\xe0"]}),(0,t.jsx)("p",{className:"text-slate-600 mt-1",children:I?"Aggiorna i dettagli della non conformit\xe0":"Registra una nuova non conformit\xe0"})]})}),(0,t.jsxs)("form",{onSubmit:T,className:"space-y-6",children:[(0,t.jsxs)(r.Zp,{children:[(0,t.jsxs)(r.aR,{children:[(0,t.jsx)(r.ZB,{children:"Informazioni Base"}),(0,t.jsx)(r.BT,{children:"Dettagli principali della non conformit\xe0"})]}),(0,t.jsxs)(r.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(k.J,{htmlFor:"id_cavo",children:"ID Cavo *"}),(0,t.jsxs)(W.l6,{value:o.id_cavo,onValueChange:e=>D("id_cavo",e),children:[(0,t.jsx)(W.bq,{className:A.id_cavo?"border-red-500":"",children:(0,t.jsx)(W.yv,{placeholder:"Seleziona cavo"})}),(0,t.jsx)(W.gC,{children:m.map(e=>(0,t.jsxs)(W.eb,{value:e.id_cavo,children:[e.id_cavo," ",e.tipologia&&"- ".concat(e.tipologia)]},e.id_cavo))})]}),A.id_cavo&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:A.id_cavo})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(k.J,{htmlFor:"tipo_non_conformita",children:"Tipo Non Conformit\xe0 *"}),(0,t.jsxs)(W.l6,{value:o.tipo_non_conformita,onValueChange:e=>D("tipo_non_conformita",e),children:[(0,t.jsx)(W.bq,{className:A.tipo_non_conformita?"border-red-500":"",children:(0,t.jsx)(W.yv,{placeholder:"Seleziona tipo"})}),(0,t.jsxs)(W.gC,{children:[(0,t.jsx)(W.eb,{value:"ISOLAMENTO",children:"Isolamento"}),(0,t.jsx)(W.eb,{value:"CONTINUITA",children:"Continuit\xe0"}),(0,t.jsx)(W.eb,{value:"RESISTENZA",children:"Resistenza"}),(0,t.jsx)(W.eb,{value:"INSTALLAZIONE",children:"Installazione"}),(0,t.jsx)(W.eb,{value:"COLLEGAMENTO",children:"Collegamento"}),(0,t.jsx)(W.eb,{value:"DOCUMENTAZIONE",children:"Documentazione"}),(0,t.jsx)(W.eb,{value:"ALTRO",children:"Altro"})]})]}),A.tipo_non_conformita&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:A.tipo_non_conformita})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(k.J,{htmlFor:"severita",children:"Severit\xe0"}),(0,t.jsxs)(W.l6,{value:o.severita,onValueChange:e=>D("severita",e),children:[(0,t.jsx)(W.bq,{children:(0,t.jsx)(W.yv,{})}),(0,t.jsxs)(W.gC,{children:[(0,t.jsx)(W.eb,{value:"BASSA",children:"Bassa"}),(0,t.jsx)(W.eb,{value:"MEDIA",children:"Media"}),(0,t.jsx)(W.eb,{value:"ALTA",children:"Alta"}),(0,t.jsx)(W.eb,{value:"CRITICA",children:"Critica"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(k.J,{htmlFor:"stato",children:"Stato"}),(0,t.jsxs)(W.l6,{value:o.stato,onValueChange:e=>D("stato",e),children:[(0,t.jsx)(W.bq,{children:(0,t.jsx)(W.yv,{})}),(0,t.jsxs)(W.gC,{children:[(0,t.jsx)(W.eb,{value:"APERTA",children:"Aperta"}),(0,t.jsx)(W.eb,{value:"IN_RISOLUZIONE",children:"In Risoluzione"}),(0,t.jsx)(W.eb,{value:"RISOLTA",children:"Risolta"}),(0,t.jsx)(W.eb,{value:"CHIUSA",children:"Chiusa"})]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(k.J,{htmlFor:"descrizione",children:"Descrizione *"}),(0,t.jsx)(M.T,{id:"descrizione",value:o.descrizione,onChange:e=>D("descrizione",e.target.value),className:A.descrizione?"border-red-500":"",placeholder:"Descrivi dettagliatamente la non conformit\xe0 rilevata...",rows:3}),A.descrizione&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:A.descrizione})]})]})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsxs)(r.aR,{children:[(0,t.jsx)(r.ZB,{children:"Responsabili e Date"}),(0,t.jsx)(r.BT,{children:"Informazioni su rilevazione e risoluzione"})]}),(0,t.jsxs)(r.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(k.J,{htmlFor:"responsabile_rilevazione",children:"Responsabile Rilevazione *"}),(0,t.jsx)(d.p,{id:"responsabile_rilevazione",value:o.responsabile_rilevazione,onChange:e=>D("responsabile_rilevazione",e.target.value),className:A.responsabile_rilevazione?"border-red-500":"",placeholder:"Nome responsabile"}),A.responsabile_rilevazione&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:A.responsabile_rilevazione})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(k.J,{htmlFor:"data_rilevazione",children:"Data Rilevazione"}),(0,t.jsx)(d.p,{id:"data_rilevazione",type:"date",value:o.data_rilevazione,onChange:e=>D("data_rilevazione",e.target.value)})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(k.J,{htmlFor:"responsabile_risoluzione",children:"Responsabile Risoluzione"}),(0,t.jsx)(d.p,{id:"responsabile_risoluzione",value:o.responsabile_risoluzione,onChange:e=>D("responsabile_risoluzione",e.target.value),placeholder:"Nome responsabile risoluzione"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(k.J,{htmlFor:"data_risoluzione",children:"Data Risoluzione"}),(0,t.jsx)(d.p,{id:"data_risoluzione",type:"date",value:o.data_risoluzione||"",onChange:e=>D("data_risoluzione",e.target.value||null)})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(k.J,{htmlFor:"azione_correttiva",children:"Azione Correttiva"}),(0,t.jsx)(M.T,{id:"azione_correttiva",value:o.azione_correttiva,onChange:e=>D("azione_correttiva",e.target.value),placeholder:"Descrivi l'azione correttiva intrapresa...",rows:2})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(k.J,{htmlFor:"note",children:"Note"}),(0,t.jsx)(M.T,{id:"note",value:o.note,onChange:e=>D("note",e.target.value),placeholder:"Note aggiuntive...",rows:2})]})]})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,t.jsxs)(c.$,{type:"button",variant:"outline",onClick:l,children:[(0,t.jsx)(V.A,{className:"h-4 w-4 mr-2"}),"Annulla"]}),(0,t.jsx)(c.$,{type:"submit",disabled:b,children:b?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Salvataggio..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(U.A,{className:"h-4 w-4 mr-2"}),I?"Aggiorna":"Crea"," Non Conformit\xe0"]})})]}),_&&(0,t.jsxs)(S.Fc,{className:"border-red-200 bg-red-50",children:[(0,t.jsx)(z.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)(S.TN,{className:"text-red-800",children:_})]})]})]})}function H(e){let{cantiereId:s,nonConformita:a,onUpdate:i}=e,[l,x]=(0,n.useState)(""),[u,p]=(0,n.useState)("all"),[N,C]=(0,n.useState)(!1),[I,E]=(0,n.useState)(""),[R,T]=(0,n.useState)(!1),[D,Z]=(0,n.useState)(null),O=e=>{Z(e),T(!0)},F=async e=>{if(confirm("Sei sicuro di voler eliminare questa non conformit\xe0?"))try{C(!0),await h.om.deleteNonConformita(s,e),i()}catch(e){var a,t;E((null==(t=e.response)||null==(a=t.data)?void 0:a.detail)||"Errore durante l'eliminazione")}finally{C(!1)}},L=e=>{switch(null==e?void 0:e.toLowerCase()){case"aperta":return(0,t.jsx)(o.E,{className:"bg-red-100 text-red-800 border-red-200",children:"Aperta"});case"in_risoluzione":return(0,t.jsx)(o.E,{className:"bg-yellow-100 text-yellow-800 border-yellow-200",children:"In Risoluzione"});case"risolta":return(0,t.jsx)(o.E,{className:"bg-green-100 text-green-800 border-green-200",children:"Risolta"});case"chiusa":return(0,t.jsx)(o.E,{className:"bg-gray-100 text-gray-800 border-gray-200",children:"Chiusa"});default:return(0,t.jsx)(o.E,{className:"bg-gray-100 text-gray-800 border-gray-200",children:"Da Verificare"})}},k=e=>{switch(null==e?void 0:e.toLowerCase()){case"critica":return(0,t.jsx)(o.E,{className:"bg-red-100 text-red-800 border-red-200",children:"Critica"});case"alta":return(0,t.jsx)(o.E,{className:"bg-orange-100 text-orange-800 border-orange-200",children:"Alta"});case"media":return(0,t.jsx)(o.E,{className:"bg-yellow-100 text-yellow-800 border-yellow-200",children:"Media"});case"bassa":return(0,t.jsx)(o.E,{className:"bg-blue-100 text-blue-800 border-blue-200",children:"Bassa"});default:return(0,t.jsx)(o.E,{variant:"outline",children:"Non Specificata"})}},M=e=>{switch(null==e?void 0:e.toLowerCase()){case"aperta":return(0,t.jsx)(z.A,{className:"h-4 w-4 text-red-500"});case"in_risoluzione":return(0,t.jsx)(_.A,{className:"h-4 w-4 text-yellow-500"});case"risolta":case"chiusa":return(0,t.jsx)(w.A,{className:"h-4 w-4 text-green-500"});default:return(0,t.jsx)(f.A,{className:"h-4 w-4 text-gray-500"})}},W=a.filter(e=>{var s,a,t,i,n;let r=l.toLowerCase(),c=(null==(s=e.id_cavo)?void 0:s.toLowerCase().includes(r))||(null==(a=e.descrizione)?void 0:a.toLowerCase().includes(r))||(null==(t=e.tipo_non_conformita)?void 0:t.toLowerCase().includes(r))||(null==(i=e.responsabile_rilevazione)?void 0:i.toLowerCase().includes(r)),o=!0;return"all"!==u&&(o=(null==(n=e.stato)?void 0:n.toLowerCase())===u),c&&o}),V={totali:a.length,aperte:a.filter(e=>"APERTA"===e.stato).length,in_risoluzione:a.filter(e=>"IN_RISOLUZIONE"===e.stato).length,risolte:a.filter(e=>"RISOLTA"===e.stato||"CHIUSA"===e.stato).length,critiche:a.filter(e=>"CRITICA"===e.severita).length};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold text-slate-900 flex items-center gap-3",children:[(0,t.jsx)(f.A,{className:"h-6 w-6 text-red-600"}),"Gestione Non Conformit\xe0"]}),(0,t.jsx)("p",{className:"text-slate-600 mt-1",children:"Tracciamento e risoluzione delle non conformit\xe0"})]}),(0,t.jsxs)(c.$,{onClick:()=>{Z(null),T(!0)},children:[(0,t.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Nuova Non Conformit\xe0"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,t.jsx)(r.Zp,{children:(0,t.jsx)(r.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Totali"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:V.totali})]}),(0,t.jsx)(v.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,t.jsx)(r.Zp,{children:(0,t.jsx)(r.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Aperte"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-red-600",children:V.aperte})]}),(0,t.jsx)(z.A,{className:"h-8 w-8 text-red-500"})]})})}),(0,t.jsx)(r.Zp,{children:(0,t.jsx)(r.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"In Risoluzione"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:V.in_risoluzione})]}),(0,t.jsx)(_.A,{className:"h-8 w-8 text-yellow-500"})]})})}),(0,t.jsx)(r.Zp,{children:(0,t.jsx)(r.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Risolte"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-green-600",children:V.risolte})]}),(0,t.jsx)(w.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,t.jsx)(r.Zp,{children:(0,t.jsx)(r.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Critiche"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-red-600",children:V.critiche})]}),(0,t.jsx)(f.A,{className:"h-8 w-8 text-red-500"})]})})})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(g.A,{className:"h-5 w-5"}),"Ricerca e Filtri"]})}),(0,t.jsx)(r.Wu,{children:(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)(d.p,{placeholder:"Cerca per ID cavo, descrizione, tipo o responsabile...",value:l,onChange:e=>x(e.target.value)})}),(0,t.jsx)("div",{className:"flex gap-2",children:[{value:"all",label:"Tutte"},{value:"aperta",label:"Aperte"},{value:"in_risoluzione",label:"In Risoluzione"},{value:"risolta",label:"Risolte"},{value:"chiusa",label:"Chiuse"}].map(e=>(0,t.jsx)(c.$,{variant:u===e.value?"default":"outline",size:"sm",onClick:()=>p(e.value),children:e.label},e.value))})]})})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsxs)(r.aR,{children:[(0,t.jsxs)(r.ZB,{children:["Elenco Non Conformit\xe0 (",W.length,")"]}),(0,t.jsx)(r.BT,{children:"Gestione delle non conformit\xe0 rilevate durante le certificazioni"})]}),(0,t.jsx)(r.Wu,{children:(0,t.jsx)("div",{className:"rounded-md border",children:(0,t.jsxs)(m.XI,{children:[(0,t.jsx)(m.A0,{children:(0,t.jsxs)(m.Hj,{children:[(0,t.jsx)(m.nd,{children:"ID Cavo"}),(0,t.jsx)(m.nd,{children:"Tipo"}),(0,t.jsx)(m.nd,{children:"Descrizione"}),(0,t.jsx)(m.nd,{children:"Severit\xe0"}),(0,t.jsx)(m.nd,{children:"Data Rilevazione"}),(0,t.jsx)(m.nd,{children:"Responsabile"}),(0,t.jsx)(m.nd,{children:"Stato"}),(0,t.jsx)(m.nd,{children:"Azioni"})]})}),(0,t.jsx)(m.BF,{children:N?(0,t.jsx)(m.Hj,{children:(0,t.jsx)(m.nA,{colSpan:8,className:"text-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 animate-spin"}),"Caricamento..."]})})}):0===W.length?(0,t.jsx)(m.Hj,{children:(0,t.jsx)(m.nA,{colSpan:8,className:"text-center py-8 text-slate-500",children:"Nessuna non conformit\xe0 trovata"})}):W.map(e=>(0,t.jsxs)(m.Hj,{children:[(0,t.jsx)(m.nA,{className:"font-medium",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[M(e.stato),e.id_cavo]})}),(0,t.jsx)(m.nA,{children:(0,t.jsx)(o.E,{variant:"outline",children:e.tipo_non_conformita||"Non Specificato"})}),(0,t.jsx)(m.nA,{children:(0,t.jsxs)("div",{className:"max-w-xs",children:[(0,t.jsx)("div",{className:"font-medium truncate",title:e.descrizione,children:e.descrizione}),e.azione_correttiva&&(0,t.jsxs)("div",{className:"text-xs text-slate-500 truncate",title:e.azione_correttiva,children:["Azione: ",e.azione_correttiva]})]})}),(0,t.jsx)(m.nA,{children:k(e.severita)}),(0,t.jsx)(m.nA,{children:new Date(e.data_rilevazione).toLocaleDateString("it-IT")}),(0,t.jsx)(m.nA,{children:e.responsabile_rilevazione||"-"}),(0,t.jsx)(m.nA,{children:L(e.stato)}),(0,t.jsx)(m.nA,{children:(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>{},title:"Visualizza Dettagli",children:(0,t.jsx)(B.A,{className:"h-4 w-4"})}),(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>O(e),title:"Modifica",children:(0,t.jsx)(y.A,{className:"h-4 w-4"})}),(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>F(e.id_non_conformita),title:"Elimina",children:(0,t.jsx)(A.A,{className:"h-4 w-4"})})]})})]},e.id_non_conformita))})]})})})]}),I&&(0,t.jsxs)(S.Fc,{className:"border-red-200 bg-red-50",children:[(0,t.jsx)(z.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)(S.TN,{className:"text-red-800",children:I})]}),R&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,t.jsx)("div",{className:"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)(G,{cantiereId:s,nonConformita:D,onSuccess:()=>{T(!1),Z(null),i()},onCancel:()=>{T(!1),Z(null)}})})})})]})}function P(e){let{cantiereId:s}=e,[a,l]=(0,n.useState)("certificazioni"),[S,I]=(0,n.useState)(""),[R,T]=(0,n.useState)("all"),[Z,O]=(0,n.useState)([]),[F,B]=(0,n.useState)([]),[k,M]=(0,n.useState)([]),[W,V]=(0,n.useState)([]),[U,G]=(0,n.useState)(!0),[P,$]=(0,n.useState)(""),[q,J]=(0,n.useState)(!1),[X,Q]=(0,n.useState)(null),{user:K,cantiere:Y}=(0,i.A)();(0,n.useEffect)(()=>{s&&ee()},[s]);let ee=async()=>{try{G(!0),$("");let[e,a,t,i]=await Promise.all([h.km.getCertificazioni(s),h.kw.getStrumenti(s),h.l9.getRapporti(s),h.om.getNonConformita(s)]);O(e),B(a),M(t),V(i)}catch(s){var e,a;$((null==(a=s.response)||null==(e=a.data)?void 0:e.detail)||"Errore durante il caricamento dei dati")}finally{G(!1)}},es=e=>{Q(e),J(!0)},ea=async e=>{if(confirm("Sei sicuro di voler eliminare questa certificazione?"))try{await h.km.deleteCertificazione(s,e),ee()}catch(e){var a,t;$((null==(t=e.response)||null==(a=t.data)?void 0:a.detail)||"Errore durante l'eliminazione")}},et=e=>{switch(e){case"CONFORME":return(0,t.jsx)(o.E,{className:"bg-green-100 text-green-800 border-green-200",children:"Conforme"});case"NON_CONFORME":return(0,t.jsx)(o.E,{className:"bg-red-100 text-red-800 border-red-200",children:"Non Conforme"});case"BOZZA":return(0,t.jsx)(o.E,{className:"bg-gray-100 text-gray-800 border-gray-200",children:"Bozza"});case"IN_REVISIONE":return(0,t.jsx)(o.E,{className:"bg-yellow-100 text-yellow-800 border-yellow-200",children:"In Revisione"});default:return(0,t.jsx)(o.E,{variant:"outline",children:e})}},ei=Z.filter(e=>{var s,a;let t=!S||(null==(s=e.id_cavo)?void 0:s.toLowerCase().includes(S.toLowerCase()))||(null==(a=e.responsabile_certificazione)?void 0:a.toLowerCase().includes(S.toLowerCase())),i="all"===R||e.stato_certificato===R;return t&&i});return U?(0,t.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 animate-spin"}),"Caricamento certificazioni..."]})}):q?(0,t.jsx)(C.A,{cantiereId:s,certificazione:X,strumenti:F,onSuccess:()=>{J(!1),ee()},onCancel:()=>J(!1),onStrumentiUpdate:ee}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold text-slate-900 flex items-center gap-3",children:[(0,t.jsx)(u.A,{className:"h-8 w-8 text-blue-600"}),"Sistema Certificazioni CEI 64-8"]}),(0,t.jsx)("p",{className:"text-slate-600 mt-1",children:"Gestione completa certificazioni, strumenti e rapporti di collaudo"})]})}),(0,t.jsxs)(x.tU,{value:a,onValueChange:l,className:"space-y-6",children:[(0,t.jsxs)(x.j7,{className:"grid w-full grid-cols-5",children:[(0,t.jsxs)(x.Xi,{value:"certificazioni",className:"flex items-center gap-2",children:[(0,t.jsx)(v.A,{className:"h-4 w-4"}),"Certificazioni"]}),(0,t.jsxs)(x.Xi,{value:"strumenti",className:"flex items-center gap-2",children:[(0,t.jsx)(p.A,{className:"h-4 w-4"}),"Strumenti"]}),(0,t.jsxs)(x.Xi,{value:"rapporti",className:"flex items-center gap-2",children:[(0,t.jsx)(N.A,{className:"h-4 w-4"}),"Rapporti Generali"]}),(0,t.jsxs)(x.Xi,{value:"non-conformita",className:"flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),"Non Conformit\xe0"]}),(0,t.jsxs)(x.Xi,{value:"statistiche",className:"flex items-center gap-2",children:[(0,t.jsx)(u.A,{className:"h-4 w-4"}),"Statistiche"]})]}),(0,t.jsxs)(x.av,{value:"certificazioni",className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 flex-1",children:[(0,t.jsxs)("div",{className:"relative flex-1 max-w-md",children:[(0,t.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4"}),(0,t.jsx)(d.p,{placeholder:"Cerca per ID cavo o responsabile...",value:S,onChange:e=>I(e.target.value),className:"pl-10"})]}),(0,t.jsxs)("select",{value:R,onChange:e=>T(e.target.value),className:"px-3 py-2 border border-slate-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"all",children:"Tutti gli stati"}),(0,t.jsx)("option",{value:"CONFORME",children:"Conforme"}),(0,t.jsx)("option",{value:"NON_CONFORME",children:"Non Conforme"}),(0,t.jsx)("option",{value:"BOZZA",children:"Bozza"}),(0,t.jsx)("option",{value:"IN_REVISIONE",children:"In Revisione"})]})]}),(0,t.jsxs)(c.$,{onClick:()=>{Q(null),J(!0)},children:[(0,t.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Nuova Certificazione"]})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsxs)(r.aR,{children:[(0,t.jsx)(r.ZB,{children:"Certificazioni Cavi"}),(0,t.jsxs)(r.BT,{children:[ei.length," certificazioni trovate"]})]}),(0,t.jsx)(r.Wu,{children:(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)(m.XI,{children:[(0,t.jsx)(m.A0,{children:(0,t.jsxs)(m.Hj,{children:[(0,t.jsx)(m.nd,{children:"ID Cavo"}),(0,t.jsx)(m.nd,{children:"Data Certificazione"}),(0,t.jsx)(m.nd,{children:"Responsabile"}),(0,t.jsx)(m.nd,{children:"Stato"}),(0,t.jsx)(m.nd,{children:"Isolamento (MΩ)"}),(0,t.jsx)(m.nd,{children:"Esito"}),(0,t.jsx)(m.nd,{children:"Azioni"})]})}),(0,t.jsx)(m.BF,{children:0===ei.length?(0,t.jsx)(m.Hj,{children:(0,t.jsx)(m.nA,{colSpan:7,className:"text-center py-8 text-slate-500",children:"Nessuna certificazione trovata"})}):ei.map(e=>(0,t.jsxs)(m.Hj,{children:[(0,t.jsx)(m.nA,{className:"font-medium",children:e.id_cavo}),(0,t.jsx)(m.nA,{children:e.data_certificazione?new Date(e.data_certificazione).toLocaleDateString("it-IT"):"-"}),(0,t.jsx)(m.nA,{children:e.responsabile_certificazione||"-"}),(0,t.jsx)(m.nA,{children:et(e.stato_certificato||"BOZZA")}),(0,t.jsx)(m.nA,{children:e.valore_isolamento||"-"}),(0,t.jsx)(m.nA,{children:"CONFORME"===e.esito_complessivo?(0,t.jsx)(w.A,{className:"h-4 w-4 text-green-600"}):"NON_CONFORME"===e.esito_complessivo?(0,t.jsx)(z.A,{className:"h-4 w-4 text-red-600"}):(0,t.jsx)(_.A,{className:"h-4 w-4 text-yellow-600"})}),(0,t.jsx)(m.nA,{children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>es(e),children:(0,t.jsx)(y.A,{className:"h-4 w-4"})}),(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>ea(e.id_certificazione),children:(0,t.jsx)(A.A,{className:"h-4 w-4"})})]})})]},e.id_certificazione))})]})})})]})]}),(0,t.jsx)(x.av,{value:"strumenti",children:(0,t.jsx)(E,{cantiereId:s,strumenti:F,onUpdate:ee})}),(0,t.jsx)(x.av,{value:"rapporti",children:(0,t.jsx)(D,{cantiereId:s,rapporti:k,onUpdate:ee})}),(0,t.jsx)(x.av,{value:"non-conformita",children:(0,t.jsx)(H,{cantiereId:s,nonConformita:W,onUpdate:ee})}),(0,t.jsx)(x.av,{value:"statistiche",children:(0,t.jsx)(L,{cantiereId:s,certificazioni:Z,strumenti:F,nonConformita:W})})]}),P&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(z.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)("span",{className:"text-red-800",children:P})]})})]})}function $(){let{user:e}=(0,i.A)(),{cantiereId:s,isLoading:a,error:n}=(0,l.jV)();return a?(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,t.jsx)("div",{className:"max-w-[90%] mx-auto",children:(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,t.jsx)("p",{className:"mt-2 text-gray-600",children:"Caricamento cantiere..."})]})})}):n||!s?(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,t.jsx)("div",{className:"max-w-[90%] mx-auto",children:(0,t.jsxs)(S.Fc,{className:"border-red-200 bg-red-50",children:[(0,t.jsx)(z.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)(S.TN,{className:"text-red-800",children:n||"Cantiere non selezionato. Seleziona un cantiere per accedere alle certificazioni."})]})})}):(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,t.jsx)("div",{className:"max-w-[95%] mx-auto",children:(0,t.jsx)(P,{cantiereId:s})})})}},85127:(e,s,a)=>{"use strict";a.d(s,{A0:()=>n,BF:()=>r,Hj:()=>c,XI:()=>l,nA:()=>d,nd:()=>o});var t=a(95155);a(12115);var i=a(59434);function l(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,t.jsx)("table",{"data-slot":"table",className:(0,i.cn)("w-full caption-bottom text-sm border-collapse",s),...a})})}function n(e){let{className:s,...a}=e;return(0,t.jsx)("thead",{"data-slot":"table-header",className:(0,i.cn)("[&_tr]:border-b",s),...a})}function r(e){let{className:s,...a}=e;return(0,t.jsx)("tbody",{"data-slot":"table-body",className:(0,i.cn)("[&_tr:last-child]:border-0",s),...a})}function c(e){let{className:s,...a}=e;return(0,t.jsx)("tr",{"data-slot":"table-row",className:(0,i.cn)("data-[state=selected]:bg-muted border-b",s),...a})}function o(e){let{className:s,...a}=e;return(0,t.jsx)("th",{"data-slot":"table-head",className:(0,i.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...a})}function d(e){let{className:s,...a}=e;return(0,t.jsx)("td",{"data-slot":"table-cell",className:(0,i.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...a})}}},e=>{var s=s=>e(e.s=s);e.O(0,[3464,3455,1909,9384,6955,8578,283,2904,4782,8441,1684,7358],()=>s(64531)),_N_E=e.O()}]);