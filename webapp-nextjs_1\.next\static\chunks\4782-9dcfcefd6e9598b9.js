"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4782],{73155:(e,a,s)=>{s.d(a,{A:()=>g});var i=s(95155),t=s(12115),r=s(66695),l=s(30285),n=s(85057),c=s(62523),o=s(88539),d=s(59409),m=s(55365),x=s(381),h=s(85339),u=s(54416),j=s(51154),p=s(4229),v=s(25731);function g(e){let{cantiereId:a,strumento:s,onSuccess:g,onCancel:N}=e,[_,b]=(0,t.useState)({nome:"",marca:"",modello:"",numero_serie:"",data_calibrazione:"",data_scadenza_calibrazione:"",note:"",tipo_strumento:"MEGGER",ente_certificatore:"",numero_certificato_calibrazione:"",range_misura:"",precisione:"",stato_strumento:"ATTIVO"}),[f,y]=(0,t.useState)(!1),[z,C]=(0,t.useState)(""),[S,A]=(0,t.useState)({}),O=!!s;(0,t.useEffect)(()=>{s&&b({nome:s.nome,marca:s.marca,modello:s.modello,numero_serie:s.numero_serie,data_calibrazione:s.data_calibrazione.split("T")[0],data_scadenza_calibrazione:s.data_scadenza_calibrazione.split("T")[0],note:s.note||"",tipo_strumento:s.tipo_strumento||"MEGGER",ente_certificatore:s.ente_certificatore||"",numero_certificato_calibrazione:s.numero_certificato_calibrazione||"",range_misura:s.range_misura||"",precisione:s.precisione||"",stato_strumento:s.stato_strumento||"ATTIVO"})},[s]);let F=()=>{let e={};if(_.nome.trim()||(e.nome="Il nome \xe8 obbligatorio"),_.marca.trim()||(e.marca="La marca \xe8 obbligatoria"),_.modello.trim()||(e.modello="Il modello \xe8 obbligatorio"),_.numero_serie.trim()||(e.numero_serie="Il numero di serie \xe8 obbligatorio"),_.data_calibrazione||(e.data_calibrazione="La data di calibrazione \xe8 obbligatoria"),_.data_scadenza_calibrazione||(e.data_scadenza_calibrazione="La data di scadenza \xe8 obbligatoria"),_.data_calibrazione&&_.data_scadenza_calibrazione){let a=new Date(_.data_calibrazione);new Date(_.data_scadenza_calibrazione)<=a&&(e.data_scadenza_calibrazione="La data di scadenza deve essere successiva alla calibrazione")}return A(e),0===Object.keys(e).length},w=async e=>{if(e.preventDefault(),F())try{y(!0),C(""),O&&s?await v.kw.updateStrumento(a,s.id_strumento,_):await v.kw.createStrumento(a,_),g()}catch(e){var i,t;C((null==(t=e.response)||null==(i=t.data)?void 0:i.detail)||"Errore durante il salvataggio")}finally{y(!1)}},E=(e,a)=>{b(s=>({...s,[e]:a})),S[e]&&A(a=>{let s={...a};return delete s[e],s})},I=e=>{if(E("data_calibrazione",e),e&&!_.data_scadenza_calibrazione){let a=new Date(new Date(e));a.setFullYear(a.getFullYear()+1),E("data_scadenza_calibrazione",a.toISOString().split("T")[0])}};return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)("div",{className:"flex items-center justify-between",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("h2",{className:"text-2xl font-bold text-slate-900 flex items-center gap-3",children:[(0,i.jsx)(x.A,{className:"h-6 w-6 text-blue-600"}),O?"Modifica Strumento":"Nuovo Strumento"]}),(0,i.jsx)("p",{className:"text-slate-600 mt-1",children:O?"Modifica i dati dello strumento esistente":"Aggiungi un nuovo strumento di misura"})]})}),(0,i.jsxs)("form",{onSubmit:w,className:"space-y-6",children:[(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsx)(r.ZB,{children:"Informazioni Base"}),(0,i.jsx)(r.BT,{children:"Dati identificativi dello strumento"})]}),(0,i.jsx)(r.Wu,{className:"space-y-4",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(n.J,{htmlFor:"nome",children:"Nome Strumento *"}),(0,i.jsx)(c.p,{id:"nome",value:_.nome,onChange:e=>E("nome",e.target.value),className:S.nome?"border-red-500":"",placeholder:"es. Megger MFT1741"}),S.nome&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:S.nome})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(n.J,{htmlFor:"tipo_strumento",children:"Tipo Strumento"}),(0,i.jsxs)(d.l6,{value:_.tipo_strumento,onValueChange:e=>E("tipo_strumento",e),children:[(0,i.jsx)(d.bq,{children:(0,i.jsx)(d.yv,{})}),(0,i.jsxs)(d.gC,{children:[(0,i.jsx)(d.eb,{value:"MEGGER",children:"Megger"}),(0,i.jsx)(d.eb,{value:"MULTIMETRO",children:"Multimetro"}),(0,i.jsx)(d.eb,{value:"OSCILLOSCOPIO",children:"Oscilloscopio"}),(0,i.jsx)(d.eb,{value:"ALTRO",children:"Altro"})]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(n.J,{htmlFor:"marca",children:"Marca *"}),(0,i.jsx)(c.p,{id:"marca",value:_.marca,onChange:e=>E("marca",e.target.value),className:S.marca?"border-red-500":"",placeholder:"es. Fluke, Megger, Keysight"}),S.marca&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:S.marca})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(n.J,{htmlFor:"modello",children:"Modello *"}),(0,i.jsx)(c.p,{id:"modello",value:_.modello,onChange:e=>E("modello",e.target.value),className:S.modello?"border-red-500":"",placeholder:"es. MFT1741, 87V"}),S.modello&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:S.modello})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(n.J,{htmlFor:"numero_serie",children:"Numero Serie *"}),(0,i.jsx)(c.p,{id:"numero_serie",value:_.numero_serie,onChange:e=>E("numero_serie",e.target.value),className:S.numero_serie?"border-red-500":"",placeholder:"Numero di serie univoco"}),S.numero_serie&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:S.numero_serie})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(n.J,{htmlFor:"stato_strumento",children:"Stato"}),(0,i.jsxs)(d.l6,{value:_.stato_strumento,onValueChange:e=>E("stato_strumento",e),children:[(0,i.jsx)(d.bq,{children:(0,i.jsx)(d.yv,{})}),(0,i.jsxs)(d.gC,{children:[(0,i.jsx)(d.eb,{value:"ATTIVO",children:"Attivo"}),(0,i.jsx)(d.eb,{value:"SCADUTO",children:"Scaduto"}),(0,i.jsx)(d.eb,{value:"FUORI_SERVIZIO",children:"Fuori Servizio"})]})]})]})]})})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsx)(r.ZB,{children:"Calibrazione"}),(0,i.jsx)(r.BT,{children:"Informazioni sulla calibrazione dello strumento"})]}),(0,i.jsx)(r.Wu,{className:"space-y-4",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(n.J,{htmlFor:"data_calibrazione",children:"Data Calibrazione *"}),(0,i.jsx)(c.p,{id:"data_calibrazione",type:"date",value:_.data_calibrazione,onChange:e=>I(e.target.value),className:S.data_calibrazione?"border-red-500":""}),S.data_calibrazione&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:S.data_calibrazione})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(n.J,{htmlFor:"data_scadenza_calibrazione",children:"Data Scadenza *"}),(0,i.jsx)(c.p,{id:"data_scadenza_calibrazione",type:"date",value:_.data_scadenza_calibrazione,onChange:e=>E("data_scadenza_calibrazione",e.target.value),className:S.data_scadenza_calibrazione?"border-red-500":""}),S.data_scadenza_calibrazione&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:S.data_scadenza_calibrazione})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(n.J,{htmlFor:"ente_certificatore",children:"Ente Certificatore"}),(0,i.jsx)(c.p,{id:"ente_certificatore",value:_.ente_certificatore,onChange:e=>E("ente_certificatore",e.target.value),placeholder:"es. LAT 123, ACCREDIA"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(n.J,{htmlFor:"numero_certificato_calibrazione",children:"Numero Certificato"}),(0,i.jsx)(c.p,{id:"numero_certificato_calibrazione",value:_.numero_certificato_calibrazione,onChange:e=>E("numero_certificato_calibrazione",e.target.value),placeholder:"Numero del certificato di calibrazione"})]})]})})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsx)(r.ZB,{children:"Specifiche Tecniche"}),(0,i.jsx)(r.BT,{children:"Caratteristiche tecniche dello strumento"})]}),(0,i.jsxs)(r.Wu,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(n.J,{htmlFor:"range_misura",children:"Range di Misura"}),(0,i.jsx)(c.p,{id:"range_misura",value:_.range_misura,onChange:e=>E("range_misura",e.target.value),placeholder:"es. 0-1000V, 0-20A"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(n.J,{htmlFor:"precisione",children:"Precisione"}),(0,i.jsx)(c.p,{id:"precisione",value:_.precisione,onChange:e=>E("precisione",e.target.value),placeholder:"es. \xb10.1%, \xb12 digit"})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(n.J,{htmlFor:"note",children:"Note"}),(0,i.jsx)(o.T,{id:"note",value:_.note,onChange:e=>E("note",e.target.value),placeholder:"Note aggiuntive sullo strumento...",rows:3})]})]})]}),z&&(0,i.jsxs)(m.Fc,{className:"border-red-200 bg-red-50",children:[(0,i.jsx)(h.A,{className:"h-4 w-4 text-red-600"}),(0,i.jsx)(m.TN,{className:"text-red-800",children:z})]}),(0,i.jsxs)("div",{className:"flex gap-3 justify-end pt-4 border-t border-gray-200",children:[(0,i.jsxs)(l.$,{variant:"outline",onClick:N,disabled:f,children:[(0,i.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Annulla"]}),(0,i.jsxs)(l.$,{onClick:w,disabled:f,children:[f?(0,i.jsx)(j.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,i.jsx)(p.A,{className:"h-4 w-4 mr-2"}),O?"Aggiorna":"Salva"]})]})]})]})}},74782:(e,a,s)=>{s.d(a,{A:()=>b});var i=s(95155),t=s(12115),r=s(30285),l=s(55365),n=s(85057),c=s(62523),o=s(88539),d=s(59409),m=s(54165),x=s(51154),h=s(57434),u=s(54416),j=s(85339),p=s(84616),v=s(381),g=s(4229),N=s(25731),_=s(73155);function b(e){var a;let{cantiereId:s,certificazione:b,strumenti:f,preselectedCavoId:y,onSuccess:z,onCancel:C,onStrumentiUpdate:S}=e,[A,O]=(0,t.useState)(!1),{formData:F,cavi:w,responsabili:E,strumenti:I,weatherData:M,selectedCavo:T,isLoading:R,isSaving:D,isLoadingWeather:J,error:k,validationErrors:V,isWeatherOverride:L,isEdit:Z,isCavoLocked:B,handleInputChange:G,handleSubmit:$,setIsWeatherOverride:q,refreshStrumenti:K,onCancel:U}=function(e){let{cantiereId:a,certificazione:s,preselectedCavoId:i,onSuccess:r,onCancel:l}=e,[n,c]=(0,t.useState)({id_cantiere:a,tipo_certificato:"SINGOLO",stato_certificato:"BOZZA",valore_continuita:"CONFORME",tensione_prova_isolamento:500,temperatura_prova:20,umidita_prova:50}),[o,d]=(0,t.useState)(!0),[m,x]=(0,t.useState)(!1),[h,u]=(0,t.useState)(!1),[j,p]=(0,t.useState)(""),[v,g]=(0,t.useState)({}),[_,b]=(0,t.useState)(!1),[f,y]=(0,t.useState)([]),[z,C]=(0,t.useState)([]),[S,A]=(0,t.useState)([]),[O,F]=(0,t.useState)(null),w=!!s,E=!!i,I=n.id_cavo?f.find(e=>e.id_cavo===n.id_cavo):null;async function M(){try{d(!0);let[e,s,i]=await Promise.all([N.At.getCavi(a),N.AR.getResponsabili(a),N.kw.getStrumenti(a)]);y(e.data||[]),C(s.data||[]),A(i.data||[])}catch(e){console.error("Errore caricamento dati:",e),p("Errore nel caricamento dei dati iniziali")}finally{d(!1)}}async function T(){try{let e=await N.kw.getStrumenti(a);A(e.data||[])}catch(e){console.error("Errore ricaricamento strumenti:",e)}}async function R(){try{u(!0);let e=await N._I.getWeatherData(a);e.data&&F(e.data)}catch(e){console.error("Errore caricamento meteo:",e)}finally{u(!1)}}async function D(){if(!function(){let e={};return n.id_cavo||(e.id_cavo="Seleziona un cavo"),(!n.valore_isolamento||n.valore_isolamento<=0)&&(e.valore_isolamento="Inserisci un valore di isolamento valido"),n.valore_continuita||(e.valore_continuita="Seleziona il risultato della continuit\xe0"),n.valore_resistenza||(e.valore_resistenza="Inserisci un valore di resistenza"),g(e),0===Object.keys(e).length}())return void p("Correggi gli errori nel form prima di continuare");try{let e;x(!0),p("");let i=function(){let e={...n};return O&&!_&&(e.temperatura_prova=O.temperature,e.umidita_prova=O.humidity),delete e.id_cantiere,delete e.data_certificazione,delete e.numero_certificato,delete e.id_certificazione,delete e.timestamp_creazione,delete e.timestamp_modifica,console.log("\uD83D\uDCE1 Dati certificazione da inviare:",e),e}();(e=w&&s?await N.km.updateCertificazione(a,s.id_certificazione,i):await N.km.createCertificazione(a,i)).data&&r(e.data)}catch(a){var e,i;console.error("Errore salvataggio:",a),p((null==(i=a.response)||null==(e=i.data)?void 0:e.detail)||"Errore durante il salvataggio")}finally{x(!1)}}return(0,t.useEffect)(()=>{M(),R()},[a]),(0,t.useEffect)(()=>{s&&c({...s,id_cantiere:a})},[s,a]),(0,t.useEffect)(()=>{i&&!s&&c(e=>({...e,id_cavo:i}))},[i,s]),(0,t.useEffect)(()=>{!O||s||_||c(e=>({...e,temperatura_prova:O.temperature,umidita_prova:O.humidity}))},[O,s,_]),{formData:n,cavi:f,responsabili:z,strumenti:S,weatherData:O,selectedCavo:I,isLoading:o,isSaving:m,isLoadingWeather:h,error:j,validationErrors:v,isWeatherOverride:_,isEdit:w,isCavoLocked:E,handleInputChange:function(e,a){c(s=>({...s,[e]:a})),v[e]&&g(a=>{let s={...a};return delete s[e],s})},handleSubmit:D,setIsWeatherOverride:b,refreshStrumenti:T,onCancel:l}}({cantiereId:s,certificazione:b,strumenti:f,preselectedCavoId:y,onSuccess:z,onCancel:C}),P=I.length>0?I:f;return R?(0,i.jsx)("div",{className:"flex items-center justify-center h-96",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)(x.A,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Caricamento dati certificazione..."})]})}):(0,i.jsxs)("div",{className:"min-w-[800px] max-w-4xl mx-auto bg-white",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("div",{className:"p-1.5 bg-blue-100 rounded-lg",children:(0,i.jsx)(h.A,{className:"h-4 w-4 text-blue-600"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-lg font-semibold text-gray-900",children:Z?"Modifica Certificazione":"Nuova Certificazione CEI 64-8"}),y&&(0,i.jsxs)("p",{className:"text-xs text-gray-500",children:["Cavo: ",y]})]})]}),(0,i.jsx)(r.$,{variant:"ghost",size:"sm",onClick:C,className:"text-gray-400 hover:text-gray-600 h-8 w-8 p-0",children:(0,i.jsx)(u.A,{className:"h-4 w-4"})})]}),k&&(0,i.jsx)("div",{className:"p-4 border-b border-red-200 bg-red-50",children:(0,i.jsxs)(l.Fc,{variant:"destructive",className:"border-red-200 bg-red-50 py-2",children:[(0,i.jsx)(j.A,{className:"h-4 w-4"}),(0,i.jsx)(l.TN,{className:"text-sm",children:k})]})}),(0,i.jsxs)("form",{onSubmit:e=>{e.preventDefault(),$()},className:"p-6 space-y-6",children:[(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 pb-2 border-b border-gray-100",children:[(0,i.jsx)(h.A,{className:"h-4 w-4 text-blue-600"}),(0,i.jsx)("h2",{className:"text-base font-semibold text-gray-900",children:"Informazioni Base"}),(0,i.jsx)("span",{className:"text-xs text-gray-500",children:"Dati principali del cavo e operatore"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[T&&(0,i.jsxs)("div",{className:"md:col-span-2 p-4 bg-gray-50 rounded-lg border",children:[(0,i.jsxs)("h3",{className:"text-sm font-medium text-gray-700 mb-3",children:["Cavo: ",T.id_cavo]}),(0,i.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3 text-sm",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium text-gray-600",children:"Tipologia:"})," ",(0,i.jsx)("span",{className:"font-bold",children:T.tipologia||"N/A"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium text-gray-600",children:"Sezione:"})," ",(0,i.jsx)("span",{className:"font-bold",children:T.sezione||"N/A"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium text-gray-600",children:"Sistema:"})," ",(0,i.jsx)("span",{className:"font-bold",children:T.sistema||"N/A"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium text-gray-600",children:"Utility:"})," ",(0,i.jsx)("span",{className:"font-bold",children:T.utility||"N/A"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium text-gray-600",children:"Da:"})," ",(0,i.jsx)("span",{className:"font-bold",children:T.ubicazione_partenza||"N/A"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium text-gray-600",children:"A:"})," ",(0,i.jsx)("span",{className:"font-bold",children:T.ubicazione_arrivo||"N/A"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium text-gray-600",children:"Metri Teorici:"})," ",(0,i.jsx)("span",{className:"font-bold",children:T.metri_teorici||0})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium text-gray-600",children:"Metri Reali:"})," ",(0,i.jsx)("span",{className:"font-bold",children:T.metratura_reale||0})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium text-gray-600",children:"Stato:"})," ",(0,i.jsx)("span",{className:"font-bold",children:T.stato_installazione||"N/A"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium text-gray-600",children:"Bobina:"})," ",(0,i.jsx)("span",{className:"font-bold",children:T.id_bobina||"N/A"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium text-gray-600",children:"Colore:"})," ",(0,i.jsx)("span",{className:"font-bold",children:T.colore_cavo||"N/A"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium text-gray-600",children:"Collegamenti:"})," ",(0,i.jsx)("span",{className:"font-bold",children:T.collegamenti||0})]})]})]}),(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsx)(n.J,{htmlFor:"id_operatore",className:"text-xs font-medium text-gray-700",children:"Operatore"}),(0,i.jsxs)(d.l6,{value:F.id_operatore||"",onValueChange:e=>G("id_operatore",e),children:[(0,i.jsx)(d.bq,{className:"h-9 text-sm border-gray-300",children:(0,i.jsx)(d.yv,{placeholder:"Seleziona operatore..."})}),(0,i.jsx)(d.gC,{children:E.map(e=>(0,i.jsx)(d.eb,{value:e.id_responsabile.toString(),children:(0,i.jsx)("span",{className:"text-sm",children:e.nome_responsabile})},e.id_responsabile))})]})]}),(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsx)(n.J,{htmlFor:"id_strumento",className:"text-xs font-medium text-gray-700",children:"Strumento di Misura"}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsxs)(d.l6,{value:(null==(a=F.id_strumento)?void 0:a.toString())||"",onValueChange:e=>{let a=P.find(a=>a.id_strumento===parseInt(e));G("id_strumento",parseInt(e)),a&&G("strumento_utilizzato","".concat(a.marca," ").concat(a.modello))},children:[(0,i.jsx)(d.bq,{className:"h-9 text-sm border-gray-300 flex-1",children:(0,i.jsx)(d.yv,{placeholder:"Seleziona strumento..."})}),(0,i.jsx)(d.gC,{children:P.map(e=>(0,i.jsxs)(d.eb,{value:e.id_strumento.toString(),children:[(0,i.jsx)("span",{className:"font-medium text-sm",children:e.nome}),(0,i.jsxs)("span",{className:"text-xs text-gray-500 ml-2",children:[e.marca," ",e.modello]})]},e.id_strumento))})]}),(0,i.jsx)(r.$,{type:"button",variant:"outline",size:"sm",className:"h-9 w-9 p-0 shrink-0",onClick:()=>O(!0),title:"Aggiungi nuovo strumento",children:(0,i.jsx)(p.A,{className:"h-4 w-4"})})]})]})]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 pb-2 border-b border-gray-100",children:[(0,i.jsx)("span",{className:"text-lg",children:"\uD83C\uDF24️"}),(0,i.jsx)("h2",{className:"text-base font-semibold text-gray-900",children:"Condizioni Ambientali"}),(0,i.jsx)("span",{className:"text-xs text-gray-500",children:"Temperatura e umidit\xe0 durante la certificazione"})]}),M&&(0,i.jsx)("div",{className:"p-3 rounded-lg border ".concat(M.isDemo?"bg-amber-50 border-amber-200":"bg-emerald-50 border-emerald-200"),children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[J?(0,i.jsx)(x.A,{className:"h-4 w-4 animate-spin text-blue-600"}):(0,i.jsx)("span",{className:"text-lg",children:M.isDemo?"\uD83D\uDD27":"\uD83C\uDF24️"}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{className:"text-sm font-semibold text-gray-900",children:[M.temperature,"\xb0C • ",M.humidity,"% UR"]}),M.city&&(0,i.jsx)("div",{className:"text-xs text-gray-600",children:M.city})]}),(0,i.jsxs)("div",{className:"text-xs text-gray-500",children:["\uD83D\uDCE1 ",M.source]})]}),(0,i.jsx)(r.$,{type:"button",variant:L?"default":"outline",size:"sm",onClick:()=>q(!L),className:"h-7 text-xs",children:L?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(u.A,{className:"h-3 w-3 mr-1"}),"Automatico"]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(v.A,{className:"h-3 w-3 mr-1"}),"Manuale"]})})]})}),L&&(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsx)(n.J,{htmlFor:"temperatura_prova",className:"text-xs font-medium text-gray-700",children:"Temperatura (\xb0C)"}),(0,i.jsx)(c.p,{id:"temperatura_prova",type:"number",step:"0.1",value:F.temperatura_prova||"",onChange:e=>G("temperatura_prova",parseFloat(e.target.value)),placeholder:"20.0",className:"h-8 text-sm"})]}),(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsx)(n.J,{htmlFor:"umidita_prova",className:"text-xs font-medium text-gray-700",children:"Umidit\xe0 Relativa (%)"}),(0,i.jsx)(c.p,{id:"umidita_prova",type:"number",min:"0",max:"100",value:F.umidita_prova||"",onChange:e=>G("umidita_prova",parseFloat(e.target.value)),placeholder:"50",className:"h-8 text-sm"})]})]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 pb-2 border-b border-gray-100",children:[(0,i.jsx)("span",{className:"text-lg",children:"⚡"}),(0,i.jsx)("h2",{className:"text-base font-semibold text-gray-900",children:"Misurazioni e Test CEI 64-8"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:[(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsx)(n.J,{htmlFor:"valore_continuita",className:"text-xs font-medium text-gray-700",children:"Continuit\xe0"}),(0,i.jsxs)(d.l6,{value:F.valore_continuita||"OK",onValueChange:e=>G("valore_continuita",e),children:[(0,i.jsx)(d.bq,{className:"h-8 text-sm",children:(0,i.jsx)(d.yv,{})}),(0,i.jsxs)(d.gC,{children:[(0,i.jsx)(d.eb,{value:"OK",children:"✅ OK"}),(0,i.jsx)(d.eb,{value:"NON_OK",children:"❌ NON OK"})]})]})]}),(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsx)(n.J,{htmlFor:"valore_isolamento",className:"text-xs font-medium text-gray-700",children:"Isolamento (MΩ)"}),(0,i.jsx)(c.p,{id:"valore_isolamento",value:F.valore_isolamento||"",onChange:e=>G("valore_isolamento",e.target.value),placeholder:"500",className:"h-8 text-sm"})]}),(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsx)(n.J,{htmlFor:"valore_resistenza",className:"text-xs font-medium text-gray-700",children:"Resistenza (Ω)"}),(0,i.jsx)(c.p,{id:"valore_resistenza",value:F.valore_resistenza||"",onChange:e=>G("valore_resistenza",e.target.value),placeholder:"0.5",className:"h-8 text-sm"})]}),(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsx)(n.J,{htmlFor:"tensione_prova_isolamento",className:"text-xs font-medium text-gray-700",children:"Tensione Prova (V)"}),(0,i.jsx)(c.p,{id:"tensione_prova_isolamento",type:"number",value:F.tensione_prova_isolamento||"",onChange:e=>G("tensione_prova_isolamento",parseInt(e.target.value)),placeholder:"500",className:"h-8 text-sm"})]}),(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsx)(n.J,{htmlFor:"esito_complessivo",className:"text-xs font-medium text-gray-700",children:"Esito Complessivo"}),(0,i.jsxs)(d.l6,{value:F.esito_complessivo||"CONFORME",onValueChange:e=>G("esito_complessivo",e),children:[(0,i.jsx)(d.bq,{className:"h-8 text-sm",children:(0,i.jsx)(d.yv,{})}),(0,i.jsxs)(d.gC,{children:[(0,i.jsx)(d.eb,{value:"CONFORME",children:"✅ CONFORME"}),(0,i.jsx)(d.eb,{value:"NON_CONFORME",children:"❌ NON CONFORME"}),(0,i.jsx)(d.eb,{value:"CONFORME_CON_OSSERVAZIONI",children:"⚠️ CONFORME CON OSSERVAZIONI"})]})]})]})]})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 pb-2 border-b border-gray-100",children:[(0,i.jsx)("span",{className:"text-lg",children:"\uD83D\uDCDD"}),(0,i.jsx)("h2",{className:"text-base font-semibold text-gray-900",children:"Note"})]}),(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsx)(n.J,{htmlFor:"note",className:"text-xs font-medium text-gray-700",children:"Osservazioni aggiuntive"}),(0,i.jsx)(o.T,{id:"note",value:F.note||"",onChange:e=>G("note",e.target.value),placeholder:"Inserisci eventuali note o osservazioni...",className:"h-16 text-sm resize-none"})]})]}),(0,i.jsxs)("div",{className:"flex justify-end gap-3 pt-4 border-t border-gray-200",children:[(0,i.jsx)(r.$,{type:"button",variant:"outline",onClick:C,disabled:D,className:"px-4 h-9",children:"Annulla"}),(0,i.jsx)(r.$,{type:"submit",disabled:D,className:"px-6 h-9 bg-green-600 hover:bg-green-700",children:D?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(x.A,{className:"h-4 w-4 animate-spin mr-2"}),"Salvando..."]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Salva e Chiudi"]})})]})]}),(0,i.jsx)(m.lG,{open:A,onOpenChange:O,children:(0,i.jsxs)(m.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,i.jsx)(m.c7,{children:(0,i.jsx)(m.L3,{children:"Aggiungi Nuovo Strumento"})}),(0,i.jsx)(_.A,{cantiereId:s,onSuccess:()=>{O(!1),K(),S&&S()},onCancel:()=>O(!1)})]})})]})}}}]);