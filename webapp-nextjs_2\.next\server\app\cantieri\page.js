(()=>{var e={};e.id=222,e.ids=[222],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,t,r)=>{"use strict";r.d(t,{A0:()=>i,BF:()=>o,Hj:()=>l,XI:()=>n,nA:()=>d,nd:()=>c});var a=r(60687);r(43210);var s=r(4780);function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm border-collapse",e),...t})})}function i({className:e,...t}){return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",e),...t})}function o({className:e,...t}){return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",e),...t})}function l({className:e,...t}){return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function c({className:e,...t}){return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function d({className:e,...t}){return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},9444:(e,t,r)=>{Promise.resolve().then(r.bind(r,90910))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},12597:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},22948:(e,t,r)=>{Promise.resolve().then(r.bind(r,79018))},26134:(e,t,r)=>{"use strict";r.d(t,{G$:()=>U,Hs:()=>w,UC:()=>er,VY:()=>es,ZL:()=>ee,bL:()=>Y,bm:()=>en,hE:()=>ea,hJ:()=>et,l9:()=>Q});var a=r(43210),s=r(70569),n=r(98599),i=r(11273),o=r(96963),l=r(65551),c=r(31355),d=r(32547),u=r(25028),m=r(46059),p=r(14163),x=r(1359),g=r(42247),h=r(63376),f=r(8730),v=r(60687),j="Dialog",[b,w]=(0,i.A)(j),[y,N]=b(j),_=e=>{let{__scopeDialog:t,children:r,open:s,defaultOpen:n,onOpenChange:i,modal:c=!0}=e,d=a.useRef(null),u=a.useRef(null),[m,p]=(0,l.i)({prop:s,defaultProp:n??!1,onChange:i,caller:j});return(0,v.jsx)(y,{scope:t,triggerRef:d,contentRef:u,contentId:(0,o.B)(),titleId:(0,o.B)(),descriptionId:(0,o.B)(),open:m,onOpenChange:p,onOpenToggle:a.useCallback(()=>p(e=>!e),[p]),modal:c,children:r})};_.displayName=j;var C="DialogTrigger",z=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,i=N(C,r),o=(0,n.s)(t,i.triggerRef);return(0,v.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":H(i.open),...a,ref:o,onClick:(0,s.m)(e.onClick,i.onOpenToggle)})});z.displayName=C;var P="DialogPortal",[A,k]=b(P,{forceMount:void 0}),E=e=>{let{__scopeDialog:t,forceMount:r,children:s,container:n}=e,i=N(P,t);return(0,v.jsx)(A,{scope:t,forceMount:r,children:a.Children.map(s,e=>(0,v.jsx)(m.C,{present:r||i.open,children:(0,v.jsx)(u.Z,{asChild:!0,container:n,children:e})}))})};E.displayName=P;var I="DialogOverlay",D=a.forwardRef((e,t)=>{let r=k(I,e.__scopeDialog),{forceMount:a=r.forceMount,...s}=e,n=N(I,e.__scopeDialog);return n.modal?(0,v.jsx)(m.C,{present:a||n.open,children:(0,v.jsx)(F,{...s,ref:t})}):null});D.displayName=I;var S=(0,f.TL)("DialogOverlay.RemoveScroll"),F=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,s=N(I,r);return(0,v.jsx)(g.A,{as:S,allowPinchZoom:!0,shards:[s.contentRef],children:(0,v.jsx)(p.sG.div,{"data-state":H(s.open),...a,ref:t,style:{pointerEvents:"auto",...a.style}})})}),M="DialogContent",R=a.forwardRef((e,t)=>{let r=k(M,e.__scopeDialog),{forceMount:a=r.forceMount,...s}=e,n=N(M,e.__scopeDialog);return(0,v.jsx)(m.C,{present:a||n.open,children:n.modal?(0,v.jsx)($,{...s,ref:t}):(0,v.jsx)(q,{...s,ref:t})})});R.displayName=M;var $=a.forwardRef((e,t)=>{let r=N(M,e.__scopeDialog),i=a.useRef(null),o=(0,n.s)(t,r.contentRef,i);return a.useEffect(()=>{let e=i.current;if(e)return(0,h.Eq)(e)},[]),(0,v.jsx)(O,{...e,ref:o,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,s.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,s.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,s.m)(e.onFocusOutside,e=>e.preventDefault())})}),q=a.forwardRef((e,t)=>{let r=N(M,e.__scopeDialog),s=a.useRef(!1),n=a.useRef(!1);return(0,v.jsx)(O,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(s.current||r.triggerRef.current?.focus(),t.preventDefault()),s.current=!1,n.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(s.current=!0,"pointerdown"===t.detail.originalEvent.type&&(n.current=!0));let a=t.target;r.triggerRef.current?.contains(a)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&n.current&&t.preventDefault()}})}),O=a.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:s,onOpenAutoFocus:i,onCloseAutoFocus:o,...l}=e,u=N(M,r),m=a.useRef(null),p=(0,n.s)(t,m);return(0,x.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(d.n,{asChild:!0,loop:!0,trapped:s,onMountAutoFocus:i,onUnmountAutoFocus:o,children:(0,v.jsx)(c.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":H(u.open),...l,ref:p,onDismiss:()=>u.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(X,{titleId:u.titleId}),(0,v.jsx)(K,{contentRef:m,descriptionId:u.descriptionId})]})]})}),J="DialogTitle",L=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,s=N(J,r);return(0,v.jsx)(p.sG.h2,{id:s.titleId,...a,ref:t})});L.displayName=J;var G="DialogDescription",T=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,s=N(G,r);return(0,v.jsx)(p.sG.p,{id:s.descriptionId,...a,ref:t})});T.displayName=G;var B="DialogClose",Z=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=N(B,r);return(0,v.jsx)(p.sG.button,{type:"button",...a,ref:t,onClick:(0,s.m)(e.onClick,()=>n.onOpenChange(!1))})});function H(e){return e?"open":"closed"}Z.displayName=B;var W="DialogTitleWarning",[U,V]=(0,i.q)(W,{contentName:M,titleName:J,docsSlug:"dialog"}),X=({titleId:e})=>{let t=V(W),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return a.useEffect(()=>{e&&(document.getElementById(e)||console.error(r))},[r,e]),null},K=({contentRef:e,descriptionId:t})=>{let r=V("DialogDescriptionWarning"),s=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return a.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&(document.getElementById(t)||console.warn(s))},[s,e,t]),null},Y=_,Q=z,ee=E,et=D,er=R,ea=L,es=T,en=Z},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35248:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var a=r(60687),s=r(63213),n=r(16189);r(43210);var i=r(41862);function o({children:e,requiresUser:t=!1,requiresAdmin:r=!1}){let{user:o,isAuthenticated:l,isLoading:c}=(0,s.A)();return((0,n.useRouter)(),c)?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsx)(i.A,{className:"h-8 w-8 animate-spin"})}):!l||r&&o?.ruolo!=="admin"||t&&!o?null:(0,a.jsx)(a.Fragment,{children:e})}function l({children:e}){return(0,a.jsx)(o,{requiresUser:!0,children:e})}},38921:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs_2\\\\src\\\\app\\\\cantieri\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_2\\src\\app\\cantieri\\layout.tsx","default")},39007:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var a=r(65239),s=r(48088),n=r(88170),i=r.n(n),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["cantieri",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,90910)),"C:\\CMS\\webapp-nextjs_2\\src\\app\\cantieri\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,38921)),"C:\\CMS\\webapp-nextjs_2\\src\\app\\cantieri\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\CMS\\webapp-nextjs_2\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\CMS\\webapp-nextjs_2\\src\\app\\cantieri\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/cantieri/page",pathname:"/cantieri",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>i});var a=r(60687);r(43210);var s=r(4780);function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,t,r)=>{"use strict";r.d(t,{Cf:()=>u,Es:()=>p,L3:()=>x,c7:()=>m,lG:()=>o,rr:()=>g,zM:()=>l});var a=r(60687);r(43210);var s=r(26134),n=r(11860),i=r(4780);function o({...e}){return(0,a.jsx)(s.bL,{"data-slot":"dialog",...e})}function l({...e}){return(0,a.jsx)(s.l9,{"data-slot":"dialog-trigger",...e})}function c({...e}){return(0,a.jsx)(s.ZL,{"data-slot":"dialog-portal",...e})}function d({className:e,...t}){return(0,a.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function u({className:e,children:t,showCloseButton:r=!0,...o}){return(0,a.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,a.jsx)(d,{}),(0,a.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...o,children:[t,r&&(0,a.jsxs)(s.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,a.jsx)(n.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function p({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function x({className:e,...t}){return(0,a.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",e),...t})}function g({className:e,...t}){return(0,a.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}},69175:(e,t,r)=>{Promise.resolve().then(r.bind(r,38921))},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79018:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>C});var a=r(60687),s=r(43210),n=r(16189),i=r(44493),o=r(29523),l=r(89667),c=r(6211),d=r(63503),u=r(80013),m=r(63213),p=r(62185),x=r(41862),g=r(99270),h=r(96474),f=r(63143),v=r(62688);let j=(0,v.A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]),b=(0,v.A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);var w=r(12597),y=r(13861),N=r(53411),_=r(70334);function C(){let{user:e,isAuthenticated:t,isLoading:r,selectCantiere:v}=(0,m.A)(),C=(0,n.useRouter)(),[z,P]=(0,s.useState)([]),[A,k]=(0,s.useState)(!0),[E,I]=(0,s.useState)(""),[D,S]=(0,s.useState)(""),[F,M]=(0,s.useState)({}),[R,$]=(0,s.useState)(!1),[q,O]=(0,s.useState)({}),[J,L]=(0,s.useState)({}),[G,T]=(0,s.useState)(!1),[B,Z]=(0,s.useState)(!1),[H,W]=(0,s.useState)(!1),[U,V]=(0,s.useState)(null),[X,K]=(0,s.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[Y,Q]=(0,s.useState)({commessa:"",descrizione:"",nome_cliente:"",indirizzo_cantiere:"",citta_cantiere:"",nazione_cantiere:"",password_cantiere:"",codice_univoco:""}),[ee,et]=(0,s.useState)(!1),er=async()=>{try{k(!0);let e=await p._I.getCantieri();P(e),await ea(e)}catch(e){I("Errore nel caricamento dei cantieri")}finally{k(!1)}},ea=async e=>{try{$(!0);let t=e.map(async e=>{try{let t=await p._I.getCantiereStatistics(e.id_cantiere);return{id:e.id_cantiere,stats:t}}catch(t){return console.error(`Errore nel caricamento statistiche cantiere ${e.id_cantiere}:`,t),{id:e.id_cantiere,stats:{percentuale_avanzamento:0}}}}),r=(await Promise.all(t)).reduce((e,{id:t,stats:r})=>(e[t]=r,e),{});M(r)}catch(e){console.error("Errore nel caricamento delle statistiche:",e)}finally{$(!1)}},es=async e=>{let t=e.id_cantiere;if(q[t])O(e=>({...e,[t]:!1})),L(e=>({...e,[t]:""}));else if(J[t])O(e=>({...e,[t]:!0}));else try{et(!0);let e=localStorage.getItem("token")||localStorage.getItem("access_token"),r=await fetch(`http://localhost:8001/api/cantieri/${t}/view-password`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e}`}});if(!r.ok){let e=await r.json();throw Error(e.detail||"Errore nel recupero password")}let a=await r.json();L(e=>({...e,[t]:a.password_cantiere})),O(e=>({...e,[t]:!0}))}catch(e){I(e instanceof Error?e.message:"Errore nel recupero password")}finally{et(!1)}},en=e=>{v(e),C.push(`/cantieri/${e.id_cantiere}`)},ei=e=>{V(e),Q({commessa:e.commessa||"",descrizione:e.descrizione||"",nome_cliente:e.nome_cliente||"",indirizzo_cantiere:e.indirizzo_cantiere||"",citta_cantiere:e.citta_cantiere||"",nazione_cantiere:e.nazione_cantiere||"",password_cantiere:e.password_cantiere||"",codice_univoco:e.codice_univoco||""}),Z(!0)},eo=async()=>{try{await p._I.createCantiere(Y),T(!1),Q({commessa:"",descrizione:"",nome_cliente:"",indirizzo_cantiere:"",citta_cantiere:"",nazione_cantiere:"Italia",password_cantiere:"",codice_univoco:""}),er()}catch(e){I("Errore nella creazione del cantiere")}},el=async()=>{if(U)try{await p._I.updateCantiere(U.id_cantiere,Y),Z(!1),V(null),er()}catch(e){I("Errore nella modifica del cantiere")}},ec=async()=>{if(U){if(X.newPassword!==X.confirmPassword)return void I("Le password non coincidono");if(!X.currentPassword)return void I("Inserisci la password attuale per confermare il cambio");if(!X.newPassword||X.newPassword.length<6)return void I("La nuova password deve essere di almeno 6 caratteri");try{k(!0),I("");let e=await fetch(`http://localhost:8001/api/cantieri/${U.id_cantiere}/change-password`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`},body:JSON.stringify({password_attuale:X.currentPassword,password_nuova:X.newPassword,conferma_password:X.confirmPassword})});if(!e.ok){let t=await e.json();throw Error(t.detail||"Errore nel cambio password")}let t=await e.json();if(t.success)K({currentPassword:"",newPassword:"",confirmPassword:""}),W(!1),I(""),alert(t.message||"Password cambiata con successo");else throw Error(t.message||"Errore nel cambio password")}catch(e){I(e instanceof Error?e.message:"Errore nel cambio password")}finally{k(!1)}}},ed=async e=>{try{await navigator.clipboard.writeText(e)}catch(e){console.error("Failed to copy to clipboard:",e)}},eu=z.filter(e=>e.commessa.toLowerCase().includes(D.toLowerCase())||e.descrizione?.toLowerCase().includes(D.toLowerCase())||e.nome_cliente?.toLowerCase().includes(D.toLowerCase()));return r?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsx)(x.A,{className:"h-8 w-8 animate-spin"})}):(0,a.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("div",{className:"flex items-center gap-4",children:(0,a.jsxs)("div",{className:"relative w-80",children:[(0,a.jsx)(g.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(l.p,{placeholder:"Cerca cantieri...",value:D,onChange:e=>S(e.target.value),className:"pl-8 w-full"})]})}),(0,a.jsxs)(d.lG,{open:G,onOpenChange:T,children:[(0,a.jsx)(d.zM,{asChild:!0,children:(0,a.jsxs)(o.$,{className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25",children:[(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Nuovo Cantiere"]})}),(0,a.jsxs)(d.Cf,{className:"sm:max-w-[425px]",children:[(0,a.jsxs)(d.c7,{children:[(0,a.jsx)(d.L3,{children:"Crea Nuovo Cantiere"}),(0,a.jsx)(d.rr,{children:"Inserisci i dettagli del nuovo cantiere"})]}),(0,a.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(u.J,{htmlFor:"commessa",className:"text-right",children:"Commessa *"}),(0,a.jsx)(l.p,{id:"commessa",value:Y.commessa,onChange:e=>Q({...Y,commessa:e.target.value}),className:"col-span-3",placeholder:"Nome commessa"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(u.J,{htmlFor:"descrizione",className:"text-right",children:"Descrizione"}),(0,a.jsx)(l.p,{id:"descrizione",value:Y.descrizione,onChange:e=>Q({...Y,descrizione:e.target.value}),className:"col-span-3",placeholder:"Descrizione cantiere"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(u.J,{htmlFor:"nome_cliente",className:"text-right",children:"Cliente"}),(0,a.jsx)(l.p,{id:"nome_cliente",value:Y.nome_cliente,onChange:e=>Q({...Y,nome_cliente:e.target.value}),className:"col-span-3",placeholder:"Nome cliente"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(u.J,{htmlFor:"indirizzo_cantiere",className:"text-right",children:"Indirizzo"}),(0,a.jsx)(l.p,{id:"indirizzo_cantiere",value:Y.indirizzo_cantiere,onChange:e=>Q({...Y,indirizzo_cantiere:e.target.value}),className:"col-span-3",placeholder:"Indirizzo cantiere"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(u.J,{htmlFor:"citta_cantiere",className:"text-right",children:"Citt\xe0"}),(0,a.jsx)(l.p,{id:"citta_cantiere",value:Y.citta_cantiere,onChange:e=>Q({...Y,citta_cantiere:e.target.value}),className:"col-span-3",placeholder:"Citt\xe0"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(u.J,{htmlFor:"password_cantiere",className:"text-right",children:"Password"}),(0,a.jsx)(l.p,{id:"password_cantiere",type:"password",value:Y.password_cantiere,onChange:e=>Q({...Y,password_cantiere:e.target.value}),className:"col-span-3",placeholder:"Password cantiere"})]})]}),(0,a.jsxs)(d.Es,{children:[(0,a.jsx)(o.$,{variant:"outline",onClick:()=>T(!1),children:"Annulla"}),(0,a.jsx)(o.$,{onClick:eo,className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25",children:"Crea Cantiere"})]})]})]}),(0,a.jsx)(d.lG,{open:B,onOpenChange:e=>{Z(e),e||(V(null),Q({commessa:"",descrizione:"",nome_cliente:"",indirizzo_cantiere:"",citta_cantiere:"",nazione_cantiere:"Italia",password_cantiere:"",codice_univoco:""}))},children:(0,a.jsxs)(d.Cf,{className:"sm:max-w-[600px]",children:[(0,a.jsxs)(d.c7,{children:[(0,a.jsxs)(d.L3,{className:"flex items-center gap-2",children:[(0,a.jsx)(f.A,{className:"h-5 w-5"}),"Modifica Cantiere - ",U?.commessa]}),(0,a.jsx)(d.rr,{children:"Modifica i dati del cantiere selezionato"})]}),(0,a.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(u.J,{htmlFor:"edit-commessa",className:"text-right",children:"Commessa"}),(0,a.jsx)("input",{id:"edit-commessa",value:Y.commessa,onChange:e=>Q({...Y,commessa:e.target.value}),className:"col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(u.J,{htmlFor:"edit-descrizione",className:"text-right",children:"Descrizione"}),(0,a.jsx)("input",{id:"edit-descrizione",value:Y.descrizione,onChange:e=>Q({...Y,descrizione:e.target.value}),className:"col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(u.J,{htmlFor:"edit-cliente",className:"text-right",children:"Cliente"}),(0,a.jsx)("input",{id:"edit-cliente",value:Y.nome_cliente,onChange:e=>Q({...Y,nome_cliente:e.target.value}),className:"col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(u.J,{htmlFor:"edit-indirizzo",className:"text-right",children:"Indirizzo"}),(0,a.jsx)("input",{id:"edit-indirizzo",value:Y.indirizzo_cantiere,onChange:e=>Q({...Y,indirizzo_cantiere:e.target.value}),className:"col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(u.J,{htmlFor:"edit-citta",className:"text-right",children:"Citt\xe0"}),(0,a.jsx)("input",{id:"edit-citta",value:Y.citta_cantiere,onChange:e=>Q({...Y,citta_cantiere:e.target.value}),className:"col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(u.J,{className:"text-right",children:"Password"}),(0,a.jsx)("div",{className:"col-span-3",children:(0,a.jsxs)(o.$,{type:"button",variant:"outline",onClick:()=>{W(!0)},className:"w-full border-orange-200 text-orange-600 hover:bg-orange-50 hover:border-orange-300",children:[(0,a.jsx)(j,{className:"mr-2 h-4 w-4"}),"Cambia Password"]})})]})]}),(0,a.jsxs)(d.Es,{children:[(0,a.jsx)(o.$,{variant:"outline",onClick:()=>Z(!1),children:"Annulla"}),(0,a.jsx)(o.$,{onClick:el,className:"bg-blue-600 hover:bg-blue-700",children:"Salva Modifiche"})]})]})}),(0,a.jsx)(d.lG,{open:H,onOpenChange:e=>{W(e),e||(K({currentPassword:"",newPassword:"",confirmPassword:""}),I(""))},children:(0,a.jsxs)(d.Cf,{className:"sm:max-w-[500px]",children:[(0,a.jsxs)(d.c7,{children:[(0,a.jsxs)(d.L3,{className:"flex items-center gap-2",children:[(0,a.jsx)(j,{className:"h-5 w-5"}),"Cambia Password - ",U?.commessa]}),(0,a.jsx)(d.rr,{children:"Inserisci la password attuale e la nuova password per il cantiere"})]}),E&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:E}),(0,a.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(u.J,{htmlFor:"current-password",className:"text-right",children:"Password Attuale"}),(0,a.jsx)("input",{id:"current-password",type:"password",value:X.currentPassword,onChange:e=>K({...X,currentPassword:e.target.value}),className:"col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Inserisci password attuale"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(u.J,{htmlFor:"new-password",className:"text-right",children:"Nuova Password"}),(0,a.jsx)("input",{id:"new-password",type:"password",value:X.newPassword,onChange:e=>K({...X,newPassword:e.target.value}),className:"col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Inserisci nuova password"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(u.J,{htmlFor:"confirm-password",className:"text-right",children:"Conferma Password"}),(0,a.jsx)("input",{id:"confirm-password",type:"password",value:X.confirmPassword,onChange:e=>K({...X,confirmPassword:e.target.value}),className:"col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Conferma nuova password"})]})]}),(0,a.jsxs)(d.Es,{children:[(0,a.jsx)(o.$,{variant:"outline",onClick:()=>W(!1),children:"Annulla"}),(0,a.jsx)(o.$,{onClick:ec,disabled:A||!X.currentPassword||!X.newPassword,className:"bg-orange-600 hover:bg-orange-700",children:A?"Cambiando...":"Cambia Password"})]})]})})]}),E&&(0,a.jsx)("div",{className:"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,a.jsx)("p",{className:"text-red-700",children:E})}),(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Zp,{children:(0,a.jsxs)(c.XI,{children:[(0,a.jsx)(c.A0,{children:(0,a.jsxs)(c.Hj,{className:"border-b border-gray-200",children:[(0,a.jsx)(c.nd,{className:"font-semibold text-gray-700",children:"Commessa"}),(0,a.jsx)(c.nd,{className:"font-semibold text-gray-700",children:"Descrizione"}),(0,a.jsx)(c.nd,{className:"font-semibold text-gray-700",children:"Cliente"}),(0,a.jsx)(c.nd,{className:"font-semibold text-gray-700",children:"Data Creazione"}),(0,a.jsx)(c.nd,{className:"font-semibold text-gray-700",children:"Codice Accesso"}),(0,a.jsx)(c.nd,{className:"font-semibold text-gray-700",children:"Password Cantiere"}),(0,a.jsx)(c.nd,{className:"font-semibold text-gray-700 w-32",children:"Avanzamento"}),(0,a.jsx)(c.nd,{className:"font-semibold text-gray-700 text-center",children:"Progresso %"}),(0,a.jsx)(c.nd,{className:"text-center font-semibold text-gray-700 w-48",children:"Azioni"})]})}),(0,a.jsx)(c.BF,{children:eu.map(e=>(0,a.jsxs)(c.Hj,{className:"hover:bg-gray-50/50 transition-colors",children:[(0,a.jsx)(c.nA,{className:"font-semibold text-gray-900 py-4",children:e.commessa}),(0,a.jsx)(c.nA,{className:"text-gray-700 py-4",children:e.descrizione}),(0,a.jsx)(c.nA,{className:"text-gray-700 py-4",children:e.nome_cliente}),(0,a.jsx)(c.nA,{className:"text-gray-600 py-4",children:new Date(e.data_creazione).toLocaleDateString()}),(0,a.jsx)(c.nA,{className:"py-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("code",{className:"text-sm bg-blue-50 text-blue-700 px-3 py-1.5 rounded-md font-mono border border-blue-200",children:e.codice_univoco}),(0,a.jsx)(o.$,{size:"sm",variant:"ghost",className:"h-7 w-7 p-0 text-gray-400 hover:bg-gray-50 hover:text-gray-600 transition-colors",title:"Copia codice",onClick:()=>ed(e.codice_univoco),children:(0,a.jsx)(b,{className:"h-3 w-3"})})]})}),(0,a.jsx)(c.nA,{className:"py-4",children:(0,a.jsx)("div",{className:"flex items-center gap-3",children:(0,a.jsx)("div",{className:"flex items-center gap-2",children:q[e.id_cantiere]&&J[e.id_cantiere]?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("code",{className:"text-sm bg-green-50 text-green-700 px-2 py-1 rounded border border-green-200 font-mono",children:J[e.id_cantiere]}),(0,a.jsx)(o.$,{size:"sm",variant:"ghost",className:"h-7 w-7 p-0 text-green-600 hover:bg-green-50 hover:text-green-700 transition-colors",title:"Nascondi password",onClick:()=>es(e),children:(0,a.jsx)(w.A,{className:"h-4 w-4"})})]}):(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,a.jsx)("span",{className:"text-sm text-gray-600 font-medium",children:"Configurata"})]}),(0,a.jsx)(o.$,{size:"sm",variant:"ghost",className:"h-7 w-7 p-0 text-blue-600 hover:bg-blue-50 hover:text-blue-700 transition-colors",title:"Mostra password",onClick:()=>es(e),disabled:ee,children:ee?(0,a.jsx)(x.A,{className:"h-4 w-4 animate-spin"}):(0,a.jsx)(y.A,{className:"h-4 w-4"})})]})})})}),(0,a.jsx)(c.nA,{className:"py-4",children:(0,a.jsx)("div",{className:"flex items-center gap-2",children:R?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 animate-spin text-gray-400"}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:"Caricamento..."})]}):(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{className:"flex-1 min-w-[120px]",children:(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 shadow-inner",children:(0,a.jsx)("div",{className:`h-3 rounded-full transition-all duration-500 ease-out shadow-sm ${(F[e.id_cantiere]?.percentuale_avanzamento||0)>=90?"bg-gradient-to-r from-green-500 to-green-600":(F[e.id_cantiere]?.percentuale_avanzamento||0)>=75?"bg-gradient-to-r from-blue-500 to-blue-600":(F[e.id_cantiere]?.percentuale_avanzamento||0)>=50?"bg-gradient-to-r from-yellow-500 to-yellow-600":(F[e.id_cantiere]?.percentuale_avanzamento||0)>=25?"bg-gradient-to-r from-orange-500 to-orange-600":"bg-gradient-to-r from-red-500 to-red-600"}`,style:{width:`${Math.min(F[e.id_cantiere]?.percentuale_avanzamento||0,100)}%`}})})})})})}),(0,a.jsx)(c.nA,{className:"py-4 text-center",children:R?(0,a.jsx)(x.A,{className:"h-4 w-4 animate-spin text-gray-400 mx-auto"}):(0,a.jsxs)("div",{className:"flex items-center justify-center gap-1",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 text-gray-500"}),(0,a.jsxs)("span",{className:`text-sm font-semibold ${(F[e.id_cantiere]?.percentuale_avanzamento||0)>=90?"text-green-700":(F[e.id_cantiere]?.percentuale_avanzamento||0)>=75?"text-blue-700":(F[e.id_cantiere]?.percentuale_avanzamento||0)>=50?"text-yellow-700":(F[e.id_cantiere]?.percentuale_avanzamento||0)>=25?"text-orange-700":"text-red-700"}`,children:[(F[e.id_cantiere]?.percentuale_avanzamento||0).toFixed(1),"%"]})]})}),(0,a.jsx)(c.nA,{className:"text-center py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsxs)(o.$,{size:"sm",variant:"outline",onClick:()=>ei(e),className:"h-9 px-3 text-gray-600 border-gray-200 hover:bg-gray-50 hover:text-gray-700 hover:border-gray-300 transition-all duration-200 ease-in-out",title:"Modifica dati cantiere",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-1.5"}),"Modifica"]}),(0,a.jsxs)(o.$,{size:"sm",onClick:()=>en(e),className:"h-9 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium transition-colors duration-200 ease-in-out",title:"Accedi al cantiere",children:[(0,a.jsx)(_.A,{className:"h-4 w-4 mr-1.5"}),"Accedi"]})]})})]},e.id_cantiere))})]})})})]})}},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,r)=>{"use strict";r.d(t,{J:()=>i});var a=r(60687);r(43210);var s=r(78148),n=r(4780);function i({className:e,...t}){return(0,a.jsx)(s.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var a=r(60687);r(43210);var s=r(4780);function n({className:e,type:t,...r}){return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},90910:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs_2\\\\src\\\\app\\\\cantieri\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_2\\src\\app\\cantieri\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")},97199:(e,t,r)=>{Promise.resolve().then(r.bind(r,35248))},99270:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,588,658,142,415],()=>r(39007));module.exports=a})();