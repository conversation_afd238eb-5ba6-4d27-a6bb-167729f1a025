(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2804],{8743:(e,t,a)=>{Promise.resolve().then(a.bind(a,13735))},13735:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c});var i=a(95155),s=a(12115),r=a(30285),n=a(84333);let l={id_cavo:"C001",id_cantiere:1,revisione_ufficiale:"Rev 1.0",tipologia:"LIYCY",sezione:"3X2.5MM",formazione:"3X2.5MM",da:"Quadro A",a:"Quadro B",ubicazione_partenza:"Quadro A",ubicazione_arrivo:"Quadro B",metri_teorici:150,metri_posati:75,metratura_reale:75,stato_installazione:"installato",id_bobina:"BOB001"},o={id_cantiere:1,commessa:"TEST-001"},d=()=>{let[e,t]=(0,s.useState)(!1),[a,d]=(0,s.useState)(!1),c=async e=>{console.log("\uD83D\uDCBE Unified Modal Save:",e),await new Promise(e=>setTimeout(e,1e3)),alert("Operazione completata con successo!\nModalit\xe0: ".concat(e.mode,"\nCavo: ").concat(e.cableId))};return(0,i.jsxs)("div",{className:"p-6 space-y-4",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Test Interfaccia Unificata"}),(0,i.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,i.jsxs)("h3",{className:"font-semibold mb-2",children:["Cavo di Test: ",l.id_cavo]}),(0,i.jsxs)("p",{className:"text-sm text-gray-600",children:[l.tipologia," ",l.sezione," - Da: ",l.da," A: ",l.a]}),(0,i.jsxs)("p",{className:"text-sm text-gray-600",children:["Metri posati: ",l.metri_posati,"m / ",l.metri_teorici,"m"]})]}),(0,i.jsxs)("div",{className:"flex gap-4",children:[(0,i.jsx)(r.$,{onClick:()=>t(!0),className:"bg-green-600 hover:bg-green-700",children:"Test Modalit\xe0: Aggiungi Metri"}),(0,i.jsx)(r.$,{onClick:()=>d(!0),className:"bg-blue-600 hover:bg-blue-700",children:"Test Modalit\xe0: Modifica Bobina"})]}),(0,i.jsx)(n.B,{mode:"aggiungi_metri",open:e,onClose:()=>t(!1),cavo:l,cantiere:o,onSave:c}),(0,i.jsx)(n.B,{mode:"modifica_bobina",open:a,onClose:()=>d(!1),cavo:l,cantiere:o,onSave:c})]})};function c(){return(0,i.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,i.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-center mb-8 text-gray-800",children:"Test Interfaccia Unificata Cable/Bobbin"}),(0,i.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6",children:[(0,i.jsx)("h2",{className:"font-semibold text-blue-800 mb-2",children:"Istruzioni per il Test"}),(0,i.jsxs)("ul",{className:"text-blue-700 text-sm space-y-1",children:[(0,i.jsx)("li",{children:"• Clicca sui pulsanti per aprire la modale unificata in diverse modalit\xe0"}),(0,i.jsx)("li",{children:"• Testa la validazione dei campi e la selezione delle bobine"}),(0,i.jsx)("li",{children:"• Verifica che le sezioni dinamiche si mostrino correttamente"}),(0,i.jsx)("li",{children:"• Controlla l'accessibilit\xe0 con Tab e Escape"}),(0,i.jsx)("li",{children:"• Verifica i messaggi di errore e successo"})]})]}),(0,i.jsx)(d,{}),(0,i.jsxs)("div",{className:"mt-8 bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,i.jsx)("h2",{className:"font-semibold text-green-800 mb-2",children:"Funzionalit\xe0 Implementate"}),(0,i.jsxs)("div",{className:"grid md:grid-cols-2 gap-4 text-sm text-green-700",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-medium mb-1",children:"Modalit\xe0 Aggiungi Metri:"}),(0,i.jsxs)("ul",{className:"space-y-1",children:[(0,i.jsx)("li",{children:"✅ Validazione input metri"}),(0,i.jsx)("li",{children:"✅ Selezione bobina compatibile/incompatibile"}),(0,i.jsx)("li",{children:"✅ Opzione BOBINA VUOTA"}),(0,i.jsx)("li",{children:"✅ Ricerca bobine"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-medium mb-1",children:"Modalit\xe0 Modifica Bobina:"}),(0,i.jsxs)("ul",{className:"space-y-1",children:[(0,i.jsx)("li",{children:"✅ Radio button per opzioni"}),(0,i.jsx)("li",{children:"✅ Selezione condizionale bobina"}),(0,i.jsx)("li",{children:"✅ Modifica metri posati"}),(0,i.jsx)("li",{children:"✅ Annulla posa"})]})]})]})]}),(0,i.jsxs)("div",{className:"mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,i.jsx)("h2",{className:"font-semibold text-yellow-800 mb-2",children:"Accessibilit\xe0"}),(0,i.jsxs)("div",{className:"text-sm text-yellow-700 space-y-1",children:[(0,i.jsx)("li",{children:"✅ Attributi ARIA per screen reader"}),(0,i.jsx)("li",{children:"✅ Navigazione da tastiera (Tab, Enter, Escape)"}),(0,i.jsx)("li",{children:"✅ Focus management e indicatori visivi"}),(0,i.jsx)("li",{children:'✅ Messaggi di errore con role="alert"'}),(0,i.jsx)("li",{children:"✅ Etichette descrittive per tutti i controlli"})]})]})]})})})}},30285:(e,t,a)=>{"use strict";a.d(t,{$:()=>o,r:()=>l});var i=a(95155);a(12115);var s=a(99708),r=a(74466),n=a(59434);let l=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:a,size:r,asChild:o=!1,...d}=e,c=o?s.DX:"button";return(0,i.jsx)(c,{"data-slot":"button",className:(0,n.cn)(l({variant:a,size:r,className:t})),...d})}},54165:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>u,Es:()=>m,L3:()=>x,c7:()=>g,lG:()=>l,rr:()=>b,zM:()=>o});var i=a(95155);a(12115);var s=a(15452),r=a(54416),n=a(59434);function l(e){let{...t}=e;return(0,i.jsx)(s.bL,{"data-slot":"dialog",...t})}function o(e){let{...t}=e;return(0,i.jsx)(s.l9,{"data-slot":"dialog-trigger",...t})}function d(e){let{...t}=e;return(0,i.jsx)(s.ZL,{"data-slot":"dialog-portal",...t})}function c(e){let{className:t,...a}=e;return(0,i.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function u(e){let{className:t,children:a,showCloseButton:l=!0,...o}=e;return(0,i.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,i.jsx)(c,{}),(0,i.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...o,children:[a,l&&(0,i.jsxs)(s.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,i.jsx)(r.A,{}),(0,i.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function g(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function m(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function x(e){let{className:t,...a}=e;return(0,i.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",t),...a})}function b(e){let{className:t,...a}=e;return(0,i.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...a})}},55365:(e,t,a)=>{"use strict";a.d(t,{Fc:()=>o,TN:()=>d});var i=a(95155),s=a(12115),r=a(74466),n=a(59434);let l=(0,r.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=s.forwardRef((e,t)=>{let{className:a,variant:s,...r}=e;return(0,i.jsx)("div",{ref:t,role:"alert",className:(0,n.cn)(l({variant:s}),a),...r})});o.displayName="Alert",s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,i.jsx)("h5",{ref:t,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",a),...s})}).displayName="AlertTitle";let d=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,i.jsx)("div",{ref:t,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",a),...s})});d.displayName="AlertDescription"},59434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>r});var i=a(52596),s=a(39688);function r(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,i.$)(t))}},62523:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var i=a(95155),s=a(12115),r=a(59434);let n=s.forwardRef((e,t)=>{let{className:a,type:s,...n}=e;return(0,i.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),ref:t,...n})});n.displayName="Input"},85057:(e,t,a)=>{"use strict";a.d(t,{J:()=>n});var i=a(95155);a(12115);var s=a(40968),r=a(59434);function n(e){let{className:t,...a}=e;return(0,i.jsx)(s.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3464,3455,1909,4858,283,4333,8441,1684,7358],()=>t(8743)),_N_E=e.O()}]);