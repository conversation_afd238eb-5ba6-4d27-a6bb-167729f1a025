"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6955],{5196:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},11275:(e,t,n)=>{n.d(t,{X:()=>l});var r=n(12115),o=n(52712);function l(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let l=t[0];if("borderBoxSize"in l){let e=l.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},35152:(e,t,n)=>{n.d(t,{Mz:()=>eZ,i3:()=>eJ,UC:()=>e$,bL:()=>eY,Bk:()=>eD});var r=n(12115);let o=["top","right","bottom","left"],l=Math.min,i=Math.max,a=Math.round,s=Math.floor,u=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(p(e))?"y":"x"}function w(e){return e.replace(/start|end/g,e=>d[e])}function y(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function x(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function b(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function S(e,t,n){let r,{reference:o,floating:l}=e,i=g(t),a=m(g(t)),s=v(a),u=p(t),c="y"===i,d=o.x+o.width/2-l.width/2,f=o.y+o.height/2-l.height/2,w=o[s]/2-l[s]/2;switch(u){case"top":r={x:d,y:o.y-l.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-l.width,y:f};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[a]-=w*(n&&c?-1:1);break;case"end":r[a]+=w*(n&&c?-1:1)}return r}let C=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:l=[],platform:i}=n,a=l.filter(Boolean),s=await (null==i.isRTL?void 0:i.isRTL(t)),u=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=S(u,r,s),f=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:l,fn:m}=a[n],{x:v,y:g,data:w,reset:y}=await m({x:c,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:u,platform:i,elements:{reference:e,floating:t}});c=null!=v?v:c,d=null!=g?g:d,p={...p,[l]:{...p[l],...w}},y&&h<=50&&(h++,"object"==typeof y&&(y.placement&&(f=y.placement),y.rects&&(u=!0===y.rects?await i.getElementRects({reference:e,floating:t,strategy:o}):y.rects),{x:c,y:d}=S(u,f,s)),n=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function R(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:l,rects:i,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=f(t,e),m=x(h),v=a[p?"floating"===d?"reference":"floating":d],g=b(await l.getClippingRect({element:null==(n=await (null==l.isElement?void 0:l.isElement(v)))||n?v:v.contextElement||await (null==l.getDocumentElement?void 0:l.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:s})),w="floating"===d?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,y=await (null==l.getOffsetParent?void 0:l.getOffsetParent(a.floating)),S=await (null==l.isElement?void 0:l.isElement(y))&&await (null==l.getScale?void 0:l.getScale(y))||{x:1,y:1},C=b(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:w,offsetParent:y,strategy:s}):w);return{top:(g.top-C.top+m.top)/S.y,bottom:(C.bottom-g.bottom+m.bottom)/S.y,left:(g.left-C.left+m.left)/S.x,right:(C.right-g.right+m.right)/S.x}}function A(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function T(e){return o.some(t=>e[t]>=0)}async function k(e,t){let{placement:n,platform:r,elements:o}=e,l=await (null==r.isRTL?void 0:r.isRTL(o.floating)),i=p(n),a=h(n),s="y"===g(n),u=["left","top"].includes(i)?-1:1,c=l&&s?-1:1,d=f(t,e),{mainAxis:m,crossAxis:v,alignmentAxis:w}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof w&&(v="end"===a?-1*w:w),s?{x:v*c,y:m*u}:{x:m*u,y:v*c}}function P(){return"undefined"!=typeof window}function E(e){return N(e)?(e.nodeName||"").toLowerCase():"#document"}function j(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function L(e){var t;return null==(t=(N(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function N(e){return!!P()&&(e instanceof Node||e instanceof j(e).Node)}function D(e){return!!P()&&(e instanceof Element||e instanceof j(e).Element)}function M(e){return!!P()&&(e instanceof HTMLElement||e instanceof j(e).HTMLElement)}function H(e){return!!P()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof j(e).ShadowRoot)}function O(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=W(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function I(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function B(e){let t=F(),n=D(e)?W(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function F(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function V(e){return["html","body","#document"].includes(E(e))}function W(e){return j(e).getComputedStyle(e)}function _(e){return D(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function z(e){if("html"===E(e))return e;let t=e.assignedSlot||e.parentNode||H(e)&&e.host||L(e);return H(t)?t.host:t}function G(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=z(t);return V(n)?t.ownerDocument?t.ownerDocument.body:t.body:M(n)&&O(n)?n:e(n)}(e),l=o===(null==(r=e.ownerDocument)?void 0:r.body),i=j(o);if(l){let e=K(i);return t.concat(i,i.visualViewport||[],O(o)?o:[],e&&n?G(e):[])}return t.concat(o,G(o,[],n))}function K(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function U(e){let t=W(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=M(e),l=o?e.offsetWidth:n,i=o?e.offsetHeight:r,s=a(n)!==l||a(r)!==i;return s&&(n=l,r=i),{width:n,height:r,$:s}}function q(e){return D(e)?e:e.contextElement}function X(e){let t=q(e);if(!M(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:l}=U(t),i=(l?a(n.width):n.width)/r,s=(l?a(n.height):n.height)/o;return i&&Number.isFinite(i)||(i=1),s&&Number.isFinite(s)||(s=1),{x:i,y:s}}let Y=u(0);function Z(e){let t=j(e);return F()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:Y}function $(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let l=e.getBoundingClientRect(),i=q(e),a=u(1);t&&(r?D(r)&&(a=X(r)):a=X(e));let s=(void 0===(o=n)&&(o=!1),r&&(!o||r===j(i))&&o)?Z(i):u(0),c=(l.left+s.x)/a.x,d=(l.top+s.y)/a.y,f=l.width/a.x,p=l.height/a.y;if(i){let e=j(i),t=r&&D(r)?j(r):r,n=e,o=K(n);for(;o&&r&&t!==n;){let e=X(o),t=o.getBoundingClientRect(),r=W(o),l=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,i=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,d*=e.y,f*=e.x,p*=e.y,c+=l,d+=i,o=K(n=j(o))}}return b({width:f,height:p,x:c,y:d})}function J(e,t){let n=_(e).scrollLeft;return t?t.left+n:$(L(e)).left+n}function Q(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:J(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=j(e),r=L(e),o=n.visualViewport,l=r.clientWidth,i=r.clientHeight,a=0,s=0;if(o){l=o.width,i=o.height;let e=F();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,s=o.offsetTop)}return{width:l,height:i,x:a,y:s}}(e,n);else if("document"===t)r=function(e){let t=L(e),n=_(e),r=e.ownerDocument.body,o=i(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),l=i(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+J(e),s=-n.scrollTop;return"rtl"===W(r).direction&&(a+=i(t.clientWidth,r.clientWidth)-o),{width:o,height:l,x:a,y:s}}(L(e));else if(D(t))r=function(e,t){let n=$(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,l=M(e)?X(e):u(1),i=e.clientWidth*l.x,a=e.clientHeight*l.y;return{width:i,height:a,x:o*l.x,y:r*l.y}}(t,n);else{let n=Z(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return b(r)}function et(e){return"static"===W(e).position}function en(e,t){if(!M(e)||"fixed"===W(e).position)return null;if(t)return t(e);let n=e.offsetParent;return L(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=j(e);if(I(e))return n;if(!M(e)){let t=z(e);for(;t&&!V(t);){if(D(t)&&!et(t))return t;t=z(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(E(r))&&et(r);)r=en(r,t);return r&&V(r)&&et(r)&&!B(r)?n:r||function(e){let t=z(e);for(;M(t)&&!V(t);){if(B(t))return t;if(I(t))break;t=z(t)}return null}(e)||n}let eo=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=M(t),o=L(t),l="fixed"===n,i=$(e,!0,l,t),a={scrollLeft:0,scrollTop:0},s=u(0);if(r||!r&&!l)if(("body"!==E(t)||O(o))&&(a=_(t)),r){let e=$(t,!0,l,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=J(o));l&&!r&&o&&(s.x=J(o));let c=!o||r||l?u(0):Q(o,a);return{x:i.left+a.scrollLeft-s.x-c.x,y:i.top+a.scrollTop-s.y-c.y,width:i.width,height:i.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},el={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,l="fixed"===o,i=L(r),a=!!t&&I(t.floating);if(r===i||a&&l)return n;let s={scrollLeft:0,scrollTop:0},c=u(1),d=u(0),f=M(r);if((f||!f&&!l)&&(("body"!==E(r)||O(i))&&(s=_(r)),M(r))){let e=$(r);c=X(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let p=!i||f||l?u(0):Q(i,s,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-s.scrollLeft*c.x+d.x+p.x,y:n.y*c.y-s.scrollTop*c.y+d.y+p.y}},getDocumentElement:L,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,a=[..."clippingAncestors"===n?I(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=G(e,[],!1).filter(e=>D(e)&&"body"!==E(e)),o=null,l="fixed"===W(e).position,i=l?z(e):e;for(;D(i)&&!V(i);){let t=W(i),n=B(i);n||"fixed"!==t.position||(o=null),(l?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||O(i)&&!n&&function e(t,n){let r=z(t);return!(r===n||!D(r)||V(r))&&("fixed"===W(r).position||e(r,n))}(e,i))?r=r.filter(e=>e!==i):o=t,i=z(i)}return t.set(e,r),r}(t,this._c):[].concat(n),r],s=a[0],u=a.reduce((e,n)=>{let r=ee(t,n,o);return e.top=i(r.top,e.top),e.right=l(r.right,e.right),e.bottom=l(r.bottom,e.bottom),e.left=i(r.left,e.left),e},ee(t,s,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:er,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=U(e);return{width:t,height:n}},getScale:X,isElement:D,isRTL:function(e){return"rtl"===W(e).direction}};function ei(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ea=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:a,platform:s,elements:u,middlewareData:c}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let w=x(p),y={x:n,y:r},b=m(g(o)),S=v(b),C=await s.getDimensions(d),R="y"===b,A=R?"clientHeight":"clientWidth",T=a.reference[S]+a.reference[b]-y[b]-a.floating[S],k=y[b]-a.reference[b],P=await (null==s.getOffsetParent?void 0:s.getOffsetParent(d)),E=P?P[A]:0;E&&await (null==s.isElement?void 0:s.isElement(P))||(E=u.floating[A]||a.floating[S]);let j=E/2-C[S]/2-1,L=l(w[R?"top":"left"],j),N=l(w[R?"bottom":"right"],j),D=E-C[S]-N,M=E/2-C[S]/2+(T/2-k/2),H=i(L,l(M,D)),O=!c.arrow&&null!=h(o)&&M!==H&&a.reference[S]/2-(M<L?L:N)-C[S]/2<0,I=O?M<L?M-L:M-D:0;return{[b]:y[b]+I,data:{[b]:H,centerOffset:M-H-I,...O&&{alignmentOffset:I}},reset:O}}}),es=(e,t,n)=>{let r=new Map,o={platform:el,...n},l={...o.platform,_c:r};return C(e,t,{...o,platform:l})};var eu=n(47650),ec="undefined"!=typeof document?r.useLayoutEffect:function(){};function ed(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ed(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ed(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ef(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ef(e);return Math.round(t*n)/n}function eh(e){let t=r.useRef(e);return ec(()=>{t.current=e}),t}let em=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ea({element:n.current,padding:r}).fn(t):{}:n?ea({element:n,padding:r}).fn(t):{}}}),ev=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:l,placement:i,middlewareData:a}=t,s=await k(t,e);return i===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:l+s.y,data:{...s,placement:i}}}}}(e),options:[e,t]}),eg=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:a=!0,crossAxis:s=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=f(e,t),d={x:n,y:r},h=await R(t,c),v=g(p(o)),w=m(v),y=d[w],x=d[v];if(a){let e="y"===w?"top":"left",t="y"===w?"bottom":"right",n=y+h[e],r=y-h[t];y=i(n,l(y,r))}if(s){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=x+h[e],r=x-h[t];x=i(n,l(x,r))}let b=u.fn({...t,[w]:y,[v]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[w]:a,[v]:s}}}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:l,middlewareData:i}=t,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=f(e,t),c={x:n,y:r},d=g(o),h=m(d),v=c[h],w=c[d],y=f(a,t),x="number"==typeof y?{mainAxis:y,crossAxis:0}:{mainAxis:0,crossAxis:0,...y};if(s){let e="y"===h?"height":"width",t=l.reference[h]-l.floating[e]+x.mainAxis,n=l.reference[h]+l.reference[e]-x.mainAxis;v<t?v=t:v>n&&(v=n)}if(u){var b,S;let e="y"===h?"width":"height",t=["top","left"].includes(p(o)),n=l.reference[d]-l.floating[e]+(t&&(null==(b=i.offset)?void 0:b[d])||0)+(t?0:x.crossAxis),r=l.reference[d]+l.reference[e]+(t?0:(null==(S=i.offset)?void 0:S[d])||0)-(t?x.crossAxis:0);w<n?w=n:w>r&&(w=r)}return{[h]:v,[d]:w}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,l,i;let{placement:a,middlewareData:s,rects:u,initialPlacement:c,platform:d,elements:x}=t,{mainAxis:b=!0,crossAxis:S=!0,fallbackPlacements:C,fallbackStrategy:A="bestFit",fallbackAxisSideDirection:T="none",flipAlignment:k=!0,...P}=f(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};let E=p(a),j=g(c),L=p(c)===c,N=await (null==d.isRTL?void 0:d.isRTL(x.floating)),D=C||(L||!k?[y(c)]:function(e){let t=y(e);return[w(e),t,w(t)]}(c)),M="none"!==T;!C&&M&&D.push(...function(e,t,n,r){let o=h(e),l=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return o&&(l=l.map(e=>e+"-"+o),t&&(l=l.concat(l.map(w)))),l}(c,k,T,N));let H=[c,...D],O=await R(t,P),I=[],B=(null==(r=s.flip)?void 0:r.overflows)||[];if(b&&I.push(O[E]),S){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=m(g(e)),l=v(o),i="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[l]>t.floating[l]&&(i=y(i)),[i,y(i)]}(a,u,N);I.push(O[e[0]],O[e[1]])}if(B=[...B,{placement:a,overflows:I}],!I.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=H[e];if(t&&("alignment"!==S||j===g(t)||B.every(e=>e.overflows[0]>0&&g(e.placement)===j)))return{data:{index:e,overflows:B},reset:{placement:t}};let n=null==(l=B.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:l.placement;if(!n)switch(A){case"bestFit":{let e=null==(i=B.filter(e=>{if(M){let t=g(e.placement);return t===j||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:i[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,a,{placement:s,rects:u,platform:c,elements:d}=t,{apply:m=()=>{},...v}=f(e,t),w=await R(t,v),y=p(s),x=h(s),b="y"===g(s),{width:S,height:C}=u.floating;"top"===y||"bottom"===y?(o=y,a=x===(await (null==c.isRTL?void 0:c.isRTL(d.floating))?"start":"end")?"left":"right"):(a=y,o="end"===x?"top":"bottom");let A=C-w.top-w.bottom,T=S-w.left-w.right,k=l(C-w[o],A),P=l(S-w[a],T),E=!t.middlewareData.shift,j=k,L=P;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(L=T),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(j=A),E&&!x){let e=i(w.left,0),t=i(w.right,0),n=i(w.top,0),r=i(w.bottom,0);b?L=S-2*(0!==e||0!==t?e+t:i(w.left,w.right)):j=C-2*(0!==n||0!==r?n+r:i(w.top,w.bottom))}await m({...t,availableWidth:L,availableHeight:j});let N=await c.getDimensions(d.floating);return S!==N.width||C!==N.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=f(e,t);switch(r){case"referenceHidden":{let e=A(await R(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:T(e)}}}case"escaped":{let e=A(await R(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:T(e)}}}default:return{}}}}}(e),options:[e,t]}),eS=(e,t)=>({...em(e),options:[e,t]});var eC=n(63655),eR=n(95155),eA=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...l}=e;return(0,eR.jsx)(eC.sG.svg,{...l,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eR.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eA.displayName="Arrow";var eT=n(6101),ek=n(46081),eP=n(39033),eE=n(52712),ej=n(11275),eL="Popper",[eN,eD]=(0,ek.A)(eL),[eM,eH]=eN(eL),eO=e=>{let{__scopePopper:t,children:n}=e,[o,l]=r.useState(null);return(0,eR.jsx)(eM,{scope:t,anchor:o,onAnchorChange:l,children:n})};eO.displayName=eL;var eI="PopperAnchor",eB=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...l}=e,i=eH(eI,n),a=r.useRef(null),s=(0,eT.s)(t,a);return r.useEffect(()=>{i.onAnchorChange((null==o?void 0:o.current)||a.current)}),o?null:(0,eR.jsx)(eC.sG.div,{...l,ref:s})});eB.displayName=eI;var eF="PopperContent",[eV,eW]=eN(eF),e_=r.forwardRef((e,t)=>{var n,o,a,u,c,d,f,p;let{__scopePopper:h,side:m="bottom",sideOffset:v=0,align:g="center",alignOffset:w=0,arrowPadding:y=0,avoidCollisions:x=!0,collisionBoundary:b=[],collisionPadding:S=0,sticky:C="partial",hideWhenDetached:R=!1,updatePositionStrategy:A="optimized",onPlaced:T,...k}=e,P=eH(eF,h),[E,j]=r.useState(null),N=(0,eT.s)(t,e=>j(e)),[D,M]=r.useState(null),H=(0,ej.X)(D),O=null!=(f=null==H?void 0:H.width)?f:0,I=null!=(p=null==H?void 0:H.height)?p:0,B="number"==typeof S?S:{top:0,right:0,bottom:0,left:0,...S},F=Array.isArray(b)?b:[b],V=F.length>0,W={padding:B,boundary:F.filter(eU),altBoundary:V},{refs:_,floatingStyles:z,placement:K,isPositioned:U,middlewareData:X}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:l,elements:{reference:i,floating:a}={},transform:s=!0,whileElementsMounted:u,open:c}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);ed(p,o)||h(o);let[m,v]=r.useState(null),[g,w]=r.useState(null),y=r.useCallback(e=>{e!==C.current&&(C.current=e,v(e))},[]),x=r.useCallback(e=>{e!==R.current&&(R.current=e,w(e))},[]),b=i||m,S=a||g,C=r.useRef(null),R=r.useRef(null),A=r.useRef(d),T=null!=u,k=eh(u),P=eh(l),E=eh(c),j=r.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:n,middleware:p};P.current&&(e.platform=P.current),es(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==E.current};L.current&&!ed(A.current,t)&&(A.current=t,eu.flushSync(()=>{f(t)}))})},[p,t,n,P,E]);ec(()=>{!1===c&&A.current.isPositioned&&(A.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let L=r.useRef(!1);ec(()=>(L.current=!0,()=>{L.current=!1}),[]),ec(()=>{if(b&&(C.current=b),S&&(R.current=S),b&&S){if(k.current)return k.current(b,S,j);j()}},[b,S,j,k,T]);let N=r.useMemo(()=>({reference:C,floating:R,setReference:y,setFloating:x}),[y,x]),D=r.useMemo(()=>({reference:b,floating:S}),[b,S]),M=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!D.floating)return e;let t=ep(D.floating,d.x),r=ep(D.floating,d.y);return s?{...e,transform:"translate("+t+"px, "+r+"px)",...ef(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,s,D.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:j,refs:N,elements:D,floatingStyles:M}),[d,j,N,D,M])}({strategy:"fixed",placement:m+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=r,p=q(e),h=a||u?[...p?G(p):[],...G(t)]:[];h.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let m=p&&d?function(e,t){let n,r=null,o=L(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function u(c,d){void 0===c&&(c=!1),void 0===d&&(d=1),a();let f=e.getBoundingClientRect(),{left:p,top:h,width:m,height:v}=f;if(c||t(),!m||!v)return;let g=s(h),w=s(o.clientWidth-(p+m)),y={rootMargin:-g+"px "+-w+"px "+-s(o.clientHeight-(h+v))+"px "+-s(p)+"px",threshold:i(0,l(1,d))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==d){if(!x)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||ei(f,e.getBoundingClientRect())||u(),x=!1}try{r=new IntersectionObserver(b,{...y,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(b,y)}r.observe(e)}(!0),a}(p,n):null,v=-1,g=null;c&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!f&&g.observe(p),g.observe(t));let w=f?$(e):null;return f&&function t(){let r=$(e);w&&!ei(w,r)&&n(),w=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{a&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===A})},elements:{reference:P.anchor},middleware:[ev({mainAxis:v+I,alignmentAxis:w}),x&&eg({mainAxis:!0,crossAxis:!1,limiter:"partial"===C?ew():void 0,...W}),x&&ey({...W}),ex({...W,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:l,height:i}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(l,"px")),a.setProperty("--radix-popper-anchor-height","".concat(i,"px"))}}),D&&eS({element:D,padding:y}),eq({arrowWidth:O,arrowHeight:I}),R&&eb({strategy:"referenceHidden",...W})]}),[Y,Z]=eX(K),J=(0,eP.c)(T);(0,eE.N)(()=>{U&&(null==J||J())},[U,J]);let Q=null==(n=X.arrow)?void 0:n.x,ee=null==(o=X.arrow)?void 0:o.y,et=(null==(a=X.arrow)?void 0:a.centerOffset)!==0,[en,er]=r.useState();return(0,eE.N)(()=>{E&&er(window.getComputedStyle(E).zIndex)},[E]),(0,eR.jsx)("div",{ref:_.setFloating,"data-radix-popper-content-wrapper":"",style:{...z,transform:U?z.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null==(u=X.transformOrigin)?void 0:u.x,null==(c=X.transformOrigin)?void 0:c.y].join(" "),...(null==(d=X.hide)?void 0:d.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eR.jsx)(eV,{scope:h,placedSide:Y,onArrowChange:M,arrowX:Q,arrowY:ee,shouldHideArrow:et,children:(0,eR.jsx)(eC.sG.div,{"data-side":Y,"data-align":Z,...k,ref:N,style:{...k.style,animation:U?void 0:"none"}})})})});e_.displayName=eF;var ez="PopperArrow",eG={top:"bottom",right:"left",bottom:"top",left:"right"},eK=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=eW(ez,n),l=eG[o.placedSide];return(0,eR.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eR.jsx)(eA,{...r,ref:t,style:{...r.style,display:"block"}})})});function eU(e){return null!==e}eK.displayName=ez;var eq=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,l,i;let{placement:a,rects:s,middlewareData:u}=t,c=(null==(n=u.arrow)?void 0:n.centerOffset)!==0,d=c?0:e.arrowWidth,f=c?0:e.arrowHeight,[p,h]=eX(a),m={start:"0%",center:"50%",end:"100%"}[h],v=(null!=(l=null==(r=u.arrow)?void 0:r.x)?l:0)+d/2,g=(null!=(i=null==(o=u.arrow)?void 0:o.y)?i:0)+f/2,w="",y="";return"bottom"===p?(w=c?m:"".concat(v,"px"),y="".concat(-f,"px")):"top"===p?(w=c?m:"".concat(v,"px"),y="".concat(s.floating.height+f,"px")):"right"===p?(w="".concat(-f,"px"),y=c?m:"".concat(g,"px")):"left"===p&&(w="".concat(s.floating.width+f,"px"),y=c?m:"".concat(g,"px")),{data:{x:w,y}}}});function eX(e){let[t,n="center"]=e.split("-");return[t,n]}var eY=eO,eZ=eB,e$=e_,eJ=eK},38715:(e,t,n)=>{n.d(t,{UC:()=>eN,In:()=>ej,q7:()=>eM,VF:()=>eO,p4:()=>eH,ZL:()=>eL,bL:()=>ek,wn:()=>eB,PP:()=>eI,l9:()=>eP,WT:()=>eE,LM:()=>eD});var r=n(12115),o=n(47650);function l(e,[t,n]){return Math.min(n,Math.max(t,e))}var i=n(85185),a=n(37328),s=n(6101),u=n(46081),c=n(94315),d=n(19178),f=n(92293),p=n(25519),h=n(61285),m=n(35152),v=n(34378),g=n(63655),w=n(99708),y=n(39033),x=n(5845),b=n(52712),S=n(45503),C=n(95155),R=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});r.forwardRef((e,t)=>(0,C.jsx)(g.sG.span,{...e,ref:t,style:{...R,...e.style}})).displayName="VisuallyHidden";var A=n(38168),T=n(93795),k=[" ","Enter","ArrowUp","ArrowDown"],P=[" ","Enter"],E="Select",[j,L,N]=(0,a.N)(E),[D,M]=(0,u.A)(E,[N,m.Bk]),H=(0,m.Bk)(),[O,I]=D(E),[B,F]=D(E),V=e=>{let{__scopeSelect:t,children:n,open:o,defaultOpen:l,onOpenChange:i,value:a,defaultValue:s,onValueChange:u,dir:d,name:f,autoComplete:p,disabled:v,required:g,form:w}=e,y=H(t),[b,S]=r.useState(null),[R,A]=r.useState(null),[T,k]=r.useState(!1),P=(0,c.jH)(d),[L,N]=(0,x.i)({prop:o,defaultProp:null!=l&&l,onChange:i,caller:E}),[D,M]=(0,x.i)({prop:a,defaultProp:s,onChange:u,caller:E}),I=r.useRef(null),F=!b||w||!!b.closest("form"),[V,W]=r.useState(new Set),_=Array.from(V).map(e=>e.props.value).join(";");return(0,C.jsx)(m.bL,{...y,children:(0,C.jsxs)(O,{required:g,scope:t,trigger:b,onTriggerChange:S,valueNode:R,onValueNodeChange:A,valueNodeHasChildren:T,onValueNodeHasChildrenChange:k,contentId:(0,h.B)(),value:D,onValueChange:M,open:L,onOpenChange:N,dir:P,triggerPointerDownPosRef:I,disabled:v,children:[(0,C.jsx)(j.Provider,{scope:t,children:(0,C.jsx)(B,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{W(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{W(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),F?(0,C.jsxs)(eC,{"aria-hidden":!0,required:g,tabIndex:-1,name:f,autoComplete:p,value:D,onChange:e=>M(e.target.value),disabled:v,form:w,children:[void 0===D?(0,C.jsx)("option",{value:""}):null,Array.from(V)]},_):null]})})};V.displayName=E;var W="SelectTrigger",_=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:o=!1,...l}=e,a=H(n),u=I(W,n),c=u.disabled||o,d=(0,s.s)(t,u.onTriggerChange),f=L(n),p=r.useRef("touch"),[h,v,w]=eA(e=>{let t=f().filter(e=>!e.disabled),n=t.find(e=>e.value===u.value),r=eT(t,e,n);void 0!==r&&u.onValueChange(r.value)}),y=e=>{c||(u.onOpenChange(!0),w()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,C.jsx)(m.Mz,{asChild:!0,...a,children:(0,C.jsx)(g.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":eR(u.value)?"":void 0,...l,ref:d,onClick:(0,i.m)(l.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&y(e)}),onPointerDown:(0,i.m)(l.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:(0,i.m)(l.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||v(e.key),(!t||" "!==e.key)&&k.includes(e.key)&&(y(),e.preventDefault())})})})});_.displayName=W;var z="SelectValue",G=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:l,placeholder:i="",...a}=e,u=I(z,n),{onValueNodeHasChildrenChange:c}=u,d=void 0!==l,f=(0,s.s)(t,u.onValueNodeChange);return(0,b.N)(()=>{c(d)},[c,d]),(0,C.jsx)(g.sG.span,{...a,ref:f,style:{pointerEvents:"none"},children:eR(u.value)?(0,C.jsx)(C.Fragment,{children:i}):l})});G.displayName=z;var K=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,C.jsx)(g.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});K.displayName="SelectIcon";var U=e=>(0,C.jsx)(v.Z,{asChild:!0,...e});U.displayName="SelectPortal";var q="SelectContent",X=r.forwardRef((e,t)=>{let n=I(q,e.__scopeSelect),[l,i]=r.useState();return((0,b.N)(()=>{i(new DocumentFragment)},[]),n.open)?(0,C.jsx)(J,{...e,ref:t}):l?o.createPortal((0,C.jsx)(Y,{scope:e.__scopeSelect,children:(0,C.jsx)(j.Slot,{scope:e.__scopeSelect,children:(0,C.jsx)("div",{children:e.children})})}),l):null});X.displayName=q;var[Y,Z]=D(q),$=(0,w.TL)("SelectContent.RemoveScroll"),J=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:l,onEscapeKeyDown:a,onPointerDownOutside:u,side:c,sideOffset:h,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:w,collisionPadding:y,sticky:x,hideWhenDetached:b,avoidCollisions:S,...R}=e,k=I(q,n),[P,E]=r.useState(null),[j,N]=r.useState(null),D=(0,s.s)(t,e=>E(e)),[M,H]=r.useState(null),[O,B]=r.useState(null),F=L(n),[V,W]=r.useState(!1),_=r.useRef(!1);r.useEffect(()=>{if(P)return(0,A.Eq)(P)},[P]),(0,f.Oh)();let z=r.useCallback(e=>{let[t,...n]=F().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&j&&(j.scrollTop=0),n===r&&j&&(j.scrollTop=j.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[F,j]),G=r.useCallback(()=>z([M,P]),[z,M,P]);r.useEffect(()=>{V&&G()},[V,G]);let{onOpenChange:K,triggerPointerDownPosRef:U}=k;r.useEffect(()=>{if(P){let e={x:0,y:0},t=t=>{var n,r,o,l;e={x:Math.abs(Math.round(t.pageX)-(null!=(o=null==(n=U.current)?void 0:n.x)?o:0)),y:Math.abs(Math.round(t.pageY)-(null!=(l=null==(r=U.current)?void 0:r.y)?l:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():P.contains(n.target)||K(!1),document.removeEventListener("pointermove",t),U.current=null};return null!==U.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[P,K,U]),r.useEffect(()=>{let e=()=>K(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[K]);let[X,Z]=eA(e=>{let t=F().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=eT(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),J=r.useCallback((e,t,n)=>{let r=!_.current&&!n;(void 0!==k.value&&k.value===t||r)&&(H(e),r&&(_.current=!0))},[k.value]),et=r.useCallback(()=>null==P?void 0:P.focus(),[P]),en=r.useCallback((e,t,n)=>{let r=!_.current&&!n;(void 0!==k.value&&k.value===t||r)&&B(e)},[k.value]),er="popper"===o?ee:Q,eo=er===ee?{side:c,sideOffset:h,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:w,collisionPadding:y,sticky:x,hideWhenDetached:b,avoidCollisions:S}:{};return(0,C.jsx)(Y,{scope:n,content:P,viewport:j,onViewportChange:N,itemRefCallback:J,selectedItem:M,onItemLeave:et,itemTextRefCallback:en,focusSelectedItem:G,selectedItemText:O,position:o,isPositioned:V,searchRef:X,children:(0,C.jsx)(T.A,{as:$,allowPinchZoom:!0,children:(0,C.jsx)(p.n,{asChild:!0,trapped:k.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,i.m)(l,e=>{var t;null==(t=k.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,C.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>k.onOpenChange(!1),children:(0,C.jsx)(er,{role:"listbox",id:k.contentId,"data-state":k.open?"open":"closed",dir:k.dir,onContextMenu:e=>e.preventDefault(),...R,...eo,onPlaced:()=>W(!0),ref:D,style:{display:"flex",flexDirection:"column",outline:"none",...R.style},onKeyDown:(0,i.m)(R.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Z(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=F().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>z(t)),e.preventDefault()}})})})})})})});J.displayName="SelectContentImpl";var Q=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:o,...i}=e,a=I(q,n),u=Z(q,n),[c,d]=r.useState(null),[f,p]=r.useState(null),h=(0,s.s)(t,e=>p(e)),m=L(n),v=r.useRef(!1),w=r.useRef(!0),{viewport:y,selectedItem:x,selectedItemText:S,focusSelectedItem:R}=u,A=r.useCallback(()=>{if(a.trigger&&a.valueNode&&c&&f&&y&&x&&S){let e=a.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),r=S.getBoundingClientRect();if("rtl"!==a.dir){let o=r.left-t.left,i=n.left-o,a=e.left-i,s=e.width+a,u=Math.max(s,t.width),d=l(i,[10,Math.max(10,window.innerWidth-10-u)]);c.style.minWidth=s+"px",c.style.left=d+"px"}else{let o=t.right-r.right,i=window.innerWidth-n.right-o,a=window.innerWidth-e.right-i,s=e.width+a,u=Math.max(s,t.width),d=l(i,[10,Math.max(10,window.innerWidth-10-u)]);c.style.minWidth=s+"px",c.style.right=d+"px"}let i=m(),s=window.innerHeight-20,u=y.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),h=parseInt(d.paddingTop,10),g=parseInt(d.borderBottomWidth,10),w=p+h+u+parseInt(d.paddingBottom,10)+g,b=Math.min(5*x.offsetHeight,w),C=window.getComputedStyle(y),R=parseInt(C.paddingTop,10),A=parseInt(C.paddingBottom,10),T=e.top+e.height/2-10,k=x.offsetHeight/2,P=p+h+(x.offsetTop+k);if(P<=T){let e=i.length>0&&x===i[i.length-1].ref.current;c.style.bottom="0px";let t=Math.max(s-T,k+(e?A:0)+(f.clientHeight-y.offsetTop-y.offsetHeight)+g);c.style.height=P+t+"px"}else{let e=i.length>0&&x===i[0].ref.current;c.style.top="0px";let t=Math.max(T,p+y.offsetTop+(e?R:0)+k);c.style.height=t+(w-P)+"px",y.scrollTop=P-T+y.offsetTop}c.style.margin="".concat(10,"px 0"),c.style.minHeight=b+"px",c.style.maxHeight=s+"px",null==o||o(),requestAnimationFrame(()=>v.current=!0)}},[m,a.trigger,a.valueNode,c,f,y,x,S,a.dir,o]);(0,b.N)(()=>A(),[A]);let[T,k]=r.useState();(0,b.N)(()=>{f&&k(window.getComputedStyle(f).zIndex)},[f]);let P=r.useCallback(e=>{e&&!0===w.current&&(A(),null==R||R(),w.current=!1)},[A,R]);return(0,C.jsx)(et,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:v,onScrollButtonChange:P,children:(0,C.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:T},children:(0,C.jsx)(g.sG.div,{...i,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}})})})});Q.displayName="SelectItemAlignedPosition";var ee=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...l}=e,i=H(n);return(0,C.jsx)(m.UC,{...i,...l,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...l.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,en]=D(q,{}),er="SelectViewport",eo=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:o,...l}=e,a=Z(er,n),u=en(er,n),c=(0,s.s)(t,a.onViewportChange),d=r.useRef(0);return(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,C.jsx)(j.Slot,{scope:n,children:(0,C.jsx)(g.sG.div,{"data-radix-select-viewport":"",role:"presentation",...l,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...l.style},onScroll:(0,i.m)(l.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=u;if((null==r?void 0:r.current)&&n){let e=Math.abs(d.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let l=o+e,i=Math.min(r,l),a=l-i;n.style.height=i+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});eo.displayName=er;var el="SelectGroup",[ei,ea]=D(el);r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,h.B)();return(0,C.jsx)(ei,{scope:n,id:o,children:(0,C.jsx)(g.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})}).displayName=el;var es="SelectLabel";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=ea(es,n);return(0,C.jsx)(g.sG.div,{id:o.id,...r,ref:t})}).displayName=es;var eu="SelectItem",[ec,ed]=D(eu),ef=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,disabled:l=!1,textValue:a,...u}=e,c=I(eu,n),d=Z(eu,n),f=c.value===o,[p,m]=r.useState(null!=a?a:""),[v,w]=r.useState(!1),y=(0,s.s)(t,e=>{var t;return null==(t=d.itemRefCallback)?void 0:t.call(d,e,o,l)}),x=(0,h.B)(),b=r.useRef("touch"),S=()=>{l||(c.onValueChange(o),c.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,C.jsx)(ec,{scope:n,value:o,disabled:l,textId:x,isSelected:f,onItemTextChange:r.useCallback(e=>{m(t=>{var n;return t||(null!=(n=null==e?void 0:e.textContent)?n:"").trim()})},[]),children:(0,C.jsx)(j.ItemSlot,{scope:n,value:o,disabled:l,textValue:p,children:(0,C.jsx)(g.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":v?"":void 0,"aria-selected":f&&v,"data-state":f?"checked":"unchecked","aria-disabled":l||void 0,"data-disabled":l?"":void 0,tabIndex:l?void 0:-1,...u,ref:y,onFocus:(0,i.m)(u.onFocus,()=>w(!0)),onBlur:(0,i.m)(u.onBlur,()=>w(!1)),onClick:(0,i.m)(u.onClick,()=>{"mouse"!==b.current&&S()}),onPointerUp:(0,i.m)(u.onPointerUp,()=>{"mouse"===b.current&&S()}),onPointerDown:(0,i.m)(u.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,i.m)(u.onPointerMove,e=>{if(b.current=e.pointerType,l){var t;null==(t=d.onItemLeave)||t.call(d)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,i.m)(u.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=d.onItemLeave)||t.call(d)}}),onKeyDown:(0,i.m)(u.onKeyDown,e=>{var t;((null==(t=d.searchRef)?void 0:t.current)===""||" "!==e.key)&&(P.includes(e.key)&&S()," "===e.key&&e.preventDefault())})})})})});ef.displayName=eu;var ep="SelectItemText",eh=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:l,style:i,...a}=e,u=I(ep,n),c=Z(ep,n),d=ed(ep,n),f=F(ep,n),[p,h]=r.useState(null),m=(0,s.s)(t,e=>h(e),d.onItemTextChange,e=>{var t;return null==(t=c.itemTextRefCallback)?void 0:t.call(c,e,d.value,d.disabled)}),v=null==p?void 0:p.textContent,w=r.useMemo(()=>(0,C.jsx)("option",{value:d.value,disabled:d.disabled,children:v},d.value),[d.disabled,d.value,v]),{onNativeOptionAdd:y,onNativeOptionRemove:x}=f;return(0,b.N)(()=>(y(w),()=>x(w)),[y,x,w]),(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)(g.sG.span,{id:d.textId,...a,ref:m}),d.isSelected&&u.valueNode&&!u.valueNodeHasChildren?o.createPortal(a.children,u.valueNode):null]})});eh.displayName=ep;var em="SelectItemIndicator",ev=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return ed(em,n).isSelected?(0,C.jsx)(g.sG.span,{"aria-hidden":!0,...r,ref:t}):null});ev.displayName=em;var eg="SelectScrollUpButton",ew=r.forwardRef((e,t)=>{let n=Z(eg,e.__scopeSelect),o=en(eg,e.__scopeSelect),[l,i]=r.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,b.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){i(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),l?(0,C.jsx)(eb,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ew.displayName=eg;var ey="SelectScrollDownButton",ex=r.forwardRef((e,t)=>{let n=Z(ey,e.__scopeSelect),o=en(ey,e.__scopeSelect),[l,i]=r.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,b.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),l?(0,C.jsx)(eb,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});ex.displayName=ey;var eb=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:o,...l}=e,a=Z("SelectScrollButton",n),s=r.useRef(null),u=L(n),c=r.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return r.useEffect(()=>()=>c(),[c]),(0,b.N)(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[u]),(0,C.jsx)(g.sG.div,{"aria-hidden":!0,...l,ref:t,style:{flexShrink:0,...l.style},onPointerDown:(0,i.m)(l.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(o,50))}),onPointerMove:(0,i.m)(l.onPointerMove,()=>{var e;null==(e=a.onItemLeave)||e.call(a),null===s.current&&(s.current=window.setInterval(o,50))}),onPointerLeave:(0,i.m)(l.onPointerLeave,()=>{c()})})});r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,C.jsx)(g.sG.div,{"aria-hidden":!0,...r,ref:t})}).displayName="SelectSeparator";var eS="SelectArrow";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=H(n),l=I(eS,n),i=Z(eS,n);return l.open&&"popper"===i.position?(0,C.jsx)(m.i3,{...o,...r,ref:t}):null}).displayName=eS;var eC=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,...l}=e,i=r.useRef(null),a=(0,s.s)(t,i),u=(0,S.Z)(o);return r.useEffect(()=>{let e=i.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(u!==o&&t){let n=new Event("change",{bubbles:!0});t.call(e,o),e.dispatchEvent(n)}},[u,o]),(0,C.jsx)(g.sG.select,{...l,style:{...R,...l.style},ref:a,defaultValue:o})});function eR(e){return""===e||void 0===e}function eA(e){let t=(0,y.c)(e),n=r.useRef(""),o=r.useRef(0),l=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),i=r.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,l,i]}function eT(e,t,n){var r,o;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,a=(r=e,o=Math.max(i,0),r.map((e,t)=>r[(o+t)%r.length]));1===l.length&&(a=a.filter(e=>e!==n));let s=a.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return s!==n?s:void 0}eC.displayName="SelectBubbleInput";var ek=V,eP=_,eE=G,ej=K,eL=U,eN=X,eD=eo,eM=ef,eH=eh,eO=ev,eI=ew,eB=ex},40968:(e,t,n)=>{n.d(t,{b:()=>a});var r=n(12115),o=n(63655),l=n(95155),i=r.forwardRef((e,t)=>(0,l.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null==(n=e.onMouseDown)||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var a=i},45503:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(12115);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},47863:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},66474:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},84616:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}}]);