'use client'

import { useState, useEffect, createContext, useContext, ReactNode } from 'react'
import { createPortal } from 'react-dom'
import { X, CheckCircle, AlertTriangle, AlertCircle, Info } from 'lucide-react'

export type ToastType = 'success' | 'error' | 'warning' | 'info'

export interface Toast {
  id: string
  type: ToastType
  title: string
  description?: string
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
}

interface ToastContextType {
  toasts: Toast[]
  addToast: (toast: Omit<Toast, 'id'>) => void
  removeToast: (id: string) => void
  clearToasts: () => void
}

const ToastContext = createContext<ToastContextType | undefined>(undefined)

export function useToast() {
  const context = useContext(ToastContext)
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider')
  }
  return context
}

const toastConfig = {
  success: {
    icon: CheckCircle,
    bgColor: 'bg-green-50',
    borderColor: 'border-green-200',
    iconColor: 'text-green-600',
    titleColor: 'text-green-900',
    descColor: 'text-green-700'
  },
  error: {
    icon: AlertCircle,
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
    iconColor: 'text-red-600',
    titleColor: 'text-red-900',
    descColor: 'text-red-700'
  },
  warning: {
    icon: AlertTriangle,
    bgColor: 'bg-orange-50',
    borderColor: 'border-orange-200',
    iconColor: 'text-orange-600',
    titleColor: 'text-orange-900',
    descColor: 'text-orange-700'
  },
  info: {
    icon: Info,
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    iconColor: 'text-blue-600',
    titleColor: 'text-blue-900',
    descColor: 'text-blue-700'
  }
}

function ToastItem({ toast, onRemove }: { toast: Toast; onRemove: (id: string) => void }) {
  const [isVisible, setIsVisible] = useState(false)
  const [isLeaving, setIsLeaving] = useState(false)
  const config = toastConfig[toast.type]
  const IconComponent = config.icon

  useEffect(() => {
    // Animazione di entrata
    const timer = setTimeout(() => setIsVisible(true), 50)
    return () => clearTimeout(timer)
  }, [])

  useEffect(() => {
    if (toast.duration !== 0) {
      const timer = setTimeout(() => {
        handleRemove()
      }, toast.duration || 5000)
      return () => clearTimeout(timer)
    }
  }, [toast.duration])

  const handleRemove = () => {
    setIsLeaving(true)
    setTimeout(() => onRemove(toast.id), 300)
  }

  return (
    <div
      className={`
        transform transition-all duration-300 ease-in-out mb-3
        ${isVisible && !isLeaving 
          ? 'translate-x-0 opacity-100 scale-100' 
          : 'translate-x-full opacity-0 scale-95'
        }
      `}
    >
      <div className={`
        max-w-sm w-full ${config.bgColor} ${config.borderColor} border-l-4 
        rounded-lg shadow-lg p-4 pointer-events-auto
      `}>
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <IconComponent className={`w-5 h-5 ${config.iconColor}`} />
          </div>
          
          <div className="ml-3 flex-1">
            <p className={`text-sm font-medium ${config.titleColor}`}>
              {toast.title}
            </p>
            {toast.description && (
              <p className={`mt-1 text-sm ${config.descColor}`}>
                {toast.description}
              </p>
            )}
            {toast.action && (
              <div className="mt-3">
                <button
                  onClick={toast.action.onClick}
                  className={`
                    text-sm font-medium ${config.iconColor} 
                    hover:underline focus:outline-none focus:underline
                  `}
                >
                  {toast.action.label}
                </button>
              </div>
            )}
          </div>
          
          <div className="ml-4 flex-shrink-0">
            <button
              onClick={handleRemove}
              className={`
                inline-flex ${config.descColor} hover:${config.titleColor} 
                focus:outline-none focus:${config.titleColor} transition-colors
              `}
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

function ToastContainer({ toasts, removeToast }: { toasts: Toast[]; removeToast: (id: string) => void }) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) return null

  return createPortal(
    <div className="fixed top-4 right-4 z-50 pointer-events-none">
      <div className="flex flex-col-reverse">
        {toasts.map((toast) => (
          <ToastItem
            key={toast.id}
            toast={toast}
            onRemove={removeToast}
          />
        ))}
      </div>
    </div>,
    document.body
  )
}

export function ToastProvider({ children }: { children: ReactNode }) {
  const [toasts, setToasts] = useState<Toast[]>([])

  const addToast = (toast: Omit<Toast, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9)
    setToasts(prev => [...prev, { ...toast, id }])
  }

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id))
  }

  const clearToasts = () => {
    setToasts([])
  }

  return (
    <ToastContext.Provider value={{ toasts, addToast, removeToast, clearToasts }}>
      {children}
      <ToastContainer toasts={toasts} removeToast={removeToast} />
    </ToastContext.Provider>
  )
}

// Hook di convenienza per toast comuni
export function useToastActions() {
  const { addToast } = useToast()

  return {
    success: (title: string, description?: string, action?: Toast['action']) =>
      addToast({ type: 'success', title, description, action }),
    
    error: (title: string, description?: string, action?: Toast['action']) =>
      addToast({ type: 'error', title, description, action, duration: 7000 }),
    
    warning: (title: string, description?: string, action?: Toast['action']) =>
      addToast({ type: 'warning', title, description, action, duration: 6000 }),
    
    info: (title: string, description?: string, action?: Toast['action']) =>
      addToast({ type: 'info', title, description, action }),

    // Toast specifici per azioni sui cavi
    cavoDisconnected: (cavoId: string) =>
      addToast({
        type: 'success',
        title: 'Cavo Scollegato',
        description: `Il cavo ${cavoId} è stato scollegato con successo.`
      }),

    pdfGenerated: (fileName: string, cavoId: string) =>
      addToast({
        type: 'success',
        title: 'PDF Generato',
        description: `Certificato per il cavo ${cavoId} salvato come ${fileName}.`,
        action: {
          label: 'Apri Cartella',
          onClick: () => {
            // Logica per aprire la cartella dei download
            console.log('Apertura cartella download...')
          }
        }
      }),

    certificationError: (cavoId: string, reason: string) =>
      addToast({
        type: 'error',
        title: 'Certificazione Fallita',
        description: `Impossibile certificare il cavo ${cavoId}: ${reason}`,
        duration: 8000
      }),

    actionInProgress: (action: string, cavoId: string) =>
      addToast({
        type: 'info',
        title: `${action} in Corso`,
        description: `Elaborazione del cavo ${cavoId}...`,
        duration: 3000
      })
  }
}
