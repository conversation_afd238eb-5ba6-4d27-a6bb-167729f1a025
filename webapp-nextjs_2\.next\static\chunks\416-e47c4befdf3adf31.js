"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[416],{1243:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},3493:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("cable",[["path",{d:"M17 21v-2a1 1 0 0 1-1-1v-1a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v1a1 1 0 0 1-1 1",key:"10bnsj"}],["path",{d:"M19 15V6.5a1 1 0 0 0-7 0v11a1 1 0 0 1-7 0V9",key:"1eqmu1"}],["path",{d:"M21 21v-2h-4",key:"14zm7j"}],["path",{d:"M3 5h4V3",key:"z442eg"}],["path",{d:"M7 5a1 1 0 0 1 1 1v1a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a1 1 0 0 1 1-1V3",key:"ebdjd7"}]])},6654:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let n=r(12115);function o(e,t){let r=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(r.current=u(e,n)),t&&(o.current=u(t,n))},[e,t])}function u(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6874:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return v},useLinkStatus:function(){return g}});let n=r(6966),o=r(95155),u=n._(r(12115)),a=r(82757),l=r(95227),i=r(69818),s=r(6654),c=r(69991),f=r(85929);r(43230);let d=r(24930),p=r(92664),h=r(6634);function m(e){return"string"==typeof e?e:(0,a.formatUrl)(e)}function v(e){let t,r,n,[a,v]=(0,u.useOptimistic)(d.IDLE_LINK_STATUS),g=(0,u.useRef)(null),{href:b,as:P,children:x,prefetch:_=null,passHref:j,replace:N,shallow:M,scroll:O,onClick:E,onMouseEnter:A,onTouchStart:w,legacyBehavior:C=!1,onNavigate:k,ref:T,unstable_dynamicOnHover:S,...L}=e;t=x,C&&("string"==typeof t||"number"==typeof t)&&(t=(0,o.jsx)("a",{children:t}));let R=u.default.useContext(l.AppRouterContext),I=!1!==_,U=null===_?i.PrefetchKind.AUTO:i.PrefetchKind.FULL,{href:D,as:z}=u.default.useMemo(()=>{let e=m(b);return{href:e,as:P?m(P):e}},[b,P]);C&&(r=u.default.Children.only(t));let F=C?r&&"object"==typeof r&&r.ref:T,K=u.default.useCallback(e=>(null!==R&&(g.current=(0,d.mountLinkInstance)(e,D,R,U,I,v)),()=>{g.current&&((0,d.unmountLinkForCurrentNavigation)(g.current),g.current=null),(0,d.unmountPrefetchableInstance)(e)}),[I,D,R,U,v]),$={ref:(0,s.useMergedRef)(K,F),onClick(e){C||"function"!=typeof E||E(e),C&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),R&&(e.defaultPrevented||function(e,t,r,n,o,a,l){let{nodeName:i}=e.currentTarget;if(!("A"===i.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),u.default.startTransition(()=>{if(l){let e=!1;if(l({preventDefault:()=>{e=!0}}),e)return}(0,h.dispatchNavigateAction)(r||t,o?"replace":"push",null==a||a,n.current)})}}(e,D,z,g,N,O,k))},onMouseEnter(e){C||"function"!=typeof A||A(e),C&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),R&&I&&(0,d.onNavigationIntent)(e.currentTarget,!0===S)},onTouchStart:function(e){C||"function"!=typeof w||w(e),C&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),R&&I&&(0,d.onNavigationIntent)(e.currentTarget,!0===S)}};return(0,c.isAbsoluteUrl)(z)?$.href=z:C&&!j&&("a"!==r.type||"href"in r.props)||($.href=(0,f.addBasePath)(z)),n=C?u.default.cloneElement(r,$):(0,o.jsx)("a",{...L,...$,children:t}),(0,o.jsx)(y.Provider,{value:a,children:n})}r(73180);let y=(0,u.createContext)(d.IDLE_LINK_STATUS),g=()=>(0,u.useContext)(y);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},35169:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},40646:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},40968:(e,t,r)=>{r.d(t,{b:()=>l});var n=r(12115),o=r(63655),u=r(95155),a=n.forwardRef((e,t)=>(0,u.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var l=a},46081:(e,t,r)=>{r.d(t,{A:()=>a,q:()=>u});var n=r(12115),o=r(95155);function u(e,t){let r=n.createContext(t),u=e=>{let{children:t,...u}=e,a=n.useMemo(()=>u,Object.values(u));return(0,o.jsx)(r.Provider,{value:a,children:t})};return u.displayName=e+"Provider",[u,function(o){let u=n.useContext(r);if(u)return u;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function a(e,t=[]){let r=[],u=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return u.scopeName=e,[function(t,u){let a=n.createContext(u),l=r.length;r=[...r,u];let i=t=>{let{scope:r,children:u,...i}=t,s=r?.[e]?.[l]||a,c=n.useMemo(()=>i,Object.values(i));return(0,o.jsx)(s.Provider,{value:c,children:u})};return i.displayName=t+"Provider",[i,function(r,o){let i=o?.[e]?.[l]||a,s=n.useContext(i);if(s)return s;if(void 0!==u)return u;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(u,...t)]}},55863:(e,t,r)=>{r.d(t,{C1:()=>x,bL:()=>P});var n=r(12115),o=r(46081),u=r(63655),a=r(95155),l="Progress",[i,s]=(0,o.A)(l),[c,f]=i(l),d=n.forwardRef((e,t)=>{var r,n,o,l;let{__scopeProgress:i,value:s=null,max:f,getValueLabel:d=m,...p}=e;(f||0===f)&&!g(f)&&console.error((r="".concat(f),n="Progress","Invalid prop `max` of value `".concat(r,"` supplied to `").concat(n,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let h=g(f)?f:100;null===s||b(s,h)||console.error((o="".concat(s),l="Progress","Invalid prop `value` of value `".concat(o,"` supplied to `").concat(l,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let P=b(s,h)?s:null,x=y(P)?d(P,h):void 0;return(0,a.jsx)(c,{scope:i,value:P,max:h,children:(0,a.jsx)(u.sG.div,{"aria-valuemax":h,"aria-valuemin":0,"aria-valuenow":y(P)?P:void 0,"aria-valuetext":x,role:"progressbar","data-state":v(P,h),"data-value":null!=P?P:void 0,"data-max":h,...p,ref:t})})});d.displayName=l;var p="ProgressIndicator",h=n.forwardRef((e,t)=>{var r;let{__scopeProgress:n,...o}=e,l=f(p,n);return(0,a.jsx)(u.sG.div,{"data-state":v(l.value,l.max),"data-value":null!=(r=l.value)?r:void 0,"data-max":l.max,...o,ref:t})});function m(e,t){return"".concat(Math.round(e/t*100),"%")}function v(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function y(e){return"number"==typeof e}function g(e){return y(e)&&!isNaN(e)&&e>0}function b(e,t){return y(e)&&!isNaN(e)&&e<=t&&e>=0}h.displayName=p;var P=d,x=h},63655:(e,t,r)=>{r.d(t,{hO:()=>i,sG:()=>l});var n=r(12115),o=r(47650),u=r(99708),a=r(95155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,u.TL)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...u}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?r:t,{...u,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function i(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},69991:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return g},MissingStaticPage:function(){return y},NormalizeError:function(){return m},PageNotFoundError:function(){return v},SP:function(){return d},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return i},getLocationOrigin:function(){return a},getURL:function(){return l},isAbsoluteUrl:function(){return u},isResSent:function(){return s},loadGetInitialProps:function(){return f},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),u=0;u<n;u++)o[u]=arguments[u];return r||(r=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,u=e=>o.test(e);function a(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function l(){let{href:e}=window.location,t=a();return e.substring(t.length)}function i(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function s(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function f(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await f(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&s(r))return n;if(!n)throw Object.defineProperty(Error('"'+i(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let d="undefined"!=typeof performance,p=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class m extends Error{}class v extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class g extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},73180:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},75525:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},78859:(e,t)=>{function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[r,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)t.append(r,n(e));else t.set(r,n(o));return t}function u(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return u},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return o}})},82757:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return u},formatWithValidation:function(){return l},urlObjectKeys:function(){return a}});let n=r(6966)._(r(78859)),o=/https?|ftp|gopher|file/;function u(e){let{auth:t,hostname:r}=e,u=e.protocol||"",a=e.pathname||"",l=e.hash||"",i=e.query||"",s=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?s=t+e.host:r&&(s=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(s+=":"+e.port)),i&&"object"==typeof i&&(i=String(n.urlQueryToSearchParams(i)));let c=e.search||i&&"?"+i||"";return u&&!u.endsWith(":")&&(u+=":"),e.slashes||(!u||o.test(u))&&!1!==s?(s="//"+(s||""),a&&"/"!==a[0]&&(a="/"+a)):s||(s=""),l&&"#"!==l[0]&&(l="#"+l),c&&"?"!==c[0]&&(c="?"+c),""+u+s+(a=a.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+l}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function l(e){return u(e)}},92664:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return u}});let n=r(69991),o=r(87102);function u(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,o.hasBasePath)(r.pathname)}catch(e){return!1}}}}]);