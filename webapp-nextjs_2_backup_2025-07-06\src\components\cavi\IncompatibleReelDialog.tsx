'use client'

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { AlertTriangle } from 'lucide-react'

interface Incompatibility {
  property: string
  cavoValue: string
  bobinaValue: string
  note?: string
}

interface Bobina {
  id_bobina: string
  tipologia: string
  formazione: string
  metri_residui: number
  fornitore?: string
}

interface Cavo {
  id_cavo: string
  tipologia: string
  formazione: string
  sistema?: string
  utility?: string
  metri_posati?: number
}

interface IncompatibleReelDialogProps {
  open: boolean
  onClose: () => void
  cavo: Cavo | null
  bobina: Bobina | null
  incompatibilities?: Incompatibility[]
  onConfirm: (bobina: Bobina, cavo: Cavo) => void
}

export default function IncompatibleReelDialog({
  open,
  onClose,
  cavo,
  bobina,
  incompatibilities: propIncompatibilities,
  onConfirm
}: IncompatibleReelDialogProps) {
  if (!cavo || !bobina) return null

  // Funzione per estrarre solo la parte "Y" dell'ID bobina
  const getBobinaNumber = (idBobina: string) => {
    if (idBobina === 'BOBINA_VUOTA') return 'BOBINA VUOTA'

    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}
    if (idBobina && idBobina.includes('_B')) {
      return idBobina.split('_B')[1]
    }
    return idBobina
  }

  // Usa le incompatibilità passate come prop o calcola quelle predefinite
  let incompatibilities = propIncompatibilities || []

  // Se non sono state passate incompatibilità, le calcoliamo qui
  if (incompatibilities.length === 0) {
    if (cavo.tipologia !== bobina.tipologia) {
      incompatibilities.push({
        property: 'Tipologia',
        cavoValue: cavo.tipologia || 'N/A',
        bobinaValue: bobina.tipologia || 'N/A'
      })
    }

    if (String(cavo.formazione) !== String(bobina.formazione)) {
      incompatibilities.push({
        property: 'Formazione',
        cavoValue: cavo.formazione || 'N/A',
        bobinaValue: bobina.formazione || 'N/A'
      })
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-amber-700">
            <AlertTriangle className="h-5 w-5" />
            Bobina incompatibile
          </DialogTitle>
          <DialogDescription>
            La bobina <strong>{getBobinaNumber(bobina.id_bobina)}</strong> non è compatibile con il cavo <strong>{cavo.id_cavo}</strong>.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <Alert className="border-amber-200 bg-amber-50">
            <AlertTriangle className="h-4 w-4 text-amber-600" />
            <AlertDescription className="text-amber-800">
              Le seguenti caratteristiche non corrispondono:
            </AlertDescription>
          </Alert>

          <div className="border rounded-lg overflow-hidden">
            <table className="w-full">
              <thead className="bg-gray-100">
                <tr>
                  <th className="px-4 py-2 text-left font-semibold">Caratteristica</th>
                  <th className="px-4 py-2 text-left font-semibold">Valore cavo</th>
                  <th className="px-4 py-2 text-left font-semibold">Valore bobina</th>
                </tr>
              </thead>
              <tbody>
                {incompatibilities.map((item, index) => (
                  <tr key={index} className="border-t">
                    <td className="px-4 py-2 font-medium">{item.property}</td>
                    <td className="px-4 py-2">
                      {item.cavoValue}
                      {item.note && <span className="text-sm text-gray-500 ml-1">{item.note}</span>}
                    </td>
                    <td className="px-4 py-2">{item.bobinaValue}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className="space-y-2">
            <p className="font-medium">Puoi scegliere di:</p>
            <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
              <li><strong>Utilizzare questa bobina incompatibile</strong> senza modificare le caratteristiche del cavo</li>
              <li><strong>Annullare l'operazione</strong> e selezionare un'altra bobina</li>
            </ul>
          </div>
        </div>

        <DialogFooter className="flex justify-between gap-2">
          <Button variant="outline" onClick={onClose}>
            Annulla
          </Button>
          <Button
            variant="default"
            className="bg-amber-600 hover:bg-amber-700 text-white"
            onClick={() => onConfirm && onConfirm(bobina, cavo)}
          >
            Usa Bobina Incompatibile
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
