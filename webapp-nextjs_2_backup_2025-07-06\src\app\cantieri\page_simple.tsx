'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { useAuth } from '@/contexts/AuthContext'
import { cantieriApi } from '@/lib/api'
import { Cantiere } from '@/types'
import { Building2, Plus, Search, Loader2 } from 'lucide-react'

export default function CantieriPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [cantieri, setCantieri] = useState<Cantiere[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated) {
      loadCantieri()
    }
  }, [isAuthenticated])

  const loadCantieri = async () => {
    try {
      setLoading(true)
      const data = await cantieriApi.getCantieri()
      setCantieri(data)
    } catch (error) {
      setError('Errore nel caricamento dei cantieri')
    } finally {
      setLoading(false)
    }
  }

  const filteredCantieri = cantieri.filter(cantiere =>
    cantiere.commessa.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cantiere.descrizione?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cantiere.nome_cliente?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="max-w-[90%] mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <div className="relative w-80">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Cerca cantieri..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8 w-full"
            />
          </div>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Nuovo Cantiere
        </Button>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Commessa</TableHead>
              <TableHead>Descrizione</TableHead>
              <TableHead>Cliente</TableHead>
              <TableHead>Data Creazione</TableHead>
              <TableHead>Codice</TableHead>
              <TableHead>Password</TableHead>
              <TableHead className="text-right">Azioni</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCantieri.map((cantiere) => (
              <TableRow key={cantiere.id_cantiere}>
                <TableCell className="font-medium">{cantiere.commessa}</TableCell>
                <TableCell>{cantiere.descrizione}</TableCell>
                <TableCell>{cantiere.nome_cliente}</TableCell>
                <TableCell>{new Date(cantiere.data_creazione).toLocaleDateString()}</TableCell>
                <TableCell>
                  <code className="text-sm bg-muted px-2 py-1 rounded">
                    {cantiere.codice_univoco}
                  </code>
                </TableCell>
                <TableCell>
                  <code className="text-sm bg-green-100 text-green-800 px-2 py-1 rounded">
                    {cantiere.password_cantiere ? '••••••••' : 'Non impostata'}
                  </code>
                </TableCell>
                <TableCell className="text-right">
                  <Button size="sm">
                    Gestisci
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>
    </div>
  )
}
