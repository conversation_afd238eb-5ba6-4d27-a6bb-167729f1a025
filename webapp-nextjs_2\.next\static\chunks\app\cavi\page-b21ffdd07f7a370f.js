(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3986],{34428:(e,a,i)=>{Promise.resolve().then(i.bind(i,96013))},46102:(e,a,i)=>{"use strict";i.d(a,{Bc:()=>n,ZI:()=>d,k$:()=>c,m_:()=>o});var s=i(95155),t=i(12115),l=i(89613),r=i(59434);let n=l.Kq,o=l.bL,c=l.l9,d=t.forwardRef((e,a)=>{let{className:i,sideOffset:t=4,...n}=e;return(0,s.jsx)(l.UC,{ref:a,sideOffset:t,className:(0,r.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",i),...n})});d.displayName=l.UC.displayName},46510:(e,a,i)=>{"use strict";i.d(a,{E:()=>v,ToastProvider:()=>p});var s=i(95155),t=i(12115),l=i(47650),r=i(40646),n=i(85339),o=i(1243),c=i(81284),d=i(54416);let m=(0,t.createContext)(void 0),x={success:{icon:r.A,bgColor:"bg-green-50",borderColor:"border-green-200",iconColor:"text-green-600",titleColor:"text-green-900",descColor:"text-green-700"},error:{icon:n.A,bgColor:"bg-red-50",borderColor:"border-red-200",iconColor:"text-red-600",titleColor:"text-red-900",descColor:"text-red-700"},warning:{icon:o.A,bgColor:"bg-orange-50",borderColor:"border-orange-200",iconColor:"text-orange-600",titleColor:"text-orange-900",descColor:"text-orange-700"},info:{icon:c.A,bgColor:"bg-blue-50",borderColor:"border-blue-200",iconColor:"text-blue-600",titleColor:"text-blue-900",descColor:"text-blue-700"}};function u(e){let{toast:a,onRemove:i}=e,[l,r]=(0,t.useState)(!1),[n,o]=(0,t.useState)(!1),c=x[a.type],m=c.icon;(0,t.useEffect)(()=>{let e=setTimeout(()=>r(!0),50);return()=>clearTimeout(e)},[]),(0,t.useEffect)(()=>{if(0!==a.duration){let e=setTimeout(()=>{u()},a.duration||5e3);return()=>clearTimeout(e)}},[a.duration]);let u=()=>{o(!0),setTimeout(()=>i(a.id),300)};return(0,s.jsx)("div",{className:"\n        transform transition-all duration-300 ease-in-out mb-3\n        ".concat(l&&!n?"translate-x-0 opacity-100 scale-100":"translate-x-full opacity-0 scale-95","\n      "),children:(0,s.jsx)("div",{className:"\n        max-w-sm w-full ".concat(c.bgColor," ").concat(c.borderColor," border-l-4 \n        rounded-lg shadow-lg p-4 pointer-events-auto\n      "),children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(m,{className:"w-5 h-5 ".concat(c.iconColor)})}),(0,s.jsxs)("div",{className:"ml-3 flex-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium ".concat(c.titleColor),children:a.title}),a.description&&(0,s.jsx)("p",{className:"mt-1 text-sm ".concat(c.descColor),children:a.description}),a.action&&(0,s.jsx)("div",{className:"mt-3",children:(0,s.jsx)("button",{onClick:a.action.onClick,className:"\n                    text-sm font-medium ".concat(c.iconColor," \n                    hover:underline focus:outline-none focus:underline\n                  "),children:a.action.label})})]}),(0,s.jsx)("div",{className:"ml-4 flex-shrink-0",children:(0,s.jsx)("button",{onClick:u,className:"\n                inline-flex ".concat(c.descColor," hover:").concat(c.titleColor," \n                focus:outline-none focus:").concat(c.titleColor," transition-colors\n              "),children:(0,s.jsx)(d.A,{className:"w-4 h-4"})})})]})})})}function h(e){let{toasts:a,removeToast:i}=e,[r,n]=(0,t.useState)(!1);return((0,t.useEffect)(()=>{n(!0)},[]),r)?(0,l.createPortal)((0,s.jsx)("div",{className:"fixed top-4 right-4 z-50 pointer-events-none",children:(0,s.jsx)("div",{className:"flex flex-col-reverse",children:a.map(e=>(0,s.jsx)(u,{toast:e,onRemove:i},e.id))})}),document.body):null}function p(e){let{children:a}=e,[i,l]=(0,t.useState)([]),r=e=>{l(a=>a.filter(a=>a.id!==e))};return(0,s.jsxs)(m.Provider,{value:{toasts:i,addToast:e=>{let a=Math.random().toString(36).substr(2,9);l(i=>[...i,{...e,id:a}])},removeToast:r,clearToasts:()=>{l([])}},children:[a,(0,s.jsx)(h,{toasts:i,removeToast:r})]})}function v(){let{addToast:e}=function(){let e=(0,t.useContext)(m);if(!e)throw Error("useToast must be used within a ToastProvider");return e}();return{success:(a,i,s)=>e({type:"success",title:a,description:i,action:s}),error:(a,i,s)=>e({type:"error",title:a,description:i,action:s,duration:7e3}),warning:(a,i,s)=>e({type:"warning",title:a,description:i,action:s,duration:6e3}),info:(a,i,s)=>e({type:"info",title:a,description:i,action:s}),cavoDisconnected:a=>e({type:"success",title:"Cavo Scollegato",description:"Il cavo ".concat(a," \xe8 stato scollegato con successo.")}),pdfGenerated:(a,i)=>e({type:"success",title:"PDF Generato",description:"Certificato per il cavo ".concat(i," salvato come ").concat(a,"."),action:{label:"Apri Cartella",onClick:()=>{console.log("Apertura cartella download...")}}}),certificationError:(a,i)=>e({type:"error",title:"Certificazione Fallita",description:"Impossibile certificare il cavo ".concat(a,": ").concat(i),duration:8e3}),actionInProgress:(a,i)=>e({type:"info",title:"".concat(a," in Corso"),description:"Elaborazione del cavo ".concat(i,"..."),duration:3e3})}}},47262:(e,a,i)=>{"use strict";i.d(a,{S:()=>n});var s=i(95155);i(12115);var t=i(76981),l=i(5196),r=i(59434);function n(e){let{className:a,...i}=e;return(0,s.jsx)(t.bL,{"data-slot":"checkbox",className:(0,r.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...i,children:(0,s.jsx)(t.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(l.A,{className:"size-3.5"})})})}},54165:(e,a,i)=>{"use strict";i.d(a,{Cf:()=>m,Es:()=>u,L3:()=>h,c7:()=>x,lG:()=>n,rr:()=>p,zM:()=>o});var s=i(95155);i(12115);var t=i(15452),l=i(54416),r=i(59434);function n(e){let{...a}=e;return(0,s.jsx)(t.bL,{"data-slot":"dialog",...a})}function o(e){let{...a}=e;return(0,s.jsx)(t.l9,{"data-slot":"dialog-trigger",...a})}function c(e){let{...a}=e;return(0,s.jsx)(t.ZL,{"data-slot":"dialog-portal",...a})}function d(e){let{className:a,...i}=e;return(0,s.jsx)(t.hJ,{"data-slot":"dialog-overlay",className:(0,r.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...i})}function m(e){let{className:a,children:i,showCloseButton:n=!0,...o}=e;return(0,s.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,s.jsx)(d,{}),(0,s.jsxs)(t.UC,{"data-slot":"dialog-content",className:(0,r.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...o,children:[i,n&&(0,s.jsxs)(t.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(l.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x(e){let{className:a,...i}=e;return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,r.cn)("flex flex-col gap-2 text-center sm:text-left",a),...i})}function u(e){let{className:a,...i}=e;return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,r.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...i})}function h(e){let{className:a,...i}=e;return(0,s.jsx)(t.hE,{"data-slot":"dialog-title",className:(0,r.cn)("text-lg leading-none font-semibold",a),...i})}function p(e){let{className:a,...i}=e;return(0,s.jsx)(t.VY,{"data-slot":"dialog-description",className:(0,r.cn)("text-muted-foreground text-sm",a),...i})}},55365:(e,a,i)=>{"use strict";i.d(a,{Fc:()=>o,TN:()=>c});var s=i(95155),t=i(12115),l=i(74466),r=i(59434);let n=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=t.forwardRef((e,a)=>{let{className:i,variant:t,...l}=e;return(0,s.jsx)("div",{ref:a,role:"alert",className:(0,r.cn)(n({variant:t}),i),...l})});o.displayName="Alert",t.forwardRef((e,a)=>{let{className:i,...t}=e;return(0,s.jsx)("h5",{ref:a,className:(0,r.cn)("mb-1 font-medium leading-none tracking-tight",i),...t})}).displayName="AlertTitle";let c=t.forwardRef((e,a)=>{let{className:i,...t}=e;return(0,s.jsx)("div",{ref:a,className:(0,r.cn)("text-sm [&_p]:leading-relaxed",i),...t})});c.displayName="AlertDescription"},87481:(e,a,i)=>{"use strict";i.d(a,{dj:()=>x});var s=i(12115);let t=0,l=new Map,r=e=>{if(l.has(e))return;let a=setTimeout(()=>{l.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);l.set(e,a)},n=(e,a)=>{switch(a.type){case"ADD_TOAST":return{...e,toasts:[a.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===a.toast.id?{...e,...a.toast}:e)};case"DISMISS_TOAST":{let{toastId:i}=a;return i?r(i):e.toasts.forEach(e=>{r(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===i||void 0===i?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===a.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==a.toastId)}}},o=[],c={toasts:[]};function d(e){c=n(c,e),o.forEach(e=>{e(c)})}function m(e){let{...a}=e,i=(t=(t+1)%Number.MAX_VALUE).toString(),s=()=>d({type:"DISMISS_TOAST",toastId:i});return d({type:"ADD_TOAST",toast:{...a,id:i,open:!0,onOpenChange:e=>{e||s()}}}),{id:i,dismiss:s,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:i}})}}function x(){let[e,a]=(0,s.useState)(c);return(0,s.useEffect)(()=>(o.push(a),()=>{let e=o.indexOf(a);e>-1&&o.splice(e,1)}),[]),{...e,toast:m,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},88539:(e,a,i)=>{"use strict";i.d(a,{T:()=>r});var s=i(95155),t=i(12115),l=i(59434);let r=t.forwardRef((e,a)=>{let{className:i,...t}=e;return(0,s.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",i),ref:a,...t})});r.displayName="Textarea"},96013:(e,a,i)=>{"use strict";i.r(a),i.d(a,{default:()=>eK});var s=i(95155),t=i(12115),l=i(35695),r=i(66695),n=i(30285),o=i(55365),c=i(40283),d=i(25731),m=i(26126),x=i(47262);i(63743);var u=i(85127),h=i(62523),p=i(59409),v=i(20547),g=i(59434);let b=v.bL,j=v.l9,f=t.forwardRef((e,a)=>{let{className:i,align:t="center",sideOffset:l=4,...r}=e;return(0,s.jsx)(v.ZL,{children:(0,s.jsx)(v.UC,{ref:a,align:t,sideOffset:l,className:(0,g.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",i),...r})})});f.displayName=v.UC.displayName;var N=i(21492),_=i(39881),y=i(58832),C=i(54416),z=i(66932),w=i(52278),A=i(42355),S=i(13052),E=i(12767);function I(e){let{data:a=[],columns:i=[],loading:l=!1,emptyMessage:o="Nessun dato disponibile",onFilteredDataChange:c,renderRow:d,className:x,pagination:h=!0,defaultRowsPerPage:v=25}=e,[I,T]=(0,t.useState)({key:null,direction:null}),[F,O]=(0,t.useState)({}),[D,R]=(0,t.useState)({}),[P,L]=(0,t.useState)(0),[B,M]=(0,t.useState)(v),$=e=>[...new Set(a.map(a=>a[e]).filter(Boolean))].sort(),U=(0,t.useMemo)(()=>{let e=[...a];return Object.entries(F).forEach(a=>{let[i,s]=a;!s.value||Array.isArray(s.value)&&0===s.value.length||"string"==typeof s.value&&""===s.value.trim()||(e=e.filter(e=>{let a=e[i];if("select"===s.type)return(Array.isArray(s.value)?s.value:[s.value]).includes(a);if("text"===s.type){let e=s.value.toLowerCase(),i=String(a||"").toLowerCase();return"equals"===s.operator?i===e:i.includes(e)}if("number"===s.type){let e=parseFloat(a),i=parseFloat(s.value);if(isNaN(e)||isNaN(i))return!1;switch(s.operator){case"equals":default:return e===i;case"gt":return e>i;case"lt":return e<i;case"gte":return e>=i;case"lte":return e<=i}}return!0}))}),I.key&&I.direction&&e.sort((e,a)=>{let i=e[I.key],s=a[I.key];if(null==i&&null==s)return 0;if(null==i)return"asc"===I.direction?-1:1;if(null==s)return"asc"===I.direction?1:-1;let t=parseFloat(i),l=parseFloat(s),r=!isNaN(t)&&!isNaN(l),n=0;return n=r?t-l:String(i).localeCompare(String(s)),"asc"===I.direction?n:-n}),e},[a,F,I]),J=(0,t.useMemo)(()=>{if(!h)return U;let e=P*B,a=e+B;return U.slice(e,a)},[U,P,B,h]);(0,t.useEffect)(()=>{L(0)},[F]);let V=Math.ceil(U.length/B),G=P*B+1,Z=Math.min((P+1)*B,U.length);(0,t.useEffect)(()=>{c&&c(U)},[U,c]);let q=e=>{let a=i.find(a=>a.field===e);null!=a&&a.disableSort||T(a=>{if(a.key===e){if("asc"===a.direction)return{key:e,direction:"desc"};if("desc"===a.direction)return{key:null,direction:null}}return{key:e,direction:"asc"}})},H=(e,a)=>{O(i=>({...i,[e]:{...i[e],...a}}))},W=e=>{O(a=>{let i={...a};return delete i[e],i})},Q=e=>I.key!==e?(0,s.jsx)(N.A,{className:"h-3 w-3"}):"asc"===I.direction?(0,s.jsx)(_.A,{className:"h-3 w-3"}):"desc"===I.direction?(0,s.jsx)(y.A,{className:"h-3 w-3"}):(0,s.jsx)(N.A,{className:"h-3 w-3"}),Y=Object.keys(F).length>0;return l?(0,s.jsx)(r.Zp,{className:x,children:(0,s.jsx)(r.Wu,{className:"p-6",children:(0,s.jsx)("div",{className:"text-center",children:"Caricamento..."})})}):(0,s.jsxs)("div",{className:x,children:[Y&&(0,s.jsxs)("div",{className:"mb-4 flex flex-wrap gap-2 items-center",children:[(0,s.jsx)("span",{className:"text-sm text-muted-foreground",children:"Filtri attivi:"}),Object.entries(F).map(e=>{let[a,t]=e,l=i.find(e=>e.field===a);if(!l)return null;let r=Array.isArray(t.value)?t.value.join(", "):String(t.value);return(0,s.jsxs)(m.E,{variant:"secondary",className:"gap-1",children:[l.headerName,": ",r,(0,s.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-auto p-0 hover:bg-transparent",onClick:()=>W(a),children:(0,s.jsx)(C.A,{className:"h-3 w-3"})})]},a)}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>{O({})},className:"h-6 px-2 text-xs",children:"Pulisci tutti"})]}),(0,s.jsx)(r.Zp,{children:(0,s.jsx)(r.Wu,{className:"p-0",children:(0,s.jsxs)(u.XI,{children:[(0,s.jsx)(u.A0,{children:(0,s.jsx)(u.Hj,{className:"bg-mariner-50 hover:bg-mariner-50",children:i.map(e=>(0,s.jsx)(u.nd,{className:(0,g.cn)("font-semibold text-mariner-900 border-b border-mariner-200","center"===e.align&&"text-center","right"===e.align&&"text-right"),style:{width:e.width,...e.headerStyle},children:e.renderHeader?e.renderHeader():(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,s.jsx)("span",{className:"truncate",children:e.headerName}),(0,s.jsxs)("div",{className:"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:[!e.disableSort&&(0,s.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-4 w-4 p-0 hover:bg-mariner-100",onClick:()=>q(e.field),children:Q(e.field)}),!e.disableFilter&&(0,s.jsxs)(b,{open:D[e.field],onOpenChange:a=>R(i=>({...i,[e.field]:a})),children:[(0,s.jsx)(j,{asChild:!0,children:(0,s.jsx)(n.$,{variant:"ghost",size:"sm",className:(0,g.cn)("h-4 w-4 p-0 hover:bg-mariner-100",F[e.field]&&"text-mariner-600 opacity-100"),children:(0,s.jsx)(z.A,{className:"h-2.5 w-2.5"})})}),(0,s.jsx)(f,{className:"w-64",align:"start",children:(0,s.jsx)(k,{column:e,data:a,currentFilter:F[e.field],onFilterChange:a=>H(e.field,a),onClearFilter:()=>W(e.field),getUniqueValues:()=>$(e.field)})})]})]})]}),F[e.field]&&(0,s.jsx)("div",{className:"absolute -top-1 -right-1 h-2 w-2 bg-mariner-600 rounded-full"})]})},e.field))})}),(0,s.jsx)(u.BF,{children:J.length>0?J.map((e,a)=>d?d(e,P*B+a):(0,s.jsx)(u.Hj,{className:"hover:bg-mariner-50 border-b border-mariner-100",children:i.map(a=>(0,s.jsx)(u.nA,{className:(0,g.cn)("py-2 px-4","center"===a.align&&"text-center","right"===a.align&&"text-right"),style:a.cellStyle,children:a.renderCell?a.renderCell(e):e[a.field]},a.field))},a)):(0,s.jsx)(u.Hj,{children:(0,s.jsx)(u.nA,{colSpan:i.length,className:"text-center py-8 text-muted-foreground",children:o})})})]})})}),h&&U.length>0&&(0,s.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"text-sm text-muted-foreground",children:"Righe per pagina:"}),(0,s.jsxs)(p.l6,{value:B.toString(),onValueChange:e=>{M(Number(e)),L(0)},children:[(0,s.jsx)(p.bq,{className:"w-20",children:(0,s.jsx)(p.yv,{})}),(0,s.jsxs)(p.gC,{children:[(0,s.jsx)(p.eb,{value:"10",children:"10"}),(0,s.jsx)(p.eb,{value:"25",children:"25"}),(0,s.jsx)(p.eb,{value:"50",children:"50"}),(0,s.jsx)(p.eb,{value:"100",children:"100"}),(0,s.jsx)(p.eb,{value:U.length.toString(),children:"Tutto"})]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"text-sm text-muted-foreground",children:U.length>0?"".concat(G,"-").concat(Z," di ").concat(U.length):"0 di 0"}),(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>L(0),disabled:0===P,className:"h-8 w-8 p-0",children:(0,s.jsx)(w.A,{className:"h-4 w-4"})}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>L(e=>Math.max(0,e-1)),disabled:0===P,className:"h-8 w-8 p-0",children:(0,s.jsx)(A.A,{className:"h-4 w-4"})}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>L(e=>Math.min(V-1,e+1)),disabled:P>=V-1,className:"h-8 w-8 p-0",children:(0,s.jsx)(S.A,{className:"h-4 w-4"})}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>L(V-1),disabled:P>=V-1,className:"h-8 w-8 p-0",children:(0,s.jsx)(E.A,{className:"h-4 w-4"})})]})]})]})]})}function k(e){let{column:a,currentFilter:i,onFilterChange:l,onClearFilter:r,getUniqueValues:o}=e,[c,d]=(0,t.useState)((null==i?void 0:i.value)||""),[m,u]=(0,t.useState)((null==i?void 0:i.operator)||"contains"),v=o(),g="number"!==a.dataType&&v.length<=20,b="number"===a.dataType,j=()=>{g?l({type:"select",value:Array.isArray(c)?c:[c]}):b?l({type:"number",value:c,operator:m}):l({type:"text",value:c,operator:m})};return(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"font-medium text-sm",children:["Filtra ",a.headerName]}),g?(0,s.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:v.map(e=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(x.S,{id:"filter-".concat(e),checked:Array.isArray(c)?c.includes(e):c===e,onCheckedChange:a=>{Array.isArray(c)?d(a?[...c,e]:c.filter(a=>a!==e)):d(a?[e]:[])}}),(0,s.jsx)("label",{htmlFor:"filter-".concat(e),className:"text-sm",children:e})]},e))}):(0,s.jsxs)("div",{className:"space-y-2",children:[b&&(0,s.jsxs)(p.l6,{value:m,onValueChange:u,children:[(0,s.jsx)(p.bq,{children:(0,s.jsx)(p.yv,{})}),(0,s.jsxs)(p.gC,{children:[(0,s.jsx)(p.eb,{value:"equals",children:"Uguale a"}),(0,s.jsx)(p.eb,{value:"gt",children:"Maggiore di"}),(0,s.jsx)(p.eb,{value:"lt",children:"Minore di"}),(0,s.jsx)(p.eb,{value:"gte",children:"Maggiore o uguale"}),(0,s.jsx)(p.eb,{value:"lte",children:"Minore o uguale"})]})]}),!b&&(0,s.jsxs)(p.l6,{value:m,onValueChange:u,children:[(0,s.jsx)(p.bq,{children:(0,s.jsx)(p.yv,{})}),(0,s.jsxs)(p.gC,{children:[(0,s.jsx)(p.eb,{value:"contains",children:"Contiene"}),(0,s.jsx)(p.eb,{value:"equals",children:"Uguale a"})]})]}),(0,s.jsx)(h.p,{placeholder:"Cerca ".concat(a.headerName.toLowerCase(),"..."),value:c,onChange:e=>d(e.target.value),onKeyDown:e=>"Enter"===e.key&&j()})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(n.$,{size:"sm",onClick:j,children:"Applica"}),(0,s.jsx)(n.$,{size:"sm",variant:"outline",onClick:r,children:"Pulisci"})]})]})}var T=i(85057),F=i(47924),O=i(47863),D=i(66474),R=i(19145),P=i(18979),L=i(40646),B=i(71539),M=i(37108),$=i(381),U=i(46102);function J(e){let{cavi:a=[],onFilteredDataChange:i,loading:l=!1,selectionEnabled:o=!1,onSelectionToggle:c}=e,[d,x]=(0,t.useState)(""),[u,v]=(0,t.useState)("contains"),[g,b]=(0,t.useState)(!1),[j,f]=(0,t.useState)("all"),[N,_]=(0,t.useState)("all"),[y,w]=(0,t.useState)("all"),[A,S]=(0,t.useState)("all"),[E,I]=(0,t.useState)(""),[k,J]=(0,t.useState)(""),V=(0,t.useCallback)(e=>e?e.toString().toLowerCase().trim():"",[]),G=(0,t.useCallback)(e=>{let a=e.match(/^([A-Z]+)(\d+)([A-Z]*)$/);return a?{prefix:a[1],number:a[2],suffix:a[3]||""}:{prefix:"",number:e,suffix:""}},[]),Z=(0,t.useCallback)((e,a,i)=>{let s=V(a);if(!s)return!0;let t=V(e.id_cavo),{prefix:l,number:r,suffix:n}=G(e.id_cavo||""),o=V(e.tipologia),c=V(e.formazione||e.sezione),d=V(e.utility),m=V(e.sistema),x=V(e.da||e.ubicazione_partenza),u=V(e.a||e.ubicazione_arrivo),h=V(e.utenza_partenza),p=V(e.utenza_arrivo),v=[t,l,r,n,o,c,d,m,x,u,h,p,V(e.id_bobina),"BOBINA_VUOTA"===e.id_bobina?"bobina vuota":null===e.id_bobina?"":V(e.id_bobina)],g=[{value:e.metri_teorici,name:"metri_teorici"},{value:e.metratura_reale||e.metri_posati,name:"metratura_reale"},{value:parseFloat(c),name:"formazione"}],b=s.match(/^([><=]+)(\d+(?:\.\d+)?)$/);if(b){let e=b[1],a=parseFloat(b[2]);return g.some(i=>{if(null==i.value||isNaN(i.value))return!1;switch(e){case">":return i.value>a;case">=":return i.value>=a;case"<":return i.value<a;case"<=":return i.value<=a;case"=":return i.value===a;default:return!1}})}let j=parseFloat(s);return!!(!isNaN(j)&&g.some(e=>null!=e.value&&!isNaN(e.value)&&e.value===j))||(i?v.some(e=>e===s):v.some(e=>e.includes(s)))},[V,G]),q=(0,t.useCallback)(e=>{let a=e;return"all"!==j&&(a=a.filter(e=>{switch(j){case"installati":return"Installato"===e.stato_installazione||e.metri_posati&&e.metri_posati>0||e.metratura_reale&&e.metratura_reale>0;case"in_corso":return"In corso"===e.stato_installazione;case"da_installare":return"Installato"!==e.stato_installazione&&"In corso"!==e.stato_installazione&&!(e.metri_posati&&e.metri_posati>0)&&!(e.metratura_reale&&e.metratura_reale>0);default:return!0}})),"all"!==N&&(a=a.filter(e=>{let a=e.collegamento||e.collegamenti||0;switch(N){case"collegati":return 3===a;case"parziali":return 1===a||2===a;case"non_collegati":return 0===a;default:return!0}})),"all"!==y&&(a=a.filter(e=>{let a=!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato;return"certificati"===y?a:!a})),"all"!==A&&(a=a.filter(e=>V(e.sistema)===V(A))),(E||k)&&(a=a.filter(e=>{let a=e.metri_posati||e.metratura_reale||0,i=E?parseFloat(E):0,s=k?parseFloat(k):1/0;return a>=i&&a<=s})),a},[j,N,y,A,E,k,V]),H=(0,t.useCallback)(()=>{let e=a;if(d.trim()){let a=d.split(",").map(e=>e.trim()).filter(e=>e.length>0);e="equals"===u?1===a.length?e.filter(e=>Z(e,a[0],!0)):e.filter(e=>a.every(a=>Z(e,a,!0))):e.filter(e=>a.some(a=>Z(e,a,!1)))}e=q(e),null==i||i(e)},[d,u,a,Z,q]),W=(0,t.useMemo)(()=>({systems:[...new Set(a.map(e=>e.sistema).filter(Boolean))].sort()}),[a]),Q=()=>{x(""),f("all"),_("all"),w("all"),S("all"),I(""),J("")},Y=d.trim()||"all"!==j||"all"!==N||"all"!==y||"all"!==A||E||k;(0,t.useEffect)(()=>{H()},[d,u,a,j,N,y,A,E,k]);let X=e=>{x(e)};return(0,s.jsx)(r.Zp,{className:"mb-4 shadow-sm border-slate-200",children:(0,s.jsxs)(r.Wu,{className:"p-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,s.jsxs)("div",{className:"flex-1 relative",children:[(0,s.jsx)(F.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,s.jsx)(h.p,{placeholder:"Ricerca intelligente cavi (ID, sistema, metri, stato...)...",value:d,onChange:e=>X(e.target.value),disabled:l,className:"pl-10 pr-12 h-10 border-slate-300 focus:border-blue-500 focus:ring-blue-500"}),d&&(0,s.jsx)(n.$,{variant:"ghost",size:"sm",className:"absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-slate-100",onClick:()=>{x(""),v("contains")},children:(0,s.jsx)(C.A,{className:"h-3 w-3"})})]}),(0,s.jsx)(U.Bc,{children:(0,s.jsxs)(U.m_,{children:[(0,s.jsx)(U.k$,{asChild:!0,children:(0,s.jsx)("div",{className:"w-36",children:(0,s.jsxs)(p.l6,{value:u,onValueChange:e=>v(e),children:[(0,s.jsx)(p.bq,{className:"h-10 border-slate-300",children:(0,s.jsx)(p.yv,{})}),(0,s.jsxs)(p.gC,{children:[(0,s.jsx)(p.eb,{value:"contains",children:"Contiene"}),(0,s.jsx)(p.eb,{value:"equals",children:"Uguale a"})]})]})})}),(0,s.jsx)(U.ZI,{children:(0,s.jsx)("p",{children:"Modalit\xe0 di ricerca: contiene o corrispondenza esatta"})})]})}),(0,s.jsx)(U.Bc,{children:(0,s.jsxs)(U.m_,{children:[(0,s.jsx)(U.k$,{asChild:!0,children:(0,s.jsxs)(n.$,{variant:g?"default":"outline",size:"sm",onClick:()=>b(!g),className:"flex items-center gap-2 transition-all duration-200 hover:scale-105",children:[(0,s.jsx)(z.A,{className:"h-4 w-4"}),"Filtri",g?(0,s.jsx)(O.A,{className:"h-4 w-4"}):(0,s.jsx)(D.A,{className:"h-4 w-4"}),Y&&(0,s.jsx)(m.E,{variant:"secondary",className:"ml-1 bg-blue-100 text-blue-800 text-xs",children:"Attivi"})]})}),(0,s.jsx)(U.ZI,{children:(0,s.jsx)("p",{children:"Apri filtri avanzati per ricerca dettagliata"})})]})}),Y&&(0,s.jsx)(U.Bc,{children:(0,s.jsxs)(U.m_,{children:[(0,s.jsx)(U.k$,{asChild:!0,children:(0,s.jsxs)(n.$,{variant:"outline",size:"sm",onClick:Q,disabled:l,className:"flex items-center gap-2 transition-all duration-200",children:[(0,s.jsx)(C.A,{className:"h-4 w-4"}),"Pulisci"]})}),(0,s.jsx)(U.ZI,{children:(0,s.jsx)("p",{children:"Rimuovi tutti i filtri attivi"})})]})}),c&&(0,s.jsx)(U.Bc,{children:(0,s.jsxs)(U.m_,{children:[(0,s.jsx)(U.k$,{asChild:!0,children:(0,s.jsxs)(n.$,{variant:o?"default":"outline",size:"sm",onClick:c,className:"flex items-center gap-2 transition-all duration-200 hover:scale-105 font-normal hover:font-bold",children:[o?(0,s.jsx)(R.A,{className:"h-4 w-4"}):(0,s.jsx)(P.A,{className:"h-4 w-4"}),o?"Disabilita Selezione":"Abilita Selezione"]})}),(0,s.jsx)(U.ZI,{children:(0,s.jsx)("p",{children:o?"Disabilita la selezione multipla":"Abilita la selezione multipla"})})]})})]}),g&&(0,s.jsxs)("div",{className:"mt-4 pt-4 border-t border-slate-200",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(T.J,{className:"text-sm font-medium text-slate-700 flex items-center gap-2",children:[(0,s.jsx)(L.A,{className:"w-4 h-4 text-green-600"}),"Stato Installazione"]}),(0,s.jsxs)(p.l6,{value:j,onValueChange:f,children:[(0,s.jsx)(p.bq,{className:"h-9 border-slate-300",children:(0,s.jsx)(p.yv,{placeholder:"Tutti gli stati"})}),(0,s.jsxs)(p.gC,{children:[(0,s.jsx)(p.eb,{value:"all",children:"Tutti gli stati"}),(0,s.jsx)(p.eb,{value:"installati",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"Installati"]})}),(0,s.jsx)(p.eb,{value:"in_corso",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),"In Corso"]})}),(0,s.jsx)(p.eb,{value:"da_installare",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-gray-500 rounded-full"}),"Da Installare"]})})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(T.J,{className:"text-sm font-medium text-slate-700 flex items-center gap-2",children:[(0,s.jsx)(B.A,{className:"w-4 h-4 text-blue-600"}),"Collegamento"]}),(0,s.jsxs)(p.l6,{value:N,onValueChange:_,children:[(0,s.jsx)(p.bq,{className:"h-9 border-slate-300",children:(0,s.jsx)(p.yv,{placeholder:"Tutti i collegamenti"})}),(0,s.jsxs)(p.gC,{children:[(0,s.jsx)(p.eb,{value:"all",children:"Tutti i collegamenti"}),(0,s.jsx)(p.eb,{value:"collegati",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"Collegati (3/3)"]})}),(0,s.jsx)(p.eb,{value:"parziali",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),"Parziali (1-2/3)"]})}),(0,s.jsx)(p.eb,{value:"non_collegati",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-gray-500 rounded-full"}),"Non Collegati (0/3)"]})})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(T.J,{className:"text-sm font-medium text-slate-700 flex items-center gap-2",children:[(0,s.jsx)(M.A,{className:"w-4 h-4 text-purple-600"}),"Certificazione"]}),(0,s.jsxs)(p.l6,{value:y,onValueChange:w,children:[(0,s.jsx)(p.bq,{className:"h-9 border-slate-300",children:(0,s.jsx)(p.yv,{placeholder:"Tutte le certificazioni"})}),(0,s.jsxs)(p.gC,{children:[(0,s.jsx)(p.eb,{value:"all",children:"Tutte le certificazioni"}),(0,s.jsx)(p.eb,{value:"certificati",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-purple-500 rounded-full"}),"Certificati"]})}),(0,s.jsx)(p.eb,{value:"non_certificati",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-gray-500 rounded-full"}),"Non Certificati"]})})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(T.J,{className:"text-sm font-medium text-slate-700 flex items-center gap-2",children:[(0,s.jsx)($.A,{className:"w-4 h-4 text-indigo-600"}),"Sistema"]}),(0,s.jsxs)(p.l6,{value:A,onValueChange:S,children:[(0,s.jsx)(p.bq,{className:"h-9 border-slate-300",children:(0,s.jsx)(p.yv,{placeholder:"Tutti i sistemi"})}),(0,s.jsxs)(p.gC,{children:[(0,s.jsx)(p.eb,{value:"all",children:"Tutti i sistemi"}),W.systems.map(e=>(0,s.jsx)(p.eb,{value:e,children:e},e))]})]})]})]}),(0,s.jsx)("div",{className:"mt-4 grid grid-cols-1 md:grid-cols-2 gap-4",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{className:"text-sm font-medium text-slate-700",children:"Range Metri Installati"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(h.p,{type:"number",placeholder:"Min",value:E,onChange:e=>I(e.target.value),className:"h-9 border-slate-300"}),(0,s.jsx)("span",{className:"text-slate-500",children:"-"}),(0,s.jsx)(h.p,{type:"number",placeholder:"Max",value:k,onChange:e=>J(e.target.value),className:"h-9 border-slate-300"})]})]})}),Y&&(0,s.jsxs)("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(z.A,{className:"w-4 h-4 text-blue-600"}),(0,s.jsx)("span",{className:"text-sm font-medium text-blue-800",children:"Filtri Attivi:"})]}),(0,s.jsxs)(n.$,{variant:"ghost",size:"sm",onClick:Q,className:"text-blue-600 hover:text-blue-800 hover:bg-blue-100",children:[(0,s.jsx)(C.A,{className:"w-4 h-4 mr-1"}),"Rimuovi Tutti"]})]}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2 mt-2",children:[d&&(0,s.jsxs)(m.E,{variant:"secondary",className:"bg-blue-100 text-blue-800",children:['Ricerca: "',d,'"']}),"all"!==j&&(0,s.jsxs)(m.E,{variant:"secondary",className:"bg-green-100 text-green-800",children:["Stato: ",j]}),"all"!==N&&(0,s.jsxs)(m.E,{variant:"secondary",className:"bg-blue-100 text-blue-800",children:["Collegamento: ",N]}),"all"!==y&&(0,s.jsxs)(m.E,{variant:"secondary",className:"bg-purple-100 text-purple-800",children:["Certificazione: ",y]}),"all"!==A&&(0,s.jsxs)(m.E,{variant:"secondary",className:"bg-indigo-100 text-indigo-800",children:["Sistema: ",A]}),(E||k)&&(0,s.jsxs)(m.E,{variant:"secondary",className:"bg-orange-100 text-orange-800",children:["Metri: ",E||"0"," - ",k||"∞"]})]})]})]}),d&&!g&&(0,s.jsx)("div",{className:"mt-3 p-2 bg-slate-50 rounded-lg border border-slate-200",children:(0,s.jsxs)("div",{className:"text-xs text-slate-600 flex items-center gap-2",children:[(0,s.jsx)("span",{children:"\uD83D\uDCA1"}),(0,s.jsx)("span",{children:"Suggerimenti: Usa virgole per ricerche multiple • Operatori: >100, <=50 per numeri"})]})})]})})}function V(e){let{text:a,maxLength:i=20,className:l=""}=e,[r,n]=(0,t.useState)(!1),[o,c]=(0,t.useState)({x:0,y:0});if(!a)return(0,s.jsx)("span",{className:"text-gray-400",children:"-"});let d=a.length>i,m=d?"".concat(a.substring(0,i),"..."):a;return d?(0,s.jsxs)("div",{className:"relative inline-block",children:[(0,s.jsx)("span",{className:"cursor-help ".concat(l),style:{textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden",maxWidth:"100%",display:"inline-block"},onMouseEnter:e=>{c({x:e.clientX,y:e.clientY}),n(!0)},onMouseMove:e=>{c({x:e.clientX,y:e.clientY})},onMouseLeave:()=>n(!1),title:a,children:m}),r&&(0,s.jsxs)("div",{className:"fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg pointer-events-none",style:{top:o.y-40,left:o.x-150,maxWidth:"300px",wordWrap:"break-word",whiteSpace:"normal"},children:[a,(0,s.jsx)("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0",style:{borderLeft:"5px solid transparent",borderRight:"5px solid transparent",borderTop:"5px solid #1f2937"}})]})]}):(0,s.jsx)("span",{className:l,children:a})}var G=i(81284),Z=i(38164),q=i(77855),H=i(91788),W=i(17580),Q=i(57434),Y=i(62525),X=i(54165),K=i(88539),ee=i(71847),ea=i(28883),ei=i(51154),es=i(1243),et=i(17649);let el=et.bL;et.l9;let er=et.ZL,en=t.forwardRef((e,a)=>{let{className:i,...t}=e;return(0,s.jsx)(et.hJ,{className:(0,g.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",i),...t,ref:a})});en.displayName=et.hJ.displayName;let eo=t.forwardRef((e,a)=>{let{className:i,...t}=e;return(0,s.jsxs)(er,{children:[(0,s.jsx)(en,{}),(0,s.jsx)(et.UC,{ref:a,className:(0,g.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",i),...t})]})});eo.displayName=et.UC.displayName;let ec=e=>{let{className:a,...i}=e;return(0,s.jsx)("div",{className:(0,g.cn)("flex flex-col space-y-2 text-center sm:text-left",a),...i})};ec.displayName="AlertDialogHeader";let ed=e=>{let{className:a,...i}=e;return(0,s.jsx)("div",{className:(0,g.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...i})};ed.displayName="AlertDialogFooter";let em=t.forwardRef((e,a)=>{let{className:i,...t}=e;return(0,s.jsx)(et.hE,{ref:a,className:(0,g.cn)("text-lg font-semibold",i),...t})});em.displayName=et.hE.displayName;let ex=t.forwardRef((e,a)=>{let{className:i,...t}=e;return(0,s.jsx)(et.VY,{ref:a,className:(0,g.cn)("text-sm text-muted-foreground",i),...t})});ex.displayName=et.VY.displayName;let eu=t.forwardRef((e,a)=>{let{className:i,...t}=e;return(0,s.jsx)(et.rc,{ref:a,className:(0,g.cn)((0,n.r)(),i),...t})});eu.displayName=et.rc.displayName;let eh=t.forwardRef((e,a)=>{let{className:i,...t}=e;return(0,s.jsx)(et.ZD,{ref:a,className:(0,g.cn)((0,n.r)({variant:"outline"}),"mt-2 sm:mt-0",i),...t})});eh.displayName=et.ZD.displayName;let ep={danger:{icon:i(54861).A,iconColor:"text-red-600",buttonClass:"bg-red-600 hover:bg-red-700 focus:ring-red-500",borderClass:"border-l-red-500"},warning:{icon:es.A,iconColor:"text-orange-600",buttonClass:"bg-orange-600 hover:bg-orange-700 focus:ring-orange-500",borderClass:"border-l-orange-500"},info:{icon:G.A,iconColor:"text-blue-600",buttonClass:"bg-blue-600 hover:bg-blue-700 focus:ring-blue-500",borderClass:"border-l-blue-500"},success:{icon:L.A,iconColor:"text-green-600",buttonClass:"bg-green-600 hover:bg-green-700 focus:ring-green-500",borderClass:"border-l-green-500"}};function ev(e){let{isOpen:a,onClose:i,onConfirm:l,title:r,description:n,confirmText:o="Conferma",cancelText:c="Annulla",variant:d="info",isLoading:m=!1,icon:x}=e,[u,h]=(0,t.useState)(!1),p=ep[d],v=x||p.icon;(0,t.useEffect)(()=>{let e=e=>{"Escape"!==e.key||!a||m||u||i()};return a&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[a,m,u,i]);let g=async()=>{try{h(!0);let e=l();e instanceof Promise&&await e,await new Promise(e=>setTimeout(e,300))}catch(e){console.error("Errore durante conferma:",e),h(!1);return}finally{h(!1)}},b=m||u;return(0,s.jsx)(el,{open:a,onOpenChange:i,children:(0,s.jsxs)(eo,{className:"border-l-4 ".concat(p.borderClass," max-w-md"),children:[(0,s.jsxs)(ec,{children:[(0,s.jsxs)(em,{className:"flex items-center space-x-3",children:[(0,s.jsx)(v,{className:"w-6 h-6 ".concat(p.iconColor," flex-shrink-0")}),(0,s.jsx)("span",{className:"text-lg font-semibold text-slate-900",children:r})]}),(0,s.jsx)(ex,{className:"text-base text-slate-600 leading-relaxed mt-2",children:n})]}),(0,s.jsxs)(ed,{className:"gap-3 mt-6",children:[(0,s.jsx)(eh,{onClick:i,disabled:b,className:"px-6 py-2 border-slate-300 text-slate-700 hover:bg-slate-50 focus:ring-slate-500",children:c}),(0,s.jsx)(eu,{onClick:g,disabled:b,className:"px-6 py-2 text-white font-medium transition-all duration-200 ".concat(p.buttonClass," disabled:opacity-50 disabled:cursor-not-allowed"),children:b?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(ei.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Elaborazione..."]}):o})]})]})})}function eg(e){let{isOpen:a,onClose:i,onConfirm:l,cavo:r}=e,[n,o]=(0,t.useState)(!1),c=async()=>{if(r){o(!0);try{await l(r.id_cavo),i()}catch(e){console.error("Errore durante scollegamento:",e)}finally{o(!1)}}};return r?(0,s.jsx)(ev,{isOpen:a,onClose:i,onConfirm:c,title:"Conferma Scollegamento Cavo",description:"Sei sicuro di voler scollegare il cavo ".concat(r.id_cavo,"? Questa azione potrebbe influenzare lo stato di altri componenti e dovr\xe0 essere ricollegato manualmente."),confirmText:"Scollega",cancelText:"Annulla",variant:"warning",isLoading:n,icon:(0,s.jsx)(ee.A,{className:"w-6 h-6"})}):null}function eb(e){let{isOpen:a,onClose:i,onGenerate:l,cavo:r}=e,[o,c]=(0,t.useState)(!1),[d,m]=(0,t.useState)({cavoId:"",fileName:"",format:"standard",includeTestData:!0,includePhotos:!1,emailRecipient:"",notes:""});(0,t.useState)(()=>{r&&m(e=>({...e,cavoId:r.id_cavo,fileName:"Certificato_".concat(r.id_cavo,"_").concat(new Date().toISOString().split("T")[0],".pdf")}))},[r]);let x=async()=>{if(r){c(!0);try{await l(d),i()}catch(e){console.error("Errore durante generazione PDF:",e)}finally{c(!1)}}};return r?(0,s.jsx)(X.lG,{open:a,onOpenChange:i,children:(0,s.jsxs)(X.Cf,{className:"max-w-2xl border-l-4 border-l-blue-500",children:[(0,s.jsxs)(X.c7,{className:"bg-gradient-to-r from-blue-50 to-transparent p-6 -m-6 mb-4",children:[(0,s.jsxs)(X.L3,{className:"flex items-center gap-3 text-xl",children:[(0,s.jsx)(Q.A,{className:"w-6 h-6 text-blue-600"}),"Genera Certificato per Cavo ",r.id_cavo]}),(0,s.jsx)(X.rr,{className:"text-base text-slate-600 mt-2",children:"Configura le opzioni per la generazione del certificato PDF"})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-slate-50 p-4 rounded-lg border",children:[(0,s.jsx)("h4",{className:"font-medium text-slate-900 mb-2",children:"Informazioni Cavo"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-slate-600",children:"ID:"}),(0,s.jsx)("span",{className:"ml-2 font-mono",children:r.id_cavo})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-slate-600",children:"Sistema:"}),(0,s.jsx)("span",{className:"ml-2",children:r.sistema||"N/A"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-slate-600",children:"Tipologia:"}),(0,s.jsx)("span",{className:"ml-2",children:r.tipologia||"N/A"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-slate-600",children:"Stato:"}),(0,s.jsx)("span",{className:"ml-2",children:r.stato||"N/A"})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"fileName",children:"Nome File *"}),(0,s.jsx)(h.p,{id:"fileName",value:d.fileName,onChange:e=>m(a=>({...a,fileName:e.target.value})),placeholder:"Certificato_C001_2024-01-01.pdf",className:"font-mono text-sm"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"format",children:"Formato Certificato"}),(0,s.jsxs)(p.l6,{value:d.format,onValueChange:e=>m(a=>({...a,format:e})),children:[(0,s.jsx)(p.bq,{children:(0,s.jsx)(p.yv,{})}),(0,s.jsxs)(p.gC,{children:[(0,s.jsx)(p.eb,{value:"standard",children:"\uD83D\uDCC4 Standard CEI 64-8"}),(0,s.jsx)(p.eb,{value:"detailed",children:"\uD83D\uDCCB Dettagliato con Misure"}),(0,s.jsx)(p.eb,{value:"summary",children:"\uD83D\uDCDD Riassunto Esecutivo"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"emailRecipient",children:"Email Destinatario (Opzionale)"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(ea.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400"}),(0,s.jsx)(h.p,{id:"emailRecipient",type:"email",value:d.emailRecipient,onChange:e=>m(a=>({...a,emailRecipient:e.target.value})),placeholder:"<EMAIL>",className:"pl-10"})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(T.J,{children:"Opzioni Aggiuntive"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,s.jsx)("input",{type:"checkbox",checked:d.includeTestData,onChange:e=>m(a=>({...a,includeTestData:e.target.checked})),className:"rounded border-slate-300 text-blue-600 focus:ring-blue-500"}),(0,s.jsx)("span",{className:"text-sm text-slate-700",children:"Includi Dati di Collaudo"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,s.jsx)("input",{type:"checkbox",checked:d.includePhotos,onChange:e=>m(a=>({...a,includePhotos:e.target.checked})),className:"rounded border-slate-300 text-blue-600 focus:ring-blue-500"}),(0,s.jsx)("span",{className:"text-sm text-slate-700",children:"Includi Foto Installazione"})]})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"notes",children:"Note Aggiuntive"}),(0,s.jsx)(K.T,{id:"notes",value:d.notes,onChange:e=>m(a=>({...a,notes:e.target.value})),placeholder:"Note o commenti da includere nel certificato...",rows:3,className:"resize-none"})]})]}),(0,s.jsxs)(X.Es,{className:"gap-3 pt-6 border-t",children:[(0,s.jsxs)(n.$,{variant:"outline",onClick:i,disabled:o,className:"px-6",children:[(0,s.jsx)(C.A,{className:"w-4 h-4 mr-2"}),"Annulla"]}),(0,s.jsx)(n.$,{onClick:x,disabled:o||!d.fileName.trim(),className:"px-6 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700",children:o?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(ei.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Generazione..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(H.A,{className:"w-4 h-4 mr-2"}),"Genera PDF"]})})]})]})}):null}function ej(e){let{isOpen:a,onClose:i,cavo:t,missingRequirements:l}=e;return t?(0,s.jsx)(X.lG,{open:a,onOpenChange:i,children:(0,s.jsxs)(X.Cf,{className:"max-w-md border-l-4 border-l-red-500",children:[(0,s.jsxs)(X.c7,{className:"bg-gradient-to-r from-red-50 to-transparent p-6 -m-6 mb-4",children:[(0,s.jsxs)(X.L3,{className:"flex items-center gap-3 text-xl",children:[(0,s.jsx)(es.A,{className:"w-6 h-6 text-red-600"}),"Impossibile Certificare Cavo"]}),(0,s.jsxs)(X.rr,{className:"text-base text-slate-600 mt-2",children:["Il cavo ",t.id_cavo," non pu\xf2 essere certificato nel suo stato attuale"]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)(o.Fc,{variant:"destructive",className:"border-red-200 bg-red-50",children:[(0,s.jsx)(es.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{className:"font-medium",children:"Requisiti mancanti per la certificazione:"})]}),(0,s.jsx)("ul",{className:"space-y-2",children:l.map((e,a)=>(0,s.jsxs)("li",{className:"flex items-center gap-2 text-sm text-slate-700",children:[(0,s.jsx)(C.A,{className:"w-4 h-4 text-red-500 flex-shrink-0"}),e]},a))}),(0,s.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-200",children:[(0,s.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"\uD83D\uDCA1 Suggerimento"}),(0,s.jsx)("p",{className:"text-sm text-blue-800",children:"Completa tutti i requisiti sopra elencati prima di procedere con la certificazione. Puoi utilizzare le azioni disponibili nella tabella per aggiornare lo stato del cavo."})]})]}),(0,s.jsx)(X.Es,{className:"pt-6 border-t",children:(0,s.jsxs)(n.$,{onClick:i,className:"px-6 bg-slate-600 hover:bg-slate-700",children:[(0,s.jsx)($.A,{className:"w-4 h-4 mr-2"}),"Ho Capito"]})})]})}):null}var ef=i(46510);function eN(e){let{cavi:a=[],loading:i=!1,selectionEnabled:l=!1,selectedCavi:r=[],onSelectionChange:o,onStatusAction:c,onContextMenuAction:d,onDisconnectCable:h,onGeneratePDF:p,onCertifyCable:v}=e,[g,b]=(0,t.useState)(a),[j,f]=(0,t.useState)(a),[z,k]=(0,t.useState)(l),[T,F]=(0,t.useState)("id_cavo"),[O,R]=(0,t.useState)("asc"),[P,B]=(0,t.useState)({disconnect:{isOpen:!1,cavo:null},generatePDF:{isOpen:!1,cavo:null},certificationError:{isOpen:!1,cavo:null,missingRequirements:[]}}),[X,K]=(0,t.useState)(1),[ee,ea]=(0,t.useState)(25),[ei,es]=(0,t.useState)(!1),et=(0,ef.E)(),el=function(e,a){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];B(s=>({...s,[e]:{isOpen:!0,cavo:a,missingRequirements:i}}))},er=e=>{B(a=>({...a,[e]:{isOpen:!1,cavo:null,missingRequirements:[]}}))},en=e=>{T===e?R(e=>"asc"===e?"desc":"asc"):(F(e),R("asc"))},eo=(0,t.useMemo)(()=>[...g].sort((e,a)=>{let i=e[T],s=a[T];return(("metri_teorici"===T||"metri_posati"===T)&&(i=parseFloat(i)||0,s=parseFloat(s)||0),"string"==typeof i&&(i=i.toLowerCase()),"string"==typeof s&&(s=s.toLowerCase()),i<s)?"asc"===O?-1:1:i>s?"asc"===O?1:-1:0}),[g,T,O]);(0,t.useEffect)(()=>{b(a)},[a]);let ec=(0,t.useMemo)(()=>{let e=(X-1)*ee,a=e+ee,i=eo.slice(e,a),s=Math.ceil(eo.length/ee),t=X<s,l=X>1;return{cavi:i,totalItems:eo.length,totalPages:s,hasNextPage:t,hasPrevPage:l,startIndex:e+1,endIndex:Math.min(a,eo.length)}},[eo,X,ee]);(0,t.useEffect)(()=>{f(ec.cavi)},[ec.cavi]),(0,t.useEffect)(()=>{K(1)},[g.length]);let ed=async e=>{try{h&&(await h(e),et.cavoDisconnected(e))}catch(e){et.error("Errore Scollegamento","Impossibile scollegare il cavo. Riprova.")}},em=async e=>{try{p&&(await p(e),et.pdfGenerated(e.fileName,e.cavoId))}catch(e){et.error("Errore Generazione PDF","Impossibile generare il certificato. Riprova.")}},ex=async e=>{let a=[];if(e.metri_posati&&0!==parseFloat(e.metri_posati)||a.push("Metri posati non inseriti"),"Collegato"!==e.stato&&a.push("Cavo non collegato"),e.data_installazione||a.push("Data installazione mancante"),a.length>0)return void el("certificationError",e,a);try{v&&(et.actionInProgress("Certificazione",e.id_cavo),await v(e.id_cavo),et.success("Cavo Certificato","Il cavo ".concat(e.id_cavo," \xe8 stato certificato con successo.")))}catch(a){et.certificationError(e.id_cavo,"Errore durante il processo di certificazione")}},eu=e=>{K(Math.max(1,Math.min(e,ec.totalPages)))},eh=()=>{null==o||o([])},ep=ec.cavi.length>0&&ec.cavi.every(e=>r.includes(e.id_cavo)),ev=ec.cavi.some(e=>r.includes(e.id_cavo)),eN=e=>{let{field:a,children:i,className:t=""}=e;return(0,s.jsx)(U.Bc,{children:(0,s.jsxs)(U.m_,{children:[(0,s.jsx)(U.k$,{asChild:!0,children:(0,s.jsxs)("button",{onClick:()=>en(a),className:"flex items-center gap-2 font-medium text-left hover:text-blue-600 transition-colors ".concat(t),children:[i,T===a?"asc"===O?(0,s.jsx)(_.A,{className:"w-4 h-4 text-blue-600"}):(0,s.jsx)(y.A,{className:"w-4 h-4 text-blue-600"}):(0,s.jsx)(N.A,{className:"w-4 h-4 text-slate-400"})]})}),(0,s.jsx)(U.ZI,{children:(0,s.jsxs)("p",{children:["Clicca per ordinare per ",i]})})]})})},e_=e=>{o&&o(e?j.map(e=>e.id_cavo):[])},ey=(e,a)=>{o&&o(a?[...r,e]:r.filter(a=>a!==e))},eC=(0,t.useMemo)(()=>{let e=[{field:"id_cavo",headerName:"ID",dataType:"text",width:70,align:"left",renderHeader:()=>(0,s.jsx)(eN,{field:"id_cavo",children:"ID"}),renderCell:e=>(0,s.jsx)("span",{className:"font-semibold text-mariner-900",children:e.id_cavo})},{field:"sistema",headerName:"Sistema",dataType:"text",width:80,renderHeader:()=>(0,s.jsx)(eN,{field:"sistema",children:"Sistema"}),renderCell:e=>(0,s.jsx)(V,{text:e.sistema||"",maxLength:8})},{field:"utility",headerName:"Utility",dataType:"text",width:80,renderCell:e=>(0,s.jsx)(V,{text:e.utility||"",maxLength:8})},{field:"tipologia",headerName:"Tipologia",dataType:"text",width:100,renderCell:e=>(0,s.jsx)(V,{text:e.tipologia||"",maxLength:12})},{field:"formazione",headerName:"Form.",dataType:"text",align:"left",width:60,renderCell:e=>e.formazione||e.sezione},{field:"metri_teorici",headerName:"M.Teor.",dataType:"number",align:"left",width:70,renderHeader:()=>(0,s.jsx)(eN,{field:"metri_teorici",children:"M.Teor."}),renderCell:e=>e.metri_teorici?e.metri_teorici.toFixed(1):"0"},{field:"metri_posati",headerName:"M.Reali",dataType:"number",align:"left",width:70,renderHeader:()=>(0,s.jsx)(eN,{field:"metri_posati",children:"M.Reali"}),renderCell:e=>{let a=e.metri_posati||e.metratura_reale||0;return a?a.toFixed(1):"0"}},{field:"ubicazione_partenza",headerName:"Da",dataType:"text",width:140,renderCell:e=>(0,s.jsx)(V,{text:e.da||e.ubicazione_partenza||"",maxLength:18})},{field:"ubicazione_arrivo",headerName:"A",dataType:"text",width:140,renderCell:e=>(0,s.jsx)(V,{text:e.a||e.ubicazione_arrivo||"",maxLength:18})},{field:"id_bobina",headerName:"Bobina",dataType:"text",width:100,align:"center",renderCell:e=>ez(e)},{field:"stato_installazione",headerName:"Stato",dataType:"text",align:"left",width:130,disableFilter:!0,renderHeader:()=>(0,s.jsx)(eN,{field:"stato",children:"Stato"}),renderCell:e=>ew(e)},{field:"collegamenti",headerName:"Collegamenti",dataType:"text",align:"left",width:160,disableFilter:!0,disableSort:!0,renderCell:e=>eA(e)},{field:"certificato",headerName:"Certificato",dataType:"text",align:"left",width:170,disableFilter:!0,disableSort:!0,renderCell:e=>eS(e)}];return z&&e.unshift({field:"selection",headerName:"",disableFilter:!0,disableSort:!0,width:50,align:"left",renderHeader:()=>(0,s.jsx)(x.S,{checked:r.length===j.length&&j.length>0,onCheckedChange:e_}),renderCell:e=>(0,s.jsx)(x.S,{checked:r.includes(e.id_cavo),onCheckedChange:a=>ey(e.id_cavo,a),onClick:e=>e.stopPropagation()})}),e},[z,r,j,e_,ey]),ez=e=>{let a=e.id_bobina;if(!a||"N/A"===a)return(0,s.jsx)("span",{className:"text-gray-400",children:"-"});if("BOBINA_VUOTA"===a)return(0,s.jsx)(m.E,{variant:"outline",className:"text-xs px-2 py-0.5 text-gray-500 border-gray-300 bg-gray-50",children:"Vuota"});let i=a,t=a.match(/_B(.+)$/);return t||(t=a.match(/_b(.+)$/))||(t=a.match(/c\d+_[bB](\d+)$/))?i=t[1]:(t=a.match(/(\d+)$/))&&(i=t[1]),(0,s.jsx)(U.Bc,{children:(0,s.jsxs)(U.m_,{children:[(0,s.jsx)(U.k$,{asChild:!0,children:(0,s.jsxs)(m.E,{variant:"outline",className:"cursor-pointer hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 hover:shadow-sm transition-all duration-200 px-2 py-1 font-medium text-slate-700 border-slate-300 bg-white flex items-center gap-1",onClick:a=>{a.stopPropagation(),(e.metri_posati||e.metratura_reale||0)>0?null==c||c(e,"modify_reel"):null==c||c(e,"insert_meters")},children:[(0,s.jsx)("span",{children:i}),(0,s.jsx)(D.A,{className:"h-3 w-3 opacity-60"})]})}),(0,s.jsx)(U.ZI,{children:(0,s.jsx)("p",{children:(e.metri_posati||e.metratura_reale||0)>0?"Clicca per modificare bobina":"Clicca per inserire metri posati"})})]})})},ew=e=>{let a=e.metri_posati||e.metratura_reale||0,i=e.comanda_posa,t=e.comanda_partenza,l=e.comanda_arrivo,r=e.comanda_certificazione,n=i||t||l||r;if(n&&"In corso"===e.stato_installazione)return(0,s.jsx)(U.Bc,{children:(0,s.jsxs)(U.m_,{children:[(0,s.jsx)(U.k$,{asChild:!0,children:(0,s.jsx)(m.E,{className:"bg-blue-500 text-white cursor-pointer hover:bg-blue-600 transition-colors duration-200 px-3 py-1 font-medium",onClick:a=>{a.stopPropagation(),null==c||c(e,"view_command",n)},children:n})}),(0,s.jsx)(U.ZI,{children:(0,s.jsx)("p",{children:"Visualizza dettagli comanda"})})]})});let o=e.stato_installazione||"Da installare";return"Installato"===o||a>0?(0,s.jsx)(m.E,{className:"bg-green-100 text-green-700 px-3 py-1 font-medium border border-green-200",children:"Installato"}):"In corso"===o?(0,s.jsx)(m.E,{className:"bg-yellow-100 text-yellow-700 px-3 py-1 font-medium border border-yellow-200",children:"In corso"}):(0,s.jsx)(m.E,{variant:"outline",className:"text-gray-600 px-3 py-1 font-medium border-gray-300 bg-gray-50",children:"Da installare"})},eA=e=>{let a,i,t,l,r=e.metri_posati||e.metratura_reale||0,o=e.stato_installazione||"Da installare",d=e.collegamento||e.collegamenti||0;if(!(r>0||"Installato"===o||"In corso"===o))return(0,s.jsx)(U.Bc,{children:(0,s.jsxs)(U.m_,{children:[(0,s.jsx)(U.k$,{asChild:!0,children:(0,s.jsxs)("span",{className:"text-gray-400 text-sm px-2 py-1 flex items-center gap-1",children:[(0,s.jsx)(G.A,{className:"h-3 w-3"}),"Non disponibile"]})}),(0,s.jsx)(U.ZI,{children:(0,s.jsx)("p",{children:"Il collegamento non pu\xf2 essere gestito perch\xe9 il cavo non \xe8 installato"})})]})});switch(d){case 0:a="Collega",i=Z.A,t="connect_cable",l="border-blue-300 text-blue-700 hover:bg-blue-50 hover:border-blue-400";break;case 1:a="Completa Arrivo",i=Z.A,t="connect_arrival",l="border-yellow-300 text-yellow-700 hover:bg-yellow-50 hover:border-yellow-400";break;case 2:a="Completa Partenza",i=Z.A,t="connect_departure",l="border-yellow-300 text-yellow-700 hover:bg-yellow-50 hover:border-yellow-400";break;case 3:a="Scollega",i=q.A,t="disconnect_cable",l="border-red-300 text-red-700 hover:bg-red-50 hover:border-red-400";break;default:a="Gestisci",i=$.A,t="manage_connections",l="border-blue-300 text-blue-700 hover:bg-blue-50 hover:border-blue-400"}let m=i;return(0,s.jsxs)(n.$,{variant:"outline",size:"sm",className:"h-7 px-2 text-xs font-medium transition-colors duration-200 ".concat(l),onClick:a=>{a.stopPropagation(),"disconnect_cable"===t?el("disconnect",e):null==c||c(e,t)},children:[(0,s.jsx)(m,{className:"h-3 w-3 mr-1"}),a]})},eS=e=>{let a=e.metri_posati||e.metratura_reale||0,i=e.stato_installazione||"Da installare",t=!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato,l=!1===e.certificato||"NO"===e.certificato||"RIFIUTATO"===e.certificato;return a>0||"Installato"===i||"In corso"===i?t?(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(m.E,{className:"bg-green-100 text-green-700 px-2 py-1 text-xs font-medium border border-green-200",children:[(0,s.jsx)(L.A,{className:"h-3 w-3 mr-1"}),"Certificato"]}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",className:"h-6 w-6 p-0 border-blue-300 text-blue-700 hover:bg-blue-50 hover:border-blue-400",onClick:a=>{a.stopPropagation(),el("generatePDF",e)},children:(0,s.jsx)(H.A,{className:"h-3 w-3"})})]}):l?(0,s.jsxs)(m.E,{className:"bg-red-100 text-red-700 px-2 py-1 text-xs font-medium border border-red-200",children:[(0,s.jsx)(C.A,{className:"h-3 w-3 mr-1"}),"Non Certificato"]}):(0,s.jsxs)(n.$,{variant:"outline",size:"sm",className:"h-7 px-2 text-xs font-medium border-purple-300 text-purple-700 hover:bg-purple-50 hover:border-purple-400 transition-colors duration-200",onClick:a=>{a.stopPropagation(),ex(e)},children:[(0,s.jsx)(L.A,{className:"h-3 w-3 mr-1"}),"Certifica"]}):(0,s.jsx)(U.Bc,{children:(0,s.jsxs)(U.m_,{children:[(0,s.jsx)(U.k$,{asChild:!0,children:(0,s.jsxs)("span",{className:"text-gray-400 text-sm px-2 py-1 flex items-center gap-1",children:[(0,s.jsx)(G.A,{className:"h-3 w-3"}),"Non disponibile"]})}),(0,s.jsx)(U.ZI,{children:(0,s.jsx)("p",{children:"La certificazione non pu\xf2 essere gestita perch\xe9 il cavo non \xe8 installato"})})]})})};return(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(J,{cavi:a,onFilteredDataChange:e=>{b(e)},loading:i,selectionEnabled:z,onSelectionToggle:()=>{k(!z),es(!z&&r.length>0)}}),(0,s.jsxs)("div",{className:"mb-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 bg-slate-50 p-4 rounded-lg border border-slate-200",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{className:"text-sm text-slate-600",children:["Mostrando ",(0,s.jsx)("span",{className:"font-semibold",children:ec.startIndex})," - ",(0,s.jsx)("span",{className:"font-semibold",children:ec.endIndex})," di ",(0,s.jsx)("span",{className:"font-semibold",children:ec.totalItems})," cavi"]}),z&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(U.Bc,{children:(0,s.jsxs)(U.m_,{children:[(0,s.jsx)(U.k$,{asChild:!0,children:(0,s.jsxs)("div",{onClick:ep?()=>{let e=new Set(ec.cavi.map(e=>e.id_cavo)),a=r.filter(a=>!e.has(a));null==o||o(a)}:()=>{let e=[...new Set([...r,...ec.cavi.map(e=>e.id_cavo)])];null==o||o(e)},className:"flex items-center gap-2 px-3 py-2 text-sm font-medium border border-slate-200 rounded-md hover:bg-slate-50 hover:border-slate-300 transition-all duration-200 cursor-pointer",children:[(0,s.jsx)(x.S,{checked:ep,ref:e=>{e&&(e.indeterminate=ev&&!ep)}}),"Pagina"]})}),(0,s.jsx)(U.ZI,{children:(0,s.jsx)("p",{children:ep?"Deseleziona tutti i cavi visibili":"Seleziona tutti i cavi visibili"})})]})}),(0,s.jsx)(U.Bc,{children:(0,s.jsxs)(U.m_,{children:[(0,s.jsx)(U.k$,{asChild:!0,children:(0,s.jsxs)(n.$,{variant:"outline",size:"sm",onClick:r.length===eo.length?eh:()=>{let e=eo.map(e=>e.id_cavo);null==o||o(e)},className:"flex items-center gap-2",children:[(0,s.jsx)(W.A,{className:"w-4 h-4"}),"Tutti (",eo.length,")"]})}),(0,s.jsx)(U.ZI,{children:(0,s.jsx)("p",{children:r.length===eo.length?"Deseleziona tutti i cavi":"Seleziona tutti i cavi filtrati"})})]})}),r.length>0&&(0,s.jsxs)(m.E,{variant:"secondary",className:"bg-blue-100 text-blue-800",children:[r.length," selezionati"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-sm text-slate-600",children:"Righe per pagina:"}),(0,s.jsxs)("select",{value:ee,onChange:e=>{ea(Number(e.target.value)),K(1)},className:"border border-slate-300 rounded px-2 py-1 text-sm",children:[(0,s.jsx)("option",{value:10,children:"10"}),(0,s.jsx)("option",{value:25,children:"25"}),(0,s.jsx)("option",{value:50,children:"50"}),(0,s.jsx)("option",{value:100,children:"100"})]})]})]}),(0,s.jsx)(I,{data:g,columns:eC,loading:i,emptyMessage:"Nessun cavo disponibile",onFilteredDataChange:e=>{f(e)},renderRow:(e,a)=>{let i=r.includes(e.id_cavo);return(0,s.jsx)(u.Hj,{className:"\n          ".concat(i?"bg-blue-50 border-blue-200":"bg-white","\n          hover:bg-blue-50 hover:border-blue-200 hover:shadow-sm\n          cursor-pointer border-b border-gray-200\n          transition-all duration-200 ease-in-out\n          ").concat(i?"ring-1 ring-blue-300":"","\n        "),onClick:()=>z&&ey(e.id_cavo,!i),onContextMenu:a=>{a.preventDefault(),null==d||d(e,"context_menu")},children:eC.map(a=>(0,s.jsx)(u.nA,{className:"\n              py-2 px-2 text-sm text-left\n              ".concat(i?"text-blue-900":"text-gray-900","\n              transition-colors duration-200\n            "),style:{width:a.width,...a.cellStyle},onClick:e=>{["stato_installazione","collegamenti","certificato"].includes(a.field)&&e.stopPropagation()},children:a.renderCell?a.renderCell(e):e[a.field]||(0,s.jsx)("span",{className:"text-gray-400",children:"-"})},a.field))},e.id_cavo)}}),ec.totalPages>1&&(0,s.jsxs)("div",{className:"mt-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 bg-white p-4 rounded-lg border border-slate-200",children:[(0,s.jsxs)("div",{className:"text-sm text-slate-600",children:["Pagina ",(0,s.jsx)("span",{className:"font-semibold",children:X})," di ",(0,s.jsx)("span",{className:"font-semibold",children:ec.totalPages})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(U.Bc,{children:(0,s.jsxs)(U.m_,{children:[(0,s.jsx)(U.k$,{asChild:!0,children:(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>eu(1),disabled:!ec.hasPrevPage,className:"p-2",children:(0,s.jsx)(w.A,{className:"w-4 h-4"})})}),(0,s.jsx)(U.ZI,{children:(0,s.jsx)("p",{children:"Prima pagina"})})]})}),(0,s.jsx)(U.Bc,{children:(0,s.jsxs)(U.m_,{children:[(0,s.jsx)(U.k$,{asChild:!0,children:(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>eu(X-1),disabled:!ec.hasPrevPage,className:"p-2",children:(0,s.jsx)(A.A,{className:"w-4 h-4"})})}),(0,s.jsx)(U.ZI,{children:(0,s.jsx)("p",{children:"Pagina precedente"})})]})}),(0,s.jsx)("div",{className:"flex items-center gap-1",children:Array.from({length:Math.min(5,ec.totalPages)},(e,a)=>{let i;return i=ec.totalPages<=5||X<=3?a+1:X>=ec.totalPages-2?ec.totalPages-4+a:X-2+a,(0,s.jsx)(n.$,{variant:X===i?"default":"outline",size:"sm",onClick:()=>eu(i),className:"w-8 h-8 p-0",children:i},i)})}),(0,s.jsx)(U.Bc,{children:(0,s.jsxs)(U.m_,{children:[(0,s.jsx)(U.k$,{asChild:!0,children:(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>eu(X+1),disabled:!ec.hasNextPage,className:"p-2",children:(0,s.jsx)(S.A,{className:"w-4 h-4"})})}),(0,s.jsx)(U.ZI,{children:(0,s.jsx)("p",{children:"Pagina successiva"})})]})}),(0,s.jsx)(U.Bc,{children:(0,s.jsxs)(U.m_,{children:[(0,s.jsx)(U.k$,{asChild:!0,children:(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>eu(ec.totalPages),disabled:!ec.hasNextPage,className:"p-2",children:(0,s.jsx)(E.A,{className:"w-4 h-4"})})}),(0,s.jsx)(U.ZI,{children:(0,s.jsx)("p",{children:"Ultima pagina"})})]})})]})]}),z&&r.length>0&&(0,s.jsx)("div",{className:"fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-white border border-slate-300 rounded-lg shadow-xl z-50 p-4 min-w-[600px]",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full animate-pulse"}),(0,s.jsxs)(m.E,{variant:"secondary",className:"bg-blue-100 text-blue-800 font-semibold",children:[r.length," cavi selezionati"]})]}),(0,s.jsx)(U.Bc,{children:(0,s.jsxs)(U.m_,{children:[(0,s.jsx)(U.k$,{asChild:!0,children:(0,s.jsxs)(n.$,{variant:"ghost",size:"sm",onClick:eh,className:"text-slate-600 hover:text-slate-800",children:[(0,s.jsx)(C.A,{className:"w-4 h-4 mr-1"}),"Deseleziona tutto"]})}),(0,s.jsx)(U.ZI,{children:(0,s.jsx)("p",{children:"Rimuovi la selezione da tutti i cavi"})})]})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(U.Bc,{children:(0,s.jsxs)(U.m_,{children:[(0,s.jsx)(U.k$,{asChild:!0,children:(0,s.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>{et.actionInProgress("Esportazione","".concat(r.length," cavi"))},className:"flex items-center gap-2 hover:bg-green-50 hover:border-green-300",children:[(0,s.jsx)(H.A,{className:"w-4 h-4"}),"Esporta"]})}),(0,s.jsx)(U.ZI,{children:(0,s.jsx)("p",{children:"Esporta i cavi selezionati in Excel"})})]})}),(0,s.jsx)(U.Bc,{children:(0,s.jsxs)(U.m_,{children:[(0,s.jsx)(U.k$,{asChild:!0,children:(0,s.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>{et.actionInProgress("Generazione PDF","".concat(r.length," cavi"))},className:"flex items-center gap-2 hover:bg-blue-50 hover:border-blue-300",children:[(0,s.jsx)(Q.A,{className:"w-4 h-4"}),"PDF Bulk"]})}),(0,s.jsx)(U.ZI,{children:(0,s.jsx)("p",{children:"Genera PDF per tutti i cavi selezionati"})})]})}),(0,s.jsx)(U.Bc,{children:(0,s.jsxs)(U.m_,{children:[(0,s.jsx)(U.k$,{asChild:!0,children:(0,s.jsxs)(n.$,{size:"sm",onClick:()=>{et.actionInProgress("Aggiornamento Stato","".concat(r.length," cavi"))},className:"flex items-center gap-2 hover:bg-yellow-50 hover:border-yellow-300",children:[(0,s.jsx)($.A,{className:"w-4 h-4"}),"Stato"]})}),(0,s.jsx)(U.ZI,{children:(0,s.jsx)("p",{children:"Cambia stato per tutti i cavi selezionati"})})]})}),(0,s.jsx)(U.Bc,{children:(0,s.jsxs)(U.m_,{children:[(0,s.jsx)(U.k$,{asChild:!0,children:(0,s.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>{et.actionInProgress("Assegnazione Comanda","".concat(r.length," cavi"))},className:"flex items-center gap-2 hover:bg-purple-50 hover:border-purple-300",children:[(0,s.jsx)(M.A,{className:"w-4 h-4"}),"Comanda"]})}),(0,s.jsx)(U.ZI,{children:(0,s.jsx)("p",{children:"Assegna comanda a tutti i cavi selezionati"})})]})}),(0,s.jsx)("div",{className:"w-px h-6 bg-slate-300"}),(0,s.jsx)(U.Bc,{children:(0,s.jsxs)(U.m_,{children:[(0,s.jsx)(U.k$,{asChild:!0,children:(0,s.jsxs)(n.$,{variant:"destructive",size:"sm",onClick:()=>{confirm("Sei sicuro di voler eliminare ".concat(r.length," cavi selezionati?"))&&et.actionInProgress("Eliminazione","".concat(r.length," cavi"))},className:"flex items-center gap-2",children:[(0,s.jsx)(Y.A,{className:"w-4 h-4"}),"Elimina"]})}),(0,s.jsx)(U.ZI,{children:(0,s.jsx)("p",{children:"Elimina tutti i cavi selezionati (azione irreversibile)"})})]})})]})]})}),(0,s.jsx)(eg,{isOpen:P.disconnect.isOpen,onClose:()=>er("disconnect"),onConfirm:ed,cavo:P.disconnect.cavo}),(0,s.jsx)(eb,{isOpen:P.generatePDF.isOpen,onClose:()=>er("generatePDF"),onGenerate:em,cavo:P.generatePDF.cavo}),(0,s.jsx)(ej,{isOpen:P.certificationError.isOpen,onClose:()=>er("certificationError"),cavo:P.certificationError.cavo,missingRequirements:P.certificationError.missingRequirements})]})}var e_=i(72713),ey=i(3493),eC=i(14186);function ez(e){let{cavi:a,filteredCavi:i,className:l,revisioneCorrente:n}=e,o=(0,t.useMemo)(()=>{let e=a.length,s=i.length,t=i.filter(e=>"Installato"===e.stato_installazione||e.metri_posati&&e.metri_posati>0||e.metratura_reale&&e.metratura_reale>0).length,l=i.filter(e=>"In corso"===e.stato_installazione).length,r=i.filter(e=>3===(e.collegamento||e.collegamenti||0)).length,n=i.filter(e=>{let a=e.collegamento||e.collegamenti||0;return 1===a||2===a}).length,o=i.filter(e=>0===(e.collegamento||e.collegamenti||0)&&(e.metri_posati>0||e.metratura_reale>0)).length,c=i.filter(e=>!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato).length,d=i.reduce((e,a)=>e+(a.metri_teorici||0),0),m=i.reduce((e,a)=>e+(a.metri_posati||a.metratura_reale||0),0),x=0===s?0:Math.round(100*(((t-r)*2+(r-c)*3.5+4*c)/(4*s)*100))/100;return{totalCavi:e,filteredCount:s,installati:t,inCorso:l,daInstallare:s-t-l,collegati:r,parzialmenteCollegati:n,nonCollegati:o,certificati:c,metriTotali:d,metriInstallati:m,percentualeInstallazione:x}},[a,i]);return(0,s.jsx)(r.Zp,{className:l,children:(0,s.jsxs)(r.Wu,{className:"p-1.5",children:[(0,s.jsx)("div",{className:"flex items-center justify-between mb-1",children:(0,s.jsxs)("div",{className:"flex items-center space-x-1.5",children:[(0,s.jsx)(e_.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,s.jsx)("span",{className:"text-xs font-semibold text-mariner-900",children:"Statistiche Cavi"}),n&&(0,s.jsxs)(m.E,{variant:"outline",className:"text-xs bg-blue-50 text-blue-700 border-blue-200",children:["Rev. ",n]})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-1.5 bg-mariner-50 px-1.5 py-1 rounded-lg",children:[(0,s.jsx)(ey.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-bold text-mariner-900 text-sm",children:o.filteredCount}),(0,s.jsxs)("div",{className:"text-xs text-mariner-600",children:["di ",o.totalCavi," cavi"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-1.5 bg-green-50 px-1.5 py-1 rounded-lg",children:[(0,s.jsx)(L.A,{className:"h-3.5 w-3.5 text-green-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-bold text-green-700 text-sm",children:o.installati}),(0,s.jsx)("div",{className:"text-xs text-green-600",children:"installati"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-1.5 bg-yellow-50 px-1.5 py-1 rounded-lg",children:[(0,s.jsx)(eC.A,{className:"h-3.5 w-3.5 text-yellow-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-bold text-yellow-700 text-sm",children:o.inCorso}),(0,s.jsx)("div",{className:"text-xs text-yellow-600",children:"in corso"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-1.5 bg-gray-50 px-1.5 py-1 rounded-lg",children:[(0,s.jsx)(es.A,{className:"h-3.5 w-3.5 text-gray-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-bold text-gray-700 text-sm",children:o.daInstallare}),(0,s.jsx)("div",{className:"text-xs text-gray-600",children:"da installare"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-1.5 bg-blue-50 px-1.5 py-1 rounded-lg",children:[(0,s.jsx)(B.A,{className:"h-3.5 w-3.5 text-blue-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-bold text-blue-700 text-sm",children:o.collegati}),(0,s.jsx)("div",{className:"text-xs text-blue-600",children:"collegati"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-1.5 bg-purple-50 px-1.5 py-1 rounded-lg",children:[(0,s.jsx)(M.A,{className:"h-3.5 w-3.5 text-purple-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-bold text-purple-700 text-sm",children:o.certificati}),(0,s.jsx)("div",{className:"text-xs text-purple-600",children:"certificati"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-1.5 bg-indigo-50 px-1.5 py-1 rounded-lg",children:[(0,s.jsx)("div",{className:"h-3.5 w-3.5 flex items-center justify-center",children:(0,s.jsx)("div",{className:"h-2 w-2 bg-indigo-600 rounded-full"})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"font-bold text-indigo-700 text-sm",children:[o.metriInstallati.toLocaleString(),"m"]}),(0,s.jsxs)("div",{className:"text-xs text-indigo-600",children:["di ",o.metriTotali.toLocaleString(),"m"]})]})]})]}),o.filteredCount>0&&(0,s.jsxs)("div",{className:"mt-2 bg-gray-50 p-2 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex justify-between text-xs font-medium text-gray-700 mb-1",children:[(0,s.jsx)("span",{children:"Avanzamento Complessivo Cavi"}),(0,s.jsxs)("span",{className:"font-bold ".concat(o.percentualeInstallazione>=80?"text-amber-700":o.percentualeInstallazione>=60?"text-orange-700":o.percentualeInstallazione>=40?"text-yellow-700":"text-emerald-700"),children:[o.percentualeInstallazione.toFixed(1),"%"]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:"h-2 rounded-full transition-all duration-500 ease-in-out ".concat(o.percentualeInstallazione>=80?"bg-gradient-to-r from-amber-500 to-amber-600":o.percentualeInstallazione>=60?"bg-gradient-to-r from-orange-500 to-orange-600":o.percentualeInstallazione>=40?"bg-gradient-to-r from-yellow-500 to-yellow-600":"bg-gradient-to-r from-emerald-500 to-emerald-600"),style:{width:"".concat(Math.min(o.percentualeInstallazione,100),"%")}})}),(0,s.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-0.5",children:[(0,s.jsx)("span",{children:"Metri installati vs totali disponibili"}),(0,s.jsxs)("span",{children:[(o.metriTotali-o.metriInstallati).toLocaleString(),"m rimanenti"]})]})]})]})})}var ew=i(6740),eA=i(85339);function eS(e){let{open:a,onClose:i,cavo:l,cantiere:r,onSuccess:m,onError:x}=e,{cantiere:u}=(0,c.A)(),p=r||u,[v,g]=(0,t.useState)("assegna_nuova"),[b,j]=(0,t.useState)(""),[f,N]=(0,t.useState)([]),[_,y]=(0,t.useState)(!1),[C,z]=(0,t.useState)(!1),[w,A]=(0,t.useState)(""),[S,E]=(0,t.useState)(""),[I,k]=(0,t.useState)("compatibili");(0,t.useEffect)(()=>{a&&(g("assegna_nuova"),j(""),E(""),k("compatibili"),A(""),(null==p?void 0:p.id_cantiere)&&T())},[a,null==p?void 0:p.id_cantiere]);let T=async()=>{if(!(null==p?void 0:p.id_cantiere))return void A("Cantiere non disponibile");try{y(!0),A(""),console.log("\uD83D\uDD04 ModificaBobinaDialog: Caricamento bobine per cantiere:",p.id_cantiere);let e=await d.Fw.getBobine(p.id_cantiere),a=[];if(Array.isArray(e))a=e;else if(e&&Array.isArray(e.data))a=e.data;else if(e&&e.bobine&&Array.isArray(e.bobine))a=e.bobine;else throw Error("Formato risposta API non valido");let i=a.filter(e=>"Terminata"!==e.stato_bobina&&"Over"!==e.stato_bobina&&e.metri_residui>0);N(i),console.log("✅ ModificaBobinaDialog: Bobine caricate:",i.length),console.log("\uD83D\uDCCB ModificaBobinaDialog: Dettaglio bobine:",i.map(e=>({id:e.id_bobina,tipologia:e.tipologia,sezione:e.sezione,metri_residui:e.metri_residui,stato:e.stato_bobina})))}catch(e){console.error("❌ ModificaBobinaDialog: Errore caricamento bobine:",e),A("Errore nel caricamento delle bobine"),N([])}finally{y(!1)}},O=(()=>{if(!l)return[];let e=f.filter(e=>{let a=e.tipologia===l.tipologia&&e.sezione===l.sezione,i=""===S||e.id_bobina.toLowerCase().includes(S.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(S.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(S.toLowerCase());return a&&i&&e.metri_residui>0});return console.log("\uD83D\uDD0D ModificaBobinaDialog: Filtro compatibili:",{cavoTipologia:l.tipologia,cavoSezione:l.sezione,totaleBobine:f.length,bobineCompatibili:e.length,searchText:S}),e})(),D=l?f.filter(e=>{let a=e.tipologia!==l.tipologia||e.sezione!==l.sezione,i=""===S||e.id_bobina.toLowerCase().includes(S.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(S.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(S.toLowerCase());return a&&i&&e.metri_residui>0}):[],R=()=>{g("assegna_nuova"),j(""),E(""),A(""),i()},P=async()=>{if(console.log("\uD83D\uDD04 ModificaBobinaDialog: Salvataggio:",{selectedOption:v,selectedBobina:b,cavoId:null==l?void 0:l.id_cavo,cantiereId:null==p?void 0:p.id_cantiere}),l)try{if(z(!0),A(""),"assegna_nuova"===v){if(!b)return void x("Selezionare una bobina");let e=await d.At.updateMetriPosati({id_cavo:l.id_cavo,metri_posati:l.metratura_reale||0,id_bobina:b,force_over:!0});e.success?(m("Bobina aggiornata con successo per il cavo ".concat(l.id_cavo)),R()):x(e.message||"Errore durante l'aggiornamento della bobina")}else if("rimuovi_bobina"===v){let e=await d.At.updateMetriPosati({id_cavo:l.id_cavo,metri_posati:l.metratura_reale||0,id_bobina:"BOBINA_VUOTA",force_over:!1});e.success?(m("Bobina rimossa dal cavo ".concat(l.id_cavo)),R()):x(e.message||"Errore durante la rimozione della bobina")}else if("annulla_installazione"===v){let e=await d.At.updateMetriPosati({id_cavo:l.id_cavo,metri_posati:0,id_bobina:"BOBINA_VUOTA",force_over:!1});e.success?(m("Installazione annullata per il cavo ".concat(l.id_cavo)),R()):x(e.message||"Errore durante l'annullamento dell'installazione")}}catch(e){console.error("❌ ModificaBobinaDialog: Errore salvataggio:",e),x("Errore durante il salvataggio")}finally{z(!1)}};return l?(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(X.lG,{open:a,onOpenChange:R,children:(0,s.jsxs)(X.Cf,{className:"max-w-4xl max-h-[90vh] flex flex-col",children:[(0,s.jsx)(X.c7,{children:(0,s.jsxs)(X.L3,{children:["Modifica Bobina Cavo ",l.id_cavo]})}),(0,s.jsxs)("div",{className:"flex-1 overflow-hidden space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(M.A,{className:"h-5 w-5 text-blue-600"}),(0,s.jsx)("h3",{className:"font-medium text-gray-900",children:"Cavo Selezionato"})]}),(0,s.jsxs)("div",{className:"p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border-2 border-blue-200 shadow-sm",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 text-sm",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"font-semibold text-blue-700 min-w-[80px]",children:"Tipologia:"}),(0,s.jsx)("span",{className:"font-medium text-gray-800",children:l.tipologia||"N/A"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"font-semibold text-blue-700 min-w-[80px]",children:"Formazione:"}),(0,s.jsx)("span",{className:"font-medium text-gray-800",children:l.sezione||"N/A"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"font-semibold text-blue-700 min-w-[80px]",children:"Colore:"}),(0,s.jsx)("span",{className:"font-medium text-gray-800",children:l.colore_cavo||"N/A"})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"font-semibold text-blue-700 min-w-[80px]",children:"Da:"}),(0,s.jsx)("span",{className:"font-medium text-gray-800",children:l.ubicazione_partenza||"N/A"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"font-semibold text-blue-700 min-w-[80px]",children:"A:"}),(0,s.jsx)("span",{className:"font-medium text-gray-800",children:l.ubicazione_arrivo||"N/A"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"font-semibold text-blue-700 min-w-[80px]",children:"Metri:"}),(0,s.jsxs)("span",{className:"font-bold text-green-700",children:[l.metratura_reale||0," m posati"]}),(0,s.jsxs)("span",{className:"text-gray-500",children:["/ ",l.metratura_teorica||0," m teorici"]})]})]})]}),l.id_bobina&&"BOBINA_VUOTA"!==l.id_bobina&&(0,s.jsx)("div",{className:"mt-3 pt-3 border-t border-blue-200",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(M.A,{className:"h-4 w-4 text-blue-600"}),(0,s.jsx)("span",{className:"font-semibold text-blue-700",children:"Bobina Attuale:"}),(0,s.jsx)("span",{className:"font-medium text-gray-800",children:(e=>{if(!e||"BOBINA_VUOTA"===e)return"VUOTA";if(e&&e.includes("_B"))return e.split("_B")[1];let a=f.find(a=>a.id_bobina===e);return a&&a.numero_bobina||e})(l.id_bobina)})]})})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("h3",{className:"font-medium",children:"Opzioni di modifica"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,s.jsx)("input",{type:"radio",name:"operazione",value:"assegna_nuova",checked:"assegna_nuova"===v,onChange:e=>g(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,s.jsx)("span",{className:"text-sm",children:"Cambia bobina"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,s.jsx)("input",{type:"radio",name:"operazione",value:"rimuovi_bobina",checked:"rimuovi_bobina"===v,onChange:e=>g(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,s.jsx)("span",{className:"text-sm",children:"Bobina vuota"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-red-50",children:[(0,s.jsx)("input",{type:"radio",name:"operazione",value:"annulla_installazione",checked:"annulla_installazione"===v,onChange:e=>g(e.target.value),className:"w-4 h-4 text-red-600"}),(0,s.jsx)("span",{className:"text-sm text-red-600 font-medium",children:"Annulla posa (reset completo)"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,s.jsx)("input",{type:"radio",name:"operazione",value:"annulla_installazione",checked:"annulla_installazione"===v,onChange:e=>g(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,s.jsx)("span",{className:"text-sm text-red-600",children:"Annulla posa"})]})]})]}),"assegna_nuova"===v&&(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("h3",{className:"font-medium",children:"Seleziona bobina"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(F.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,s.jsx)(h.p,{placeholder:"Cerca bobina per ID, tipologia o numero...",value:S,onChange:e=>E(e.target.value),className:"pl-10"})]}),(0,s.jsxs)("div",{className:"flex space-x-1 border-b",children:[(0,s.jsx)("button",{onClick:()=>k("compatibili"),className:"px-4 py-2 text-sm font-medium border-b-2 transition-colors ".concat("compatibili"===I?"border-green-500 text-green-600 bg-green-50":"border-transparent text-gray-500 hover:text-gray-700"),children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(L.A,{className:"h-4 w-4"}),(0,s.jsxs)("span",{children:["Bobine Compatibili (",O.length,")"]})]})}),(0,s.jsx)("button",{onClick:()=>k("incompatibili"),className:"px-4 py-2 text-sm font-medium border-b-2 transition-colors ".concat("incompatibili"===I?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700"),children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(eA.A,{className:"h-4 w-4"}),(0,s.jsxs)("span",{children:["Bobine Incompatibili (",D.length,")"]})]})})]}),(0,s.jsx)("div",{className:"border rounded-lg h-64 overflow-y-auto",children:_?(0,s.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(ei.A,{className:"h-4 w-4 animate-spin"}),(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Caricamento bobine..."})]})}):(0,s.jsx)("div",{className:"p-2",children:"compatibili"===I?0===O.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"text-gray-500 text-sm mb-2",children:"Nessuna bobina compatibile trovata"}),(0,s.jsxs)("div",{className:"text-xs text-gray-400",children:["Cercando bobine con tipologia ",(0,s.jsx)("strong",{children:l.tipologia})," e formazione ",(0,s.jsx)("strong",{children:l.sezione})]})]}):(0,s.jsx)("div",{className:"space-y-2",children:O.map(e=>(0,s.jsx)("div",{onClick:()=>j(e.id_bobina),className:"p-3 rounded-lg cursor-pointer transition-all duration-200 ".concat(b===e.id_bobina?"bg-blue-100 border-2 border-blue-300 shadow-md":"hover:bg-gray-50 border border-gray-200 hover:border-gray-300"),children:(0,s.jsxs)("div",{className:"flex justify-between items-start",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,s.jsx)("div",{className:"font-medium text-sm text-gray-900",children:e.id_bobina}),e.stato_bobina&&(0,s.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("Disponibile"===e.stato_bobina?"bg-green-100 text-green-800":"In uso"===e.stato_bobina?"bg-blue-100 text-blue-800":"bg-yellow-100 text-yellow-800"),children:e.stato_bobina})]}),(0,s.jsxs)("div",{className:"text-xs text-gray-500 mb-1",children:[(0,s.jsx)("span",{className:"font-medium",children:e.tipologia})," • ",(0,s.jsx)("span",{children:e.sezione})]})]}),(0,s.jsxs)("div",{className:"text-right ml-3",children:[(0,s.jsxs)("div",{className:"text-sm font-medium ".concat(e.metri_residui>0?"text-green-600":"text-gray-500"),children:[e.metri_residui,"m"]}),(0,s.jsx)("div",{className:"text-xs text-gray-400",children:e.metri_residui>0?"disponibili":"esaurita"})]})]})},e.id_bobina))}):0===D.length?(0,s.jsx)("div",{className:"text-center py-8 text-gray-500 text-sm",children:"Nessuna bobina incompatibile trovata"}):(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("div",{className:"p-3 bg-orange-50 border border-orange-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,s.jsx)(eA.A,{className:"h-4 w-4 text-orange-600 mt-0.5 flex-shrink-0"}),(0,s.jsxs)("div",{className:"text-sm text-orange-800",children:[(0,s.jsx)("div",{className:"font-medium mb-1",children:"Bobine Incompatibili"}),(0,s.jsx)("div",{className:"text-xs",children:"Selezionando una bobina incompatibile, le caratteristiche del cavo verranno aggiornate."})]})]})}),D.map(e=>(0,s.jsx)("div",{onClick:()=>j(e.id_bobina),className:"p-3 rounded-lg cursor-pointer transition-all duration-200 ".concat(b===e.id_bobina?"bg-orange-100 border-2 border-orange-300 shadow-md":"hover:bg-gray-50 border border-gray-200 hover:border-gray-300"),children:(0,s.jsxs)("div",{className:"flex justify-between items-start",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,s.jsx)("div",{className:"font-medium text-sm text-gray-900",children:e.id_bobina}),(0,s.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800",children:"INCOMPATIBILE"})]}),(0,s.jsxs)("div",{className:"text-xs text-gray-500 mb-1",children:[(0,s.jsx)("span",{className:"font-medium",children:e.tipologia})," • ",(0,s.jsx)("span",{children:e.sezione})]})]}),(0,s.jsxs)("div",{className:"text-right ml-3",children:[(0,s.jsxs)("div",{className:"text-sm font-medium ".concat(e.metri_residui>0?"text-orange-600":"text-gray-500"),children:[e.metri_residui,"m"]}),(0,s.jsx)("div",{className:"text-xs text-gray-400",children:e.metri_residui>0?"disponibili":"esaurita"})]})]})},e.id_bobina))]})})})]})]}),w&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(eA.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:w})]}),(0,s.jsxs)(X.Es,{className:"flex justify-end space-x-2",children:[(0,s.jsx)(n.$,{variant:"outline",onClick:R,disabled:C,children:"Annulla"}),(0,s.jsxs)(n.$,{onClick:P,disabled:C||"assegna_nuova"===v&&!b,children:[C&&(0,s.jsx)(ei.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salva"]})]})]})})}):null}function eE(e){let{open:a,onClose:i,cavo:l,cantiere:r,onSuccess:x,onError:u}=e,{cantiere:p}=(0,c.A)(),v=r||p,[g,b]=(0,t.useState)({metri_posati:"",id_bobina:""});(0,t.useEffect)(()=>{console.log("\uD83D\uDCCA InserisciMetriDialog: FormData aggiornato:",{hasMetri:!!g.metri_posati,hasBobina:!!g.id_bobina,metri_posati:g.metri_posati,id_bobina:g.id_bobina})},[g]);let[j,f]=(0,t.useState)({}),[N,_]=(0,t.useState)({}),[y,z]=(0,t.useState)(!1),[w,A]=(0,t.useState)([]),[S,E]=(0,t.useState)(!1),[I,k]=(0,t.useState)(""),[O,D]=(0,t.useState)(!1);(0,t.useEffect)(()=>{a&&v&&U()},[a,v]),(0,t.useEffect)(()=>{a&&l&&(b({metri_posati:"",id_bobina:""}),f({}),_({}),k(""))},[a,l]);let R=e=>{if(!e||"BOBINA_VUOTA"===e)return"VUOTA";if(e&&e.includes("_B"))return e.split("_B")[1];let a=w.find(a=>a.id_bobina===e);return a&&a.numero_bobina||e};w.filter(e=>{if(!I)return!0;let a=I.toLowerCase();return e.id_bobina.toLowerCase().includes(a)||e.tipologia.toLowerCase().includes(a)||e.formazione.toLowerCase().includes(a)||R(e.id_bobina).toLowerCase().includes(a)});let P=l?w.filter(e=>{let a=e.tipologia===l.tipologia&&e.sezione===l.sezione,i=""===I||e.id_bobina.toLowerCase().includes(I.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(I.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(I.toLowerCase());return a&&i&&e.metri_residui>0}):[],B=l?w.filter(e=>{let a=e.tipologia!==l.tipologia||e.sezione!==l.sezione,i=""===I||e.id_bobina.toLowerCase().includes(I.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(I.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(I.toLowerCase());return a&&i&&e.metri_residui>0}):[],M=e=>{console.log("\uD83C\uDFAF Bobina selezionata:",{id:e.id_bobina,numero:R(e.id_bobina),tipologia:e.tipologia,formazione:e.formazione,metri_residui:e.metri_residui}),b(a=>({...a,id_bobina:e.id_bobina})),f(e=>({...e,id_bobina:void 0}))};(0,t.useEffect)(()=>{a&&l&&(v&&U(),b({metri_posati:"0",id_bobina:""}),f({}),_({}),k(""))},[a,l,v]),(0,t.useEffect)(()=>{g.metri_posati&&l?$(parseFloat(g.metri_posati)):(f(e=>({...e,metri_posati:void 0})),_(e=>({...e,metri_posati:void 0})))},[g.metri_posati,l]);let $=e=>{if(!l)return;let a={...j},i={...N};delete a.metri_posati,delete i.metri_posati,e>1.1*(l.metri_teorici||0)?i.metri_posati="Attenzione: i metri posati superano del 10% i metri teorici (".concat(l.metri_teorici,"m)"):e>(l.metri_teorici||0)&&(i.metri_posati="Metratura superiore ai metri teorici"),f(a),_(i)},U=async()=>{if(console.log({cavo:!!l,cantiere:!!v,cavoId:null==l?void 0:l.id_cavo,cantiereId:null==v?void 0:v.id_cantiere}),l&&v)try{E(!0);let e=await d.Fw.getBobine(v.id_cantiere),a=[];if(Array.isArray(e))a=e;else if(e&&Array.isArray(e.data))a=e.data;else if(e&&e.bobine&&Array.isArray(e.bobine))a=e.bobine;else throw Error("Formato risposta API non valido");let i=a.filter(e=>"Terminata"!==e.stato_bobina&&"Over"!==e.stato_bobina&&e.metri_residui>0);if(l){console.log({tipologia:l.tipologia,sezione:l.sezione});let e=i.filter(e=>e.tipologia===l.tipologia&&e.sezione===l.sezione),a=i.filter(e=>e.tipologia!==l.tipologia||e.sezione!==l.sezione);e.sort((e,a)=>a.metri_residui-e.metri_residui),a.sort((e,a)=>a.metri_residui-e.metri_residui);let s=[...e,...a];A(s)}else i.sort((e,a)=>a.metri_residui-e.metri_residui),A(i)}catch(s){var e,a,i;console.log({message:s.message,response:s.response,status:null==(e=s.response)?void 0:e.status,data:null==(a=s.response)?void 0:a.data}),(null==(i=s.response)?void 0:i.status)!==404&&u("Errore nel caricamento delle bobine. Puoi comunque usare BOBINA VUOTA."),A([])}finally{E(!1)}},J=async()=>{if(console.log({cavo:null==l?void 0:l.id_cavo,metri_posati:g.metri_posati,id_bobina:g.id_bobina}),!l)return;if(!g.metri_posati||0>parseFloat(g.metri_posati))return void u("Inserire metri posati validi (≥ 0)");if(!g.id_bobina)return void u("Selezionare una bobina o BOBINA VUOTA");let e=parseFloat(g.metri_posati);if("BOBINA_VUOTA"!==g.id_bobina){let e=w.find(e=>e.id_bobina===g.id_bobina);e&&e.metri_residui}try{if(z(!0),!v)throw Error("Cantiere non selezionato");console.log({cantiere:v.id_cantiere,cavo:l.id_cavo,metri:e,bobina:g.id_bobina,isBobinaVuota:"BOBINA_VUOTA"===g.id_bobina}),await d.At.updateMetriPosati(v.id_cantiere,l.id_cavo,e,g.id_bobina,!0),x("Metri posati aggiornati con successo per il cavo ".concat(l.id_cavo,": ").concat(e,"m")),i()}catch(e){var a,s;u((null==(s=e.response)||null==(a=s.data)?void 0:a.detail)||e.message||"Errore durante il salvataggio dei metri posati")}finally{z(!1)}},V=()=>{y||(b({metri_posati:"",id_bobina:""}),f({}),_({}),k(""),i())};return l?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(X.lG,{open:a,onOpenChange:V,children:(0,s.jsxs)(X.Cf,{className:"max-w-7xl h-[90vh] flex flex-col",children:[(0,s.jsxs)(X.c7,{className:"flex-shrink-0",children:[(0,s.jsxs)(X.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(ew.A,{className:"h-5 w-5"}),"Inserisci Metri Posati - ",l.id_cavo]}),(0,s.jsx)(X.rr,{children:"Inserisci i metri effettivamente posati per il cavo e seleziona una bobina o usa BOBINA VUOTA"})]}),(0,s.jsxs)("div",{className:"flex-1 overflow-y-auto space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-4",children:[(0,s.jsx)("div",{className:"lg:col-span-2",children:(0,s.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-200",children:[(0,s.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"Informazioni Cavo"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Tipologia:"})," ",l.tipologia||"N/A"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Da:"})," ",l.ubicazione_partenza||"N/A"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Formazione:"})," ",l.sezione||"N/A"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"A:"})," ",l.ubicazione_arrivo||"N/A"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Metri teorici:"})," ",l.metri_teorici||"N/A"," m"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Gi\xe0 posati:"})," ",l.metratura_reale||0," m"]})]})]})}),(0,s.jsx)("div",{className:"lg:col-span-1",children:(0,s.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-300 h-full",children:[(0,s.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"Metri da Installare"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"metri",className:"text-sm font-medium",children:"Metri Posati"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(h.p,{id:"metri",type:"number",value:g.metri_posati,onChange:e=>b(a=>({...a,metri_posati:e.target.value})),placeholder:"Inserisci metri posati",disabled:y,step:"0.1",min:"0",className:"text-lg font-bold text-center border-2 border-blue-400 focus:border-blue-600",autoFocus:!0}),(0,s.jsx)("span",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-lg font-bold text-blue-600",children:"m"})]}),j.metri_posati&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:j.metri_posati}),N.metri_posati&&(0,s.jsx)("p",{className:"text-sm text-amber-600",children:N.metri_posati})]})]})})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"font-semibold text-blue-800 text-lg",children:"Selezione Bobina"}),(0,s.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-12 gap-3 items-center",children:[(0,s.jsx)("div",{className:"sm:col-span-5",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(F.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,s.jsx)(h.p,{placeholder:"ID, tipologia, formazione...",value:I,onChange:e=>k(e.target.value),className:"pl-10",disabled:y}),I&&(0,s.jsx)(n.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0",onClick:()=>k(""),children:(0,s.jsx)(C.A,{className:"h-4 w-4"})})]})}),(0,s.jsx)("div",{className:"sm:col-span-7",children:(0,s.jsxs)(n.$,{type:"button",variant:"BOBINA_VUOTA"===g.id_bobina?"default":"outline",className:"w-full h-10 font-bold flex items-center justify-center gap-2 ".concat("BOBINA_VUOTA"===g.id_bobina?"bg-green-600 hover:bg-green-700 text-white":"border-blue-400 text-blue-700 hover:bg-blue-50"),onClick:()=>{console.log("\uD83C\uDFAF BOBINA VUOTA selezionata - cavo sar\xe0 posato senza bobina specifica"),b(e=>({...e,id_bobina:"BOBINA_VUOTA"})),f(e=>{let a={...e};return delete a.id_bobina,a})},disabled:y,children:["BOBINA_VUOTA"===g.id_bobina&&(0,s.jsx)(L.A,{className:"h-5 w-5"}),"BOBINA VUOTA"]})})]}),"BOBINA_VUOTA"===g.id_bobina&&(0,s.jsx)("div",{className:"mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-start gap-2",children:[(0,s.jsx)(L.A,{className:"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0"}),(0,s.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,s.jsx)("p",{className:"font-medium",children:"Bobina Vuota Selezionata"}),(0,s.jsx)("p",{className:"mt-1",children:'Il cavo sar\xe0 posato senza assegnazione di bobina specifica. Potrai collegarlo a una bobina in seguito tramite la funzione "Modifica Bobina".'})]})]})})]}),S?(0,s.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,s.jsx)(ei.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,s.jsx)("span",{children:"Caricamento bobine..."})]}):(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h4",{className:"font-medium text-green-700 mb-2 flex items-center gap-2",children:[(0,s.jsx)(L.A,{className:"h-4 w-4"}),"Bobine Compatibili (",P.length,")"]}),(0,s.jsx)("div",{className:"max-h-72 overflow-y-auto border rounded-lg",children:0===P.length?(0,s.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina compatibile trovata"}):(0,s.jsx)("div",{className:"divide-y",children:P.map(e=>(0,s.jsx)("div",{className:"p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ".concat(g.id_bobina===e.id_bobina?"bg-green-100 border-green-500 shadow-md":"border-gray-200 hover:bg-green-50 hover:border-green-300"),onClick:()=>M(e),children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[g.id_bobina===e.id_bobina&&(0,s.jsx)(L.A,{className:"h-5 w-5 text-green-600 flex-shrink-0"}),(0,s.jsx)("div",{className:"font-bold text-base min-w-fit",children:R(e.id_bobina)}),(0,s.jsxs)("div",{className:"text-sm text-gray-600 truncate",children:[e.tipologia," - ",e.sezione]})]}),(0,s.jsxs)(m.E,{variant:"outline",className:"bg-green-100 text-green-800 border-green-300 font-medium min-w-fit",children:[e.metri_residui,"m"]})]})},e.id_bobina))})})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("h4",{className:"font-medium text-amber-700 mb-2 flex items-center gap-2",children:[(0,s.jsx)(es.A,{className:"h-4 w-4"}),"Bobine Incompatibili (",B.length,")"]}),(0,s.jsx)("div",{className:"max-h-72 overflow-y-auto border rounded-lg",children:0===B.length?(0,s.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina incompatibile trovata"}):(0,s.jsx)("div",{className:"divide-y",children:B.map(e=>(0,s.jsx)("div",{className:"p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ".concat(g.id_bobina===e.id_bobina?"bg-amber-100 border-amber-500 shadow-md":"border-gray-200 hover:bg-amber-50 hover:border-amber-300"),onClick:()=>M(e),children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[g.id_bobina===e.id_bobina&&(0,s.jsx)(L.A,{className:"h-5 w-5 text-amber-600 flex-shrink-0"}),(0,s.jsx)("div",{className:"font-bold text-base min-w-fit",children:R(e.id_bobina)}),(0,s.jsxs)("div",{className:"text-sm text-gray-600 truncate",children:[e.tipologia," - ",e.sezione]})]}),(0,s.jsxs)(m.E,{variant:"outline",className:"bg-amber-100 text-amber-800 border-amber-300 font-medium min-w-fit",children:[e.metri_residui,"m"]})]})},e.id_bobina))})})]})]}),0===w.length&&!S&&(0,s.jsxs)(o.Fc,{className:"border-amber-200 bg-amber-50",children:[(0,s.jsx)(es.A,{className:"h-4 w-4 text-amber-600"}),(0,s.jsx)(o.TN,{className:"text-amber-800",children:"Non ci sono bobine disponibili. Puoi procedere con BOBINA VUOTA o aggiungere prima una nuova bobina."})]}),j.id_bobina&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(eA.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:j.id_bobina})]})]})]}),(0,s.jsxs)(X.Es,{className:"flex-shrink-0 border-t pt-4 mt-4 flex justify-between items-center",children:[(0,s.jsx)("div",{children:"installato"===l.stato_installazione&&l.id_bobina&&(0,s.jsx)(n.$,{variant:"outline",onClick:()=>{D(!0)},disabled:y,className:"text-blue-600 border-blue-300 hover:bg-blue-50",children:"Modifica Bobina"})}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(n.$,{variant:"outline",onClick:V,disabled:y,children:"Annulla"}),(0,s.jsxs)(n.$,{onClick:J,disabled:y||!g.metri_posati||0>parseFloat(g.metri_posati)||!g.id_bobina,className:"bg-mariner-600 hover:bg-mariner-700 text-white disabled:bg-gray-400 disabled:text-gray-200",children:[y&&(0,s.jsx)(ei.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salva"]})]})]})]})}),(0,s.jsx)(eS,{open:O,onClose:()=>D(!1),cavo:l,onSuccess:e=>{x(e),D(!1),i()},onError:u})]}):null}var eI=i(2564);function ek(e){let{open:a,onClose:i,onConfirm:l,title:r,description:c,isLoading:d,isDangerous:m=!1}=e,[x,u]=(0,t.useState)(!1);return(0,t.useEffect)(()=>{a||u(!1)},[a]),(0,t.useEffect)(()=>{let e=e=>{"Escape"===e.key&&a&&!d&&i()};if(a)return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[a,i,d]),(0,s.jsx)(X.lG,{open:a,onOpenChange:i,children:(0,s.jsx)(X.Cf,{className:"sm:max-w-[400px]","aria-describedby":"confirm-disconnect-description",children:x?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(X.c7,{children:(0,s.jsx)(X.L3,{className:"text-center text-orange-600",children:"Conferma Finale"})}),(0,s.jsxs)("div",{className:"py-4 text-center",children:[(0,s.jsx)("div",{className:"mx-auto w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-4",children:(0,s.jsx)(es.A,{className:"h-6 w-6 text-orange-600"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Sei veramente sicuro?"}),(0,s.jsxs)("p",{className:"text-sm text-gray-600 mb-4",children:["Questa azione scollegher\xe0 ",(0,s.jsx)("strong",{children:"entrambi i lati"})," del cavo."]}),(0,s.jsx)("div",{className:"bg-orange-50 border border-orange-200 rounded-md p-3",children:(0,s.jsx)("p",{className:"text-sm text-orange-800 font-medium",children:"⚠️ Operazione irreversibile"})})]}),(0,s.jsxs)(X.Es,{className:"gap-2",children:[(0,s.jsx)(n.$,{variant:"outline",onClick:()=>u(!1),disabled:d,className:"flex-1",children:"No, Annulla"}),(0,s.jsx)(n.$,{variant:"outline",onClick:()=>{l()},disabled:d,className:"flex-1 bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100 hover:border-orange-300",children:d?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(ei.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Scollegando..."]}):"S\xec, Scollega"})]})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(X.c7,{children:[(0,s.jsxs)(X.L3,{className:"flex items-center gap-2 text-orange-600",children:[(0,s.jsx)(es.A,{className:"h-5 w-5"}),r]}),(0,s.jsx)(X.rr,{id:"confirm-disconnect-description",children:c})]}),(0,s.jsx)("div",{className:"py-4",children:(0,s.jsxs)(o.Fc,{className:"border-orange-200 bg-orange-50",children:[(0,s.jsx)(es.A,{className:"h-4 w-4 text-orange-600"}),(0,s.jsxs)(o.TN,{className:"text-orange-800",children:[(0,s.jsx)("strong",{children:"Attenzione:"})," Questa azione modificher\xe0 lo stato del collegamento del cavo."]})]})}),(0,s.jsxs)(X.Es,{className:"gap-2",children:[(0,s.jsx)(n.$,{variant:"outline",onClick:i,disabled:d,className:"flex-1 hover:bg-gray-50",children:"Annulla"}),(0,s.jsxs)(n.$,{variant:"outline",onClick:()=>{m?u(!0):l()},disabled:d,className:"flex-1 bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100 hover:border-orange-300",children:[(0,s.jsx)(es.A,{className:"mr-2 h-4 w-4"}),m?"Procedi":"Conferma"]})]})]})})})}let eT=function(e){let{open:a,onClose:i,cavo:l,onSuccess:r,onError:m}=e,{cantiere:x}=(0,c.A)(),u=(0,ef.E)(),[h,p]=(0,t.useState)("cantiere"),[v,g]=(0,t.useState)(!1),[b,j]=(0,t.useState)(""),[f,N]=(0,t.useState)({open:!1,type:null,title:"",description:""}),_=(0,t.useRef)(null),y=(0,t.useRef)(null),z=(0,t.useRef)(null),[w,A]=(0,t.useState)("");(0,t.useEffect)(()=>{a&&l&&(p("cantiere"),j(""))},[a,l]),(0,t.useEffect)(()=>{if(a&&_.current){let e=_.current.querySelectorAll('button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])');e.length>0&&e[0].focus()}},[a]),(0,t.useEffect)(()=>{let e=e=>{if(a&&!v&&!f.open)switch(e.key){case"Escape":i();break;case"Tab":var s;let t=null==(s=_.current)?void 0:s.querySelectorAll('button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])');if(t&&t.length>0){let a=t[0],i=t[t.length-1];e.shiftKey&&document.activeElement===a?(e.preventDefault(),i.focus()):e.shiftKey||document.activeElement!==i||(e.preventDefault(),a.focus())}break;case"1":case"2":case"3":if(!v){e.preventDefault();let a=parseInt(e.key);1===a?I():2===a?F():3===a&&D()}}};if(a)return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[a,v,f.open]);let S=e=>{A(e),setTimeout(()=>A(""),1e3)},E=()=>{if(!l)return{stato:"non_collegato",descrizione:"Non collegato"};switch(l.collegamento||l.collegamenti||0){case 1:return{stato:"partenza",descrizione:"\uD83D\uDFE2⚪ Collegato lato partenza"};case 2:return{stato:"arrivo",descrizione:"⚪\uD83D\uDFE2 Collegato lato arrivo"};case 3:return{stato:"completo",descrizione:"\uD83D\uDFE2\uD83D\uDFE2 Completamente collegato"};default:return{stato:"non_collegato",descrizione:"⚪⚪ Non collegato"}}},I=()=>{if(!l)return;let e=E();"partenza"===e.stato||"completo"===e.stato?N({open:!0,type:"partenza",title:"Scollega lato partenza",description:"Vuoi scollegare il lato partenza del cavo ".concat(l.id_cavo,"?")}):k()},k=async()=>{if(l&&x)try{g(!0),j(""),S("Collegamento in corso..."),await d.At.collegaCavo(x.id_cantiere,l.id_cavo,"partenza",h);let e="Collegamento lato partenza completato per il cavo ".concat(l.id_cavo);S(e),u.success("Successo",e),r&&r(),i()}catch(s){var e,a;let i=(null==(a=s.response)||null==(e=a.data)?void 0:e.detail)||s.message||"Errore durante il collegamento";j(i),S("Errore: ".concat(i)),m&&m(i)}finally{g(!1)}},F=()=>{if(!l)return;let e=E();"arrivo"===e.stato||"completo"===e.stato?N({open:!0,type:"arrivo",title:"Scollega lato arrivo",description:"Vuoi scollegare il lato arrivo del cavo ".concat(l.id_cavo,"?")}):O()},O=async()=>{if(l&&x)try{g(!0),j(""),S("Collegamento in corso..."),await d.At.collegaCavo(x.id_cantiere,l.id_cavo,"arrivo",h);let e="Collegamento lato arrivo completato per il cavo ".concat(l.id_cavo);S(e),u.success("Successo",e),r&&r(),i()}catch(s){var e,a;let i=(null==(a=s.response)||null==(e=a.data)?void 0:e.detail)||s.message||"Errore durante il collegamento";j(i),S("Errore: ".concat(i)),m&&m(i)}finally{g(!1)}},D=()=>{l&&("completo"===E().stato?N({open:!0,type:"entrambi",title:"Scollega entrambi i lati",description:"Vuoi scollegare completamente il cavo ".concat(l.id_cavo,"? Questa operazione rimuover\xe0 tutti i collegamenti.")}):R())},R=async()=>{if(l&&x)try{g(!0),j(""),S("Collegamento entrambi i lati in corso..."),await d.At.collegaCavo(x.id_cantiere,l.id_cavo,"entrambi",h);let e="Collegamento completo per il cavo ".concat(l.id_cavo);S(e),u.success("Successo",e),r&&r(),i()}catch(s){var e,a;let i=(null==(a=s.response)||null==(e=a.data)?void 0:e.detail)||s.message||"Errore durante il collegamento";j(i),S("Errore: ".concat(i)),m&&m(i)}finally{g(!1)}},P=async()=>{if(l&&x&&f.type)try{g(!0),j(""),S("Scollegamento in corso..."),await d.At.scollegaCavo(x.id_cantiere,l.id_cavo,"entrambi"===f.type?void 0:f.type);let e="entrambi"===f.type?"":" lato ".concat(f.type),a="Scollegamento".concat(e," completato per il cavo ").concat(l.id_cavo);S(a),u.success("Successo",a),r&&r(),N({open:!1,type:null,title:"",description:""}),i()}catch(s){var e,a;let i=(null==(a=s.response)||null==(e=a.data)?void 0:e.detail)||s.message||"Errore durante lo scollegamento";j(i),S("Errore: ".concat(i)),m&&m(i)}finally{g(!1)}};if(!l)return null;let M=E(),$=(l.metri_posati||l.metratura_reale||0)>0;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(eI.bL,{children:(0,s.jsx)("div",{"aria-live":"polite","aria-atomic":"true",children:w})}),(0,s.jsx)(X.lG,{open:a,onOpenChange:i,children:(0,s.jsxs)(X.Cf,{className:"sm:max-w-[600px] max-h-[90vh] overflow-y-auto",ref:_,"aria-describedby":"collegamenti-description",children:[(0,s.jsxs)(X.c7,{children:[(0,s.jsxs)(X.L3,{className:"flex items-center gap-2 text-blue-600",children:[(0,s.jsx)(B.A,{className:"h-5 w-5"}),"Gestione Collegamenti - ",l.id_cavo]}),(0,s.jsxs)(X.rr,{id:"collegamenti-description",children:["Gestisci i collegamenti del cavo ",l.id_cavo,". Usa i tasti 1, 2, 3 per azioni rapide."]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-200",children:(0,s.jsxs)("div",{className:"text-sm font-medium text-blue-800",children:["Informazioni Cavo / Tipologia: ",l.tipologia||"N/A"," / Da: ",l.ubicazione_partenza||"N/A"," / Formazione: ",l.sezione||"N/A"," / A: ",l.ubicazione_arrivo||"N/A"," / Metri Posati: ",l.metratura_reale||0," m"]})}),(0,s.jsx)("div",{className:"p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border",children:(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{children:[(0,s.jsx)(T.J,{className:"text-sm font-medium text-gray-700",children:"Stato Collegamento"}),(0,s.jsxs)("div",{className:"mt-1 text-lg font-semibold flex items-center gap-2",children:["completo"===M.stato&&(0,s.jsx)(L.A,{className:"h-5 w-5 text-green-600"}),"non_collegato"===M.stato&&(0,s.jsx)(eA.A,{className:"h-5 w-5 text-gray-400"}),("partenza"===M.stato||"arrivo"===M.stato)&&(0,s.jsx)(es.A,{className:"h-5 w-5 text-orange-500"}),M.descrizione]})]})})}),!$&&(0,s.jsxs)(o.Fc,{className:"border-orange-200 bg-orange-50",children:[(0,s.jsx)(eA.A,{className:"h-4 w-4 text-orange-600"}),(0,s.jsxs)(o.TN,{className:"text-orange-800",children:[(0,s.jsx)("strong",{children:"Attenzione:"})," Il cavo deve essere installato prima di poter essere collegato."]})]}),b&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(eA.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:b})]}),$&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(T.J,{className:"text-sm font-medium",children:"Responsabile Collegamento"}),(0,s.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg border",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(L.A,{className:"h-4 w-4 text-green-600"}),(0,s.jsx)("span",{className:"font-medium",children:"Cantiere"})]}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Collegamento eseguito dal responsabile del cantiere"})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(T.J,{className:"text-sm font-medium",children:"Azioni Disponibili"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-3",children:[(0,s.jsx)(n.$,{ref:y,onClick:I,disabled:v,className:"w-full h-12 text-left justify-start bg-green-50 text-green-700 border-green-200 hover:bg-green-100 hover:border-green-300",variant:"outline",children:(0,s.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-sm font-bold text-green-700",children:"1"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:"partenza"===M.stato||"completo"===M.stato?"Scollega Partenza":"Collega Partenza"}),(0,s.jsx)("div",{className:"text-xs text-green-600",children:"partenza"===M.stato||"completo"===M.stato?"Rimuovi collegamento lato partenza":"Connetti il lato partenza del cavo"})]})]}),v?(0,s.jsx)(ei.A,{className:"h-4 w-4 animate-spin"}):(0,s.jsx)(B.A,{className:"h-4 w-4"})]})}),(0,s.jsx)(n.$,{onClick:F,disabled:v,className:"w-full h-12 text-left justify-start bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 hover:border-blue-300",variant:"outline",children:(0,s.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-sm font-bold text-blue-700",children:"2"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:"arrivo"===M.stato||"completo"===M.stato?"Scollega Arrivo":"Collega Arrivo"}),(0,s.jsx)("div",{className:"text-xs text-blue-600",children:"arrivo"===M.stato||"completo"===M.stato?"Rimuovi collegamento lato arrivo":"Connetti il lato arrivo del cavo"})]})]}),v?(0,s.jsx)(ei.A,{className:"h-4 w-4 animate-spin"}):(0,s.jsx)(B.A,{className:"h-4 w-4"})]})}),(0,s.jsx)(n.$,{onClick:D,disabled:v,className:"w-full h-12 text-left justify-start bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100 hover:border-purple-300",variant:"outline",children:(0,s.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-sm font-bold text-purple-700",children:"3"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:"completo"===M.stato?"Scollega Completamente":"Collega Entrambi"}),(0,s.jsx)("div",{className:"text-xs text-purple-600",children:"completo"===M.stato?"Rimuovi tutti i collegamenti":"Connetti entrambi i lati del cavo"})]})]}),v?(0,s.jsx)(ei.A,{className:"h-4 w-4 animate-spin"}):(0,s.jsx)(B.A,{className:"h-4 w-4"})]})})]})]})]})]}),(0,s.jsx)(X.Es,{children:(0,s.jsxs)(n.$,{ref:z,variant:"outline",onClick:i,disabled:v,className:"hover:bg-gray-50",children:[(0,s.jsx)(C.A,{className:"mr-2 h-4 w-4"}),"Chiudi"]})})]})}),(0,s.jsx)(ek,{open:f.open,onClose:()=>N({open:!1,type:null,title:"",description:""}),onConfirm:P,title:f.title,description:f.description,isLoading:v,isDangerous:"entrambi"===f.type})]})};var eF=i(69037),eO=i(87481);function eD(e){let{open:a,onClose:i,cavo:l,onSuccess:r,onError:m}=e,{cantiere:x}=(0,c.A)(),{toast:u}=(0,eO.dj)(),[v,g]=(0,t.useState)({responsabile_certificazione:"",data_certificazione:new Date().toISOString().split("T")[0],esito_certificazione:"CONFORME",note_certificazione:""}),[b,j]=(0,t.useState)([]),[f,N]=(0,t.useState)(!1),[_,y]=(0,t.useState)(!1),[z,w]=(0,t.useState)(""),[A,S]=(0,t.useState)(""),E=e=>{S(e),setTimeout(()=>S(""),1e3)};(0,t.useEffect)(()=>{a&&l&&(g({responsabile_certificazione:"",data_certificazione:new Date().toISOString().split("T")[0],esito_certificazione:"CONFORME",note_certificazione:""}),w(""),I())},[a,l]);let I=async()=>{if(x)try{y(!0);let e=await d.AR.getResponsabili(x.id_cantiere);j(e.data)}catch(e){j([])}finally{y(!1)}},k=()=>!!l&&3===(l.collegamento||l.collegamenti||0),F=async()=>{if(!l||!x)return!1;try{return E("Collegamento automatico in corso..."),await d.At.collegaCavo(x.id_cantiere,l.id_cavo,"entrambi","cantiere"),E("Cavo collegato automaticamente"),!0}catch(e){return console.error("Errore nel collegamento automatico:",e),!1}},O=async()=>{if(l&&x){if(!v.responsabile_certificazione){w("Seleziona un responsabile per la certificazione"),E("Errore: Seleziona un responsabile per la certificazione");return}try{if(N(!0),w(""),E("Certificazione in corso..."),!k()&&(E("Collegamento automatico del cavo..."),!await F())){w("Impossibile collegare automaticamente il cavo. Collegalo manualmente prima di certificare."),E("Errore: Impossibile collegare automaticamente il cavo");return}let e={id_cavo:l.id_cavo,responsabile_certificazione:v.responsabile_certificazione,data_certificazione:v.data_certificazione,esito_certificazione:v.esito_certificazione,note_certificazione:v.note_certificazione||null};await d.km.createCertificazione(x.id_cantiere,e);let a="Certificazione completata per il cavo ".concat(l.id_cavo);E(a),u({title:"Successo",description:a}),r&&r(a),i()}catch(s){var e,a;let i=(null==(a=s.response)||null==(e=a.data)?void 0:e.detail)||s.message||"Errore durante la certificazione";w(i),E("Errore: ".concat(i)),m&&m(i)}finally{N(!1)}}},D=async()=>{if(l&&x)try{N(!0),w(""),E("Generazione PDF in corso...");let e=await d.km.generatePDF(x.id_cantiere,l.id_cavo),a=window.URL.createObjectURL(new Blob([e.data])),i=document.createElement("a");i.href=a,i.setAttribute("download","certificato_".concat(l.id_cavo,".pdf")),document.body.appendChild(i),i.click(),i.remove(),window.URL.revokeObjectURL(a);let s="PDF certificato generato per il cavo ".concat(l.id_cavo);E(s),u({title:"Successo",description:s}),r&&r(s)}catch(s){var e,a;let i=(null==(a=s.response)||null==(e=a.data)?void 0:e.detail)||s.message||"Errore durante la generazione del PDF";w(i),E("Errore: ".concat(i)),m&&m(i)}finally{N(!1)}};if(!l)return null;let R="Installato"===l.stato_installazione||"INSTALLATO"===l.stato_installazione||"POSATO"===l.stato_installazione||(l.metri_posati||l.metratura_reale||0)>0,P=k(),B=l.responsabile_partenza&&l.responsabile_arrivo,M=!!l&&(!0===l.certificato||"SI"===l.certificato||"CERTIFICATO"===l.certificato);return(0,s.jsxs)(X.lG,{open:a,onOpenChange:i,children:[(0,s.jsx)("div",{"aria-live":"polite","aria-atomic":"true",className:"sr-only",children:A}),(0,s.jsxs)(X.Cf,{className:"sm:max-w-[700px] max-h-[90vh] overflow-y-auto","aria-describedby":"certificazione-description",children:[(0,s.jsxs)(X.c7,{children:[(0,s.jsxs)(X.L3,{className:"flex items-center gap-2 text-blue-600",children:[(0,s.jsx)(eF.A,{className:"h-5 w-5"}),"Gestione Certificazione - ",l.id_cavo]}),(0,s.jsxs)(X.rr,{id:"certificazione-description",children:["Certifica il cavo ",l.id_cavo," secondo normativa CEI 64-8 o genera il PDF del certificato"]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-200",children:(0,s.jsxs)("div",{className:"text-sm font-medium text-blue-800",children:["Informazioni Cavo / Tipologia: ",l.tipologia||"N/A"," / Da: ",l.ubicazione_partenza||"N/A"," / Formazione: ",l.sezione||"N/A"," / A: ",l.ubicazione_arrivo||"N/A"," / Metri Posati: ",l.metratura_reale||0," m"]})}),(0,s.jsxs)("div",{className:"p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border",children:[(0,s.jsx)(T.J,{className:"text-sm font-medium text-gray-700",children:"Stato Cavo"}),(0,s.jsxs)("div",{className:"mt-2 space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[R?(0,s.jsx)(L.A,{className:"w-4 h-4 text-green-600"}):(0,s.jsx)(eA.A,{className:"w-4 h-4 text-red-500"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:R?"Installato/Posato":"Non installato"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[P?(0,s.jsx)(L.A,{className:"w-4 h-4 text-green-600"}):(0,s.jsx)(eA.A,{className:"w-4 h-4 text-orange-500"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:P?"Collegato":"Non collegato"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[M?(0,s.jsx)(L.A,{className:"w-4 h-4 text-green-600"}):(0,s.jsx)(eA.A,{className:"w-4 h-4 text-gray-400"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:M?"Certificato":"Non certificato"})]})]})]}),!R&&(0,s.jsxs)(o.Fc,{className:"border-red-200 bg-red-50",children:[(0,s.jsx)(eA.A,{className:"h-4 w-4 text-red-600"}),(0,s.jsxs)(o.TN,{className:"text-red-800",children:[(0,s.jsx)("strong",{children:"ATTENZIONE:"})," Il cavo deve essere installato/posato prima di poter essere certificato secondo CEI 64-8."]})]}),R&&!(P&&B)&&(0,s.jsxs)(o.Fc,{className:"border-amber-200 bg-amber-50",children:[(0,s.jsx)(eA.A,{className:"h-4 w-4 text-amber-600"}),(0,s.jsxs)(o.TN,{className:"text-amber-800",children:[(0,s.jsx)("strong",{children:"ATTENZIONE:"}),' Il cavo non risulta completamente collegato. Durante la certificazione sar\xe0 possibile collegarlo automaticamente a "cantiere" su entrambi i lati.']})]}),z&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(eA.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:z})]}),M?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)(o.Fc,{className:"border-green-200 bg-green-50",children:[(0,s.jsx)(eF.A,{className:"h-4 w-4 text-green-600"}),(0,s.jsxs)(o.TN,{className:"text-green-800",children:[(0,s.jsx)("strong",{children:"CERTIFICATO:"})," Questo cavo \xe8 gi\xe0 stato certificato. Puoi generare il PDF del certificato."]})]}),(0,s.jsxs)(n.$,{onClick:D,disabled:f,className:"w-full bg-green-600 hover:bg-green-700 text-white",size:"lg",children:[f?(0,s.jsx)(ei.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,s.jsx)(H.A,{className:"h-4 w-4 mr-2"}),"Genera PDF Certificato"]})]}):R&&(0,s.jsxs)("div",{className:"space-y-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border-2 border-blue-200",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-blue-800 mb-4",children:"\uD83D\uDCCB Dati Certificazione CEI 64-8"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"responsabile",className:"text-sm font-medium text-gray-700",children:"Responsabile Certificazione *"}),(0,s.jsxs)(p.l6,{value:v.responsabile_certificazione,onValueChange:e=>g(a=>({...a,responsabile_certificazione:e})),disabled:_,children:[(0,s.jsx)(p.bq,{className:"border-2 border-gray-300 focus:border-blue-500",children:(0,s.jsx)(p.yv,{placeholder:"Seleziona responsabile..."})}),(0,s.jsx)(p.gC,{children:b.map(e=>(0,s.jsxs)(p.eb,{value:e.nome_responsabile,children:[e.nome_responsabile,e.numero_telefono&&" - ".concat(e.numero_telefono)]},e.id))})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"data",className:"text-sm font-medium text-gray-700",children:"Data Certificazione"}),(0,s.jsx)(h.p,{id:"data",type:"date",value:v.data_certificazione,onChange:e=>g(a=>({...a,data_certificazione:e.target.value})),className:"border-2 border-gray-300 focus:border-blue-500"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"esito",className:"text-sm font-medium text-gray-700",children:"Esito Certificazione"}),(0,s.jsxs)(p.l6,{value:v.esito_certificazione,onValueChange:e=>g(a=>({...a,esito_certificazione:e})),children:[(0,s.jsx)(p.bq,{className:"border-2 border-gray-300 focus:border-blue-500",children:(0,s.jsx)(p.yv,{})}),(0,s.jsxs)(p.gC,{children:[(0,s.jsx)(p.eb,{value:"CONFORME",children:"✅ CONFORME"}),(0,s.jsx)(p.eb,{value:"NON_CONFORME",children:"❌ NON CONFORME"}),(0,s.jsx)(p.eb,{value:"CONFORME_CON_RISERVA",children:"⚠️ CONFORME CON RISERVA"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"note",className:"text-sm font-medium text-gray-700",children:"Note (opzionale)"}),(0,s.jsx)(K.T,{id:"note",placeholder:"Inserisci eventuali note sulla certificazione...",value:v.note_certificazione,onChange:e=>g(a=>({...a,note_certificazione:e.target.value})),rows:3,className:"border-2 border-gray-300 focus:border-blue-500"})]}),(0,s.jsxs)(n.$,{onClick:O,disabled:f||!v.responsabile_certificazione,className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3",size:"lg",children:[f?(0,s.jsx)(ei.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,s.jsx)(eF.A,{className:"h-4 w-4 mr-2"}),"Certifica Cavo CEI 64-8"]})]})]}),(0,s.jsx)(X.Es,{className:"border-t pt-4",children:(0,s.jsxs)(n.$,{variant:"outline",onClick:i,disabled:f,className:"flex items-center gap-2",children:[(0,s.jsx)(C.A,{className:"h-4 w-4"}),"Chiudi"]})})]})]})}var eR=i(25273);function eP(e){let{open:a,onClose:i,caviSelezionati:l,tipoComanda:r,onSuccess:m,onError:x}=e,{cantiere:u}=(0,c.A)(),[h,v]=(0,t.useState)({tipo_comanda:r||"POSA",responsabile:"",note:""}),[g,b]=(0,t.useState)([]),[j,f]=(0,t.useState)(!1),[N,_]=(0,t.useState)(!1),[y,C]=(0,t.useState)("");(0,t.useEffect)(()=>{a&&(v({tipo_comanda:r||"POSA",responsabile:"",note:""}),C(""),z())},[a,r]);let z=async()=>{if(u)try{_(!0);let e=await d.AR.getResponsabili(u.id_cantiere);b(e.data)}catch(e){b([])}finally{_(!1)}},w=async()=>{if(u){if(!h.responsabile)return void C("Seleziona un responsabile per la comanda");if(0===l.length)return void C("Seleziona almeno un cavo per la comanda");try{f(!0),C("");let e={tipo_comanda:h.tipo_comanda,responsabile:h.responsabile,note:h.note||null},a=await d.CV.createComandaWithCavi(u.id_cantiere,e,l);m("Comanda ".concat(a.data.codice_comanda," creata con successo per ").concat(l.length," cavi")),i()}catch(i){var e,a;x((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante la creazione della comanda")}finally{f(!1)}}};return(0,s.jsx)(X.lG,{open:a,onOpenChange:i,children:(0,s.jsxs)(X.Cf,{className:"sm:max-w-[600px]",children:[(0,s.jsxs)(X.c7,{children:[(0,s.jsxs)(X.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(eR.A,{className:"h-5 w-5"}),"Crea Nuova Comanda"]}),(0,s.jsxs)(X.rr,{children:["Crea una nuova comanda per ",l.length," cavi selezionati"]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,s.jsxs)(T.J,{className:"text-sm font-medium",children:["Cavi Selezionati (",l.length,")"]}),(0,s.jsx)("div",{className:"mt-2 max-h-32 overflow-y-auto",children:(0,s.jsxs)("div",{className:"flex flex-wrap gap-1",children:[l.slice(0,10).map(e=>(0,s.jsx)("span",{className:"inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded",children:e},e)),l.length>10&&(0,s.jsxs)("span",{className:"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded",children:["+",l.length-10," altri..."]})]})})]}),y&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(eA.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:y})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"tipo",children:"Tipo Comanda *"}),(0,s.jsxs)(p.l6,{value:h.tipo_comanda,onValueChange:e=>v(a=>({...a,tipo_comanda:e})),children:[(0,s.jsx)(p.bq,{children:(0,s.jsx)(p.yv,{})}),(0,s.jsxs)(p.gC,{children:[(0,s.jsx)(p.eb,{value:"POSA",children:"\uD83D\uDD27 Posa Cavi"}),(0,s.jsx)(p.eb,{value:"COLLEGAMENTO_PARTENZA",children:"\uD83D\uDD0C Collegamento Partenza"}),(0,s.jsx)(p.eb,{value:"COLLEGAMENTO_ARRIVO",children:"⚡ Collegamento Arrivo"}),(0,s.jsx)(p.eb,{value:"CERTIFICAZIONE",children:"\uD83D\uDCCB Certificazione"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"responsabile",children:"Responsabile *"}),(0,s.jsxs)(p.l6,{value:h.responsabile,onValueChange:e=>v(a=>({...a,responsabile:e})),disabled:N,children:[(0,s.jsx)(p.bq,{children:(0,s.jsx)(p.yv,{placeholder:"Seleziona responsabile..."})}),(0,s.jsx)(p.gC,{children:g.map(e=>(0,s.jsx)(p.eb,{value:e.nome_responsabile,children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(W.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:e.nome_responsabile}),e.numero_telefono&&(0,s.jsxs)("span",{className:"text-xs text-gray-500",children:["- ",e.numero_telefono]})]})},e.id))})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"note",children:"Note (opzionale)"}),(0,s.jsx)(K.T,{id:"note",placeholder:"Inserisci eventuali note per la comanda...",value:h.note,onChange:e=>v(a=>({...a,note:e.target.value})),rows:3})]}),(0,s.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,s.jsx)(T.J,{className:"text-sm font-medium",children:"Riepilogo Comanda"}),(0,s.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Tipo:"})," ",(e=>{switch(e){case"POSA":return"Posa Cavi";case"COLLEGAMENTO_PARTENZA":return"Collegamento Partenza";case"COLLEGAMENTO_ARRIVO":return"Collegamento Arrivo";case"CERTIFICAZIONE":return"Certificazione";default:return e}})(h.tipo_comanda)]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Responsabile:"})," ",h.responsabile||"Non selezionato"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Cavi:"})," ",l.length," selezionati"]}),h.note&&(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Note:"})," ",h.note]})]})]})]}),(0,s.jsxs)(X.Es,{children:[(0,s.jsx)(n.$,{variant:"outline",onClick:i,disabled:j,children:"Annulla"}),(0,s.jsxs)(n.$,{onClick:w,disabled:j||!h.responsabile||0===l.length,children:[j?(0,s.jsx)(ei.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,s.jsx)(eR.A,{className:"h-4 w-4 mr-2"}),"Crea Comanda"]})]})]})})}var eL=i(29869),eB=i(64261);function eM(e){let{open:a,onClose:i,tipo:l,onSuccess:r,onError:m}=e,{cantiere:x}=(0,c.A)(),[u,p]=(0,t.useState)(null),[v,g]=(0,t.useState)(""),[b,j]=(0,t.useState)(!1),[f,N]=(0,t.useState)(""),[_,y]=(0,t.useState)(0),C=(0,t.useRef)(null),z=async()=>{if(u&&x){if("cavi"===l&&!v.trim())return void N("Inserisci il codice revisione per l'importazione cavi");try{let e;if(j(!0),N(""),y(0),e="cavi"===l?await d.mg.importCavi(x.id_cantiere,u,v.trim()):await d.mg.importBobine(x.id_cantiere,u),y(100),e.data.success){let a=e.data.details,s=e.data.message;"cavi"===l&&(null==a?void 0:a.cavi_importati)?s+=" (".concat(a.cavi_importati," cavi importati)"):"bobine"===l&&(null==a?void 0:a.bobine_importate)&&(s+=" (".concat(a.bobine_importate," bobine importate)")),r(s),i()}else m(e.data.message||"Errore durante l'importazione")}catch(i){var e,a;m((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante l'importazione del file")}finally{j(!1),y(0)}}},w=()=>{b||(p(null),g(""),N(""),y(0),C.current&&(C.current.value=""),i())},A=()=>"cavi"===l?"Cavi":"Bobine";return(0,s.jsx)(X.lG,{open:a,onOpenChange:w,children:(0,s.jsxs)(X.Cf,{className:"sm:max-w-[600px]",children:[(0,s.jsxs)(X.c7,{children:[(0,s.jsxs)(X.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(eL.A,{className:"h-5 w-5"}),"Importa ",A()," da Excel"]}),(0,s.jsxs)(X.rr,{children:["Carica un file Excel per importare ",A().toLowerCase()," nel cantiere"]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,s.jsx)(T.J,{className:"text-sm font-medium",children:"Requisiti File"}),(0,s.jsx)("ul",{className:"mt-2 space-y-1 text-sm text-gray-600",children:("cavi"===l?["File Excel (.xlsx o .xls)","Colonne richieste: ID_CAVO, SISTEMA, UTILITY, TIPOLOGIA, ecc.","Prima riga deve contenere le intestazioni","Codice revisione obbligatorio per tracciabilit\xe0"]:["File Excel (.xlsx o .xls)","Colonne richieste: NUMERO_BOBINA, UTILITY, TIPOLOGIA, METRI_TOTALI, ecc.","Prima riga deve contenere le intestazioni","I metri residui saranno impostati uguali ai metri totali"]).map((e,a)=>(0,s.jsxs)("li",{className:"flex items-start gap-2",children:[(0,s.jsx)("span",{className:"text-blue-500 mt-0.5",children:"•"}),(0,s.jsx)("span",{children:e})]},a))})]}),f&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(eA.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:f})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"file",children:"File Excel *"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(h.p,{ref:C,id:"file",type:"file",accept:".xlsx,.xls",onChange:e=>{var a;let i=null==(a=e.target.files)?void 0:a[0];if(i){if(!["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel"].includes(i.type)&&!i.name.toLowerCase().endsWith(".xlsx")&&!i.name.toLowerCase().endsWith(".xls"))return void N("Seleziona un file Excel valido (.xlsx o .xls)");p(i),N("")}},disabled:b,className:"flex-1"}),u&&(0,s.jsxs)("div",{className:"flex items-center gap-1 text-green-600",children:[(0,s.jsx)(L.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"text-sm",children:"File selezionato"})]})]}),u&&(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,s.jsx)(eB.A,{className:"h-4 w-4 inline mr-1"}),u.name," (",(u.size/1024/1024).toFixed(2)," MB)"]})]}),"cavi"===l&&(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"revisione",children:"Codice Revisione *"}),(0,s.jsx)(h.p,{id:"revisione",value:v,onChange:e=>g(e.target.value),placeholder:"es. REV001, V1.0, 2024-01",disabled:b}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Codice identificativo della revisione per tracciabilit\xe0 delle modifiche"})]}),b&&(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(ei.A,{className:"h-4 w-4 animate-spin"}),(0,s.jsx)("span",{className:"text-sm",children:"Caricamento in corso..."})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(_,"%")}})})]}),u&&(0,s.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,s.jsx)(T.J,{className:"text-sm font-medium",children:"Riepilogo Importazione"}),(0,s.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Tipo:"})," ",A()]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"File:"})," ",u.name]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Dimensione:"})," ",(u.size/1024/1024).toFixed(2)," MB"]}),"cavi"===l&&v&&(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Revisione:"})," ",v]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Cantiere:"})," ",null==x?void 0:x.nome_cantiere]})]})]})]}),(0,s.jsxs)(X.Es,{children:[(0,s.jsx)(n.$,{variant:"outline",onClick:w,disabled:b,children:"Annulla"}),(0,s.jsxs)(n.$,{onClick:z,disabled:b||!u||"cavi"===l&&!v.trim(),children:[b?(0,s.jsx)(ei.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,s.jsx)(eL.A,{className:"h-4 w-4 mr-2"}),"Importa ",A()]})]})]})})}var e$=i(54213);function eU(e){let{open:a,onClose:i,onSuccess:l,onError:r}=e,{cantiere:m}=(0,c.A)(),[u,h]=(0,t.useState)({cavi:!0,bobine:!0,comande:!1,certificazioni:!1,responsabili:!1}),[p,v]=(0,t.useState)(!1),[g,b]=(0,t.useState)(""),j=(e,a)=>{h(i=>({...i,[e]:a}))},f=async()=>{if(m)try{v(!0);let e=await d.mg.exportCavi(m.id_cantiere),a=window.URL.createObjectURL(new Blob([e.data])),i=document.createElement("a");i.href=a,i.setAttribute("download","cavi_".concat(m.nome_cantiere,"_").concat(new Date().toISOString().split("T")[0],".xlsx")),document.body.appendChild(i),i.click(),i.remove(),window.URL.revokeObjectURL(a),l("Export cavi completato con successo")}catch(i){var e,a;r((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante l'export dei cavi")}finally{v(!1)}},N=async()=>{if(m)try{v(!0);let e=await d.mg.exportBobine(m.id_cantiere),a=window.URL.createObjectURL(new Blob([e.data])),i=document.createElement("a");i.href=a,i.setAttribute("download","bobine_".concat(m.nome_cantiere,"_").concat(new Date().toISOString().split("T")[0],".xlsx")),document.body.appendChild(i),i.click(),i.remove(),window.URL.revokeObjectURL(a),l("Export bobine completato con successo")}catch(i){var e,a;r((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante l'export delle bobine")}finally{v(!1)}},_=async()=>{if(m)try{v(!0),b("");let e=[];u.cavi&&e.push(f()),u.bobine&&e.push(N()),u.comande,u.certificazioni,u.responsabili,await Promise.all(e);let a=Object.values(u).filter(Boolean).length;l("Export completato: ".concat(a," file scaricati")),i()}catch(i){var e,a;r((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante l'export dei dati")}finally{v(!1)}},y=[{key:"cavi",label:"Cavi",description:"Esporta tutti i cavi del cantiere con stato, collegamenti e certificazioni",icon:(0,s.jsx)(e$.A,{className:"h-4 w-4"}),available:!0},{key:"bobine",label:"Bobine",description:"Esporta tutte le bobine del parco cavi con metri residui e assegnazioni",icon:(0,s.jsx)(eB.A,{className:"h-4 w-4"}),available:!0},{key:"comande",label:"Comande",description:"Esporta tutte le comande con cavi assegnati e responsabili",icon:(0,s.jsx)(eB.A,{className:"h-4 w-4"}),available:!1},{key:"certificazioni",label:"Certificazioni",description:"Esporta tutte le certificazioni con esiti e responsabili",icon:(0,s.jsx)(eB.A,{className:"h-4 w-4"}),available:!1},{key:"responsabili",label:"Responsabili",description:"Esporta tutti i responsabili con contatti e ruoli",icon:(0,s.jsx)(eB.A,{className:"h-4 w-4"}),available:!1}];return(0,s.jsx)(X.lG,{open:a,onOpenChange:i,children:(0,s.jsxs)(X.Cf,{className:"sm:max-w-[600px]",children:[(0,s.jsxs)(X.c7,{children:[(0,s.jsxs)(X.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(H.A,{className:"h-5 w-5"}),"Esporta Dati Cantiere"]}),(0,s.jsxs)(X.rr,{children:["Seleziona i dati da esportare dal cantiere ",null==m?void 0:m.nome_cantiere]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[g&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(eA.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:g})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(T.J,{className:"text-sm font-medium",children:"Seleziona Dati da Esportare"}),y.map(e=>(0,s.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg border ".concat(e.available?"bg-white":"bg-gray-50"),children:[(0,s.jsx)(x.S,{id:e.key,checked:u[e.key],onCheckedChange:a=>j(e.key,a),disabled:!e.available||p}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,(0,s.jsxs)(T.J,{htmlFor:e.key,className:"font-medium ".concat(e.available?"":"text-gray-500"),children:[e.label,!e.available&&(0,s.jsx)("span",{className:"ml-2 text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded",children:"In sviluppo"})]})]}),(0,s.jsx)("p",{className:"text-sm mt-1 ".concat(e.available?"text-gray-600":"text-gray-400"),children:e.description})]})]},e.key))]}),(0,s.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,s.jsx)(T.J,{className:"text-sm font-medium",children:"Informazioni Export"}),(0,s.jsxs)("ul",{className:"mt-2 space-y-1 text-sm text-gray-600",children:[(0,s.jsx)("li",{children:"• I file saranno scaricati in formato Excel (.xlsx)"}),(0,s.jsx)("li",{children:"• I nomi file includeranno data e nome cantiere"}),(0,s.jsx)("li",{children:"• I dati esportati riflettono lo stato attuale del database"}),(0,s.jsx)("li",{children:"• L'export non modifica i dati originali"})]})]}),Object.values(u).filter(Boolean).length>0&&(0,s.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,s.jsx)(T.J,{className:"text-sm font-medium",children:"Riepilogo Export"}),(0,s.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Cantiere:"})," ",null==m?void 0:m.nome_cantiere]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"File da scaricare:"})," ",Object.values(u).filter(Boolean).length]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Data export:"})," ",new Date().toLocaleDateString("it-IT")]})]})]})]}),(0,s.jsxs)(X.Es,{children:[(0,s.jsx)(n.$,{variant:"outline",onClick:i,disabled:p,children:"Annulla"}),(0,s.jsxs)(n.$,{onClick:_,disabled:p||0===Object.values(u).filter(Boolean).length,children:[p?(0,s.jsx)(ei.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,s.jsx)(H.A,{className:"h-4 w-4 mr-2"}),"Esporta ",Object.values(u).filter(Boolean).length>0?"(".concat(Object.values(u).filter(Boolean).length,")"):""]})]})]})})}var eJ=i(84616);let eV={id_cavo:"",utility:"",sistema:"",colore_cavo:"",tipologia:"",sezione:"",ubicazione_partenza:"",utenza_partenza:"",descrizione_utenza_partenza:"",ubicazione_arrivo:"",utenza_arrivo:"",descrizione_utenza_arrivo:"",metri_teorici:""};function eG(e){let{open:a,onClose:i,cantiere:l,onSuccess:r,onError:m}=e,{cantiere:x}=(0,c.A)(),u=l||x,[p,v]=(0,t.useState)(eV),[g,b]=(0,t.useState)(!1),[j,f]=(0,t.useState)({});(0,t.useEffect)(()=>{a&&(v(eV),f({}))},[a]);let N=(e,a)=>{v(i=>({...i,[e]:"id_cavo"===e?a.toUpperCase():a})),j[e]&&f(a=>{let i={...a};return delete i[e],i})},_=()=>{let e={};if(p.id_cavo.trim()||(e.id_cavo="ID Cavo \xe8 obbligatorio"),p.utility.trim()||(e.utility="Utility \xe8 obbligatoria"),p.metri_teorici.trim()){let a=parseFloat(p.metri_teorici);(isNaN(a)||a<=0)&&(e.metri_teorici="Metri Teorici deve essere un numero positivo")}else e.metri_teorici="Metri Teorici sono obbligatori";return f(e),0===Object.keys(e).length},y=async()=>{if(_()){if(!(null==u?void 0:u.id_cantiere))return void m("Cantiere non selezionato");try{b(!0);let e={...p,metri_teorici:parseFloat(p.metri_teorici),id_cantiere:u.id_cantiere};await d.At.createCavo(parseInt(u.id_cantiere),e),r("Cavo ".concat(p.id_cavo," aggiunto con successo")),C()}catch(i){var e,a;m((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante l'aggiunta del cavo")}finally{b(!1)}}},C=()=>{g||(v(eV),f({}),i())};return(0,s.jsx)(X.lG,{open:a,onOpenChange:C,children:(0,s.jsxs)(X.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,s.jsxs)(X.c7,{children:[(0,s.jsxs)(X.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(eJ.A,{className:"h-5 w-5"}),"Aggiungi Nuovo Cavo"]}),(0,s.jsxs)(X.rr,{children:["Inserisci i dati del nuovo cavo per il cantiere ",null==u?void 0:u.nome_cantiere]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Informazioni Generali"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"id_cavo",children:"ID Cavo *"}),(0,s.jsx)(h.p,{id:"id_cavo",value:p.id_cavo,onChange:e=>N("id_cavo",e.target.value),placeholder:"Es. C001",className:j.id_cavo?"border-red-500":""}),j.id_cavo&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:j.id_cavo})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"utility",children:"Utility *"}),(0,s.jsx)(h.p,{id:"utility",value:p.utility,onChange:e=>N("utility",e.target.value),placeholder:"Es. ENEL",className:j.utility?"border-red-500":""}),j.utility&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:j.utility})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"sistema",children:"Sistema"}),(0,s.jsx)(h.p,{id:"sistema",value:p.sistema,onChange:e=>N("sistema",e.target.value),placeholder:"Es. MT"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Caratteristiche Tecniche"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"colore_cavo",children:"Colore Cavo"}),(0,s.jsx)(h.p,{id:"colore_cavo",value:p.colore_cavo,onChange:e=>N("colore_cavo",e.target.value),placeholder:"Es. Nero"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"tipologia",children:"Tipologia"}),(0,s.jsx)(h.p,{id:"tipologia",value:p.tipologia,onChange:e=>N("tipologia",e.target.value),placeholder:"Es. ARE4H5E"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"sezione",children:"Formazione"}),(0,s.jsx)(h.p,{id:"sezione",value:p.sezione,onChange:e=>N("sezione",e.target.value),placeholder:"Es. 3X240+120"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Partenza"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"ubicazione_partenza",children:"Ubicazione Partenza"}),(0,s.jsx)(h.p,{id:"ubicazione_partenza",value:p.ubicazione_partenza,onChange:e=>N("ubicazione_partenza",e.target.value)})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"utenza_partenza",children:"Utenza Partenza"}),(0,s.jsx)(h.p,{id:"utenza_partenza",value:p.utenza_partenza,onChange:e=>N("utenza_partenza",e.target.value)})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"descrizione_utenza_partenza",children:"Descrizione Utenza Partenza"}),(0,s.jsx)(h.p,{id:"descrizione_utenza_partenza",value:p.descrizione_utenza_partenza,onChange:e=>N("descrizione_utenza_partenza",e.target.value)})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Arrivo"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"ubicazione_arrivo",children:"Ubicazione Arrivo"}),(0,s.jsx)(h.p,{id:"ubicazione_arrivo",value:p.ubicazione_arrivo,onChange:e=>N("ubicazione_arrivo",e.target.value)})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"utenza_arrivo",children:"Utenza Arrivo"}),(0,s.jsx)(h.p,{id:"utenza_arrivo",value:p.utenza_arrivo,onChange:e=>N("utenza_arrivo",e.target.value)})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"descrizione_utenza_arrivo",children:"Descrizione Utenza Arrivo"}),(0,s.jsx)(h.p,{id:"descrizione_utenza_arrivo",value:p.descrizione_utenza_arrivo,onChange:e=>N("descrizione_utenza_arrivo",e.target.value)})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Metratura"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"metri_teorici",children:"Metri Teorici *"}),(0,s.jsx)(h.p,{id:"metri_teorici",type:"number",step:"0.01",min:"0",value:p.metri_teorici,onChange:e=>N("metri_teorici",e.target.value),placeholder:"Es. 100.50",className:j.metri_teorici?"border-red-500":""}),j.metri_teorici&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:j.metri_teorici})]})})]}),Object.keys(j).length>0&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(eA.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:"Correggere i campi evidenziati in rosso prima di salvare."})]})]}),(0,s.jsxs)(X.Es,{children:[(0,s.jsx)(n.$,{variant:"outline",onClick:C,disabled:g,children:"Annulla"}),(0,s.jsxs)(n.$,{onClick:y,disabled:g,children:[g&&(0,s.jsx)(ei.A,{className:"mr-2 h-4 w-4 animate-spin"}),g?"Aggiungendo...":"Aggiungi Cavo"]})]})]})})}var eZ=i(13717);function eq(e){let{open:a,onClose:i,cavo:l,cantiere:r,onSuccess:m,onError:x}=e,{cantiere:u}=(0,c.A)(),p=r||u,[v,g]=(0,t.useState)({id_cavo:"",utility:"",sistema:"",colore_cavo:"",tipologia:"",sezione:"",ubicazione_partenza:"",utenza_partenza:"",descrizione_utenza_partenza:"",ubicazione_arrivo:"",utenza_arrivo:"",descrizione_utenza_arrivo:"",metri_teorici:""}),[b,j]=(0,t.useState)(!1),[f,N]=(0,t.useState)({});(0,t.useEffect)(()=>{if(a&&l){var e;g({id_cavo:l.id_cavo||"",utility:l.utility||"",sistema:l.sistema||"",colore_cavo:l.colore_cavo||"",tipologia:l.tipologia||"",sezione:l.sezione||"",ubicazione_partenza:l.ubicazione_partenza||"",utenza_partenza:l.utenza_partenza||"",descrizione_utenza_partenza:l.descrizione_utenza_partenza||"",ubicazione_arrivo:l.ubicazione_arrivo||"",utenza_arrivo:l.utenza_arrivo||"",descrizione_utenza_arrivo:l.descrizione_utenza_arrivo||"",metri_teorici:(null==(e=l.metri_teorici)?void 0:e.toString())||""}),N({})}},[a,l]);let _=(e,a)=>{g(i=>({...i,[e]:"id_cavo"===e?a.toUpperCase():a})),f[e]&&N(a=>{let i={...a};return delete i[e],i})},y=()=>{let e={};if(v.id_cavo.trim()||(e.id_cavo="ID Cavo \xe8 obbligatorio"),v.utility.trim()||(e.utility="Utility \xe8 obbligatoria"),v.metri_teorici.trim()){let a=parseFloat(v.metri_teorici);(isNaN(a)||a<=0)&&(e.metri_teorici="Metri Teorici deve essere un numero positivo")}else e.metri_teorici="Metri Teorici sono obbligatori";return N(e),0===Object.keys(e).length},C=async()=>{if(y()){if(!(null==l?void 0:l.id_cavo))return void x("Cavo non selezionato");if(!(null==p?void 0:p.id_cantiere))return void x("Cantiere non selezionato");try{j(!0);let e={...v,metri_teorici:parseFloat(v.metri_teorici),id_cantiere:p.id_cantiere};await d.At.updateCavo(parseInt(p.id_cantiere),l.id_cavo,e),m("Cavo ".concat(v.id_cavo," modificato con successo")),z()}catch(i){var e,a;x((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante la modifica del cavo")}finally{j(!1)}}},z=()=>{b||(N({}),i())},w=l&&(l.metratura_reale>0||"da installare"!==l.stato_installazione);return l?(0,s.jsx)(X.lG,{open:a,onOpenChange:z,children:(0,s.jsxs)(X.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,s.jsxs)(X.c7,{children:[(0,s.jsxs)(X.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(eZ.A,{className:"h-5 w-5"}),"Modifica Cavo: ",l.id_cavo]}),(0,s.jsxs)(X.rr,{children:["Modifica i dati del cavo nel cantiere ",null==p?void 0:p.nome_cantiere]})]}),w&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(es.A,{className:"h-4 w-4"}),(0,s.jsxs)(o.TN,{children:[(0,s.jsx)("strong",{children:"Attenzione:"})," Questo cavo \xe8 gi\xe0 stato installato. La modifica potrebbe influire sui dati di installazione esistenti."]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Informazioni Generali"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"id_cavo",children:"ID Cavo *"}),(0,s.jsx)(h.p,{id:"id_cavo",value:v.id_cavo,onChange:e=>_("id_cavo",e.target.value),placeholder:"Es. C001",className:f.id_cavo?"border-red-500":""}),f.id_cavo&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:f.id_cavo})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"utility",children:"Utility *"}),(0,s.jsx)(h.p,{id:"utility",value:v.utility,onChange:e=>_("utility",e.target.value),placeholder:"Es. ENEL",className:f.utility?"border-red-500":""}),f.utility&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:f.utility})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"sistema",children:"Sistema"}),(0,s.jsx)(h.p,{id:"sistema",value:v.sistema,onChange:e=>_("sistema",e.target.value),placeholder:"Es. MT"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Caratteristiche Tecniche"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"colore_cavo",children:"Colore Cavo"}),(0,s.jsx)(h.p,{id:"colore_cavo",value:v.colore_cavo,onChange:e=>_("colore_cavo",e.target.value),placeholder:"Es. Nero"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"tipologia",children:"Tipologia"}),(0,s.jsx)(h.p,{id:"tipologia",value:v.tipologia,onChange:e=>_("tipologia",e.target.value),placeholder:"Es. ARE4H5E"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"sezione",children:"Formazione"}),(0,s.jsx)(h.p,{id:"sezione",value:v.sezione,onChange:e=>_("sezione",e.target.value),placeholder:"Es. 3X240+120"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Partenza"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"ubicazione_partenza",children:"Ubicazione Partenza"}),(0,s.jsx)(h.p,{id:"ubicazione_partenza",value:v.ubicazione_partenza,onChange:e=>_("ubicazione_partenza",e.target.value)})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"utenza_partenza",children:"Utenza Partenza"}),(0,s.jsx)(h.p,{id:"utenza_partenza",value:v.utenza_partenza,onChange:e=>_("utenza_partenza",e.target.value)})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"descrizione_utenza_partenza",children:"Descrizione Utenza Partenza"}),(0,s.jsx)(h.p,{id:"descrizione_utenza_partenza",value:v.descrizione_utenza_partenza,onChange:e=>_("descrizione_utenza_partenza",e.target.value)})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Arrivo"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"ubicazione_arrivo",children:"Ubicazione Arrivo"}),(0,s.jsx)(h.p,{id:"ubicazione_arrivo",value:v.ubicazione_arrivo,onChange:e=>_("ubicazione_arrivo",e.target.value)})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"utenza_arrivo",children:"Utenza Arrivo"}),(0,s.jsx)(h.p,{id:"utenza_arrivo",value:v.utenza_arrivo,onChange:e=>_("utenza_arrivo",e.target.value)})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"descrizione_utenza_arrivo",children:"Descrizione Utenza Arrivo"}),(0,s.jsx)(h.p,{id:"descrizione_utenza_arrivo",value:v.descrizione_utenza_arrivo,onChange:e=>_("descrizione_utenza_arrivo",e.target.value)})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Metratura"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(T.J,{htmlFor:"metri_teorici",children:"Metri Teorici *"}),(0,s.jsx)(h.p,{id:"metri_teorici",type:"number",step:"0.01",min:"0",value:v.metri_teorici,onChange:e=>_("metri_teorici",e.target.value),placeholder:"Es. 100.50",className:f.metri_teorici?"border-red-500":""}),f.metri_teorici&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:f.metri_teorici})]})})]}),Object.keys(f).length>0&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(eA.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:"Correggere i campi evidenziati in rosso prima di salvare."})]})]}),(0,s.jsxs)(X.Es,{children:[(0,s.jsx)(n.$,{variant:"outline",onClick:z,disabled:b,children:"Annulla"}),(0,s.jsxs)(n.$,{onClick:C,disabled:b,children:[b&&(0,s.jsx)(ei.A,{className:"mr-2 h-4 w-4 animate-spin"}),b?"Salvando...":"Salva Modifiche"]})]})]})}):null}var eH=i(54059),eW=i(9428);let eQ=t.forwardRef((e,a)=>{let{className:i,...t}=e;return(0,s.jsx)(eH.bL,{className:(0,g.cn)("grid gap-2",i),...t,ref:a})});eQ.displayName=eH.bL.displayName;let eY=t.forwardRef((e,a)=>{let{className:i,...t}=e;return(0,s.jsx)(eH.q7,{ref:a,className:(0,g.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",i),...t,children:(0,s.jsx)(eH.C1,{className:"flex items-center justify-center",children:(0,s.jsx)(eW.A,{className:"h-2.5 w-2.5 fill-current text-current"})})})});function eX(e){let{open:a,onClose:i,cavo:l,cantiere:r,onSuccess:m,onError:x}=e,{cantiere:u}=(0,c.A)(),h=r||u,[p,v]=(0,t.useState)(!1),[g,b]=(0,t.useState)("spare"),j=async()=>{if(!(null==l?void 0:l.id_cavo))return void x("Cavo non selezionato");if(!(null==h?void 0:h.id_cantiere))return void x("Cantiere non selezionato");try{if(v(!0),console.log("Iniziando operazione ".concat(g," per cavo ").concat(l.id_cavo)),"spare"===g){console.log("Chiamando markAsSpare API...");let e=await d.At.markAsSpare(parseInt(h.id_cantiere),l.id_cavo,!0);console.log("Risultato markAsSpare:",e),m("Cavo ".concat(l.id_cavo," marcato come SPARE"))}else{console.log("Chiamando deleteCavo API...");let e=await d.At.deleteCavo(parseInt(h.id_cantiere),l.id_cavo);console.log("Risultato deleteCavo:",e),m("Cavo ".concat(l.id_cavo," eliminato definitivamente"))}f()}catch(s){var e,a;console.error("Errore durante operazione:",s);let i=(null==(a=s.response)||null==(e=a.data)?void 0:e.detail)||s.message||"Errore durante l'eliminazione del cavo";console.error("Messaggio errore:",i),x(i)}finally{v(!1)}},f=()=>{p||(b("spare"),i())};if(!l)return null;let N=l.metratura_reale>0||l.stato_installazione&&"da installare"!==l.stato_installazione.toLowerCase(),_=!N,y={id:l.id_cavo,tipologia:l.tipologia||"N/A",sezione:l.sezione||"N/A",metri_teorici:l.metri_teorici||0,metri_reali:l.metratura_reale||0,stato:l.stato_installazione||"N/A",bobina:l.id_bobina||"N/A"};return(0,s.jsx)(X.lG,{open:a,onOpenChange:f,children:(0,s.jsxs)(X.Cf,{className:"max-w-2xl",children:[(0,s.jsxs)(X.c7,{children:[(0,s.jsxs)(X.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(Y.A,{className:"h-5 w-5"}),"Elimina Cavo: ",l.id_cavo]}),(0,s.jsxs)(X.rr,{children:["Scegli come gestire l'eliminazione del cavo dal cantiere ",null==h?void 0:h.nome_cantiere]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Informazioni Cavo"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"ID:"}),(0,s.jsx)("span",{className:"ml-2 font-medium",children:y.id})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Tipologia:"}),(0,s.jsx)("span",{className:"ml-2 font-medium",children:y.tipologia})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Formazione:"}),(0,s.jsx)("span",{className:"ml-2 font-medium",children:y.sezione})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Stato:"}),(0,s.jsx)("span",{className:"ml-2 font-medium",children:y.stato})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Metri Teorici:"}),(0,s.jsx)("span",{className:"ml-2 font-medium",children:y.metri_teorici})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Metri Installati:"}),(0,s.jsx)("span",{className:"ml-2 font-medium",children:y.metri_reali})]}),(0,s.jsxs)("div",{className:"col-span-2",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Bobina:"}),(0,s.jsx)("span",{className:"ml-2 font-medium",children:y.bobina})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Modalit\xe0 di Eliminazione"}),(0,s.jsxs)(eQ,{value:g,onValueChange:e=>b(e),children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(eY,{value:"spare",id:"spare"}),(0,s.jsxs)(T.J,{htmlFor:"spare",className:"flex-1",children:[(0,s.jsx)("div",{className:"font-medium",children:"Marca come SPARE"}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Il cavo viene marcato come spare/consumato ma rimane nel database per tracciabilit\xe0"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(eY,{value:"permanent",id:"permanent",disabled:!_}),(0,s.jsxs)(T.J,{htmlFor:"permanent",className:"flex-1 ".concat(_?"":"opacity-50"),children:[(0,s.jsx)("div",{className:"font-medium",children:"Eliminazione Definitiva"}),(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:["Il cavo viene rimosso completamente dal database",!_&&" (non disponibile per cavi installati)"]})]})]})]})]}),N&&(0,s.jsxs)(o.Fc,{children:[(0,s.jsx)(G.A,{className:"h-4 w-4"}),(0,s.jsxs)(o.TN,{children:[(0,s.jsx)("strong",{children:"Cavo Installato:"})," Questo cavo ha metri installati. Si consiglia di marcarlo come SPARE piuttosto che eliminarlo definitivamente."]})]}),"permanent"===g&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(es.A,{className:"h-4 w-4"}),(0,s.jsxs)(o.TN,{children:[(0,s.jsx)("strong",{children:"Attenzione:"})," L'eliminazione definitiva \xe8 irreversibile. Tutti i dati del cavo verranno persi permanentemente."]})]}),"spare"===g&&(0,s.jsxs)(o.Fc,{children:[(0,s.jsx)(G.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:"Il cavo verr\xe0 marcato come SPARE e non apparir\xe0 pi\xf9 nelle liste attive, ma rimarr\xe0 nel database per eventuali controlli futuri."})]})]}),(0,s.jsxs)(X.Es,{children:[(0,s.jsx)(n.$,{variant:"outline",onClick:f,disabled:p,children:"Annulla"}),(0,s.jsxs)(n.$,{variant:"destructive",onClick:j,disabled:p,children:[p&&(0,s.jsx)(ei.A,{className:"mr-2 h-4 w-4 animate-spin"}),p?"Eliminando...":"spare"===g?"Marca come SPARE":"Elimina Definitivamente"]})]})]})})}function eK(){let{user:e,cantiere:a,isAuthenticated:i,isLoading:m}=(0,c.A)(),x=(0,l.useRouter)(),u=(0,ef.E)();console.log({cantiereFromAuth:a,user:e,userRole:null==e?void 0:e.ruolo});let[h,p]=(0,t.useState)([]),[v,g]=(0,t.useState)([]),[b,j]=(0,t.useState)(!0),[f,N]=(0,t.useState)(""),[_,y]=(0,t.useState)([]),[C,z]=(0,t.useState)(!1),[w,A]=(0,t.useState)([]),[S,E]=(0,t.useState)("");(0,t.useEffect)(()=>{let e=h;a&&(e=e.filter(e=>e.cantiere===a.nome)),A(e)},[h,a]);let[I,k]=(0,t.useState)({open:!1,cavo:null}),[T,F]=(0,t.useState)({open:!1,cavo:null}),[O,D]=(0,t.useState)({open:!1,cavo:null}),[R,P]=(0,t.useState)({open:!1,cavo:null}),[L,B]=(0,t.useState)({open:!1}),[$,U]=(0,t.useState)({open:!1}),[J,V]=(0,t.useState)(!1),[G,Z]=(0,t.useState)(!1),[q,H]=(0,t.useState)({open:!1,cavo:null}),[W,Q]=(0,t.useState)({open:!1,cavo:null}),[Y,X]=(0,t.useState)({totali:0,installati:0,collegati:0,certificati:0,percentualeInstallazione:0,percentualeCollegamento:0,percentualeCertificazione:0,metriTotali:0,metriInstallati:0,metriCollegati:0,metriCertificati:0}),[K,ee]=(0,t.useState)(0);(0,t.useEffect)(()=>{m||i||x.push("/login")},[i,m,x]),(0,t.useEffect)(()=>{{let e=(null==a?void 0:a.id_cantiere)||parseInt(localStorage.getItem("selectedCantiereId")||"0");ee(e),console.log({cantiereFromAuth:null==a?void 0:a.id_cantiere,storedId:e,localStorage:localStorage.getItem("selectedCantiereId")})}},[a]);let ea=a||(K>0?{id_cantiere:K,commessa:"Cantiere ".concat(K)}:null);(0,t.useEffect)(()=>{K&&K>0&&(et(),es())},[K]);let es=async()=>{try{let e=await fetch("http://localhost:8001/api/cavi/".concat(K,"/revisione-corrente"),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}});if(e.ok){let a=await e.json();E(a.revisione_corrente||"00")}else E("00")}catch(e){E("00")}},et=async()=>{try{j(!0),N("");try{let e=await d.At.getCavi(K),a=e.filter(e=>!e.spare),i=e.filter(e=>e.spare);p(a),g(i),el(a)}catch(e){try{let e=await fetch("http://localhost:8001/api/cavi/debug/".concat(K)),a=await e.json();if(a.cavi&&Array.isArray(a.cavi)){let e=a.cavi.filter(e=>!e.spare),i=a.cavi.filter(e=>e.spare);p(e),g(i),el(e),N("⚠️ Dati caricati tramite endpoint debug (problema autenticazione)")}else throw Error("Formato dati debug non valido")}catch(a){throw e}}}catch(i){var e,a;N("Errore nel caricamento dei cavi: ".concat((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message))}finally{j(!1)}},el=e=>{let a=e.length,i=e.filter(e=>(e.metri_posati||e.metratura_reale||0)>0).length,s=e.filter(e=>3===(e.collegamento||e.collegamenti)).length,t=e.filter(e=>e.certificato).length,l=e.reduce((e,a)=>e+(a.metri_teorici||0),0),r=e.reduce((e,a)=>e+(a.metri_posati||0),0);X({totali:a,installati:i,collegati:s,certificati:t,percentualeInstallazione:a>0?Math.round(i/a*100):0,percentualeCollegamento:a>0?Math.round(s/a*100):0,percentualeCertificazione:a>0?Math.round(t/a*100):0,metriTotali:l,metriInstallati:r,metriCollegati:e.filter(e=>3===e.collegamento).reduce((e,a)=>e+(a.metri_posati||0),0),metriCertificati:e.filter(e=>e.certificato).reduce((e,a)=>e+(a.metri_posati||0),0)})},er=async e=>{try{await new Promise(e=>setTimeout(e,1e3)),p(a=>a.map(a=>a.id_cavo===e?{...a,collegamento:0,collegamenti:0}:a)),await et(),u.success("Cavo Scollegato","Il cavo ".concat(e," \xe8 stato scollegato con successo."))}catch(e){throw u.error("Errore Scollegamento","Impossibile scollegare il cavo. Riprova."),e}},en=async e=>{try{await new Promise(e=>setTimeout(e,2e3));let a=new Blob(["PDF Content"],{type:"application/pdf"}),i=window.URL.createObjectURL(a),s=document.createElement("a");s.href=i,s.download=e.fileName,document.body.appendChild(s),s.click(),window.URL.revokeObjectURL(i),document.body.removeChild(s),u.success("PDF Generato","Certificato per il cavo ".concat(e.cavoId," generato con successo."))}catch(e){throw u.error("Errore Generazione PDF","Impossibile generare il certificato. Riprova."),e}},eo=async e=>{try{await new Promise(e=>setTimeout(e,1500)),p(a=>a.map(a=>a.id_cavo===e?{...a,certificato:!0,data_certificazione:new Date().toISOString()}:a)),await et(),u.success("Cavo Certificato","Il cavo ".concat(e," \xe8 stato certificato con successo."))}catch(e){throw u.error("Errore Certificazione","Impossibile certificare il cavo. Riprova."),e}},ec=(e,a,i)=>{switch(a){case"insert_meters":k({open:!0,cavo:e});break;case"modify_reel":F({open:!0,cavo:e});break;case"view_command":u({title:"Visualizza Comanda",description:"Apertura comanda ".concat(i," per cavo ").concat(e.id_cavo)});break;case"connect_cable":case"connect_arrival":case"connect_departure":case"disconnect_cable":case"manage_connections":D({open:!0,cavo:e});break;case"create_certificate":case"generate_pdf":P({open:!0,cavo:e})}},ed=(e,a)=>{switch(a){case"view_details":u({title:"Visualizza Dettagli",description:"Apertura dettagli per cavo ".concat(e.id_cavo)});break;case"edit":H({open:!0,cavo:e});break;case"delete":Q({open:!0,cavo:e});break;case"add_new":Z(!0);break;case"select":_.includes(e.id_cavo)?(y(_.filter(a=>a!==e.id_cavo)),u({title:"Cavo Deselezionato",description:"Cavo ".concat(e.id_cavo," deselezionato")})):(y([..._,e.id_cavo]),u({title:"Cavo Selezionato",description:"Cavo ".concat(e.id_cavo," selezionato")}));break;case"copy_id":navigator.clipboard.writeText(e.id_cavo),u({title:"ID Copiato",description:"ID cavo ".concat(e.id_cavo," copiato negli appunti")});break;case"copy_details":let i="ID: ".concat(e.id_cavo,", Tipologia: ").concat(e.tipologia,", Formazione: ").concat(e.formazione||e.sezione,", Metri: ").concat(e.metri_teorici);navigator.clipboard.writeText(i),u({title:"Dettagli Copiati",description:"Dettagli cavo copiati negli appunti"});break;case"add_to_command":u({title:"Aggiungi a Comanda",description:"Funzione aggiunta a comanda in sviluppo"});break;case"remove_from_command":u({title:"Rimuovi da Comanda",description:"Funzione rimozione da comanda in sviluppo"});break;case"create_command_posa":B({open:!0,tipoComanda:"POSA"});break;case"create_command_collegamento_partenza":B({open:!0,tipoComanda:"COLLEGAMENTO_PARTENZA"});break;case"create_command_collegamento_arrivo":B({open:!0,tipoComanda:"COLLEGAMENTO_ARRIVO"});break;case"create_command_certificazione":B({open:!0,tipoComanda:"CERTIFICAZIONE"});break;case"add_multiple_to_command":u({title:"Aggiungi Tutti a Comanda",description:"Funzione aggiunta multipla a comanda in sviluppo"});break;case"remove_multiple_from_commands":u({title:"Rimuovi Tutti dalle Comande",description:"Funzione rimozione multipla dalle comande in sviluppo"});break;default:u({title:"Azione non implementata",description:"Azione ".concat(a," non ancora implementata")})}},em=e=>{u({title:"Operazione completata",description:e}),et()},ex=e=>{u({title:"Errore",description:e,variant:"destructive"})};return m||b?(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsx)(ei.A,{className:"h-8 w-8 animate-spin"})}):K?f?(0,s.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(eA.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:f})]}),(0,s.jsx)(n.$,{onClick:et,className:"mt-4",children:"Riprova"})]}):(0,s.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,s.jsx)(ez,{cavi:h,filteredCavi:w,revisioneCorrente:S,className:"mb-2"}),(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsx)(eN,{cavi:h,loading:b,selectionEnabled:C,selectedCavi:_,onSelectionChange:y,onStatusAction:ec,onContextMenuAction:ed,onDisconnectCable:er,onGeneratePDF:en,onCertifyCable:eo})}),v.length>0&&(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsxs)(r.Zp,{children:[(0,s.jsx)(r.aR,{children:(0,s.jsxs)(r.ZB,{className:"flex items-center space-x-2",children:[(0,s.jsx)(M.A,{className:"h-5 w-5"}),(0,s.jsxs)("span",{children:["Cavi Spare (",v.length,")"]})]})}),(0,s.jsx)(r.Wu,{children:(0,s.jsx)(eN,{cavi:v,loading:b,selectionEnabled:!1,onStatusAction:ec,onContextMenuAction:ed,onDisconnectCable:er,onGeneratePDF:en,onCertifyCable:eo})})]})}),!1,(0,s.jsx)(eE,{open:I.open,onClose:()=>k({open:!1,cavo:null}),cavo:I.cavo,cantiere:ea,onSuccess:em,onError:ex}),(0,s.jsx)(eS,{open:T.open,onClose:()=>F({open:!1,cavo:null}),cavo:T.cavo,cantiere:ea,onSuccess:em,onError:ex}),(0,s.jsx)(eT,{open:O.open,onClose:()=>D({open:!1,cavo:null}),cavo:O.cavo,onSuccess:em,onError:ex}),(0,s.jsx)(eD,{open:R.open,onClose:()=>P({open:!1,cavo:null}),cavo:R.cavo,onSuccess:em,onError:ex}),(0,s.jsx)(eP,{open:L.open,onClose:()=>B({open:!1}),caviSelezionati:_,tipoComanda:L.tipoComanda,onSuccess:em,onError:ex}),(0,s.jsx)(eM,{open:$.open,onClose:()=>U({open:!1}),tipo:$.tipo||"cavi",onSuccess:em,onError:ex}),(0,s.jsx)(eU,{open:J,onClose:()=>V(!1),onSuccess:em,onError:ex}),(0,s.jsx)(eG,{open:G,onClose:()=>Z(!1),cantiere:a,onSuccess:e=>{em(e),et()},onError:ex}),(0,s.jsx)(eq,{open:q.open,onClose:()=>H({open:!1,cavo:null}),cavo:q.cavo,cantiere:a,onSuccess:e=>{em(e),et()},onError:ex}),(0,s.jsx)(eX,{open:W.open,onClose:()=>Q({open:!1,cavo:null}),cavo:W.cavo,cantiere:a,onSuccess:e=>{em(e),et()},onError:ex})]}):(0,s.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,s.jsxs)(o.Fc,{children:[(0,s.jsx)(eA.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:"Nessun cantiere selezionato. Seleziona un cantiere per visualizzare i cavi."})]}),(0,s.jsxs)("div",{className:"mt-4 p-4 bg-gray-100 rounded",children:[(0,s.jsx)("h3",{className:"font-bold",children:"Debug Info:"}),(0,s.jsxs)("p",{children:["User: ",e?e.username:"Non autenticato"]}),(0,s.jsxs)("p",{children:["Cantiere context: ",a?a.commessa:"Nessuno"]}),(0,s.jsxs)("p",{children:["Token presente: ",localStorage.getItem("token")?"S\xec":"No"]})]})]})}eY.displayName=eH.q7.displayName}},e=>{var a=a=>e(e.s=a);e.O(0,[3464,3455,9121,5560,5664,8034,7131,7538,2199,2461,283,1642,8441,1684,7358],()=>a(34428)),_N_E=e.O()}]);