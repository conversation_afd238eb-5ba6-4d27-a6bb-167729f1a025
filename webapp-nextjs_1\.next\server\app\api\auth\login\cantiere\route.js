(()=>{var e={};e.id=4810,e.ids=[4810],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33537:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>l,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{POST:()=>u});var o=r(96559),n=r(48088),a=r(37719),i=r(32190);async function u(e){try{let t=await e.json();if(!t.codice_univoco||!t.password)return i.NextResponse.json({detail:"Codice univoco e password sono richiesti"},{status:400});let r="http://localhost:8001";console.log("\uD83D\uDD04 Auth API: Proxying cantiere login request to backend:",`${r}/api/auth/login/cantiere`);let s=await fetch(`${r}/api/auth/login/cantiere`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});console.log("\uD83D\uDCE1 Auth API: Backend response status:",s.status);let o=await s.json();return console.log("\uD83D\uDCE1 Auth API: Backend response data:",o),i.NextResponse.json(o,{status:s.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("❌ Auth API: Cantiere login error:",e),i.NextResponse.json({detail:"Errore interno del server"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/login/cantiere/route",pathname:"/api/auth/login/cantiere",filename:"route",bundlePath:"app/api/auth/login/cantiere/route"},resolvedPagePath:"C:\\CMS\\webapp-nextjs_1\\src\\app\\api\\auth\\login\\cantiere\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:l}=p;function h(){return(0,a.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(33537));module.exports=s})();