"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4858],{381:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1243:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},15452:(e,t,a)=>{a.d(t,{G$:()=>J,Hs:()=>b,UC:()=>ea,VY:()=>er,ZL:()=>ee,bL:()=>Q,bm:()=>eo,hE:()=>en,hJ:()=>et,l9:()=>X});var n=a(12115),r=a(85185),o=a(6101),l=a(46081),i=a(61285),s=a(5845),d=a(19178),u=a(25519),c=a(34378),p=a(28905),f=a(63655),g=a(92293),m=a(93795),h=a(38168),v=a(99708),y=a(95155),D="Dialog",[x,b]=(0,l.A)(D),[j,k]=x(D),w=e=>{let{__scopeDialog:t,children:a,open:r,defaultOpen:o,onOpenChange:l,modal:d=!0}=e,u=n.useRef(null),c=n.useRef(null),[p,f]=(0,s.i)({prop:r,defaultProp:null!=o&&o,onChange:l,caller:D});return(0,y.jsx)(j,{scope:t,triggerRef:u,contentRef:c,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:d,children:a})};w.displayName=D;var C="DialogTrigger",R=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,l=k(C,a),i=(0,o.s)(t,l.triggerRef);return(0,y.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":H(l.open),...n,ref:i,onClick:(0,r.m)(e.onClick,l.onOpenToggle)})});R.displayName=C;var A="DialogPortal",[I,N]=x(A,{forceMount:void 0}),O=e=>{let{__scopeDialog:t,forceMount:a,children:r,container:o}=e,l=k(A,t);return(0,y.jsx)(I,{scope:t,forceMount:a,children:n.Children.map(r,e=>(0,y.jsx)(p.C,{present:a||l.open,children:(0,y.jsx)(c.Z,{asChild:!0,container:o,children:e})}))})};O.displayName=A;var _="DialogOverlay",E=n.forwardRef((e,t)=>{let a=N(_,e.__scopeDialog),{forceMount:n=a.forceMount,...r}=e,o=k(_,e.__scopeDialog);return o.modal?(0,y.jsx)(p.C,{present:n||o.open,children:(0,y.jsx)(M,{...r,ref:t})}):null});E.displayName=_;var F=(0,v.TL)("DialogOverlay.RemoveScroll"),M=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,r=k(_,a);return(0,y.jsx)(m.A,{as:F,allowPinchZoom:!0,shards:[r.contentRef],children:(0,y.jsx)(f.sG.div,{"data-state":H(r.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),P="DialogContent",q=n.forwardRef((e,t)=>{let a=N(P,e.__scopeDialog),{forceMount:n=a.forceMount,...r}=e,o=k(P,e.__scopeDialog);return(0,y.jsx)(p.C,{present:n||o.open,children:o.modal?(0,y.jsx)(G,{...r,ref:t}):(0,y.jsx)(T,{...r,ref:t})})});q.displayName=P;var G=n.forwardRef((e,t)=>{let a=k(P,e.__scopeDialog),l=n.useRef(null),i=(0,o.s)(t,a.contentRef,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,h.Eq)(e)},[]),(0,y.jsx)(V,{...e,ref:i,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,r.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=a.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,r.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,a=0===t.button&&!0===t.ctrlKey;(2===t.button||a)&&e.preventDefault()}),onFocusOutside:(0,r.m)(e.onFocusOutside,e=>e.preventDefault())})}),T=n.forwardRef((e,t)=>{let a=k(P,e.__scopeDialog),r=n.useRef(!1),o=n.useRef(!1);return(0,y.jsx)(V,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,l;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(r.current||null==(l=a.triggerRef.current)||l.focus(),t.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:t=>{var n,l;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let i=t.target;(null==(l=a.triggerRef.current)?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),V=n.forwardRef((e,t)=>{let{__scopeDialog:a,trapFocus:r,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,c=k(P,a),p=n.useRef(null),f=(0,o.s)(t,p);return(0,g.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(u.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,y.jsx)(d.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":H(c.open),...s,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(Y,{titleId:c.titleId}),(0,y.jsx)($,{contentRef:p,descriptionId:c.descriptionId})]})]})}),B="DialogTitle",L=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,r=k(B,a);return(0,y.jsx)(f.sG.h2,{id:r.titleId,...n,ref:t})});L.displayName=B;var z="DialogDescription",W=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,r=k(z,a);return(0,y.jsx)(f.sG.p,{id:r.descriptionId,...n,ref:t})});W.displayName=z;var Z="DialogClose",S=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,o=k(Z,a);return(0,y.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,r.m)(e.onClick,()=>o.onOpenChange(!1))})});function H(e){return e?"open":"closed"}S.displayName=Z;var U="DialogTitleWarning",[J,K]=(0,l.q)(U,{contentName:P,titleName:B,docsSlug:"dialog"}),Y=e=>{let{titleId:t}=e,a=K(U),r="`".concat(a.contentName,"` requires a `").concat(a.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(a.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(a.docsSlug);return n.useEffect(()=>{t&&(document.getElementById(t)||console.error(r))},[r,t]),null},$=e=>{let{contentRef:t,descriptionId:a}=e,r=K("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return n.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");a&&n&&(document.getElementById(a)||console.warn(o))},[o,t,a]),null},Q=w,X=R,ee=O,et=E,ea=q,en=L,er=W,eo=S},37108:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},40968:(e,t,a)=>{a.d(t,{b:()=>i});var n=a(12115),r=a(63655),o=a(95155),l=n.forwardRef((e,t)=>(0,o.jsx)(r.sG.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var i=l},47924:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},66140:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("ruler",[["path",{d:"M21.3 15.3a2.4 2.4 0 0 1 0 3.4l-2.6 2.6a2.4 2.4 0 0 1-3.4 0L2.7 8.7a2.41 2.41 0 0 1 0-3.4l2.6-2.6a2.41 2.41 0 0 1 3.4 0Z",key:"icamh8"}],["path",{d:"m14.5 12.5 2-2",key:"inckbg"}],["path",{d:"m11.5 9.5 2-2",key:"fmmyf7"}],["path",{d:"m8.5 6.5 2-2",key:"vc6u1g"}],["path",{d:"m17.5 15.5 2-2",key:"wo5hmg"}]])}}]);