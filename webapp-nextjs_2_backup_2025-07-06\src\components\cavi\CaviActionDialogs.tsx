'use client'

import { useState } from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Loader2, 
  FileText, 
  Download, 
  Unplug, 
  AlertTriangle,
  X,
  Settings,
  Mail
} from 'lucide-react'
import { Cavo } from '@/types'
import ConfirmationDialog from '@/components/ui/confirmation-dialog'

interface DisconnectCableDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: (cavoId: string) => Promise<void>
  cavo: Cavo | null
}

export function DisconnectCableDialog({
  isOpen,
  onClose,
  onConfirm,
  cavo
}: DisconnectCableDialogProps) {
  const [isLoading, setIsLoading] = useState(false)

  const handleConfirm = async () => {
    if (!cavo) return
    
    setIsLoading(true)
    try {
      await onConfirm(cavo.id_cavo)
      onClose()
    } catch (error) {
      console.error('Errore durante scollegamento:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (!cavo) return null

  return (
    <ConfirmationDialog
      isOpen={isOpen}
      onClose={onClose}
      onConfirm={handleConfirm}
      title="Conferma Scollegamento Cavo"
      description={`Sei sicuro di voler scollegare il cavo ${cavo.id_cavo}? Questa azione potrebbe influenzare lo stato di altri componenti e dovrà essere ricollegato manualmente.`}
      confirmText="Scollega"
      cancelText="Annulla"
      variant="warning"
      isLoading={isLoading}
      icon={<Unplug className="w-6 h-6" />}
    />
  )
}

interface GeneratePDFDialogProps {
  isOpen: boolean
  onClose: () => void
  onGenerate: (config: PDFGenerationConfig) => Promise<void>
  cavo: Cavo | null
}

export interface PDFGenerationConfig {
  cavoId: string
  fileName: string
  format: 'standard' | 'detailed' | 'summary'
  includeTestData: boolean
  includePhotos: boolean
  emailRecipient?: string
  notes?: string
}

export function GeneratePDFDialog({
  isOpen,
  onClose,
  onGenerate,
  cavo
}: GeneratePDFDialogProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [config, setConfig] = useState<PDFGenerationConfig>({
    cavoId: '',
    fileName: '',
    format: 'standard',
    includeTestData: true,
    includePhotos: false,
    emailRecipient: '',
    notes: ''
  })

  // Aggiorna config quando cambia il cavo
  useState(() => {
    if (cavo) {
      setConfig(prev => ({
        ...prev,
        cavoId: cavo.id_cavo,
        fileName: `Certificato_${cavo.id_cavo}_${new Date().toISOString().split('T')[0]}.pdf`
      }))
    }
  }, [cavo])

  const handleGenerate = async () => {
    if (!cavo) return
    
    setIsLoading(true)
    try {
      await onGenerate(config)
      onClose()
    } catch (error) {
      console.error('Errore durante generazione PDF:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (!cavo) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl border-l-4 border-l-blue-500">
        <DialogHeader className="bg-gradient-to-r from-blue-50 to-transparent p-6 -m-6 mb-4">
          <DialogTitle className="flex items-center gap-3 text-xl">
            <FileText className="w-6 h-6 text-blue-600" />
            Genera Certificato per Cavo {cavo.id_cavo}
          </DialogTitle>
          <DialogDescription className="text-base text-slate-600 mt-2">
            Configura le opzioni per la generazione del certificato PDF
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Informazioni Cavo */}
          <div className="bg-slate-50 p-4 rounded-lg border">
            <h4 className="font-medium text-slate-900 mb-2">Informazioni Cavo</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-slate-600">ID:</span>
                <span className="ml-2 font-mono">{cavo.id_cavo}</span>
              </div>
              <div>
                <span className="text-slate-600">Sistema:</span>
                <span className="ml-2">{cavo.sistema || 'N/A'}</span>
              </div>
              <div>
                <span className="text-slate-600">Tipologia:</span>
                <span className="ml-2">{cavo.tipologia || 'N/A'}</span>
              </div>
              <div>
                <span className="text-slate-600">Stato:</span>
                <span className="ml-2">{cavo.stato || 'N/A'}</span>
              </div>
            </div>
          </div>

          {/* Configurazione PDF */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="fileName">Nome File *</Label>
              <Input
                id="fileName"
                value={config.fileName}
                onChange={(e) => setConfig(prev => ({ ...prev, fileName: e.target.value }))}
                placeholder="Certificato_C001_2024-01-01.pdf"
                className="font-mono text-sm"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="format">Formato Certificato</Label>
              <Select 
                value={config.format} 
                onValueChange={(value: any) => setConfig(prev => ({ ...prev, format: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="standard">📄 Standard CEI 64-8</SelectItem>
                  <SelectItem value="detailed">📋 Dettagliato con Misure</SelectItem>
                  <SelectItem value="summary">📝 Riassunto Esecutivo</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="emailRecipient">Email Destinatario (Opzionale)</Label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                <Input
                  id="emailRecipient"
                  type="email"
                  value={config.emailRecipient}
                  onChange={(e) => setConfig(prev => ({ ...prev, emailRecipient: e.target.value }))}
                  placeholder="<EMAIL>"
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-3">
              <Label>Opzioni Aggiuntive</Label>
              <div className="space-y-2">
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={config.includeTestData}
                    onChange={(e) => setConfig(prev => ({ ...prev, includeTestData: e.target.checked }))}
                    className="rounded border-slate-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-slate-700">Includi Dati di Collaudo</span>
                </label>
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={config.includePhotos}
                    onChange={(e) => setConfig(prev => ({ ...prev, includePhotos: e.target.checked }))}
                    className="rounded border-slate-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-slate-700">Includi Foto Installazione</span>
                </label>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Note Aggiuntive</Label>
            <Textarea
              id="notes"
              value={config.notes}
              onChange={(e) => setConfig(prev => ({ ...prev, notes: e.target.value }))}
              placeholder="Note o commenti da includere nel certificato..."
              rows={3}
              className="resize-none"
            />
          </div>
        </div>

        <DialogFooter className="gap-3 pt-6 border-t">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
            className="px-6"
          >
            <X className="w-4 h-4 mr-2" />
            Annulla
          </Button>
          
          <Button
            onClick={handleGenerate}
            disabled={isLoading || !config.fileName.trim()}
            className="px-6 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Generazione...
              </>
            ) : (
              <>
                <Download className="w-4 h-4 mr-2" />
                Genera PDF
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

interface CertificationErrorDialogProps {
  isOpen: boolean
  onClose: () => void
  cavo: Cavo | null
  missingRequirements: string[]
}

export function CertificationErrorDialog({
  isOpen,
  onClose,
  cavo,
  missingRequirements
}: CertificationErrorDialogProps) {
  if (!cavo) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md border-l-4 border-l-red-500">
        <DialogHeader className="bg-gradient-to-r from-red-50 to-transparent p-6 -m-6 mb-4">
          <DialogTitle className="flex items-center gap-3 text-xl">
            <AlertTriangle className="w-6 h-6 text-red-600" />
            Impossibile Certificare Cavo
          </DialogTitle>
          <DialogDescription className="text-base text-slate-600 mt-2">
            Il cavo {cavo.id_cavo} non può essere certificato nel suo stato attuale
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <Alert variant="destructive" className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="font-medium">
              Requisiti mancanti per la certificazione:
            </AlertDescription>
          </Alert>

          <ul className="space-y-2">
            {missingRequirements.map((requirement, index) => (
              <li key={index} className="flex items-center gap-2 text-sm text-slate-700">
                <X className="w-4 h-4 text-red-500 flex-shrink-0" />
                {requirement}
              </li>
            ))}
          </ul>

          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h4 className="font-medium text-blue-900 mb-2">💡 Suggerimento</h4>
            <p className="text-sm text-blue-800">
              Completa tutti i requisiti sopra elencati prima di procedere con la certificazione. 
              Puoi utilizzare le azioni disponibili nella tabella per aggiornare lo stato del cavo.
            </p>
          </div>
        </div>

        <DialogFooter className="pt-6 border-t">
          <Button
            onClick={onClose}
            className="px-6 bg-slate-600 hover:bg-slate-700"
          >
            <Settings className="w-4 h-4 mr-2" />
            Ho Capito
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
