(()=>{var e={};e.id=2881,e.ids=[2881],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75224:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>p,serverHooks:()=>c,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{GET:()=>u});var o=r(96559),a=r(48088),n=r(37719),i=r(32190);async function u(e,{params:t}){try{let r=t.id,s=e.headers.get("authorization");if(!s||!s.startsWith("Bearer "))return i.NextResponse.json({detail:"Token di autorizzazione mancante"},{status:401});let o="http://localhost:8001";console.log("\uD83D\uDD04 User Toggle API: Proxying request to backend:",`${o}/api/users/toggle/${r}`);let a=await fetch(`${o}/api/users/toggle/${r}`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:s}});if(console.log("\uD83D\uDCE1 User Toggle API: Backend response status:",a.status),!a.ok){let e=await a.json().catch(()=>({detail:"Errore sconosciuto"}));return console.error("❌ User Toggle API: Backend error:",e),i.NextResponse.json(e,{status:a.status})}let n=await a.json();return console.log("\uD83D\uDCE1 User Toggle API: Backend response data:",n),i.NextResponse.json(n,{status:a.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("❌ User Toggle API: Error:",e),i.NextResponse.json({detail:"Errore interno del server"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/users/toggle/[id]/route",pathname:"/api/users/toggle/[id]",filename:"route",bundlePath:"app/api/users/toggle/[id]/route"},resolvedPagePath:"C:\\CMS\\webapp-nextjs_1\\src\\app\\api\\users\\toggle\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:c}=p;function g(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}},78335:()=>{},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(75224));module.exports=s})();