(()=>{var e={};e.id=2804,e.ids=[2804],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20089:(e,t,a)=>{Promise.resolve().then(a.bind(a,63548))},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},54582:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var i=a(65239),s=a(48088),r=a(88170),n=a.n(r),o=a(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);a.d(t,l);let d={children:["",{children:["test-unified-modal",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,63548)),"C:\\CMS\\webapp-nextjs_1\\src\\app\\test-unified-modal\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\CMS\\webapp-nextjs_1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\CMS\\webapp-nextjs_1\\src\\app\\test-unified-modal\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},p=new i.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/test-unified-modal/page",pathname:"/test-unified-modal",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56537:(e,t,a)=>{Promise.resolve().then(a.bind(a,92406))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>u,Es:()=>m,L3:()=>x,c7:()=>p,lG:()=>o,rr:()=>f,zM:()=>l});var i=a(60687);a(43210);var s=a(26134),r=a(11860),n=a(4780);function o({...e}){return(0,i.jsx)(s.bL,{"data-slot":"dialog",...e})}function l({...e}){return(0,i.jsx)(s.l9,{"data-slot":"dialog-trigger",...e})}function d({...e}){return(0,i.jsx)(s.ZL,{"data-slot":"dialog-portal",...e})}function c({className:e,...t}){return(0,i.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function u({className:e,children:t,showCloseButton:a=!0,...o}){return(0,i.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,i.jsx)(c,{}),(0,i.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...o,children:[t,a&&(0,i.jsxs)(s.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,i.jsx)(r.A,{}),(0,i.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function p({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function m({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function x({className:e,...t}){return(0,i.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",e),...t})}function f({className:e,...t}){return(0,i.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}},63548:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});let i=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs_1\\\\src\\\\app\\\\test-unified-modal\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_1\\src\\app\\test-unified-modal\\page.tsx","default")},70440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});var i=a(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,i.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},76628:(e,t,a)=>{"use strict";a.d(t,{jV:()=>r});var i=a(43210),s=a(63213);function r(){let{cantiere:e,isLoading:t}=(0,s.A)(),[a,r]=(0,i.useState)(null),[n,o]=(0,i.useState)(!0),[l,d]=(0,i.useState)(null);return{cantiereId:a,cantiere:e,isValidCantiere:null!==a&&a>0,isLoading:n,error:l,validateCantiere:e=>{if(null==e)return!1;let t="string"==typeof e?parseInt(e,10):e;return!isNaN(t)&&!(t<=0)||(console.warn("\uD83C\uDFD7️ useCantiere: ID cantiere non valido:",e),!1)},clearError:()=>d(null)}}},78148:(e,t,a)=>{"use strict";a.d(t,{b:()=>o});var i=a(43210),s=a(14163),r=a(60687),n=i.forwardRef((e,t)=>(0,r.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var o=n},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,a)=>{"use strict";a.d(t,{J:()=>n});var i=a(60687);a(43210);var s=a(78148),r=a(4780);function n({className:e,...t}){return(0,i.jsx)(s.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84027:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},89667:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var i=a(60687),s=a(43210),r=a(4780);let n=s.forwardRef(({className:e,type:t,...a},s)=>(0,i.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),ref:s,...a}));n.displayName="Input"},92406:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c});var i=a(60687),s=a(43210),r=a(29523),n=a(49039);let o={id_cavo:"C001",id_cantiere:1,revisione_ufficiale:"Rev 1.0",tipologia:"LIYCY",sezione:"3X2.5MM",formazione:"3X2.5MM",da:"Quadro A",a:"Quadro B",ubicazione_partenza:"Quadro A",ubicazione_arrivo:"Quadro B",metri_teorici:150,metri_posati:75,metratura_reale:75,stato_installazione:"installato",id_bobina:"BOB001"},l={id_cantiere:1,commessa:"TEST-001"},d=()=>{let[e,t]=(0,s.useState)(!1),[a,d]=(0,s.useState)(!1),c=async e=>{console.log("\uD83D\uDCBE Unified Modal Save:",e),await new Promise(e=>setTimeout(e,1e3)),alert(`Operazione completata con successo!
Modalit\xe0: ${e.mode}
Cavo: ${e.cableId}`)};return(0,i.jsxs)("div",{className:"p-6 space-y-4",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Test Interfaccia Unificata"}),(0,i.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,i.jsxs)("h3",{className:"font-semibold mb-2",children:["Cavo di Test: ",o.id_cavo]}),(0,i.jsxs)("p",{className:"text-sm text-gray-600",children:[o.tipologia," ",o.sezione," - Da: ",o.da," A: ",o.a]}),(0,i.jsxs)("p",{className:"text-sm text-gray-600",children:["Metri posati: ",o.metri_posati,"m / ",o.metri_teorici,"m"]})]}),(0,i.jsxs)("div",{className:"flex gap-4",children:[(0,i.jsx)(r.$,{onClick:()=>t(!0),className:"bg-green-600 hover:bg-green-700",children:"Test Modalit\xe0: Aggiungi Metri"}),(0,i.jsx)(r.$,{onClick:()=>d(!0),className:"bg-blue-600 hover:bg-blue-700",children:"Test Modalit\xe0: Modifica Bobina"})]}),(0,i.jsx)(n.B,{mode:"aggiungi_metri",open:e,onClose:()=>t(!1),cavo:o,cantiere:l,onSave:c}),(0,i.jsx)(n.B,{mode:"modifica_bobina",open:a,onClose:()=>d(!1),cavo:o,cantiere:l,onSave:c})]})};function c(){return(0,i.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,i.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-center mb-8 text-gray-800",children:"Test Interfaccia Unificata Cable/Bobbin"}),(0,i.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6",children:[(0,i.jsx)("h2",{className:"font-semibold text-blue-800 mb-2",children:"Istruzioni per il Test"}),(0,i.jsxs)("ul",{className:"text-blue-700 text-sm space-y-1",children:[(0,i.jsx)("li",{children:"• Clicca sui pulsanti per aprire la modale unificata in diverse modalit\xe0"}),(0,i.jsx)("li",{children:"• Testa la validazione dei campi e la selezione delle bobine"}),(0,i.jsx)("li",{children:"• Verifica che le sezioni dinamiche si mostrino correttamente"}),(0,i.jsx)("li",{children:"• Controlla l'accessibilit\xe0 con Tab e Escape"}),(0,i.jsx)("li",{children:"• Verifica i messaggi di errore e successo"})]})]}),(0,i.jsx)(d,{}),(0,i.jsxs)("div",{className:"mt-8 bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,i.jsx)("h2",{className:"font-semibold text-green-800 mb-2",children:"Funzionalit\xe0 Implementate"}),(0,i.jsxs)("div",{className:"grid md:grid-cols-2 gap-4 text-sm text-green-700",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-medium mb-1",children:"Modalit\xe0 Aggiungi Metri:"}),(0,i.jsxs)("ul",{className:"space-y-1",children:[(0,i.jsx)("li",{children:"✅ Validazione input metri"}),(0,i.jsx)("li",{children:"✅ Selezione bobina compatibile/incompatibile"}),(0,i.jsx)("li",{children:"✅ Opzione BOBINA VUOTA"}),(0,i.jsx)("li",{children:"✅ Ricerca bobine"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-medium mb-1",children:"Modalit\xe0 Modifica Bobina:"}),(0,i.jsxs)("ul",{className:"space-y-1",children:[(0,i.jsx)("li",{children:"✅ Radio button per opzioni"}),(0,i.jsx)("li",{children:"✅ Selezione condizionale bobina"}),(0,i.jsx)("li",{children:"✅ Modifica metri posati"}),(0,i.jsx)("li",{children:"✅ Annulla posa"})]})]})]})]}),(0,i.jsxs)("div",{className:"mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,i.jsx)("h2",{className:"font-semibold text-yellow-800 mb-2",children:"Accessibilit\xe0"}),(0,i.jsxs)("div",{className:"text-sm text-yellow-700 space-y-1",children:[(0,i.jsx)("li",{children:"✅ Attributi ARIA per screen reader"}),(0,i.jsx)("li",{children:"✅ Navigazione da tastiera (Tab, Enter, Escape)"}),(0,i.jsx)("li",{children:"✅ Focus management e indicatori visivi"}),(0,i.jsx)("li",{children:'✅ Messaggi di errore con role="alert"'}),(0,i.jsx)("li",{children:"✅ Etichette descrittive per tutti i controlli"})]})]})]})})})}},94735:e=>{"use strict";e.exports=require("events")},99270:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),i=t.X(0,[4447,6539,1658,4951,9039],()=>a(54582));module.exports=i})();