'use client'

import { useEffect, useRef } from 'react'
import { useAuth } from '@/contexts/AuthContext'

/**
 * Hook per gestire la persistenza della sessione e prevenire stati "fantasma"
 * Risolve il bug dove l'utente appare loggato ma i reindirizzamenti non funzionano
 */
export const useSessionPersistence = () => {
  const { isAuthenticated, user, cantiere, checkAuth, logout } = useAuth()
  const heartbeatInterval = useRef<NodeJS.Timeout | null>(null)
  const lastActivityTime = useRef<number>(Date.now())

  // Aggiorna il timestamp dell'ultima attività
  const updateActivity = () => {
    lastActivityTime.current = Date.now()
  }

  // Verifica periodicamente la validità della sessione
  const startHeartbeat = () => {
    if (heartbeatInterval.current) {
      clearInterval(heartbeatInterval.current)
    }

    heartbeatInterval.current = setInterval(async () => {
      try {
        // Verifica se la sessione è ancora valida
        if (isAuthenticated && (user || cantiere)) {
          console.log('🔄 SessionPersistence: Verifica heartbeat sessione')
          await checkAuth()
        }
      } catch (error) {
        console.error('🔄 SessionPersistence: Errore durante heartbeat:', error)
        // Se il heartbeat fallisce, forza il logout per evitare stati inconsistenti
        logout()
      }
    }, 5 * 60 * 1000) // Ogni 5 minuti
  }

  // Ferma il heartbeat
  const stopHeartbeat = () => {
    if (heartbeatInterval.current) {
      clearInterval(heartbeatInterval.current)
      heartbeatInterval.current = null
    }
  }

  // Gestisce la visibilità della pagina (quando l'utente torna alla tab)
  const handleVisibilityChange = async () => {
    if (document.visibilityState === 'visible' && isAuthenticated) {
      console.log('🔄 SessionPersistence: Pagina tornata visibile, verifica sessione')
      try {
        await checkAuth()
      } catch (error) {
        console.error('🔄 SessionPersistence: Errore durante verifica visibilità:', error)
        logout()
      }
    }
  }

  // Gestisce il focus della finestra
  const handleWindowFocus = async () => {
    if (isAuthenticated) {
      console.log('🔄 SessionPersistence: Finestra tornata in focus, verifica sessione')
      try {
        await checkAuth()
      } catch (error) {
        console.error('🔄 SessionPersistence: Errore durante verifica focus:', error)
        logout()
      }
    }
  }

  // Gestisce la chiusura della finestra/tab
  const handleBeforeUnload = () => {
    // Salva il timestamp dell'ultima attività
    if (typeof window !== 'undefined') {
      localStorage.setItem('lastActivity', lastActivityTime.current.toString())
    }
  }

  // Verifica se la sessione è scaduta per inattività
  const checkSessionExpiry = () => {
    if (typeof window !== 'undefined' && isAuthenticated) {
      const lastActivity = localStorage.getItem('lastActivity')
      if (lastActivity) {
        const timeDiff = Date.now() - parseInt(lastActivity, 10)
        const maxInactivity = 24 * 60 * 60 * 1000 // 24 ore

        if (timeDiff > maxInactivity) {
          console.log('🔄 SessionPersistence: Sessione scaduta per inattività')
          logout()
          return false
        }
      }
    }
    return true
  }

  useEffect(() => {
    if (typeof window === 'undefined') return

    // Se non autenticato, non fare nulla
    if (!isAuthenticated) {
      console.log('🔄 SessionPersistence: Utente non autenticato, skip')
      stopHeartbeat()
      return
    }

    // Verifica la scadenza della sessione all'avvio
    if (!checkSessionExpiry()) {
      return
    }

    console.log('🔄 SessionPersistence: Attivazione per utente autenticato')

    // Avvia il heartbeat per sessioni autenticate
    startHeartbeat()

    // Aggiungi event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('focus', handleWindowFocus)
    window.addEventListener('beforeunload', handleBeforeUnload)

    // Event listeners per tracciare l'attività dell'utente
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click']
    activityEvents.forEach(event => {
      document.addEventListener(event, updateActivity, { passive: true })
    })

    return () => {
      // Cleanup
      console.log('🔄 SessionPersistence: Cleanup event listeners')
      stopHeartbeat()
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('focus', handleWindowFocus)
      window.removeEventListener('beforeunload', handleBeforeUnload)

      activityEvents.forEach(event => {
        document.removeEventListener(event, updateActivity)
      })
    }
  }, [isAuthenticated, user, cantiere])

  // Cleanup finale
  useEffect(() => {
    return () => {
      stopHeartbeat()
    }
  }, [])

  return {
    updateActivity,
    checkSessionExpiry
  }
}
