{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/cavi/CaviContextMenu.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport {\n  ContextMenu,\n  ContextMenuContent,\n  ContextMenuItem,\n  ContextMenuSeparator,\n  ContextMenuSub,\n  ContextMenuSubContent,\n  ContextMenuSubTrigger,\n  ContextMenuTrigger,\n} from '@/components/ui/context-menu'\nimport { Cavo } from '@/types'\nimport {\n  Eye,\n  Edit,\n  Trash2,\n  Copy,\n  Plus,\n  Minus,\n  CheckSquare,\n  Square,\n  FileText,\n  Link,\n  Zap,\n  Award,\n  Settings,\n  Users,\n  ClipboardList\n} from 'lucide-react'\n\ninterface CaviContextMenuProps {\n  children: React.ReactNode\n  cavo: Cavo\n  isSelected: boolean\n  hasMultipleSelection: boolean\n  totalSelectedCount: number\n  onAction: (cavo: Cavo, action: string) => void\n}\n\nexport default function CaviContextMenu({\n  children,\n  cavo,\n  isSelected,\n  hasMultipleSelection,\n  totalSelectedCount,\n  onAction\n}: CaviContextMenuProps) {\n\n  const handleAction = (action: string) => {\n    onAction(cavo, action)\n  }\n\n  return (\n    <ContextMenu>\n      <ContextMenuTrigger asChild>\n        {children}\n      </ContextMenuTrigger>\n      <ContextMenuContent className=\"w-64\">\n        \n        {/* Sezione comande multiple (solo se ci sono più cavi selezionati) */}\n        {hasMultipleSelection && (\n          <>\n            <div className=\"px-2 py-1.5 text-sm font-semibold text-muted-foreground\">\n              📋 Crea Comande Multiple ({totalSelectedCount} cavi)\n            </div>\n            \n            <ContextMenuItem onClick={() => handleAction('create_command_posa')}>\n              <Settings className=\"mr-2 h-4 w-4\" />\n              Comanda Posa\n            </ContextMenuItem>\n            \n            <ContextMenuItem onClick={() => handleAction('create_command_collegamento_partenza')}>\n              <Link className=\"mr-2 h-4 w-4\" />\n              Comanda Collegamento Partenza\n            </ContextMenuItem>\n            \n            <ContextMenuItem onClick={() => handleAction('create_command_collegamento_arrivo')}>\n              <Zap className=\"mr-2 h-4 w-4\" />\n              Comanda Collegamento Arrivo\n            </ContextMenuItem>\n            \n            <ContextMenuItem onClick={() => handleAction('create_command_certificazione')}>\n              <Award className=\"mr-2 h-4 w-4\" />\n              Comanda Certificazione\n            </ContextMenuItem>\n            \n            <ContextMenuSeparator />\n            \n            <div className=\"px-2 py-1.5 text-sm font-semibold text-muted-foreground\">\n              📋 Gestione Comande Esistenti ({totalSelectedCount} cavi)\n            </div>\n            \n            <ContextMenuItem onClick={() => handleAction('add_multiple_to_command')}>\n              <ClipboardList className=\"mr-2 h-4 w-4\" />\n              Aggiungi Tutti a Comanda\n            </ContextMenuItem>\n            \n            <ContextMenuItem onClick={() => handleAction('remove_multiple_from_commands')}>\n              <Minus className=\"mr-2 h-4 w-4\" />\n              Rimuovi Tutti dalle Comande\n            </ContextMenuItem>\n            \n            <ContextMenuSeparator />\n          </>\n        )}\n\n        {/* Azioni singolo cavo (solo se non c'è selezione multipla) */}\n        {!hasMultipleSelection && (\n          <>\n            <ContextMenuItem onClick={() => handleAction('view_details')}>\n              <Eye className=\"mr-2 h-4 w-4\" />\n              Visualizza Dettagli\n            </ContextMenuItem>\n            \n            <ContextMenuSeparator />\n            \n            <div className=\"px-2 py-1.5 text-sm font-semibold text-muted-foreground\">\n              📋 Gestione Comande\n            </div>\n            \n            <ContextMenuItem onClick={() => handleAction('add_to_command')}>\n              <ClipboardList className=\"mr-2 h-4 w-4\" />\n              Aggiungi a Comanda\n            </ContextMenuItem>\n            \n            {(cavo.comanda_posa || cavo.comanda_partenza || cavo.comanda_arrivo || cavo.comanda_certificazione) && (\n              <ContextMenuItem onClick={() => handleAction('remove_from_command')}>\n                <Minus className=\"mr-2 h-4 w-4\" />\n                Rimuovi da Comanda\n              </ContextMenuItem>\n            )}\n            \n            <ContextMenuSeparator />\n            \n            <ContextMenuItem onClick={() => handleAction('edit')}>\n              <Edit className=\"mr-2 h-4 w-4\" />\n              Modifica\n            </ContextMenuItem>\n            \n            <ContextMenuItem onClick={() => handleAction('delete')} className=\"text-red-600\">\n              <Trash2 className=\"mr-2 h-4 w-4\" />\n              Elimina\n            </ContextMenuItem>\n            \n            <ContextMenuSeparator />\n            \n            <ContextMenuItem onClick={() => handleAction('add_new')}>\n              <Plus className=\"mr-2 h-4 w-4\" />\n              Aggiungi nuovo cavo\n            </ContextMenuItem>\n            \n            <ContextMenuSeparator />\n          </>\n        )}\n\n        {/* Azioni di selezione (sempre presenti) */}\n        <ContextMenuItem onClick={() => handleAction('select')}>\n          {isSelected ? (\n            <>\n              <Square className=\"mr-2 h-4 w-4\" />\n              Deseleziona\n            </>\n          ) : (\n            <>\n              <CheckSquare className=\"mr-2 h-4 w-4\" />\n              Seleziona\n            </>\n          )}\n        </ContextMenuItem>\n\n        {/* Azioni di copia (sempre presenti) */}\n        <ContextMenuSeparator />\n        \n        <ContextMenuItem onClick={() => handleAction('copy_id')}>\n          <Copy className=\"mr-2 h-4 w-4\" />\n          {hasMultipleSelection ? 'Copia IDs Selezionati' : 'Copia ID'}\n          <span className=\"ml-auto text-xs text-muted-foreground\">Ctrl+C</span>\n        </ContextMenuItem>\n        \n        <ContextMenuItem onClick={() => handleAction('copy_details')}>\n          <FileText className=\"mr-2 h-4 w-4\" />\n          {hasMultipleSelection ? 'Copia Dettagli Selezionati' : 'Copia Dettagli'}\n        </ContextMenuItem>\n\n      </ContextMenuContent>\n    </ContextMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAWA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAdA;;;;AAyCe,SAAS,gBAAgB,EACtC,QAAQ,EACR,IAAI,EACJ,UAAU,EACV,oBAAoB,EACpB,kBAAkB,EAClB,QAAQ,EACa;IAErB,MAAM,eAAe,CAAC;QACpB,SAAS,MAAM;IACjB;IAEA,qBACE,8OAAC,2IAAA,CAAA,cAAW;;0BACV,8OAAC,2IAAA,CAAA,qBAAkB;gBAAC,OAAO;0BACxB;;;;;;0BAEH,8OAAC,2IAAA,CAAA,qBAAkB;gBAAC,WAAU;;oBAG3B,sCACC;;0CACE,8OAAC;gCAAI,WAAU;;oCAA0D;oCAC5C;oCAAmB;;;;;;;0CAGhD,8OAAC,2IAAA,CAAA,kBAAe;gCAAC,SAAS,IAAM,aAAa;;kDAC3C,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIvC,8OAAC,2IAAA,CAAA,kBAAe;gCAAC,SAAS,IAAM,aAAa;;kDAC3C,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAInC,8OAAC,2IAAA,CAAA,kBAAe;gCAAC,SAAS,IAAM,aAAa;;kDAC3C,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIlC,8OAAC,2IAAA,CAAA,kBAAe;gCAAC,SAAS,IAAM,aAAa;;kDAC3C,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIpC,8OAAC,2IAAA,CAAA,uBAAoB;;;;;0CAErB,8OAAC;gCAAI,WAAU;;oCAA0D;oCACvC;oCAAmB;;;;;;;0CAGrD,8OAAC,2IAAA,CAAA,kBAAe;gCAAC,SAAS,IAAM,aAAa;;kDAC3C,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAI5C,8OAAC,2IAAA,CAAA,kBAAe;gCAAC,SAAS,IAAM,aAAa;;kDAC3C,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIpC,8OAAC,2IAAA,CAAA,uBAAoB;;;;;;;oBAKxB,CAAC,sCACA;;0CACE,8OAAC,2IAAA,CAAA,kBAAe;gCAAC,SAAS,IAAM,aAAa;;kDAC3C,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIlC,8OAAC,2IAAA,CAAA,uBAAoB;;;;;0CAErB,8OAAC;gCAAI,WAAU;0CAA0D;;;;;;0CAIzE,8OAAC,2IAAA,CAAA,kBAAe;gCAAC,SAAS,IAAM,aAAa;;kDAC3C,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;4BAI3C,CAAC,KAAK,YAAY,IAAI,KAAK,gBAAgB,IAAI,KAAK,cAAc,IAAI,KAAK,sBAAsB,mBAChG,8OAAC,2IAAA,CAAA,kBAAe;gCAAC,SAAS,IAAM,aAAa;;kDAC3C,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAKtC,8OAAC,2IAAA,CAAA,uBAAoB;;;;;0CAErB,8OAAC,2IAAA,CAAA,kBAAe;gCAAC,SAAS,IAAM,aAAa;;kDAC3C,8OAAC,2MAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAInC,8OAAC,2IAAA,CAAA,kBAAe;gCAAC,SAAS,IAAM,aAAa;gCAAW,WAAU;;kDAChE,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIrC,8OAAC,2IAAA,CAAA,uBAAoB;;;;;0CAErB,8OAAC,2IAAA,CAAA,kBAAe;gCAAC,SAAS,IAAM,aAAa;;kDAC3C,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAInC,8OAAC,2IAAA,CAAA,uBAAoB;;;;;;;kCAKzB,8OAAC,2IAAA,CAAA,kBAAe;wBAAC,SAAS,IAAM,aAAa;kCAC1C,2BACC;;8CACE,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;;yDAIrC;;8CACE,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;kCAO9C,8OAAC,2IAAA,CAAA,uBAAoB;;;;;kCAErB,8OAAC,2IAAA,CAAA,kBAAe;wBAAC,SAAS,IAAM,aAAa;;0CAC3C,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BACf,uBAAuB,0BAA0B;0CAClD,8OAAC;gCAAK,WAAU;0CAAwC;;;;;;;;;;;;kCAG1D,8OAAC,2IAAA,CAAA,kBAAe;wBAAC,SAAS,IAAM,aAAa;;0CAC3C,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BACnB,uBAAuB,+BAA+B;;;;;;;;;;;;;;;;;;;AAMjE", "debugId": null}}, {"offset": {"line": 418, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/cavi/CaviActionDialogs.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport {\n  <PERSON>alog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { \n  Loader2, \n  FileText, \n  Download, \n  Unplug, \n  AlertTriangle,\n  X,\n  Settings,\n  Mail\n} from 'lucide-react'\nimport { Cavo } from '@/types'\nimport ConfirmationDialog from '@/components/ui/confirmation-dialog'\n\ninterface DisconnectCableDialogProps {\n  isOpen: boolean\n  onClose: () => void\n  onConfirm: (cavoId: string) => Promise<void>\n  cavo: Cavo | null\n}\n\nexport function DisconnectCableDialog({\n  isOpen,\n  onClose,\n  onConfirm,\n  cavo\n}: DisconnectCableDialogProps) {\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleConfirm = async () => {\n    if (!cavo) return\n    \n    setIsLoading(true)\n    try {\n      await onConfirm(cavo.id_cavo)\n      onClose()\n    } catch (error) {\n      console.error('Errore durante scollegamento:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  if (!cavo) return null\n\n  return (\n    <ConfirmationDialog\n      isOpen={isOpen}\n      onClose={onClose}\n      onConfirm={handleConfirm}\n      title=\"Conferma Scollegamento Cavo\"\n      description={`Sei sicuro di voler scollegare il cavo ${cavo.id_cavo}? Questa azione potrebbe influenzare lo stato di altri componenti e dovrà essere ricollegato manualmente.`}\n      confirmText=\"Scollega\"\n      cancelText=\"Annulla\"\n      variant=\"warning\"\n      isLoading={isLoading}\n      icon={<Unplug className=\"w-6 h-6\" />}\n    />\n  )\n}\n\ninterface GeneratePDFDialogProps {\n  isOpen: boolean\n  onClose: () => void\n  onGenerate: (config: PDFGenerationConfig) => Promise<void>\n  cavo: Cavo | null\n}\n\nexport interface PDFGenerationConfig {\n  cavoId: string\n  fileName: string\n  format: 'standard' | 'detailed' | 'summary'\n  includeTestData: boolean\n  includePhotos: boolean\n  emailRecipient?: string\n  notes?: string\n}\n\nexport function GeneratePDFDialog({\n  isOpen,\n  onClose,\n  onGenerate,\n  cavo\n}: GeneratePDFDialogProps) {\n  const [isLoading, setIsLoading] = useState(false)\n  const [config, setConfig] = useState<PDFGenerationConfig>({\n    cavoId: '',\n    fileName: '',\n    format: 'standard',\n    includeTestData: true,\n    includePhotos: false,\n    emailRecipient: '',\n    notes: ''\n  })\n\n  // Aggiorna config quando cambia il cavo\n  useState(() => {\n    if (cavo) {\n      setConfig(prev => ({\n        ...prev,\n        cavoId: cavo.id_cavo,\n        fileName: `Certificato_${cavo.id_cavo}_${new Date().toISOString().split('T')[0]}.pdf`\n      }))\n    }\n  }, [cavo])\n\n  const handleGenerate = async () => {\n    if (!cavo) return\n    \n    setIsLoading(true)\n    try {\n      await onGenerate(config)\n      onClose()\n    } catch (error) {\n      console.error('Errore durante generazione PDF:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  if (!cavo) return null\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-2xl border-l-4 border-l-blue-500\">\n        <DialogHeader className=\"bg-gradient-to-r from-blue-50 to-transparent p-6 -m-6 mb-4\">\n          <DialogTitle className=\"flex items-center gap-3 text-xl\">\n            <FileText className=\"w-6 h-6 text-blue-600\" />\n            Genera Certificato per Cavo {cavo.id_cavo}\n          </DialogTitle>\n          <DialogDescription className=\"text-base text-slate-600 mt-2\">\n            Configura le opzioni per la generazione del certificato PDF\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-6\">\n          {/* Informazioni Cavo */}\n          <div className=\"bg-slate-50 p-4 rounded-lg border\">\n            <h4 className=\"font-medium text-slate-900 mb-2\">Informazioni Cavo</h4>\n            <div className=\"grid grid-cols-2 gap-4 text-sm\">\n              <div>\n                <span className=\"text-slate-600\">ID:</span>\n                <span className=\"ml-2 font-mono\">{cavo.id_cavo}</span>\n              </div>\n              <div>\n                <span className=\"text-slate-600\">Sistema:</span>\n                <span className=\"ml-2\">{cavo.sistema || 'N/A'}</span>\n              </div>\n              <div>\n                <span className=\"text-slate-600\">Tipologia:</span>\n                <span className=\"ml-2\">{cavo.tipologia || 'N/A'}</span>\n              </div>\n              <div>\n                <span className=\"text-slate-600\">Stato:</span>\n                <span className=\"ml-2\">{cavo.stato || 'N/A'}</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Configurazione PDF */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"fileName\">Nome File *</Label>\n              <Input\n                id=\"fileName\"\n                value={config.fileName}\n                onChange={(e) => setConfig(prev => ({ ...prev, fileName: e.target.value }))}\n                placeholder=\"Certificato_C001_2024-01-01.pdf\"\n                className=\"font-mono text-sm\"\n              />\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"format\">Formato Certificato</Label>\n              <Select \n                value={config.format} \n                onValueChange={(value: any) => setConfig(prev => ({ ...prev, format: value }))}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"standard\">📄 Standard CEI 64-8</SelectItem>\n                  <SelectItem value=\"detailed\">📋 Dettagliato con Misure</SelectItem>\n                  <SelectItem value=\"summary\">📝 Riassunto Esecutivo</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"emailRecipient\">Email Destinatario (Opzionale)</Label>\n              <div className=\"relative\">\n                <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400\" />\n                <Input\n                  id=\"emailRecipient\"\n                  type=\"email\"\n                  value={config.emailRecipient}\n                  onChange={(e) => setConfig(prev => ({ ...prev, emailRecipient: e.target.value }))}\n                  placeholder=\"<EMAIL>\"\n                  className=\"pl-10\"\n                />\n              </div>\n            </div>\n\n            <div className=\"space-y-3\">\n              <Label>Opzioni Aggiuntive</Label>\n              <div className=\"space-y-2\">\n                <label className=\"flex items-center space-x-2 cursor-pointer\">\n                  <input\n                    type=\"checkbox\"\n                    checked={config.includeTestData}\n                    onChange={(e) => setConfig(prev => ({ ...prev, includeTestData: e.target.checked }))}\n                    className=\"rounded border-slate-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                  <span className=\"text-sm text-slate-700\">Includi Dati di Collaudo</span>\n                </label>\n                <label className=\"flex items-center space-x-2 cursor-pointer\">\n                  <input\n                    type=\"checkbox\"\n                    checked={config.includePhotos}\n                    onChange={(e) => setConfig(prev => ({ ...prev, includePhotos: e.target.checked }))}\n                    className=\"rounded border-slate-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                  <span className=\"text-sm text-slate-700\">Includi Foto Installazione</span>\n                </label>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"notes\">Note Aggiuntive</Label>\n            <Textarea\n              id=\"notes\"\n              value={config.notes}\n              onChange={(e) => setConfig(prev => ({ ...prev, notes: e.target.value }))}\n              placeholder=\"Note o commenti da includere nel certificato...\"\n              rows={3}\n              className=\"resize-none\"\n            />\n          </div>\n        </div>\n\n        <DialogFooter className=\"gap-3 pt-6 border-t\">\n          <Button\n            variant=\"outline\"\n            onClick={onClose}\n            disabled={isLoading}\n            className=\"px-6\"\n          >\n            <X className=\"w-4 h-4 mr-2\" />\n            Annulla\n          </Button>\n          \n          <Button\n            onClick={handleGenerate}\n            disabled={isLoading || !config.fileName.trim()}\n            className=\"px-6 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700\"\n          >\n            {isLoading ? (\n              <>\n                <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                Generazione...\n              </>\n            ) : (\n              <>\n                <Download className=\"w-4 h-4 mr-2\" />\n                Genera PDF\n              </>\n            )}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n\ninterface CertificationErrorDialogProps {\n  isOpen: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  missingRequirements: string[]\n}\n\nexport function CertificationErrorDialog({\n  isOpen,\n  onClose,\n  cavo,\n  missingRequirements\n}: CertificationErrorDialogProps) {\n  if (!cavo) return null\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-md border-l-4 border-l-red-500\">\n        <DialogHeader className=\"bg-gradient-to-r from-red-50 to-transparent p-6 -m-6 mb-4\">\n          <DialogTitle className=\"flex items-center gap-3 text-xl\">\n            <AlertTriangle className=\"w-6 h-6 text-red-600\" />\n            Impossibile Certificare Cavo\n          </DialogTitle>\n          <DialogDescription className=\"text-base text-slate-600 mt-2\">\n            Il cavo {cavo.id_cavo} non può essere certificato nel suo stato attuale\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          <Alert variant=\"destructive\" className=\"border-red-200 bg-red-50\">\n            <AlertTriangle className=\"h-4 w-4\" />\n            <AlertDescription className=\"font-medium\">\n              Requisiti mancanti per la certificazione:\n            </AlertDescription>\n          </Alert>\n\n          <ul className=\"space-y-2\">\n            {missingRequirements.map((requirement, index) => (\n              <li key={index} className=\"flex items-center gap-2 text-sm text-slate-700\">\n                <X className=\"w-4 h-4 text-red-500 flex-shrink-0\" />\n                {requirement}\n              </li>\n            ))}\n          </ul>\n\n          <div className=\"bg-blue-50 p-4 rounded-lg border border-blue-200\">\n            <h4 className=\"font-medium text-blue-900 mb-2\">💡 Suggerimento</h4>\n            <p className=\"text-sm text-blue-800\">\n              Completa tutti i requisiti sopra elencati prima di procedere con la certificazione. \n              Puoi utilizzare le azioni disponibili nella tabella per aggiornare lo stato del cavo.\n            </p>\n          </div>\n        </div>\n\n        <DialogFooter className=\"pt-6 border-t\">\n          <Button\n            onClick={onClose}\n            className=\"px-6 bg-slate-600 hover:bg-slate-700\"\n          >\n            <Settings className=\"w-4 h-4 mr-2\" />\n            Ho Capito\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AAOA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAlCA;;;;;;;;;;;;AA2CO,SAAS,sBAAsB,EACpC,MAAM,EACN,OAAO,EACP,SAAS,EACT,IAAI,EACuB;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,gBAAgB;QACpB,IAAI,CAAC,MAAM;QAEX,aAAa;QACb,IAAI;YACF,MAAM,UAAU,KAAK,OAAO;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC,kJAAA,CAAA,UAAkB;QACjB,QAAQ;QACR,SAAS;QACT,WAAW;QACX,OAAM;QACN,aAAa,CAAC,uCAAuC,EAAE,KAAK,OAAO,CAAC,yGAAyG,CAAC;QAC9K,aAAY;QACZ,YAAW;QACX,SAAQ;QACR,WAAW;QACX,oBAAM,8OAAC,sMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;;;;;;AAG9B;AAmBO,SAAS,kBAAkB,EAChC,MAAM,EACN,OAAO,EACP,UAAU,EACV,IAAI,EACmB;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;QACxD,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,iBAAiB;QACjB,eAAe;QACf,gBAAgB;QAChB,OAAO;IACT;IAEA,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACP,IAAI,MAAM;YACR,UAAU,CAAA,OAAQ,CAAC;oBACjB,GAAG,IAAI;oBACP,QAAQ,KAAK,OAAO;oBACpB,UAAU,CAAC,YAAY,EAAE,KAAK,OAAO,CAAC,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;gBACvF,CAAC;QACH;IACF,GAAG;QAAC;KAAK;IAET,MAAM,iBAAiB;QACrB,IAAI,CAAC,MAAM;QAEX,aAAa;QACb,IAAI;YACF,MAAM,WAAW;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAA0B;gCACjB,KAAK,OAAO;;;;;;;sCAE3C,8OAAC,kIAAA,CAAA,oBAAiB;4BAAC,WAAU;sCAAgC;;;;;;;;;;;;8BAK/D,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAkC;;;;;;8CAChD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAiB;;;;;;8DACjC,8OAAC;oDAAK,WAAU;8DAAkB,KAAK,OAAO;;;;;;;;;;;;sDAEhD,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAiB;;;;;;8DACjC,8OAAC;oDAAK,WAAU;8DAAQ,KAAK,OAAO,IAAI;;;;;;;;;;;;sDAE1C,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAiB;;;;;;8DACjC,8OAAC;oDAAK,WAAU;8DAAQ,KAAK,SAAS,IAAI;;;;;;;;;;;;sDAE5C,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAiB;;;;;;8DACjC,8OAAC;oDAAK,WAAU;8DAAQ,KAAK,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;sCAM5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,OAAO,QAAQ;4CACtB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CACzE,aAAY;4CACZ,WAAU;;;;;;;;;;;;8CAId,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAS;;;;;;sDACxB,8OAAC,kIAAA,CAAA,SAAM;4CACL,OAAO,OAAO,MAAM;4CACpB,eAAe,CAAC,QAAe,UAAU,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,QAAQ;oDAAM,CAAC;;8DAE5E,8OAAC,kIAAA,CAAA,gBAAa;8DACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;8DAEd,8OAAC,kIAAA,CAAA,gBAAa;;sEACZ,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAW;;;;;;sEAC7B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAW;;;;;;sEAC7B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAU;;;;;;;;;;;;;;;;;;;;;;;;8CAKlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAiB;;;;;;sDAChC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,OAAO,cAAc;oDAC5B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDAC/E,aAAY;oDACZ,WAAU;;;;;;;;;;;;;;;;;;8CAKhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;4DACC,MAAK;4DACL,SAAS,OAAO,eAAe;4DAC/B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,iBAAiB,EAAE,MAAM,CAAC,OAAO;oEAAC,CAAC;4DAClF,WAAU;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEAAyB;;;;;;;;;;;;8DAE3C,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;4DACC,MAAK;4DACL,SAAS,OAAO,aAAa;4DAC7B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,eAAe,EAAE,MAAM,CAAC,OAAO;oEAAC,CAAC;4DAChF,WAAU;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMjD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAQ;;;;;;8CACvB,8OAAC,oIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,OAAO,OAAO,KAAK;oCACnB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACtE,aAAY;oCACZ,MAAM;oCACN,WAAU;;;;;;;;;;;;;;;;;;8BAKhB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;4BACV,WAAU;;8CAEV,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAIhC,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,aAAa,CAAC,OAAO,QAAQ,CAAC,IAAI;4BAC5C,WAAU;sCAET,0BACC;;kDACE,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;6DAInD;;kDACE,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AASrD;AASO,SAAS,yBAAyB,EACvC,MAAM,EACN,OAAO,EACP,IAAI,EACJ,mBAAmB,EACW;IAC9B,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAyB;;;;;;;sCAGpD,8OAAC,kIAAA,CAAA,oBAAiB;4BAAC,WAAU;;gCAAgC;gCAClD,KAAK,OAAO;gCAAC;;;;;;;;;;;;;8BAI1B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAc,WAAU;;8CACrC,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC,iIAAA,CAAA,mBAAgB;oCAAC,WAAU;8CAAc;;;;;;;;;;;;sCAK5C,8OAAC;4BAAG,WAAU;sCACX,oBAAoB,GAAG,CAAC,CAAC,aAAa,sBACrC,8OAAC;oCAAe,WAAU;;sDACxB,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;wCACZ;;mCAFM;;;;;;;;;;sCAOb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;8BAOzC,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;8BACtB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,WAAU;;0CAEV,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 1222, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/cavi/CaviStatistics.tsx"], "sourcesContent": ["'use client'\n\nimport { useMemo, useState } from 'react'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  Cable,\n  CheckCircle,\n  Clock,\n  AlertTriangle,\n  Zap,\n  Package,\n  BarChart3\n} from 'lucide-react'\nimport { Cavo } from '@/types'\n\n\n\ninterface CaviStatisticsProps {\n  cavi: Cavo[]\n  filteredCavi: Cavo[]\n  className?: string\n  revisioneCorrente?: string\n}\n\nexport default function CaviStatistics({\n  cavi,\n  filteredCavi,\n  className,\n  revisioneCorrente\n}: CaviStatisticsProps) {\n  const stats = useMemo(() => {\n    const totalCavi = cavi.length\n    const filteredCount = filteredCavi.length\n    \n    // Installation status\n    const installati = filteredCavi.filter(c => \n      c.stato_installazione === 'Installato' || \n      (c.metri_posati && c.metri_posati > 0) ||\n      (c.metratura_reale && c.metratura_reale > 0)\n    ).length\n    \n    const inCorso = filteredCavi.filter(c => \n      c.stato_installazione === 'In corso'\n    ).length\n    \n    const daInstallare = filteredCount - installati - inCorso\n    \n    // Connection status\n    const collegati = filteredCavi.filter(c => {\n      const collegamento = c.collegamento || c.collegamenti || 0\n      return collegamento === 3 // Both sides connected\n    }).length\n    \n    const parzialmenteCollegati = filteredCavi.filter(c => {\n      const collegamento = c.collegamento || c.collegamenti || 0\n      return collegamento === 1 || collegamento === 2 // One side connected\n    }).length\n    \n    const nonCollegati = filteredCavi.filter(c => {\n      const collegamento = c.collegamento || c.collegamenti || 0\n      return collegamento === 0 && (c.metri_posati > 0 || c.metratura_reale > 0)\n    }).length\n    \n    // Certification status\n    const certificati = filteredCavi.filter(c => \n      c.certificato === true || \n      c.certificato === 'SI' || \n      c.certificato === 'CERTIFICATO'\n    ).length\n    \n    // Meters calculation\n    const metriTotali = filteredCavi.reduce((sum, c) => sum + (c.metri_teorici || 0), 0)\n    const metriInstallati = filteredCavi.reduce((sum, c) => {\n      const metri = c.metri_posati || c.metratura_reale || 0\n      return sum + metri\n    }, 0)\n\n    // Calcolo IAP (Indice di Avanzamento Ponderato) come nella webapp originale\n    const calculateIAP = (nTot: number, nInst: number, nColl: number, nCert: number): number => {\n      // Pesi per le fasi del progetto\n      const Wp = 2.0  // Peso fase Posa\n      const Wc = 1.5  // Peso fase Collegamento\n      const Wz = 0.5  // Peso fase Certificazione\n\n      // Se non ci sono cavi, ritorna 0\n      if (nTot === 0) return 0\n\n      // Calcolo del numeratore (Sforzo Completato)\n      const sforzoSoloInstallati = (nInst - nColl) * Wp\n      const sforzoSoloCollegati = (nColl - nCert) * (Wp + Wc)\n      const sforzoCertificati = nCert * (Wp + Wc + Wz)\n      const numeratore = sforzoSoloInstallati + sforzoSoloCollegati + sforzoCertificati\n\n      // Calcolo del denominatore (Sforzo Massimo Previsto)\n      const denominatore = nTot * (Wp + Wc + Wz)\n\n      // Calcolo finale dell'IAP in percentuale\n      const iap = (numeratore / denominatore) * 100\n\n      return Math.round(iap * 100) / 100 // Arrotonda a 2 decimali\n    }\n\n    const percentualeInstallazione = calculateIAP(filteredCount, installati, collegati, certificati)\n    \n    return {\n      totalCavi,\n      filteredCount,\n      installati,\n      inCorso,\n      daInstallare,\n      collegati,\n      parzialmenteCollegati,\n      nonCollegati,\n      certificati,\n      metriTotali,\n      metriInstallati,\n      percentualeInstallazione\n    }\n  }, [cavi, filteredCavi])\n\n  // Componente KPI interattivo\n\n\n  return (\n    <Card className={className}>\n      <CardContent className=\"p-1.5\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between mb-1\">\n          <div className=\"flex items-center space-x-1.5\">\n            <BarChart3 className=\"h-3.5 w-3.5 text-mariner-600\" />\n            <span className=\"text-xs font-semibold text-mariner-900\">Statistiche Cavi</span>\n            {revisioneCorrente && (\n              <Badge variant=\"outline\" className=\"text-xs bg-blue-50 text-blue-700 border-blue-200\">\n                Rev. {revisioneCorrente}\n              </Badge>\n            )}\n          </div>\n        </div>\n\n        {/* Statistics in single row */}\n        <div className=\"flex flex-wrap gap-2 justify-between\">\n\n          {/* Total cavi */}\n          <div className=\"flex items-center space-x-1.5 bg-mariner-50 px-1.5 py-1 rounded-lg\">\n            <Cable className=\"h-3.5 w-3.5 text-mariner-600\" />\n            <div>\n              <div className=\"font-bold text-mariner-900 text-sm\">{stats.filteredCount}</div>\n              <div className=\"text-xs text-mariner-600\">di {stats.totalCavi} cavi</div>\n            </div>\n          </div>\n\n          {/* Installati */}\n          <div className=\"flex items-center space-x-1.5 bg-green-50 px-1.5 py-1 rounded-lg\">\n            <CheckCircle className=\"h-3.5 w-3.5 text-green-600\" />\n            <div>\n              <div className=\"font-bold text-green-700 text-sm\">{stats.installati}</div>\n              <div className=\"text-xs text-green-600\">installati</div>\n            </div>\n          </div>\n\n          {/* In corso */}\n          <div className=\"flex items-center space-x-1.5 bg-yellow-50 px-1.5 py-1 rounded-lg\">\n            <Clock className=\"h-3.5 w-3.5 text-yellow-600\" />\n            <div>\n              <div className=\"font-bold text-yellow-700 text-sm\">{stats.inCorso}</div>\n              <div className=\"text-xs text-yellow-600\">in corso</div>\n            </div>\n          </div>\n\n          {/* Da installare */}\n          <div className=\"flex items-center space-x-1.5 bg-gray-50 px-1.5 py-1 rounded-lg\">\n            <AlertTriangle className=\"h-3.5 w-3.5 text-gray-600\" />\n            <div>\n              <div className=\"font-bold text-gray-700 text-sm\">{stats.daInstallare}</div>\n              <div className=\"text-xs text-gray-600\">da installare</div>\n            </div>\n          </div>\n\n          {/* Collegati */}\n          <div className=\"flex items-center space-x-1.5 bg-blue-50 px-1.5 py-1 rounded-lg\">\n            <Zap className=\"h-3.5 w-3.5 text-blue-600\" />\n            <div>\n              <div className=\"font-bold text-blue-700 text-sm\">{stats.collegati}</div>\n              <div className=\"text-xs text-blue-600\">collegati</div>\n            </div>\n          </div>\n\n          {/* Certificati */}\n          <div className=\"flex items-center space-x-1.5 bg-purple-50 px-1.5 py-1 rounded-lg\">\n            <Package className=\"h-3.5 w-3.5 text-purple-600\" />\n            <div>\n              <div className=\"font-bold text-purple-700 text-sm\">{stats.certificati}</div>\n              <div className=\"text-xs text-purple-600\">certificati</div>\n            </div>\n          </div>\n\n          {/* Meters progress */}\n          <div className=\"flex items-center space-x-1.5 bg-indigo-50 px-1.5 py-1 rounded-lg\">\n            <div className=\"h-3.5 w-3.5 flex items-center justify-center\">\n              <div className=\"h-2 w-2 bg-indigo-600 rounded-full\"></div>\n            </div>\n            <div>\n              <div className=\"font-bold text-indigo-700 text-sm\">{stats.metriInstallati.toLocaleString()}m</div>\n              <div className=\"text-xs text-indigo-600\">di {stats.metriTotali.toLocaleString()}m</div>\n            </div>\n          </div>\n\n        </div>\n\n        {/* Progress bar - Colori morbidi */}\n        {stats.filteredCount > 0 && (\n          <div className=\"mt-2 bg-gray-50 p-2 rounded-lg\">\n            <div className=\"flex justify-between text-xs font-medium text-gray-700 mb-1\">\n              <span>Avanzamento Complessivo Cavi</span>\n              <span className={`font-bold ${\n                stats.percentualeInstallazione >= 80 ? 'text-amber-700' :\n                stats.percentualeInstallazione >= 60 ? 'text-orange-700' :\n                stats.percentualeInstallazione >= 40 ? 'text-yellow-700' : 'text-emerald-700'\n              }`}>\n                {stats.percentualeInstallazione.toFixed(1)}%\n              </span>\n            </div>\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <div\n                className={`h-2 rounded-full transition-all duration-500 ease-in-out ${\n                  stats.percentualeInstallazione >= 80 ? 'bg-gradient-to-r from-amber-500 to-amber-600' :\n                  stats.percentualeInstallazione >= 60 ? 'bg-gradient-to-r from-orange-500 to-orange-600' :\n                  stats.percentualeInstallazione >= 40 ? 'bg-gradient-to-r from-yellow-500 to-yellow-600' :\n                  'bg-gradient-to-r from-emerald-500 to-emerald-600'\n                }`}\n                style={{ width: `${Math.min(stats.percentualeInstallazione, 100)}%` }}\n              />\n            </div>\n            <div className=\"flex justify-between text-xs text-gray-500 mt-0.5\">\n              <span>Metri installati vs totali disponibili</span>\n              <span>{(stats.metriTotali - stats.metriInstallati).toLocaleString()}m rimanenti</span>\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAyBe,SAAS,eAAe,EACrC,IAAI,EACJ,YAAY,EACZ,SAAS,EACT,iBAAiB,EACG;IACpB,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACpB,MAAM,YAAY,KAAK,MAAM;QAC7B,MAAM,gBAAgB,aAAa,MAAM;QAEzC,sBAAsB;QACtB,MAAM,aAAa,aAAa,MAAM,CAAC,CAAA,IACrC,EAAE,mBAAmB,KAAK,gBACzB,EAAE,YAAY,IAAI,EAAE,YAAY,GAAG,KACnC,EAAE,eAAe,IAAI,EAAE,eAAe,GAAG,GAC1C,MAAM;QAER,MAAM,UAAU,aAAa,MAAM,CAAC,CAAA,IAClC,EAAE,mBAAmB,KAAK,YAC1B,MAAM;QAER,MAAM,eAAe,gBAAgB,aAAa;QAElD,oBAAoB;QACpB,MAAM,YAAY,aAAa,MAAM,CAAC,CAAA;YACpC,MAAM,eAAe,EAAE,YAAY,IAAI,EAAE,YAAY,IAAI;YACzD,OAAO,iBAAiB,EAAE,uBAAuB;;QACnD,GAAG,MAAM;QAET,MAAM,wBAAwB,aAAa,MAAM,CAAC,CAAA;YAChD,MAAM,eAAe,EAAE,YAAY,IAAI,EAAE,YAAY,IAAI;YACzD,OAAO,iBAAiB,KAAK,iBAAiB,EAAE,qBAAqB;;QACvE,GAAG,MAAM;QAET,MAAM,eAAe,aAAa,MAAM,CAAC,CAAA;YACvC,MAAM,eAAe,EAAE,YAAY,IAAI,EAAE,YAAY,IAAI;YACzD,OAAO,iBAAiB,KAAK,CAAC,EAAE,YAAY,GAAG,KAAK,EAAE,eAAe,GAAG,CAAC;QAC3E,GAAG,MAAM;QAET,uBAAuB;QACvB,MAAM,cAAc,aAAa,MAAM,CAAC,CAAA,IACtC,EAAE,WAAW,KAAK,QAClB,EAAE,WAAW,KAAK,QAClB,EAAE,WAAW,KAAK,eAClB,MAAM;QAER,qBAAqB;QACrB,MAAM,cAAc,aAAa,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,aAAa,IAAI,CAAC,GAAG;QAClF,MAAM,kBAAkB,aAAa,MAAM,CAAC,CAAC,KAAK;YAChD,MAAM,QAAQ,EAAE,YAAY,IAAI,EAAE,eAAe,IAAI;YACrD,OAAO,MAAM;QACf,GAAG;QAEH,4EAA4E;QAC5E,MAAM,eAAe,CAAC,MAAc,OAAe,OAAe;YAChE,gCAAgC;YAChC,MAAM,KAAK,IAAK,iBAAiB;;YACjC,MAAM,KAAK,IAAK,yBAAyB;;YACzC,MAAM,KAAK,IAAK,2BAA2B;;YAE3C,iCAAiC;YACjC,IAAI,SAAS,GAAG,OAAO;YAEvB,6CAA6C;YAC7C,MAAM,uBAAuB,CAAC,QAAQ,KAAK,IAAI;YAC/C,MAAM,sBAAsB,CAAC,QAAQ,KAAK,IAAI,CAAC,KAAK,EAAE;YACtD,MAAM,oBAAoB,QAAQ,CAAC,KAAK,KAAK,EAAE;YAC/C,MAAM,aAAa,uBAAuB,sBAAsB;YAEhE,qDAAqD;YACrD,MAAM,eAAe,OAAO,CAAC,KAAK,KAAK,EAAE;YAEzC,yCAAyC;YACzC,MAAM,MAAM,AAAC,aAAa,eAAgB;YAE1C,OAAO,KAAK,KAAK,CAAC,MAAM,OAAO,IAAI,yBAAyB;;QAC9D;QAEA,MAAM,2BAA2B,aAAa,eAAe,YAAY,WAAW;QAEpF,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;IACF,GAAG;QAAC;QAAM;KAAa;IAEvB,6BAA6B;IAG7B,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW;kBACf,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;;8BAErB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,8OAAC;gCAAK,WAAU;0CAAyC;;;;;;4BACxD,mCACC,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;;oCAAmD;oCAC9E;;;;;;;;;;;;;;;;;;8BAOd,8OAAC;oBAAI,WAAU;;sCAGb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAsC,MAAM,aAAa;;;;;;sDACxE,8OAAC;4CAAI,WAAU;;gDAA2B;gDAAI,MAAM,SAAS;gDAAC;;;;;;;;;;;;;;;;;;;sCAKlE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAoC,MAAM,UAAU;;;;;;sDACnE,8OAAC;4CAAI,WAAU;sDAAyB;;;;;;;;;;;;;;;;;;sCAK5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAqC,MAAM,OAAO;;;;;;sDACjE,8OAAC;4CAAI,WAAU;sDAA0B;;;;;;;;;;;;;;;;;;sCAK7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAmC,MAAM,YAAY;;;;;;sDACpE,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAK3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAmC,MAAM,SAAS;;;;;;sDACjE,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAK3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAqC,MAAM,WAAW;;;;;;sDACrE,8OAAC;4CAAI,WAAU;sDAA0B;;;;;;;;;;;;;;;;;;sCAK7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;;8CAEjB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;;gDAAqC,MAAM,eAAe,CAAC,cAAc;gDAAG;;;;;;;sDAC3F,8OAAC;4CAAI,WAAU;;gDAA0B;gDAAI,MAAM,WAAW,CAAC,cAAc;gDAAG;;;;;;;;;;;;;;;;;;;;;;;;;gBAOrF,MAAM,aAAa,GAAG,mBACrB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAK;;;;;;8CACN,8OAAC;oCAAK,WAAW,CAAC,UAAU,EAC1B,MAAM,wBAAwB,IAAI,KAAK,mBACvC,MAAM,wBAAwB,IAAI,KAAK,oBACvC,MAAM,wBAAwB,IAAI,KAAK,oBAAoB,oBAC3D;;wCACC,MAAM,wBAAwB,CAAC,OAAO,CAAC;wCAAG;;;;;;;;;;;;;sCAG/C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAW,CAAC,yDAAyD,EACnE,MAAM,wBAAwB,IAAI,KAAK,iDACvC,MAAM,wBAAwB,IAAI,KAAK,mDACvC,MAAM,wBAAwB,IAAI,KAAK,mDACvC,oDACA;gCACF,OAAO;oCAAE,OAAO,GAAG,KAAK,GAAG,CAAC,MAAM,wBAAwB,EAAE,KAAK,CAAC,CAAC;gCAAC;;;;;;;;;;;sCAGxE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAK;;;;;;8CACN,8OAAC;;wCAAM,CAAC,MAAM,WAAW,GAAG,MAAM,eAAe,EAAE,cAAc;wCAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlF", "debugId": null}}, {"offset": {"line": 1768, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/cavi/ModificaBobinaDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>ontent,\n  <PERSON><PERSON><PERSON>ooter,\n  <PERSON><PERSON>Header,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Badge } from '@/components/ui/badge'\nimport { Loader2, AlertCircle, Package, Search, CheckCircle, AlertTriangle } from 'lucide-react'\nimport { Cavo, Bobina } from '@/types'\nimport { parcoCaviApi, caviApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface ModificaBobinaDialogProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  cantiere?: { id_cantiere: string; nome_cantiere: string } | null\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\nexport default function ModificaBobinaD<PERSON>og({\n  open,\n  onClose,\n  cavo,\n  cantiere: cantiereProp,\n  onSuccess,\n  onError\n}: ModificaBobinaDialogProps) {\n  const { cantiere: cantiereFromContext } = useAuth()\n  const cantiere = cantiereProp || cantiereFromContext\n\n  const [selectedOption, setSelectedOption] = useState<string>('assegna_nuova')\n  const [selectedBobina, setSelectedBobina] = useState<string>('')\n  const [bobine, setBobine] = useState<Bobina[]>([])\n  const [loadingBobine, setLoadingBobine] = useState(false)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState<string>('')\n  const [searchText, setSearchText] = useState('')\n  const [activeTab, setActiveTab] = useState<'compatibili' | 'incompatibili'>('compatibili')\n\n  // Reset form quando il dialog si apre/chiude\n  useEffect(() => {\n    if (open) {\n      setSelectedOption('assegna_nuova')\n      setSelectedBobina('')\n      setSearchText('')\n      setActiveTab('compatibili')\n      setError('')\n      if (cantiere?.id_cantiere) {\n        loadBobine()\n      }\n    }\n  }, [open, cantiere?.id_cantiere])\n\n  const loadBobine = async () => {\n    if (!cantiere?.id_cantiere) {\n      setError('Cantiere non disponibile')\n      return\n    }\n\n    try {\n      setLoadingBobine(true)\n      setError('')\n\n      console.log('🔄 ModificaBobinaDialog: Caricamento bobine per cantiere:', cantiere.id_cantiere)\n\n      // Usa la stessa logica di InserisciMetriDialog\n      const response = await parcoCaviApi.getBobine(cantiere.id_cantiere)\n\n      // Gestisce diversi formati di risposta - STESSA LOGICA DI InserisciMetriDialog\n      let bobineData = []\n      if (Array.isArray(response)) {\n        bobineData = response\n      } else if (response && Array.isArray(response.data)) {\n        bobineData = response.data\n      } else if (response && response.bobine && Array.isArray(response.bobine)) {\n        bobineData = response.bobine\n      } else {\n        throw new Error('Formato risposta API non valido')\n      }\n\n      // Filtra solo per stato (disponibile o in uso) e metri residui > 0 - STESSA LOGICA DI InserisciMetriDialog\n      const bobineUtilizzabili = bobineData.filter((bobina) =>\n        bobina.stato_bobina !== 'Terminata' &&\n        bobina.stato_bobina !== 'Over' &&\n        bobina.metri_residui > 0\n      )\n\n      setBobine(bobineUtilizzabili)\n      console.log('✅ ModificaBobinaDialog: Bobine caricate:', bobineUtilizzabili.length)\n      console.log('📋 ModificaBobinaDialog: Dettaglio bobine:', bobineUtilizzabili.map(b => ({\n        id: b.id_bobina,\n        tipologia: b.tipologia,\n        sezione: b.sezione,\n        metri_residui: b.metri_residui,\n        stato: b.stato_bobina\n      })))\n    } catch (error) {\n      console.error('❌ ModificaBobinaDialog: Errore caricamento bobine:', error)\n      setError('Errore nel caricamento delle bobine')\n      setBobine([])\n    } finally {\n      setLoadingBobine(false)\n    }\n  }\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = (idBobina: string) => {\n    if (!idBobina || idBobina === 'BOBINA_VUOTA') return 'VUOTA'\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1]\n    }\n    const bobina = bobine.find(b => b.id_bobina === idBobina)\n    return bobina ? bobina.numero_bobina || idBobina : idBobina\n  }\n\n  // Filtra le bobine compatibili\n  const getBobineCompatibili = () => {\n    if (!cavo) return []\n\n    const compatibili = bobine.filter(bobina => {\n      const isCompatible = bobina.tipologia === cavo.tipologia &&\n                          bobina.sezione === cavo.sezione\n      const matchesSearch = searchText === '' ||\n                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase()) ||\n                           (bobina.tipologia && bobina.tipologia.toLowerCase().includes(searchText.toLowerCase())) ||\n                           (bobina.numero_bobina && bobina.numero_bobina.toLowerCase().includes(searchText.toLowerCase()))\n      return isCompatible && matchesSearch && bobina.metri_residui > 0\n    })\n\n    console.log('🔍 ModificaBobinaDialog: Filtro compatibili:', {\n      cavoTipologia: cavo.tipologia,\n      cavoSezione: cavo.sezione,\n      totaleBobine: bobine.length,\n      bobineCompatibili: compatibili.length,\n      searchText\n    })\n\n    return compatibili\n  }\n\n  // Filtra le bobine incompatibili\n  const getBobineIncompatibili = () => {\n    if (!cavo) return []\n\n    return bobine.filter(bobina => {\n      const isIncompatible = bobina.tipologia !== cavo.tipologia ||\n                            bobina.sezione !== cavo.sezione\n      const matchesSearch = searchText === '' ||\n                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase()) ||\n                           (bobina.tipologia && bobina.tipologia.toLowerCase().includes(searchText.toLowerCase())) ||\n                           (bobina.numero_bobina && bobina.numero_bobina.toLowerCase().includes(searchText.toLowerCase()))\n      return isIncompatible && matchesSearch && bobina.metri_residui > 0\n    })\n  }\n\n  const bobineCompatibili = getBobineCompatibili()\n  const bobineIncompatibili = getBobineIncompatibili()\n\n  const handleClose = () => {\n    setSelectedOption('assegna_nuova')\n    setSelectedBobina('')\n    setSearchText('')\n    setError('')\n    onClose()\n  }\n\n  const handleSave = async () => {\n    console.log('🔄 ModificaBobinaDialog: Salvataggio:', {\n      selectedOption,\n      selectedBobina,\n      cavoId: cavo?.id_cavo,\n      cantiereId: cantiere?.id_cantiere\n    })\n\n    if (!cavo) {\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError('')\n\n      if (selectedOption === 'assegna_nuova') {\n        if (!selectedBobina) {\n          onError('Selezionare una bobina')\n          return\n        }\n\n        // Usa updateMetriPosati come in InserisciMetriDialog\n        const result = await caviApi.updateMetriPosati({\n          id_cavo: cavo.id_cavo,\n          metri_posati: cavo.metratura_reale || 0, // Mantieni i metri attuali\n          id_bobina: selectedBobina,\n          force_over: true // Permetti operazioni incompatibili\n        })\n\n        if (result.success) {\n          onSuccess(`Bobina aggiornata con successo per il cavo ${cavo.id_cavo}`)\n          handleClose()\n        } else {\n          onError(result.message || 'Errore durante l\\'aggiornamento della bobina')\n        }\n      } else if (selectedOption === 'rimuovi_bobina') {\n        // Assegna BOBINA_VUOTA\n        const result = await caviApi.updateMetriPosati({\n          id_cavo: cavo.id_cavo,\n          metri_posati: cavo.metratura_reale || 0,\n          id_bobina: 'BOBINA_VUOTA',\n          force_over: false\n        })\n\n        if (result.success) {\n          onSuccess(`Bobina rimossa dal cavo ${cavo.id_cavo}`)\n          handleClose()\n        } else {\n          onError(result.message || 'Errore durante la rimozione della bobina')\n        }\n      } else if (selectedOption === 'annulla_installazione') {\n        // Reset completo del cavo\n        const result = await caviApi.updateMetriPosati({\n          id_cavo: cavo.id_cavo,\n          metri_posati: 0,\n          id_bobina: 'BOBINA_VUOTA',\n          force_over: false\n        })\n\n        if (result.success) {\n          onSuccess(`Installazione annullata per il cavo ${cavo.id_cavo}`)\n          handleClose()\n        } else {\n          onError(result.message || 'Errore durante l\\'annullamento dell\\'installazione')\n        }\n      }\n    } catch (error) {\n      console.error('❌ ModificaBobinaDialog: Errore salvataggio:', error)\n      onError('Errore durante il salvataggio')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (!cavo) return null\n\n  return (\n    <>\n      <Dialog open={open} onOpenChange={handleClose}>\n        <DialogContent className=\"max-w-4xl max-h-[90vh] flex flex-col\">\n          <DialogHeader>\n            <DialogTitle>\n              Modifica Bobina Cavo {cavo.id_cavo}\n            </DialogTitle>\n          </DialogHeader>\n\n          <div className=\"flex-1 overflow-hidden space-y-6\">\n            {/* Sezione Cavo Selezionato */}\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center space-x-2\">\n                <Package className=\"h-5 w-5 text-blue-600\" />\n                <h3 className=\"font-medium text-gray-900\">Cavo Selezionato</h3>\n              </div>\n\n              {/* Informazioni Cavo - Formato migliorato */}\n              <div className=\"p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border-2 border-blue-200 shadow-sm\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3 text-sm\">\n                  <div className=\"space-y-2\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"font-semibold text-blue-700 min-w-[80px]\">Tipologia:</span>\n                      <span className=\"font-medium text-gray-800\">{cavo.tipologia || 'N/A'}</span>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"font-semibold text-blue-700 min-w-[80px]\">Formazione:</span>\n                      <span className=\"font-medium text-gray-800\">{cavo.sezione || 'N/A'}</span>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"font-semibold text-blue-700 min-w-[80px]\">Colore:</span>\n                      <span className=\"font-medium text-gray-800\">{cavo.colore_cavo || 'N/A'}</span>\n                    </div>\n                  </div>\n                  <div className=\"space-y-2\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"font-semibold text-blue-700 min-w-[80px]\">Da:</span>\n                      <span className=\"font-medium text-gray-800\">{cavo.ubicazione_partenza || 'N/A'}</span>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"font-semibold text-blue-700 min-w-[80px]\">A:</span>\n                      <span className=\"font-medium text-gray-800\">{cavo.ubicazione_arrivo || 'N/A'}</span>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"font-semibold text-blue-700 min-w-[80px]\">Metri:</span>\n                      <span className=\"font-bold text-green-700\">{cavo.metratura_reale || 0} m posati</span>\n                      <span className=\"text-gray-500\">/ {cavo.metratura_teorica || 0} m teorici</span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Bobina attuale */}\n                {cavo.id_bobina && cavo.id_bobina !== 'BOBINA_VUOTA' && (\n                  <div className=\"mt-3 pt-3 border-t border-blue-200\">\n                    <div className=\"flex items-center space-x-2\">\n                      <Package className=\"h-4 w-4 text-blue-600\" />\n                      <span className=\"font-semibold text-blue-700\">Bobina Attuale:</span>\n                      <span className=\"font-medium text-gray-800\">{getBobinaNumber(cavo.id_bobina)}</span>\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Sezione Opzioni di modifica */}\n            <div className=\"space-y-3\">\n              <h3 className=\"font-medium\">Opzioni di modifica</h3>\n\n              <div className=\"space-y-2\">\n                <label className=\"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50\">\n                  <input\n                    type=\"radio\"\n                    name=\"operazione\"\n                    value=\"assegna_nuova\"\n                    checked={selectedOption === 'assegna_nuova'}\n                    onChange={(e) => setSelectedOption(e.target.value)}\n                    className=\"w-4 h-4 text-blue-600\"\n                  />\n                  <span className=\"text-sm\">Cambia bobina</span>\n                </label>\n\n                <label className=\"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50\">\n                  <input\n                    type=\"radio\"\n                    name=\"operazione\"\n                    value=\"rimuovi_bobina\"\n                    checked={selectedOption === 'rimuovi_bobina'}\n                    onChange={(e) => setSelectedOption(e.target.value)}\n                    className=\"w-4 h-4 text-blue-600\"\n                  />\n                  <span className=\"text-sm\">Bobina vuota</span>\n                </label>\n\n                <label className=\"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-red-50\">\n                  <input\n                    type=\"radio\"\n                    name=\"operazione\"\n                    value=\"annulla_installazione\"\n                    checked={selectedOption === 'annulla_installazione'}\n                    onChange={(e) => setSelectedOption(e.target.value)}\n                    className=\"w-4 h-4 text-red-600\"\n                  />\n                  <span className=\"text-sm text-red-600 font-medium\">Annulla posa (reset completo)</span>\n                </label>\n\n                <label className=\"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50\">\n                  <input\n                    type=\"radio\"\n                    name=\"operazione\"\n                    value=\"annulla_installazione\"\n                    checked={selectedOption === 'annulla_installazione'}\n                    onChange={(e) => setSelectedOption(e.target.value)}\n                    className=\"w-4 h-4 text-blue-600\"\n                  />\n                  <span className=\"text-sm text-red-600\">Annulla posa</span>\n                </label>\n              </div>\n            </div>\n\n            {/* Sezione Seleziona bobina - solo se \"Assegna nuova bobina\" è selezionato */}\n            {selectedOption === 'assegna_nuova' && (\n              <div className=\"space-y-3\">\n                <h3 className=\"font-medium\">Seleziona bobina</h3>\n\n                {/* Campo ricerca */}\n                <div className=\"relative\">\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n                  <Input\n                    placeholder=\"Cerca bobina per ID, tipologia o numero...\"\n                    value={searchText}\n                    onChange={(e) => setSearchText(e.target.value)}\n                    className=\"pl-10\"\n                  />\n                </div>\n\n                {/* Tab per bobine compatibili/incompatibili */}\n                <div className=\"flex space-x-1 border-b\">\n                  <button\n                    onClick={() => setActiveTab('compatibili')}\n                    className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${\n                      activeTab === 'compatibili'\n                        ? 'border-green-500 text-green-600 bg-green-50'\n                        : 'border-transparent text-gray-500 hover:text-gray-700'\n                    }`}\n                  >\n                    <div className=\"flex items-center space-x-2\">\n                      <CheckCircle className=\"h-4 w-4\" />\n                      <span>Bobine Compatibili ({bobineCompatibili.length})</span>\n                    </div>\n                  </button>\n                  <button\n                    onClick={() => setActiveTab('incompatibili')}\n                    className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${\n                      activeTab === 'incompatibili'\n                        ? 'border-orange-500 text-orange-600 bg-orange-50'\n                        : 'border-transparent text-gray-500 hover:text-gray-700'\n                    }`}\n                  >\n                    <div className=\"flex items-center space-x-2\">\n                      <AlertCircle className=\"h-4 w-4\" />\n                      <span>Bobine Incompatibili ({bobineIncompatibili.length})</span>\n                    </div>\n                  </button>\n                </div>\n\n                {/* Lista bobine */}\n                <div className=\"border rounded-lg h-64 overflow-y-auto\">\n                  {loadingBobine ? (\n                    <div className=\"flex items-center justify-center h-full\">\n                      <div className=\"flex items-center space-x-2\">\n                        <Loader2 className=\"h-4 w-4 animate-spin\" />\n                        <span className=\"text-sm text-gray-600\">Caricamento bobine...</span>\n                      </div>\n                    </div>\n                  ) : (\n                    <div className=\"p-2\">\n                      {activeTab === 'compatibili' ? (\n                        bobineCompatibili.length === 0 ? (\n                          <div className=\"text-center py-8\">\n                            <div className=\"text-gray-500 text-sm mb-2\">\n                              Nessuna bobina compatibile trovata\n                            </div>\n                            <div className=\"text-xs text-gray-400\">\n                              Cercando bobine con tipologia <strong>{cavo.tipologia}</strong> e formazione <strong>{cavo.sezione}</strong>\n                            </div>\n                          </div>\n                        ) : (\n                          <div className=\"space-y-2\">\n                            {bobineCompatibili.map((bobina) => (\n                              <div\n                                key={bobina.id_bobina}\n                                onClick={() => setSelectedBobina(bobina.id_bobina)}\n                                className={`p-3 rounded-lg cursor-pointer transition-all duration-200 ${\n                                  selectedBobina === bobina.id_bobina\n                                    ? 'bg-blue-100 border-2 border-blue-300 shadow-md'\n                                    : 'hover:bg-gray-50 border border-gray-200 hover:border-gray-300'\n                                }`}\n                              >\n                                <div className=\"flex justify-between items-start\">\n                                  <div className=\"flex-1\">\n                                    <div className=\"flex items-center space-x-2 mb-1\">\n                                      <div className=\"font-medium text-sm text-gray-900\">{bobina.id_bobina}</div>\n                                      {bobina.stato_bobina && (\n                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                                          bobina.stato_bobina === 'Disponibile' ? 'bg-green-100 text-green-800' :\n                                          bobina.stato_bobina === 'In uso' ? 'bg-blue-100 text-blue-800' :\n                                          'bg-yellow-100 text-yellow-800'\n                                        }`}>\n                                          {bobina.stato_bobina}\n                                        </span>\n                                      )}\n                                    </div>\n                                    <div className=\"text-xs text-gray-500 mb-1\">\n                                      <span className=\"font-medium\">{bobina.tipologia}</span> • <span>{bobina.sezione}</span>\n                                    </div>\n                                  </div>\n                                  <div className=\"text-right ml-3\">\n                                    <div className={`text-sm font-medium ${\n                                      bobina.metri_residui > 0 ? 'text-green-600' : 'text-gray-500'\n                                    }`}>\n                                      {bobina.metri_residui}m\n                                    </div>\n                                    <div className=\"text-xs text-gray-400\">\n                                      {bobina.metri_residui > 0 ? 'disponibili' : 'esaurita'}\n                                    </div>\n                                  </div>\n                                </div>\n                              </div>\n                            ))}\n                          </div>\n                        )\n                      ) : (\n                        bobineIncompatibili.length === 0 ? (\n                          <div className=\"text-center py-8 text-gray-500 text-sm\">\n                            Nessuna bobina incompatibile trovata\n                          </div>\n                        ) : (\n                          <div className=\"space-y-2\">\n                            {/* Avviso per bobine incompatibili */}\n                            <div className=\"p-3 bg-orange-50 border border-orange-200 rounded-lg\">\n                              <div className=\"flex items-start space-x-2\">\n                                <AlertCircle className=\"h-4 w-4 text-orange-600 mt-0.5 flex-shrink-0\" />\n                                <div className=\"text-sm text-orange-800\">\n                                  <div className=\"font-medium mb-1\">Bobine Incompatibili</div>\n                                  <div className=\"text-xs\">\n                                    Selezionando una bobina incompatibile, le caratteristiche del cavo verranno aggiornate.\n                                  </div>\n                                </div>\n                              </div>\n                            </div>\n\n                            {bobineIncompatibili.map((bobina) => (\n                              <div\n                                key={bobina.id_bobina}\n                                onClick={() => setSelectedBobina(bobina.id_bobina)}\n                                className={`p-3 rounded-lg cursor-pointer transition-all duration-200 ${\n                                  selectedBobina === bobina.id_bobina\n                                    ? 'bg-orange-100 border-2 border-orange-300 shadow-md'\n                                    : 'hover:bg-gray-50 border border-gray-200 hover:border-gray-300'\n                                }`}\n                              >\n                                <div className=\"flex justify-between items-start\">\n                                  <div className=\"flex-1\">\n                                    <div className=\"flex items-center space-x-2 mb-1\">\n                                      <div className=\"font-medium text-sm text-gray-900\">{bobina.id_bobina}</div>\n                                      <span className=\"px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800\">\n                                        INCOMPATIBILE\n                                      </span>\n                                    </div>\n                                    <div className=\"text-xs text-gray-500 mb-1\">\n                                      <span className=\"font-medium\">{bobina.tipologia}</span> • <span>{bobina.sezione}</span>\n                                    </div>\n                                  </div>\n                                  <div className=\"text-right ml-3\">\n                                    <div className={`text-sm font-medium ${\n                                      bobina.metri_residui > 0 ? 'text-orange-600' : 'text-gray-500'\n                                    }`}>\n                                      {bobina.metri_residui}m\n                                    </div>\n                                    <div className=\"text-xs text-gray-400\">\n                                      {bobina.metri_residui > 0 ? 'disponibili' : 'esaurita'}\n                                    </div>\n                                  </div>\n                                </div>\n                              </div>\n                            ))}\n                          </div>\n                        )\n                      )}\n                    </div>\n                  )}\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Errori */}\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          <DialogFooter className=\"flex justify-end space-x-2\">\n            <Button variant=\"outline\" onClick={handleClose} disabled={loading}>\n              Annulla\n            </Button>\n            <Button\n              onClick={handleSave}\n              disabled={loading || (selectedOption === 'assegna_nuova' && !selectedBobina)}\n            >\n              {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n              Salva\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAjBA;;;;;;;;;;AA4Be,SAAS,qBAAqB,EAC3C,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,UAAU,YAAY,EACtB,SAAS,EACT,OAAO,EACmB;IAC1B,MAAM,EAAE,UAAU,mBAAmB,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChD,MAAM,WAAW,gBAAgB;IAEjC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;IAE5E,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,kBAAkB;YAClB,kBAAkB;YAClB,cAAc;YACd,aAAa;YACb,SAAS;YACT,IAAI,UAAU,aAAa;gBACzB;YACF;QACF;IACF,GAAG;QAAC;QAAM,UAAU;KAAY;IAEhC,MAAM,aAAa;QACjB,IAAI,CAAC,UAAU,aAAa;YAC1B,SAAS;YACT;QACF;QAEA,IAAI;YACF,iBAAiB;YACjB,SAAS;YAET,QAAQ,GAAG,CAAC,6DAA6D,SAAS,WAAW;YAE7F,+CAA+C;YAC/C,MAAM,WAAW,MAAM,iHAAA,CAAA,eAAY,CAAC,SAAS,CAAC,SAAS,WAAW;YAElE,+EAA+E;YAC/E,IAAI,aAAa,EAAE;YACnB,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,aAAa;YACf,OAAO,IAAI,YAAY,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBACnD,aAAa,SAAS,IAAI;YAC5B,OAAO,IAAI,YAAY,SAAS,MAAM,IAAI,MAAM,OAAO,CAAC,SAAS,MAAM,GAAG;gBACxE,aAAa,SAAS,MAAM;YAC9B,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;YAEA,2GAA2G;YAC3G,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAC,SAC5C,OAAO,YAAY,KAAK,eACxB,OAAO,YAAY,KAAK,UACxB,OAAO,aAAa,GAAG;YAGzB,UAAU;YACV,QAAQ,GAAG,CAAC,4CAA4C,mBAAmB,MAAM;YACjF,QAAQ,GAAG,CAAC,8CAA8C,mBAAmB,GAAG,CAAC,CAAA,IAAK,CAAC;oBACrF,IAAI,EAAE,SAAS;oBACf,WAAW,EAAE,SAAS;oBACtB,SAAS,EAAE,OAAO;oBAClB,eAAe,EAAE,aAAa;oBAC9B,OAAO,EAAE,YAAY;gBACvB,CAAC;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sDAAsD;YACpE,SAAS;YACT,UAAU,EAAE;QACd,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,gEAAgE;IAChE,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,YAAY,aAAa,gBAAgB,OAAO;QACrD,IAAI,YAAY,SAAS,QAAQ,CAAC,OAAO;YACvC,OAAO,SAAS,KAAK,CAAC,KAAK,CAAC,EAAE;QAChC;QACA,MAAM,SAAS,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;QAChD,OAAO,SAAS,OAAO,aAAa,IAAI,WAAW;IACrD;IAEA,+BAA+B;IAC/B,MAAM,uBAAuB;QAC3B,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,MAAM,cAAc,OAAO,MAAM,CAAC,CAAA;YAChC,MAAM,eAAe,OAAO,SAAS,KAAK,KAAK,SAAS,IACpC,OAAO,OAAO,KAAK,KAAK,OAAO;YACnD,MAAM,gBAAgB,eAAe,MAChB,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC7D,OAAO,SAAS,IAAI,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAClF,OAAO,aAAa,IAAI,OAAO,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;YAChH,OAAO,gBAAgB,iBAAiB,OAAO,aAAa,GAAG;QACjE;QAEA,QAAQ,GAAG,CAAC,gDAAgD;YAC1D,eAAe,KAAK,SAAS;YAC7B,aAAa,KAAK,OAAO;YACzB,cAAc,OAAO,MAAM;YAC3B,mBAAmB,YAAY,MAAM;YACrC;QACF;QAEA,OAAO;IACT;IAEA,iCAAiC;IACjC,MAAM,yBAAyB;QAC7B,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,OAAO,OAAO,MAAM,CAAC,CAAA;YACnB,MAAM,iBAAiB,OAAO,SAAS,KAAK,KAAK,SAAS,IACpC,OAAO,OAAO,KAAK,KAAK,OAAO;YACrD,MAAM,gBAAgB,eAAe,MAChB,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC7D,OAAO,SAAS,IAAI,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAClF,OAAO,aAAa,IAAI,OAAO,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;YAChH,OAAO,kBAAkB,iBAAiB,OAAO,aAAa,GAAG;QACnE;IACF;IAEA,MAAM,oBAAoB;IAC1B,MAAM,sBAAsB;IAE5B,MAAM,cAAc;QAClB,kBAAkB;QAClB,kBAAkB;QAClB,cAAc;QACd,SAAS;QACT;IACF;IAEA,MAAM,aAAa;QACjB,QAAQ,GAAG,CAAC,yCAAyC;YACnD;YACA;YACA,QAAQ,MAAM;YACd,YAAY,UAAU;QACxB;QAEA,IAAI,CAAC,MAAM;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,IAAI,mBAAmB,iBAAiB;gBACtC,IAAI,CAAC,gBAAgB;oBACnB,QAAQ;oBACR;gBACF;gBAEA,qDAAqD;gBACrD,MAAM,SAAS,MAAM,iHAAA,CAAA,UAAO,CAAC,iBAAiB,CAAC;oBAC7C,SAAS,KAAK,OAAO;oBACrB,cAAc,KAAK,eAAe,IAAI;oBACtC,WAAW;oBACX,YAAY,KAAK,oCAAoC;gBACvD;gBAEA,IAAI,OAAO,OAAO,EAAE;oBAClB,UAAU,CAAC,2CAA2C,EAAE,KAAK,OAAO,EAAE;oBACtE;gBACF,OAAO;oBACL,QAAQ,OAAO,OAAO,IAAI;gBAC5B;YACF,OAAO,IAAI,mBAAmB,kBAAkB;gBAC9C,uBAAuB;gBACvB,MAAM,SAAS,MAAM,iHAAA,CAAA,UAAO,CAAC,iBAAiB,CAAC;oBAC7C,SAAS,KAAK,OAAO;oBACrB,cAAc,KAAK,eAAe,IAAI;oBACtC,WAAW;oBACX,YAAY;gBACd;gBAEA,IAAI,OAAO,OAAO,EAAE;oBAClB,UAAU,CAAC,wBAAwB,EAAE,KAAK,OAAO,EAAE;oBACnD;gBACF,OAAO;oBACL,QAAQ,OAAO,OAAO,IAAI;gBAC5B;YACF,OAAO,IAAI,mBAAmB,yBAAyB;gBACrD,0BAA0B;gBAC1B,MAAM,SAAS,MAAM,iHAAA,CAAA,UAAO,CAAC,iBAAiB,CAAC;oBAC7C,SAAS,KAAK,OAAO;oBACrB,cAAc;oBACd,WAAW;oBACX,YAAY;gBACd;gBAEA,IAAI,OAAO,OAAO,EAAE;oBAClB,UAAU,CAAC,oCAAoC,EAAE,KAAK,OAAO,EAAE;oBAC/D;gBACF,OAAO;oBACL,QAAQ,OAAO,OAAO,IAAI;gBAC5B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE;kBACE,cAAA,8OAAC,kIAAA,CAAA,SAAM;YAAC,MAAM;YAAM,cAAc;sBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,8OAAC,kIAAA,CAAA,eAAY;kCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;;gCAAC;gCACW,KAAK,OAAO;;;;;;;;;;;;kCAItC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,8OAAC;gDAAG,WAAU;0DAA4B;;;;;;;;;;;;kDAI5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAA2C;;;;;;kFAC3D,8OAAC;wEAAK,WAAU;kFAA6B,KAAK,SAAS,IAAI;;;;;;;;;;;;0EAEjE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAA2C;;;;;;kFAC3D,8OAAC;wEAAK,WAAU;kFAA6B,KAAK,OAAO,IAAI;;;;;;;;;;;;0EAE/D,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAA2C;;;;;;kFAC3D,8OAAC;wEAAK,WAAU;kFAA6B,KAAK,WAAW,IAAI;;;;;;;;;;;;;;;;;;kEAGrE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAA2C;;;;;;kFAC3D,8OAAC;wEAAK,WAAU;kFAA6B,KAAK,mBAAmB,IAAI;;;;;;;;;;;;0EAE3E,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAA2C;;;;;;kFAC3D,8OAAC;wEAAK,WAAU;kFAA6B,KAAK,iBAAiB,IAAI;;;;;;;;;;;;0EAEzE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAA2C;;;;;;kFAC3D,8OAAC;wEAAK,WAAU;;4EAA4B,KAAK,eAAe,IAAI;4EAAE;;;;;;;kFACtE,8OAAC;wEAAK,WAAU;;4EAAgB;4EAAG,KAAK,iBAAiB,IAAI;4EAAE;;;;;;;;;;;;;;;;;;;;;;;;;4CAMpE,KAAK,SAAS,IAAI,KAAK,SAAS,KAAK,gCACpC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,wMAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,8OAAC;4DAAK,WAAU;sEAA8B;;;;;;sEAC9C,8OAAC;4DAAK,WAAU;sEAA6B,gBAAgB,KAAK,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQrF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAc;;;;;;kDAE5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAM;wDACN,SAAS,mBAAmB;wDAC5B,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDACjD,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;0DAG5B,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAM;wDACN,SAAS,mBAAmB;wDAC5B,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDACjD,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;0DAG5B,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAM;wDACN,SAAS,mBAAmB;wDAC5B,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDACjD,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEAAmC;;;;;;;;;;;;0DAGrD,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAM;wDACN,SAAS,mBAAmB;wDAC5B,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDACjD,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEAAuB;;;;;;;;;;;;;;;;;;;;;;;;4BAM5C,mBAAmB,iCAClB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAc;;;;;;kDAG5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;kDAKd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAW,CAAC,2DAA2D,EACrE,cAAc,gBACV,gDACA,wDACJ;0DAEF,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,8OAAC;;gEAAK;gEAAqB,kBAAkB,MAAM;gEAAC;;;;;;;;;;;;;;;;;;0DAGxD,8OAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAW,CAAC,2DAA2D,EACrE,cAAc,kBACV,mDACA,wDACJ;0DAEF,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,8OAAC;;gEAAK;gEAAuB,oBAAoB,MAAM;gEAAC;;;;;;;;;;;;;;;;;;;;;;;;kDAM9D,8OAAC;wCAAI,WAAU;kDACZ,8BACC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;;;;;iEAI5C,8OAAC;4CAAI,WAAU;sDACZ,cAAc,gBACb,kBAAkB,MAAM,KAAK,kBAC3B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAA6B;;;;;;kEAG5C,8OAAC;wDAAI,WAAU;;4DAAwB;0EACP,8OAAC;0EAAQ,KAAK,SAAS;;;;;;4DAAU;0EAAc,8OAAC;0EAAQ,KAAK,OAAO;;;;;;;;;;;;;;;;;qEAItG,8OAAC;gDAAI,WAAU;0DACZ,kBAAkB,GAAG,CAAC,CAAC,uBACtB,8OAAC;wDAEC,SAAS,IAAM,kBAAkB,OAAO,SAAS;wDACjD,WAAW,CAAC,0DAA0D,EACpE,mBAAmB,OAAO,SAAS,GAC/B,mDACA,iEACJ;kEAEF,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;8FAAqC,OAAO,SAAS;;;;;;gFACnE,OAAO,YAAY,kBAClB,8OAAC;oFAAK,WAAW,CAAC,2CAA2C,EAC3D,OAAO,YAAY,KAAK,gBAAgB,gCACxC,OAAO,YAAY,KAAK,WAAW,8BACnC,iCACA;8FACC,OAAO,YAAY;;;;;;;;;;;;sFAI1B,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAK,WAAU;8FAAe,OAAO,SAAS;;;;;;gFAAQ;8FAAG,8OAAC;8FAAM,OAAO,OAAO;;;;;;;;;;;;;;;;;;8EAGnF,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAW,CAAC,oBAAoB,EACnC,OAAO,aAAa,GAAG,IAAI,mBAAmB,iBAC9C;;gFACC,OAAO,aAAa;gFAAC;;;;;;;sFAExB,8OAAC;4EAAI,WAAU;sFACZ,OAAO,aAAa,GAAG,IAAI,gBAAgB;;;;;;;;;;;;;;;;;;uDAjC7C,OAAO,SAAS;;;;;;;;;uDA0C7B,oBAAoB,MAAM,KAAK,kBAC7B,8OAAC;gDAAI,WAAU;0DAAyC;;;;;qEAIxD,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oNAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;8EACvB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAAmB;;;;;;sFAClC,8OAAC;4EAAI,WAAU;sFAAU;;;;;;;;;;;;;;;;;;;;;;;oDAO9B,oBAAoB,GAAG,CAAC,CAAC,uBACxB,8OAAC;4DAEC,SAAS,IAAM,kBAAkB,OAAO,SAAS;4DACjD,WAAW,CAAC,0DAA0D,EACpE,mBAAmB,OAAO,SAAS,GAC/B,uDACA,iEACJ;sEAEF,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAI,WAAU;kGAAqC,OAAO,SAAS;;;;;;kGACpE,8OAAC;wFAAK,WAAU;kGAA2E;;;;;;;;;;;;0FAI7F,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAK,WAAU;kGAAe,OAAO,SAAS;;;;;;oFAAQ;kGAAG,8OAAC;kGAAM,OAAO,OAAO;;;;;;;;;;;;;;;;;;kFAGnF,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAW,CAAC,oBAAoB,EACnC,OAAO,aAAa,GAAG,IAAI,oBAAoB,iBAC/C;;oFACC,OAAO,aAAa;oFAAC;;;;;;;0FAExB,8OAAC;gFAAI,WAAU;0FACZ,OAAO,aAAa,GAAG,IAAI,gBAAgB;;;;;;;;;;;;;;;;;;2DA3B7C,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBA4C1C,uBACC,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;;0CACb,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC,iIAAA,CAAA,mBAAgB;0CAAE;;;;;;;;;;;;kCAIvB,8OAAC,kIAAA,CAAA,eAAY;wBAAC,WAAU;;0CACtB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;gCAAa,UAAU;0CAAS;;;;;;0CAGnE,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU,WAAY,mBAAmB,mBAAmB,CAAC;;oCAE5D,yBAAW,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA+B;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5E", "debugId": null}}, {"offset": {"line": 2985, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/cavi/InserisciMetriDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  <PERSON>alogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Badge } from '@/components/ui/badge'\nimport { Loader2, AlertCircle, Calculator, Search, CheckCircle, AlertTriangle, X } from 'lucide-react'\nimport { Cavo } from '@/types'\nimport { parcoCaviApi, caviApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useToast } from '@/hooks/use-toast'\n\nimport ModificaBobinaDialog from './ModificaBobinaDialog'\n\ninterface Bobina {\n  id_bobina: string\n  numero_bobina?: string\n  tipologia: string\n  formazione: string\n  metri_residui: number\n  fornitore?: string\n  stato_bobina?: string\n}\n\ninterface FormData {\n  metri_posati: string\n  id_bobina: string\n}\n\ninterface FormErrors {\n  metri_posati?: string\n  id_bobina?: string\n}\n\ninterface FormWarnings {\n  metri_posati?: string\n}\n\ninterface InserisciMetriDialogProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  cantiere?: { id_cantiere: number; commessa: string } | null\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\nexport default function InserisciMetriDialog({\n  open,\n  onClose,\n  cavo,\n  cantiere: cantiereProp,\n  onSuccess,\n  onError\n}: InserisciMetriDialogProps) {\n  const { cantiere: cantiereAuth } = useAuth()\n\n  // Usa il cantiere passato come prop o quello dall'auth come fallback\n  const cantiere = cantiereProp || cantiereAuth\n\n  // Stati per il form\n  const [formData, setFormData] = useState<FormData>({\n    metri_posati: '',\n    id_bobina: ''\n  })\n\n  // Debug formData changes\n  useEffect(() => {\n    console.log('📊 InserisciMetriDialog: FormData aggiornato:', {\n      hasMetri: !!formData.metri_posati,\n      hasBobina: !!formData.id_bobina,\n      metri_posati: formData.metri_posati,\n      id_bobina: formData.id_bobina\n    })\n  }, [formData])\n  const [formErrors, setFormErrors] = useState<FormErrors>({})\n  const [formWarnings, setFormWarnings] = useState<FormWarnings>({})\n  const [saving, setSaving] = useState(false)\n\n  // Stati per bobine\n  const [bobine, setBobine] = useState<Bobina[]>([])\n  const [bobineLoading, setBobineLoading] = useState(false)\n\n  // Stati per la ricerca delle bobine\n  const [searchText, setSearchText] = useState('')\n\n  // Stati per dialoghi\n  const [showModificaBobinaDialog, setShowModificaBobinaDialog] = useState(false)\n\n  // Carica bobine quando si apre il dialog\n  useEffect(() => {\n    if (open && cantiere) {\n      loadBobine()\n    }\n  }, [open, cantiere])\n\n  // Reset form quando si apre il dialog\n  useEffect(() => {\n    if (open && cavo) {\n      setFormData({\n        metri_posati: '',\n        id_bobina: ''\n      })\n      setFormErrors({})\n      setFormWarnings({})\n      setSearchText('')\n    }\n  }, [open, cavo])\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = (idBobina: string) => {\n    if (!idBobina || idBobina === 'BOBINA_VUOTA') return 'VUOTA'\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1]\n    }\n    // Cerca nella lista bobine per ottenere il numero_bobina\n    const bobina = bobine.find(b => b.id_bobina === idBobina)\n    return bobina ? bobina.numero_bobina || idBobina : idBobina\n  }\n\n  // Filtra le bobine compatibili\n  const getBobineCompatibili = () => {\n    if (!cavo) return []\n\n    return bobine.filter(bobina => {\n      const isCompatible = bobina.tipologia === cavo.tipologia &&\n                          bobina.sezione === cavo.sezione\n      const matchesSearch = searchText === '' ||\n                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase()) ||\n                           (bobina.tipologia && bobina.tipologia.toLowerCase().includes(searchText.toLowerCase())) ||\n                           (bobina.numero_bobina && bobina.numero_bobina.toLowerCase().includes(searchText.toLowerCase()))\n      return isCompatible && matchesSearch && bobina.metri_residui > 0\n    })\n  }\n\n  // Filtra le bobine incompatibili\n  const getBobineIncompatibili = () => {\n    if (!cavo) return []\n\n    return bobine.filter(bobina => {\n      const isIncompatible = bobina.tipologia !== cavo.tipologia ||\n                            bobina.sezione !== cavo.sezione\n      const matchesSearch = searchText === '' ||\n                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase()) ||\n                           (bobina.tipologia && bobina.tipologia.toLowerCase().includes(searchText.toLowerCase())) ||\n                           (bobina.numero_bobina && bobina.numero_bobina.toLowerCase().includes(searchText.toLowerCase()))\n      return isIncompatible && matchesSearch && bobina.metri_residui > 0\n    })\n  }\n\n  // Filtra bobine in base alla ricerca\n  const bobineFiltrate = bobine.filter(bobina => {\n    if (!searchText) return true\n    const searchLower = searchText.toLowerCase()\n    return (\n      bobina.id_bobina.toLowerCase().includes(searchLower) ||\n      bobina.tipologia.toLowerCase().includes(searchLower) ||\n      bobina.formazione.toLowerCase().includes(searchLower) ||\n      getBobinaNumber(bobina.id_bobina).toLowerCase().includes(searchLower)\n    )\n  })\n\n  // Usa le funzioni di filtro per evitare duplicazioni\n  const bobineCompatibili = getBobineCompatibili()\n  const bobineIncompatibili = getBobineIncompatibili()\n\n  // Gestisce la selezione di una bobina\n  const handleBobinaSelect = (bobina: Bobina) => {\n    console.log('🎯 Bobina selezionata:', {\n      id: bobina.id_bobina,\n      numero: getBobinaNumber(bobina.id_bobina),\n      tipologia: bobina.tipologia,\n      formazione: bobina.formazione,\n      metri_residui: bobina.metri_residui\n    })\n\n    setFormData(prev => ({ ...prev, id_bobina: bobina.id_bobina }))\n    setFormErrors(prev => ({ ...prev, id_bobina: undefined }))\n  }\n\n  // Gestisce la selezione di BOBINA VUOTA\n  // Permette di posare il cavo senza assegnare una bobina specifica\n  // Il cavo potrà essere collegato a una bobina in seguito tramite ModificaBobinaDialog\n  const handleBobinaVuotaSelect = () => {\n    console.log('🎯 BOBINA VUOTA selezionata - cavo sarà posato senza bobina specifica')\n    setFormData(prev => ({ ...prev, id_bobina: 'BOBINA_VUOTA' }))\n\n    // Reset completo degli errori - rimuovi la chiave invece di impostarla vuota\n    setFormErrors(prev => {\n      const newErrors = { ...prev }\n      delete newErrors.id_bobina  // Rimuovi completamente la chiave\n      return newErrors\n    })\n  }\n\n  // Carica bobine quando si apre il dialog\n  useEffect(() => {\n    if (open && cavo) {\n\n      if (cantiere) {\n        loadBobine()\n      } else {\n      }\n\n      setFormData({\n        metri_posati: '0', // Default a 0 come nell'originale\n        id_bobina: ''\n      })\n      setFormErrors({})\n      setFormWarnings({})\n      setSearchText('')\n    }\n  }, [open, cavo, cantiere])\n\n  // Validazione real-time dei metri posati\n  useEffect(() => {\n    if (formData.metri_posati && cavo) {\n      validateMetriPosati(parseFloat(formData.metri_posati))\n    } else {\n      setFormErrors(prev => ({ ...prev, metri_posati: undefined }))\n      setFormWarnings(prev => ({ ...prev, metri_posati: undefined }))\n    }\n  }, [formData.metri_posati, cavo])\n\n  const validateMetriPosati = (metri: number) => {\n    if (!cavo) return\n\n    let errors: FormErrors = { ...formErrors }\n    let warnings: FormWarnings = { ...formWarnings }\n\n    // Rimuovi errori/warning precedenti per metri_posati\n    delete errors.metri_posati\n    delete warnings.metri_posati\n\n    // NESSUN ERRORE BLOCCANTE - Solo warning informativi\n    // I warning non impediscono il salvataggio\n\n    if (metri > (cavo.metri_teorici || 0) * 1.1) {\n      warnings.metri_posati = `Attenzione: i metri posati superano del 10% i metri teorici (${cavo.metri_teorici}m)`\n    } else if (metri > (cavo.metri_teorici || 0)) {\n      warnings.metri_posati = 'Metratura superiore ai metri teorici'\n    }\n\n    setFormErrors(errors)\n    setFormWarnings(warnings)\n  }\n\n  const loadBobine = async () => {\n    console.log({\n      cavo: !!cavo,\n      cantiere: !!cantiere,\n      cavoId: cavo?.id_cavo,\n      cantiereId: cantiere?.id_cantiere\n    })\n\n    if (!cavo || !cantiere) {\n      return\n    }\n\n    try {\n      setBobineLoading(true)\n\n      // Carica tutte le bobine disponibili\n      const response = await parcoCaviApi.getBobine(cantiere.id_cantiere)\n\n      // Gestisce diversi formati di risposta\n      let bobineData = []\n      if (Array.isArray(response)) {\n        bobineData = response\n      } else if (response && Array.isArray(response.data)) {\n        bobineData = response.data\n      } else if (response && response.bobine && Array.isArray(response.bobine)) {\n        bobineData = response.bobine\n      } else {\n        throw new Error('Formato risposta API non valido')\n      }\n\n      // Filtra solo per stato (disponibile o in uso) e metri residui > 0\n      const bobineUtilizzabili = bobineData.filter((bobina: Bobina) =>\n        bobina.stato_bobina !== 'Terminata' &&\n        bobina.stato_bobina !== 'Over' &&\n        bobina.metri_residui > 0\n      )\n\n      if (cavo) {\n        console.log({\n          tipologia: cavo.tipologia,\n          sezione: cavo.sezione\n        })\n\n        // Separa bobine compatibili e incompatibili\n        const bobineCompatibili = bobineUtilizzabili.filter(bobina => {\n          const isCompatible = bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione\n          return isCompatible\n        })\n        const bobineNonCompatibili = bobineUtilizzabili.filter(bobina =>\n          !(bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione)\n        )\n\n        // Ordina entrambi gli array per metri residui (decrescente)\n        bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui)\n        bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui)\n\n        // Concatena gli array: prima le compatibili, poi le non compatibili\n        const bobineOrdinate = [...bobineCompatibili, ...bobineNonCompatibili]\n        setBobine(bobineOrdinate)\n      } else {\n        // Se non c'è un cavo selezionato, mostra tutte le bobine ordinate per metri residui\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui)\n        setBobine(bobineUtilizzabili)\n      }\n    } catch (error: any) {\n      console.log({\n        message: error.message,\n        response: error.response,\n        status: error.response?.status,\n        data: error.response?.data\n      })\n\n      // Non mostrare errore se è solo un problema di rete, permetti di usare BOBINA_VUOTA\n      if (error.response?.status !== 404) {\n        onError('Errore nel caricamento delle bobine. Puoi comunque usare BOBINA VUOTA.')\n      }\n      setBobine([])\n    } finally {\n      setBobineLoading(false)\n    }\n  }\n\n\n\n  const handleSave = async () => {\n    console.log({\n      cavo: cavo?.id_cavo,\n      metri_posati: formData.metri_posati,\n      id_bobina: formData.id_bobina\n    })\n\n    if (!cavo) {\n      return\n    }\n\n    // Validazioni di base (solo controlli essenziali)\n    if (!formData.metri_posati || parseFloat(formData.metri_posati) < 0) {\n      onError('Inserire metri posati validi (≥ 0)')\n      return\n    }\n\n    if (!formData.id_bobina) {\n      onError('Selezionare una bobina o BOBINA VUOTA')\n      return\n    }\n\n    const metri = parseFloat(formData.metri_posati)\n\n    // Gestione stato OVER per bobine reali (NON BLOCCANTE)\n    if (formData.id_bobina !== 'BOBINA_VUOTA') {\n      const bobina = bobine.find(b => b.id_bobina === formData.id_bobina)\n      if (bobina && metri > bobina.metri_residui) {\n        // OVER state - salva comunque ma avvisa\n        // Il salvataggio continua - lo stato OVER viene gestito dal backend\n      }\n    }\n\n    try {\n      setSaving(true)\n\n      if (!cantiere) {\n        throw new Error('Cantiere non selezionato')\n      }\n\n      // Aggiorna metri posati tramite API\n      console.log({\n        cantiere: cantiere.id_cantiere,\n        cavo: cavo.id_cavo,\n        metri: metri,\n        bobina: formData.id_bobina,  // Mostra il valore reale che viene passato\n        isBobinaVuota: formData.id_bobina === 'BOBINA_VUOTA'\n      })\n\n      await caviApi.updateMetriPosati(\n        cantiere.id_cantiere,\n        cavo.id_cavo,\n        metri,\n        formData.id_bobina,  // Passa sempre il valore, incluso 'BOBINA_VUOTA'\n        true  // force_over: true per permettere bobine incompatibili e OVER state\n      )\n\n      onSuccess(`Metri posati aggiornati con successo per il cavo ${cavo.id_cavo}: ${metri}m`)\n      onClose()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante il salvataggio dei metri posati'\n      onError(errorMessage)\n    } finally {\n      setSaving(false)\n    }\n  }\n\n  const handleClose = () => {\n    if (!saving) {\n      setFormData({ metri_posati: '', id_bobina: '' })\n      setFormErrors({})\n      setFormWarnings({})\n      setSearchText('')\n      onClose()\n    }\n  }\n\n  if (!cavo) return null\n\n  return (\n    <>\n      <Dialog open={open} onOpenChange={handleClose}>\n        <DialogContent className=\"max-w-7xl h-[90vh] flex flex-col\">\n          <DialogHeader className=\"flex-shrink-0\">\n            <DialogTitle className=\"flex items-center gap-2\">\n              <Calculator className=\"h-5 w-5\" />\n              Inserisci Metri Posati - {cavo.id_cavo}\n            </DialogTitle>\n            <DialogDescription>\n              Inserisci i metri effettivamente posati per il cavo e seleziona una bobina o usa BOBINA VUOTA\n            </DialogDescription>\n          </DialogHeader>\n\n          <div className=\"flex-1 overflow-y-auto space-y-6\">\n            {/* Sezione informazioni cavo e metri posati */}\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-4\">\n              {/* Informazioni cavo - 2/3 della larghezza */}\n              <div className=\"lg:col-span-2\">\n                <div className=\"p-4 bg-blue-50 rounded-lg border-2 border-blue-200\">\n                  <h3 className=\"font-semibold text-blue-800 mb-3\">Informazioni Cavo</h3>\n                  <div className=\"grid grid-cols-2 gap-3 text-sm\">\n                    <div><strong>Tipologia:</strong> {cavo.tipologia || 'N/A'}</div>\n                    <div><strong>Da:</strong> {cavo.ubicazione_partenza || 'N/A'}</div>\n                    <div><strong>Formazione:</strong> {cavo.sezione || 'N/A'}</div>\n                    <div><strong>A:</strong> {cavo.ubicazione_arrivo || 'N/A'}</div>\n                    <div><strong>Metri teorici:</strong> {cavo.metri_teorici || 'N/A'} m</div>\n                    <div><strong>Già posati:</strong> {cavo.metratura_reale || 0} m</div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Campo metri posati - 1/3 della larghezza */}\n              <div className=\"lg:col-span-1\">\n                <div className=\"p-4 bg-blue-50 rounded-lg border-2 border-blue-300 h-full\">\n                  <h3 className=\"font-semibold text-blue-800 mb-3\">Metri da Installare</h3>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"metri\" className=\"text-sm font-medium\">\n                      Metri Posati\n                    </Label>\n                    <div className=\"relative\">\n                      <Input\n                        id=\"metri\"\n                        type=\"number\"\n                        value={formData.metri_posati}\n                        onChange={(e) => setFormData(prev => ({ ...prev, metri_posati: e.target.value }))}\n                        placeholder=\"Inserisci metri posati\"\n                        disabled={saving}\n                        step=\"0.1\"\n                        min=\"0\"\n                        className=\"text-lg font-bold text-center border-2 border-blue-400 focus:border-blue-600\"\n                        autoFocus\n                      />\n                      <span className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-lg font-bold text-blue-600\">\n                        m\n                      </span>\n                    </div>\n                    {formErrors.metri_posati && (\n                      <p className=\"text-sm text-red-600\">{formErrors.metri_posati}</p>\n                    )}\n                    {formWarnings.metri_posati && (\n                      <p className=\"text-sm text-amber-600\">{formWarnings.metri_posati}</p>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Selezione bobina */}\n            <div className=\"space-y-4\">\n              <h3 className=\"font-semibold text-blue-800 text-lg\">Selezione Bobina</h3>\n\n              {/* Controlli di ricerca e BOBINA VUOTA */}\n              <div className=\"p-4 bg-gray-50 rounded-lg\">\n                <div className=\"grid grid-cols-1 sm:grid-cols-12 gap-3 items-center\">\n                  {/* Campo di ricerca - 5 colonne */}\n                  <div className=\"sm:col-span-5\">\n                    <div className=\"relative\">\n                      <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                      <Input\n                        placeholder=\"ID, tipologia, formazione...\"\n                        value={searchText}\n                        onChange={(e) => setSearchText(e.target.value)}\n                        className=\"pl-10\"\n                        disabled={saving}\n                      />\n                      {searchText && (\n                        <Button\n                          type=\"button\"\n                          variant=\"ghost\"\n                          size=\"sm\"\n                          className=\"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0\"\n                          onClick={() => setSearchText('')}\n                        >\n                          <X className=\"h-4 w-4\" />\n                        </Button>\n                      )}\n                    </div>\n                  </div>\n\n                  {/* Pulsante BOBINA VUOTA - 7 colonne */}\n                  <div className=\"sm:col-span-7\">\n                    <Button\n                      type=\"button\"\n                      variant={formData.id_bobina === 'BOBINA_VUOTA' ? 'default' : 'outline'}\n                      className={`w-full h-10 font-bold flex items-center justify-center gap-2 ${\n                        formData.id_bobina === 'BOBINA_VUOTA'\n                          ? 'bg-green-600 hover:bg-green-700 text-white'\n                          : 'border-blue-400 text-blue-700 hover:bg-blue-50'\n                      }`}\n                      onClick={handleBobinaVuotaSelect}\n                      disabled={saving}\n                    >\n                      {formData.id_bobina === 'BOBINA_VUOTA' && (\n                        <CheckCircle className=\"h-5 w-5\" />\n                      )}\n                      BOBINA VUOTA\n                    </Button>\n                  </div>\n                </div>\n\n                {/* Messaggio informativo per BOBINA VUOTA */}\n                {formData.id_bobina === 'BOBINA_VUOTA' && (\n                  <div className=\"mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg\">\n                    <div className=\"flex items-start gap-2\">\n                      <CheckCircle className=\"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0\" />\n                      <div className=\"text-sm text-blue-800\">\n                        <p className=\"font-medium\">Bobina Vuota Selezionata</p>\n                        <p className=\"mt-1\">\n                          Il cavo sarà posato senza assegnazione di bobina specifica.\n                          Potrai collegarlo a una bobina in seguito tramite la funzione \"Modifica Bobina\".\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n\n              {bobineLoading ? (\n                <div className=\"flex items-center justify-center p-8\">\n                  <Loader2 className=\"h-6 w-6 animate-spin mr-2\" />\n                  <span>Caricamento bobine...</span>\n                </div>\n              ) : (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  {/* Bobine compatibili */}\n                  <div>\n                    <h4 className=\"font-medium text-green-700 mb-2 flex items-center gap-2\">\n                      <CheckCircle className=\"h-4 w-4\" />\n                      Bobine Compatibili ({bobineCompatibili.length})\n                    </h4>\n                    <div className=\"max-h-72 overflow-y-auto border rounded-lg\">\n                      {bobineCompatibili.length === 0 ? (\n                        <div className=\"p-4 text-center text-gray-500\">\n                          Nessuna bobina compatibile trovata\n                        </div>\n                      ) : (\n                        <div className=\"divide-y\">\n                          {bobineCompatibili.map((bobina) => (\n                            <div\n                              key={bobina.id_bobina}\n                              className={`p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ${\n                                formData.id_bobina === bobina.id_bobina\n                                  ? 'bg-green-100 border-green-500 shadow-md'\n                                  : 'border-gray-200 hover:bg-green-50 hover:border-green-300'\n                              }`}\n                              onClick={() => handleBobinaSelect(bobina)}\n                            >\n                              <div className=\"flex justify-between items-center\">\n                                <div className=\"flex items-center gap-3 flex-1 min-w-0\">\n                                  {formData.id_bobina === bobina.id_bobina && (\n                                    <CheckCircle className=\"h-5 w-5 text-green-600 flex-shrink-0\" />\n                                  )}\n                                  <div className=\"font-bold text-base min-w-fit\">\n                                    {getBobinaNumber(bobina.id_bobina)}\n                                  </div>\n                                  <div className=\"text-sm text-gray-600 truncate\">\n                                    {bobina.tipologia} - {bobina.sezione}\n                                  </div>\n                                </div>\n                                <Badge variant=\"outline\" className=\"bg-green-100 text-green-800 border-green-300 font-medium min-w-fit\">\n                                  {bobina.metri_residui}m\n                                </Badge>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n\n                  {/* Bobine incompatibili */}\n                  <div>\n                    <h4 className=\"font-medium text-amber-700 mb-2 flex items-center gap-2\">\n                      <AlertTriangle className=\"h-4 w-4\" />\n                      Bobine Incompatibili ({bobineIncompatibili.length})\n                    </h4>\n                    <div className=\"max-h-72 overflow-y-auto border rounded-lg\">\n                      {bobineIncompatibili.length === 0 ? (\n                        <div className=\"p-4 text-center text-gray-500\">\n                          Nessuna bobina incompatibile trovata\n                        </div>\n                      ) : (\n                        <div className=\"divide-y\">\n                          {bobineIncompatibili.map((bobina) => (\n                            <div\n                              key={bobina.id_bobina}\n                              className={`p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ${\n                                formData.id_bobina === bobina.id_bobina\n                                  ? 'bg-amber-100 border-amber-500 shadow-md'\n                                  : 'border-gray-200 hover:bg-amber-50 hover:border-amber-300'\n                              }`}\n                              onClick={() => handleBobinaSelect(bobina)}\n                            >\n                              <div className=\"flex justify-between items-center\">\n                                <div className=\"flex items-center gap-3 flex-1 min-w-0\">\n                                  {formData.id_bobina === bobina.id_bobina && (\n                                    <CheckCircle className=\"h-5 w-5 text-amber-600 flex-shrink-0\" />\n                                  )}\n                                  <div className=\"font-bold text-base min-w-fit\">\n                                    {getBobinaNumber(bobina.id_bobina)}\n                                  </div>\n                                  <div className=\"text-sm text-gray-600 truncate\">\n                                    {bobina.tipologia} - {bobina.sezione}\n                                  </div>\n                                </div>\n                                <Badge variant=\"outline\" className=\"bg-amber-100 text-amber-800 border-amber-300 font-medium min-w-fit\">\n                                  {bobina.metri_residui}m\n                                </Badge>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {bobine.length === 0 && !bobineLoading && (\n                <Alert className=\"border-amber-200 bg-amber-50\">\n                  <AlertTriangle className=\"h-4 w-4 text-amber-600\" />\n                  <AlertDescription className=\"text-amber-800\">\n                    Non ci sono bobine disponibili. Puoi procedere con BOBINA VUOTA o aggiungere prima una nuova bobina.\n                  </AlertDescription>\n                </Alert>\n              )}\n\n              {formErrors.id_bobina && (\n                <Alert variant=\"destructive\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  <AlertDescription>{formErrors.id_bobina}</AlertDescription>\n                </Alert>\n              )}\n            </div>\n          </div>\n\n          <DialogFooter className=\"flex-shrink-0 border-t pt-4 mt-4 flex justify-between items-center\">\n            {/* Pulsante Modifica Bobina a sinistra (solo se cavo è installato) */}\n            <div>\n              {cavo.stato_installazione === 'installato' && cavo.id_bobina && (\n                <Button\n                  variant=\"outline\"\n                  onClick={() => {\n                    setShowModificaBobinaDialog(true)\n                  }}\n                  disabled={saving}\n                  className=\"text-blue-600 border-blue-300 hover:bg-blue-50\"\n                >\n                  Modifica Bobina\n                </Button>\n              )}\n            </div>\n\n            {/* Pulsanti principali a destra */}\n            <div className=\"flex gap-2\">\n              <Button variant=\"outline\" onClick={handleClose} disabled={saving}>\n                Annulla\n              </Button>\n              <Button\n                onClick={handleSave}\n                disabled={\n                  saving ||\n                  !formData.metri_posati ||\n                  parseFloat(formData.metri_posati) < 0 ||\n                  !formData.id_bobina\n                }\n                className=\"bg-mariner-600 hover:bg-mariner-700 text-white disabled:bg-gray-400 disabled:text-gray-200\"\n              >\n                {saving && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n                Salva\n              </Button>\n            </div>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* Dialog per modifica bobina */}\n      <ModificaBobinaDialog\n        open={showModificaBobinaDialog}\n        onClose={() => setShowModificaBobinaDialog(false)}\n        cavo={cavo}\n        onSuccess={(message) => {\n          onSuccess(message)\n          setShowModificaBobinaDialog(false)\n          onClose() // Chiudi anche il dialog principale dopo il successo\n        }}\n        onError={onError}\n      />\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAGA;AAtBA;;;;;;;;;;;;;AAyDe,SAAS,qBAAqB,EAC3C,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,UAAU,YAAY,EACtB,SAAS,EACT,OAAO,EACmB;IAC1B,MAAM,EAAE,UAAU,YAAY,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEzC,qEAAqE;IACrE,MAAM,WAAW,gBAAgB;IAEjC,oBAAoB;IACpB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,cAAc;QACd,WAAW;IACb;IAEA,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,iDAAiD;YAC3D,UAAU,CAAC,CAAC,SAAS,YAAY;YACjC,WAAW,CAAC,CAAC,SAAS,SAAS;YAC/B,cAAc,SAAS,YAAY;YACnC,WAAW,SAAS,SAAS;QAC/B;IACF,GAAG;QAAC;KAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,CAAC;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,CAAC;IAChE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,mBAAmB;IACnB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,oCAAoC;IACpC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBAAqB;IACrB,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzE,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,UAAU;YACpB;QACF;IACF,GAAG;QAAC;QAAM;KAAS;IAEnB,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,MAAM;YAChB,YAAY;gBACV,cAAc;gBACd,WAAW;YACb;YACA,cAAc,CAAC;YACf,gBAAgB,CAAC;YACjB,cAAc;QAChB;IACF,GAAG;QAAC;QAAM;KAAK;IAEf,gEAAgE;IAChE,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,YAAY,aAAa,gBAAgB,OAAO;QACrD,8DAA8D;QAC9D,IAAI,YAAY,SAAS,QAAQ,CAAC,OAAO;YACvC,OAAO,SAAS,KAAK,CAAC,KAAK,CAAC,EAAE;QAChC;QACA,yDAAyD;QACzD,MAAM,SAAS,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;QAChD,OAAO,SAAS,OAAO,aAAa,IAAI,WAAW;IACrD;IAEA,+BAA+B;IAC/B,MAAM,uBAAuB;QAC3B,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,OAAO,OAAO,MAAM,CAAC,CAAA;YACnB,MAAM,eAAe,OAAO,SAAS,KAAK,KAAK,SAAS,IACpC,OAAO,OAAO,KAAK,KAAK,OAAO;YACnD,MAAM,gBAAgB,eAAe,MAChB,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC7D,OAAO,SAAS,IAAI,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAClF,OAAO,aAAa,IAAI,OAAO,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;YAChH,OAAO,gBAAgB,iBAAiB,OAAO,aAAa,GAAG;QACjE;IACF;IAEA,iCAAiC;IACjC,MAAM,yBAAyB;QAC7B,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,OAAO,OAAO,MAAM,CAAC,CAAA;YACnB,MAAM,iBAAiB,OAAO,SAAS,KAAK,KAAK,SAAS,IACpC,OAAO,OAAO,KAAK,KAAK,OAAO;YACrD,MAAM,gBAAgB,eAAe,MAChB,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC7D,OAAO,SAAS,IAAI,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAClF,OAAO,aAAa,IAAI,OAAO,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;YAChH,OAAO,kBAAkB,iBAAiB,OAAO,aAAa,GAAG;QACnE;IACF;IAEA,qCAAqC;IACrC,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA;QACnC,IAAI,CAAC,YAAY,OAAO;QACxB,MAAM,cAAc,WAAW,WAAW;QAC1C,OACE,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACxC,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACxC,OAAO,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACzC,gBAAgB,OAAO,SAAS,EAAE,WAAW,GAAG,QAAQ,CAAC;IAE7D;IAEA,qDAAqD;IACrD,MAAM,oBAAoB;IAC1B,MAAM,sBAAsB;IAE5B,sCAAsC;IACtC,MAAM,qBAAqB,CAAC;QAC1B,QAAQ,GAAG,CAAC,0BAA0B;YACpC,IAAI,OAAO,SAAS;YACpB,QAAQ,gBAAgB,OAAO,SAAS;YACxC,WAAW,OAAO,SAAS;YAC3B,YAAY,OAAO,UAAU;YAC7B,eAAe,OAAO,aAAa;QACrC;QAEA,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,WAAW,OAAO,SAAS;YAAC,CAAC;QAC7D,cAAc,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,WAAW;YAAU,CAAC;IAC1D;IAEA,wCAAwC;IACxC,kEAAkE;IAClE,sFAAsF;IACtF,MAAM,0BAA0B;QAC9B,QAAQ,GAAG,CAAC;QACZ,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,WAAW;YAAe,CAAC;QAE3D,6EAA6E;QAC7E,cAAc,CAAA;YACZ,MAAM,YAAY;gBAAE,GAAG,IAAI;YAAC;YAC5B,OAAO,UAAU,SAAS,CAAE,kCAAkC;;YAC9D,OAAO;QACT;IACF;IAEA,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,MAAM;YAEhB,IAAI,UAAU;gBACZ;YACF,OAAO,CACP;YAEA,YAAY;gBACV,cAAc;gBACd,WAAW;YACb;YACA,cAAc,CAAC;YACf,gBAAgB,CAAC;YACjB,cAAc;QAChB;IACF,GAAG;QAAC;QAAM;QAAM;KAAS;IAEzB,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,YAAY,IAAI,MAAM;YACjC,oBAAoB,WAAW,SAAS,YAAY;QACtD,OAAO;YACL,cAAc,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,cAAc;gBAAU,CAAC;YAC3D,gBAAgB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,cAAc;gBAAU,CAAC;QAC/D;IACF,GAAG;QAAC,SAAS,YAAY;QAAE;KAAK;IAEhC,MAAM,sBAAsB,CAAC;QAC3B,IAAI,CAAC,MAAM;QAEX,IAAI,SAAqB;YAAE,GAAG,UAAU;QAAC;QACzC,IAAI,WAAyB;YAAE,GAAG,YAAY;QAAC;QAE/C,qDAAqD;QACrD,OAAO,OAAO,YAAY;QAC1B,OAAO,SAAS,YAAY;QAE5B,qDAAqD;QACrD,2CAA2C;QAE3C,IAAI,QAAQ,CAAC,KAAK,aAAa,IAAI,CAAC,IAAI,KAAK;YAC3C,SAAS,YAAY,GAAG,CAAC,6DAA6D,EAAE,KAAK,aAAa,CAAC,EAAE,CAAC;QAChH,OAAO,IAAI,QAAQ,CAAC,KAAK,aAAa,IAAI,CAAC,GAAG;YAC5C,SAAS,YAAY,GAAG;QAC1B;QAEA,cAAc;QACd,gBAAgB;IAClB;IAEA,MAAM,aAAa;QACjB,QAAQ,GAAG,CAAC;YACV,MAAM,CAAC,CAAC;YACR,UAAU,CAAC,CAAC;YACZ,QAAQ,MAAM;YACd,YAAY,UAAU;QACxB;QAEA,IAAI,CAAC,QAAQ,CAAC,UAAU;YACtB;QACF;QAEA,IAAI;YACF,iBAAiB;YAEjB,qCAAqC;YACrC,MAAM,WAAW,MAAM,iHAAA,CAAA,eAAY,CAAC,SAAS,CAAC,SAAS,WAAW;YAElE,uCAAuC;YACvC,IAAI,aAAa,EAAE;YACnB,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,aAAa;YACf,OAAO,IAAI,YAAY,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBACnD,aAAa,SAAS,IAAI;YAC5B,OAAO,IAAI,YAAY,SAAS,MAAM,IAAI,MAAM,OAAO,CAAC,SAAS,MAAM,GAAG;gBACxE,aAAa,SAAS,MAAM;YAC9B,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;YAEA,mEAAmE;YACnE,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAC,SAC5C,OAAO,YAAY,KAAK,eACxB,OAAO,YAAY,KAAK,UACxB,OAAO,aAAa,GAAG;YAGzB,IAAI,MAAM;gBACR,QAAQ,GAAG,CAAC;oBACV,WAAW,KAAK,SAAS;oBACzB,SAAS,KAAK,OAAO;gBACvB;gBAEA,4CAA4C;gBAC5C,MAAM,oBAAoB,mBAAmB,MAAM,CAAC,CAAA;oBAClD,MAAM,eAAe,OAAO,SAAS,KAAK,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,KAAK,OAAO;oBAC3F,OAAO;gBACT;gBACA,MAAM,uBAAuB,mBAAmB,MAAM,CAAC,CAAA,SACrD,CAAC,CAAC,OAAO,SAAS,KAAK,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,KAAK,OAAO;gBAG1E,4DAA4D;gBAC5D,kBAAkB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,aAAa,GAAG,EAAE,aAAa;gBAClE,qBAAqB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,aAAa,GAAG,EAAE,aAAa;gBAErE,oEAAoE;gBACpE,MAAM,iBAAiB;uBAAI;uBAAsB;iBAAqB;gBACtE,UAAU;YACZ,OAAO;gBACL,oFAAoF;gBACpF,mBAAmB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,aAAa,GAAG,EAAE,aAAa;gBACnE,UAAU;YACZ;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,GAAG,CAAC;gBACV,SAAS,MAAM,OAAO;gBACtB,UAAU,MAAM,QAAQ;gBACxB,QAAQ,MAAM,QAAQ,EAAE;gBACxB,MAAM,MAAM,QAAQ,EAAE;YACxB;YAEA,oFAAoF;YACpF,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,QAAQ;YACV;YACA,UAAU,EAAE;QACd,SAAU;YACR,iBAAiB;QACnB;IACF;IAIA,MAAM,aAAa;QACjB,QAAQ,GAAG,CAAC;YACV,MAAM,MAAM;YACZ,cAAc,SAAS,YAAY;YACnC,WAAW,SAAS,SAAS;QAC/B;QAEA,IAAI,CAAC,MAAM;YACT;QACF;QAEA,kDAAkD;QAClD,IAAI,CAAC,SAAS,YAAY,IAAI,WAAW,SAAS,YAAY,IAAI,GAAG;YACnE,QAAQ;YACR;QACF;QAEA,IAAI,CAAC,SAAS,SAAS,EAAE;YACvB,QAAQ;YACR;QACF;QAEA,MAAM,QAAQ,WAAW,SAAS,YAAY;QAE9C,uDAAuD;QACvD,IAAI,SAAS,SAAS,KAAK,gBAAgB;YACzC,MAAM,SAAS,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK,SAAS,SAAS;YAClE,IAAI,UAAU,QAAQ,OAAO,aAAa,EAAE;YAC1C,wCAAwC;YACxC,oEAAoE;YACtE;QACF;QAEA,IAAI;YACF,UAAU;YAEV,IAAI,CAAC,UAAU;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,oCAAoC;YACpC,QAAQ,GAAG,CAAC;gBACV,UAAU,SAAS,WAAW;gBAC9B,MAAM,KAAK,OAAO;gBAClB,OAAO;gBACP,QAAQ,SAAS,SAAS;gBAC1B,eAAe,SAAS,SAAS,KAAK;YACxC;YAEA,MAAM,iHAAA,CAAA,UAAO,CAAC,iBAAiB,CAC7B,SAAS,WAAW,EACpB,KAAK,OAAO,EACZ,OACA,SAAS,SAAS,EAClB,KAAM,oEAAoE;;YAG5E,UAAU,CAAC,iDAAiD,EAAE,KAAK,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;YACvF;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,QAAQ;YACX,YAAY;gBAAE,cAAc;gBAAI,WAAW;YAAG;YAC9C,cAAc,CAAC;YACf,gBAAgB,CAAC;YACjB,cAAc;YACd;QACF;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE;;0BACE,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAM,cAAc;0BAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,8OAAC,kIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC,8MAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAY;wCACR,KAAK,OAAO;;;;;;;8CAExC,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAKrB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFAAI,8OAAC;kFAAO;;;;;;oEAAmB;oEAAE,KAAK,SAAS,IAAI;;;;;;;0EACpD,8OAAC;;kFAAI,8OAAC;kFAAO;;;;;;oEAAY;oEAAE,KAAK,mBAAmB,IAAI;;;;;;;0EACvD,8OAAC;;kFAAI,8OAAC;kFAAO;;;;;;oEAAoB;oEAAE,KAAK,OAAO,IAAI;;;;;;;0EACnD,8OAAC;;kFAAI,8OAAC;kFAAO;;;;;;oEAAW;oEAAE,KAAK,iBAAiB,IAAI;;;;;;;0EACpD,8OAAC;;kFAAI,8OAAC;kFAAO;;;;;;oEAAuB;oEAAE,KAAK,aAAa,IAAI;oEAAM;;;;;;;0EAClE,8OAAC;;kFAAI,8OAAC;kFAAO;;;;;;oEAAoB;oEAAE,KAAK,eAAe,IAAI;oEAAE;;;;;;;;;;;;;;;;;;;;;;;;sDAMnE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAQ,WAAU;0EAAsB;;;;;;0EAGvD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEACJ,IAAG;wEACH,MAAK;wEACL,OAAO,SAAS,YAAY;wEAC5B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oFAAE,GAAG,IAAI;oFAAE,cAAc,EAAE,MAAM,CAAC,KAAK;gFAAC,CAAC;wEAC/E,aAAY;wEACZ,UAAU;wEACV,MAAK;wEACL,KAAI;wEACJ,WAAU;wEACV,SAAS;;;;;;kFAEX,8OAAC;wEAAK,WAAU;kFAAsF;;;;;;;;;;;;4DAIvG,WAAW,YAAY,kBACtB,8OAAC;gEAAE,WAAU;0EAAwB,WAAW,YAAY;;;;;;4DAE7D,aAAa,YAAY,kBACxB,8OAAC;gEAAE,WAAU;0EAA0B,aAAa,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQ1E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsC;;;;;;sDAGpD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEAEb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,sMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,8OAAC,iIAAA,CAAA,QAAK;wEACJ,aAAY;wEACZ,OAAO;wEACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wEAC7C,WAAU;wEACV,UAAU;;;;;;oEAEX,4BACC,8OAAC,kIAAA,CAAA,SAAM;wEACL,MAAK;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS,IAAM,cAAc;kFAE7B,cAAA,8OAAC,4LAAA,CAAA,IAAC;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sEAOrB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAS,SAAS,SAAS,KAAK,iBAAiB,YAAY;gEAC7D,WAAW,CAAC,6DAA6D,EACvE,SAAS,SAAS,KAAK,iBACnB,+CACA,kDACJ;gEACF,SAAS;gEACT,UAAU;;oEAET,SAAS,SAAS,KAAK,gCACtB,8OAAC,2NAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;oEACvB;;;;;;;;;;;;;;;;;;gDAOP,SAAS,SAAS,KAAK,gCACtB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;kFAAc;;;;;;kFAC3B,8OAAC;wEAAE,WAAU;kFAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCAU7B,8BACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,8OAAC;8DAAK;;;;;;;;;;;iEAGR,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAAY;gEACd,kBAAkB,MAAM;gEAAC;;;;;;;sEAEhD,8OAAC;4DAAI,WAAU;sEACZ,kBAAkB,MAAM,KAAK,kBAC5B,8OAAC;gEAAI,WAAU;0EAAgC;;;;;qFAI/C,8OAAC;gEAAI,WAAU;0EACZ,kBAAkB,GAAG,CAAC,CAAC,uBACtB,8OAAC;wEAEC,WAAW,CAAC,2DAA2D,EACrE,SAAS,SAAS,KAAK,OAAO,SAAS,GACnC,4CACA,4DACJ;wEACF,SAAS,IAAM,mBAAmB;kFAElC,cAAA,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;wFACZ,SAAS,SAAS,KAAK,OAAO,SAAS,kBACtC,8OAAC,2NAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;sGAEzB,8OAAC;4FAAI,WAAU;sGACZ,gBAAgB,OAAO,SAAS;;;;;;sGAEnC,8OAAC;4FAAI,WAAU;;gGACZ,OAAO,SAAS;gGAAC;gGAAI,OAAO,OAAO;;;;;;;;;;;;;8FAGxC,8OAAC,iIAAA,CAAA,QAAK;oFAAC,SAAQ;oFAAU,WAAU;;wFAChC,OAAO,aAAa;wFAAC;;;;;;;;;;;;;uEArBrB,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;8DAgCjC,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,wNAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;gEAAY;gEACd,oBAAoB,MAAM;gEAAC;;;;;;;sEAEpD,8OAAC;4DAAI,WAAU;sEACZ,oBAAoB,MAAM,KAAK,kBAC9B,8OAAC;gEAAI,WAAU;0EAAgC;;;;;qFAI/C,8OAAC;gEAAI,WAAU;0EACZ,oBAAoB,GAAG,CAAC,CAAC,uBACxB,8OAAC;wEAEC,WAAW,CAAC,2DAA2D,EACrE,SAAS,SAAS,KAAK,OAAO,SAAS,GACnC,4CACA,4DACJ;wEACF,SAAS,IAAM,mBAAmB;kFAElC,cAAA,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;wFACZ,SAAS,SAAS,KAAK,OAAO,SAAS,kBACtC,8OAAC,2NAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;sGAEzB,8OAAC;4FAAI,WAAU;sGACZ,gBAAgB,OAAO,SAAS;;;;;;sGAEnC,8OAAC;4FAAI,WAAU;;gGACZ,OAAO,SAAS;gGAAC;gGAAI,OAAO,OAAO;;;;;;;;;;;;;8FAGxC,8OAAC,iIAAA,CAAA,QAAK;oFAAC,SAAQ;oFAAU,WAAU;;wFAChC,OAAO,aAAa;wFAAC;;;;;;;;;;;;;uEArBrB,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;wCAiCpC,OAAO,MAAM,KAAK,KAAK,CAAC,+BACvB,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;8DACzB,8OAAC,iIAAA,CAAA,mBAAgB;oDAAC,WAAU;8DAAiB;;;;;;;;;;;;wCAMhD,WAAW,SAAS,kBACnB,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;;8DACb,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC,iIAAA,CAAA,mBAAgB;8DAAE,WAAW,SAAS;;;;;;;;;;;;;;;;;;;;;;;;sCAM/C,8OAAC,kIAAA,CAAA,eAAY;4BAAC,WAAU;;8CAEtB,8OAAC;8CACE,KAAK,mBAAmB,KAAK,gBAAgB,KAAK,SAAS,kBAC1D,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS;4CACP,4BAA4B;wCAC9B;wCACA,UAAU;wCACV,WAAU;kDACX;;;;;;;;;;;8CAOL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS;4CAAa,UAAU;sDAAQ;;;;;;sDAGlE,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UACE,UACA,CAAC,SAAS,YAAY,IACtB,WAAW,SAAS,YAAY,IAAI,KACpC,CAAC,SAAS,SAAS;4CAErB,WAAU;;gDAET,wBAAU,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASvE,8OAAC,kJAAA,CAAA,UAAoB;gBACnB,MAAM;gBACN,SAAS,IAAM,4BAA4B;gBAC3C,MAAM;gBACN,WAAW,CAAC;oBACV,UAAU;oBACV,4BAA4B;oBAC5B,UAAU,qDAAqD;;gBACjE;gBACA,SAAS;;;;;;;;AAIjB", "debugId": null}}, {"offset": {"line": 4198, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/cavi/CollegamentiDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport * as VisuallyHidden from '@radix-ui/react-visually-hidden'\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Zap, CheckCircle, AlertTriangle, X } from 'lucide-react'\nimport { Cavo } from '@/types'\nimport { caviApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useToastActions } from '@/components/ui/toast-notification'\n\ninterface CollegamentiDialogProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  onSuccess?: () => void\n  onError?: (error: string) => void\n}\n\ninterface ConfirmDialogState {\n  open: boolean\n  type: 'partenza' | 'arrivo' | 'entrambi' | null\n  title: string\n  description: string\n}\n\ninterface ConfirmDisconnectDialogProps {\n  open: boolean\n  onClose: () => void\n  onConfirm: () => void\n  title: string\n  description: string\n  isLoading: boolean\n  isDangerous?: boolean\n}\n\nfunction ConfirmDisconnectDialog({\n  open,\n  onClose,\n  onConfirm,\n  title,\n  description,\n  isLoading,\n  isDangerous = false\n}: ConfirmDisconnectDialogProps) {\n  const [showFinalConfirmation, setShowFinalConfirmation] = useState(false)\n\n  useEffect(() => {\n    if (!open) {\n      setShowFinalConfirmation(false)\n    }\n  }, [open])\n\n  useEffect(() => {\n    const handleEsc = (e: KeyboardEvent) => {\n      if (e.key === 'Escape' && open && !isLoading) {\n        onClose()\n      }\n    }\n\n    if (open) {\n      document.addEventListener('keydown', handleEsc)\n      return () => document.removeEventListener('keydown', handleEsc)\n    }\n  }, [open, onClose, isLoading])\n\n  const handleInitialConfirm = () => {\n    if (isDangerous) {\n      setShowFinalConfirmation(true)\n    } else {\n      onConfirm()\n    }\n  }\n\n  const handleFinalConfirm = () => {\n    onConfirm()\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <DialogContent\n        className=\"sm:max-w-[400px]\"\n        aria-describedby=\"confirm-disconnect-description\"\n      >\n        {!showFinalConfirmation ? (\n          <>\n            <DialogHeader>\n              <DialogTitle className=\"flex items-center gap-2 text-orange-600\">\n                <AlertTriangle className=\"h-5 w-5\" />\n                {title}\n              </DialogTitle>\n              <DialogDescription id=\"confirm-disconnect-description\">\n                {description}\n              </DialogDescription>\n            </DialogHeader>\n\n            <div className=\"py-4\">\n              <Alert className=\"border-orange-200 bg-orange-50\">\n                <AlertTriangle className=\"h-4 w-4 text-orange-600\" />\n                <AlertDescription className=\"text-orange-800\">\n                  <strong>Attenzione:</strong> Questa azione modificherà lo stato del collegamento del cavo.\n                </AlertDescription>\n              </Alert>\n            </div>\n\n            <DialogFooter className=\"gap-2\">\n              <Button\n                variant=\"outline\"\n                onClick={onClose}\n                disabled={isLoading}\n                className=\"flex-1 hover:bg-gray-50\"\n              >\n                Annulla\n              </Button>\n              <Button\n                variant=\"outline\"\n                onClick={handleInitialConfirm}\n                disabled={isLoading}\n                className=\"flex-1 bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100 hover:border-orange-300\"\n              >\n                <AlertTriangle className=\"mr-2 h-4 w-4\" />\n                {isDangerous ? 'Procedi' : 'Conferma'}\n              </Button>\n            </DialogFooter>\n          </>\n        ) : (\n          <>\n            <DialogHeader>\n              <DialogTitle className=\"text-center text-orange-600\">\n                Conferma Finale\n              </DialogTitle>\n            </DialogHeader>\n\n            <div className=\"py-4 text-center\">\n              <div className=\"mx-auto w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-4\">\n                <AlertTriangle className=\"h-6 w-6 text-orange-600\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                Sei veramente sicuro?\n              </h3>\n              <p className=\"text-sm text-gray-600 mb-4\">\n                Questa azione scollegherà <strong>entrambi i lati</strong> del cavo.\n              </p>\n              <div className=\"bg-orange-50 border border-orange-200 rounded-md p-3\">\n                <p className=\"text-sm text-orange-800 font-medium\">\n                  ⚠️ Operazione irreversibile\n                </p>\n              </div>\n            </div>\n\n            <DialogFooter className=\"gap-2\">\n              <Button\n                variant=\"outline\"\n                onClick={() => setShowFinalConfirmation(false)}\n                disabled={isLoading}\n                className=\"flex-1\"\n              >\n                No, Annulla\n              </Button>\n              <Button\n                variant=\"outline\"\n                onClick={handleFinalConfirm}\n                disabled={isLoading}\n                className=\"flex-1 bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100 hover:border-orange-300\"\n              >\n                {isLoading ? (\n                  <>\n                    <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                    Scollegando...\n                  </>\n                ) : (\n                  'Sì, Scollega'\n                )}\n              </Button>\n            </DialogFooter>\n          </>\n        )}\n      </DialogContent>\n    </Dialog>\n  )\n}\n\nfunction CollegamentiDialog({\n  open,\n  onClose,\n  cavo,\n  onSuccess,\n  onError\n}: CollegamentiDialogProps) {\n  const { cantiere } = useAuth()\n  const toast = useToastActions()\n  const [selectedResponsabile, setSelectedResponsabile] = useState('cantiere')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const [confirmDialog, setConfirmDialog] = useState<ConfirmDialogState>({\n    open: false,\n    type: null,\n    title: '',\n    description: ''\n  })\n\n  const dialogRef = useRef<HTMLDivElement>(null)\n  const firstFocusableRef = useRef<HTMLButtonElement>(null)\n  const lastFocusableRef = useRef<HTMLButtonElement>(null)\n\n  const [screenReaderAnnouncement, setScreenReaderAnnouncement] = useState('')\n\n  useEffect(() => {\n    if (open && cavo) {\n      setSelectedResponsabile('cantiere')\n      setError('')\n    }\n  }, [open, cavo])\n\n  useEffect(() => {\n    if (open && dialogRef.current) {\n      const focusableElements = dialogRef.current.querySelectorAll(\n        'button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex=\"-1\"])'\n      )\n\n      if (focusableElements.length > 0) {\n        (focusableElements[0] as HTMLElement).focus()\n      }\n    }\n  }, [open])\n\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if (!open || loading || confirmDialog.open) return\n\n      switch (e.key) {\n        case 'Escape':\n          onClose()\n          break\n\n        case 'Tab':\n          const focusableElements = dialogRef.current?.querySelectorAll(\n            'button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex=\"-1\"])'\n          )\n\n          if (focusableElements && focusableElements.length > 0) {\n            const firstElement = focusableElements[0] as HTMLElement\n            const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement\n\n            if (e.shiftKey && document.activeElement === firstElement) {\n              e.preventDefault()\n              lastElement.focus()\n            } else if (!e.shiftKey && document.activeElement === lastElement) {\n              e.preventDefault()\n              firstElement.focus()\n            }\n          }\n          break\n\n        case '1':\n        case '2':\n        case '3':\n          if (!loading) {\n            e.preventDefault()\n            const num = parseInt(e.key)\n            if (num === 1) handleCollegaPartenza()\n            else if (num === 2) handleCollegaArrivo()\n            else if (num === 3) handleCollegaEntrambi()\n          }\n          break\n      }\n    }\n\n    if (open) {\n      document.addEventListener('keydown', handleKeyDown)\n      return () => document.removeEventListener('keydown', handleKeyDown)\n    }\n  }, [open, loading, confirmDialog.open])\n\n  const announceToScreenReader = (message: string) => {\n    setScreenReaderAnnouncement(message)\n    setTimeout(() => setScreenReaderAnnouncement(''), 1000)\n  }\n\n  const getStatoCollegamento = () => {\n    if (!cavo) return { stato: 'non_collegato', descrizione: 'Non collegato' }\n    \n    const collegamento = cavo.collegamento || cavo.collegamenti || 0\n    \n    switch (collegamento) {\n      case 1:\n        return { stato: 'partenza', descrizione: '🟢⚪ Collegato lato partenza' }\n      case 2:\n        return { stato: 'arrivo', descrizione: '⚪🟢 Collegato lato arrivo' }\n      case 3:\n        return { stato: 'completo', descrizione: '🟢🟢 Completamente collegato' }\n      default:\n        return { stato: 'non_collegato', descrizione: '⚪⚪ Non collegato' }\n    }\n  }\n\n  const handleCollegaPartenza = () => {\n    if (!cavo) return\n\n    const statoAttuale = getStatoCollegamento()\n\n    if (statoAttuale.stato === 'partenza' || statoAttuale.stato === 'completo') {\n      setConfirmDialog({\n        open: true,\n        type: 'partenza',\n        title: 'Scollega lato partenza',\n        description: `Vuoi scollegare il lato partenza del cavo ${cavo.id_cavo}?`\n      })\n    } else {\n      executeCollegaPartenza()\n    }\n  }\n\n  const executeCollegaPartenza = async () => {\n    if (!cavo || !cantiere) return\n\n    try {\n      setLoading(true)\n      setError('')\n      announceToScreenReader('Collegamento in corso...')\n\n      await caviApi.collegaCavo(\n        cantiere.id_cantiere,\n        cavo.id_cavo,\n        'partenza',\n        selectedResponsabile\n      )\n\n      const message = `Collegamento lato partenza completato per il cavo ${cavo.id_cavo}`\n      announceToScreenReader(message)\n      toast.success(\"Successo\", message)\n\n      if (onSuccess) onSuccess()\n      onClose()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante il collegamento'\n      setError(errorMessage)\n      announceToScreenReader(`Errore: ${errorMessage}`)\n      if (onError) onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleCollegaArrivo = () => {\n    if (!cavo) return\n\n    const statoAttuale = getStatoCollegamento()\n\n    if (statoAttuale.stato === 'arrivo' || statoAttuale.stato === 'completo') {\n      setConfirmDialog({\n        open: true,\n        type: 'arrivo',\n        title: 'Scollega lato arrivo',\n        description: `Vuoi scollegare il lato arrivo del cavo ${cavo.id_cavo}?`\n      })\n    } else {\n      executeCollegaArrivo()\n    }\n  }\n\n  const executeCollegaArrivo = async () => {\n    if (!cavo || !cantiere) return\n\n    try {\n      setLoading(true)\n      setError('')\n      announceToScreenReader('Collegamento in corso...')\n\n      await caviApi.collegaCavo(\n        cantiere.id_cantiere,\n        cavo.id_cavo,\n        'arrivo',\n        selectedResponsabile\n      )\n\n      const message = `Collegamento lato arrivo completato per il cavo ${cavo.id_cavo}`\n      announceToScreenReader(message)\n      toast.success(\"Successo\", message)\n\n      if (onSuccess) onSuccess()\n      onClose()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante il collegamento'\n      setError(errorMessage)\n      announceToScreenReader(`Errore: ${errorMessage}`)\n      if (onError) onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleCollegaEntrambi = () => {\n    if (!cavo) return\n\n    const statoAttuale = getStatoCollegamento()\n\n    if (statoAttuale.stato === 'completo') {\n      setConfirmDialog({\n        open: true,\n        type: 'entrambi',\n        title: 'Scollega entrambi i lati',\n        description: `Vuoi scollegare completamente il cavo ${cavo.id_cavo}? Questa operazione rimuoverà tutti i collegamenti.`\n      })\n    } else {\n      executeCollegaEntrambi()\n    }\n  }\n\n  const executeCollegaEntrambi = async () => {\n    if (!cavo || !cantiere) return\n\n    try {\n      setLoading(true)\n      setError('')\n      announceToScreenReader('Collegamento entrambi i lati in corso...')\n\n      await caviApi.collegaCavo(\n        cantiere.id_cantiere,\n        cavo.id_cavo,\n        'entrambi',\n        selectedResponsabile\n      )\n\n      const message = `Collegamento completo per il cavo ${cavo.id_cavo}`\n      announceToScreenReader(message)\n      toast.success(\"Successo\", message)\n\n      if (onSuccess) onSuccess()\n      onClose()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante il collegamento'\n      setError(errorMessage)\n      announceToScreenReader(`Errore: ${errorMessage}`)\n      if (onError) onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const executeDisconnect = async () => {\n    if (!cavo || !cantiere || !confirmDialog.type) return\n\n    try {\n      setLoading(true)\n      setError('')\n      announceToScreenReader('Scollegamento in corso...')\n\n      await caviApi.scollegaCavo(\n        cantiere.id_cantiere,\n        cavo.id_cavo,\n        confirmDialog.type === 'entrambi' ? undefined : confirmDialog.type\n      )\n\n      const latoText = confirmDialog.type === 'entrambi' ? '' : ` lato ${confirmDialog.type}`\n      const message = `Scollegamento${latoText} completato per il cavo ${cavo.id_cavo}`\n      announceToScreenReader(message)\n      toast.success(\"Successo\", message)\n\n      if (onSuccess) onSuccess()\n      setConfirmDialog({ open: false, type: null, title: '', description: '' })\n      onClose()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante lo scollegamento'\n      setError(errorMessage)\n      announceToScreenReader(`Errore: ${errorMessage}`)\n      if (onError) onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (!cavo) return null\n\n  const statoCollegamento = getStatoCollegamento()\n  const isInstalled = (cavo.metri_posati || cavo.metratura_reale || 0) > 0\n\n  return (\n    <>\n      <VisuallyHidden.Root>\n        <div aria-live=\"polite\" aria-atomic=\"true\">\n          {screenReaderAnnouncement}\n        </div>\n      </VisuallyHidden.Root>\n\n      <Dialog open={open} onOpenChange={onClose}>\n        <DialogContent\n          className=\"sm:max-w-[600px] max-h-[90vh] overflow-y-auto\"\n          ref={dialogRef}\n          aria-describedby=\"collegamenti-description\"\n        >\n          <DialogHeader>\n            <DialogTitle className=\"flex items-center gap-2 text-blue-600\">\n              <Zap className=\"h-5 w-5\" />\n              Gestione Collegamenti - {cavo.id_cavo}\n            </DialogTitle>\n            <DialogDescription id=\"collegamenti-description\">\n              Gestisci i collegamenti del cavo {cavo.id_cavo}. Usa i tasti 1, 2, 3 per azioni rapide.\n            </DialogDescription>\n          </DialogHeader>\n\n          <div className=\"space-y-6\">\n            {/* Informazioni cavo */}\n            <div className=\"p-4 bg-blue-50 rounded-lg border-2 border-blue-200\">\n              <div className=\"text-sm font-medium text-blue-800\">\n                Informazioni Cavo / Tipologia: {cavo.tipologia || 'N/A'} / Da: {cavo.ubicazione_partenza || 'N/A'} / Formazione: {cavo.sezione || 'N/A'} / A: {cavo.ubicazione_arrivo || 'N/A'} / Metri Posati: {cavo.metratura_reale || 0} m\n              </div>\n            </div>\n\n            {/* Stato attuale con icone migliorate */}\n            <div className=\"p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <Label className=\"text-sm font-medium text-gray-700\">Stato Collegamento</Label>\n                  <div className=\"mt-1 text-lg font-semibold flex items-center gap-2\">\n                    {statoCollegamento.stato === 'completo' && <CheckCircle className=\"h-5 w-5 text-green-600\" />}\n                    {statoCollegamento.stato === 'non_collegato' && <AlertCircle className=\"h-5 w-5 text-gray-400\" />}\n                    {(statoCollegamento.stato === 'partenza' || statoCollegamento.stato === 'arrivo') && <AlertTriangle className=\"h-5 w-5 text-orange-500\" />}\n                    {statoCollegamento.descrizione}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {!isInstalled && (\n              <Alert className=\"border-orange-200 bg-orange-50\">\n                <AlertCircle className=\"h-4 w-4 text-orange-600\" />\n                <AlertDescription className=\"text-orange-800\">\n                  <strong>Attenzione:</strong> Il cavo deve essere installato prima di poter essere collegato.\n                </AlertDescription>\n              </Alert>\n            )}\n\n            {error && (\n              <Alert variant=\"destructive\">\n                <AlertCircle className=\"h-4 w-4\" />\n                <AlertDescription>{error}</AlertDescription>\n              </Alert>\n            )}\n\n            {isInstalled && (\n              <>\n                {/* Selezione responsabile semplificata */}\n                <div className=\"space-y-3\">\n                  <Label className=\"text-sm font-medium\">Responsabile Collegamento</Label>\n                  <div className=\"p-3 bg-gray-50 rounded-lg border\">\n                    <div className=\"flex items-center gap-2\">\n                      <CheckCircle className=\"h-4 w-4 text-green-600\" />\n                      <span className=\"font-medium\">Cantiere</span>\n                    </div>\n                    <p className=\"text-sm text-gray-600 mt-1\">\n                      Collegamento eseguito dal responsabile del cantiere\n                    </p>\n                  </div>\n                </div>\n\n                {/* Azioni di collegamento con design migliorato */}\n                <div className=\"space-y-4\">\n                  <Label className=\"text-sm font-medium\">Azioni Disponibili</Label>\n\n                  <div className=\"grid grid-cols-1 gap-3\">\n                    <Button\n                      ref={firstFocusableRef}\n                      onClick={handleCollegaPartenza}\n                      disabled={loading}\n                      className=\"w-full h-12 text-left justify-start bg-green-50 text-green-700 border-green-200 hover:bg-green-100 hover:border-green-300\"\n                      variant=\"outline\"\n                    >\n                      <div className=\"flex items-center justify-between w-full\">\n                        <div className=\"flex items-center gap-3\">\n                          <div className=\"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\">\n                            <span className=\"text-sm font-bold text-green-700\">1</span>\n                          </div>\n                          <div>\n                            <div className=\"font-medium\">\n                              {statoCollegamento.stato === 'partenza' || statoCollegamento.stato === 'completo'\n                                ? 'Scollega Partenza'\n                                : 'Collega Partenza'}\n                            </div>\n                            <div className=\"text-xs text-green-600\">\n                              {statoCollegamento.stato === 'partenza' || statoCollegamento.stato === 'completo'\n                                ? 'Rimuovi collegamento lato partenza'\n                                : 'Connetti il lato partenza del cavo'}\n                            </div>\n                          </div>\n                        </div>\n                        {loading ? <Loader2 className=\"h-4 w-4 animate-spin\" /> : <Zap className=\"h-4 w-4\" />}\n                      </div>\n                    </Button>\n\n                    <Button\n                      onClick={handleCollegaArrivo}\n                      disabled={loading}\n                      className=\"w-full h-12 text-left justify-start bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 hover:border-blue-300\"\n                      variant=\"outline\"\n                    >\n                      <div className=\"flex items-center justify-between w-full\">\n                        <div className=\"flex items-center gap-3\">\n                          <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\n                            <span className=\"text-sm font-bold text-blue-700\">2</span>\n                          </div>\n                          <div>\n                            <div className=\"font-medium\">\n                              {statoCollegamento.stato === 'arrivo' || statoCollegamento.stato === 'completo'\n                                ? 'Scollega Arrivo'\n                                : 'Collega Arrivo'}\n                            </div>\n                            <div className=\"text-xs text-blue-600\">\n                              {statoCollegamento.stato === 'arrivo' || statoCollegamento.stato === 'completo'\n                                ? 'Rimuovi collegamento lato arrivo'\n                                : 'Connetti il lato arrivo del cavo'}\n                            </div>\n                          </div>\n                        </div>\n                        {loading ? <Loader2 className=\"h-4 w-4 animate-spin\" /> : <Zap className=\"h-4 w-4\" />}\n                      </div>\n                    </Button>\n\n                    <Button\n                      onClick={handleCollegaEntrambi}\n                      disabled={loading}\n                      className=\"w-full h-12 text-left justify-start bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100 hover:border-purple-300\"\n                      variant=\"outline\"\n                    >\n                      <div className=\"flex items-center justify-between w-full\">\n                        <div className=\"flex items-center gap-3\">\n                          <div className=\"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center\">\n                            <span className=\"text-sm font-bold text-purple-700\">3</span>\n                          </div>\n                          <div>\n                            <div className=\"font-medium\">\n                              {statoCollegamento.stato === 'completo'\n                                ? 'Scollega Completamente'\n                                : 'Collega Entrambi'}\n                            </div>\n                            <div className=\"text-xs text-purple-600\">\n                              {statoCollegamento.stato === 'completo'\n                                ? 'Rimuovi tutti i collegamenti'\n                                : 'Connetti entrambi i lati del cavo'}\n                            </div>\n                          </div>\n                        </div>\n                        {loading ? <Loader2 className=\"h-4 w-4 animate-spin\" /> : <Zap className=\"h-4 w-4\" />}\n                      </div>\n                    </Button>\n                  </div>\n                </div>\n              </>\n            )}\n          </div>\n\n          <DialogFooter>\n            <Button\n              ref={lastFocusableRef}\n              variant=\"outline\"\n              onClick={onClose}\n              disabled={loading}\n              className=\"hover:bg-gray-50\"\n            >\n              <X className=\"mr-2 h-4 w-4\" />\n              Chiudi\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      <ConfirmDisconnectDialog\n        open={confirmDialog.open}\n        onClose={() => setConfirmDialog({ open: false, type: null, title: '', description: '' })}\n        onConfirm={executeDisconnect}\n        title={confirmDialog.title}\n        description={confirmDialog.description}\n        isLoading={loading}\n        isDangerous={confirmDialog.type === 'entrambi'}\n      />\n    </>\n  )\n}\n\nexport default CollegamentiDialog\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAQA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AApBA;;;;;;;;;;;;AA+CA,SAAS,wBAAwB,EAC/B,IAAI,EACJ,OAAO,EACP,SAAS,EACT,KAAK,EACL,WAAW,EACX,SAAS,EACT,cAAc,KAAK,EACU;IAC7B,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,MAAM;YACT,yBAAyB;QAC3B;IACF,GAAG;QAAC;KAAK;IAET,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,CAAC;YACjB,IAAI,EAAE,GAAG,KAAK,YAAY,QAAQ,CAAC,WAAW;gBAC5C;YACF;QACF;QAEA,IAAI,MAAM;YACR,SAAS,gBAAgB,CAAC,WAAW;YACrC,OAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;QACvD;IACF,GAAG;QAAC;QAAM;QAAS;KAAU;IAE7B,MAAM,uBAAuB;QAC3B,IAAI,aAAa;YACf,yBAAyB;QAC3B,OAAO;YACL;QACF;IACF;IAEA,MAAM,qBAAqB;QACzB;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YACZ,WAAU;YACV,oBAAiB;sBAEhB,CAAC,sCACA;;kCACE,8OAAC,kIAAA,CAAA,eAAY;;0CACX,8OAAC,kIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCACxB;;;;;;;0CAEH,8OAAC,kIAAA,CAAA,oBAAiB;gCAAC,IAAG;0CACnB;;;;;;;;;;;;kCAIL,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC,iIAAA,CAAA,mBAAgB;oCAAC,WAAU;;sDAC1B,8OAAC;sDAAO;;;;;;wCAAoB;;;;;;;;;;;;;;;;;;kCAKlC,8OAAC,kIAAA,CAAA,eAAY;wBAAC,WAAU;;0CACtB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU;gCACV,WAAU;0CACX;;;;;;0CAGD,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU;gCACV,WAAU;;kDAEV,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCACxB,cAAc,YAAY;;;;;;;;;;;;;;6CAKjC;;kCACE,8OAAC,kIAAA,CAAA,eAAY;kCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;sCAA8B;;;;;;;;;;;kCAKvD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;;;;;;0CAE3B,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,8OAAC;gCAAE,WAAU;;oCAA6B;kDACd,8OAAC;kDAAO;;;;;;oCAAwB;;;;;;;0CAE5D,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAsC;;;;;;;;;;;;;;;;;kCAMvD,8OAAC,kIAAA,CAAA,eAAY;wBAAC,WAAU;;0CACtB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS,IAAM,yBAAyB;gCACxC,UAAU;gCACV,WAAU;0CACX;;;;;;0CAGD,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,0BACC;;sDACE,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAA8B;;mDAInD;;;;;;;;;;;;;;;;;;;;;;;;AASlB;AAEA,SAAS,mBAAmB,EAC1B,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,SAAS,EACT,OAAO,EACiB;IACxB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,QAAQ,CAAA,GAAA,iJAAA,CAAA,kBAAe,AAAD;IAC5B,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;QACrE,MAAM;QACN,MAAM;QACN,OAAO;QACP,aAAa;IACf;IAEA,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IACpD,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAEnD,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,MAAM;YAChB,wBAAwB;YACxB,SAAS;QACX;IACF,GAAG;QAAC;QAAM;KAAK;IAEf,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,UAAU,OAAO,EAAE;YAC7B,MAAM,oBAAoB,UAAU,OAAO,CAAC,gBAAgB,CAC1D;YAGF,IAAI,kBAAkB,MAAM,GAAG,GAAG;gBAC/B,iBAAiB,CAAC,EAAE,CAAiB,KAAK;YAC7C;QACF;IACF,GAAG;QAAC;KAAK;IAET,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,IAAI,CAAC,QAAQ,WAAW,cAAc,IAAI,EAAE;YAE5C,OAAQ,EAAE,GAAG;gBACX,KAAK;oBACH;oBACA;gBAEF,KAAK;oBACH,MAAM,oBAAoB,UAAU,OAAO,EAAE,iBAC3C;oBAGF,IAAI,qBAAqB,kBAAkB,MAAM,GAAG,GAAG;wBACrD,MAAM,eAAe,iBAAiB,CAAC,EAAE;wBACzC,MAAM,cAAc,iBAAiB,CAAC,kBAAkB,MAAM,GAAG,EAAE;wBAEnE,IAAI,EAAE,QAAQ,IAAI,SAAS,aAAa,KAAK,cAAc;4BACzD,EAAE,cAAc;4BAChB,YAAY,KAAK;wBACnB,OAAO,IAAI,CAAC,EAAE,QAAQ,IAAI,SAAS,aAAa,KAAK,aAAa;4BAChE,EAAE,cAAc;4BAChB,aAAa,KAAK;wBACpB;oBACF;oBACA;gBAEF,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH,IAAI,CAAC,SAAS;wBACZ,EAAE,cAAc;wBAChB,MAAM,MAAM,SAAS,EAAE,GAAG;wBAC1B,IAAI,QAAQ,GAAG;6BACV,IAAI,QAAQ,GAAG;6BACf,IAAI,QAAQ,GAAG;oBACtB;oBACA;YACJ;QACF;QAEA,IAAI,MAAM;YACR,SAAS,gBAAgB,CAAC,WAAW;YACrC,OAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;QACvD;IACF,GAAG;QAAC;QAAM;QAAS,cAAc,IAAI;KAAC;IAEtC,MAAM,yBAAyB,CAAC;QAC9B,4BAA4B;QAC5B,WAAW,IAAM,4BAA4B,KAAK;IACpD;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,MAAM,OAAO;YAAE,OAAO;YAAiB,aAAa;QAAgB;QAEzE,MAAM,eAAe,KAAK,YAAY,IAAI,KAAK,YAAY,IAAI;QAE/D,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,OAAO;oBAAY,aAAa;gBAA8B;YACzE,KAAK;gBACH,OAAO;oBAAE,OAAO;oBAAU,aAAa;gBAA4B;YACrE,KAAK;gBACH,OAAO;oBAAE,OAAO;oBAAY,aAAa;gBAA+B;YAC1E;gBACE,OAAO;oBAAE,OAAO;oBAAiB,aAAa;gBAAmB;QACrE;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,MAAM;QAEX,MAAM,eAAe;QAErB,IAAI,aAAa,KAAK,KAAK,cAAc,aAAa,KAAK,KAAK,YAAY;YAC1E,iBAAiB;gBACf,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,aAAa,CAAC,0CAA0C,EAAE,KAAK,OAAO,CAAC,CAAC,CAAC;YAC3E;QACF,OAAO;YACL;QACF;IACF;IAEA,MAAM,yBAAyB;QAC7B,IAAI,CAAC,QAAQ,CAAC,UAAU;QAExB,IAAI;YACF,WAAW;YACX,SAAS;YACT,uBAAuB;YAEvB,MAAM,iHAAA,CAAA,UAAO,CAAC,WAAW,CACvB,SAAS,WAAW,EACpB,KAAK,OAAO,EACZ,YACA;YAGF,MAAM,UAAU,CAAC,kDAAkD,EAAE,KAAK,OAAO,EAAE;YACnF,uBAAuB;YACvB,MAAM,OAAO,CAAC,YAAY;YAE1B,IAAI,WAAW;YACf;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,SAAS;YACT,uBAAuB,CAAC,QAAQ,EAAE,cAAc;YAChD,IAAI,SAAS,QAAQ;QACvB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,MAAM;QAEX,MAAM,eAAe;QAErB,IAAI,aAAa,KAAK,KAAK,YAAY,aAAa,KAAK,KAAK,YAAY;YACxE,iBAAiB;gBACf,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,aAAa,CAAC,wCAAwC,EAAE,KAAK,OAAO,CAAC,CAAC,CAAC;YACzE;QACF,OAAO;YACL;QACF;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,QAAQ,CAAC,UAAU;QAExB,IAAI;YACF,WAAW;YACX,SAAS;YACT,uBAAuB;YAEvB,MAAM,iHAAA,CAAA,UAAO,CAAC,WAAW,CACvB,SAAS,WAAW,EACpB,KAAK,OAAO,EACZ,UACA;YAGF,MAAM,UAAU,CAAC,gDAAgD,EAAE,KAAK,OAAO,EAAE;YACjF,uBAAuB;YACvB,MAAM,OAAO,CAAC,YAAY;YAE1B,IAAI,WAAW;YACf;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,SAAS;YACT,uBAAuB,CAAC,QAAQ,EAAE,cAAc;YAChD,IAAI,SAAS,QAAQ;QACvB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,MAAM;QAEX,MAAM,eAAe;QAErB,IAAI,aAAa,KAAK,KAAK,YAAY;YACrC,iBAAiB;gBACf,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,aAAa,CAAC,sCAAsC,EAAE,KAAK,OAAO,CAAC,mDAAmD,CAAC;YACzH;QACF,OAAO;YACL;QACF;IACF;IAEA,MAAM,yBAAyB;QAC7B,IAAI,CAAC,QAAQ,CAAC,UAAU;QAExB,IAAI;YACF,WAAW;YACX,SAAS;YACT,uBAAuB;YAEvB,MAAM,iHAAA,CAAA,UAAO,CAAC,WAAW,CACvB,SAAS,WAAW,EACpB,KAAK,OAAO,EACZ,YACA;YAGF,MAAM,UAAU,CAAC,kCAAkC,EAAE,KAAK,OAAO,EAAE;YACnE,uBAAuB;YACvB,MAAM,OAAO,CAAC,YAAY;YAE1B,IAAI,WAAW;YACf;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,SAAS;YACT,uBAAuB,CAAC,QAAQ,EAAE,cAAc;YAChD,IAAI,SAAS,QAAQ;QACvB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,cAAc,IAAI,EAAE;QAE/C,IAAI;YACF,WAAW;YACX,SAAS;YACT,uBAAuB;YAEvB,MAAM,iHAAA,CAAA,UAAO,CAAC,YAAY,CACxB,SAAS,WAAW,EACpB,KAAK,OAAO,EACZ,cAAc,IAAI,KAAK,aAAa,YAAY,cAAc,IAAI;YAGpE,MAAM,WAAW,cAAc,IAAI,KAAK,aAAa,KAAK,CAAC,MAAM,EAAE,cAAc,IAAI,EAAE;YACvF,MAAM,UAAU,CAAC,aAAa,EAAE,SAAS,wBAAwB,EAAE,KAAK,OAAO,EAAE;YACjF,uBAAuB;YACvB,MAAM,OAAO,CAAC,YAAY;YAE1B,IAAI,WAAW;YACf,iBAAiB;gBAAE,MAAM;gBAAO,MAAM;gBAAM,OAAO;gBAAI,aAAa;YAAG;YACvE;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,SAAS;YACT,uBAAuB,CAAC,QAAQ,EAAE,cAAc;YAChD,IAAI,SAAS,QAAQ;QACvB,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,oBAAoB;IAC1B,MAAM,cAAc,CAAC,KAAK,YAAY,IAAI,KAAK,eAAe,IAAI,CAAC,IAAI;IAEvE,qBACE;;0BACE,8OAAC,8KAAA,CAAA,OAAmB;0BAClB,cAAA,8OAAC;oBAAI,aAAU;oBAAS,eAAY;8BACjC;;;;;;;;;;;0BAIL,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAM,cAAc;0BAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBACZ,WAAU;oBACV,KAAK;oBACL,oBAAiB;;sCAEjB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAY;wCACF,KAAK,OAAO;;;;;;;8CAEvC,8OAAC,kIAAA,CAAA,oBAAiB;oCAAC,IAAG;;wCAA2B;wCACb,KAAK,OAAO;wCAAC;;;;;;;;;;;;;sCAInD,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;4CAAoC;4CACjB,KAAK,SAAS,IAAI;4CAAM;4CAAQ,KAAK,mBAAmB,IAAI;4CAAM;4CAAgB,KAAK,OAAO,IAAI;4CAAM;4CAAO,KAAK,iBAAiB,IAAI;4CAAM;4CAAkB,KAAK,eAAe,IAAI;4CAAE;;;;;;;;;;;;8CAK/N,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;8DAAoC;;;;;;8DACrD,8OAAC;oDAAI,WAAU;;wDACZ,kBAAkB,KAAK,KAAK,4BAAc,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDACjE,kBAAkB,KAAK,KAAK,iCAAmB,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDACtE,CAAC,kBAAkB,KAAK,KAAK,cAAc,kBAAkB,KAAK,KAAK,QAAQ,mBAAK,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;wDAC7G,kBAAkB,WAAW;;;;;;;;;;;;;;;;;;;;;;;gCAMrC,CAAC,6BACA,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;;sDACf,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC,iIAAA,CAAA,mBAAgB;4CAAC,WAAU;;8DAC1B,8OAAC;8DAAO;;;;;;gDAAoB;;;;;;;;;;;;;gCAKjC,uBACC,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;;sDACb,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC,iIAAA,CAAA,mBAAgB;sDAAE;;;;;;;;;;;;gCAItB,6BACC;;sDAEE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;8DAAsB;;;;;;8DACvC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;8EACvB,8OAAC;oEAAK,WAAU;8EAAc;;;;;;;;;;;;sEAEhC,8OAAC;4DAAE,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;sDAO9C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;8DAAsB;;;;;;8DAEvC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,KAAK;4DACL,SAAS;4DACT,UAAU;4DACV,WAAU;4DACV,SAAQ;sEAER,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC;oFAAK,WAAU;8FAAmC;;;;;;;;;;;0FAErD,8OAAC;;kGACC,8OAAC;wFAAI,WAAU;kGACZ,kBAAkB,KAAK,KAAK,cAAc,kBAAkB,KAAK,KAAK,aACnE,sBACA;;;;;;kGAEN,8OAAC;wFAAI,WAAU;kGACZ,kBAAkB,KAAK,KAAK,cAAc,kBAAkB,KAAK,KAAK,aACnE,uCACA;;;;;;;;;;;;;;;;;;oEAIT,wBAAU,8OAAC,iNAAA,CAAA,UAAO;wEAAC,WAAU;;;;;6FAA4B,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;;;;;;;sEAI7E,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS;4DACT,UAAU;4DACV,WAAU;4DACV,SAAQ;sEAER,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC;oFAAK,WAAU;8FAAkC;;;;;;;;;;;0FAEpD,8OAAC;;kGACC,8OAAC;wFAAI,WAAU;kGACZ,kBAAkB,KAAK,KAAK,YAAY,kBAAkB,KAAK,KAAK,aACjE,oBACA;;;;;;kGAEN,8OAAC;wFAAI,WAAU;kGACZ,kBAAkB,KAAK,KAAK,YAAY,kBAAkB,KAAK,KAAK,aACjE,qCACA;;;;;;;;;;;;;;;;;;oEAIT,wBAAU,8OAAC,iNAAA,CAAA,UAAO;wEAAC,WAAU;;;;;6FAA4B,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;;;;;;;sEAI7E,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS;4DACT,UAAU;4DACV,WAAU;4DACV,SAAQ;sEAER,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC;oFAAK,WAAU;8FAAoC;;;;;;;;;;;0FAEtD,8OAAC;;kGACC,8OAAC;wFAAI,WAAU;kGACZ,kBAAkB,KAAK,KAAK,aACzB,2BACA;;;;;;kGAEN,8OAAC;wFAAI,WAAU;kGACZ,kBAAkB,KAAK,KAAK,aACzB,iCACA;;;;;;;;;;;;;;;;;;oEAIT,wBAAU,8OAAC,iNAAA,CAAA,UAAO;wEAAC,WAAU;;;;;6FAA4B,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASvF,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,KAAK;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU;gCACV,WAAU;;kDAEV,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;0BAOtC,8OAAC;gBACC,MAAM,cAAc,IAAI;gBACxB,SAAS,IAAM,iBAAiB;wBAAE,MAAM;wBAAO,MAAM;wBAAM,OAAO;wBAAI,aAAa;oBAAG;gBACtF,WAAW;gBACX,OAAO,cAAc,KAAK;gBAC1B,aAAa,cAAc,WAAW;gBACtC,WAAW;gBACX,aAAa,cAAc,IAAI,KAAK;;;;;;;;AAI5C;uCAEe", "debugId": null}}, {"offset": {"line": 5378, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/cavi/CertificazioneDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Award, FileText, Download, CheckCircle, X } from 'lucide-react'\nimport { Cavo } from '@/types'\nimport { certificazioniApi, responsabiliApi, caviApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useToast } from '@/hooks/use-toast'\n\ninterface CertificazioneDialogProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\ninterface Responsabile {\n  id: number\n  nome_responsabile: string\n  numero_telefono?: string\n  mail?: string\n}\n\nexport default function CertificazioneDialog({\n  open,\n  onClose,\n  cavo,\n  onSuccess,\n  onError\n}: CertificazioneDialogProps) {\n  const { cantiere } = useAuth()\n  const { toast } = useToast()\n  const [formData, setFormData] = useState({\n    responsabile_certificazione: '',\n    data_certificazione: new Date().toISOString().split('T')[0],\n    esito_certificazione: 'CONFORME',\n    note_certificazione: ''\n  })\n  const [responsabili, setResponsabili] = useState<Responsabile[]>([])\n  const [loading, setLoading] = useState(false)\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false)\n  const [error, setError] = useState('')\n  const [screenReaderAnnouncement, setScreenReaderAnnouncement] = useState('')\n\n  // Funzione per annunci screen reader\n  const announceToScreenReader = (message: string) => {\n    setScreenReaderAnnouncement(message)\n    setTimeout(() => setScreenReaderAnnouncement(''), 1000)\n  }\n\n  // Reset form quando si apre il dialog\n  useEffect(() => {\n    if (open && cavo) {\n      setFormData({\n        responsabile_certificazione: '',\n        data_certificazione: new Date().toISOString().split('T')[0],\n        esito_certificazione: 'CONFORME',\n        note_certificazione: ''\n      })\n      setError('')\n      loadResponsabili()\n    }\n  }, [open, cavo])\n\n  const loadResponsabili = async () => {\n    if (!cantiere) return\n\n    try {\n      setLoadingResponsabili(true)\n      const response = await responsabiliApi.getResponsabili(cantiere.id_cantiere)\n      setResponsabili(response.data)\n    } catch (error) {\n      setResponsabili([])\n    } finally {\n      setLoadingResponsabili(false)\n    }\n  }\n\n  const isCavoCollegato = () => {\n    if (!cavo) return false\n    const collegamento = cavo.collegamento || cavo.collegamenti || 0\n    return collegamento === 3 // Completamente collegato\n  }\n\n  const isCavoCertificato = () => {\n    if (!cavo) return false\n    return cavo.certificato === true || cavo.certificato === 'SI' || cavo.certificato === 'CERTIFICATO'\n  }\n\n  // Funzione per collegare automaticamente il cavo durante la certificazione\n  const autoCollegaCavo = async () => {\n    if (!cavo || !cantiere) return false\n\n    try {\n      announceToScreenReader('Collegamento automatico in corso...')\n      \n      // Collega entrambi i lati a \"cantiere\"\n      await caviApi.collegaCavo(\n        cantiere.id_cantiere,\n        cavo.id_cavo,\n        'entrambi',\n        'cantiere'\n      )\n\n      announceToScreenReader('Cavo collegato automaticamente')\n      return true\n    } catch (error) {\n      console.error('Errore nel collegamento automatico:', error)\n      return false\n    }\n  }\n\n  const handleCertifica = async () => {\n    if (!cavo || !cantiere) return\n\n    if (!formData.responsabile_certificazione) {\n      setError('Seleziona un responsabile per la certificazione')\n      announceToScreenReader('Errore: Seleziona un responsabile per la certificazione')\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError('')\n      announceToScreenReader('Certificazione in corso...')\n\n      // Se il cavo non è collegato, prova a collegarlo automaticamente\n      const isCollegato = isCavoCollegato()\n      if (!isCollegato) {\n        announceToScreenReader('Collegamento automatico del cavo...')\n        const collegamentoRiuscito = await autoCollegaCavo()\n        if (!collegamentoRiuscito) {\n          setError('Impossibile collegare automaticamente il cavo. Collegalo manualmente prima di certificare.')\n          announceToScreenReader('Errore: Impossibile collegare automaticamente il cavo')\n          return\n        }\n      }\n\n      const certificazioneData = {\n        id_cavo: cavo.id_cavo,\n        responsabile_certificazione: formData.responsabile_certificazione,\n        data_certificazione: formData.data_certificazione,\n        esito_certificazione: formData.esito_certificazione,\n        note_certificazione: formData.note_certificazione || null\n      }\n\n      await certificazioniApi.createCertificazione(cantiere.id_cantiere, certificazioneData)\n\n      const message = `Certificazione completata per il cavo ${cavo.id_cavo}`\n      announceToScreenReader(message)\n      toast({\n        title: \"Successo\",\n        description: message,\n      })\n      \n      if (onSuccess) onSuccess(message)\n      onClose()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la certificazione'\n      setError(errorMessage)\n      announceToScreenReader(`Errore: ${errorMessage}`)\n      if (onError) onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleGeneraPDF = async () => {\n    if (!cavo || !cantiere) return\n\n    try {\n      setLoading(true)\n      setError('')\n      announceToScreenReader('Generazione PDF in corso...')\n\n      const response = await certificazioniApi.generatePDF(cantiere.id_cantiere, cavo.id_cavo)\n      \n      // Crea un link per il download\n      const url = window.URL.createObjectURL(new Blob([response.data]))\n      const link = document.createElement('a')\n      link.href = url\n      link.setAttribute('download', `certificato_${cavo.id_cavo}.pdf`)\n      document.body.appendChild(link)\n      link.click()\n      link.remove()\n      window.URL.revokeObjectURL(url)\n\n      const message = `PDF certificato generato per il cavo ${cavo.id_cavo}`\n      announceToScreenReader(message)\n      toast({\n        title: \"Successo\",\n        description: message,\n      })\n      \n      if (onSuccess) onSuccess(message)\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la generazione del PDF'\n      setError(errorMessage)\n      announceToScreenReader(`Errore: ${errorMessage}`)\n      if (onError) onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (!cavo) return null\n\n  // Controlli di validazione allineati con webapp originale CEI 64-8\n  const isInstalled = cavo.stato_installazione === 'Installato' ||\n                     cavo.stato_installazione === 'INSTALLATO' ||\n                     cavo.stato_installazione === 'POSATO' ||\n                     (cavo.metri_posati || cavo.metratura_reale || 0) > 0\n\n  const isCollegato = isCavoCollegato()\n  const hasResponsabili = cavo.responsabile_partenza && cavo.responsabile_arrivo\n  const isCompletelyConnected = isCollegato && hasResponsabili\n  const isCertificato = isCavoCertificato()\n\n  // Per la certificazione CEI 64-8 basta che sia posato (il collegamento può essere gestito durante la certificazione)\n  const puoEssereCertificato = isInstalled\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      {/* Screen reader announcements */}\n      <div \n        aria-live=\"polite\" \n        aria-atomic=\"true\" \n        className=\"sr-only\"\n      >\n        {screenReaderAnnouncement}\n      </div>\n      \n      <DialogContent \n        className=\"sm:max-w-[700px] max-h-[90vh] overflow-y-auto\"\n        aria-describedby=\"certificazione-description\"\n      >\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2 text-blue-600\">\n            <Award className=\"h-5 w-5\" />\n            Gestione Certificazione - {cavo.id_cavo}\n          </DialogTitle>\n          <DialogDescription id=\"certificazione-description\">\n            Certifica il cavo {cavo.id_cavo} secondo normativa CEI 64-8 o genera il PDF del certificato\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-6\">\n          {/* Informazioni cavo */}\n          <div className=\"p-4 bg-blue-50 rounded-lg border-2 border-blue-200\">\n            <div className=\"text-sm font-medium text-blue-800\">\n              Informazioni Cavo / Tipologia: {cavo.tipologia || 'N/A'} / Da: {cavo.ubicazione_partenza || 'N/A'} / Formazione: {cavo.sezione || 'N/A'} / A: {cavo.ubicazione_arrivo || 'N/A'} / Metri Posati: {cavo.metratura_reale || 0} m\n            </div>\n          </div>\n\n          {/* Stato attuale con icone migliorate */}\n          <div className=\"p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border\">\n            <Label className=\"text-sm font-medium text-gray-700\">Stato Cavo</Label>\n            <div className=\"mt-2 space-y-2\">\n              <div className=\"flex items-center gap-2\">\n                {isInstalled ? (\n                  <CheckCircle className=\"w-4 h-4 text-green-600\" />\n                ) : (\n                  <AlertCircle className=\"w-4 h-4 text-red-500\" />\n                )}\n                <span className=\"text-sm font-medium\">\n                  {isInstalled ? 'Installato/Posato' : 'Non installato'}\n                </span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                {isCollegato ? (\n                  <CheckCircle className=\"w-4 h-4 text-green-600\" />\n                ) : (\n                  <AlertCircle className=\"w-4 h-4 text-orange-500\" />\n                )}\n                <span className=\"text-sm font-medium\">\n                  {isCollegato ? 'Collegato' : 'Non collegato'}\n                </span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                {isCertificato ? (\n                  <CheckCircle className=\"w-4 h-4 text-green-600\" />\n                ) : (\n                  <AlertCircle className=\"w-4 h-4 text-gray-400\" />\n                )}\n                <span className=\"text-sm font-medium\">\n                  {isCertificato ? 'Certificato' : 'Non certificato'}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          {/* Alert di validazione */}\n          {!puoEssereCertificato && (\n            <Alert className=\"border-red-200 bg-red-50\">\n              <AlertCircle className=\"h-4 w-4 text-red-600\" />\n              <AlertDescription className=\"text-red-800\">\n                <strong>ATTENZIONE:</strong> Il cavo deve essere installato/posato prima di poter essere certificato secondo CEI 64-8.\n              </AlertDescription>\n            </Alert>\n          )}\n\n          {puoEssereCertificato && !isCompletelyConnected && (\n            <Alert className=\"border-amber-200 bg-amber-50\">\n              <AlertCircle className=\"h-4 w-4 text-amber-600\" />\n              <AlertDescription className=\"text-amber-800\">\n                <strong>ATTENZIONE:</strong> Il cavo non risulta completamente collegato.\n                Durante la certificazione sarà possibile collegarlo automaticamente a \"cantiere\" su entrambi i lati.\n              </AlertDescription>\n            </Alert>\n          )}\n\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          {isCertificato ? (\n            // Cavo già certificato - mostra opzione per generare PDF\n            <div className=\"space-y-4\">\n              <Alert className=\"border-green-200 bg-green-50\">\n                <Award className=\"h-4 w-4 text-green-600\" />\n                <AlertDescription className=\"text-green-800\">\n                  <strong>CERTIFICATO:</strong> Questo cavo è già stato certificato. Puoi generare il PDF del certificato.\n                </AlertDescription>\n              </Alert>\n\n              <Button\n                onClick={handleGeneraPDF}\n                disabled={loading}\n                className=\"w-full bg-green-600 hover:bg-green-700 text-white\"\n                size=\"lg\"\n              >\n                {loading ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <Download className=\"h-4 w-4 mr-2\" />}\n                Genera PDF Certificato\n              </Button>\n            </div>\n          ) : (\n            // Cavo non certificato - mostra form per certificazione (CEI 64-8: basta che sia posato)\n            puoEssereCertificato && (\n              <div className=\"space-y-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border-2 border-blue-200\">\n                <div className=\"text-sm font-medium text-blue-800 mb-4\">\n                  📋 Dati Certificazione CEI 64-8\n                </div>\n\n                {/* Responsabile */}\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"responsabile\" className=\"text-sm font-medium text-gray-700\">\n                    Responsabile Certificazione *\n                  </Label>\n                  <Select\n                    value={formData.responsabile_certificazione}\n                    onValueChange={(value) => setFormData(prev => ({ ...prev, responsabile_certificazione: value }))}\n                    disabled={loadingResponsabili}\n                  >\n                    <SelectTrigger className=\"border-2 border-gray-300 focus:border-blue-500\">\n                      <SelectValue placeholder=\"Seleziona responsabile...\" />\n                    </SelectTrigger>\n                    <SelectContent>\n                      {responsabili.map((resp) => (\n                        <SelectItem key={resp.id} value={resp.nome_responsabile}>\n                          {resp.nome_responsabile}\n                          {resp.numero_telefono && ` - ${resp.numero_telefono}`}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                </div>\n\n                {/* Data certificazione */}\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"data\" className=\"text-sm font-medium text-gray-700\">\n                    Data Certificazione\n                  </Label>\n                  <Input\n                    id=\"data\"\n                    type=\"date\"\n                    value={formData.data_certificazione}\n                    onChange={(e) => setFormData(prev => ({ ...prev, data_certificazione: e.target.value }))}\n                    className=\"border-2 border-gray-300 focus:border-blue-500\"\n                  />\n                </div>\n\n                {/* Esito */}\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"esito\" className=\"text-sm font-medium text-gray-700\">\n                    Esito Certificazione\n                  </Label>\n                  <Select\n                    value={formData.esito_certificazione}\n                    onValueChange={(value) => setFormData(prev => ({ ...prev, esito_certificazione: value }))}\n                  >\n                    <SelectTrigger className=\"border-2 border-gray-300 focus:border-blue-500\">\n                      <SelectValue />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"CONFORME\">✅ CONFORME</SelectItem>\n                      <SelectItem value=\"NON_CONFORME\">❌ NON CONFORME</SelectItem>\n                      <SelectItem value=\"CONFORME_CON_RISERVA\">⚠️ CONFORME CON RISERVA</SelectItem>\n                    </SelectContent>\n                  </Select>\n                </div>\n\n                {/* Note */}\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"note\" className=\"text-sm font-medium text-gray-700\">\n                    Note (opzionale)\n                  </Label>\n                  <Textarea\n                    id=\"note\"\n                    placeholder=\"Inserisci eventuali note sulla certificazione...\"\n                    value={formData.note_certificazione}\n                    onChange={(e) => setFormData(prev => ({ ...prev, note_certificazione: e.target.value }))}\n                    rows={3}\n                    className=\"border-2 border-gray-300 focus:border-blue-500\"\n                  />\n                </div>\n\n                <Button\n                  onClick={handleCertifica}\n                  disabled={loading || !formData.responsabile_certificazione}\n                  className=\"w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3\"\n                  size=\"lg\"\n                >\n                  {loading ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <Award className=\"h-4 w-4 mr-2\" />}\n                  Certifica Cavo CEI 64-8\n                </Button>\n              </div>\n            )\n          )}\n        </div>\n\n        <DialogFooter className=\"border-t pt-4\">\n          <Button\n            variant=\"outline\"\n            onClick={onClose}\n            disabled={loading}\n            className=\"flex items-center gap-2\"\n          >\n            <X className=\"h-4 w-4\" />\n            Chiudi\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AAOA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AA3BA;;;;;;;;;;;;;;AA4Ce,SAAS,qBAAqB,EAC3C,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,SAAS,EACT,OAAO,EACmB;IAC1B,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,6BAA6B;QAC7B,qBAAqB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC3D,sBAAsB;QACtB,qBAAqB;IACvB;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzE,qCAAqC;IACrC,MAAM,yBAAyB,CAAC;QAC9B,4BAA4B;QAC5B,WAAW,IAAM,4BAA4B,KAAK;IACpD;IAEA,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,MAAM;YAChB,YAAY;gBACV,6BAA6B;gBAC7B,qBAAqB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC3D,sBAAsB;gBACtB,qBAAqB;YACvB;YACA,SAAS;YACT;QACF;IACF,GAAG;QAAC;QAAM;KAAK;IAEf,MAAM,mBAAmB;QACvB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,uBAAuB;YACvB,MAAM,WAAW,MAAM,iHAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,SAAS,WAAW;YAC3E,gBAAgB,SAAS,IAAI;QAC/B,EAAE,OAAO,OAAO;YACd,gBAAgB,EAAE;QACpB,SAAU;YACR,uBAAuB;QACzB;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM,OAAO;QAClB,MAAM,eAAe,KAAK,YAAY,IAAI,KAAK,YAAY,IAAI;QAC/D,OAAO,iBAAiB,EAAE,0BAA0B;;IACtD;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,KAAK,WAAW,KAAK,QAAQ,KAAK,WAAW,KAAK,QAAQ,KAAK,WAAW,KAAK;IACxF;IAEA,2EAA2E;IAC3E,MAAM,kBAAkB;QACtB,IAAI,CAAC,QAAQ,CAAC,UAAU,OAAO;QAE/B,IAAI;YACF,uBAAuB;YAEvB,uCAAuC;YACvC,MAAM,iHAAA,CAAA,UAAO,CAAC,WAAW,CACvB,SAAS,WAAW,EACpB,KAAK,OAAO,EACZ,YACA;YAGF,uBAAuB;YACvB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,OAAO;QACT;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,QAAQ,CAAC,UAAU;QAExB,IAAI,CAAC,SAAS,2BAA2B,EAAE;YACzC,SAAS;YACT,uBAAuB;YACvB;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YACT,uBAAuB;YAEvB,iEAAiE;YACjE,MAAM,cAAc;YACpB,IAAI,CAAC,aAAa;gBAChB,uBAAuB;gBACvB,MAAM,uBAAuB,MAAM;gBACnC,IAAI,CAAC,sBAAsB;oBACzB,SAAS;oBACT,uBAAuB;oBACvB;gBACF;YACF;YAEA,MAAM,qBAAqB;gBACzB,SAAS,KAAK,OAAO;gBACrB,6BAA6B,SAAS,2BAA2B;gBACjE,qBAAqB,SAAS,mBAAmB;gBACjD,sBAAsB,SAAS,oBAAoB;gBACnD,qBAAqB,SAAS,mBAAmB,IAAI;YACvD;YAEA,MAAM,iHAAA,CAAA,oBAAiB,CAAC,oBAAoB,CAAC,SAAS,WAAW,EAAE;YAEnE,MAAM,UAAU,CAAC,sCAAsC,EAAE,KAAK,OAAO,EAAE;YACvE,uBAAuB;YACvB,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA,IAAI,WAAW,UAAU;YACzB;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,SAAS;YACT,uBAAuB,CAAC,QAAQ,EAAE,cAAc;YAChD,IAAI,SAAS,QAAQ;QACvB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,QAAQ,CAAC,UAAU;QAExB,IAAI;YACF,WAAW;YACX,SAAS;YACT,uBAAuB;YAEvB,MAAM,WAAW,MAAM,iHAAA,CAAA,oBAAiB,CAAC,WAAW,CAAC,SAAS,WAAW,EAAE,KAAK,OAAO;YAEvF,+BAA+B;YAC/B,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC,IAAI,KAAK;gBAAC,SAAS,IAAI;aAAC;YAC/D,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,YAAY,CAAC,YAAY,CAAC,YAAY,EAAE,KAAK,OAAO,CAAC,IAAI,CAAC;YAC/D,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,KAAK,MAAM;YACX,OAAO,GAAG,CAAC,eAAe,CAAC;YAE3B,MAAM,UAAU,CAAC,qCAAqC,EAAE,KAAK,OAAO,EAAE;YACtE,uBAAuB;YACvB,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA,IAAI,WAAW,UAAU;QAC3B,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,SAAS;YACT,uBAAuB,CAAC,QAAQ,EAAE,cAAc;YAChD,IAAI,SAAS,QAAQ;QACvB,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,mEAAmE;IACnE,MAAM,cAAc,KAAK,mBAAmB,KAAK,gBAC9B,KAAK,mBAAmB,KAAK,gBAC7B,KAAK,mBAAmB,KAAK,YAC7B,CAAC,KAAK,YAAY,IAAI,KAAK,eAAe,IAAI,CAAC,IAAI;IAEtE,MAAM,cAAc;IACpB,MAAM,kBAAkB,KAAK,qBAAqB,IAAI,KAAK,mBAAmB;IAC9E,MAAM,wBAAwB,eAAe;IAC7C,MAAM,gBAAgB;IAEtB,qHAAqH;IACrH,MAAM,uBAAuB;IAE7B,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;;0BAEhC,8OAAC;gBACC,aAAU;gBACV,eAAY;gBACZ,WAAU;0BAET;;;;;;0BAGH,8OAAC,kIAAA,CAAA,gBAAa;gBACZ,WAAU;gBACV,oBAAiB;;kCAEjB,8OAAC,kIAAA,CAAA,eAAY;;0CACX,8OAAC,kIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAY;oCACF,KAAK,OAAO;;;;;;;0CAEzC,8OAAC,kIAAA,CAAA,oBAAiB;gCAAC,IAAG;;oCAA6B;oCAC9B,KAAK,OAAO;oCAAC;;;;;;;;;;;;;kCAIpC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;wCAAoC;wCACjB,KAAK,SAAS,IAAI;wCAAM;wCAAQ,KAAK,mBAAmB,IAAI;wCAAM;wCAAgB,KAAK,OAAO,IAAI;wCAAM;wCAAO,KAAK,iBAAiB,IAAI;wCAAM;wCAAkB,KAAK,eAAe,IAAI;wCAAE;;;;;;;;;;;;0CAK/N,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,WAAU;kDAAoC;;;;;;kDACrD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;oDACZ,4BACC,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;6EAEvB,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEAEzB,8OAAC;wDAAK,WAAU;kEACb,cAAc,sBAAsB;;;;;;;;;;;;0DAGzC,8OAAC;gDAAI,WAAU;;oDACZ,4BACC,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;6EAEvB,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEAEzB,8OAAC;wDAAK,WAAU;kEACb,cAAc,cAAc;;;;;;;;;;;;0DAGjC,8OAAC;gDAAI,WAAU;;oDACZ,8BACC,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;6EAEvB,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEAEzB,8OAAC;wDAAK,WAAU;kEACb,gBAAgB,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;4BAOxC,CAAC,sCACA,8OAAC,iIAAA,CAAA,QAAK;gCAAC,WAAU;;kDACf,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC,iIAAA,CAAA,mBAAgB;wCAAC,WAAU;;0DAC1B,8OAAC;0DAAO;;;;;;4CAAoB;;;;;;;;;;;;;4BAKjC,wBAAwB,CAAC,uCACxB,8OAAC,iIAAA,CAAA,QAAK;gCAAC,WAAU;;kDACf,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC,iIAAA,CAAA,mBAAgB;wCAAC,WAAU;;0DAC1B,8OAAC;0DAAO;;;;;;4CAAoB;;;;;;;;;;;;;4BAMjC,uBACC,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;;kDACb,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC,iIAAA,CAAA,mBAAgB;kDAAE;;;;;;;;;;;;4BAItB,gBACC,yDAAyD;0CACzD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,WAAU;;0DACf,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC,iIAAA,CAAA,mBAAgB;gDAAC,WAAU;;kEAC1B,8OAAC;kEAAO;;;;;;oDAAqB;;;;;;;;;;;;;kDAIjC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU;wCACV,WAAU;wCACV,MAAK;;4CAEJ,wBAAU,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAAiC,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAkB;;;;;;;;;;;;uCAKzG,yFAAyF;4BACzF,sCACE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAyC;;;;;;kDAKxD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAe,WAAU;0DAAoC;;;;;;0DAG5E,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,SAAS,2BAA2B;gDAC3C,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,6BAA6B;wDAAM,CAAC;gDAC9F,UAAU;;kEAEV,8OAAC,kIAAA,CAAA,gBAAa;wDAAC,WAAU;kEACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,8OAAC,kIAAA,CAAA,gBAAa;kEACX,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,kIAAA,CAAA,aAAU;gEAAe,OAAO,KAAK,iBAAiB;;oEACpD,KAAK,iBAAiB;oEACtB,KAAK,eAAe,IAAI,CAAC,GAAG,EAAE,KAAK,eAAe,EAAE;;+DAFtC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;kDAUhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAO,WAAU;0DAAoC;;;;;;0DAGpE,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,mBAAmB;gDACnC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,qBAAqB,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDACtF,WAAU;;;;;;;;;;;;kDAKd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAQ,WAAU;0DAAoC;;;;;;0DAGrE,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,SAAS,oBAAoB;gDACpC,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,sBAAsB;wDAAM,CAAC;;kEAEvF,8OAAC,kIAAA,CAAA,gBAAa;wDAAC,WAAU;kEACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAW;;;;;;0EAC7B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAe;;;;;;0EACjC,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAuB;;;;;;;;;;;;;;;;;;;;;;;;kDAM/C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAO,WAAU;0DAAoC;;;;;;0DAGpE,8OAAC,oIAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,aAAY;gDACZ,OAAO,SAAS,mBAAmB;gDACnC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,qBAAqB,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDACtF,MAAM;gDACN,WAAU;;;;;;;;;;;;kDAId,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,WAAW,CAAC,SAAS,2BAA2B;wCAC1D,WAAU;wCACV,MAAK;;4CAEJ,wBAAU,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAAiC,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAkB;;;;;;;;;;;;;;;;;;;kCAQ5G,8OAAC,kIAAA,CAAA,eAAY;wBAAC,WAAU;kCACtB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;4BACV,WAAU;;8CAEV,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;;;;;;;;;;;AAOrC", "debugId": null}}, {"offset": {"line": 6232, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/cavi/CreaComandaDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, ClipboardList, Users } from 'lucide-react'\nimport { comandeApi, responsabiliApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface CreaComandaDialogProps {\n  open: boolean\n  onClose: () => void\n  caviSelezionati: string[]\n  tipoComanda?: 'POSA' | 'COLLEGAMENTO_PARTENZA' | 'COLLEGAMENTO_ARRIVO' | 'CERTIFICAZIONE'\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\ninterface Responsabile {\n  id: number\n  nome_responsabile: string\n  numero_telefono?: string\n  mail?: string\n}\n\nexport default function CreaComandaDialog({\n  open,\n  onClose,\n  caviSelezionati,\n  tipoComanda,\n  onSuccess,\n  onError\n}: CreaComandaDialogProps) {\n  const { cantiere } = useAuth()\n  const [formData, setFormData] = useState({\n    tipo_comanda: tipoComanda || 'POSA',\n    responsabile: '',\n    note: ''\n  })\n  const [responsabili, setResponsabili] = useState<Responsabile[]>([])\n  const [loading, setLoading] = useState(false)\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false)\n  const [error, setError] = useState('')\n\n  // Reset form quando si apre il dialog\n  useEffect(() => {\n    if (open) {\n      setFormData({\n        tipo_comanda: tipoComanda || 'POSA',\n        responsabile: '',\n        note: ''\n      })\n      setError('')\n      loadResponsabili()\n    }\n  }, [open, tipoComanda])\n\n  const loadResponsabili = async () => {\n    if (!cantiere) return\n\n    try {\n      setLoadingResponsabili(true)\n      const response = await responsabiliApi.getResponsabili(cantiere.id_cantiere)\n      setResponsabili(response.data)\n    } catch (error) {\n      setResponsabili([])\n    } finally {\n      setLoadingResponsabili(false)\n    }\n  }\n\n  const getTipoComandaLabel = (tipo: string) => {\n    switch (tipo) {\n      case 'POSA':\n        return 'Posa Cavi'\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'Collegamento Partenza'\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'Collegamento Arrivo'\n      case 'CERTIFICAZIONE':\n        return 'Certificazione'\n      default:\n        return tipo\n    }\n  }\n\n  const handleCreaComanda = async () => {\n    if (!cantiere) return\n\n    if (!formData.responsabile) {\n      setError('Seleziona un responsabile per la comanda')\n      return\n    }\n\n    if (caviSelezionati.length === 0) {\n      setError('Seleziona almeno un cavo per la comanda')\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError('')\n\n      // Crea la comanda con i cavi assegnati\n      const comandaData = {\n        tipo_comanda: formData.tipo_comanda,\n        responsabile: formData.responsabile,\n        note: formData.note || null\n      }\n\n      const response = await comandeApi.createComandaWithCavi(\n        cantiere.id_cantiere,\n        comandaData,\n        caviSelezionati\n      )\n\n      onSuccess(`Comanda ${response.data.codice_comanda} creata con successo per ${caviSelezionati.length} cavi`)\n      onClose()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la creazione della comanda'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <DialogContent className=\"sm:max-w-[600px]\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <ClipboardList className=\"h-5 w-5\" />\n            Crea Nuova Comanda\n          </DialogTitle>\n          <DialogDescription>\n            Crea una nuova comanda per {caviSelezionati.length} cavi selezionati\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          {/* Cavi selezionati */}\n          <div className=\"p-4 bg-gray-50 rounded-lg\">\n            <Label className=\"text-sm font-medium\">Cavi Selezionati ({caviSelezionati.length})</Label>\n            <div className=\"mt-2 max-h-32 overflow-y-auto\">\n              <div className=\"flex flex-wrap gap-1\">\n                {caviSelezionati.slice(0, 10).map((cavoId) => (\n                  <span\n                    key={cavoId}\n                    className=\"inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded\"\n                  >\n                    {cavoId}\n                  </span>\n                ))}\n                {caviSelezionati.length > 10 && (\n                  <span className=\"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded\">\n                    +{caviSelezionati.length - 10} altri...\n                  </span>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          {/* Tipo comanda */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"tipo\">Tipo Comanda *</Label>\n            <Select\n              value={formData.tipo_comanda}\n              onValueChange={(value) => setFormData(prev => ({ ...prev, tipo_comanda: value }))}\n            >\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"POSA\">🔧 Posa Cavi</SelectItem>\n                <SelectItem value=\"COLLEGAMENTO_PARTENZA\">🔌 Collegamento Partenza</SelectItem>\n                <SelectItem value=\"COLLEGAMENTO_ARRIVO\">⚡ Collegamento Arrivo</SelectItem>\n                <SelectItem value=\"CERTIFICAZIONE\">📋 Certificazione</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Responsabile */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"responsabile\">Responsabile *</Label>\n            <Select\n              value={formData.responsabile}\n              onValueChange={(value) => setFormData(prev => ({ ...prev, responsabile: value }))}\n              disabled={loadingResponsabili}\n            >\n              <SelectTrigger>\n                <SelectValue placeholder=\"Seleziona responsabile...\" />\n              </SelectTrigger>\n              <SelectContent>\n                {responsabili.map((resp) => (\n                  <SelectItem key={resp.id} value={resp.nome_responsabile}>\n                    <div className=\"flex items-center gap-2\">\n                      <Users className=\"h-4 w-4\" />\n                      <span>{resp.nome_responsabile}</span>\n                      {resp.numero_telefono && (\n                        <span className=\"text-xs text-gray-500\">- {resp.numero_telefono}</span>\n                      )}\n                    </div>\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Note */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"note\">Note (opzionale)</Label>\n            <Textarea\n              id=\"note\"\n              placeholder=\"Inserisci eventuali note per la comanda...\"\n              value={formData.note}\n              onChange={(e) => setFormData(prev => ({ ...prev, note: e.target.value }))}\n              rows={3}\n            />\n          </div>\n\n          {/* Riepilogo */}\n          <div className=\"p-4 bg-blue-50 rounded-lg\">\n            <Label className=\"text-sm font-medium\">Riepilogo Comanda</Label>\n            <div className=\"mt-2 space-y-1 text-sm\">\n              <div><strong>Tipo:</strong> {getTipoComandaLabel(formData.tipo_comanda)}</div>\n              <div><strong>Responsabile:</strong> {formData.responsabile || 'Non selezionato'}</div>\n              <div><strong>Cavi:</strong> {caviSelezionati.length} selezionati</div>\n              {formData.note && <div><strong>Note:</strong> {formData.note}</div>}\n            </div>\n          </div>\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={onClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button\n            onClick={handleCreaComanda}\n            disabled={loading || !formData.responsabile || caviSelezionati.length === 0}\n          >\n            {loading ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <ClipboardList className=\"h-4 w-4 mr-2\" />}\n            Crea Comanda\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AAEA;AACA;AAOA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAzBA;;;;;;;;;;;;AA2Ce,SAAS,kBAAkB,EACxC,IAAI,EACJ,OAAO,EACP,eAAe,EACf,WAAW,EACX,SAAS,EACT,OAAO,EACgB;IACvB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,cAAc,eAAe;QAC7B,cAAc;QACd,MAAM;IACR;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,YAAY;gBACV,cAAc,eAAe;gBAC7B,cAAc;gBACd,MAAM;YACR;YACA,SAAS;YACT;QACF;IACF,GAAG;QAAC;QAAM;KAAY;IAEtB,MAAM,mBAAmB;QACvB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,uBAAuB;YACvB,MAAM,WAAW,MAAM,iHAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,SAAS,WAAW;YAC3E,gBAAgB,SAAS,IAAI;QAC/B,EAAE,OAAO,OAAO;YACd,gBAAgB,EAAE;QACpB,SAAU;YACR,uBAAuB;QACzB;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,UAAU;QAEf,IAAI,CAAC,SAAS,YAAY,EAAE;YAC1B,SAAS;YACT;QACF;QAEA,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,SAAS;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,uCAAuC;YACvC,MAAM,cAAc;gBAClB,cAAc,SAAS,YAAY;gBACnC,cAAc,SAAS,YAAY;gBACnC,MAAM,SAAS,IAAI,IAAI;YACzB;YAEA,MAAM,WAAW,MAAM,iHAAA,CAAA,aAAU,CAAC,qBAAqB,CACrD,SAAS,WAAW,EACpB,aACA;YAGF,UAAU,CAAC,QAAQ,EAAE,SAAS,IAAI,CAAC,cAAc,CAAC,yBAAyB,EAAE,gBAAgB,MAAM,CAAC,KAAK,CAAC;YAC1G;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGvC,8OAAC,kIAAA,CAAA,oBAAiB;;gCAAC;gCACW,gBAAgB,MAAM;gCAAC;;;;;;;;;;;;;8BAIvD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;;wCAAsB;wCAAmB,gBAAgB,MAAM;wCAAC;;;;;;;8CACjF,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;4CACZ,gBAAgB,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,uBACjC,8OAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;4CAMR,gBAAgB,MAAM,GAAG,oBACxB,8OAAC;gDAAK,WAAU;;oDAAmE;oDAC/E,gBAAgB,MAAM,GAAG;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;wBAOvC,uBACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;sCAKvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAO;;;;;;8CACtB,8OAAC,kIAAA,CAAA,SAAM;oCACL,OAAO,SAAS,YAAY;oCAC5B,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,cAAc;4CAAM,CAAC;;sDAE/E,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;sDAEd,8OAAC,kIAAA,CAAA,gBAAa;;8DACZ,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAO;;;;;;8DACzB,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAwB;;;;;;8DAC1C,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAsB;;;;;;8DACxC,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAiB;;;;;;;;;;;;;;;;;;;;;;;;sCAMzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAe;;;;;;8CAC9B,8OAAC,kIAAA,CAAA,SAAM;oCACL,OAAO,SAAS,YAAY;oCAC5B,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,cAAc;4CAAM,CAAC;oCAC/E,UAAU;;sDAEV,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,8OAAC,kIAAA,CAAA,gBAAa;sDACX,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,kIAAA,CAAA,aAAU;oDAAe,OAAO,KAAK,iBAAiB;8DACrD,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;0EAAM,KAAK,iBAAiB;;;;;;4DAC5B,KAAK,eAAe,kBACnB,8OAAC;gEAAK,WAAU;;oEAAwB;oEAAG,KAAK,eAAe;;;;;;;;;;;;;mDALpD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;sCAehC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAO;;;;;;8CACtB,8OAAC,oIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,aAAY;oCACZ,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACvE,MAAM;;;;;;;;;;;;sCAKV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAc;gDAAE,oBAAoB,SAAS,YAAY;;;;;;;sDACtE,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAsB;gDAAE,SAAS,YAAY,IAAI;;;;;;;sDAC9D,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAc;gDAAE,gBAAgB,MAAM;gDAAC;;;;;;;wCACnD,SAAS,IAAI,kBAAI,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAc;gDAAE,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;8BAKlE,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAS,UAAU;sCAAS;;;;;;sCAG/D,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,WAAW,CAAC,SAAS,YAAY,IAAI,gBAAgB,MAAM,KAAK;;gCAEzE,wBAAU,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAAiC,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAkB;;;;;;;;;;;;;;;;;;;;;;;;AAOtH", "debugId": null}}, {"offset": {"line": 6831, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/cavi/ImportExcelDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef } from 'react'\nimport {\n  <PERSON><PERSON>,\n  <PERSON>alogContent,\n  DialogDescription,\n  DialogFooter,\n  <PERSON>alogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Upload, FileSpreadsheet, CheckCircle } from 'lucide-react'\nimport { excelApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface ImportExcelDialogProps {\n  open: boolean\n  onClose: () => void\n  tipo: 'cavi' | 'bobine'\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\nexport default function ImportExcelDialog({\n  open,\n  onClose,\n  tipo,\n  onSuccess,\n  onError\n}: ImportExcelDialogProps) {\n  const { cantiere } = useAuth()\n  const [file, setFile] = useState<File | null>(null)\n  const [revisione, setRevisione] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [uploadProgress, setUploadProgress] = useState(0)\n  const fileInputRef = useRef<HTMLInputElement>(null)\n\n  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const selectedFile = event.target.files?.[0]\n    if (selectedFile) {\n      // Verifica che sia un file Excel\n      const validTypes = [\n        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n        'application/vnd.ms-excel'\n      ]\n      \n      if (!validTypes.includes(selectedFile.type) && \n          !selectedFile.name.toLowerCase().endsWith('.xlsx') && \n          !selectedFile.name.toLowerCase().endsWith('.xls')) {\n        setError('Seleziona un file Excel valido (.xlsx o .xls)')\n        return\n      }\n\n      setFile(selectedFile)\n      setError('')\n    }\n  }\n\n  const handleImport = async () => {\n    if (!file || !cantiere) return\n\n    if (tipo === 'cavi' && !revisione.trim()) {\n      setError('Inserisci il codice revisione per l\\'importazione cavi')\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError('')\n      setUploadProgress(0)\n\n      let response\n      if (tipo === 'cavi') {\n        response = await excelApi.importCavi(cantiere.id_cantiere, file, revisione.trim())\n      } else {\n        response = await excelApi.importBobine(cantiere.id_cantiere, file)\n      }\n\n      setUploadProgress(100)\n\n      if (response.data.success) {\n        const details = response.data.details\n        let message = response.data.message\n        \n        if (tipo === 'cavi' && details?.cavi_importati) {\n          message += ` (${details.cavi_importati} cavi importati)`\n        } else if (tipo === 'bobine' && details?.bobine_importate) {\n          message += ` (${details.bobine_importate} bobine importate)`\n        }\n\n        onSuccess(message)\n        onClose()\n      } else {\n        onError(response.data.message || 'Errore durante l\\'importazione')\n      }\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\\'importazione del file'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n      setUploadProgress(0)\n    }\n  }\n\n  const handleClose = () => {\n    if (!loading) {\n      setFile(null)\n      setRevisione('')\n      setError('')\n      setUploadProgress(0)\n      if (fileInputRef.current) {\n        fileInputRef.current.value = ''\n      }\n      onClose()\n    }\n  }\n\n  const getTipoLabel = () => {\n    return tipo === 'cavi' ? 'Cavi' : 'Bobine'\n  }\n\n  const getFileRequirements = () => {\n    if (tipo === 'cavi') {\n      return [\n        'File Excel (.xlsx o .xls)',\n        'Colonne richieste: ID_CAVO, SISTEMA, UTILITY, TIPOLOGIA, ecc.',\n        'Prima riga deve contenere le intestazioni',\n        'Codice revisione obbligatorio per tracciabilità'\n      ]\n    } else {\n      return [\n        'File Excel (.xlsx o .xls)',\n        'Colonne richieste: NUMERO_BOBINA, UTILITY, TIPOLOGIA, METRI_TOTALI, ecc.',\n        'Prima riga deve contenere le intestazioni',\n        'I metri residui saranno impostati uguali ai metri totali'\n      ]\n    }\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent className=\"sm:max-w-[600px]\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Upload className=\"h-5 w-5\" />\n            Importa {getTipoLabel()} da Excel\n          </DialogTitle>\n          <DialogDescription>\n            Carica un file Excel per importare {getTipoLabel().toLowerCase()} nel cantiere\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          {/* Requisiti file */}\n          <div className=\"p-4 bg-blue-50 rounded-lg\">\n            <Label className=\"text-sm font-medium\">Requisiti File</Label>\n            <ul className=\"mt-2 space-y-1 text-sm text-gray-600\">\n              {getFileRequirements().map((req, index) => (\n                <li key={index} className=\"flex items-start gap-2\">\n                  <span className=\"text-blue-500 mt-0.5\">•</span>\n                  <span>{req}</span>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          {/* Selezione file */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"file\">File Excel *</Label>\n            <div className=\"flex items-center gap-2\">\n              <Input\n                ref={fileInputRef}\n                id=\"file\"\n                type=\"file\"\n                accept=\".xlsx,.xls\"\n                onChange={handleFileSelect}\n                disabled={loading}\n                className=\"flex-1\"\n              />\n              {file && (\n                <div className=\"flex items-center gap-1 text-green-600\">\n                  <CheckCircle className=\"h-4 w-4\" />\n                  <span className=\"text-sm\">File selezionato</span>\n                </div>\n              )}\n            </div>\n            {file && (\n              <div className=\"text-sm text-gray-600\">\n                <FileSpreadsheet className=\"h-4 w-4 inline mr-1\" />\n                {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)\n              </div>\n            )}\n          </div>\n\n          {/* Revisione (solo per cavi) */}\n          {tipo === 'cavi' && (\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"revisione\">Codice Revisione *</Label>\n              <Input\n                id=\"revisione\"\n                value={revisione}\n                onChange={(e) => setRevisione(e.target.value)}\n                placeholder=\"es. REV001, V1.0, 2024-01\"\n                disabled={loading}\n              />\n              <p className=\"text-sm text-gray-500\">\n                Codice identificativo della revisione per tracciabilità delle modifiche\n              </p>\n            </div>\n          )}\n\n          {/* Progress bar */}\n          {loading && (\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center gap-2\">\n                <Loader2 className=\"h-4 w-4 animate-spin\" />\n                <span className=\"text-sm\">Caricamento in corso...</span>\n              </div>\n              <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                <div \n                  className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                  style={{ width: `${uploadProgress}%` }}\n                ></div>\n              </div>\n            </div>\n          )}\n\n          {/* Riepilogo */}\n          {file && (\n            <div className=\"p-4 bg-gray-50 rounded-lg\">\n              <Label className=\"text-sm font-medium\">Riepilogo Importazione</Label>\n              <div className=\"mt-2 space-y-1 text-sm\">\n                <div><strong>Tipo:</strong> {getTipoLabel()}</div>\n                <div><strong>File:</strong> {file.name}</div>\n                <div><strong>Dimensione:</strong> {(file.size / 1024 / 1024).toFixed(2)} MB</div>\n                {tipo === 'cavi' && revisione && (\n                  <div><strong>Revisione:</strong> {revisione}</div>\n                )}\n                <div><strong>Cantiere:</strong> {cantiere?.nome_cantiere}</div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={handleClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button\n            onClick={handleImport}\n            disabled={loading || !file || (tipo === 'cavi' && !revisione.trim())}\n          >\n            {loading ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <Upload className=\"h-4 w-4 mr-2\" />}\n            Importa {getTipoLabel()}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAjBA;;;;;;;;;;;AA2Be,SAAS,kBAAkB,EACxC,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,SAAS,EACT,OAAO,EACgB;IACvB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,mBAAmB,CAAC;QACxB,MAAM,eAAe,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAC5C,IAAI,cAAc;YAChB,iCAAiC;YACjC,MAAM,aAAa;gBACjB;gBACA;aACD;YAED,IAAI,CAAC,WAAW,QAAQ,CAAC,aAAa,IAAI,KACtC,CAAC,aAAa,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAC1C,CAAC,aAAa,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS;gBACrD,SAAS;gBACT;YACF;YAEA,QAAQ;YACR,SAAS;QACX;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,QAAQ,CAAC,UAAU;QAExB,IAAI,SAAS,UAAU,CAAC,UAAU,IAAI,IAAI;YACxC,SAAS;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YACT,kBAAkB;YAElB,IAAI;YACJ,IAAI,SAAS,QAAQ;gBACnB,WAAW,MAAM,iHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,SAAS,WAAW,EAAE,MAAM,UAAU,IAAI;YACjF,OAAO;gBACL,WAAW,MAAM,iHAAA,CAAA,WAAQ,CAAC,YAAY,CAAC,SAAS,WAAW,EAAE;YAC/D;YAEA,kBAAkB;YAElB,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACzB,MAAM,UAAU,SAAS,IAAI,CAAC,OAAO;gBACrC,IAAI,UAAU,SAAS,IAAI,CAAC,OAAO;gBAEnC,IAAI,SAAS,UAAU,SAAS,gBAAgB;oBAC9C,WAAW,CAAC,EAAE,EAAE,QAAQ,cAAc,CAAC,gBAAgB,CAAC;gBAC1D,OAAO,IAAI,SAAS,YAAY,SAAS,kBAAkB;oBACzD,WAAW,CAAC,EAAE,EAAE,QAAQ,gBAAgB,CAAC,kBAAkB,CAAC;gBAC9D;gBAEA,UAAU;gBACV;YACF,OAAO;gBACL,QAAQ,SAAS,IAAI,CAAC,OAAO,IAAI;YACnC;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;YACX,kBAAkB;QACpB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ,QAAQ;YACR,aAAa;YACb,SAAS;YACT,kBAAkB;YAClB,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,KAAK,GAAG;YAC/B;YACA;QACF;IACF;IAEA,MAAM,eAAe;QACnB,OAAO,SAAS,SAAS,SAAS;IACpC;IAEA,MAAM,sBAAsB;QAC1B,IAAI,SAAS,QAAQ;YACnB,OAAO;gBACL;gBACA;gBACA;gBACA;aACD;QACH,OAAO;YACL,OAAO;gBACL;gBACA;gBACA;gBACA;aACD;QACH;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;gCACrB;gCAAe;;;;;;;sCAE1B,8OAAC,kIAAA,CAAA,oBAAiB;;gCAAC;gCACmB,eAAe,WAAW;gCAAG;;;;;;;;;;;;;8BAIrE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,8OAAC;oCAAG,WAAU;8CACX,sBAAsB,GAAG,CAAC,CAAC,KAAK,sBAC/B,8OAAC;4CAAe,WAAU;;8DACxB,8OAAC;oDAAK,WAAU;8DAAuB;;;;;;8DACvC,8OAAC;8DAAM;;;;;;;2CAFA;;;;;;;;;;;;;;;;wBAQd,uBACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;sCAKvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAO;;;;;;8CACtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CACJ,KAAK;4CACL,IAAG;4CACH,MAAK;4CACL,QAAO;4CACP,UAAU;4CACV,UAAU;4CACV,WAAU;;;;;;wCAEX,sBACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;gCAI/B,sBACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4NAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;wCAC1B,KAAK,IAAI;wCAAC;wCAAG,CAAC,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;wCAAG;;;;;;;;;;;;;wBAMxD,SAAS,wBACR,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAY;;;;;;8CAC3B,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oCAC5C,aAAY;oCACZ,UAAU;;;;;;8CAEZ,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;wBAOxC,yBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;8CAE5B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,GAAG,eAAe,CAAC,CAAC;wCAAC;;;;;;;;;;;;;;;;;wBAO5C,sBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAc;gDAAE;;;;;;;sDAC7B,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAc;gDAAE,KAAK,IAAI;;;;;;;sDACtC,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAoB;gDAAE,CAAC,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;gDAAG;;;;;;;wCACvE,SAAS,UAAU,2BAClB,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAmB;gDAAE;;;;;;;sDAEpC,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAkB;gDAAE,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;8BAMnD,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAa,UAAU;sCAAS;;;;;;sCAGnE,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,WAAW,CAAC,QAAS,SAAS,UAAU,CAAC,UAAU,IAAI;;gCAEhE,wBAAU,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAAiC,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAkB;gCAC1F;;;;;;;;;;;;;;;;;;;;;;;;AAMrB", "debugId": null}}, {"offset": {"line": 7419, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/cavi/ExportDataDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Download, FileSpreadsheet, Database } from 'lucide-react'\nimport { excelApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface ExportDataDialogProps {\n  open: boolean\n  onClose: () => void\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\nexport default function ExportDataDialog({\n  open,\n  onClose,\n  onSuccess,\n  onError\n}: ExportDataDialogProps) {\n  const { cantiere } = useAuth()\n  const [selectedExports, setSelectedExports] = useState({\n    cavi: true,\n    bobine: true,\n    comande: false,\n    certificazioni: false,\n    responsabili: false\n  })\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const handleExportChange = (exportType: string, checked: boolean) => {\n    setSelectedExports(prev => ({\n      ...prev,\n      [exportType]: checked\n    }))\n  }\n\n  const handleExportCavi = async () => {\n    if (!cantiere) return\n\n    try {\n      setLoading(true)\n      const response = await excelApi.exportCavi(cantiere.id_cantiere)\n      \n      // Crea un link per il download\n      const url = window.URL.createObjectURL(new Blob([response.data]))\n      const link = document.createElement('a')\n      link.href = url\n      link.setAttribute('download', `cavi_${cantiere.nome_cantiere}_${new Date().toISOString().split('T')[0]}.xlsx`)\n      document.body.appendChild(link)\n      link.click()\n      link.remove()\n      window.URL.revokeObjectURL(url)\n\n      onSuccess('Export cavi completato con successo')\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\\'export dei cavi'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleExportBobine = async () => {\n    if (!cantiere) return\n\n    try {\n      setLoading(true)\n      const response = await excelApi.exportBobine(cantiere.id_cantiere)\n      \n      // Crea un link per il download\n      const url = window.URL.createObjectURL(new Blob([response.data]))\n      const link = document.createElement('a')\n      link.href = url\n      link.setAttribute('download', `bobine_${cantiere.nome_cantiere}_${new Date().toISOString().split('T')[0]}.xlsx`)\n      document.body.appendChild(link)\n      link.click()\n      link.remove()\n      window.URL.revokeObjectURL(url)\n\n      onSuccess('Export bobine completato con successo')\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\\'export delle bobine'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleExportAll = async () => {\n    if (!cantiere) return\n\n    try {\n      setLoading(true)\n      setError('')\n\n      const exports = []\n\n      if (selectedExports.cavi) {\n        exports.push(handleExportCavi())\n      }\n\n      if (selectedExports.bobine) {\n        exports.push(handleExportBobine())\n      }\n\n      // TODO: Implementare export per comande, certificazioni, responsabili\n      if (selectedExports.comande) {\n      }\n\n      if (selectedExports.certificazioni) {\n      }\n\n      if (selectedExports.responsabili) {\n      }\n\n      await Promise.all(exports)\n\n      const exportCount = Object.values(selectedExports).filter(Boolean).length\n      onSuccess(`Export completato: ${exportCount} file scaricati`)\n      onClose()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\\'export dei dati'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const getSelectedCount = () => {\n    return Object.values(selectedExports).filter(Boolean).length\n  }\n\n  const exportOptions = [\n    {\n      key: 'cavi',\n      label: 'Cavi',\n      description: 'Esporta tutti i cavi del cantiere con stato, collegamenti e certificazioni',\n      icon: <Database className=\"h-4 w-4\" />,\n      available: true\n    },\n    {\n      key: 'bobine',\n      label: 'Bobine',\n      description: 'Esporta tutte le bobine del parco cavi con metri residui e assegnazioni',\n      icon: <FileSpreadsheet className=\"h-4 w-4\" />,\n      available: true\n    },\n    {\n      key: 'comande',\n      label: 'Comande',\n      description: 'Esporta tutte le comande con cavi assegnati e responsabili',\n      icon: <FileSpreadsheet className=\"h-4 w-4\" />,\n      available: false\n    },\n    {\n      key: 'certificazioni',\n      label: 'Certificazioni',\n      description: 'Esporta tutte le certificazioni con esiti e responsabili',\n      icon: <FileSpreadsheet className=\"h-4 w-4\" />,\n      available: false\n    },\n    {\n      key: 'responsabili',\n      label: 'Responsabili',\n      description: 'Esporta tutti i responsabili con contatti e ruoli',\n      icon: <FileSpreadsheet className=\"h-4 w-4\" />,\n      available: false\n    }\n  ]\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <DialogContent className=\"sm:max-w-[600px]\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Download className=\"h-5 w-5\" />\n            Esporta Dati Cantiere\n          </DialogTitle>\n          <DialogDescription>\n            Seleziona i dati da esportare dal cantiere {cantiere?.nome_cantiere}\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          {/* Opzioni di export */}\n          <div className=\"space-y-3\">\n            <Label className=\"text-sm font-medium\">Seleziona Dati da Esportare</Label>\n            \n            {exportOptions.map((option) => (\n              <div\n                key={option.key}\n                className={`flex items-start space-x-3 p-3 rounded-lg border ${\n                  option.available ? 'bg-white' : 'bg-gray-50'\n                }`}\n              >\n                <Checkbox\n                  id={option.key}\n                  checked={selectedExports[option.key as keyof typeof selectedExports]}\n                  onCheckedChange={(checked) => handleExportChange(option.key, checked as boolean)}\n                  disabled={!option.available || loading}\n                />\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center gap-2\">\n                    {option.icon}\n                    <Label\n                      htmlFor={option.key}\n                      className={`font-medium ${!option.available ? 'text-gray-500' : ''}`}\n                    >\n                      {option.label}\n                      {!option.available && (\n                        <span className=\"ml-2 text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded\">\n                          In sviluppo\n                        </span>\n                      )}\n                    </Label>\n                  </div>\n                  <p className={`text-sm mt-1 ${!option.available ? 'text-gray-400' : 'text-gray-600'}`}>\n                    {option.description}\n                  </p>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* Informazioni export */}\n          <div className=\"p-4 bg-blue-50 rounded-lg\">\n            <Label className=\"text-sm font-medium\">Informazioni Export</Label>\n            <ul className=\"mt-2 space-y-1 text-sm text-gray-600\">\n              <li>• I file saranno scaricati in formato Excel (.xlsx)</li>\n              <li>• I nomi file includeranno data e nome cantiere</li>\n              <li>• I dati esportati riflettono lo stato attuale del database</li>\n              <li>• L'export non modifica i dati originali</li>\n            </ul>\n          </div>\n\n          {/* Riepilogo */}\n          {getSelectedCount() > 0 && (\n            <div className=\"p-4 bg-gray-50 rounded-lg\">\n              <Label className=\"text-sm font-medium\">Riepilogo Export</Label>\n              <div className=\"mt-2 space-y-1 text-sm\">\n                <div><strong>Cantiere:</strong> {cantiere?.nome_cantiere}</div>\n                <div><strong>File da scaricare:</strong> {getSelectedCount()}</div>\n                <div><strong>Data export:</strong> {new Date().toLocaleDateString('it-IT')}</div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={onClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button\n            onClick={handleExportAll}\n            disabled={loading || getSelectedCount() === 0}\n          >\n            {loading ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <Download className=\"h-4 w-4 mr-2\" />}\n            Esporta {getSelectedCount() > 0 ? `(${getSelectedCount()})` : ''}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAjBA;;;;;;;;;;;AA0Be,SAAS,iBAAiB,EACvC,IAAI,EACJ,OAAO,EACP,SAAS,EACT,OAAO,EACe;IACtB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrD,MAAM;QACN,QAAQ;QACR,SAAS;QACT,gBAAgB;QAChB,cAAc;IAChB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,qBAAqB,CAAC,YAAoB;QAC9C,mBAAmB,CAAA,OAAQ,CAAC;gBAC1B,GAAG,IAAI;gBACP,CAAC,WAAW,EAAE;YAChB,CAAC;IACH;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,iHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,SAAS,WAAW;YAE/D,+BAA+B;YAC/B,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC,IAAI,KAAK;gBAAC,SAAS,IAAI;aAAC;YAC/D,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,YAAY,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,aAAa,CAAC,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;YAC7G,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,KAAK,MAAM;YACX,OAAO,GAAG,CAAC,eAAe,CAAC;YAE3B,UAAU;QACZ,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,iHAAA,CAAA,WAAQ,CAAC,YAAY,CAAC,SAAS,WAAW;YAEjE,+BAA+B;YAC/B,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC,IAAI,KAAK;gBAAC,SAAS,IAAI;aAAC;YAC/D,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,YAAY,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,aAAa,CAAC,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;YAC/G,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,KAAK,MAAM;YACX,OAAO,GAAG,CAAC,eAAe,CAAC;YAE3B,UAAU;QACZ,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,UAAU,EAAE;YAElB,IAAI,gBAAgB,IAAI,EAAE;gBACxB,QAAQ,IAAI,CAAC;YACf;YAEA,IAAI,gBAAgB,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC;YACf;YAEA,sEAAsE;YACtE,IAAI,gBAAgB,OAAO,EAAE,CAC7B;YAEA,IAAI,gBAAgB,cAAc,EAAE,CACpC;YAEA,IAAI,gBAAgB,YAAY,EAAE,CAClC;YAEA,MAAM,QAAQ,GAAG,CAAC;YAElB,MAAM,cAAc,OAAO,MAAM,CAAC,iBAAiB,MAAM,CAAC,SAAS,MAAM;YACzE,UAAU,CAAC,mBAAmB,EAAE,YAAY,eAAe,CAAC;YAC5D;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,OAAO,OAAO,MAAM,CAAC,iBAAiB,MAAM,CAAC,SAAS,MAAM;IAC9D;IAEA,MAAM,gBAAgB;QACpB;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,WAAW;QACb;QACA;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,4NAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,WAAW;QACb;QACA;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,4NAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,WAAW;QACb;QACA;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,4NAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,WAAW;QACb;QACA;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,4NAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,WAAW;QACb;KACD;IAED,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGlC,8OAAC,kIAAA,CAAA,oBAAiB;;gCAAC;gCAC2B,UAAU;;;;;;;;;;;;;8BAI1D,8OAAC;oBAAI,WAAU;;wBACZ,uBACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;sCAKvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;gCAEtC,cAAc,GAAG,CAAC,CAAC,uBAClB,8OAAC;wCAEC,WAAW,CAAC,iDAAiD,EAC3D,OAAO,SAAS,GAAG,aAAa,cAChC;;0DAEF,8OAAC,oIAAA,CAAA,WAAQ;gDACP,IAAI,OAAO,GAAG;gDACd,SAAS,eAAe,CAAC,OAAO,GAAG,CAAiC;gDACpE,iBAAiB,CAAC,UAAY,mBAAmB,OAAO,GAAG,EAAE;gDAC7D,UAAU,CAAC,OAAO,SAAS,IAAI;;;;;;0DAEjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;4DACZ,OAAO,IAAI;0EACZ,8OAAC,iIAAA,CAAA,QAAK;gEACJ,SAAS,OAAO,GAAG;gEACnB,WAAW,CAAC,YAAY,EAAE,CAAC,OAAO,SAAS,GAAG,kBAAkB,IAAI;;oEAEnE,OAAO,KAAK;oEACZ,CAAC,OAAO,SAAS,kBAChB,8OAAC;wEAAK,WAAU;kFAA2D;;;;;;;;;;;;;;;;;;kEAMjF,8OAAC;wDAAE,WAAW,CAAC,aAAa,EAAE,CAAC,OAAO,SAAS,GAAG,kBAAkB,iBAAiB;kEAClF,OAAO,WAAW;;;;;;;;;;;;;uCA3BlB,OAAO,GAAG;;;;;;;;;;;sCAmCrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;wBAKP,qBAAqB,mBACpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAkB;gDAAE,UAAU;;;;;;;sDAC3C,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAA2B;gDAAE;;;;;;;sDAC1C,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAqB;gDAAE,IAAI,OAAO,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;8BAM1E,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAS,UAAU;sCAAS;;;;;;sCAG/D,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,WAAW,uBAAuB;;gCAE3C,wBAAU,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAAiC,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAkB;gCAC5F,qBAAqB,IAAI,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC,GAAG;;;;;;;;;;;;;;;;;;;;;;;;AAM1E", "debugId": null}}, {"offset": {"line": 7956, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/cavi/AggiungiCavoDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, Plus, AlertCircle } from 'lucide-react'\nimport { caviApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface AggiungiCavoDialogProps {\n  open: boolean\n  onClose: () => void\n  cantiere?: { id_cantiere: string; nome_cantiere: string } | null\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\ninterface CavoFormData {\n  id_cavo: string\n  utility: string\n  sistema: string\n  colore_cavo: string\n  tipologia: string\n  sezione: string\n  ubicazione_partenza: string\n  utenza_partenza: string\n  descrizione_utenza_partenza: string\n  ubicazione_arrivo: string\n  utenza_arrivo: string\n  descrizione_utenza_arrivo: string\n  metri_teorici: string\n}\n\nconst defaultFormData: CavoFormData = {\n  id_cavo: '',\n  utility: '',\n  sistema: '',\n  colore_cavo: '',\n  tipologia: '',\n  sezione: '',\n  ubicazione_partenza: '',\n  utenza_partenza: '',\n  descrizione_utenza_partenza: '',\n  ubicazione_arrivo: '',\n  utenza_arrivo: '',\n  descrizione_utenza_arrivo: '',\n  metri_teorici: ''\n}\n\nexport default function AggiungiCavoDialog({\n  open,\n  onClose,\n  cantiere: cantiereProp,\n  onSuccess,\n  onError\n}: AggiungiCavoDialogProps) {\n  const { cantiere: cantiereFromContext } = useAuth()\n  const cantiere = cantiereProp || cantiereFromContext\n\n  const [formData, setFormData] = useState<CavoFormData>(defaultFormData)\n  const [loading, setLoading] = useState(false)\n  const [errors, setErrors] = useState<Record<string, string>>({})\n\n  // Reset form quando il dialog si apre\n  useEffect(() => {\n    if (open) {\n      setFormData(defaultFormData)\n      setErrors({})\n    }\n  }, [open])\n\n  const handleInputChange = (field: keyof CavoFormData, value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: field === 'id_cavo' ? value.toUpperCase() : value\n    }))\n    \n    // Rimuovi errore quando l'utente inizia a digitare\n    if (errors[field]) {\n      setErrors(prev => {\n        const newErrors = { ...prev }\n        delete newErrors[field]\n        return newErrors\n      })\n    }\n  }\n\n  const validateForm = (): boolean => {\n    const newErrors: Record<string, string> = {}\n\n    // Campi obbligatori\n    if (!formData.id_cavo.trim()) {\n      newErrors.id_cavo = 'ID Cavo è obbligatorio'\n    }\n    if (!formData.utility.trim()) {\n      newErrors.utility = 'Utility è obbligatoria'\n    }\n    if (!formData.metri_teorici.trim()) {\n      newErrors.metri_teorici = 'Metri Teorici sono obbligatori'\n    } else {\n      const metri = parseFloat(formData.metri_teorici)\n      if (isNaN(metri) || metri <= 0) {\n        newErrors.metri_teorici = 'Metri Teorici deve essere un numero positivo'\n      }\n    }\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  const handleSave = async () => {\n    if (!validateForm()) {\n      return\n    }\n\n    if (!cantiere?.id_cantiere) {\n      onError('Cantiere non selezionato')\n      return\n    }\n\n    try {\n      setLoading(true)\n\n      // Prepara i dati per l'API\n      const dataToSubmit = {\n        ...formData,\n        metri_teorici: parseFloat(formData.metri_teorici),\n        id_cantiere: cantiere.id_cantiere\n      }\n\n      await caviApi.createCavo(parseInt(cantiere.id_cantiere), dataToSubmit)\n\n      onSuccess(`Cavo ${formData.id_cavo} aggiunto con successo`)\n      handleClose()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\\'aggiunta del cavo'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleClose = () => {\n    if (!loading) {\n      setFormData(defaultFormData)\n      setErrors({})\n      onClose()\n    }\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Plus className=\"h-5 w-5\" />\n            Aggiungi Nuovo Cavo\n          </DialogTitle>\n          <DialogDescription>\n            Inserisci i dati del nuovo cavo per il cantiere {cantiere?.nome_cantiere}\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-6\">\n          {/* Informazioni Generali */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-medium\">Informazioni Generali</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"id_cavo\">ID Cavo *</Label>\n                <Input\n                  id=\"id_cavo\"\n                  value={formData.id_cavo}\n                  onChange={(e) => handleInputChange('id_cavo', e.target.value)}\n                  placeholder=\"Es. C001\"\n                  className={errors.id_cavo ? 'border-red-500' : ''}\n                />\n                {errors.id_cavo && (\n                  <p className=\"text-sm text-red-500\">{errors.id_cavo}</p>\n                )}\n              </div>\n              \n              <div className=\"space-y-2\">\n                <Label htmlFor=\"utility\">Utility *</Label>\n                <Input\n                  id=\"utility\"\n                  value={formData.utility}\n                  onChange={(e) => handleInputChange('utility', e.target.value)}\n                  placeholder=\"Es. ENEL\"\n                  className={errors.utility ? 'border-red-500' : ''}\n                />\n                {errors.utility && (\n                  <p className=\"text-sm text-red-500\">{errors.utility}</p>\n                )}\n              </div>\n              \n              <div className=\"space-y-2\">\n                <Label htmlFor=\"sistema\">Sistema</Label>\n                <Input\n                  id=\"sistema\"\n                  value={formData.sistema}\n                  onChange={(e) => handleInputChange('sistema', e.target.value)}\n                  placeholder=\"Es. MT\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Caratteristiche Tecniche */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-medium\">Caratteristiche Tecniche</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"colore_cavo\">Colore Cavo</Label>\n                <Input\n                  id=\"colore_cavo\"\n                  value={formData.colore_cavo}\n                  onChange={(e) => handleInputChange('colore_cavo', e.target.value)}\n                  placeholder=\"Es. Nero\"\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"tipologia\">Tipologia</Label>\n                <Input\n                  id=\"tipologia\"\n                  value={formData.tipologia}\n                  onChange={(e) => handleInputChange('tipologia', e.target.value)}\n                  placeholder=\"Es. ARE4H5E\"\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"sezione\">Formazione</Label>\n                <Input\n                  id=\"sezione\"\n                  value={formData.sezione}\n                  onChange={(e) => handleInputChange('sezione', e.target.value)}\n                  placeholder=\"Es. 3X240+120\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Partenza */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-medium\">Partenza</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"ubicazione_partenza\">Ubicazione Partenza</Label>\n                <Input\n                  id=\"ubicazione_partenza\"\n                  value={formData.ubicazione_partenza}\n                  onChange={(e) => handleInputChange('ubicazione_partenza', e.target.value)}\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"utenza_partenza\">Utenza Partenza</Label>\n                <Input\n                  id=\"utenza_partenza\"\n                  value={formData.utenza_partenza}\n                  onChange={(e) => handleInputChange('utenza_partenza', e.target.value)}\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"descrizione_utenza_partenza\">Descrizione Utenza Partenza</Label>\n                <Input\n                  id=\"descrizione_utenza_partenza\"\n                  value={formData.descrizione_utenza_partenza}\n                  onChange={(e) => handleInputChange('descrizione_utenza_partenza', e.target.value)}\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Arrivo */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-medium\">Arrivo</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"ubicazione_arrivo\">Ubicazione Arrivo</Label>\n                <Input\n                  id=\"ubicazione_arrivo\"\n                  value={formData.ubicazione_arrivo}\n                  onChange={(e) => handleInputChange('ubicazione_arrivo', e.target.value)}\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"utenza_arrivo\">Utenza Arrivo</Label>\n                <Input\n                  id=\"utenza_arrivo\"\n                  value={formData.utenza_arrivo}\n                  onChange={(e) => handleInputChange('utenza_arrivo', e.target.value)}\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"descrizione_utenza_arrivo\">Descrizione Utenza Arrivo</Label>\n                <Input\n                  id=\"descrizione_utenza_arrivo\"\n                  value={formData.descrizione_utenza_arrivo}\n                  onChange={(e) => handleInputChange('descrizione_utenza_arrivo', e.target.value)}\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Metratura */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-medium\">Metratura</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"metri_teorici\">Metri Teorici *</Label>\n                <Input\n                  id=\"metri_teorici\"\n                  type=\"number\"\n                  step=\"0.01\"\n                  min=\"0\"\n                  value={formData.metri_teorici}\n                  onChange={(e) => handleInputChange('metri_teorici', e.target.value)}\n                  placeholder=\"Es. 100.50\"\n                  className={errors.metri_teorici ? 'border-red-500' : ''}\n                />\n                {errors.metri_teorici && (\n                  <p className=\"text-sm text-red-500\">{errors.metri_teorici}</p>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Errori generali */}\n          {Object.keys(errors).length > 0 && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>\n                Correggere i campi evidenziati in rosso prima di salvare.\n              </AlertDescription>\n            </Alert>\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={handleClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button onClick={handleSave} disabled={loading}>\n            {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n            {loading ? 'Aggiungendo...' : 'Aggiungi Cavo'}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAjBA;;;;;;;;;;;AA2CA,MAAM,kBAAgC;IACpC,SAAS;IACT,SAAS;IACT,SAAS;IACT,aAAa;IACb,WAAW;IACX,SAAS;IACT,qBAAqB;IACrB,iBAAiB;IACjB,6BAA6B;IAC7B,mBAAmB;IACnB,eAAe;IACf,2BAA2B;IAC3B,eAAe;AACjB;AAEe,SAAS,mBAAmB,EACzC,IAAI,EACJ,OAAO,EACP,UAAU,YAAY,EACtB,SAAS,EACT,OAAO,EACiB;IACxB,MAAM,EAAE,UAAU,mBAAmB,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChD,MAAM,WAAW,gBAAgB;IAEjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,YAAY;YACZ,UAAU,CAAC;QACb;IACF,GAAG;QAAC;KAAK;IAET,MAAM,oBAAoB,CAAC,OAA2B;QACpD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE,UAAU,YAAY,MAAM,WAAW,KAAK;YACvD,CAAC;QAED,mDAAmD;QACnD,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA;gBACR,MAAM,YAAY;oBAAE,GAAG,IAAI;gBAAC;gBAC5B,OAAO,SAAS,CAAC,MAAM;gBACvB,OAAO;YACT;QACF;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,oBAAoB;QACpB,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,UAAU,OAAO,GAAG;QACtB;QACA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,UAAU,OAAO,GAAG;QACtB;QACA,IAAI,CAAC,SAAS,aAAa,CAAC,IAAI,IAAI;YAClC,UAAU,aAAa,GAAG;QAC5B,OAAO;YACL,MAAM,QAAQ,WAAW,SAAS,aAAa;YAC/C,IAAI,MAAM,UAAU,SAAS,GAAG;gBAC9B,UAAU,aAAa,GAAG;YAC5B;QACF;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,IAAI,CAAC,UAAU,aAAa;YAC1B,QAAQ;YACR;QACF;QAEA,IAAI;YACF,WAAW;YAEX,2BAA2B;YAC3B,MAAM,eAAe;gBACnB,GAAG,QAAQ;gBACX,eAAe,WAAW,SAAS,aAAa;gBAChD,aAAa,SAAS,WAAW;YACnC;YAEA,MAAM,iHAAA,CAAA,UAAO,CAAC,UAAU,CAAC,SAAS,SAAS,WAAW,GAAG;YAEzD,UAAU,CAAC,KAAK,EAAE,SAAS,OAAO,CAAC,sBAAsB,CAAC;YAC1D;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ,YAAY;YACZ,UAAU,CAAC;YACX;QACF;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAG9B,8OAAC,kIAAA,CAAA,oBAAiB;;gCAAC;gCACgC,UAAU;;;;;;;;;;;;;8BAI/D,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsB;;;;;;8CACpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;8DACzB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oDAC5D,aAAY;oDACZ,WAAW,OAAO,OAAO,GAAG,mBAAmB;;;;;;gDAEhD,OAAO,OAAO,kBACb,8OAAC;oDAAE,WAAU;8DAAwB,OAAO,OAAO;;;;;;;;;;;;sDAIvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;8DACzB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oDAC5D,aAAY;oDACZ,WAAW,OAAO,OAAO,GAAG,mBAAmB;;;;;;gDAEhD,OAAO,OAAO,kBACb,8OAAC;oDAAE,WAAU;8DAAwB,OAAO,OAAO;;;;;;;;;;;;sDAIvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;8DACzB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oDAC5D,aAAY;;;;;;;;;;;;;;;;;;;;;;;;sCAOpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsB;;;;;;8CACpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAc;;;;;;8DAC7B,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,WAAW;oDAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;oDAChE,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAY;;;;;;8DAC3B,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,SAAS;oDACzB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;oDAC9D,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;8DACzB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oDAC5D,aAAY;;;;;;;;;;;;;;;;;;;;;;;;sCAOpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsB;;;;;;8CACpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAsB;;;;;;8DACrC,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,mBAAmB;oDACnC,UAAU,CAAC,IAAM,kBAAkB,uBAAuB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;sDAI5E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAkB;;;;;;8DACjC,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,eAAe;oDAC/B,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;sDAIxE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAA8B;;;;;;8DAC7C,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,2BAA2B;oDAC3C,UAAU,CAAC,IAAM,kBAAkB,+BAA+B,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAOxF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsB;;;;;;8CACpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAoB;;;;;;8DACnC,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,iBAAiB;oDACjC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;sDAI1E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAgB;;;;;;8DAC/B,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,aAAa;oDAC7B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;sDAItE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAA4B;;;;;;8DAC3C,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,yBAAyB;oDACzC,UAAU,CAAC,IAAM,kBAAkB,6BAA6B,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAOtF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsB;;;;;;8CACpC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAgB;;;;;;0DAC/B,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,KAAI;gDACJ,OAAO,SAAS,aAAa;gDAC7B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAClE,aAAY;gDACZ,WAAW,OAAO,aAAa,GAAG,mBAAmB;;;;;;4CAEtD,OAAO,aAAa,kBACnB,8OAAC;gDAAE,WAAU;0DAAwB,OAAO,aAAa;;;;;;;;;;;;;;;;;;;;;;;wBAOhE,OAAO,IAAI,CAAC,QAAQ,MAAM,GAAG,mBAC5B,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAC;;;;;;;;;;;;;;;;;;8BAOxB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAa,UAAU;sCAAS;;;;;;sCAGnE,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAY,UAAU;;gCACpC,yBAAW,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAC9B,UAAU,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;AAM1C", "debugId": null}}, {"offset": {"line": 8711, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/cavi/ModificaCavoDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, Edit, AlertCircle, AlertTriangle } from 'lucide-react'\nimport { caviApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Cavo } from '@/types'\n\ninterface ModificaCavoDialogProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  cantiere?: { id_cantiere: string; nome_cantiere: string } | null\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\ninterface CavoFormData {\n  id_cavo: string\n  utility: string\n  sistema: string\n  colore_cavo: string\n  tipologia: string\n  sezione: string\n  ubicazione_partenza: string\n  utenza_partenza: string\n  descrizione_utenza_partenza: string\n  ubicazione_arrivo: string\n  utenza_arrivo: string\n  descrizione_utenza_arrivo: string\n  metri_teorici: string\n}\n\nexport default function ModificaCavoDialog({\n  open,\n  onClose,\n  cavo,\n  cantiere: cantiereProp,\n  onSuccess,\n  onError\n}: ModificaCavoDialogProps) {\n  const { cantiere: cantiereFromContext } = useAuth()\n  const cantiere = cantiereProp || cantiereFromContext\n\n  const [formData, setFormData] = useState<CavoFormData>({\n    id_cavo: '',\n    utility: '',\n    sistema: '',\n    colore_cavo: '',\n    tipologia: '',\n    sezione: '',\n    ubicazione_partenza: '',\n    utenza_partenza: '',\n    descrizione_utenza_partenza: '',\n    ubicazione_arrivo: '',\n    utenza_arrivo: '',\n    descrizione_utenza_arrivo: '',\n    metri_teorici: ''\n  })\n  \n  const [loading, setLoading] = useState(false)\n  const [errors, setErrors] = useState<Record<string, string>>({})\n\n  // Popola il form quando il cavo cambia\n  useEffect(() => {\n    if (open && cavo) {\n      setFormData({\n        id_cavo: cavo.id_cavo || '',\n        utility: cavo.utility || '',\n        sistema: cavo.sistema || '',\n        colore_cavo: cavo.colore_cavo || '',\n        tipologia: cavo.tipologia || '',\n        sezione: cavo.sezione || '',\n        ubicazione_partenza: cavo.ubicazione_partenza || '',\n        utenza_partenza: cavo.utenza_partenza || '',\n        descrizione_utenza_partenza: cavo.descrizione_utenza_partenza || '',\n        ubicazione_arrivo: cavo.ubicazione_arrivo || '',\n        utenza_arrivo: cavo.utenza_arrivo || '',\n        descrizione_utenza_arrivo: cavo.descrizione_utenza_arrivo || '',\n        metri_teorici: cavo.metri_teorici?.toString() || ''\n      })\n      setErrors({})\n    }\n  }, [open, cavo])\n\n  const handleInputChange = (field: keyof CavoFormData, value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: field === 'id_cavo' ? value.toUpperCase() : value\n    }))\n    \n    // Rimuovi errore quando l'utente inizia a digitare\n    if (errors[field]) {\n      setErrors(prev => {\n        const newErrors = { ...prev }\n        delete newErrors[field]\n        return newErrors\n      })\n    }\n  }\n\n  const validateForm = (): boolean => {\n    const newErrors: Record<string, string> = {}\n\n    // Campi obbligatori\n    if (!formData.id_cavo.trim()) {\n      newErrors.id_cavo = 'ID Cavo è obbligatorio'\n    }\n    if (!formData.utility.trim()) {\n      newErrors.utility = 'Utility è obbligatoria'\n    }\n    if (!formData.metri_teorici.trim()) {\n      newErrors.metri_teorici = 'Metri Teorici sono obbligatori'\n    } else {\n      const metri = parseFloat(formData.metri_teorici)\n      if (isNaN(metri) || metri <= 0) {\n        newErrors.metri_teorici = 'Metri Teorici deve essere un numero positivo'\n      }\n    }\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  const handleSave = async () => {\n    if (!validateForm()) {\n      return\n    }\n\n    if (!cavo?.id_cavo) {\n      onError('Cavo non selezionato')\n      return\n    }\n\n    if (!cantiere?.id_cantiere) {\n      onError('Cantiere non selezionato')\n      return\n    }\n\n    try {\n      setLoading(true)\n\n      // Prepara i dati per l'API\n      const dataToSubmit = {\n        ...formData,\n        metri_teorici: parseFloat(formData.metri_teorici),\n        id_cantiere: cantiere.id_cantiere\n      }\n\n      await caviApi.updateCavo(parseInt(cantiere.id_cantiere), cavo.id_cavo, dataToSubmit)\n\n      onSuccess(`Cavo ${formData.id_cavo} modificato con successo`)\n      handleClose()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la modifica del cavo'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleClose = () => {\n    if (!loading) {\n      setErrors({})\n      onClose()\n    }\n  }\n\n  // Verifica se il cavo è installato (non può essere modificato)\n  const isInstalled = cavo && (cavo.metratura_reale > 0 || cavo.stato_installazione !== 'da installare')\n\n  if (!cavo) return null\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Edit className=\"h-5 w-5\" />\n            Modifica Cavo: {cavo.id_cavo}\n          </DialogTitle>\n          <DialogDescription>\n            Modifica i dati del cavo nel cantiere {cantiere?.nome_cantiere}\n          </DialogDescription>\n        </DialogHeader>\n\n        {isInstalled && (\n          <Alert variant=\"destructive\">\n            <AlertTriangle className=\"h-4 w-4\" />\n            <AlertDescription>\n              <strong>Attenzione:</strong> Questo cavo è già stato installato. \n              La modifica potrebbe influire sui dati di installazione esistenti.\n            </AlertDescription>\n          </Alert>\n        )}\n\n        <div className=\"space-y-6\">\n          {/* Informazioni Generali */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-medium\">Informazioni Generali</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"id_cavo\">ID Cavo *</Label>\n                <Input\n                  id=\"id_cavo\"\n                  value={formData.id_cavo}\n                  onChange={(e) => handleInputChange('id_cavo', e.target.value)}\n                  placeholder=\"Es. C001\"\n                  className={errors.id_cavo ? 'border-red-500' : ''}\n                />\n                {errors.id_cavo && (\n                  <p className=\"text-sm text-red-500\">{errors.id_cavo}</p>\n                )}\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"utility\">Utility *</Label>\n                <Input\n                  id=\"utility\"\n                  value={formData.utility}\n                  onChange={(e) => handleInputChange('utility', e.target.value)}\n                  placeholder=\"Es. ENEL\"\n                  className={errors.utility ? 'border-red-500' : ''}\n                />\n                {errors.utility && (\n                  <p className=\"text-sm text-red-500\">{errors.utility}</p>\n                )}\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"sistema\">Sistema</Label>\n                <Input\n                  id=\"sistema\"\n                  value={formData.sistema}\n                  onChange={(e) => handleInputChange('sistema', e.target.value)}\n                  placeholder=\"Es. MT\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Caratteristiche Tecniche */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-medium\">Caratteristiche Tecniche</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"colore_cavo\">Colore Cavo</Label>\n                <Input\n                  id=\"colore_cavo\"\n                  value={formData.colore_cavo}\n                  onChange={(e) => handleInputChange('colore_cavo', e.target.value)}\n                  placeholder=\"Es. Nero\"\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"tipologia\">Tipologia</Label>\n                <Input\n                  id=\"tipologia\"\n                  value={formData.tipologia}\n                  onChange={(e) => handleInputChange('tipologia', e.target.value)}\n                  placeholder=\"Es. ARE4H5E\"\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"sezione\">Formazione</Label>\n                <Input\n                  id=\"sezione\"\n                  value={formData.sezione}\n                  onChange={(e) => handleInputChange('sezione', e.target.value)}\n                  placeholder=\"Es. 3X240+120\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Partenza */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-medium\">Partenza</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"ubicazione_partenza\">Ubicazione Partenza</Label>\n                <Input\n                  id=\"ubicazione_partenza\"\n                  value={formData.ubicazione_partenza}\n                  onChange={(e) => handleInputChange('ubicazione_partenza', e.target.value)}\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"utenza_partenza\">Utenza Partenza</Label>\n                <Input\n                  id=\"utenza_partenza\"\n                  value={formData.utenza_partenza}\n                  onChange={(e) => handleInputChange('utenza_partenza', e.target.value)}\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"descrizione_utenza_partenza\">Descrizione Utenza Partenza</Label>\n                <Input\n                  id=\"descrizione_utenza_partenza\"\n                  value={formData.descrizione_utenza_partenza}\n                  onChange={(e) => handleInputChange('descrizione_utenza_partenza', e.target.value)}\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Arrivo */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-medium\">Arrivo</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"ubicazione_arrivo\">Ubicazione Arrivo</Label>\n                <Input\n                  id=\"ubicazione_arrivo\"\n                  value={formData.ubicazione_arrivo}\n                  onChange={(e) => handleInputChange('ubicazione_arrivo', e.target.value)}\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"utenza_arrivo\">Utenza Arrivo</Label>\n                <Input\n                  id=\"utenza_arrivo\"\n                  value={formData.utenza_arrivo}\n                  onChange={(e) => handleInputChange('utenza_arrivo', e.target.value)}\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"descrizione_utenza_arrivo\">Descrizione Utenza Arrivo</Label>\n                <Input\n                  id=\"descrizione_utenza_arrivo\"\n                  value={formData.descrizione_utenza_arrivo}\n                  onChange={(e) => handleInputChange('descrizione_utenza_arrivo', e.target.value)}\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Metratura */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-medium\">Metratura</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"metri_teorici\">Metri Teorici *</Label>\n                <Input\n                  id=\"metri_teorici\"\n                  type=\"number\"\n                  step=\"0.01\"\n                  min=\"0\"\n                  value={formData.metri_teorici}\n                  onChange={(e) => handleInputChange('metri_teorici', e.target.value)}\n                  placeholder=\"Es. 100.50\"\n                  className={errors.metri_teorici ? 'border-red-500' : ''}\n                />\n                {errors.metri_teorici && (\n                  <p className=\"text-sm text-red-500\">{errors.metri_teorici}</p>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Errori generali */}\n          {Object.keys(errors).length > 0 && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>\n                Correggere i campi evidenziati in rosso prima di salvare.\n              </AlertDescription>\n            </Alert>\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={handleClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button onClick={handleSave} disabled={loading}>\n            {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n            {loading ? 'Salvando...' : 'Salva Modifiche'}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAjBA;;;;;;;;;;;AA6Ce,SAAS,mBAAmB,EACzC,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,UAAU,YAAY,EACtB,SAAS,EACT,OAAO,EACiB;IACxB,MAAM,EAAE,UAAU,mBAAmB,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChD,MAAM,WAAW,gBAAgB;IAEjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QACrD,SAAS;QACT,SAAS;QACT,SAAS;QACT,aAAa;QACb,WAAW;QACX,SAAS;QACT,qBAAqB;QACrB,iBAAiB;QACjB,6BAA6B;QAC7B,mBAAmB;QACnB,eAAe;QACf,2BAA2B;QAC3B,eAAe;IACjB;IAEA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,MAAM;YAChB,YAAY;gBACV,SAAS,KAAK,OAAO,IAAI;gBACzB,SAAS,KAAK,OAAO,IAAI;gBACzB,SAAS,KAAK,OAAO,IAAI;gBACzB,aAAa,KAAK,WAAW,IAAI;gBACjC,WAAW,KAAK,SAAS,IAAI;gBAC7B,SAAS,KAAK,OAAO,IAAI;gBACzB,qBAAqB,KAAK,mBAAmB,IAAI;gBACjD,iBAAiB,KAAK,eAAe,IAAI;gBACzC,6BAA6B,KAAK,2BAA2B,IAAI;gBACjE,mBAAmB,KAAK,iBAAiB,IAAI;gBAC7C,eAAe,KAAK,aAAa,IAAI;gBACrC,2BAA2B,KAAK,yBAAyB,IAAI;gBAC7D,eAAe,KAAK,aAAa,EAAE,cAAc;YACnD;YACA,UAAU,CAAC;QACb;IACF,GAAG;QAAC;QAAM;KAAK;IAEf,MAAM,oBAAoB,CAAC,OAA2B;QACpD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE,UAAU,YAAY,MAAM,WAAW,KAAK;YACvD,CAAC;QAED,mDAAmD;QACnD,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA;gBACR,MAAM,YAAY;oBAAE,GAAG,IAAI;gBAAC;gBAC5B,OAAO,SAAS,CAAC,MAAM;gBACvB,OAAO;YACT;QACF;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,oBAAoB;QACpB,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,UAAU,OAAO,GAAG;QACtB;QACA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,UAAU,OAAO,GAAG;QACtB;QACA,IAAI,CAAC,SAAS,aAAa,CAAC,IAAI,IAAI;YAClC,UAAU,aAAa,GAAG;QAC5B,OAAO;YACL,MAAM,QAAQ,WAAW,SAAS,aAAa;YAC/C,IAAI,MAAM,UAAU,SAAS,GAAG;gBAC9B,UAAU,aAAa,GAAG;YAC5B;QACF;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,IAAI,CAAC,MAAM,SAAS;YAClB,QAAQ;YACR;QACF;QAEA,IAAI,CAAC,UAAU,aAAa;YAC1B,QAAQ;YACR;QACF;QAEA,IAAI;YACF,WAAW;YAEX,2BAA2B;YAC3B,MAAM,eAAe;gBACnB,GAAG,QAAQ;gBACX,eAAe,WAAW,SAAS,aAAa;gBAChD,aAAa,SAAS,WAAW;YACnC;YAEA,MAAM,iHAAA,CAAA,UAAO,CAAC,UAAU,CAAC,SAAS,SAAS,WAAW,GAAG,KAAK,OAAO,EAAE;YAEvE,UAAU,CAAC,KAAK,EAAE,SAAS,OAAO,CAAC,wBAAwB,CAAC;YAC5D;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ,UAAU,CAAC;YACX;QACF;IACF;IAEA,+DAA+D;IAC/D,MAAM,cAAc,QAAQ,CAAC,KAAK,eAAe,GAAG,KAAK,KAAK,mBAAmB,KAAK,eAAe;IAErG,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,2MAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;gCACZ,KAAK,OAAO;;;;;;;sCAE9B,8OAAC,kIAAA,CAAA,oBAAiB;;gCAAC;gCACsB,UAAU;;;;;;;;;;;;;gBAIpD,6BACC,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;;sCACb,8OAAC,wNAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,8OAAC,iIAAA,CAAA,mBAAgB;;8CACf,8OAAC;8CAAO;;;;;;gCAAoB;;;;;;;;;;;;;8BAMlC,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsB;;;;;;8CACpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;8DACzB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oDAC5D,aAAY;oDACZ,WAAW,OAAO,OAAO,GAAG,mBAAmB;;;;;;gDAEhD,OAAO,OAAO,kBACb,8OAAC;oDAAE,WAAU;8DAAwB,OAAO,OAAO;;;;;;;;;;;;sDAIvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;8DACzB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oDAC5D,aAAY;oDACZ,WAAW,OAAO,OAAO,GAAG,mBAAmB;;;;;;gDAEhD,OAAO,OAAO,kBACb,8OAAC;oDAAE,WAAU;8DAAwB,OAAO,OAAO;;;;;;;;;;;;sDAIvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;8DACzB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oDAC5D,aAAY;;;;;;;;;;;;;;;;;;;;;;;;sCAOpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsB;;;;;;8CACpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAc;;;;;;8DAC7B,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,WAAW;oDAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;oDAChE,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAY;;;;;;8DAC3B,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,SAAS;oDACzB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;oDAC9D,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;8DACzB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oDAC5D,aAAY;;;;;;;;;;;;;;;;;;;;;;;;sCAOpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsB;;;;;;8CACpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAsB;;;;;;8DACrC,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,mBAAmB;oDACnC,UAAU,CAAC,IAAM,kBAAkB,uBAAuB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;sDAI5E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAkB;;;;;;8DACjC,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,eAAe;oDAC/B,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;sDAIxE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAA8B;;;;;;8DAC7C,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,2BAA2B;oDAC3C,UAAU,CAAC,IAAM,kBAAkB,+BAA+B,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAOxF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsB;;;;;;8CACpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAoB;;;;;;8DACnC,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,iBAAiB;oDACjC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;sDAI1E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAgB;;;;;;8DAC/B,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,aAAa;oDAC7B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;sDAItE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAA4B;;;;;;8DAC3C,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,yBAAyB;oDACzC,UAAU,CAAC,IAAM,kBAAkB,6BAA6B,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAOtF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsB;;;;;;8CACpC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAgB;;;;;;0DAC/B,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,KAAI;gDACJ,OAAO,SAAS,aAAa;gDAC7B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAClE,aAAY;gDACZ,WAAW,OAAO,aAAa,GAAG,mBAAmB;;;;;;4CAEtD,OAAO,aAAa,kBACnB,8OAAC;gDAAE,WAAU;0DAAwB,OAAO,aAAa;;;;;;;;;;;;;;;;;;;;;;;wBAOhE,OAAO,IAAI,CAAC,QAAQ,MAAM,GAAG,mBAC5B,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAC;;;;;;;;;;;;;;;;;;8BAOxB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAa,UAAU;sCAAS;;;;;;sCAGnE,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAY,UAAU;;gCACpC,yBAAW,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAC9B,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;AAMvC", "debugId": null}}, {"offset": {"line": 9520, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_2/src/components/cavi/EliminaCavoDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'\nimport { Label } from '@/components/ui/label'\nimport { Loader2, Trash2, AlertTriangle, Info } from 'lucide-react'\nimport { caviApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Cavo } from '@/types'\n\ninterface EliminaCavoDialogProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  cantiere?: { id_cantiere: string; nome_cantiere: string } | null\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\nexport default function EliminaCavoDialog({\n  open,\n  onClose,\n  cavo,\n  cantiere: cantiereProp,\n  onSuccess,\n  onError\n}: EliminaCavoDialogProps) {\n  const { cantiere: cantiereFromContext } = useAuth()\n  const cantiere = cantiereProp || cantiereFromContext\n\n  const [loading, setLoading] = useState(false)\n  const [deleteOption, setDeleteOption] = useState<'spare' | 'permanent'>('spare')\n\n  const handleDelete = async () => {\n    if (!cavo?.id_cavo) {\n      onError('Cavo non selezionato')\n      return\n    }\n\n    if (!cantiere?.id_cantiere) {\n      onError('Cantiere non selezionato')\n      return\n    }\n\n    try {\n      setLoading(true)\n      console.log(`Iniziando operazione ${deleteOption} per cavo ${cavo.id_cavo}`)\n\n      if (deleteOption === 'spare') {\n        // Marca come SPARE (flag 3)\n        console.log('Chiamando markAsSpare API...')\n        const result = await caviApi.markAsSpare(parseInt(cantiere.id_cantiere), cavo.id_cavo, true)\n        console.log('Risultato markAsSpare:', result)\n        onSuccess(`Cavo ${cavo.id_cavo} marcato come SPARE`)\n      } else {\n        // Eliminazione definitiva\n        console.log('Chiamando deleteCavo API...')\n        const result = await caviApi.deleteCavo(parseInt(cantiere.id_cantiere), cavo.id_cavo)\n        console.log('Risultato deleteCavo:', result)\n        onSuccess(`Cavo ${cavo.id_cavo} eliminato definitivamente`)\n      }\n\n      handleClose()\n    } catch (error: any) {\n      console.error('Errore durante operazione:', error)\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\\'eliminazione del cavo'\n      console.error('Messaggio errore:', errorMessage)\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleClose = () => {\n    if (!loading) {\n      setDeleteOption('spare')\n      onClose()\n    }\n  }\n\n  if (!cavo) return null\n\n  // Determina se il cavo è installato\n  const isInstalled = cavo.metratura_reale > 0 ||\n    (cavo.stato_installazione && cavo.stato_installazione.toLowerCase() !== 'da installare')\n  const canPermanentDelete = !isInstalled\n\n  // Informazioni sul cavo\n  const cavoInfo = {\n    id: cavo.id_cavo,\n    tipologia: cavo.tipologia || 'N/A',\n    sezione: cavo.sezione || 'N/A',\n    metri_teorici: cavo.metri_teorici || 0,\n    metri_reali: cavo.metratura_reale || 0,\n    stato: cavo.stato_installazione || 'N/A',\n    bobina: cavo.id_bobina || 'N/A'\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent className=\"max-w-2xl\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Trash2 className=\"h-5 w-5\" />\n            Elimina Cavo: {cavo.id_cavo}\n          </DialogTitle>\n          <DialogDescription>\n            Scegli come gestire l'eliminazione del cavo dal cantiere {cantiere?.nome_cantiere}\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-6\">\n          {/* Informazioni sul cavo */}\n          <div className=\"space-y-3\">\n            <h3 className=\"text-lg font-medium\">Informazioni Cavo</h3>\n            <div className=\"grid grid-cols-2 gap-4 text-sm\">\n              <div>\n                <span className=\"text-gray-600\">ID:</span>\n                <span className=\"ml-2 font-medium\">{cavoInfo.id}</span>\n              </div>\n              <div>\n                <span className=\"text-gray-600\">Tipologia:</span>\n                <span className=\"ml-2 font-medium\">{cavoInfo.tipologia}</span>\n              </div>\n              <div>\n                <span className=\"text-gray-600\">Formazione:</span>\n                <span className=\"ml-2 font-medium\">{cavoInfo.sezione}</span>\n              </div>\n              <div>\n                <span className=\"text-gray-600\">Stato:</span>\n                <span className=\"ml-2 font-medium\">{cavoInfo.stato}</span>\n              </div>\n              <div>\n                <span className=\"text-gray-600\">Metri Teorici:</span>\n                <span className=\"ml-2 font-medium\">{cavoInfo.metri_teorici}</span>\n              </div>\n              <div>\n                <span className=\"text-gray-600\">Metri Installati:</span>\n                <span className=\"ml-2 font-medium\">{cavoInfo.metri_reali}</span>\n              </div>\n              <div className=\"col-span-2\">\n                <span className=\"text-gray-600\">Bobina:</span>\n                <span className=\"ml-2 font-medium\">{cavoInfo.bobina}</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Opzioni di eliminazione */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-medium\">Modalità di Eliminazione</h3>\n            \n            <RadioGroup value={deleteOption} onValueChange={(value) => setDeleteOption(value as 'spare' | 'permanent')}>\n              <div className=\"flex items-center space-x-2\">\n                <RadioGroupItem value=\"spare\" id=\"spare\" />\n                <Label htmlFor=\"spare\" className=\"flex-1\">\n                  <div className=\"font-medium\">Marca come SPARE</div>\n                  <div className=\"text-sm text-gray-600\">\n                    Il cavo viene marcato come spare/consumato ma rimane nel database per tracciabilità\n                  </div>\n                </Label>\n              </div>\n              \n              <div className=\"flex items-center space-x-2\">\n                <RadioGroupItem \n                  value=\"permanent\" \n                  id=\"permanent\" \n                  disabled={!canPermanentDelete}\n                />\n                <Label htmlFor=\"permanent\" className={`flex-1 ${!canPermanentDelete ? 'opacity-50' : ''}`}>\n                  <div className=\"font-medium\">Eliminazione Definitiva</div>\n                  <div className=\"text-sm text-gray-600\">\n                    Il cavo viene rimosso completamente dal database\n                    {!canPermanentDelete && ' (non disponibile per cavi installati)'}\n                  </div>\n                </Label>\n              </div>\n            </RadioGroup>\n          </div>\n\n          {/* Avvisi */}\n          {isInstalled && (\n            <Alert>\n              <Info className=\"h-4 w-4\" />\n              <AlertDescription>\n                <strong>Cavo Installato:</strong> Questo cavo ha metri installati. \n                Si consiglia di marcarlo come SPARE piuttosto che eliminarlo definitivamente.\n              </AlertDescription>\n            </Alert>\n          )}\n\n          {deleteOption === 'permanent' && (\n            <Alert variant=\"destructive\">\n              <AlertTriangle className=\"h-4 w-4\" />\n              <AlertDescription>\n                <strong>Attenzione:</strong> L'eliminazione definitiva è irreversibile. \n                Tutti i dati del cavo verranno persi permanentemente.\n              </AlertDescription>\n            </Alert>\n          )}\n\n          {deleteOption === 'spare' && (\n            <Alert>\n              <Info className=\"h-4 w-4\" />\n              <AlertDescription>\n                Il cavo verrà marcato come SPARE e non apparirà più nelle liste attive, \n                ma rimarrà nel database per eventuali controlli futuri.\n              </AlertDescription>\n            </Alert>\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={handleClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button \n            variant=\"destructive\" \n            onClick={handleDelete} \n            disabled={loading}\n          >\n            {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n            {loading \n              ? 'Eliminando...' \n              : deleteOption === 'spare' \n                ? 'Marca come SPARE' \n                : 'Elimina Definitivamente'\n            }\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAjBA;;;;;;;;;;;AA6Be,SAAS,kBAAkB,EACxC,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,UAAU,YAAY,EACtB,SAAS,EACT,OAAO,EACgB;IACvB,MAAM,EAAE,UAAU,mBAAmB,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChD,MAAM,WAAW,gBAAgB;IAEjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAExE,MAAM,eAAe;QACnB,IAAI,CAAC,MAAM,SAAS;YAClB,QAAQ;YACR;QACF;QAEA,IAAI,CAAC,UAAU,aAAa;YAC1B,QAAQ;YACR;QACF;QAEA,IAAI;YACF,WAAW;YACX,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,aAAa,UAAU,EAAE,KAAK,OAAO,EAAE;YAE3E,IAAI,iBAAiB,SAAS;gBAC5B,4BAA4B;gBAC5B,QAAQ,GAAG,CAAC;gBACZ,MAAM,SAAS,MAAM,iHAAA,CAAA,UAAO,CAAC,WAAW,CAAC,SAAS,SAAS,WAAW,GAAG,KAAK,OAAO,EAAE;gBACvF,QAAQ,GAAG,CAAC,0BAA0B;gBACtC,UAAU,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,mBAAmB,CAAC;YACrD,OAAO;gBACL,0BAA0B;gBAC1B,QAAQ,GAAG,CAAC;gBACZ,MAAM,SAAS,MAAM,iHAAA,CAAA,UAAO,CAAC,UAAU,CAAC,SAAS,SAAS,WAAW,GAAG,KAAK,OAAO;gBACpF,QAAQ,GAAG,CAAC,yBAAyB;gBACrC,UAAU,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,0BAA0B,CAAC;YAC5D;YAEA;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ,KAAK,CAAC,qBAAqB;YACnC,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ,gBAAgB;YAChB;QACF;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,oCAAoC;IACpC,MAAM,cAAc,KAAK,eAAe,GAAG,KACxC,KAAK,mBAAmB,IAAI,KAAK,mBAAmB,CAAC,WAAW,OAAO;IAC1E,MAAM,qBAAqB,CAAC;IAE5B,wBAAwB;IACxB,MAAM,WAAW;QACf,IAAI,KAAK,OAAO;QAChB,WAAW,KAAK,SAAS,IAAI;QAC7B,SAAS,KAAK,OAAO,IAAI;QACzB,eAAe,KAAK,aAAa,IAAI;QACrC,aAAa,KAAK,eAAe,IAAI;QACrC,OAAO,KAAK,mBAAmB,IAAI;QACnC,QAAQ,KAAK,SAAS,IAAI;IAC5B;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,0MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;gCACf,KAAK,OAAO;;;;;;;sCAE7B,8OAAC,kIAAA,CAAA,oBAAiB;;gCAAC;gCACyC,UAAU;;;;;;;;;;;;;8BAIxE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsB;;;;;;8CACpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DAAoB,SAAS,EAAE;;;;;;;;;;;;sDAEjD,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DAAoB,SAAS,SAAS;;;;;;;;;;;;sDAExD,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DAAoB,SAAS,OAAO;;;;;;;;;;;;sDAEtD,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DAAoB,SAAS,KAAK;;;;;;;;;;;;sDAEpD,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DAAoB,SAAS,aAAa;;;;;;;;;;;;sDAE5D,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DAAoB,SAAS,WAAW;;;;;;;;;;;;sDAE1D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DAAoB,SAAS,MAAM;;;;;;;;;;;;;;;;;;;;;;;;sCAMzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsB;;;;;;8CAEpC,8OAAC,0IAAA,CAAA,aAAU;oCAAC,OAAO;oCAAc,eAAe,CAAC,QAAU,gBAAgB;;sDACzE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0IAAA,CAAA,iBAAc;oDAAC,OAAM;oDAAQ,IAAG;;;;;;8DACjC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAQ,WAAU;;sEAC/B,8OAAC;4DAAI,WAAU;sEAAc;;;;;;sEAC7B,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAM3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0IAAA,CAAA,iBAAc;oDACb,OAAM;oDACN,IAAG;oDACH,UAAU,CAAC;;;;;;8DAEb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAW,CAAC,OAAO,EAAE,CAAC,qBAAqB,eAAe,IAAI;;sEACvF,8OAAC;4DAAI,WAAU;sEAAc;;;;;;sEAC7B,8OAAC;4DAAI,WAAU;;gEAAwB;gEAEpC,CAAC,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAQjC,6BACC,8OAAC,iIAAA,CAAA,QAAK;;8CACJ,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC,iIAAA,CAAA,mBAAgB;;sDACf,8OAAC;sDAAO;;;;;;wCAAyB;;;;;;;;;;;;;wBAMtC,iBAAiB,6BAChB,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC,iIAAA,CAAA,mBAAgB;;sDACf,8OAAC;sDAAO;;;;;;wCAAoB;;;;;;;;;;;;;wBAMjC,iBAAiB,yBAChB,8OAAC,iIAAA,CAAA,QAAK;;8CACJ,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAC;;;;;;;;;;;;;;;;;;8BAQxB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAa,UAAU;sCAAS;;;;;;sCAGnE,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;;gCAET,yBAAW,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAC9B,UACG,kBACA,iBAAiB,UACf,qBACA;;;;;;;;;;;;;;;;;;;;;;;;AAOlB", "debugId": null}}]}