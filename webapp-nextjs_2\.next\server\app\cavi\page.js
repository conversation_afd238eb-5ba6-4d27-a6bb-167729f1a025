(()=>{var e={};e.id=986,e.ids=[986],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3402:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>eW});var i=t(60687),s=t(43210),r=t(16189),n=t(44493),l=t(29523),o=t(91821),c=t(63213),d=t(62185),m=t(96834),x=t(56896);t(15391);var u=t(6211),p=t(89667),h=t(15079),b=t(70569),g=t(98599),v=t(11273),j=t(31355),f=t(1359),N=t(32547),y=t(96963),C=t(55509),w=t(25028),_=t(46059),A=t(14163),z=t(8730),k=t(65551),S=t(63376),E=t(42247),I="Popover",[O,$]=(0,v.A)(I,[C.Bk]),T=(0,C.Bk)(),[M,F]=O(I),L=e=>{let{__scopePopover:a,children:t,open:r,defaultOpen:n,onOpenChange:l,modal:o=!1}=e,c=T(a),d=s.useRef(null),[m,x]=s.useState(!1),[u,p]=(0,k.i)({prop:r,defaultProp:n??!1,onChange:l,caller:I});return(0,i.jsx)(C.bL,{...c,children:(0,i.jsx)(M,{scope:a,contentId:(0,y.B)(),triggerRef:d,open:u,onOpenChange:p,onOpenToggle:s.useCallback(()=>p(e=>!e),[p]),hasCustomAnchor:m,onCustomAnchorAdd:s.useCallback(()=>x(!0),[]),onCustomAnchorRemove:s.useCallback(()=>x(!1),[]),modal:o,children:t})})};L.displayName=I;var D="PopoverAnchor";s.forwardRef((e,a)=>{let{__scopePopover:t,...r}=e,n=F(D,t),l=T(t),{onCustomAnchorAdd:o,onCustomAnchorRemove:c}=n;return s.useEffect(()=>(o(),()=>c()),[o,c]),(0,i.jsx)(C.Mz,{...l,...r,ref:a})}).displayName=D;var P="PopoverTrigger",B=s.forwardRef((e,a)=>{let{__scopePopover:t,...s}=e,r=F(P,t),n=T(t),l=(0,g.s)(a,r.triggerRef),o=(0,i.jsx)(A.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":r.open,"aria-controls":r.contentId,"data-state":K(r.open),...s,ref:l,onClick:(0,b.m)(e.onClick,r.onOpenToggle)});return r.hasCustomAnchor?o:(0,i.jsx)(C.Mz,{asChild:!0,...n,children:o})});B.displayName=P;var R="PopoverPortal",[V,q]=O(R,{forceMount:void 0}),U=e=>{let{__scopePopover:a,forceMount:t,children:s,container:r}=e,n=F(R,a);return(0,i.jsx)(V,{scope:a,forceMount:t,children:(0,i.jsx)(_.C,{present:t||n.open,children:(0,i.jsx)(w.Z,{asChild:!0,container:r,children:s})})})};U.displayName=R;var G="PopoverContent",J=s.forwardRef((e,a)=>{let t=q(G,e.__scopePopover),{forceMount:s=t.forceMount,...r}=e,n=F(G,e.__scopePopover);return(0,i.jsx)(_.C,{present:s||n.open,children:n.modal?(0,i.jsx)(H,{...r,ref:a}):(0,i.jsx)(W,{...r,ref:a})})});J.displayName=G;var Z=(0,z.TL)("PopoverContent.RemoveScroll"),H=s.forwardRef((e,a)=>{let t=F(G,e.__scopePopover),r=s.useRef(null),n=(0,g.s)(a,r),l=s.useRef(!1);return s.useEffect(()=>{let e=r.current;if(e)return(0,S.Eq)(e)},[]),(0,i.jsx)(E.A,{as:Z,allowPinchZoom:!0,children:(0,i.jsx)(Y,{...e,ref:n,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,b.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),l.current||t.triggerRef.current?.focus()}),onPointerDownOutside:(0,b.m)(e.onPointerDownOutside,e=>{let a=e.detail.originalEvent,t=0===a.button&&!0===a.ctrlKey;l.current=2===a.button||t},{checkForDefaultPrevented:!1}),onFocusOutside:(0,b.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),W=s.forwardRef((e,a)=>{let t=F(G,e.__scopePopover),r=s.useRef(!1),n=s.useRef(!1);return(0,i.jsx)(Y,{...e,ref:a,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{e.onCloseAutoFocus?.(a),a.defaultPrevented||(r.current||t.triggerRef.current?.focus(),a.preventDefault()),r.current=!1,n.current=!1},onInteractOutside:a=>{e.onInteractOutside?.(a),a.defaultPrevented||(r.current=!0,"pointerdown"===a.detail.originalEvent.type&&(n.current=!0));let i=a.target;t.triggerRef.current?.contains(i)&&a.preventDefault(),"focusin"===a.detail.originalEvent.type&&n.current&&a.preventDefault()}})}),Y=s.forwardRef((e,a)=>{let{__scopePopover:t,trapFocus:s,onOpenAutoFocus:r,onCloseAutoFocus:n,disableOutsidePointerEvents:l,onEscapeKeyDown:o,onPointerDownOutside:c,onFocusOutside:d,onInteractOutside:m,...x}=e,u=F(G,t),p=T(t);return(0,f.Oh)(),(0,i.jsx)(N.n,{asChild:!0,loop:!0,trapped:s,onMountAutoFocus:r,onUnmountAutoFocus:n,children:(0,i.jsx)(j.qW,{asChild:!0,disableOutsidePointerEvents:l,onInteractOutside:m,onEscapeKeyDown:o,onPointerDownOutside:c,onFocusOutside:d,onDismiss:()=>u.onOpenChange(!1),children:(0,i.jsx)(C.UC,{"data-state":K(u.open),role:"dialog",id:u.contentId,...p,...x,ref:a,style:{...x.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),X="PopoverClose";function K(e){return e?"open":"closed"}s.forwardRef((e,a)=>{let{__scopePopover:t,...s}=e,r=F(X,t);return(0,i.jsx)(A.sG.button,{type:"button",...s,ref:a,onClick:(0,b.m)(e.onClick,()=>r.onOpenChange(!1))})}).displayName=X,s.forwardRef((e,a)=>{let{__scopePopover:t,...s}=e,r=T(t);return(0,i.jsx)(C.i3,{...r,...s,ref:a})}).displayName="PopoverArrow";var Q=t(4780);let ee=s.forwardRef(({className:e,align:a="center",sideOffset:t=4,...s},r)=>(0,i.jsx)(U,{children:(0,i.jsx)(J,{ref:r,align:a,sideOffset:t,className:(0,Q.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...s})}));ee.displayName=J.displayName;var ea=t(62688);let et=(0,ea.A)("arrow-up-down",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]]),ei=(0,ea.A)("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]),es=(0,ea.A)("arrow-down",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);var er=t(11860);let en=(0,ea.A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]),el=(0,ea.A)("chevrons-left",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]]),eo=(0,ea.A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),ec=(0,ea.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),ed=(0,ea.A)("chevrons-right",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]]);function em({data:e=[],columns:a=[],loading:t=!1,emptyMessage:r="Nessun dato disponibile",onFilteredDataChange:o,renderRow:c,className:d,pagination:x=!0,defaultRowsPerPage:p=25}){let[b,g]=(0,s.useState)({key:null,direction:null}),[v,j]=(0,s.useState)({}),[f,N]=(0,s.useState)({}),[y,C]=(0,s.useState)(0),[w,_]=(0,s.useState)(p),A=a=>[...new Set(e.map(e=>e[a]).filter(Boolean))].sort(),z=(0,s.useMemo)(()=>{let a=[...e];return Object.entries(v).forEach(([e,t])=>{!t.value||Array.isArray(t.value)&&0===t.value.length||"string"==typeof t.value&&""===t.value.trim()||(a=a.filter(a=>{let i=a[e];if("select"===t.type)return(Array.isArray(t.value)?t.value:[t.value]).includes(i);if("text"===t.type){let e=t.value.toLowerCase(),a=String(i||"").toLowerCase();return"equals"===t.operator?a===e:a.includes(e)}if("number"===t.type){let e=parseFloat(i),a=parseFloat(t.value);if(isNaN(e)||isNaN(a))return!1;switch(t.operator){case"equals":default:return e===a;case"gt":return e>a;case"lt":return e<a;case"gte":return e>=a;case"lte":return e<=a}}return!0}))}),b.key&&b.direction&&a.sort((e,a)=>{let t=e[b.key],i=a[b.key];if(null==t&&null==i)return 0;if(null==t)return"asc"===b.direction?-1:1;if(null==i)return"asc"===b.direction?1:-1;let s=parseFloat(t),r=parseFloat(i),n=!isNaN(s)&&!isNaN(r),l=0;return l=n?s-r:String(t).localeCompare(String(i)),"asc"===b.direction?l:-l}),a},[e,v,b]),k=(0,s.useMemo)(()=>{if(!x)return z;let e=y*w,a=e+w;return z.slice(e,a)},[z,y,w,x]),S=Math.ceil(z.length/w),E=y*w+1,I=Math.min((y+1)*w,z.length),O=e=>{let t=a.find(a=>a.field===e);t?.disableSort||g(a=>{if(a.key===e){if("asc"===a.direction)return{key:e,direction:"desc"};if("desc"===a.direction)return{key:null,direction:null}}return{key:e,direction:"asc"}})},$=(e,a)=>{j(t=>({...t,[e]:{...t[e],...a}}))},T=e=>{j(a=>{let t={...a};return delete t[e],t})},M=e=>b.key!==e?(0,i.jsx)(et,{className:"h-3 w-3"}):"asc"===b.direction?(0,i.jsx)(ei,{className:"h-3 w-3"}):"desc"===b.direction?(0,i.jsx)(es,{className:"h-3 w-3"}):(0,i.jsx)(et,{className:"h-3 w-3"}),F=Object.keys(v).length>0;return t?(0,i.jsx)(n.Zp,{className:d,children:(0,i.jsx)(n.Wu,{className:"p-6",children:(0,i.jsx)("div",{className:"text-center",children:"Caricamento..."})})}):(0,i.jsxs)("div",{className:d,children:[F&&(0,i.jsxs)("div",{className:"mb-4 flex flex-wrap gap-2 items-center",children:[(0,i.jsx)("span",{className:"text-sm text-muted-foreground",children:"Filtri attivi:"}),Object.entries(v).map(([e,t])=>{let s=a.find(a=>a.field===e);if(!s)return null;let r=Array.isArray(t.value)?t.value.join(", "):String(t.value);return(0,i.jsxs)(m.E,{variant:"secondary",className:"gap-1",children:[s.headerName,": ",r,(0,i.jsx)(l.$,{variant:"ghost",size:"sm",className:"h-auto p-0 hover:bg-transparent",onClick:()=>T(e),children:(0,i.jsx)(er.A,{className:"h-3 w-3"})})]},e)}),(0,i.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>{j({})},className:"h-6 px-2 text-xs",children:"Pulisci tutti"})]}),(0,i.jsx)(n.Zp,{children:(0,i.jsx)(n.Wu,{className:"p-0",children:(0,i.jsxs)(u.XI,{children:[(0,i.jsx)(u.A0,{children:(0,i.jsx)(u.Hj,{className:"bg-mariner-50 hover:bg-mariner-50",children:a.map(a=>(0,i.jsx)(u.nd,{className:(0,Q.cn)("font-semibold text-mariner-900 border-b border-mariner-200","center"===a.align&&"text-center","right"===a.align&&"text-right"),style:{width:a.width,...a.headerStyle},children:a.renderHeader?a.renderHeader():(0,i.jsxs)("div",{className:"relative group",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,i.jsx)("span",{className:"truncate",children:a.headerName}),(0,i.jsxs)("div",{className:"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:[!a.disableSort&&(0,i.jsx)(l.$,{variant:"ghost",size:"sm",className:"h-4 w-4 p-0 hover:bg-mariner-100",onClick:()=>O(a.field),children:M(a.field)}),!a.disableFilter&&(0,i.jsxs)(L,{open:f[a.field],onOpenChange:e=>N(t=>({...t,[a.field]:e})),children:[(0,i.jsx)(B,{asChild:!0,children:(0,i.jsx)(l.$,{variant:"ghost",size:"sm",className:(0,Q.cn)("h-4 w-4 p-0 hover:bg-mariner-100",v[a.field]&&"text-mariner-600 opacity-100"),children:(0,i.jsx)(en,{className:"h-2.5 w-2.5"})})}),(0,i.jsx)(ee,{className:"w-64",align:"start",children:(0,i.jsx)(ex,{column:a,data:e,currentFilter:v[a.field],onFilterChange:e=>$(a.field,e),onClearFilter:()=>T(a.field),getUniqueValues:()=>A(a.field)})})]})]})]}),v[a.field]&&(0,i.jsx)("div",{className:"absolute -top-1 -right-1 h-2 w-2 bg-mariner-600 rounded-full"})]})},a.field))})}),(0,i.jsx)(u.BF,{children:k.length>0?k.map((e,t)=>c?c(e,y*w+t):(0,i.jsx)(u.Hj,{className:"hover:bg-mariner-50 border-b border-mariner-100",children:a.map(a=>(0,i.jsx)(u.nA,{className:(0,Q.cn)("py-2 px-4","center"===a.align&&"text-center","right"===a.align&&"text-right"),style:a.cellStyle,children:a.renderCell?a.renderCell(e):e[a.field]},a.field))},t)):(0,i.jsx)(u.Hj,{children:(0,i.jsx)(u.nA,{colSpan:a.length,className:"text-center py-8 text-muted-foreground",children:r})})})]})})}),x&&z.length>0&&(0,i.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("span",{className:"text-sm text-muted-foreground",children:"Righe per pagina:"}),(0,i.jsxs)(h.l6,{value:w.toString(),onValueChange:e=>{_(Number(e)),C(0)},children:[(0,i.jsx)(h.bq,{className:"w-20",children:(0,i.jsx)(h.yv,{})}),(0,i.jsxs)(h.gC,{children:[(0,i.jsx)(h.eb,{value:"10",children:"10"}),(0,i.jsx)(h.eb,{value:"25",children:"25"}),(0,i.jsx)(h.eb,{value:"50",children:"50"}),(0,i.jsx)(h.eb,{value:"100",children:"100"}),(0,i.jsx)(h.eb,{value:z.length.toString(),children:"Tutto"})]})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("span",{className:"text-sm text-muted-foreground",children:z.length>0?`${E}-${I} di ${z.length}`:"0 di 0"}),(0,i.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,i.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>C(0),disabled:0===y,className:"h-8 w-8 p-0",children:(0,i.jsx)(el,{className:"h-4 w-4"})}),(0,i.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>C(e=>Math.max(0,e-1)),disabled:0===y,className:"h-8 w-8 p-0",children:(0,i.jsx)(eo,{className:"h-4 w-4"})}),(0,i.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>C(e=>Math.min(S-1,e+1)),disabled:y>=S-1,className:"h-8 w-8 p-0",children:(0,i.jsx)(ec,{className:"h-4 w-4"})}),(0,i.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>C(S-1),disabled:y>=S-1,className:"h-8 w-8 p-0",children:(0,i.jsx)(ed,{className:"h-4 w-4"})})]})]})]})]})}function ex({column:e,currentFilter:a,onFilterChange:t,onClearFilter:r,getUniqueValues:n}){let[o,c]=(0,s.useState)(a?.value||""),[d,m]=(0,s.useState)(a?.operator||"contains"),u=n(),b="number"!==e.dataType&&u.length<=20,g="number"===e.dataType,v=()=>{b?t({type:"select",value:Array.isArray(o)?o:[o]}):g?t({type:"number",value:o,operator:d}):t({type:"text",value:o,operator:d})};return(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"font-medium text-sm",children:["Filtra ",e.headerName]}),b?(0,i.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:u.map(e=>(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(x.S,{id:`filter-${e}`,checked:Array.isArray(o)?o.includes(e):o===e,onCheckedChange:a=>{Array.isArray(o)?c(a?[...o,e]:o.filter(a=>a!==e)):c(a?[e]:[])}}),(0,i.jsx)("label",{htmlFor:`filter-${e}`,className:"text-sm",children:e})]},e))}):(0,i.jsxs)("div",{className:"space-y-2",children:[g&&(0,i.jsxs)(h.l6,{value:d,onValueChange:m,children:[(0,i.jsx)(h.bq,{children:(0,i.jsx)(h.yv,{})}),(0,i.jsxs)(h.gC,{children:[(0,i.jsx)(h.eb,{value:"equals",children:"Uguale a"}),(0,i.jsx)(h.eb,{value:"gt",children:"Maggiore di"}),(0,i.jsx)(h.eb,{value:"lt",children:"Minore di"}),(0,i.jsx)(h.eb,{value:"gte",children:"Maggiore o uguale"}),(0,i.jsx)(h.eb,{value:"lte",children:"Minore o uguale"})]})]}),!g&&(0,i.jsxs)(h.l6,{value:d,onValueChange:m,children:[(0,i.jsx)(h.bq,{children:(0,i.jsx)(h.yv,{})}),(0,i.jsxs)(h.gC,{children:[(0,i.jsx)(h.eb,{value:"contains",children:"Contiene"}),(0,i.jsx)(h.eb,{value:"equals",children:"Uguale a"})]})]}),(0,i.jsx)(p.p,{placeholder:`Cerca ${e.headerName.toLowerCase()}...`,value:o,onChange:e=>c(e.target.value),onKeyDown:e=>"Enter"===e.key&&v()})]}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsx)(l.$,{size:"sm",onClick:v,children:"Applica"}),(0,i.jsx)(l.$,{size:"sm",variant:"outline",onClick:r,children:"Pulisci"})]})]})}var eu=t(99270);let ep=(0,ea.A)("square-check-big",[["path",{d:"M21 10.656V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.344",key:"2acyp4"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),eh=(0,ea.A)("square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);function eb({cavi:e=[],onFilteredDataChange:a,loading:t=!1,selectionEnabled:r=!1,onSelectionToggle:o}){let[c,d]=(0,s.useState)(""),[m,x]=(0,s.useState)("contains"),u=e=>e?e.toString().toLowerCase().trim():"",b=e=>{let a=e.match(/^([A-Z]+)(\d+)([A-Z]*)$/);return a?{prefix:a[1],number:a[2],suffix:a[3]||""}:{prefix:"",number:e,suffix:""}},g=(0,s.useCallback)((e,a,t)=>{let i=u(a);if(!i)return!0;let s=u(e.id_cavo),{prefix:r,number:n,suffix:l}=b(e.id_cavo||""),o=u(e.tipologia),c=u(e.formazione||e.sezione),d=u(e.utility),m=u(e.sistema),x=u(e.da||e.ubicazione_partenza),p=u(e.a||e.ubicazione_arrivo),h=u(e.utenza_partenza),g=u(e.utenza_arrivo),v=[s,r,n,l,o,c,d,m,x,p,h,g,u(e.id_bobina),"BOBINA_VUOTA"===e.id_bobina?"bobina vuota":null===e.id_bobina?"":u(e.id_bobina)],j=[{value:e.metri_teorici,name:"metri_teorici"},{value:e.metratura_reale||e.metri_posati,name:"metratura_reale"},{value:parseFloat(c),name:"formazione"}],f=i.match(/^([><=]+)(\d+(?:\.\d+)?)$/);if(f){let e=f[1],a=parseFloat(f[2]);return j.some(t=>{if(null==t.value||isNaN(t.value))return!1;switch(e){case">":return t.value>a;case">=":return t.value>=a;case"<":return t.value<a;case"<=":return t.value<=a;case"=":return t.value===a;default:return!1}})}let N=parseFloat(i);return!!(!isNaN(N)&&j.some(e=>null!=e.value&&!isNaN(e.value)&&e.value===N))||(t?v.some(e=>e===i):v.some(e=>e.includes(i)))},[]);(0,s.useCallback)(()=>{if(!c.trim())return void a?.(e);let t=c.split(",").map(e=>e.trim()).filter(e=>e.length>0),i=[];i="equals"===m?1===t.length?e.filter(e=>g(e,t[0],!0)):e.filter(e=>t.every(a=>g(e,a,!0))):e.filter(e=>t.some(a=>g(e,a,!1))),a?.(i)},[c,m,e,a,g]);let v=e=>{d(e)},j=()=>{d(""),x("contains")};return(0,i.jsx)(n.Zp,{className:"mb-1",children:(0,i.jsxs)(n.Wu,{className:"p-1",children:[(0,i.jsxs)("div",{className:"flex items-center gap-1",children:[(0,i.jsxs)("div",{className:"flex-1 relative",children:[(0,i.jsx)(eu.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,i.jsx)(p.p,{placeholder:"Ricerca intelligente cavi...",value:c,onChange:e=>v(e.target.value),disabled:t,className:"pl-10 pr-10 h-8"}),c&&(0,i.jsx)(l.$,{variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-5 w-5 p-0",onClick:j,children:(0,i.jsx)(er.A,{className:"h-2.5 w-2.5"})})]}),(0,i.jsx)("div",{className:"w-32",children:(0,i.jsxs)(h.l6,{value:m,onValueChange:e=>x(e),children:[(0,i.jsx)(h.bq,{className:"h-8",children:(0,i.jsx)(h.yv,{})}),(0,i.jsxs)(h.gC,{children:[(0,i.jsx)(h.eb,{value:"contains",children:"Contiene"}),(0,i.jsx)(h.eb,{value:"equals",children:"Uguale a"})]})]})}),c&&(0,i.jsx)(l.$,{variant:"outline",size:"sm",onClick:j,disabled:t,className:"transition-all duration-200 hover:scale-105",children:"Pulisci"}),o&&(0,i.jsxs)(l.$,{variant:r?"default":"outline",size:"sm",onClick:o,className:"flex items-center gap-2 transition-all duration-200 hover:scale-105",children:[r?(0,i.jsx)(ep,{className:"h-4 w-4"}):(0,i.jsx)(eh,{className:"h-4 w-4"}),r?"Disabilita Selezione":"Abilita Selezione"]})]}),c&&(0,i.jsx)("div",{className:"mt-0.5 text-xs text-muted-foreground",children:(0,i.jsxs)("div",{className:"flex flex-wrap gap-1",children:[(0,i.jsx)("span",{children:"\uD83D\uDCA1"}),(0,i.jsx)("span",{children:"• Virgole per multipli"}),(0,i.jsx)("span",{children:"• >100, <=50 per numeri"})]})})]})})}function eg({text:e,maxLength:a=20,className:t=""}){let[r,n]=(0,s.useState)(!1),[l,o]=(0,s.useState)({x:0,y:0});if(!e)return(0,i.jsx)("span",{className:"text-gray-400",children:"-"});let c=e.length>a,d=c?`${e.substring(0,a)}...`:e;return c?(0,i.jsxs)("div",{className:"relative inline-block",children:[(0,i.jsx)("span",{className:`cursor-help ${t}`,style:{textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden",maxWidth:"100%",display:"inline-block"},onMouseEnter:e=>{o({x:e.clientX,y:e.clientY}),n(!0)},onMouseMove:e=>{o({x:e.clientX,y:e.clientY})},onMouseLeave:()=>n(!1),title:e,children:d}),r&&(0,i.jsxs)("div",{className:"fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg pointer-events-none",style:{top:l.y-40,left:l.x-150,maxWidth:"300px",wordWrap:"break-word",whiteSpace:"normal"},children:[e,(0,i.jsx)("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0",style:{borderLeft:"5px solid transparent",borderRight:"5px solid transparent",borderTop:"5px solid #1f2937"}})]})]}):(0,i.jsx)("span",{className:t,children:e})}function ev({cavi:e=[],loading:a=!1,selectionEnabled:t=!1,selectedCavi:r=[],onSelectionChange:n,onStatusAction:o,onContextMenuAction:c}){let[d,p]=(0,s.useState)(e),[h,b]=(0,s.useState)(e),[g,v]=(0,s.useState)(t),j=e=>{n&&n(e?h.map(e=>e.id_cavo):[])},f=(e,a)=>{n&&n(a?[...r,e]:r.filter(a=>a!==e))},N=async()=>{try{let e=await fetch("/api/cavi/export",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({selectedIds:r,cantiereId:1})});if(e.ok){let a=await e.blob(),t=window.URL.createObjectURL(a),i=document.createElement("a");i.href=t,i.download=`cavi_export_${new Date().toISOString().split("T")[0]}.csv`,document.body.appendChild(i),i.click(),window.URL.revokeObjectURL(t),document.body.removeChild(i)}else{let a=await e.json();alert(`Errore durante l'esportazione: ${a.error}`)}}catch(e){alert("Errore durante l'esportazione")}},y=async()=>{let e=prompt("Inserisci il nuovo stato (Da installare, In corso, Installato):");if(e)try{let a=await fetch("/api/cavi/bulk-status",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({selectedIds:r,cantiereId:1,newStatus:e})}),t=await a.json();t.success?alert(t.message):alert(`Errore: ${t.error}`)}catch(e){alert("Errore durante il cambio stato")}},C=()=>{alert(`Assegnazione comanda per ${r.length} cavi`)},w=async()=>{if(confirm(`Sei sicuro di voler eliminare ${r.length} cavi?`))try{let e=await fetch("/api/cavi/bulk-delete",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({selectedIds:r,cantiereId:1})}),a=await e.json();a.success?alert(a.message):alert(`Errore: ${a.error}`)}catch(e){alert("Errore durante l'eliminazione")}},_=(0,s.useMemo)(()=>{let e=[{field:"id_cavo",headerName:"ID",dataType:"text",width:70,align:"left",renderCell:e=>(0,i.jsx)("span",{className:"font-semibold text-mariner-900",children:e.id_cavo})},{field:"sistema",headerName:"Sistema",dataType:"text",width:80,renderCell:e=>(0,i.jsx)(eg,{text:e.sistema||"",maxLength:8})},{field:"utility",headerName:"Utility",dataType:"text",width:80,renderCell:e=>(0,i.jsx)(eg,{text:e.utility||"",maxLength:8})},{field:"tipologia",headerName:"Tipologia",dataType:"text",width:100,renderCell:e=>(0,i.jsx)(eg,{text:e.tipologia||"",maxLength:12})},{field:"formazione",headerName:"Form.",dataType:"text",align:"left",width:60,renderCell:e=>e.formazione||e.sezione},{field:"metri_teorici",headerName:"M.Teor.",dataType:"number",align:"left",width:70,renderCell:e=>e.metri_teorici?e.metri_teorici.toFixed(1):"0"},{field:"metri_posati",headerName:"M.Reali",dataType:"number",align:"left",width:70,renderCell:e=>{let a=e.metri_posati||e.metratura_reale||0;return a?a.toFixed(1):"0"}},{field:"ubicazione_partenza",headerName:"Da",dataType:"text",width:140,renderCell:e=>(0,i.jsx)(eg,{text:e.da||e.ubicazione_partenza||"",maxLength:18})},{field:"ubicazione_arrivo",headerName:"A",dataType:"text",width:140,renderCell:e=>(0,i.jsx)(eg,{text:e.a||e.ubicazione_arrivo||"",maxLength:18})},{field:"id_bobina",headerName:"Bobina",dataType:"text",width:80,align:"center",renderCell:e=>{let a=e.id_bobina;if(a,!a||"N/A"===a)return(0,i.jsx)("span",{className:"text-gray-400",children:"-"});if("BOBINA_VUOTA"===a)return(0,i.jsx)(m.E,{variant:"outline",className:"text-xs px-2 py-0.5 text-orange-600 border-orange-300 bg-orange-50",children:"Vuota"});let t=a.match(/_B(.+)$/);return t||(t=a.match(/_b(.+)$/))||(t=a.match(/c\d+_[bB](\d+)$/))||(t=a.match(/(\d+)$/))?(0,i.jsx)("span",{className:"font-medium",children:t[1]}):(0,i.jsx)("span",{className:"font-medium text-xs",children:a})}},{field:"stato_installazione",headerName:"Stato",dataType:"text",align:"left",width:120,disableFilter:!0,disableSort:!0,renderCell:e=>A(e)},{field:"collegamenti",headerName:"Collegamenti",dataType:"text",align:"left",width:180,disableFilter:!0,disableSort:!0,renderCell:e=>z(e)},{field:"certificato",headerName:"Certificato",dataType:"text",align:"left",width:130,disableFilter:!0,disableSort:!0,renderCell:e=>k(e)}];return g&&e.unshift({field:"selection",headerName:"",disableFilter:!0,disableSort:!0,width:50,align:"left",renderHeader:()=>(0,i.jsx)(x.S,{checked:r.length===h.length&&h.length>0,onCheckedChange:j}),renderCell:e=>(0,i.jsx)(x.S,{checked:r.includes(e.id_cavo),onCheckedChange:a=>f(e.id_cavo,a),onClick:e=>e.stopPropagation()})}),e},[g,r,h,j,f]),A=e=>{let a=e.metri_posati||e.metratura_reale||0,t=e.comanda_posa,s=e.comanda_partenza,r=e.comanda_arrivo,n=e.comanda_certificazione,l=t||s||r||n;if(l&&"In corso"===e.stato_installazione)return(0,i.jsx)(m.E,{className:"bg-blue-600 text-white cursor-pointer hover:bg-blue-700 hover:scale-105 transition-all duration-200 hover:shadow-md px-3 py-1 font-semibold",onClick:a=>{a.stopPropagation(),o?.(e,"view_command",l)},children:l});let c=e.stato_installazione||"Da installare";return"Installato"===c||a>0?(0,i.jsx)(m.E,{className:"bg-green-100 text-green-800 cursor-pointer hover:bg-green-200 hover:text-green-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-green-300",onClick:a=>{a.stopPropagation(),o?.(e,"modify_reel")},children:"Installato"}):"In corso"===c?(0,i.jsx)(m.E,{className:"bg-yellow-100 text-yellow-800 cursor-pointer hover:bg-yellow-200 hover:text-yellow-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-yellow-300",onClick:a=>{a.stopPropagation(),o?.(e,"insert_meters")},children:"In corso"}):(0,i.jsx)(m.E,{variant:"outline",className:"text-gray-700 cursor-pointer hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium",onClick:a=>{a.stopPropagation(),o?.(e,"insert_meters")},children:"Da installare"})},z=e=>{let a,t,s,r=e.metri_posati>0||e.metratura_reale>0,n=e.collegamento||e.collegamenti||0;if(!r)return(0,i.jsx)(m.E,{variant:"outline",className:"text-gray-400 cursor-not-allowed bg-gray-50 border-gray-200 px-3 py-1",children:"✕ Non disponibile"});switch(n){case 0:a="⚪⚪ Collega",t="connect_cable",s="bg-gray-100 text-gray-700 cursor-pointer hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-gray-300";break;case 1:a="\uD83D\uDFE2⚪ Completa",t="connect_arrival",s="bg-yellow-100 text-yellow-800 cursor-pointer hover:bg-yellow-200 hover:text-yellow-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-yellow-300";break;case 2:a="⚪\uD83D\uDFE2 Completa",t="connect_departure",s="bg-yellow-100 text-yellow-800 cursor-pointer hover:bg-yellow-200 hover:text-yellow-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-yellow-300";break;case 3:a="\uD83D\uDFE2\uD83D\uDFE2 Scollega",t="disconnect_cable",s="bg-green-100 text-green-800 cursor-pointer hover:bg-green-200 hover:text-green-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-green-300";break;default:a="Gestisci",t="manage_connections",s="bg-blue-100 text-blue-800 cursor-pointer hover:bg-blue-200 hover:text-blue-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-blue-300"}return(0,i.jsx)(m.E,{className:s,onClick:a=>{a.stopPropagation(),o?.(e,t)},children:a})},k=e=>{let a=e.metri_posati>0||e.metratura_reale>0,t=!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato;return a?t?(0,i.jsx)(m.E,{className:"bg-green-600 text-white cursor-pointer hover:bg-green-700 transition-all duration-200 hover:scale-105 px-3 py-1 font-semibold",onClick:a=>{a.stopPropagation(),o?.(e,"generate_pdf")},children:"✓ Genera PDF"}):(0,i.jsx)(m.E,{variant:"outline",className:"text-purple-700 cursor-pointer hover:bg-purple-50 hover:text-purple-800 hover:border-purple-300 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border-purple-200",onClick:a=>{a.stopPropagation(),o?.(e,"create_certificate")},children:"\uD83D\uDD50 Certifica"}):(0,i.jsx)(m.E,{variant:"outline",className:"text-gray-400 cursor-not-allowed bg-gray-50 border-gray-200 px-3 py-1",children:"✕ Non disponibile"})};return(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)(eb,{cavi:e,onFilteredDataChange:e=>{p(e)},loading:a,selectionEnabled:g,onSelectionToggle:()=>{v(!g)}}),(0,i.jsx)(em,{data:d,columns:_,loading:a,emptyMessage:"Nessun cavo disponibile",onFilteredDataChange:e=>{b(e)},renderRow:(e,a)=>{let t=r.includes(e.id_cavo);return(0,i.jsx)(u.Hj,{className:`
          ${t?"bg-blue-50 border-blue-200":"bg-white"}
          hover:bg-blue-50 hover:border-blue-200 hover:shadow-sm
          cursor-pointer border-b border-gray-200
          transition-all duration-200 ease-in-out
          ${t?"ring-1 ring-blue-300":""}
        `,onClick:()=>g&&f(e.id_cavo,!t),onContextMenu:a=>{a.preventDefault(),c?.(e,"context_menu")},children:_.map(a=>(0,i.jsx)(u.nA,{className:`
              py-2 px-2 text-sm text-left
              ${t?"text-blue-900":"text-gray-900"}
              transition-colors duration-200
            `,style:{width:a.width,...a.cellStyle},onClick:e=>{["stato_installazione","collegamenti","certificato"].includes(a.field)&&e.stopPropagation()},children:a.renderCell?a.renderCell(e):e[a.field]||(0,i.jsx)("span",{className:"text-gray-400",children:"-"})},a.field))},e.id_cavo)}}),g&&r.length>0&&(0,i.jsx)("div",{className:"sticky bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-10",children:(0,i.jsxs)("div",{className:"flex items-center justify-between p-3",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsxs)(m.E,{variant:"secondary",className:"bg-mariner-100 text-mariner-800",children:[r.length," cavi selezionati"]}),(0,i.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>j(!1),className:"text-xs",children:"Deseleziona tutto"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>N(),className:"flex items-center space-x-1",children:[(0,i.jsx)("span",{children:"\uD83D\uDCCA"}),(0,i.jsx)("span",{children:"Esporta"})]}),(0,i.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>y(),className:"flex items-center space-x-1",children:[(0,i.jsx)("span",{children:"\uD83D\uDD04"}),(0,i.jsx)("span",{children:"Cambia Stato"})]}),(0,i.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>C(),className:"flex items-center space-x-1",children:[(0,i.jsx)("span",{children:"\uD83D\uDCCB"}),(0,i.jsx)("span",{children:"Assegna Comanda"})]}),(0,i.jsxs)(l.$,{variant:"destructive",size:"sm",onClick:()=>w(),className:"flex items-center space-x-1",children:[(0,i.jsx)("span",{children:"\uD83D\uDDD1️"}),(0,i.jsx)("span",{children:"Elimina"})]})]})]})})]})}var ej=t(53411),ef=t(23361),eN=t(5336),ey=t(48730),eC=t(43649),ew=t(45583),e_=t(19080);function eA({cavi:e,filteredCavi:a,className:t,revisioneCorrente:r}){let l=(0,s.useMemo)(()=>{let t=e.length,i=a.length,s=a.filter(e=>"Installato"===e.stato_installazione||e.metri_posati&&e.metri_posati>0||e.metratura_reale&&e.metratura_reale>0).length,r=a.filter(e=>"In corso"===e.stato_installazione).length,n=a.filter(e=>3===(e.collegamento||e.collegamenti||0)).length,l=a.filter(e=>{let a=e.collegamento||e.collegamenti||0;return 1===a||2===a}).length,o=a.filter(e=>0===(e.collegamento||e.collegamenti||0)&&(e.metri_posati>0||e.metratura_reale>0)).length,c=a.filter(e=>!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato).length,d=a.reduce((e,a)=>e+(a.metri_teorici||0),0),m=a.reduce((e,a)=>e+(a.metri_posati||a.metratura_reale||0),0),x=0===i?0:Math.round(100*(((s-n)*2+(n-c)*3.5+4*c)/(4*i)*100))/100;return{totalCavi:t,filteredCount:i,installati:s,inCorso:r,daInstallare:i-s-r,collegati:n,parzialmenteCollegati:l,nonCollegati:o,certificati:c,metriTotali:d,metriInstallati:m,percentualeInstallazione:x}},[e,a]);return(0,i.jsx)(n.Zp,{className:t,children:(0,i.jsxs)(n.Wu,{className:"p-1.5",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-1.5",children:[(0,i.jsx)(ej.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,i.jsx)("span",{className:"text-xs font-semibold text-mariner-900",children:"Statistiche Cavi"})]}),r&&(0,i.jsxs)(m.E,{variant:"outline",className:"text-xs font-medium py-0 px-1.5 h-5",children:["Rev. ",r]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-1.5 bg-mariner-50 px-1.5 py-1 rounded-lg",children:[(0,i.jsx)(ef.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-bold text-mariner-900 text-sm",children:l.filteredCount}),(0,i.jsxs)("div",{className:"text-xs text-mariner-600",children:["di ",l.totalCavi," cavi"]})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-1.5 bg-green-50 px-1.5 py-1 rounded-lg",children:[(0,i.jsx)(eN.A,{className:"h-3.5 w-3.5 text-green-600"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-bold text-green-700 text-sm",children:l.installati}),(0,i.jsx)("div",{className:"text-xs text-green-600",children:"installati"})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-1.5 bg-yellow-50 px-1.5 py-1 rounded-lg",children:[(0,i.jsx)(ey.A,{className:"h-3.5 w-3.5 text-yellow-600"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-bold text-yellow-700 text-sm",children:l.inCorso}),(0,i.jsx)("div",{className:"text-xs text-yellow-600",children:"in corso"})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-1.5 bg-gray-50 px-1.5 py-1 rounded-lg",children:[(0,i.jsx)(eC.A,{className:"h-3.5 w-3.5 text-gray-600"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-bold text-gray-700 text-sm",children:l.daInstallare}),(0,i.jsx)("div",{className:"text-xs text-gray-600",children:"da installare"})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-1.5 bg-blue-50 px-1.5 py-1 rounded-lg",children:[(0,i.jsx)(ew.A,{className:"h-3.5 w-3.5 text-blue-600"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-bold text-blue-700 text-sm",children:l.collegati}),(0,i.jsx)("div",{className:"text-xs text-blue-600",children:"collegati"})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-1.5 bg-purple-50 px-1.5 py-1 rounded-lg",children:[(0,i.jsx)(e_.A,{className:"h-3.5 w-3.5 text-purple-600"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-bold text-purple-700 text-sm",children:l.certificati}),(0,i.jsx)("div",{className:"text-xs text-purple-600",children:"certificati"})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-1.5 bg-indigo-50 px-1.5 py-1 rounded-lg",children:[(0,i.jsx)("div",{className:"h-3.5 w-3.5 flex items-center justify-center",children:(0,i.jsx)("div",{className:"h-2 w-2 bg-indigo-600 rounded-full"})}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{className:"font-bold text-indigo-700 text-sm",children:[l.metriInstallati.toLocaleString(),"m"]}),(0,i.jsxs)("div",{className:"text-xs text-indigo-600",children:["di ",l.metriTotali.toLocaleString(),"m"]})]})]})]}),l.filteredCount>0&&(0,i.jsxs)("div",{className:"mt-2 bg-gray-50 p-2 rounded-lg",children:[(0,i.jsxs)("div",{className:"flex justify-between text-xs font-medium text-gray-700 mb-1",children:[(0,i.jsx)("span",{children:"IAP - Indice Avanzamento Ponderato"}),(0,i.jsxs)("span",{className:`font-bold ${l.percentualeInstallazione>=80?"text-emerald-700":l.percentualeInstallazione>=50?"text-yellow-700":l.percentualeInstallazione>=25?"text-orange-700":"text-amber-700"}`,children:[l.percentualeInstallazione.toFixed(1),"%"]})]}),(0,i.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,i.jsx)("div",{className:`h-2 rounded-full transition-all duration-500 ease-in-out ${l.percentualeInstallazione>=80?"bg-gradient-to-r from-emerald-500 to-emerald-600":l.percentualeInstallazione>=50?"bg-gradient-to-r from-yellow-500 to-yellow-600":l.percentualeInstallazione>=25?"bg-gradient-to-r from-orange-500 to-orange-600":"bg-gradient-to-r from-amber-500 to-amber-600"}`,style:{width:`${Math.min(l.percentualeInstallazione,100)}%`}})}),(0,i.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-0.5",children:[(0,i.jsx)("span",{children:"Pesi: Posa(2.0) + Collegamento(1.5) + Certificazione(0.5)"}),(0,i.jsxs)("span",{children:[l.installati,"I + ",l.collegati,"C + ",l.certificati,"Cert"]})]})]})]})})}var ez=t(63503),ek=t(80013);let eS=(0,ea.A)("calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]);var eE=t(41862),eI=t(93613);function eO({open:e,onClose:a,cavo:t,cantiere:r,onSuccess:n,onError:x}){let{cantiere:u}=(0,c.A)(),h=r||u,[b,g]=(0,s.useState)("assegna_nuova"),[v,j]=(0,s.useState)(""),[f,N]=(0,s.useState)([]),[y,C]=(0,s.useState)(!1),[w,_]=(0,s.useState)(!1),[A,z]=(0,s.useState)(""),[k,S]=(0,s.useState)(""),[E,I]=(0,s.useState)("compatibili"),O=e=>{if(!e||"BOBINA_VUOTA"===e)return"VUOTA";if(e&&e.includes("_B"))return e.split("_B")[1];let a=f.find(a=>a.id_bobina===e);return a&&a.numero_bobina||e},$=(()=>{if(!t)return[];let e=f.filter(e=>{let a=e.tipologia===t.tipologia&&e.formazione===t.sezione,i=""===k||e.id_bobina.toLowerCase().includes(k.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(k.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(k.toLowerCase());return a&&i&&e.metri_residui>0});return console.log("\uD83D\uDD0D ModificaBobinaDialog: Filtro compatibili:",{cavoTipologia:t.tipologia,cavoSezione:t.sezione,totaleBobine:f.length,bobineCompatibili:e.length,searchText:k}),e})(),T=t?f.filter(e=>{let a=e.tipologia!==t.tipologia||e.formazione!==t.sezione,i=""===k||e.id_bobina.toLowerCase().includes(k.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(k.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(k.toLowerCase());return a&&i&&e.metri_residui>0}):[],M=()=>{g("assegna_nuova"),j(""),S(""),z(""),a()},F=async()=>{if(console.log("\uD83D\uDD04 ModificaBobinaDialog: Salvataggio:",{selectedOption:b,selectedBobina:v,cavoId:t?.id_cavo,cantiereId:h?.id_cantiere}),t)try{if(_(!0),z(""),"assegna_nuova"===b){if(!v)return void x("Selezionare una bobina");let e=await d.At.updateMetriPosati(h?.id_cantiere,t.id_cavo,t.metratura_reale||0,v,!0);console.log("✅ ModificaBobinaDialog: Bobina aggiornata:",e),n(`Bobina aggiornata con successo per il cavo ${t.id_cavo}`),M()}else if("rimuovi_bobina"===b){let e=await d.At.updateMetriPosati(h?.id_cantiere,t.id_cavo,t.metratura_reale||0,"BOBINA_VUOTA",!0);console.log("✅ ModificaBobinaDialog: Bobina rimossa:",e),n(`Bobina rimossa con successo dal cavo ${t.id_cavo}`),M()}}catch(e){console.error("❌ ModificaBobinaDialog: Errore salvataggio:",e),x(e.response?.data?.detail||e.message||"Errore durante la modifica della bobina")}finally{_(!1)}};return t?(0,i.jsx)(i.Fragment,{children:(0,i.jsx)(ez.lG,{open:e,onOpenChange:M,children:(0,i.jsxs)(ez.Cf,{className:"max-w-4xl max-h-[90vh] flex flex-col",children:[(0,i.jsx)(ez.c7,{children:(0,i.jsxs)(ez.L3,{children:["Modifica Bobina Cavo ",t.id_cavo]})}),(0,i.jsxs)("div",{className:"flex-1 overflow-hidden space-y-6",children:[(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(e_.A,{className:"h-5 w-5 text-blue-600"}),(0,i.jsx)("h3",{className:"font-medium text-gray-900",children:"Cavo Selezionato"})]}),(0,i.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-200",children:(0,i.jsxs)("div",{className:"text-sm font-medium text-blue-800",children:["Informazioni Cavo / Tipologia: ",t.tipologia||"N/A"," / Da: ",t.ubicazione_partenza||"N/A"," / Formazione: ",t.sezione||"N/A"," / A: ",t.ubicazione_arrivo||"N/A"," / Metri Posati: ",t.metratura_reale||0," m"]})})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)("h3",{className:"font-medium",children:"Opzioni di modifica"}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,i.jsx)("input",{type:"radio",name:"operazione",value:"assegna_nuova",checked:"assegna_nuova"===b,onChange:e=>g(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,i.jsx)("span",{className:"text-sm",children:"Cambia bobina"})]}),(0,i.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,i.jsx)("input",{type:"radio",name:"operazione",value:"rimuovi_bobina",checked:"rimuovi_bobina"===b,onChange:e=>g(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,i.jsx)("span",{className:"text-sm",children:"Bobina vuota"})]})]})]}),"assegna_nuova"===b&&(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)("h3",{className:"font-medium",children:"Seleziona bobina"}),(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)(eu.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,i.jsx)(p.p,{placeholder:"Cerca bobina per ID, tipologia o numero...",value:k,onChange:e=>S(e.target.value),className:"pl-10"})]}),(0,i.jsxs)("div",{className:"flex space-x-1 border-b",children:[(0,i.jsx)("button",{onClick:()=>I("compatibili"),className:`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${"compatibili"===E?"border-green-500 text-green-600 bg-green-50":"border-transparent text-gray-500 hover:text-gray-700"}`,children:(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(eN.A,{className:"h-4 w-4"}),(0,i.jsxs)("span",{children:["Bobine Compatibili (",$.length,")"]})]})}),(0,i.jsx)("button",{onClick:()=>I("incompatibili"),className:`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${"incompatibili"===E?"border-amber-500 text-amber-600 bg-amber-50":"border-transparent text-gray-500 hover:text-gray-700"}`,children:(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(eC.A,{className:"h-4 w-4"}),(0,i.jsxs)("span",{children:["Bobine Incompatibili (",T.length,")"]})]})})]}),(0,i.jsx)("div",{className:"max-h-64 overflow-y-auto border rounded-lg",children:y?(0,i.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,i.jsx)(eE.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,i.jsx)("span",{children:"Caricamento bobine..."})]}):(0,i.jsx)("div",{className:"divide-y",children:"compatibili"===E?0===$.length?(0,i.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina compatibile trovata"}):$.map(e=>(0,i.jsx)("div",{className:`p-3 cursor-pointer transition-all ${v===e.id_bobina?"bg-green-100 border-l-4 border-green-500":"hover:bg-green-50"}`,onClick:()=>j(e.id_bobina),children:(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[v===e.id_bobina&&(0,i.jsx)(eN.A,{className:"h-5 w-5 text-green-600"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium text-sm",children:O(e.id_bobina)}),(0,i.jsxs)("div",{className:"text-xs text-gray-500",children:[e.tipologia," - ",e.formazione]})]})]}),(0,i.jsxs)(m.E,{variant:"outline",className:"bg-green-100 text-green-800 border-green-300",children:[e.metri_residui,"m"]})]})},e.id_bobina)):0===T.length?(0,i.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina incompatibile trovata"}):T.map(e=>(0,i.jsx)("div",{className:`p-3 cursor-pointer transition-all ${v===e.id_bobina?"bg-amber-100 border-l-4 border-amber-500":"hover:bg-amber-50"}`,onClick:()=>j(e.id_bobina),children:(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[v===e.id_bobina&&(0,i.jsx)(eN.A,{className:"h-5 w-5 text-amber-600"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium text-sm",children:O(e.id_bobina)}),(0,i.jsxs)("div",{className:"text-xs text-gray-500",children:[e.tipologia," - ",e.formazione]})]})]}),(0,i.jsxs)(m.E,{variant:"outline",className:"bg-amber-100 text-amber-800 border-amber-300",children:[e.metri_residui,"m"]})]})},e.id_bobina))})})]}),A&&(0,i.jsxs)(o.Fc,{variant:"destructive",children:[(0,i.jsx)(eI.A,{className:"h-4 w-4"}),(0,i.jsx)(o.TN,{children:A})]})]}),(0,i.jsxs)(ez.Es,{children:[(0,i.jsx)(l.$,{variant:"outline",onClick:M,disabled:w,children:"Annulla"}),(0,i.jsxs)(l.$,{onClick:F,disabled:w,children:[w&&(0,i.jsx)(eE.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salva"]})]})]})})}):null}function e$({open:e,onClose:a,cavo:t,cantiere:r,onSuccess:n,onError:x}){let{cantiere:u}=(0,c.A)(),h=r||u,[b,g]=(0,s.useState)({metri_posati:"",id_bobina:""}),[v,j]=(0,s.useState)({}),[f,N]=(0,s.useState)({}),[y,C]=(0,s.useState)(!1),[w,_]=(0,s.useState)([]),[A,z]=(0,s.useState)(!1),[k,S]=(0,s.useState)(""),[E,I]=(0,s.useState)(!1),O=e=>{if(!e||"BOBINA_VUOTA"===e)return"VUOTA";if(e&&e.includes("_B"))return e.split("_B")[1];let a=w.find(a=>a.id_bobina===e);return a&&a.numero_bobina||e};w.filter(e=>{if(!k)return!0;let a=k.toLowerCase();return e.id_bobina.toLowerCase().includes(a)||e.tipologia.toLowerCase().includes(a)||e.formazione.toLowerCase().includes(a)||O(e.id_bobina).toLowerCase().includes(a)});let $=F(),T=L(),M=e=>{console.log("\uD83C\uDFAF Bobina selezionata:",{id:e.id_bobina,numero:O(e.id_bobina),tipologia:e.tipologia,formazione:e.formazione,metri_residui:e.metri_residui}),g(a=>({...a,id_bobina:e.id_bobina})),j(e=>({...e,id_bobina:void 0}))},F=()=>t?w.filter(e=>{let a=e.tipologia===t.tipologia&&e.sezione===t.sezione,i=""===k||e.id_bobina.toLowerCase().includes(k.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(k.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(k.toLowerCase());return a&&i&&e.metri_residui>0}):[],L=()=>t?w.filter(e=>{let a=e.tipologia!==t.tipologia||e.sezione!==t.sezione,i=""===k||e.id_bobina.toLowerCase().includes(k.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(k.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(k.toLowerCase());return a&&i&&e.metri_residui>0}):[],D=async()=>{if(console.log({cavo:t?.id_cavo,metri_posati:b.metri_posati,id_bobina:b.id_bobina}),!t)return;if(!b.metri_posati||0>parseFloat(b.metri_posati))return void x("Inserire metri posati validi (≥ 0)");if(!b.id_bobina)return void x("Selezionare una bobina o BOBINA VUOTA");let e=parseFloat(b.metri_posati);if("BOBINA_VUOTA"!==b.id_bobina){let e=w.find(e=>e.id_bobina===b.id_bobina);e&&e.metri_residui}try{if(C(!0),!h)throw Error("Cantiere non selezionato");console.log({cantiere:h.id_cantiere,cavo:t.id_cavo,metri:e,bobina:b.id_bobina,isBobinaVuota:"BOBINA_VUOTA"===b.id_bobina}),await d.At.updateMetriPosati(h.id_cantiere,t.id_cavo,e,b.id_bobina,!0),n(`Metri posati aggiornati con successo per il cavo ${t.id_cavo}: ${e}m`),a()}catch(e){x(e.response?.data?.detail||e.message||"Errore durante il salvataggio dei metri posati")}finally{C(!1)}},P=()=>{y||(g({metri_posati:"",id_bobina:""}),j({}),N({}),S(""),a())};return t?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(ez.lG,{open:e,onOpenChange:P,children:(0,i.jsxs)(ez.Cf,{className:"max-w-7xl h-[90vh] flex flex-col",children:[(0,i.jsxs)(ez.c7,{className:"flex-shrink-0",children:[(0,i.jsxs)(ez.L3,{className:"flex items-center gap-2",children:[(0,i.jsx)(eS,{className:"h-5 w-5"}),"Inserisci Metri Posati - ",t.id_cavo]}),(0,i.jsx)(ez.rr,{children:"Inserisci i metri effettivamente posati per il cavo e seleziona una bobina o usa BOBINA VUOTA"})]}),(0,i.jsxs)("div",{className:"flex-1 overflow-y-auto space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-4",children:[(0,i.jsx)("div",{className:"lg:col-span-2",children:(0,i.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-200",children:[(0,i.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"Informazioni Cavo"}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Tipologia:"})," ",t.tipologia||"N/A"]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Da:"})," ",t.ubicazione_partenza||"N/A"]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Formazione:"})," ",t.sezione||"N/A"]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"A:"})," ",t.ubicazione_arrivo||"N/A"]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Metri teorici:"})," ",t.metri_teorici||"N/A"," m"]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Gi\xe0 posati:"})," ",t.metratura_reale||0," m"]})]})]})}),(0,i.jsx)("div",{className:"lg:col-span-1",children:(0,i.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-300 h-full",children:[(0,i.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"Metri da Installare"}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(ek.J,{htmlFor:"metri",className:"text-sm font-medium",children:"Metri Posati"}),(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)(p.p,{id:"metri",type:"number",value:b.metri_posati,onChange:e=>g(a=>({...a,metri_posati:e.target.value})),placeholder:"Inserisci metri posati",disabled:y,step:"0.1",min:"0",className:"text-lg font-bold text-center border-2 border-blue-400 focus:border-blue-600",autoFocus:!0}),(0,i.jsx)("span",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-lg font-bold text-blue-600",children:"m"})]}),v.metri_posati&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:v.metri_posati}),f.metri_posati&&(0,i.jsx)("p",{className:"text-sm text-amber-600",children:f.metri_posati})]})]})})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h3",{className:"font-semibold text-blue-800 text-lg",children:"Selezione Bobina"}),(0,i.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-12 gap-3 items-center",children:[(0,i.jsx)("div",{className:"sm:col-span-5",children:(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)(eu.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,i.jsx)(p.p,{placeholder:"ID, tipologia, formazione...",value:k,onChange:e=>S(e.target.value),className:"pl-10",disabled:y}),k&&(0,i.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0",onClick:()=>S(""),children:(0,i.jsx)(er.A,{className:"h-4 w-4"})})]})}),(0,i.jsx)("div",{className:"sm:col-span-7",children:(0,i.jsxs)(l.$,{type:"button",variant:"BOBINA_VUOTA"===b.id_bobina?"default":"outline",className:`w-full h-10 font-bold flex items-center justify-center gap-2 ${"BOBINA_VUOTA"===b.id_bobina?"bg-green-600 hover:bg-green-700 text-white":"border-blue-400 text-blue-700 hover:bg-blue-50"}`,onClick:()=>{console.log("\uD83C\uDFAF BOBINA VUOTA selezionata - cavo sar\xe0 posato senza bobina specifica"),g(e=>({...e,id_bobina:"BOBINA_VUOTA"})),j(e=>{let a={...e};return delete a.id_bobina,a})},disabled:y,children:["BOBINA_VUOTA"===b.id_bobina&&(0,i.jsx)(eN.A,{className:"h-5 w-5"}),"BOBINA VUOTA"]})})]}),"BOBINA_VUOTA"===b.id_bobina&&(0,i.jsx)("div",{className:"mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:(0,i.jsxs)("div",{className:"flex items-start gap-2",children:[(0,i.jsx)(eN.A,{className:"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0"}),(0,i.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,i.jsx)("p",{className:"font-medium",children:"Bobina Vuota Selezionata"}),(0,i.jsx)("p",{className:"mt-1",children:'Il cavo sar\xe0 posato senza assegnazione di bobina specifica. Potrai collegarlo a una bobina in seguito tramite la funzione "Modifica Bobina".'})]})]})})]}),A?(0,i.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,i.jsx)(eE.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,i.jsx)("span",{children:"Caricamento bobine..."})]}):(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("h4",{className:"font-medium text-green-700 mb-2 flex items-center gap-2",children:[(0,i.jsx)(eN.A,{className:"h-4 w-4"}),"Bobine Compatibili (",$.length,")"]}),(0,i.jsx)("div",{className:"max-h-72 overflow-y-auto border rounded-lg",children:0===$.length?(0,i.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina compatibile trovata"}):(0,i.jsx)("div",{className:"divide-y",children:$.map(e=>(0,i.jsx)("div",{className:`p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ${b.id_bobina===e.id_bobina?"bg-green-100 border-green-500 shadow-md":"border-gray-200 hover:bg-green-50 hover:border-green-300"}`,onClick:()=>M(e),children:(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[b.id_bobina===e.id_bobina&&(0,i.jsx)(eN.A,{className:"h-5 w-5 text-green-600 flex-shrink-0"}),(0,i.jsx)("div",{className:"font-bold text-base min-w-fit",children:O(e.id_bobina)}),(0,i.jsxs)("div",{className:"text-sm text-gray-600 truncate",children:[e.tipologia," - ",e.sezione]})]}),(0,i.jsxs)(m.E,{variant:"outline",className:"bg-green-100 text-green-800 border-green-300 font-medium min-w-fit",children:[e.metri_residui,"m"]})]})},e.id_bobina))})})]}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("h4",{className:"font-medium text-amber-700 mb-2 flex items-center gap-2",children:[(0,i.jsx)(eC.A,{className:"h-4 w-4"}),"Bobine Incompatibili (",T.length,")"]}),(0,i.jsx)("div",{className:"max-h-72 overflow-y-auto border rounded-lg",children:0===T.length?(0,i.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina incompatibile trovata"}):(0,i.jsx)("div",{className:"divide-y",children:T.map(e=>(0,i.jsx)("div",{className:`p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ${b.id_bobina===e.id_bobina?"bg-amber-100 border-amber-500 shadow-md":"border-gray-200 hover:bg-amber-50 hover:border-amber-300"}`,onClick:()=>M(e),children:(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[b.id_bobina===e.id_bobina&&(0,i.jsx)(eN.A,{className:"h-5 w-5 text-amber-600 flex-shrink-0"}),(0,i.jsx)("div",{className:"font-bold text-base min-w-fit",children:O(e.id_bobina)}),(0,i.jsxs)("div",{className:"text-sm text-gray-600 truncate",children:[e.tipologia," - ",e.sezione]})]}),(0,i.jsxs)(m.E,{variant:"outline",className:"bg-amber-100 text-amber-800 border-amber-300 font-medium min-w-fit",children:[e.metri_residui,"m"]})]})},e.id_bobina))})})]})]}),0===w.length&&!A&&(0,i.jsxs)(o.Fc,{className:"border-amber-200 bg-amber-50",children:[(0,i.jsx)(eC.A,{className:"h-4 w-4 text-amber-600"}),(0,i.jsx)(o.TN,{className:"text-amber-800",children:"Non ci sono bobine disponibili. Puoi procedere con BOBINA VUOTA o aggiungere prima una nuova bobina."})]}),v.id_bobina&&(0,i.jsxs)(o.Fc,{variant:"destructive",children:[(0,i.jsx)(eI.A,{className:"h-4 w-4"}),(0,i.jsx)(o.TN,{children:v.id_bobina})]})]})]}),(0,i.jsxs)(ez.Es,{className:"flex-shrink-0 border-t pt-4 mt-4 flex justify-between items-center",children:[(0,i.jsx)("div",{children:"installato"===t.stato_installazione&&t.id_bobina&&(0,i.jsx)(l.$,{variant:"outline",onClick:()=>{I(!0)},disabled:y,className:"text-blue-600 border-blue-300 hover:bg-blue-50",children:"Modifica Bobina"})}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsx)(l.$,{variant:"outline",onClick:P,disabled:y,children:"Annulla"}),(0,i.jsxs)(l.$,{onClick:D,disabled:y||!b.metri_posati||0>parseFloat(b.metri_posati)||!b.id_bobina,className:"bg-mariner-600 hover:bg-mariner-700 text-white disabled:bg-gray-400 disabled:text-gray-200",children:[y&&(0,i.jsx)(eE.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salva"]})]})]})]})}),(0,i.jsx)(eO,{open:E,onClose:()=>I(!1),cavo:t,onSuccess:e=>{n(e),I(!1),a()},onError:x})]}):null}var eT=t(69024),eM=t(29867);function eF({open:e,onClose:a,onConfirm:t,title:r,description:n,isLoading:c,isDangerous:d=!1}){let[m,x]=(0,s.useState)(!1);return(0,i.jsx)(ez.lG,{open:e,onOpenChange:a,children:(0,i.jsx)(ez.Cf,{className:"sm:max-w-[400px]","aria-describedby":"confirm-disconnect-description",children:m?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(ez.c7,{children:(0,i.jsx)(ez.L3,{className:"text-center text-orange-600",children:"Conferma Finale"})}),(0,i.jsxs)("div",{className:"py-4 text-center",children:[(0,i.jsx)("div",{className:"mx-auto w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-4",children:(0,i.jsx)(eC.A,{className:"h-6 w-6 text-orange-600"})}),(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Sei veramente sicuro?"}),(0,i.jsxs)("p",{className:"text-sm text-gray-600 mb-4",children:["Questa azione scollegher\xe0 ",(0,i.jsx)("strong",{children:"entrambi i lati"})," del cavo."]}),(0,i.jsx)("div",{className:"bg-orange-50 border border-orange-200 rounded-md p-3",children:(0,i.jsx)("p",{className:"text-sm text-orange-800 font-medium",children:"⚠️ Operazione irreversibile"})})]}),(0,i.jsxs)(ez.Es,{className:"gap-2",children:[(0,i.jsx)(l.$,{variant:"outline",onClick:()=>x(!1),disabled:c,className:"flex-1",children:"No, Annulla"}),(0,i.jsx)(l.$,{variant:"outline",onClick:()=>{t()},disabled:c,className:"flex-1 bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100 hover:border-orange-300",children:c?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(eE.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Scollegando..."]}):"S\xec, Scollega"})]})]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(ez.c7,{children:[(0,i.jsxs)(ez.L3,{className:"flex items-center gap-2 text-orange-600",children:[(0,i.jsx)(eC.A,{className:"h-5 w-5"}),r]}),(0,i.jsx)(ez.rr,{id:"confirm-disconnect-description",children:n})]}),(0,i.jsx)("div",{className:"py-4",children:(0,i.jsxs)(o.Fc,{className:"border-orange-200 bg-orange-50",children:[(0,i.jsx)(eC.A,{className:"h-4 w-4 text-orange-600"}),(0,i.jsxs)(o.TN,{className:"text-orange-800",children:[(0,i.jsx)("strong",{children:"Attenzione:"})," Questa azione modificher\xe0 lo stato del collegamento del cavo."]})]})}),(0,i.jsxs)(ez.Es,{className:"gap-2",children:[(0,i.jsx)(l.$,{variant:"outline",onClick:a,disabled:c,className:"flex-1 hover:bg-gray-50",children:"Annulla"}),(0,i.jsxs)(l.$,{variant:"outline",onClick:()=>{d?x(!0):t()},disabled:c,className:"flex-1 bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100 hover:border-orange-300",children:[(0,i.jsx)(eC.A,{className:"mr-2 h-4 w-4"}),d?"Procedi":"Conferma"]})]})]})})})}let eL=function({open:e,onClose:a,cavo:t,onSuccess:r,onError:n}){let{cantiere:m}=(0,c.A)(),{toast:x}=(0,eM.dj)(),[u,p]=(0,s.useState)("cantiere"),[h,b]=(0,s.useState)(!1),[g,v]=(0,s.useState)(""),[j,f]=(0,s.useState)({open:!1,type:null,title:"",description:""}),N=(0,s.useRef)(null),y=(0,s.useRef)(null),C=(0,s.useRef)(null),[w,_]=(0,s.useState)(""),A=e=>{_(e),setTimeout(()=>_(""),1e3)},z=()=>{if(!t)return{stato:"non_collegato",descrizione:"Non collegato"};switch(t.collegamento||t.collegamenti||0){case 1:return{stato:"partenza",descrizione:"\uD83D\uDFE2⚪ Collegato lato partenza"};case 2:return{stato:"arrivo",descrizione:"⚪\uD83D\uDFE2 Collegato lato arrivo"};case 3:return{stato:"completo",descrizione:"\uD83D\uDFE2\uD83D\uDFE2 Completamente collegato"};default:return{stato:"non_collegato",descrizione:"⚪⚪ Non collegato"}}},k=async()=>{if(t&&m)try{b(!0),v(""),A("Collegamento in corso..."),await d.At.collegaCavo(m.id_cantiere,t.id_cavo,"partenza",u);let e=`Collegamento lato partenza completato per il cavo ${t.id_cavo}`;A(e),x({title:"Successo",description:e}),r&&r(),a()}catch(a){let e=a.response?.data?.detail||a.message||"Errore durante il collegamento";v(e),A(`Errore: ${e}`),n&&n(e)}finally{b(!1)}},S=async()=>{if(t&&m)try{b(!0),v(""),A("Collegamento in corso..."),await d.At.collegaCavo(m.id_cantiere,t.id_cavo,"arrivo",u);let e=`Collegamento lato arrivo completato per il cavo ${t.id_cavo}`;A(e),x({title:"Successo",description:e}),r&&r(),a()}catch(a){let e=a.response?.data?.detail||a.message||"Errore durante il collegamento";v(e),A(`Errore: ${e}`),n&&n(e)}finally{b(!1)}},E=async()=>{if(t&&m)try{b(!0),v(""),A("Collegamento entrambi i lati in corso..."),await d.At.collegaCavo(m.id_cantiere,t.id_cavo,"entrambi",u);let e=`Collegamento completo per il cavo ${t.id_cavo}`;A(e),x({title:"Successo",description:e}),r&&r(),a()}catch(a){let e=a.response?.data?.detail||a.message||"Errore durante il collegamento";v(e),A(`Errore: ${e}`),n&&n(e)}finally{b(!1)}},I=async()=>{if(t&&m&&j.type)try{b(!0),v(""),A("Scollegamento in corso..."),await d.At.scollegaCavo(m.id_cantiere,t.id_cavo,"entrambi"===j.type?void 0:j.type);let e="entrambi"===j.type?"":` lato ${j.type}`,i=`Scollegamento${e} completato per il cavo ${t.id_cavo}`;A(i),x({title:"Successo",description:i}),r&&r(),f({open:!1,type:null,title:"",description:""}),a()}catch(a){let e=a.response?.data?.detail||a.message||"Errore durante lo scollegamento";v(e),A(`Errore: ${e}`),n&&n(e)}finally{b(!1)}};if(!t)return null;let O=z(),$=(t.metri_posati||t.metratura_reale||0)>0;return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(eT.bL,{children:(0,i.jsx)("div",{"aria-live":"polite","aria-atomic":"true",children:w})}),(0,i.jsx)(ez.lG,{open:e,onOpenChange:a,children:(0,i.jsxs)(ez.Cf,{className:"sm:max-w-[600px] max-h-[90vh] overflow-y-auto",ref:N,"aria-describedby":"collegamenti-description",children:[(0,i.jsxs)(ez.c7,{children:[(0,i.jsxs)(ez.L3,{className:"flex items-center gap-2 text-blue-600",children:[(0,i.jsx)(ew.A,{className:"h-5 w-5"}),"Gestione Collegamenti - ",t.id_cavo]}),(0,i.jsxs)(ez.rr,{id:"collegamenti-description",children:["Gestisci i collegamenti del cavo ",t.id_cavo,". Usa i tasti 1, 2, 3 per azioni rapide."]})]}),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-200",children:(0,i.jsxs)("div",{className:"text-sm font-medium text-blue-800",children:["Informazioni Cavo / Tipologia: ",t.tipologia||"N/A"," / Da: ",t.ubicazione_partenza||"N/A"," / Formazione: ",t.sezione||"N/A"," / A: ",t.ubicazione_arrivo||"N/A"," / Metri Posati: ",t.metratura_reale||0," m"]})}),(0,i.jsx)("div",{className:"p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border",children:(0,i.jsx)("div",{className:"flex items-center justify-between",children:(0,i.jsxs)("div",{children:[(0,i.jsx)(ek.J,{className:"text-sm font-medium text-gray-700",children:"Stato Collegamento"}),(0,i.jsxs)("div",{className:"mt-1 text-lg font-semibold flex items-center gap-2",children:["completo"===O.stato&&(0,i.jsx)(eN.A,{className:"h-5 w-5 text-green-600"}),"non_collegato"===O.stato&&(0,i.jsx)(eI.A,{className:"h-5 w-5 text-gray-400"}),("partenza"===O.stato||"arrivo"===O.stato)&&(0,i.jsx)(eC.A,{className:"h-5 w-5 text-orange-500"}),O.descrizione]})]})})}),!$&&(0,i.jsxs)(o.Fc,{className:"border-orange-200 bg-orange-50",children:[(0,i.jsx)(eI.A,{className:"h-4 w-4 text-orange-600"}),(0,i.jsxs)(o.TN,{className:"text-orange-800",children:[(0,i.jsx)("strong",{children:"Attenzione:"})," Il cavo deve essere installato prima di poter essere collegato."]})]}),g&&(0,i.jsxs)(o.Fc,{variant:"destructive",children:[(0,i.jsx)(eI.A,{className:"h-4 w-4"}),(0,i.jsx)(o.TN,{children:g})]}),$&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)(ek.J,{className:"text-sm font-medium",children:"Responsabile Collegamento"}),(0,i.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg border",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(eN.A,{className:"h-4 w-4 text-green-600"}),(0,i.jsx)("span",{className:"font-medium",children:"Cantiere"})]}),(0,i.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Collegamento eseguito dal responsabile del cantiere"})]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)(ek.J,{className:"text-sm font-medium",children:"Azioni Disponibili"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 gap-3",children:[(0,i.jsx)(l.$,{ref:y,onClick:()=>{if(!t)return;let e=z();"partenza"===e.stato||"completo"===e.stato?f({open:!0,type:"partenza",title:"Scollega lato partenza",description:`Vuoi scollegare il lato partenza del cavo ${t.id_cavo}?`}):k()},disabled:h,className:"w-full h-12 text-left justify-start bg-green-50 text-green-700 border-green-200 hover:bg-green-100 hover:border-green-300",variant:"outline",children:(0,i.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,i.jsx)("span",{className:"text-sm font-bold text-green-700",children:"1"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium",children:"partenza"===O.stato||"completo"===O.stato?"Scollega Partenza":"Collega Partenza"}),(0,i.jsx)("div",{className:"text-xs text-green-600",children:"partenza"===O.stato||"completo"===O.stato?"Rimuovi collegamento lato partenza":"Connetti il lato partenza del cavo"})]})]}),h?(0,i.jsx)(eE.A,{className:"h-4 w-4 animate-spin"}):(0,i.jsx)(ew.A,{className:"h-4 w-4"})]})}),(0,i.jsx)(l.$,{onClick:()=>{if(!t)return;let e=z();"arrivo"===e.stato||"completo"===e.stato?f({open:!0,type:"arrivo",title:"Scollega lato arrivo",description:`Vuoi scollegare il lato arrivo del cavo ${t.id_cavo}?`}):S()},disabled:h,className:"w-full h-12 text-left justify-start bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 hover:border-blue-300",variant:"outline",children:(0,i.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,i.jsx)("span",{className:"text-sm font-bold text-blue-700",children:"2"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium",children:"arrivo"===O.stato||"completo"===O.stato?"Scollega Arrivo":"Collega Arrivo"}),(0,i.jsx)("div",{className:"text-xs text-blue-600",children:"arrivo"===O.stato||"completo"===O.stato?"Rimuovi collegamento lato arrivo":"Connetti il lato arrivo del cavo"})]})]}),h?(0,i.jsx)(eE.A,{className:"h-4 w-4 animate-spin"}):(0,i.jsx)(ew.A,{className:"h-4 w-4"})]})}),(0,i.jsx)(l.$,{onClick:()=>{t&&("completo"===z().stato?f({open:!0,type:"entrambi",title:"Scollega entrambi i lati",description:`Vuoi scollegare completamente il cavo ${t.id_cavo}? Questa operazione rimuover\xe0 tutti i collegamenti.`}):E())},disabled:h,className:"w-full h-12 text-left justify-start bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100 hover:border-purple-300",variant:"outline",children:(0,i.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:(0,i.jsx)("span",{className:"text-sm font-bold text-purple-700",children:"3"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium",children:"completo"===O.stato?"Scollega Completamente":"Collega Entrambi"}),(0,i.jsx)("div",{className:"text-xs text-purple-600",children:"completo"===O.stato?"Rimuovi tutti i collegamenti":"Connetti entrambi i lati del cavo"})]})]}),h?(0,i.jsx)(eE.A,{className:"h-4 w-4 animate-spin"}):(0,i.jsx)(ew.A,{className:"h-4 w-4"})]})})]})]})]})]}),(0,i.jsx)(ez.Es,{children:(0,i.jsxs)(l.$,{ref:C,variant:"outline",onClick:a,disabled:h,className:"hover:bg-gray-50",children:[(0,i.jsx)(er.A,{className:"mr-2 h-4 w-4"}),"Chiudi"]})})]})}),(0,i.jsx)(eF,{open:j.open,onClose:()=>f({open:!1,type:null,title:"",description:""}),onConfirm:I,title:j.title,description:j.description,isLoading:h,isDangerous:"entrambi"===j.type})]})};var eD=t(13253),eP=t(34729),eB=t(6727),eR=t(41312);function eV({open:e,onClose:a,caviSelezionati:t,tipoComanda:r,onSuccess:n,onError:m}){let{cantiere:x}=(0,c.A)(),[u,p]=(0,s.useState)({tipo_comanda:r||"POSA",responsabile:"",note:""}),[b,g]=(0,s.useState)([]),[v,j]=(0,s.useState)(!1),[f,N]=(0,s.useState)(!1),[y,C]=(0,s.useState)(""),w=async()=>{if(x){if(!u.responsabile)return void C("Seleziona un responsabile per la comanda");if(0===t.length)return void C("Seleziona almeno un cavo per la comanda");try{j(!0),C("");let e={tipo_comanda:u.tipo_comanda,responsabile:u.responsabile,note:u.note||null},i=await d.CV.createComandaWithCavi(x.id_cantiere,e,t);n(`Comanda ${i.data.codice_comanda} creata con successo per ${t.length} cavi`),a()}catch(e){m(e.response?.data?.detail||e.message||"Errore durante la creazione della comanda")}finally{j(!1)}}};return(0,i.jsx)(ez.lG,{open:e,onOpenChange:a,children:(0,i.jsxs)(ez.Cf,{className:"sm:max-w-[600px]",children:[(0,i.jsxs)(ez.c7,{children:[(0,i.jsxs)(ez.L3,{className:"flex items-center gap-2",children:[(0,i.jsx)(eB.A,{className:"h-5 w-5"}),"Crea Nuova Comanda"]}),(0,i.jsxs)(ez.rr,{children:["Crea una nuova comanda per ",t.length," cavi selezionati"]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,i.jsxs)(ek.J,{className:"text-sm font-medium",children:["Cavi Selezionati (",t.length,")"]}),(0,i.jsx)("div",{className:"mt-2 max-h-32 overflow-y-auto",children:(0,i.jsxs)("div",{className:"flex flex-wrap gap-1",children:[t.slice(0,10).map(e=>(0,i.jsx)("span",{className:"inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded",children:e},e)),t.length>10&&(0,i.jsxs)("span",{className:"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded",children:["+",t.length-10," altri..."]})]})})]}),y&&(0,i.jsxs)(o.Fc,{variant:"destructive",children:[(0,i.jsx)(eI.A,{className:"h-4 w-4"}),(0,i.jsx)(o.TN,{children:y})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(ek.J,{htmlFor:"tipo",children:"Tipo Comanda *"}),(0,i.jsxs)(h.l6,{value:u.tipo_comanda,onValueChange:e=>p(a=>({...a,tipo_comanda:e})),children:[(0,i.jsx)(h.bq,{children:(0,i.jsx)(h.yv,{})}),(0,i.jsxs)(h.gC,{children:[(0,i.jsx)(h.eb,{value:"POSA",children:"\uD83D\uDD27 Posa Cavi"}),(0,i.jsx)(h.eb,{value:"COLLEGAMENTO_PARTENZA",children:"\uD83D\uDD0C Collegamento Partenza"}),(0,i.jsx)(h.eb,{value:"COLLEGAMENTO_ARRIVO",children:"⚡ Collegamento Arrivo"}),(0,i.jsx)(h.eb,{value:"CERTIFICAZIONE",children:"\uD83D\uDCCB Certificazione"})]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(ek.J,{htmlFor:"responsabile",children:"Responsabile *"}),(0,i.jsxs)(h.l6,{value:u.responsabile,onValueChange:e=>p(a=>({...a,responsabile:e})),disabled:f,children:[(0,i.jsx)(h.bq,{children:(0,i.jsx)(h.yv,{placeholder:"Seleziona responsabile..."})}),(0,i.jsx)(h.gC,{children:b.map(e=>(0,i.jsx)(h.eb,{value:e.nome_responsabile,children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(eR.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:e.nome_responsabile}),e.numero_telefono&&(0,i.jsxs)("span",{className:"text-xs text-gray-500",children:["- ",e.numero_telefono]})]})},e.id))})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(ek.J,{htmlFor:"note",children:"Note (opzionale)"}),(0,i.jsx)(eP.T,{id:"note",placeholder:"Inserisci eventuali note per la comanda...",value:u.note,onChange:e=>p(a=>({...a,note:e.target.value})),rows:3})]}),(0,i.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,i.jsx)(ek.J,{className:"text-sm font-medium",children:"Riepilogo Comanda"}),(0,i.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Tipo:"})," ",(e=>{switch(e){case"POSA":return"Posa Cavi";case"COLLEGAMENTO_PARTENZA":return"Collegamento Partenza";case"COLLEGAMENTO_ARRIVO":return"Collegamento Arrivo";case"CERTIFICAZIONE":return"Certificazione";default:return e}})(u.tipo_comanda)]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Responsabile:"})," ",u.responsabile||"Non selezionato"]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Cavi:"})," ",t.length," selezionati"]}),u.note&&(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Note:"})," ",u.note]})]})]})]}),(0,i.jsxs)(ez.Es,{children:[(0,i.jsx)(l.$,{variant:"outline",onClick:a,disabled:v,children:"Annulla"}),(0,i.jsxs)(l.$,{onClick:w,disabled:v||!u.responsabile||0===t.length,children:[v?(0,i.jsx)(eE.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,i.jsx)(eB.A,{className:"h-4 w-4 mr-2"}),"Crea Comanda"]})]})]})})}var eq=t(16023);let eU=(0,ea.A)("file-spreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]]);function eG({open:e,onClose:a,tipo:t,onSuccess:r,onError:n}){let{cantiere:m}=(0,c.A)(),[x,u]=(0,s.useState)(null),[h,b]=(0,s.useState)(""),[g,v]=(0,s.useState)(!1),[j,f]=(0,s.useState)(""),[N,y]=(0,s.useState)(0),C=(0,s.useRef)(null),w=async()=>{if(x&&m){if("cavi"===t&&!h.trim())return void f("Inserisci il codice revisione per l'importazione cavi");try{let e;if(v(!0),f(""),y(0),e="cavi"===t?await d.mg.importCavi(m.id_cantiere,x,h.trim()):await d.mg.importBobine(m.id_cantiere,x),y(100),e.data.success){let i=e.data.details,s=e.data.message;"cavi"===t&&i?.cavi_importati?s+=` (${i.cavi_importati} cavi importati)`:"bobine"===t&&i?.bobine_importate&&(s+=` (${i.bobine_importate} bobine importate)`),r(s),a()}else n(e.data.message||"Errore durante l'importazione")}catch(e){n(e.response?.data?.detail||e.message||"Errore durante l'importazione del file")}finally{v(!1),y(0)}}},_=()=>{g||(u(null),b(""),f(""),y(0),C.current&&(C.current.value=""),a())},A=()=>"cavi"===t?"Cavi":"Bobine";return(0,i.jsx)(ez.lG,{open:e,onOpenChange:_,children:(0,i.jsxs)(ez.Cf,{className:"sm:max-w-[600px]",children:[(0,i.jsxs)(ez.c7,{children:[(0,i.jsxs)(ez.L3,{className:"flex items-center gap-2",children:[(0,i.jsx)(eq.A,{className:"h-5 w-5"}),"Importa ",A()," da Excel"]}),(0,i.jsxs)(ez.rr,{children:["Carica un file Excel per importare ",A().toLowerCase()," nel cantiere"]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,i.jsx)(ek.J,{className:"text-sm font-medium",children:"Requisiti File"}),(0,i.jsx)("ul",{className:"mt-2 space-y-1 text-sm text-gray-600",children:("cavi"===t?["File Excel (.xlsx o .xls)","Colonne richieste: ID_CAVO, SISTEMA, UTILITY, TIPOLOGIA, ecc.","Prima riga deve contenere le intestazioni","Codice revisione obbligatorio per tracciabilit\xe0"]:["File Excel (.xlsx o .xls)","Colonne richieste: NUMERO_BOBINA, UTILITY, TIPOLOGIA, METRI_TOTALI, ecc.","Prima riga deve contenere le intestazioni","I metri residui saranno impostati uguali ai metri totali"]).map((e,a)=>(0,i.jsxs)("li",{className:"flex items-start gap-2",children:[(0,i.jsx)("span",{className:"text-blue-500 mt-0.5",children:"•"}),(0,i.jsx)("span",{children:e})]},a))})]}),j&&(0,i.jsxs)(o.Fc,{variant:"destructive",children:[(0,i.jsx)(eI.A,{className:"h-4 w-4"}),(0,i.jsx)(o.TN,{children:j})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(ek.J,{htmlFor:"file",children:"File Excel *"}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(p.p,{ref:C,id:"file",type:"file",accept:".xlsx,.xls",onChange:e=>{let a=e.target.files?.[0];if(a){if(!["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel"].includes(a.type)&&!a.name.toLowerCase().endsWith(".xlsx")&&!a.name.toLowerCase().endsWith(".xls"))return void f("Seleziona un file Excel valido (.xlsx o .xls)");u(a),f("")}},disabled:g,className:"flex-1"}),x&&(0,i.jsxs)("div",{className:"flex items-center gap-1 text-green-600",children:[(0,i.jsx)(eN.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{className:"text-sm",children:"File selezionato"})]})]}),x&&(0,i.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,i.jsx)(eU,{className:"h-4 w-4 inline mr-1"}),x.name," (",(x.size/1024/1024).toFixed(2)," MB)"]})]}),"cavi"===t&&(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(ek.J,{htmlFor:"revisione",children:"Codice Revisione *"}),(0,i.jsx)(p.p,{id:"revisione",value:h,onChange:e=>b(e.target.value),placeholder:"es. REV001, V1.0, 2024-01",disabled:g}),(0,i.jsx)("p",{className:"text-sm text-gray-500",children:"Codice identificativo della revisione per tracciabilit\xe0 delle modifiche"})]}),g&&(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(eE.A,{className:"h-4 w-4 animate-spin"}),(0,i.jsx)("span",{className:"text-sm",children:"Caricamento in corso..."})]}),(0,i.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,i.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${N}%`}})})]}),x&&(0,i.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,i.jsx)(ek.J,{className:"text-sm font-medium",children:"Riepilogo Importazione"}),(0,i.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Tipo:"})," ",A()]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"File:"})," ",x.name]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Dimensione:"})," ",(x.size/1024/1024).toFixed(2)," MB"]}),"cavi"===t&&h&&(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Revisione:"})," ",h]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Cantiere:"})," ",m?.nome_cantiere]})]})]})]}),(0,i.jsxs)(ez.Es,{children:[(0,i.jsx)(l.$,{variant:"outline",onClick:_,disabled:g,children:"Annulla"}),(0,i.jsxs)(l.$,{onClick:w,disabled:g||!x||"cavi"===t&&!h.trim(),children:[g?(0,i.jsx)(eE.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,i.jsx)(eq.A,{className:"h-4 w-4 mr-2"}),"Importa ",A()]})]})]})})}var eJ=t(61611),eZ=t(31158);function eH({open:e,onClose:a,onSuccess:t,onError:r}){let{cantiere:n}=(0,c.A)(),[m,u]=(0,s.useState)({cavi:!0,bobine:!0,comande:!1,certificazioni:!1,responsabili:!1}),[p,h]=(0,s.useState)(!1),[b,g]=(0,s.useState)(""),v=(e,a)=>{u(t=>({...t,[e]:a}))},j=async()=>{if(n)try{h(!0);let e=await d.mg.exportCavi(n.id_cantiere),a=window.URL.createObjectURL(new Blob([e.data])),i=document.createElement("a");i.href=a,i.setAttribute("download",`cavi_${n.nome_cantiere}_${new Date().toISOString().split("T")[0]}.xlsx`),document.body.appendChild(i),i.click(),i.remove(),window.URL.revokeObjectURL(a),t("Export cavi completato con successo")}catch(e){r(e.response?.data?.detail||e.message||"Errore durante l'export dei cavi")}finally{h(!1)}},f=async()=>{if(n)try{h(!0);let e=await d.mg.exportBobine(n.id_cantiere),a=window.URL.createObjectURL(new Blob([e.data])),i=document.createElement("a");i.href=a,i.setAttribute("download",`bobine_${n.nome_cantiere}_${new Date().toISOString().split("T")[0]}.xlsx`),document.body.appendChild(i),i.click(),i.remove(),window.URL.revokeObjectURL(a),t("Export bobine completato con successo")}catch(e){r(e.response?.data?.detail||e.message||"Errore durante l'export delle bobine")}finally{h(!1)}},N=async()=>{if(n)try{h(!0),g("");let e=[];m.cavi&&e.push(j()),m.bobine&&e.push(f()),m.comande,m.certificazioni,m.responsabili,await Promise.all(e);let i=Object.values(m).filter(Boolean).length;t(`Export completato: ${i} file scaricati`),a()}catch(e){r(e.response?.data?.detail||e.message||"Errore durante l'export dei dati")}finally{h(!1)}},y=[{key:"cavi",label:"Cavi",description:"Esporta tutti i cavi del cantiere con stato, collegamenti e certificazioni",icon:(0,i.jsx)(eJ.A,{className:"h-4 w-4"}),available:!0},{key:"bobine",label:"Bobine",description:"Esporta tutte le bobine del parco cavi con metri residui e assegnazioni",icon:(0,i.jsx)(eU,{className:"h-4 w-4"}),available:!0},{key:"comande",label:"Comande",description:"Esporta tutte le comande con cavi assegnati e responsabili",icon:(0,i.jsx)(eU,{className:"h-4 w-4"}),available:!1},{key:"certificazioni",label:"Certificazioni",description:"Esporta tutte le certificazioni con esiti e responsabili",icon:(0,i.jsx)(eU,{className:"h-4 w-4"}),available:!1},{key:"responsabili",label:"Responsabili",description:"Esporta tutti i responsabili con contatti e ruoli",icon:(0,i.jsx)(eU,{className:"h-4 w-4"}),available:!1}];return(0,i.jsx)(ez.lG,{open:e,onOpenChange:a,children:(0,i.jsxs)(ez.Cf,{className:"sm:max-w-[600px]",children:[(0,i.jsxs)(ez.c7,{children:[(0,i.jsxs)(ez.L3,{className:"flex items-center gap-2",children:[(0,i.jsx)(eZ.A,{className:"h-5 w-5"}),"Esporta Dati Cantiere"]}),(0,i.jsxs)(ez.rr,{children:["Seleziona i dati da esportare dal cantiere ",n?.nome_cantiere]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[b&&(0,i.jsxs)(o.Fc,{variant:"destructive",children:[(0,i.jsx)(eI.A,{className:"h-4 w-4"}),(0,i.jsx)(o.TN,{children:b})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)(ek.J,{className:"text-sm font-medium",children:"Seleziona Dati da Esportare"}),y.map(e=>(0,i.jsxs)("div",{className:`flex items-start space-x-3 p-3 rounded-lg border ${e.available?"bg-white":"bg-gray-50"}`,children:[(0,i.jsx)(x.S,{id:e.key,checked:m[e.key],onCheckedChange:a=>v(e.key,a),disabled:!e.available||p}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,(0,i.jsxs)(ek.J,{htmlFor:e.key,className:`font-medium ${!e.available?"text-gray-500":""}`,children:[e.label,!e.available&&(0,i.jsx)("span",{className:"ml-2 text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded",children:"In sviluppo"})]})]}),(0,i.jsx)("p",{className:`text-sm mt-1 ${!e.available?"text-gray-400":"text-gray-600"}`,children:e.description})]})]},e.key))]}),(0,i.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,i.jsx)(ek.J,{className:"text-sm font-medium",children:"Informazioni Export"}),(0,i.jsxs)("ul",{className:"mt-2 space-y-1 text-sm text-gray-600",children:[(0,i.jsx)("li",{children:"• I file saranno scaricati in formato Excel (.xlsx)"}),(0,i.jsx)("li",{children:"• I nomi file includeranno data e nome cantiere"}),(0,i.jsx)("li",{children:"• I dati esportati riflettono lo stato attuale del database"}),(0,i.jsx)("li",{children:"• L'export non modifica i dati originali"})]})]}),Object.values(m).filter(Boolean).length>0&&(0,i.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,i.jsx)(ek.J,{className:"text-sm font-medium",children:"Riepilogo Export"}),(0,i.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Cantiere:"})," ",n?.nome_cantiere]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"File da scaricare:"})," ",Object.values(m).filter(Boolean).length]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Data export:"})," ",new Date().toLocaleDateString("it-IT")]})]})]})]}),(0,i.jsxs)(ez.Es,{children:[(0,i.jsx)(l.$,{variant:"outline",onClick:a,disabled:p,children:"Annulla"}),(0,i.jsxs)(l.$,{onClick:N,disabled:p||0===Object.values(m).filter(Boolean).length,children:[p?(0,i.jsx)(eE.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,i.jsx)(eZ.A,{className:"h-4 w-4 mr-2"}),"Esporta ",Object.values(m).filter(Boolean).length>0?`(${Object.values(m).filter(Boolean).length})`:""]})]})]})})}function eW(){let{user:e,cantiere:a,isAuthenticated:t,isLoading:m}=(0,c.A)();(0,r.useRouter)(),console.log({cantiereFromAuth:a,user:e,userRole:e?.ruolo});let x=({title:e,description:a,variant:t})=>{},[u,p]=(0,s.useState)([]),[h,b]=(0,s.useState)([]),[g,v]=(0,s.useState)(!0),[j,f]=(0,s.useState)(""),[N,y]=(0,s.useState)([]),[C,w]=(0,s.useState)(!0),[_,A]=(0,s.useState)([]),[z,k]=(0,s.useState)(""),[S,E]=(0,s.useState)({open:!1,cavo:null}),[I,O]=(0,s.useState)({open:!1,cavo:null}),[$,T]=(0,s.useState)({open:!1,cavo:null}),[M,F]=(0,s.useState)({open:!1,cavo:null}),[L,D]=(0,s.useState)({open:!1}),[P,B]=(0,s.useState)({open:!1}),[R,V]=(0,s.useState)(!1),[q,U]=(0,s.useState)({totali:0,installati:0,collegati:0,certificati:0,percentualeInstallazione:0,percentualeCollegamento:0,percentualeCertificazione:0,metriTotali:0,metriInstallati:0,metriCollegati:0,metriCertificati:0}),[G,J]=(0,s.useState)(0),Z=a||(G>0?{id_cantiere:G,commessa:`Cantiere ${G}`}:null),H=async()=>{try{v(!0),f("");try{let e=await d.At.getCavi(G),a=e.filter(e=>!e.spare),t=e.filter(e=>e.spare);p(a),b(t),W(a)}catch(e){try{let e=await fetch(`http://localhost:8001/api/cavi/debug/${G}`),a=await e.json();if(a.cavi&&Array.isArray(a.cavi)){let e=a.cavi.filter(e=>!e.spare),t=a.cavi.filter(e=>e.spare);p(e),b(t),W(e),f("⚠️ Dati caricati tramite endpoint debug (problema autenticazione)")}else throw Error("Formato dati debug non valido")}catch(a){throw e}}}catch(e){f(`Errore nel caricamento dei cavi: ${e.response?.data?.detail||e.message}`)}finally{v(!1)}},W=e=>{let a=e.length,t=e.filter(e=>(e.metri_posati||e.metratura_reale||0)>0).length,i=e.filter(e=>3===(e.collegamento||e.collegamenti)).length,s=e.filter(e=>e.certificato).length,r=e.reduce((e,a)=>e+(a.metri_teorici||0),0),n=e.reduce((e,a)=>e+(a.metri_posati||0),0);U({totali:a,installati:t,collegati:i,certificati:s,percentualeInstallazione:a>0?Math.round(t/a*100):0,percentualeCollegamento:a>0?Math.round(i/a*100):0,percentualeCertificazione:a>0?Math.round(s/a*100):0,metriTotali:r,metriInstallati:n,metriCollegati:e.filter(e=>3===e.collegamento).reduce((e,a)=>e+(a.metri_posati||0),0),metriCertificati:e.filter(e=>e.certificato).reduce((e,a)=>e+(a.metri_posati||0),0)})},Y=(e,a,t)=>{switch(a){case"insert_meters":E({open:!0,cavo:e});break;case"modify_reel":O({open:!0,cavo:e});break;case"view_command":x({title:"Visualizza Comanda",description:`Apertura comanda ${t} per cavo ${e.id_cavo}`});break;case"connect_cable":case"connect_arrival":case"connect_departure":case"disconnect_cable":case"manage_connections":T({open:!0,cavo:e});break;case"create_certificate":case"generate_pdf":F({open:!0,cavo:e})}},X=(e,a)=>{switch(a){case"view_details":x({title:"Visualizza Dettagli",description:`Apertura dettagli per cavo ${e.id_cavo}`});break;case"edit":x({title:"Modifica Cavo",description:"Funzione modifica cavo in sviluppo"});break;case"delete":x({title:"Elimina Cavo",description:"Funzione eliminazione cavo in sviluppo",variant:"destructive"});break;case"add_new":x({title:"Aggiungi Nuovo Cavo",description:"Funzione aggiunta nuovo cavo in sviluppo"});break;case"select":N.includes(e.id_cavo)?(y(N.filter(a=>a!==e.id_cavo)),x({title:"Cavo Deselezionato",description:`Cavo ${e.id_cavo} deselezionato`})):(y([...N,e.id_cavo]),x({title:"Cavo Selezionato",description:`Cavo ${e.id_cavo} selezionato`}));break;case"copy_id":navigator.clipboard.writeText(e.id_cavo),x({title:"ID Copiato",description:`ID cavo ${e.id_cavo} copiato negli appunti`});break;case"copy_details":let t=`ID: ${e.id_cavo}, Tipologia: ${e.tipologia}, Formazione: ${e.formazione||e.sezione}, Metri: ${e.metri_teorici}`;navigator.clipboard.writeText(t),x({title:"Dettagli Copiati",description:"Dettagli cavo copiati negli appunti"});break;case"add_to_command":x({title:"Aggiungi a Comanda",description:"Funzione aggiunta a comanda in sviluppo"});break;case"remove_from_command":x({title:"Rimuovi da Comanda",description:"Funzione rimozione da comanda in sviluppo"});break;case"create_command_posa":D({open:!0,tipoComanda:"POSA"});break;case"create_command_collegamento_partenza":D({open:!0,tipoComanda:"COLLEGAMENTO_PARTENZA"});break;case"create_command_collegamento_arrivo":D({open:!0,tipoComanda:"COLLEGAMENTO_ARRIVO"});break;case"create_command_certificazione":D({open:!0,tipoComanda:"CERTIFICAZIONE"});break;case"add_multiple_to_command":x({title:"Aggiungi Tutti a Comanda",description:"Funzione aggiunta multipla a comanda in sviluppo"});break;case"remove_multiple_from_commands":x({title:"Rimuovi Tutti dalle Comande",description:"Funzione rimozione multipla dalle comande in sviluppo"});break;default:x({title:"Azione non implementata",description:`Azione ${a} non ancora implementata`})}},K=e=>{x({title:"Operazione completata",description:e}),H()},Q=e=>{x({title:"Errore",description:e,variant:"destructive"})};return m||g?(0,i.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,i.jsx)(eE.A,{className:"h-8 w-8 animate-spin"})}):G?j?(0,i.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,i.jsxs)(o.Fc,{variant:"destructive",children:[(0,i.jsx)(eI.A,{className:"h-4 w-4"}),(0,i.jsx)(o.TN,{children:j})]}),(0,i.jsx)(l.$,{onClick:H,className:"mt-4",children:"Riprova"})]}):(0,i.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,i.jsx)(eA,{cavi:u,filteredCavi:_,revisioneCorrente:z,className:"mb-2"}),(0,i.jsx)("div",{className:"mb-8",children:(0,i.jsx)(ev,{cavi:u,loading:g,selectionEnabled:C,selectedCavi:N,onSelectionChange:y,onStatusAction:Y,onContextMenuAction:X})}),h.length>0&&(0,i.jsx)("div",{className:"mb-8",children:(0,i.jsxs)(n.Zp,{children:[(0,i.jsx)(n.aR,{children:(0,i.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,i.jsx)(e_.A,{className:"h-5 w-5"}),(0,i.jsxs)("span",{children:["Cavi Spare (",h.length,")"]})]})}),(0,i.jsx)(n.Wu,{children:(0,i.jsx)(ev,{cavi:h,loading:g,selectionEnabled:!1,onStatusAction:Y,onContextMenuAction:X})})]})}),!1,(0,i.jsx)(e$,{open:S.open,onClose:()=>E({open:!1,cavo:null}),cavo:S.cavo,cantiere:Z,onSuccess:K,onError:Q}),(0,i.jsx)(eO,{open:I.open,onClose:()=>O({open:!1,cavo:null}),cavo:I.cavo,cantiere:Z,onSuccess:K,onError:Q}),(0,i.jsx)(eL,{open:$.open,onClose:()=>T({open:!1,cavo:null}),cavo:$.cavo,onSuccess:K,onError:Q}),(0,i.jsx)(eD.A,{open:M.open,onClose:()=>F({open:!1,cavo:null}),cavo:M.cavo,onSuccess:K,onError:Q}),(0,i.jsx)(eV,{open:L.open,onClose:()=>D({open:!1}),caviSelezionati:N,tipoComanda:L.tipoComanda,onSuccess:K,onError:Q}),(0,i.jsx)(eG,{open:P.open,onClose:()=>B({open:!1}),tipo:P.tipo||"cavi",onSuccess:K,onError:Q}),(0,i.jsx)(eH,{open:R,onClose:()=>V(!1),onSuccess:K,onError:Q})]}):(0,i.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,i.jsxs)(o.Fc,{children:[(0,i.jsx)(eI.A,{className:"h-4 w-4"}),(0,i.jsx)(o.TN,{children:"Nessun cantiere selezionato. Seleziona un cantiere per visualizzare i cavi."})]}),(0,i.jsxs)("div",{className:"mt-4 p-4 bg-gray-100 rounded",children:[(0,i.jsx)("h3",{className:"font-bold",children:"Debug Info:"}),(0,i.jsxs)("p",{children:["User: ",e?e.username:"Non autenticato"]}),(0,i.jsxs)("p",{children:["Cantiere context: ",a?a.commessa:"Nessuno"]}),(0,i.jsxs)("p",{children:["Token presente: ","N/A"]})]})]})}},10698:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>i});let i=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs_2\\\\src\\\\app\\\\cavi\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_2\\src\\app\\cavi\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13253:()=>{throw Error('Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31mx\x1b[0m Expression expected\n     ,-[\x1b[36;1;4mC:\\CMS\\webapp-nextjs_2\\src\\components\\cavi\\CertificazioneDialog.tsx\x1b[0m:245:1]\n \x1b[2m242\x1b[0m |   const puoEssereCertificato = isInstalled\n \x1b[2m243\x1b[0m | \n \x1b[2m244\x1b[0m |   return (\n \x1b[2m245\x1b[0m |     <>\n     : \x1b[35;1m     ^\x1b[0m\n \x1b[2m246\x1b[0m |       <VisuallyHidden.Root>\n \x1b[2m247\x1b[0m |         <div aria-live="polite" aria-atomic="true">\n \x1b[2m248\x1b[0m |           {screenReaderAnnouncement}\n     `----\n  \x1b[31mx\x1b[0m Expected \',\', got \'open\'\n     ,-[\x1b[36;1;4mC:\\CMS\\webapp-nextjs_2\\src\\components\\cavi\\CertificazioneDialog.tsx\x1b[0m:252:1]\n \x1b[2m249\x1b[0m |         </div>\n \x1b[2m250\x1b[0m |       </VisuallyHidden.Root>\n \x1b[2m251\x1b[0m | \n \x1b[2m252\x1b[0m |       <Dialog open={open} onOpenChange={onClose}>\n     : \x1b[35;1m              ^^^^\x1b[0m\n \x1b[2m253\x1b[0m |         <DialogContent\n \x1b[2m254\x1b[0m |           className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto"\n \x1b[2m255\x1b[0m |           aria-describedby="certificazione-description"\n     `----\n\n\nCaused by:\n    Syntax Error')},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34729:(e,a,t)=>{"use strict";t.d(a,{T:()=>n});var i=t(60687),s=t(43210),r=t(4780);let n=s.forwardRef(({className:e,...a},t)=>(0,i.jsx)("textarea",{className:(0,r.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...a}));n.displayName="Textarea"},35476:(e,a,t)=>{Promise.resolve().then(t.bind(t,10698))},45583:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});let i=(0,t(62688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56896:(e,a,t)=>{"use strict";t.d(a,{S:()=>l});var i=t(60687);t(43210);var s=t(40211),r=t(13964),n=t(4780);function l({className:e,...a}){return(0,i.jsx)(s.bL,{"data-slot":"checkbox",className:(0,n.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...a,children:(0,i.jsx)(s.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,i.jsx)(r.A,{className:"size-3.5"})})})}},61611:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});let i=(0,t(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,a,t)=>{"use strict";t.d(a,{Cf:()=>m,Es:()=>u,L3:()=>p,c7:()=>x,lG:()=>l,rr:()=>h,zM:()=>o});var i=t(60687);t(43210);var s=t(26134),r=t(11860),n=t(4780);function l({...e}){return(0,i.jsx)(s.bL,{"data-slot":"dialog",...e})}function o({...e}){return(0,i.jsx)(s.l9,{"data-slot":"dialog-trigger",...e})}function c({...e}){return(0,i.jsx)(s.ZL,{"data-slot":"dialog-portal",...e})}function d({className:e,...a}){return(0,i.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...a})}function m({className:e,children:a,showCloseButton:t=!0,...l}){return(0,i.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,i.jsx)(d,{}),(0,i.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...l,children:[a,t&&(0,i.jsxs)(s.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,i.jsx)(r.A,{}),(0,i.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x({className:e,...a}){return(0,i.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",e),...a})}function u({className:e,...a}){return(0,i.jsx)("div",{"data-slot":"dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...a})}function p({className:e,...a}){return(0,i.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",e),...a})}function h({className:e,...a}){return(0,i.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...a})}},65831:(e,a,t)=>{Promise.resolve().then(t.bind(t,3402))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86691:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c});var i=t(65239),s=t(48088),r=t(88170),n=t.n(r),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(a,o);let c={children:["",{children:["cavi",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,10698)),"C:\\CMS\\webapp-nextjs_2\\src\\app\\cavi\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\CMS\\webapp-nextjs_2\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\CMS\\webapp-nextjs_2\\src\\app\\cavi\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new i.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/cavi/page",pathname:"/cavi",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},94735:e=>{"use strict";e.exports=require("events")}};var a=require("../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),i=a.X(0,[447,124,658,952,400,86,891,807,109],()=>t(86691));module.exports=i})();