(()=>{var e={};e.id=986,e.ids=[986],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10698:(e,a,i)=>{"use strict";i.r(a),i.d(a,{default:()=>s});let s=(0,i(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs_2\\\\src\\\\app\\\\cavi\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_2\\src\\app\\cavi\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34729:(e,a,i)=>{"use strict";i.d(a,{T:()=>l});var s=i(60687),t=i(43210),r=i(4780);let l=t.forwardRef(({className:e,...a},i)=>(0,s.jsx)("textarea",{className:(0,r.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:i,...a}));l.displayName="Textarea"},35071:(e,a,i)=>{"use strict";i.d(a,{A:()=>s});let s=(0,i(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},35476:(e,a,i)=>{Promise.resolve().then(i.bind(i,10698))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56896:(e,a,i)=>{"use strict";i.d(a,{S:()=>n});var s=i(60687);i(43210);var t=i(40211),r=i(13964),l=i(4780);function n({className:e,...a}){return(0,s.jsx)(t.bL,{"data-slot":"checkbox",className:(0,l.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...a,children:(0,s.jsx)(t.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(r.A,{className:"size-3.5"})})})}},61611:(e,a,i)=>{"use strict";i.d(a,{A:()=>s});let s=(0,i(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,a,i)=>{"use strict";i.d(a,{Cf:()=>m,Es:()=>u,L3:()=>h,c7:()=>x,lG:()=>n,rr:()=>p,zM:()=>o});var s=i(60687);i(43210);var t=i(26134),r=i(11860),l=i(4780);function n({...e}){return(0,s.jsx)(t.bL,{"data-slot":"dialog",...e})}function o({...e}){return(0,s.jsx)(t.l9,{"data-slot":"dialog-trigger",...e})}function c({...e}){return(0,s.jsx)(t.ZL,{"data-slot":"dialog-portal",...e})}function d({className:e,...a}){return(0,s.jsx)(t.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...a})}function m({className:e,children:a,showCloseButton:i=!0,...n}){return(0,s.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,s.jsx)(d,{}),(0,s.jsxs)(t.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...n,children:[a,i&&(0,s.jsxs)(t.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(r.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x({className:e,...a}){return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",e),...a})}function u({className:e,...a}){return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...a})}function h({className:e,...a}){return(0,s.jsx)(t.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",e),...a})}function p({className:e,...a}){return(0,s.jsx)(t.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",e),...a})}},65831:(e,a,i)=>{Promise.resolve().then(i.bind(i,73540))},70615:(e,a,i)=>{"use strict";i.d(a,{A:()=>s});let s=(0,i(62688).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},72942:(e,a,i)=>{"use strict";i.d(a,{RG:()=>N,bL:()=>E,q7:()=>I});var s=i(43210),t=i(70569),r=i(9510),l=i(98599),n=i(11273),o=i(96963),c=i(14163),d=i(13495),m=i(65551),x=i(43),u=i(60687),h="rovingFocusGroup.onEntryFocus",p={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[g,b,j]=(0,r.N)(v),[f,N]=(0,n.A)(v,[j]),[y,_]=f(v),C=s.forwardRef((e,a)=>(0,u.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,u.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,u.jsx)(w,{...e,ref:a})})}));C.displayName=v;var w=s.forwardRef((e,a)=>{let{__scopeRovingFocusGroup:i,orientation:r,loop:n=!1,dir:o,currentTabStopId:g,defaultCurrentTabStopId:j,onCurrentTabStopIdChange:f,onEntryFocus:N,preventScrollOnEntryFocus:_=!1,...C}=e,w=s.useRef(null),z=(0,l.s)(a,w),A=(0,x.jH)(o),[k,E]=(0,m.i)({prop:g,defaultProp:j??null,onChange:f,caller:v}),[I,F]=s.useState(!1),$=(0,d.c)(N),O=b(i),D=s.useRef(!1),[R,T]=s.useState(0);return s.useEffect(()=>{let e=w.current;if(e)return e.addEventListener(h,$),()=>e.removeEventListener(h,$)},[$]),(0,u.jsx)(y,{scope:i,orientation:r,dir:A,loop:n,currentTabStopId:k,onItemFocus:s.useCallback(e=>E(e),[E]),onItemShiftTab:s.useCallback(()=>F(!0),[]),onFocusableItemAdd:s.useCallback(()=>T(e=>e+1),[]),onFocusableItemRemove:s.useCallback(()=>T(e=>e-1),[]),children:(0,u.jsx)(c.sG.div,{tabIndex:I||0===R?-1:0,"data-orientation":r,...C,ref:z,style:{outline:"none",...e.style},onMouseDown:(0,t.m)(e.onMouseDown,()=>{D.current=!0}),onFocus:(0,t.m)(e.onFocus,e=>{let a=!D.current;if(e.target===e.currentTarget&&a&&!I){let a=new CustomEvent(h,p);if(e.currentTarget.dispatchEvent(a),!a.defaultPrevented){let e=O().filter(e=>e.focusable);S([e.find(e=>e.active),e.find(e=>e.id===k),...e].filter(Boolean).map(e=>e.ref.current),_)}}D.current=!1}),onBlur:(0,t.m)(e.onBlur,()=>F(!1))})})}),z="RovingFocusGroupItem",A=s.forwardRef((e,a)=>{let{__scopeRovingFocusGroup:i,focusable:r=!0,active:l=!1,tabStopId:n,children:d,...m}=e,x=(0,o.B)(),h=n||x,p=_(z,i),v=p.currentTabStopId===h,j=b(i),{onFocusableItemAdd:f,onFocusableItemRemove:N,currentTabStopId:y}=p;return s.useEffect(()=>{if(r)return f(),()=>N()},[r,f,N]),(0,u.jsx)(g.ItemSlot,{scope:i,id:h,focusable:r,active:l,children:(0,u.jsx)(c.sG.span,{tabIndex:v?0:-1,"data-orientation":p.orientation,...m,ref:a,onMouseDown:(0,t.m)(e.onMouseDown,e=>{r?p.onItemFocus(h):e.preventDefault()}),onFocus:(0,t.m)(e.onFocus,()=>p.onItemFocus(h)),onKeyDown:(0,t.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void p.onItemShiftTab();if(e.target!==e.currentTarget)return;let a=function(e,a,i){var s;let t=(s=e.key,"rtl"!==i?s:"ArrowLeft"===s?"ArrowRight":"ArrowRight"===s?"ArrowLeft":s);if(!("vertical"===a&&["ArrowLeft","ArrowRight"].includes(t))&&!("horizontal"===a&&["ArrowUp","ArrowDown"].includes(t)))return k[t]}(e,p.orientation,p.dir);if(void 0!==a){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let i=j().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===a)i.reverse();else if("prev"===a||"next"===a){"prev"===a&&i.reverse();let s=i.indexOf(e.currentTarget);i=p.loop?function(e,a){return e.map((i,s)=>e[(a+s)%e.length])}(i,s+1):i.slice(s+1)}setTimeout(()=>S(i))}}),children:"function"==typeof d?d({isCurrentTabStop:v,hasTabStop:null!=y}):d})})});A.displayName=z;var k={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function S(e,a=!1){let i=document.activeElement;for(let s of e)if(s===i||(s.focus({preventScroll:a}),document.activeElement!==i))return}var E=C,I=A},73540:(e,a,i)=>{"use strict";i.r(a),i.d(a,{default:()=>ir});var s=i(60687),t=i(43210),r=i(16189),l=i(44493),n=i(29523),o=i(91821),c=i(63213),d=i(62185),m=i(96834),x=i(56896);i(15391);var u=i(6211),h=i(89667),p=i(15079),v=i(70569),g=i(98599),b=i(11273),j=i(31355),f=i(1359),N=i(32547),y=i(96963),_=i(55509),C=i(25028),w=i(46059),z=i(14163),A=i(8730),k=i(65551),S=i(63376),E=i(42247),I="Popover",[F,$]=(0,b.A)(I,[_.Bk]),O=(0,_.Bk)(),[D,R]=F(I),T=e=>{let{__scopePopover:a,children:i,open:r,defaultOpen:l,onOpenChange:n,modal:o=!1}=e,c=O(a),d=t.useRef(null),[m,x]=t.useState(!1),[u,h]=(0,k.i)({prop:r,defaultProp:l??!1,onChange:n,caller:I});return(0,s.jsx)(_.bL,{...c,children:(0,s.jsx)(D,{scope:a,contentId:(0,y.B)(),triggerRef:d,open:u,onOpenChange:h,onOpenToggle:t.useCallback(()=>h(e=>!e),[h]),hasCustomAnchor:m,onCustomAnchorAdd:t.useCallback(()=>x(!0),[]),onCustomAnchorRemove:t.useCallback(()=>x(!1),[]),modal:o,children:i})})};T.displayName=I;var P="PopoverAnchor";t.forwardRef((e,a)=>{let{__scopePopover:i,...r}=e,l=R(P,i),n=O(i),{onCustomAnchorAdd:o,onCustomAnchorRemove:c}=l;return t.useEffect(()=>(o(),()=>c()),[o,c]),(0,s.jsx)(_.Mz,{...n,...r,ref:a})}).displayName=P;var M="PopoverTrigger",L=t.forwardRef((e,a)=>{let{__scopePopover:i,...t}=e,r=R(M,i),l=O(i),n=(0,g.s)(a,r.triggerRef),o=(0,s.jsx)(z.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":r.open,"aria-controls":r.contentId,"data-state":Y(r.open),...t,ref:n,onClick:(0,v.m)(e.onClick,r.onOpenToggle)});return r.hasCustomAnchor?o:(0,s.jsx)(_.Mz,{asChild:!0,...l,children:o})});L.displayName=M;var B="PopoverPortal",[U,G]=F(B,{forceMount:void 0}),q=e=>{let{__scopePopover:a,forceMount:i,children:t,container:r}=e,l=R(B,a);return(0,s.jsx)(U,{scope:a,forceMount:i,children:(0,s.jsx)(w.C,{present:i||l.open,children:(0,s.jsx)(C.Z,{asChild:!0,container:r,children:t})})})};q.displayName=B;var V="PopoverContent",J=t.forwardRef((e,a)=>{let i=G(V,e.__scopePopover),{forceMount:t=i.forceMount,...r}=e,l=R(V,e.__scopePopover);return(0,s.jsx)(w.C,{present:t||l.open,children:l.modal?(0,s.jsx)(H,{...r,ref:a}):(0,s.jsx)(W,{...r,ref:a})})});J.displayName=V;var Z=(0,A.TL)("PopoverContent.RemoveScroll"),H=t.forwardRef((e,a)=>{let i=R(V,e.__scopePopover),r=t.useRef(null),l=(0,g.s)(a,r),n=t.useRef(!1);return t.useEffect(()=>{let e=r.current;if(e)return(0,S.Eq)(e)},[]),(0,s.jsx)(E.A,{as:Z,allowPinchZoom:!0,children:(0,s.jsx)(K,{...e,ref:l,trapFocus:i.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,v.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),n.current||i.triggerRef.current?.focus()}),onPointerDownOutside:(0,v.m)(e.onPointerDownOutside,e=>{let a=e.detail.originalEvent,i=0===a.button&&!0===a.ctrlKey;n.current=2===a.button||i},{checkForDefaultPrevented:!1}),onFocusOutside:(0,v.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),W=t.forwardRef((e,a)=>{let i=R(V,e.__scopePopover),r=t.useRef(!1),l=t.useRef(!1);return(0,s.jsx)(K,{...e,ref:a,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{e.onCloseAutoFocus?.(a),a.defaultPrevented||(r.current||i.triggerRef.current?.focus(),a.preventDefault()),r.current=!1,l.current=!1},onInteractOutside:a=>{e.onInteractOutside?.(a),a.defaultPrevented||(r.current=!0,"pointerdown"===a.detail.originalEvent.type&&(l.current=!0));let s=a.target;i.triggerRef.current?.contains(s)&&a.preventDefault(),"focusin"===a.detail.originalEvent.type&&l.current&&a.preventDefault()}})}),K=t.forwardRef((e,a)=>{let{__scopePopover:i,trapFocus:t,onOpenAutoFocus:r,onCloseAutoFocus:l,disableOutsidePointerEvents:n,onEscapeKeyDown:o,onPointerDownOutside:c,onFocusOutside:d,onInteractOutside:m,...x}=e,u=R(V,i),h=O(i);return(0,f.Oh)(),(0,s.jsx)(N.n,{asChild:!0,loop:!0,trapped:t,onMountAutoFocus:r,onUnmountAutoFocus:l,children:(0,s.jsx)(j.qW,{asChild:!0,disableOutsidePointerEvents:n,onInteractOutside:m,onEscapeKeyDown:o,onPointerDownOutside:c,onFocusOutside:d,onDismiss:()=>u.onOpenChange(!1),children:(0,s.jsx)(_.UC,{"data-state":Y(u.open),role:"dialog",id:u.contentId,...h,...x,ref:a,style:{...x.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),X="PopoverClose";function Y(e){return e?"open":"closed"}t.forwardRef((e,a)=>{let{__scopePopover:i,...t}=e,r=R(X,i);return(0,s.jsx)(z.sG.button,{type:"button",...t,ref:a,onClick:(0,v.m)(e.onClick,()=>r.onOpenChange(!1))})}).displayName=X,t.forwardRef((e,a)=>{let{__scopePopover:i,...t}=e,r=O(i);return(0,s.jsx)(_.i3,{...r,...t,ref:a})}).displayName="PopoverArrow";var Q=i(4780);let ee=t.forwardRef(({className:e,align:a="center",sideOffset:i=4,...t},r)=>(0,s.jsx)(q,{children:(0,s.jsx)(J,{ref:r,align:a,sideOffset:i,className:(0,Q.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})}));ee.displayName=J.displayName;var ea=i(62688);let ei=(0,ea.A)("arrow-up-down",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]]),es=(0,ea.A)("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]),et=(0,ea.A)("arrow-down",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);var er=i(11860);let el=(0,ea.A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]),en=(0,ea.A)("chevrons-left",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]]),eo=(0,ea.A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),ec=(0,ea.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),ed=(0,ea.A)("chevrons-right",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]]);function em({data:e=[],columns:a=[],loading:i=!1,emptyMessage:r="Nessun dato disponibile",onFilteredDataChange:o,renderRow:c,className:d,pagination:x=!0,defaultRowsPerPage:h=25}){let[v,g]=(0,t.useState)({key:null,direction:null}),[b,j]=(0,t.useState)({}),[f,N]=(0,t.useState)({}),[y,_]=(0,t.useState)(0),[C,w]=(0,t.useState)(h),z=a=>[...new Set(e.map(e=>e[a]).filter(Boolean))].sort(),A=(0,t.useMemo)(()=>{let a=[...e];return Object.entries(b).forEach(([e,i])=>{!i.value||Array.isArray(i.value)&&0===i.value.length||"string"==typeof i.value&&""===i.value.trim()||(a=a.filter(a=>{let s=a[e];if("select"===i.type)return(Array.isArray(i.value)?i.value:[i.value]).includes(s);if("text"===i.type){let e=i.value.toLowerCase(),a=String(s||"").toLowerCase();return"equals"===i.operator?a===e:a.includes(e)}if("number"===i.type){let e=parseFloat(s),a=parseFloat(i.value);if(isNaN(e)||isNaN(a))return!1;switch(i.operator){case"equals":default:return e===a;case"gt":return e>a;case"lt":return e<a;case"gte":return e>=a;case"lte":return e<=a}}return!0}))}),v.key&&v.direction&&a.sort((e,a)=>{let i=e[v.key],s=a[v.key];if(null==i&&null==s)return 0;if(null==i)return"asc"===v.direction?-1:1;if(null==s)return"asc"===v.direction?1:-1;let t=parseFloat(i),r=parseFloat(s),l=!isNaN(t)&&!isNaN(r),n=0;return n=l?t-r:String(i).localeCompare(String(s)),"asc"===v.direction?n:-n}),a},[e,b,v]),k=(0,t.useMemo)(()=>{if(!x)return A;let e=y*C,a=e+C;return A.slice(e,a)},[A,y,C,x]),S=Math.ceil(A.length/C),E=y*C+1,I=Math.min((y+1)*C,A.length),F=e=>{let i=a.find(a=>a.field===e);i?.disableSort||g(a=>{if(a.key===e){if("asc"===a.direction)return{key:e,direction:"desc"};if("desc"===a.direction)return{key:null,direction:null}}return{key:e,direction:"asc"}})},$=(e,a)=>{j(i=>({...i,[e]:{...i[e],...a}}))},O=e=>{j(a=>{let i={...a};return delete i[e],i})},D=e=>v.key!==e?(0,s.jsx)(ei,{className:"h-3 w-3"}):"asc"===v.direction?(0,s.jsx)(es,{className:"h-3 w-3"}):"desc"===v.direction?(0,s.jsx)(et,{className:"h-3 w-3"}):(0,s.jsx)(ei,{className:"h-3 w-3"}),R=Object.keys(b).length>0;return i?(0,s.jsx)(l.Zp,{className:d,children:(0,s.jsx)(l.Wu,{className:"p-6",children:(0,s.jsx)("div",{className:"text-center",children:"Caricamento..."})})}):(0,s.jsxs)("div",{className:d,children:[R&&(0,s.jsxs)("div",{className:"mb-4 flex flex-wrap gap-2 items-center",children:[(0,s.jsx)("span",{className:"text-sm text-muted-foreground",children:"Filtri attivi:"}),Object.entries(b).map(([e,i])=>{let t=a.find(a=>a.field===e);if(!t)return null;let r=Array.isArray(i.value)?i.value.join(", "):String(i.value);return(0,s.jsxs)(m.E,{variant:"secondary",className:"gap-1",children:[t.headerName,": ",r,(0,s.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-auto p-0 hover:bg-transparent",onClick:()=>O(e),children:(0,s.jsx)(er.A,{className:"h-3 w-3"})})]},e)}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>{j({})},className:"h-6 px-2 text-xs",children:"Pulisci tutti"})]}),(0,s.jsx)(l.Zp,{children:(0,s.jsx)(l.Wu,{className:"p-0",children:(0,s.jsxs)(u.XI,{children:[(0,s.jsx)(u.A0,{children:(0,s.jsx)(u.Hj,{className:"bg-mariner-50 hover:bg-mariner-50",children:a.map(a=>(0,s.jsx)(u.nd,{className:(0,Q.cn)("font-semibold text-mariner-900 border-b border-mariner-200","center"===a.align&&"text-center","right"===a.align&&"text-right"),style:{width:a.width,...a.headerStyle},children:a.renderHeader?a.renderHeader():(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,s.jsx)("span",{className:"truncate",children:a.headerName}),(0,s.jsxs)("div",{className:"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:[!a.disableSort&&(0,s.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-4 w-4 p-0 hover:bg-mariner-100",onClick:()=>F(a.field),children:D(a.field)}),!a.disableFilter&&(0,s.jsxs)(T,{open:f[a.field],onOpenChange:e=>N(i=>({...i,[a.field]:e})),children:[(0,s.jsx)(L,{asChild:!0,children:(0,s.jsx)(n.$,{variant:"ghost",size:"sm",className:(0,Q.cn)("h-4 w-4 p-0 hover:bg-mariner-100",b[a.field]&&"text-mariner-600 opacity-100"),children:(0,s.jsx)(el,{className:"h-2.5 w-2.5"})})}),(0,s.jsx)(ee,{className:"w-64",align:"start",children:(0,s.jsx)(ex,{column:a,data:e,currentFilter:b[a.field],onFilterChange:e=>$(a.field,e),onClearFilter:()=>O(a.field),getUniqueValues:()=>z(a.field)})})]})]})]}),b[a.field]&&(0,s.jsx)("div",{className:"absolute -top-1 -right-1 h-2 w-2 bg-mariner-600 rounded-full"})]})},a.field))})}),(0,s.jsx)(u.BF,{children:k.length>0?k.map((e,i)=>c?c(e,y*C+i):(0,s.jsx)(u.Hj,{className:"hover:bg-mariner-50 border-b border-mariner-100",children:a.map(a=>(0,s.jsx)(u.nA,{className:(0,Q.cn)("py-2 px-4","center"===a.align&&"text-center","right"===a.align&&"text-right"),style:a.cellStyle,children:a.renderCell?a.renderCell(e):e[a.field]},a.field))},i)):(0,s.jsx)(u.Hj,{children:(0,s.jsx)(u.nA,{colSpan:a.length,className:"text-center py-8 text-muted-foreground",children:r})})})]})})}),x&&A.length>0&&(0,s.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"text-sm text-muted-foreground",children:"Righe per pagina:"}),(0,s.jsxs)(p.l6,{value:C.toString(),onValueChange:e=>{w(Number(e)),_(0)},children:[(0,s.jsx)(p.bq,{className:"w-20",children:(0,s.jsx)(p.yv,{})}),(0,s.jsxs)(p.gC,{children:[(0,s.jsx)(p.eb,{value:"10",children:"10"}),(0,s.jsx)(p.eb,{value:"25",children:"25"}),(0,s.jsx)(p.eb,{value:"50",children:"50"}),(0,s.jsx)(p.eb,{value:"100",children:"100"}),(0,s.jsx)(p.eb,{value:A.length.toString(),children:"Tutto"})]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"text-sm text-muted-foreground",children:A.length>0?`${E}-${I} di ${A.length}`:"0 di 0"}),(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>_(0),disabled:0===y,className:"h-8 w-8 p-0",children:(0,s.jsx)(en,{className:"h-4 w-4"})}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>_(e=>Math.max(0,e-1)),disabled:0===y,className:"h-8 w-8 p-0",children:(0,s.jsx)(eo,{className:"h-4 w-4"})}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>_(e=>Math.min(S-1,e+1)),disabled:y>=S-1,className:"h-8 w-8 p-0",children:(0,s.jsx)(ec,{className:"h-4 w-4"})}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>_(S-1),disabled:y>=S-1,className:"h-8 w-8 p-0",children:(0,s.jsx)(ed,{className:"h-4 w-4"})})]})]})]})]})}function ex({column:e,currentFilter:a,onFilterChange:i,onClearFilter:r,getUniqueValues:l}){let[o,c]=(0,t.useState)(a?.value||""),[d,m]=(0,t.useState)(a?.operator||"contains"),u=l(),v="number"!==e.dataType&&u.length<=20,g="number"===e.dataType,b=()=>{v?i({type:"select",value:Array.isArray(o)?o:[o]}):g?i({type:"number",value:o,operator:d}):i({type:"text",value:o,operator:d})};return(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"font-medium text-sm",children:["Filtra ",e.headerName]}),v?(0,s.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:u.map(e=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(x.S,{id:`filter-${e}`,checked:Array.isArray(o)?o.includes(e):o===e,onCheckedChange:a=>{Array.isArray(o)?c(a?[...o,e]:o.filter(a=>a!==e)):c(a?[e]:[])}}),(0,s.jsx)("label",{htmlFor:`filter-${e}`,className:"text-sm",children:e})]},e))}):(0,s.jsxs)("div",{className:"space-y-2",children:[g&&(0,s.jsxs)(p.l6,{value:d,onValueChange:m,children:[(0,s.jsx)(p.bq,{children:(0,s.jsx)(p.yv,{})}),(0,s.jsxs)(p.gC,{children:[(0,s.jsx)(p.eb,{value:"equals",children:"Uguale a"}),(0,s.jsx)(p.eb,{value:"gt",children:"Maggiore di"}),(0,s.jsx)(p.eb,{value:"lt",children:"Minore di"}),(0,s.jsx)(p.eb,{value:"gte",children:"Maggiore o uguale"}),(0,s.jsx)(p.eb,{value:"lte",children:"Minore o uguale"})]})]}),!g&&(0,s.jsxs)(p.l6,{value:d,onValueChange:m,children:[(0,s.jsx)(p.bq,{children:(0,s.jsx)(p.yv,{})}),(0,s.jsxs)(p.gC,{children:[(0,s.jsx)(p.eb,{value:"contains",children:"Contiene"}),(0,s.jsx)(p.eb,{value:"equals",children:"Uguale a"})]})]}),(0,s.jsx)(h.p,{placeholder:`Cerca ${e.headerName.toLowerCase()}...`,value:o,onChange:e=>c(e.target.value),onKeyDown:e=>"Enter"===e.key&&b()})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(n.$,{size:"sm",onClick:b,children:"Applica"}),(0,s.jsx)(n.$,{size:"sm",variant:"outline",onClick:r,children:"Pulisci"})]})]})}var eu=i(99270);let eh=(0,ea.A)("square-check-big",[["path",{d:"M21 10.656V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.344",key:"2acyp4"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),ep=(0,ea.A)("square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);var ev=i(76242);function eg({cavi:e=[],onFilteredDataChange:a,loading:i=!1,selectionEnabled:r=!1,onSelectionToggle:o}){let[c,d]=(0,t.useState)(""),[m,x]=(0,t.useState)("contains"),u=(0,t.useCallback)(e=>e?e.toString().toLowerCase().trim():"",[]),v=(0,t.useCallback)(e=>{let a=e.match(/^([A-Z]+)(\d+)([A-Z]*)$/);return a?{prefix:a[1],number:a[2],suffix:a[3]||""}:{prefix:"",number:e,suffix:""}},[]),g=(0,t.useCallback)((e,a,i)=>{let s=u(a);if(!s)return!0;let t=u(e.id_cavo),{prefix:r,number:l,suffix:n}=v(e.id_cavo||""),o=u(e.tipologia),c=u(e.formazione||e.sezione),d=u(e.utility),m=u(e.sistema),x=u(e.da||e.ubicazione_partenza),h=u(e.a||e.ubicazione_arrivo),p=u(e.utenza_partenza),g=u(e.utenza_arrivo),b=[t,r,l,n,o,c,d,m,x,h,p,g,u(e.id_bobina),"BOBINA_VUOTA"===e.id_bobina?"bobina vuota":null===e.id_bobina?"":u(e.id_bobina)],j=[{value:e.metri_teorici,name:"metri_teorici"},{value:e.metratura_reale||e.metri_posati,name:"metratura_reale"},{value:parseFloat(c),name:"formazione"}],f=s.match(/^([><=]+)(\d+(?:\.\d+)?)$/);if(f){let e=f[1],a=parseFloat(f[2]);return j.some(i=>{if(null==i.value||isNaN(i.value))return!1;switch(e){case">":return i.value>a;case">=":return i.value>=a;case"<":return i.value<a;case"<=":return i.value<=a;case"=":return i.value===a;default:return!1}})}let N=parseFloat(s);return!!(!isNaN(N)&&j.some(e=>null!=e.value&&!isNaN(e.value)&&e.value===N))||(i?b.some(e=>e===s):b.some(e=>e.includes(s)))},[u,v]);(0,t.useCallback)(()=>{let i=e;if(c.trim()){let e=c.split(",").map(e=>e.trim()).filter(e=>e.length>0);i="equals"===m?1===e.length?i.filter(a=>g(a,e[0],!0)):i.filter(a=>e.every(e=>g(a,e,!0))):i.filter(a=>e.some(e=>g(a,e,!1)))}a?.(i)},[c,m,e,g]);let b=e=>{d(e)};return(0,s.jsx)(l.Zp,{className:"mb-4 shadow-sm border-slate-200",children:(0,s.jsxs)(l.Wu,{className:"p-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,s.jsxs)("div",{className:"flex-1 relative",children:[(0,s.jsx)(eu.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,s.jsx)(h.p,{placeholder:"Ricerca intelligente cavi (ID, sistema, metri, stato...)...",value:c,onChange:e=>b(e.target.value),disabled:i,className:"pl-10 pr-12 h-10 border-slate-300 focus:border-blue-500 focus:ring-blue-500"}),c&&(0,s.jsx)(n.$,{variant:"ghost",size:"sm",className:"absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-slate-100",onClick:()=>{d(""),x("contains")},children:(0,s.jsx)(er.A,{className:"h-3 w-3"})})]}),(0,s.jsx)(ev.Bc,{children:(0,s.jsxs)(ev.m_,{children:[(0,s.jsx)(ev.k$,{asChild:!0,children:(0,s.jsx)("div",{className:"w-36",children:(0,s.jsxs)(p.l6,{value:m,onValueChange:e=>x(e),children:[(0,s.jsx)(p.bq,{className:"h-10 border-slate-300",children:(0,s.jsx)(p.yv,{})}),(0,s.jsxs)(p.gC,{children:[(0,s.jsx)(p.eb,{value:"contains",children:"Contiene"}),(0,s.jsx)(p.eb,{value:"equals",children:"Uguale a"})]})]})})}),(0,s.jsx)(ev.ZI,{children:(0,s.jsx)("p",{children:"Modalit\xe0 di ricerca: contiene o corrispondenza esatta"})})]})}),o&&(0,s.jsx)(ev.Bc,{children:(0,s.jsxs)(ev.m_,{children:[(0,s.jsx)(ev.k$,{asChild:!0,children:(0,s.jsxs)(n.$,{variant:r?"default":"outline",size:"sm",onClick:o,className:"flex items-center gap-2 transition-all duration-200 hover:scale-105 font-normal hover:font-bold",children:[r?(0,s.jsx)(eh,{className:"h-4 w-4"}):(0,s.jsx)(ep,{className:"h-4 w-4"}),r?"Disabilita Selezione":"Abilita Selezione"]})}),(0,s.jsx)(ev.ZI,{children:(0,s.jsx)("p",{children:r?"Disabilita la selezione multipla":"Abilita la selezione multipla"})})]})})]}),c&&(0,s.jsx)("div",{className:"mt-3 p-2 bg-slate-50 rounded-lg border border-slate-200",children:(0,s.jsxs)("div",{className:"text-xs text-slate-600 flex items-center gap-2",children:[(0,s.jsx)("span",{children:"\uD83D\uDCA1"}),(0,s.jsx)("span",{children:"Suggerimenti: Usa virgole per ricerche multiple • Operatori: >100, <=50 per numeri"})]})})]})})}function eb({text:e,maxLength:a=20,className:i=""}){let[r,l]=(0,t.useState)(!1),[n,o]=(0,t.useState)({x:0,y:0});if(!e)return(0,s.jsx)("span",{className:"text-gray-400",children:"-"});let c=e.length>a,d=c?`${e.substring(0,a)}...`:e;return c?(0,s.jsxs)("div",{className:"relative inline-block",children:[(0,s.jsx)("span",{className:`cursor-help ${i}`,style:{textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden",maxWidth:"100%",display:"inline-block"},onMouseEnter:e=>{o({x:e.clientX,y:e.clientY}),l(!0)},onMouseMove:e=>{o({x:e.clientX,y:e.clientY})},onMouseLeave:()=>l(!1),title:e,children:d}),r&&(0,s.jsxs)("div",{className:"fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg pointer-events-none",style:{top:n.y-40,left:n.x-150,maxWidth:"300px",wordWrap:"break-word",whiteSpace:"normal"},children:[e,(0,s.jsx)("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0",style:{borderLeft:"5px solid transparent",borderRight:"5px solid transparent",borderTop:"5px solid #1f2937"}})]})]}):(0,s.jsx)("span",{className:i,children:e})}var ej=i(78272),ef=i(96474),eN=i(96882);let ey=(0,ea.A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]),e_=(0,ea.A)("unlink",[["path",{d:"m18.84 12.25 1.72-1.71h-.02a5.004 5.004 0 0 0-.12-7.07 5.006 5.006 0 0 0-6.95 0l-1.72 1.71",key:"yqzxt4"}],["path",{d:"m5.17 11.75-1.71 1.71a5.004 5.004 0 0 0 .12 7.07 5.006 5.006 0 0 0 6.95 0l1.71-1.71",key:"4qinb0"}],["line",{x1:"8",x2:"8",y1:"2",y2:"5",key:"1041cp"}],["line",{x1:"2",x2:"5",y1:"8",y2:"8",key:"14m1p5"}],["line",{x1:"16",x2:"16",y1:"19",y2:"22",key:"rzdirn"}],["line",{x1:"19",x2:"22",y1:"16",y2:"16",key:"ox905f"}]]),eC=(0,ea.A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var ew=i(5336),ez=i(31158),eA=i(41312),ek=i(10022),eS=i(19080),eE=i(88233),eI=i(63503),eF=i(80013),e$=i(34729),eO=i(19169),eD=i(41862),eR=i(43649),eT=i(26134),eP="AlertDialog",[eM,eL]=(0,b.A)(eP,[eT.Hs]),eB=(0,eT.Hs)(),eU=e=>{let{__scopeAlertDialog:a,...i}=e,t=eB(a);return(0,s.jsx)(eT.bL,{...t,...i,modal:!0})};eU.displayName=eP,t.forwardRef((e,a)=>{let{__scopeAlertDialog:i,...t}=e,r=eB(i);return(0,s.jsx)(eT.l9,{...r,...t,ref:a})}).displayName="AlertDialogTrigger";var eG=e=>{let{__scopeAlertDialog:a,...i}=e,t=eB(a);return(0,s.jsx)(eT.ZL,{...t,...i})};eG.displayName="AlertDialogPortal";var eq=t.forwardRef((e,a)=>{let{__scopeAlertDialog:i,...t}=e,r=eB(i);return(0,s.jsx)(eT.hJ,{...r,...t,ref:a})});eq.displayName="AlertDialogOverlay";var eV="AlertDialogContent",[eJ,eZ]=eM(eV),eH=(0,A.Dc)("AlertDialogContent"),eW=t.forwardRef((e,a)=>{let{__scopeAlertDialog:i,children:r,...l}=e,n=eB(i),o=t.useRef(null),c=(0,g.s)(a,o),d=t.useRef(null);return(0,s.jsx)(eT.G$,{contentName:eV,titleName:eK,docsSlug:"alert-dialog",children:(0,s.jsx)(eJ,{scope:i,cancelRef:d,children:(0,s.jsxs)(eT.UC,{role:"alertdialog",...n,...l,ref:c,onOpenAutoFocus:(0,v.m)(l.onOpenAutoFocus,e=>{e.preventDefault(),d.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsx)(eH,{children:r}),(0,s.jsx)(e4,{contentRef:o})]})})})});eW.displayName=eV;var eK="AlertDialogTitle",eX=t.forwardRef((e,a)=>{let{__scopeAlertDialog:i,...t}=e,r=eB(i);return(0,s.jsx)(eT.hE,{...r,...t,ref:a})});eX.displayName=eK;var eY="AlertDialogDescription",eQ=t.forwardRef((e,a)=>{let{__scopeAlertDialog:i,...t}=e,r=eB(i);return(0,s.jsx)(eT.VY,{...r,...t,ref:a})});eQ.displayName=eY;var e0=t.forwardRef((e,a)=>{let{__scopeAlertDialog:i,...t}=e,r=eB(i);return(0,s.jsx)(eT.bm,{...r,...t,ref:a})});e0.displayName="AlertDialogAction";var e1="AlertDialogCancel",e2=t.forwardRef((e,a)=>{let{__scopeAlertDialog:i,...t}=e,{cancelRef:r}=eZ(e1,i),l=eB(i),n=(0,g.s)(a,r);return(0,s.jsx)(eT.bm,{...l,...t,ref:n})});e2.displayName=e1;var e4=({contentRef:e})=>{let a=`\`${eV}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${eV}\` by passing a \`${eY}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${eV}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return t.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(a)},[a,e]),null};let e5=t.forwardRef(({className:e,...a},i)=>(0,s.jsx)(eq,{className:(0,Q.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...a,ref:i}));e5.displayName=eq.displayName;let e3=t.forwardRef(({className:e,...a},i)=>(0,s.jsxs)(eG,{children:[(0,s.jsx)(e5,{}),(0,s.jsx)(eW,{ref:i,className:(0,Q.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...a})]}));e3.displayName=eW.displayName;let e6=({className:e,...a})=>(0,s.jsx)("div",{className:(0,Q.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...a});e6.displayName="AlertDialogHeader";let e7=({className:e,...a})=>(0,s.jsx)("div",{className:(0,Q.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...a});e7.displayName="AlertDialogFooter";let e8=t.forwardRef(({className:e,...a},i)=>(0,s.jsx)(eX,{ref:i,className:(0,Q.cn)("text-lg font-semibold",e),...a}));e8.displayName=eX.displayName;let e9=t.forwardRef(({className:e,...a},i)=>(0,s.jsx)(eQ,{ref:i,className:(0,Q.cn)("text-sm text-muted-foreground",e),...a}));e9.displayName=eQ.displayName;let ae=t.forwardRef(({className:e,...a},i)=>(0,s.jsx)(e0,{ref:i,className:(0,Q.cn)((0,n.r)(),e),...a}));ae.displayName=e0.displayName;let aa=t.forwardRef(({className:e,...a},i)=>(0,s.jsx)(e2,{ref:i,className:(0,Q.cn)((0,n.r)({variant:"outline"}),"mt-2 sm:mt-0",e),...a}));aa.displayName=e2.displayName;let ai={danger:{icon:i(35071).A,iconColor:"text-red-600",buttonClass:"bg-red-600 hover:bg-red-700 focus:ring-red-500",borderClass:"border-l-red-500"},warning:{icon:eR.A,iconColor:"text-orange-600",buttonClass:"bg-orange-600 hover:bg-orange-700 focus:ring-orange-500",borderClass:"border-l-orange-500"},info:{icon:eN.A,iconColor:"text-blue-600",buttonClass:"bg-blue-600 hover:bg-blue-700 focus:ring-blue-500",borderClass:"border-l-blue-500"},success:{icon:ew.A,iconColor:"text-green-600",buttonClass:"bg-green-600 hover:bg-green-700 focus:ring-green-500",borderClass:"border-l-green-500"}};function as({isOpen:e,onClose:a,onConfirm:i,title:r,description:l,confirmText:n="Conferma",cancelText:o="Annulla",variant:c="info",isLoading:d=!1,icon:m}){let[x,u]=(0,t.useState)(!1),h=ai[c],p=m||h.icon,v=async()=>{try{u(!0);let e=i();e instanceof Promise&&await e,await new Promise(e=>setTimeout(e,300))}catch(e){console.error("Errore durante conferma:",e),u(!1);return}finally{u(!1)}},g=d||x;return(0,s.jsx)(eU,{open:e,onOpenChange:a,children:(0,s.jsxs)(e3,{className:`border-l-4 ${h.borderClass} max-w-md`,children:[(0,s.jsxs)(e6,{children:[(0,s.jsxs)(e8,{className:"flex items-center space-x-3",children:[(0,s.jsx)(p,{className:`w-6 h-6 ${h.iconColor} flex-shrink-0`}),(0,s.jsx)("span",{className:"text-lg font-semibold text-slate-900",children:r})]}),(0,s.jsx)(e9,{className:"text-base text-slate-600 leading-relaxed mt-2",children:l})]}),(0,s.jsxs)(e7,{className:"gap-3 mt-6",children:[(0,s.jsx)(aa,{onClick:a,disabled:g,className:"px-6 py-2 border-slate-300 text-slate-700 hover:bg-slate-50 focus:ring-slate-500",children:o}),(0,s.jsx)(ae,{onClick:v,disabled:g,className:`px-6 py-2 text-white font-medium transition-all duration-200 ${h.buttonClass} disabled:opacity-50 disabled:cursor-not-allowed`,children:g?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(eD.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Elaborazione..."]}):n})]})]})})}function at({isOpen:e,onClose:a,onConfirm:i,cavo:r}){let[l,n]=(0,t.useState)(!1),o=async()=>{if(r){n(!0);try{await i(r.id_cavo),a()}catch(e){console.error("Errore durante scollegamento:",e)}finally{n(!1)}}};return r?(0,s.jsx)(as,{isOpen:e,onClose:a,onConfirm:o,title:"Conferma Scollegamento Cavo",description:`Sei sicuro di voler scollegare il cavo ${r.id_cavo}? Questa azione potrebbe influenzare lo stato di altri componenti e dovr\xe0 essere ricollegato manualmente.`,confirmText:"Scollega",cancelText:"Annulla",variant:"warning",isLoading:l,icon:(0,s.jsx)(er.A,{className:"w-6 h-6"})}):null}function ar({isOpen:e,onClose:a,onGenerate:i,cavo:r}){let[l,o]=(0,t.useState)(!1),[c,d]=(0,t.useState)({cavoId:"",fileName:"",format:"standard",includeTestData:!0,includePhotos:!1,emailRecipient:"",notes:""});(0,t.useState)(()=>{r&&d(e=>({...e,cavoId:r.id_cavo,fileName:`Certificato_${r.id_cavo}_${new Date().toISOString().split("T")[0]}.pdf`}))},[r]);let m=async()=>{if(r){o(!0);try{await i(c),a()}catch(e){console.error("Errore durante generazione PDF:",e)}finally{o(!1)}}};return r?(0,s.jsx)(eI.lG,{open:e,onOpenChange:a,children:(0,s.jsxs)(eI.Cf,{className:"max-w-2xl border-l-4 border-l-blue-500",children:[(0,s.jsxs)(eI.c7,{className:"bg-gradient-to-r from-blue-50 to-transparent p-6 -m-6 mb-4",children:[(0,s.jsxs)(eI.L3,{className:"flex items-center gap-3 text-xl",children:[(0,s.jsx)(ek.A,{className:"w-6 h-6 text-blue-600"}),"Genera Certificato per Cavo ",r.id_cavo]}),(0,s.jsx)(eI.rr,{className:"text-base text-slate-600 mt-2",children:"Configura le opzioni per la generazione del certificato PDF"})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-slate-50 p-4 rounded-lg border",children:[(0,s.jsx)("h4",{className:"font-medium text-slate-900 mb-2",children:"Informazioni Cavo"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-slate-600",children:"ID:"}),(0,s.jsx)("span",{className:"ml-2 font-mono",children:r.id_cavo})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-slate-600",children:"Sistema:"}),(0,s.jsx)("span",{className:"ml-2",children:r.sistema||"N/A"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-slate-600",children:"Tipologia:"}),(0,s.jsx)("span",{className:"ml-2",children:r.tipologia||"N/A"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-slate-600",children:"Stato:"}),(0,s.jsx)("span",{className:"ml-2",children:r.stato||"N/A"})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"fileName",children:"Nome File *"}),(0,s.jsx)(h.p,{id:"fileName",value:c.fileName,onChange:e=>d(a=>({...a,fileName:e.target.value})),placeholder:"Certificato_C001_2024-01-01.pdf",className:"font-mono text-sm"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"format",children:"Formato Certificato"}),(0,s.jsxs)(p.l6,{value:c.format,onValueChange:e=>d(a=>({...a,format:e})),children:[(0,s.jsx)(p.bq,{children:(0,s.jsx)(p.yv,{})}),(0,s.jsxs)(p.gC,{children:[(0,s.jsx)(p.eb,{value:"standard",children:"\uD83D\uDCC4 Standard CEI 64-8"}),(0,s.jsx)(p.eb,{value:"detailed",children:"\uD83D\uDCCB Dettagliato con Misure"}),(0,s.jsx)(p.eb,{value:"summary",children:"\uD83D\uDCDD Riassunto Esecutivo"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"emailRecipient",children:"Email Destinatario (Opzionale)"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(eO.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400"}),(0,s.jsx)(h.p,{id:"emailRecipient",type:"email",value:c.emailRecipient,onChange:e=>d(a=>({...a,emailRecipient:e.target.value})),placeholder:"<EMAIL>",className:"pl-10"})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(eF.J,{children:"Opzioni Aggiuntive"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,s.jsx)("input",{type:"checkbox",checked:c.includeTestData,onChange:e=>d(a=>({...a,includeTestData:e.target.checked})),className:"rounded border-slate-300 text-blue-600 focus:ring-blue-500"}),(0,s.jsx)("span",{className:"text-sm text-slate-700",children:"Includi Dati di Collaudo"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,s.jsx)("input",{type:"checkbox",checked:c.includePhotos,onChange:e=>d(a=>({...a,includePhotos:e.target.checked})),className:"rounded border-slate-300 text-blue-600 focus:ring-blue-500"}),(0,s.jsx)("span",{className:"text-sm text-slate-700",children:"Includi Foto Installazione"})]})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"notes",children:"Note Aggiuntive"}),(0,s.jsx)(e$.T,{id:"notes",value:c.notes,onChange:e=>d(a=>({...a,notes:e.target.value})),placeholder:"Note o commenti da includere nel certificato...",rows:3,className:"resize-none"})]})]}),(0,s.jsxs)(eI.Es,{className:"gap-3 pt-6 border-t",children:[(0,s.jsxs)(n.$,{variant:"outline",onClick:a,disabled:l,className:"px-6",children:[(0,s.jsx)(er.A,{className:"w-4 h-4 mr-2"}),"Annulla"]}),(0,s.jsx)(n.$,{onClick:m,disabled:l||!c.fileName.trim(),className:"px-6 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700",children:l?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(eD.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Generazione..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(ez.A,{className:"w-4 h-4 mr-2"}),"Genera PDF"]})})]})]})}):null}function al({isOpen:e,onClose:a,cavo:i,missingRequirements:t}){return i?(0,s.jsx)(eI.lG,{open:e,onOpenChange:a,children:(0,s.jsxs)(eI.Cf,{className:"max-w-md border-l-4 border-l-red-500",children:[(0,s.jsxs)(eI.c7,{className:"bg-gradient-to-r from-red-50 to-transparent p-6 -m-6 mb-4",children:[(0,s.jsxs)(eI.L3,{className:"flex items-center gap-3 text-xl",children:[(0,s.jsx)(eR.A,{className:"w-6 h-6 text-red-600"}),"Impossibile Certificare Cavo"]}),(0,s.jsxs)(eI.rr,{className:"text-base text-slate-600 mt-2",children:["Il cavo ",i.id_cavo," non pu\xf2 essere certificato nel suo stato attuale"]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)(o.Fc,{variant:"destructive",className:"border-red-200 bg-red-50",children:[(0,s.jsx)(eR.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{className:"font-medium",children:"Requisiti mancanti per la certificazione:"})]}),(0,s.jsx)("ul",{className:"space-y-2",children:t.map((e,a)=>(0,s.jsxs)("li",{className:"flex items-center gap-2 text-sm text-slate-700",children:[(0,s.jsx)(er.A,{className:"w-4 h-4 text-red-500 flex-shrink-0"}),e]},a))}),(0,s.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-200",children:[(0,s.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"\uD83D\uDCA1 Suggerimento"}),(0,s.jsx)("p",{className:"text-sm text-blue-800",children:"Completa tutti i requisiti sopra elencati prima di procedere con la certificazione. Puoi utilizzare le azioni disponibili nella tabella per aggiornare lo stato del cavo."})]})]}),(0,s.jsx)(eI.Es,{className:"pt-6 border-t",children:(0,s.jsxs)(n.$,{onClick:a,className:"px-6 bg-slate-600 hover:bg-slate-700",children:[(0,s.jsx)(eC,{className:"w-4 h-4 mr-2"}),"Ho Capito"]})})]})}):null}var an=i(70524),ao=i(51215),ac=i(13861),ad=i(63143),am=i(86561),ax=i(70615);function au({isOpen:e,position:a,cavo:i,isSelected:r,hasMultipleSelection:l,totalSelectedCount:o,onAction:c,onClose:d}){let m=(0,t.useRef)(null);if(!e||!i)return null;let x=e=>{c(i,e),d()},u=[{section:"Gestione Cavi",items:[{icon:ac.A,label:"Visualizza Dettagli",action:"view_details"},{icon:ad.A,label:"Modifica Cavo",action:"edit"},{icon:eE.A,label:"Elimina Cavo",action:"delete",destructive:!0},{icon:ef.A,label:"Aggiungi Nuovo Cavo",action:"add_new"}]},{section:"Comandi",items:[{icon:ef.A,label:"Inserisci Metri",action:"insert_meters"},{icon:ey,label:"Gestisci Collegamenti",action:"manage_connections"},{icon:eS.A,label:"Gestisci Bobina",action:"manage_reel"},{icon:am.A,label:"Certifica Cavo",action:"create_certificate"}]},{section:"Generale",items:[{icon:ax.A,label:"Copia ID",action:"copy_id"},{icon:ek.A,label:"Copia Dettagli",action:"copy_details"},{icon:ez.A,label:"Esporta Dati",action:"export_data"}]}],h={...a};return(0,ao.createPortal)((0,s.jsxs)("div",{ref:m,className:"fixed z-[9999] bg-white border-2 border-gray-300 rounded-lg shadow-2xl py-2 min-w-[220px] max-w-[300px]",style:{left:h.x,top:h.y,boxShadow:"0 10px 25px rgba(0, 0, 0, 0.2)"},children:[(0,s.jsxs)("div",{className:"px-3 py-2 border-b border-gray-100",children:[(0,s.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:["Cavo: ",i.id_cavo]}),r&&(0,s.jsxs)("div",{className:"text-xs text-blue-600",children:["Selezionato ",l?`(${o} totali)`:""]})]}),u.map((e,a)=>(0,s.jsxs)("div",{children:[a>0&&(0,s.jsx)("hr",{className:"my-1 border-gray-200"}),(0,s.jsxs)("div",{className:"px-2 py-1",children:[(0,s.jsx)("div",{className:"text-xs font-medium text-gray-500 px-2 py-1",children:e.section}),e.items.map(e=>(0,s.jsxs)(n.$,{variant:"ghost",size:"sm",className:`w-full justify-start text-left h-8 px-2 ${e.destructive?"text-red-600 hover:text-red-700 hover:bg-red-50":"text-gray-700 hover:text-gray-900 hover:bg-gray-50"}`,onClick:()=>x(e.action),children:[(0,s.jsx)(e.icon,{className:"w-4 h-4 mr-2"}),e.label]},e.action))]})]},e.section))]}),document.body)}function ah({cavi:e=[],loading:a=!1,selectionEnabled:i=!1,selectedCavi:r=[],onSelectionChange:l,onStatusAction:o,onContextMenuAction:c,onDisconnectCable:d,onGeneratePDF:h,onCertifyCable:p}){let[v,g]=(0,t.useState)(e),[b,j]=(0,t.useState)(e),[f,N]=(0,t.useState)(i),[y,_]=(0,t.useState)("id_cavo"),[C,w]=(0,t.useState)("asc"),[z,A]=(0,t.useState)({disconnect:{isOpen:!1,cavo:null},generatePDF:{isOpen:!1,cavo:null},certificationError:{isOpen:!1,cavo:null,missingRequirements:[]}}),[k,S]=(0,t.useState)({isOpen:!1,position:{x:0,y:0},cavo:null}),[E,I]=(0,t.useState)(1),[F,$]=(0,t.useState)(25),[O,D]=(0,t.useState)(!1),R=(0,an.E)(),T=(e,a,i=[])=>{A(s=>({...s,[e]:{isOpen:!0,cavo:a,missingRequirements:i}}))},P=e=>{A(a=>({...a,[e]:{isOpen:!1,cavo:null,missingRequirements:[]}}))},M=e=>{y===e?w(e=>"asc"===e?"desc":"asc"):(_(e),w("asc"))},L=(0,t.useMemo)(()=>[...v].sort((e,a)=>{let i=e[y],s=a[y];return(("metri_teorici"===y||"metri_posati"===y)&&(i=parseFloat(i)||0,s=parseFloat(s)||0),"string"==typeof i&&(i=i.toLowerCase()),"string"==typeof s&&(s=s.toLowerCase()),i<s)?"asc"===C?-1:1:i>s?"asc"===C?1:-1:0}),[v,y,C]),B=(0,t.useMemo)(()=>{let e=(E-1)*F,a=e+F,i=L.slice(e,a),s=Math.ceil(L.length/F),t=E<s,r=E>1;return{cavi:i,totalItems:L.length,totalPages:s,hasNextPage:t,hasPrevPage:r,startIndex:e+1,endIndex:Math.min(a,L.length)}},[L,E,F]),U=async e=>{try{d&&(await d(e),R.cavoDisconnected(e))}catch(e){R.error("Errore Scollegamento","Impossibile scollegare il cavo. Riprova.")}},G=async e=>{try{h&&(await h(e),R.pdfGenerated(e.fileName,e.cavoId))}catch(e){R.error("Errore Generazione PDF","Impossibile generare il certificato. Riprova.")}},q=async e=>{let a=[];if(e.metri_posati&&0!==parseFloat(e.metri_posati)||a.push("Metri posati non inseriti"),"Collegato"!==e.stato&&a.push("Cavo non collegato"),e.data_installazione||a.push("Data installazione mancante"),a.length>0)return void T("certificationError",e,a);try{p&&(R.actionInProgress("Certificazione",e.id_cavo),await p(e.id_cavo),R.success("Cavo Certificato",`Il cavo ${e.id_cavo} \xe8 stato certificato con successo.`))}catch(a){R.certificationError(e.id_cavo,"Errore durante il processo di certificazione")}},V=e=>{I(Math.max(1,Math.min(e,B.totalPages)))},J=()=>{l?.([])},Z=B.cavi.length>0&&B.cavi.every(e=>r.includes(e.id_cavo)),H=B.cavi.some(e=>r.includes(e.id_cavo)),W=({field:e,children:a,className:i=""})=>(0,s.jsx)(ev.Bc,{children:(0,s.jsxs)(ev.m_,{children:[(0,s.jsx)(ev.k$,{asChild:!0,children:(0,s.jsxs)("button",{onClick:()=>M(e),className:`flex items-center gap-2 font-medium text-left hover:text-blue-600 transition-colors ${i}`,children:[a,y===e?"asc"===C?(0,s.jsx)(es,{className:"w-4 h-4 text-blue-600"}):(0,s.jsx)(et,{className:"w-4 h-4 text-blue-600"}):(0,s.jsx)(ei,{className:"w-4 h-4 text-slate-400"})]})}),(0,s.jsx)(ev.ZI,{children:(0,s.jsxs)("p",{children:["Clicca per ordinare per ",a]})})]})}),K=e=>{l&&l(e?b.map(e=>e.id_cavo):[])},X=(e,a)=>{l&&l(a?[...r,e]:r.filter(a=>a!==e))},Y=(0,t.useMemo)(()=>{let e=[{field:"id_cavo",headerName:"ID",dataType:"text",width:70,align:"left",renderHeader:()=>(0,s.jsx)(W,{field:"id_cavo",children:"ID"}),renderCell:e=>(0,s.jsx)("span",{className:"font-semibold text-mariner-900",children:e.id_cavo})},{field:"sistema",headerName:"Sistema",dataType:"text",width:80,renderHeader:()=>(0,s.jsx)(W,{field:"sistema",children:"Sistema"}),renderCell:e=>(0,s.jsx)(eb,{text:e.sistema||"",maxLength:8})},{field:"utility",headerName:"Utility",dataType:"text",width:80,renderCell:e=>(0,s.jsx)(eb,{text:e.utility||"",maxLength:8})},{field:"tipologia",headerName:"Tipologia",dataType:"text",width:100,renderCell:e=>(0,s.jsx)(eb,{text:e.tipologia||"",maxLength:12})},{field:"formazione",headerName:"Form.",dataType:"text",align:"left",width:60,renderCell:e=>e.formazione||e.sezione},{field:"metri_teorici",headerName:"M.Teor.",dataType:"number",align:"left",width:70,renderHeader:()=>(0,s.jsx)(W,{field:"metri_teorici",children:"M.Teor."}),renderCell:e=>e.metri_teorici?e.metri_teorici.toFixed(1):"0"},{field:"metri_posati",headerName:"M.Reali",dataType:"number",align:"left",width:70,renderHeader:()=>(0,s.jsx)(W,{field:"metri_posati",children:"M.Reali"}),renderCell:e=>{let a=e.metri_posati||e.metratura_reale||0;return a?a.toFixed(1):"0"}},{field:"ubicazione_partenza",headerName:"Da",dataType:"text",width:140,renderCell:e=>(0,s.jsx)(eb,{text:e.da||e.ubicazione_partenza||"",maxLength:18})},{field:"ubicazione_arrivo",headerName:"A",dataType:"text",width:140,renderCell:e=>(0,s.jsx)(eb,{text:e.a||e.ubicazione_arrivo||"",maxLength:18})},{field:"id_bobina",headerName:"Bobina",dataType:"text",width:100,align:"center",renderCell:e=>Q(e)},{field:"stato_installazione",headerName:"Stato",dataType:"text",align:"left",width:130,renderHeader:()=>(0,s.jsx)(W,{field:"stato",children:"Stato"}),renderCell:e=>ee(e)},{field:"collegamenti",headerName:"Collegamenti",dataType:"text",align:"left",width:160,disableSort:!0,renderCell:e=>ea(e)},{field:"certificato",headerName:"Certificato",dataType:"text",align:"left",width:170,disableSort:!0,renderCell:e=>el(e)}];return f&&e.unshift({field:"selection",headerName:"",disableFilter:!0,disableSort:!0,width:50,align:"left",renderHeader:()=>(0,s.jsx)(x.S,{checked:r.length===b.length&&b.length>0,onCheckedChange:K}),renderCell:e=>(0,s.jsx)(x.S,{checked:r.includes(e.id_cavo),onCheckedChange:a=>X(e.id_cavo,a),onClick:e=>e.stopPropagation()})}),e},[f,r,b,K,X]),Q=e=>{let a=e.id_bobina;if(!a||"N/A"===a)return(0,s.jsx)("span",{className:"text-gray-400",children:"-"});if("BOBINA_VUOTA"===a)return(0,s.jsx)(m.E,{variant:"outline",className:"text-xs px-2 py-0.5 text-gray-500 border-gray-300 bg-gray-50",children:"Vuota"});let i=a,t=a.match(/_B(.+)$/);return t||(t=a.match(/_b(.+)$/))||(t=a.match(/c\d+_[bB](\d+)$/))?i=t[1]:(t=a.match(/(\d+)$/))&&(i=t[1]),(0,s.jsx)(ev.Bc,{children:(0,s.jsxs)(ev.m_,{children:[(0,s.jsx)(ev.k$,{asChild:!0,children:(0,s.jsxs)(m.E,{variant:"outline",className:"cursor-pointer hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 hover:shadow-sm transition-all duration-200 px-2 py-1 font-medium text-slate-700 border-slate-300 bg-white flex items-center gap-1",onClick:a=>{a.stopPropagation(),(e.metri_posati||e.metratura_reale||0)>0?o?.(e,"modify_reel"):o?.(e,"insert_meters")},children:[(0,s.jsx)("span",{children:i}),(0,s.jsx)(ej.A,{className:"h-3 w-3 opacity-60"})]})}),(0,s.jsx)(ev.ZI,{children:(0,s.jsx)("p",{children:(e.metri_posati||e.metratura_reale||0)>0?"Clicca per modificare bobina":"Clicca per inserire metri posati"})})]})})},ee=e=>{let a=e.metri_posati||e.metratura_reale||0,i=e.comanda_posa,t=e.comanda_partenza,r=e.comanda_arrivo,l=e.comanda_certificazione,c=i||t||r||l;if(c&&"In corso"===e.stato_installazione)return(0,s.jsx)(ev.Bc,{children:(0,s.jsxs)(ev.m_,{children:[(0,s.jsx)(ev.k$,{asChild:!0,children:(0,s.jsx)(m.E,{className:"bg-blue-500 text-white cursor-pointer hover:bg-blue-600 transition-colors duration-200 px-3 py-1 font-medium",onClick:a=>{a.stopPropagation(),o?.(e,"view_command",c)},children:c})}),(0,s.jsx)(ev.ZI,{children:(0,s.jsx)("p",{children:"Visualizza dettagli comanda"})})]})});let d=e.stato_installazione||"Da installare";return"Installato"===d||a>0?(0,s.jsx)(m.E,{className:"bg-green-100 text-green-700 px-3 py-1 font-medium border border-green-200",children:"Installato"}):"In corso"===d?(0,s.jsx)(m.E,{className:"bg-yellow-100 text-yellow-700 px-3 py-1 font-medium border border-yellow-200",children:"In corso"}):(0,s.jsx)(ev.Bc,{children:(0,s.jsxs)(ev.m_,{children:[(0,s.jsx)(ev.k$,{asChild:!0,children:(0,s.jsxs)(n.$,{variant:"outline",size:"sm",className:"h-7 px-2 text-xs font-medium border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-colors duration-200",onClick:a=>{a.stopPropagation(),o?.(e,"insert_meters")},children:[(0,s.jsx)(ef.A,{className:"h-3 w-3 mr-1"}),"Da installare"]})}),(0,s.jsx)(ev.ZI,{children:(0,s.jsx)("p",{children:"Clicca per inserire metri posati"})})]})})},ea=e=>{let a,i,t,r,l=e.metri_posati||e.metratura_reale||0,c=e.stato_installazione||"Da installare",d=e.collegamento||e.collegamenti||0;if(!(l>0||"Installato"===c||"In corso"===c))return(0,s.jsx)(ev.Bc,{children:(0,s.jsxs)(ev.m_,{children:[(0,s.jsx)(ev.k$,{asChild:!0,children:(0,s.jsxs)("span",{className:"text-gray-400 text-sm px-2 py-1 flex items-center gap-1",children:[(0,s.jsx)(eN.A,{className:"h-3 w-3"}),"Non disponibile"]})}),(0,s.jsx)(ev.ZI,{children:(0,s.jsx)("p",{children:"Il collegamento non pu\xf2 essere gestito perch\xe9 il cavo non \xe8 installato"})})]})});switch(d){case 0:a="Collega",i=ey,t="connect_cable",r="border-blue-300 text-blue-700 hover:bg-blue-50 hover:border-blue-400";break;case 1:a="Completa Arrivo",i=ey,t="connect_arrival",r="border-yellow-300 text-yellow-700 hover:bg-yellow-50 hover:border-yellow-400";break;case 2:a="Completa Partenza",i=ey,t="connect_departure",r="border-yellow-300 text-yellow-700 hover:bg-yellow-50 hover:border-yellow-400";break;case 3:a="Scollega",i=e_,t="disconnect_cable",r="border-red-300 text-red-700 hover:bg-red-50 hover:border-red-400";break;default:a="Gestisci",i=eC,t="manage_connections",r="border-blue-300 text-blue-700 hover:bg-blue-50 hover:border-blue-400"}let m=i;return(0,s.jsxs)(n.$,{variant:"outline",size:"sm",className:`h-7 px-2 text-xs font-medium transition-colors duration-200 ${r}`,onClick:a=>{a.stopPropagation(),"disconnect_cable"===t?T("disconnect",e):o?.(e,t)},children:[(0,s.jsx)(m,{className:"h-3 w-3 mr-1"}),a]})},el=e=>{let a=e.metri_posati||e.metratura_reale||0,i=e.stato_installazione||"Da installare",t=!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato,r=!1===e.certificato||"NO"===e.certificato||"RIFIUTATO"===e.certificato;return a>0||"Installato"===i||"In corso"===i?t?(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(m.E,{className:"bg-green-100 text-green-700 px-2 py-1 text-xs font-medium border border-green-200",children:[(0,s.jsx)(ew.A,{className:"h-3 w-3 mr-1"}),"Certificato"]}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",className:"h-6 w-6 p-0 border-blue-300 text-blue-700 hover:bg-blue-50 hover:border-blue-400",onClick:a=>{a.stopPropagation(),T("generatePDF",e)},children:(0,s.jsx)(ez.A,{className:"h-3 w-3"})})]}):r?(0,s.jsxs)(m.E,{className:"bg-red-100 text-red-700 px-2 py-1 text-xs font-medium border border-red-200",children:[(0,s.jsx)(er.A,{className:"h-3 w-3 mr-1"}),"Non Certificato"]}):(0,s.jsxs)(n.$,{variant:"outline",size:"sm",className:"h-7 px-2 text-xs font-medium border-purple-300 text-purple-700 hover:bg-purple-50 hover:border-purple-400 transition-colors duration-200",onClick:a=>{a.stopPropagation(),q(e)},children:[(0,s.jsx)(ew.A,{className:"h-3 w-3 mr-1"}),"Certifica"]}):(0,s.jsx)(ev.Bc,{children:(0,s.jsxs)(ev.m_,{children:[(0,s.jsx)(ev.k$,{asChild:!0,children:(0,s.jsxs)("span",{className:"text-gray-400 text-sm px-2 py-1 flex items-center gap-1",children:[(0,s.jsx)(eN.A,{className:"h-3 w-3"}),"Non disponibile"]})}),(0,s.jsx)(ev.ZI,{children:(0,s.jsx)("p",{children:"La certificazione non pu\xf2 essere gestita perch\xe9 il cavo non \xe8 installato"})})]})})};return(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(eg,{cavi:e,onFilteredDataChange:e=>{g(e)},loading:a,selectionEnabled:f,onSelectionToggle:()=>{N(!f),D(!f&&r.length>0)}}),(0,s.jsxs)("div",{className:"mb-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 bg-slate-50 p-4 rounded-lg border border-slate-200",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{className:"text-sm text-slate-600",children:["Mostrando ",(0,s.jsx)("span",{className:"font-semibold",children:B.startIndex})," - ",(0,s.jsx)("span",{className:"font-semibold",children:B.endIndex})," di ",(0,s.jsx)("span",{className:"font-semibold",children:B.totalItems})," cavi"]}),f&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(ev.Bc,{children:(0,s.jsxs)(ev.m_,{children:[(0,s.jsx)(ev.k$,{asChild:!0,children:(0,s.jsxs)("div",{onClick:Z?()=>{let e=new Set(B.cavi.map(e=>e.id_cavo)),a=r.filter(a=>!e.has(a));l?.(a)}:()=>{let e=[...new Set([...r,...B.cavi.map(e=>e.id_cavo)])];l?.(e)},className:"flex items-center gap-2 px-3 py-2 text-sm font-medium border border-slate-200 rounded-md hover:bg-slate-50 hover:border-slate-300 transition-all duration-200 cursor-pointer",children:[(0,s.jsx)(x.S,{checked:Z,ref:e=>{e&&(e.indeterminate=H&&!Z)}}),"Pagina"]})}),(0,s.jsx)(ev.ZI,{children:(0,s.jsx)("p",{children:Z?"Deseleziona tutti i cavi visibili":"Seleziona tutti i cavi visibili"})})]})}),(0,s.jsx)(ev.Bc,{children:(0,s.jsxs)(ev.m_,{children:[(0,s.jsx)(ev.k$,{asChild:!0,children:(0,s.jsxs)(n.$,{variant:"outline",size:"sm",onClick:r.length===L.length?J:()=>{let e=L.map(e=>e.id_cavo);l?.(e)},className:"flex items-center gap-2",children:[(0,s.jsx)(eA.A,{className:"w-4 h-4"}),"Tutti (",L.length,")"]})}),(0,s.jsx)(ev.ZI,{children:(0,s.jsx)("p",{children:r.length===L.length?"Deseleziona tutti i cavi":"Seleziona tutti i cavi filtrati"})})]})}),r.length>0&&(0,s.jsxs)(m.E,{variant:"secondary",className:"bg-blue-100 text-blue-800",children:[r.length," selezionati"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-sm text-slate-600",children:"Righe per pagina:"}),(0,s.jsxs)("select",{value:F,onChange:e=>{$(Number(e.target.value)),I(1)},className:"border border-slate-300 rounded px-2 py-1 text-sm",children:[(0,s.jsx)("option",{value:10,children:"10"}),(0,s.jsx)("option",{value:25,children:"25"}),(0,s.jsx)("option",{value:50,children:"50"}),(0,s.jsx)("option",{value:100,children:"100"})]})]})]}),(0,s.jsx)(em,{data:v,columns:Y,loading:a,emptyMessage:"Nessun cavo disponibile",onFilteredDataChange:e=>{j(e)},renderRow:(e,a)=>{let i=r.includes(e.id_cavo);return r.length,(0,s.jsx)(u.Hj,{className:`
          ${i?"bg-blue-50 border-blue-200":"bg-white"}
          hover:bg-blue-50 hover:border-blue-200 hover:shadow-sm
          cursor-pointer border-b border-gray-200
          transition-all duration-200 ease-in-out
          ${i?"ring-1 ring-blue-300":""}
        `,onClick:()=>f&&X(e.id_cavo,!i),onContextMenu:a=>{a.preventDefault(),S({isOpen:!0,position:{x:a.clientX,y:a.clientY},cavo:e})},children:Y.map(a=>(0,s.jsx)(u.nA,{className:`
              py-2 px-2 text-sm text-left
              ${i?"text-blue-900":"text-gray-900"}
              transition-colors duration-200
            `,style:{width:a.width,...a.cellStyle},onClick:e=>{["stato_installazione","collegamenti","certificato"].includes(a.field)&&e.stopPropagation()},children:a.renderCell?a.renderCell(e):e[a.field]||(0,s.jsx)("span",{className:"text-gray-400",children:"-"})},a.field))},e.id_cavo)}}),B.totalPages>1&&(0,s.jsxs)("div",{className:"mt-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 bg-white p-4 rounded-lg border border-slate-200",children:[(0,s.jsxs)("div",{className:"text-sm text-slate-600",children:["Pagina ",(0,s.jsx)("span",{className:"font-semibold",children:E})," di ",(0,s.jsx)("span",{className:"font-semibold",children:B.totalPages})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(ev.Bc,{children:(0,s.jsxs)(ev.m_,{children:[(0,s.jsx)(ev.k$,{asChild:!0,children:(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>V(1),disabled:!B.hasPrevPage,className:"p-2",children:(0,s.jsx)(en,{className:"w-4 h-4"})})}),(0,s.jsx)(ev.ZI,{children:(0,s.jsx)("p",{children:"Prima pagina"})})]})}),(0,s.jsx)(ev.Bc,{children:(0,s.jsxs)(ev.m_,{children:[(0,s.jsx)(ev.k$,{asChild:!0,children:(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>V(E-1),disabled:!B.hasPrevPage,className:"p-2",children:(0,s.jsx)(eo,{className:"w-4 h-4"})})}),(0,s.jsx)(ev.ZI,{children:(0,s.jsx)("p",{children:"Pagina precedente"})})]})}),(0,s.jsx)("div",{className:"flex items-center gap-1",children:Array.from({length:Math.min(5,B.totalPages)},(e,a)=>{let i;return i=B.totalPages<=5||E<=3?a+1:E>=B.totalPages-2?B.totalPages-4+a:E-2+a,(0,s.jsx)(n.$,{variant:E===i?"default":"outline",size:"sm",onClick:()=>V(i),className:"w-8 h-8 p-0",children:i},i)})}),(0,s.jsx)(ev.Bc,{children:(0,s.jsxs)(ev.m_,{children:[(0,s.jsx)(ev.k$,{asChild:!0,children:(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>V(E+1),disabled:!B.hasNextPage,className:"p-2",children:(0,s.jsx)(ec,{className:"w-4 h-4"})})}),(0,s.jsx)(ev.ZI,{children:(0,s.jsx)("p",{children:"Pagina successiva"})})]})}),(0,s.jsx)(ev.Bc,{children:(0,s.jsxs)(ev.m_,{children:[(0,s.jsx)(ev.k$,{asChild:!0,children:(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>V(B.totalPages),disabled:!B.hasNextPage,className:"p-2",children:(0,s.jsx)(ed,{className:"w-4 h-4"})})}),(0,s.jsx)(ev.ZI,{children:(0,s.jsx)("p",{children:"Ultima pagina"})})]})})]})]}),f&&r.length>0&&(0,s.jsx)("div",{className:"fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-white border border-slate-300 rounded-lg shadow-xl z-50 p-4 min-w-[600px]",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full animate-pulse"}),(0,s.jsxs)(m.E,{variant:"secondary",className:"bg-blue-100 text-blue-800 font-semibold",children:[r.length," cavi selezionati"]})]}),(0,s.jsx)(ev.Bc,{children:(0,s.jsxs)(ev.m_,{children:[(0,s.jsx)(ev.k$,{asChild:!0,children:(0,s.jsxs)(n.$,{variant:"ghost",size:"sm",onClick:J,className:"text-slate-600 hover:text-slate-800",children:[(0,s.jsx)(er.A,{className:"w-4 h-4 mr-1"}),"Deseleziona tutto"]})}),(0,s.jsx)(ev.ZI,{children:(0,s.jsx)("p",{children:"Rimuovi la selezione da tutti i cavi"})})]})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(ev.Bc,{children:(0,s.jsxs)(ev.m_,{children:[(0,s.jsx)(ev.k$,{asChild:!0,children:(0,s.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>{R.actionInProgress("Esportazione",`${r.length} cavi`)},className:"flex items-center gap-2 hover:bg-green-50 hover:border-green-300",children:[(0,s.jsx)(ez.A,{className:"w-4 h-4"}),"Esporta"]})}),(0,s.jsx)(ev.ZI,{children:(0,s.jsx)("p",{children:"Esporta i cavi selezionati in Excel"})})]})}),(0,s.jsx)(ev.Bc,{children:(0,s.jsxs)(ev.m_,{children:[(0,s.jsx)(ev.k$,{asChild:!0,children:(0,s.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>{R.actionInProgress("Generazione PDF",`${r.length} cavi`)},className:"flex items-center gap-2 hover:bg-blue-50 hover:border-blue-300",children:[(0,s.jsx)(ek.A,{className:"w-4 h-4"}),"PDF Bulk"]})}),(0,s.jsx)(ev.ZI,{children:(0,s.jsx)("p",{children:"Genera PDF per tutti i cavi selezionati"})})]})}),(0,s.jsx)(ev.Bc,{children:(0,s.jsxs)(ev.m_,{children:[(0,s.jsx)(ev.k$,{asChild:!0,children:(0,s.jsxs)(n.$,{size:"sm",onClick:()=>{R.actionInProgress("Aggiornamento Stato",`${r.length} cavi`)},className:"flex items-center gap-2 hover:bg-yellow-50 hover:border-yellow-300",children:[(0,s.jsx)(eC,{className:"w-4 h-4"}),"Stato"]})}),(0,s.jsx)(ev.ZI,{children:(0,s.jsx)("p",{children:"Cambia stato per tutti i cavi selezionati"})})]})}),(0,s.jsx)(ev.Bc,{children:(0,s.jsxs)(ev.m_,{children:[(0,s.jsx)(ev.k$,{asChild:!0,children:(0,s.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>{R.actionInProgress("Assegnazione Comanda",`${r.length} cavi`)},className:"flex items-center gap-2 hover:bg-purple-50 hover:border-purple-300",children:[(0,s.jsx)(eS.A,{className:"w-4 h-4"}),"Comanda"]})}),(0,s.jsx)(ev.ZI,{children:(0,s.jsx)("p",{children:"Assegna comanda a tutti i cavi selezionati"})})]})}),(0,s.jsx)("div",{className:"w-px h-6 bg-slate-300"}),(0,s.jsx)(ev.Bc,{children:(0,s.jsxs)(ev.m_,{children:[(0,s.jsx)(ev.k$,{asChild:!0,children:(0,s.jsxs)(n.$,{variant:"destructive",size:"sm",onClick:()=>{confirm(`Sei sicuro di voler eliminare ${r.length} cavi selezionati?`)&&R.actionInProgress("Eliminazione",`${r.length} cavi`)},className:"flex items-center gap-2",children:[(0,s.jsx)(eE.A,{className:"w-4 h-4"}),"Elimina"]})}),(0,s.jsx)(ev.ZI,{children:(0,s.jsx)("p",{children:"Elimina tutti i cavi selezionati (azione irreversibile)"})})]})})]})]})}),(0,s.jsx)(at,{isOpen:z.disconnect.isOpen,onClose:()=>P("disconnect"),onConfirm:U,cavo:z.disconnect.cavo}),(0,s.jsx)(ar,{isOpen:z.generatePDF.isOpen,onClose:()=>P("generatePDF"),onGenerate:G,cavo:z.generatePDF.cavo}),(0,s.jsx)(al,{isOpen:z.certificationError.isOpen,onClose:()=>P("certificationError"),cavo:z.certificationError.cavo,missingRequirements:z.certificationError.missingRequirements}),(0,s.jsx)(au,{isOpen:k.isOpen,position:k.position,cavo:k.cavo,isSelected:!!k.cavo&&r.includes(k.cavo.id_cavo),hasMultipleSelection:r.length>1,totalSelectedCount:r.length,onAction:(e,a)=>{switch(a){case"view_details":o?.(e,"view_details");break;case"edit":o?.(e,"edit");break;case"delete":o?.(e,"delete");break;case"add_new":o?.(e,"add_new");break;case"insert_meters":o?.(e,"insert_meters");break;case"manage_connections":o?.(e,"manage_connections");break;case"manage_reel":o?.(e,"manage_reel");break;case"create_certificate":o?.(e,"create_certificate");break;case"copy_id":navigator.clipboard.writeText(e.id_cavo),R.success("ID cavo copiato negli appunti");break;case"copy_details":let i=`ID: ${e.id_cavo}
Tipologia: ${e.tipologia}
Da: ${e.da}
A: ${e.a}`;navigator.clipboard.writeText(i),R.success("Dettagli cavo copiati negli appunti");break;case"export_data":o?.(e,"export_data");break;default:c?.(e,a)}},onClose:()=>S({isOpen:!1,position:{x:0,y:0},cavo:null})})]})}var ap=i(53411),av=i(23361),ag=i(48730),ab=i(45583);function aj({cavi:e,filteredCavi:a,className:i,revisioneCorrente:r}){let n=(0,t.useMemo)(()=>{let i=e.length,s=a.length,t=a.filter(e=>"Installato"===e.stato_installazione||e.metri_posati&&e.metri_posati>0||e.metratura_reale&&e.metratura_reale>0).length,r=a.filter(e=>"In corso"===e.stato_installazione).length,l=a.filter(e=>3===(e.collegamento||e.collegamenti||0)).length,n=a.filter(e=>{let a=e.collegamento||e.collegamenti||0;return 1===a||2===a}).length,o=a.filter(e=>0===(e.collegamento||e.collegamenti||0)&&(e.metri_posati>0||e.metratura_reale>0)).length,c=a.filter(e=>!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato).length,d=a.reduce((e,a)=>e+(a.metri_teorici||0),0),m=a.reduce((e,a)=>e+(a.metri_posati||a.metratura_reale||0),0),x=0===s?0:Math.round(100*(((t-l)*2+(l-c)*3.5+4*c)/(4*s)*100))/100;return{totalCavi:i,filteredCount:s,installati:t,inCorso:r,daInstallare:s-t-r,collegati:l,parzialmenteCollegati:n,nonCollegati:o,certificati:c,metriTotali:d,metriInstallati:m,percentualeInstallazione:x}},[e,a]);return(0,s.jsx)(l.Zp,{className:i,children:(0,s.jsxs)(l.Wu,{className:"p-1.5",children:[(0,s.jsx)("div",{className:"flex items-center justify-between mb-1",children:(0,s.jsxs)("div",{className:"flex items-center space-x-1.5",children:[(0,s.jsx)(ap.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,s.jsx)("span",{className:"text-xs font-semibold text-mariner-900",children:"Statistiche Cavi"}),r&&(0,s.jsxs)(m.E,{variant:"outline",className:"text-xs bg-blue-50 text-blue-700 border-blue-200",children:["Rev. ",r]})]})}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2 justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-1.5 bg-mariner-50 px-1.5 py-1 rounded-lg",children:[(0,s.jsx)(av.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-bold text-mariner-900 text-sm",children:n.filteredCount}),(0,s.jsxs)("div",{className:"text-xs text-mariner-600",children:["di ",n.totalCavi," cavi"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-1.5 bg-green-50 px-1.5 py-1 rounded-lg",children:[(0,s.jsx)(ew.A,{className:"h-3.5 w-3.5 text-green-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-bold text-green-700 text-sm",children:n.installati}),(0,s.jsx)("div",{className:"text-xs text-green-600",children:"installati"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-1.5 bg-yellow-50 px-1.5 py-1 rounded-lg",children:[(0,s.jsx)(ag.A,{className:"h-3.5 w-3.5 text-yellow-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-bold text-yellow-700 text-sm",children:n.inCorso}),(0,s.jsx)("div",{className:"text-xs text-yellow-600",children:"in corso"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-1.5 bg-gray-50 px-1.5 py-1 rounded-lg",children:[(0,s.jsx)(eR.A,{className:"h-3.5 w-3.5 text-gray-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-bold text-gray-700 text-sm",children:n.daInstallare}),(0,s.jsx)("div",{className:"text-xs text-gray-600",children:"da installare"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-1.5 bg-blue-50 px-1.5 py-1 rounded-lg",children:[(0,s.jsx)(ab.A,{className:"h-3.5 w-3.5 text-blue-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-bold text-blue-700 text-sm",children:n.collegati}),(0,s.jsx)("div",{className:"text-xs text-blue-600",children:"collegati"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-1.5 bg-purple-50 px-1.5 py-1 rounded-lg",children:[(0,s.jsx)(eS.A,{className:"h-3.5 w-3.5 text-purple-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-bold text-purple-700 text-sm",children:n.certificati}),(0,s.jsx)("div",{className:"text-xs text-purple-600",children:"certificati"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-1.5 bg-indigo-50 px-1.5 py-1 rounded-lg",children:[(0,s.jsx)("div",{className:"h-3.5 w-3.5 flex items-center justify-center",children:(0,s.jsx)("div",{className:"h-2 w-2 bg-indigo-600 rounded-full"})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"font-bold text-indigo-700 text-sm",children:[n.metriInstallati.toLocaleString(),"m"]}),(0,s.jsxs)("div",{className:"text-xs text-indigo-600",children:["di ",n.metriTotali.toLocaleString(),"m"]})]})]})]}),n.filteredCount>0&&(0,s.jsxs)("div",{className:"mt-2 bg-gray-50 p-2 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex justify-between text-xs font-medium text-gray-700 mb-1",children:[(0,s.jsx)("span",{children:"Avanzamento Complessivo Cavi"}),(0,s.jsxs)("span",{className:`font-bold ${n.percentualeInstallazione>=80?"text-amber-700":n.percentualeInstallazione>=60?"text-orange-700":n.percentualeInstallazione>=40?"text-yellow-700":"text-emerald-700"}`,children:[n.percentualeInstallazione.toFixed(1),"%"]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:`h-2 rounded-full transition-all duration-500 ease-in-out ${n.percentualeInstallazione>=80?"bg-gradient-to-r from-amber-500 to-amber-600":n.percentualeInstallazione>=60?"bg-gradient-to-r from-orange-500 to-orange-600":n.percentualeInstallazione>=40?"bg-gradient-to-r from-yellow-500 to-yellow-600":"bg-gradient-to-r from-emerald-500 to-emerald-600"}`,style:{width:`${Math.min(n.percentualeInstallazione,100)}%`}})}),(0,s.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-0.5",children:[(0,s.jsx)("span",{children:"Metri installati vs totali disponibili"}),(0,s.jsxs)("span",{children:[(n.metriTotali-n.metriInstallati).toLocaleString(),"m rimanenti"]})]})]})]})})}let af=(0,ea.A)("calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]);var aN=i(93613);function ay({open:e,onClose:a,cavo:i,cantiere:r,onSuccess:l,onError:m}){let{cantiere:x}=(0,c.A)(),u=r||x,[p,v]=(0,t.useState)("assegna_nuova"),[g,b]=(0,t.useState)(""),[j,f]=(0,t.useState)([]),[N,y]=(0,t.useState)(!1),[_,C]=(0,t.useState)(!1),[w,z]=(0,t.useState)(""),[A,k]=(0,t.useState)(""),[S,E]=(0,t.useState)("compatibili"),I=(()=>{if(!i)return[];let e=j.filter(e=>{let a=e.tipologia===i.tipologia&&e.sezione===i.sezione,s=""===A||e.id_bobina.toLowerCase().includes(A.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(A.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(A.toLowerCase());return a&&s&&e.metri_residui>0});return console.log("\uD83D\uDD0D ModificaBobinaDialog: Filtro compatibili:",{cavoTipologia:i.tipologia,cavoSezione:i.sezione,totaleBobine:j.length,bobineCompatibili:e.length,searchText:A}),e})(),F=i?j.filter(e=>{let a=e.tipologia!==i.tipologia||e.sezione!==i.sezione,s=""===A||e.id_bobina.toLowerCase().includes(A.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(A.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(A.toLowerCase());return a&&s&&e.metri_residui>0}):[],$=()=>{v("assegna_nuova"),b(""),k(""),z(""),a()},O=async()=>{if(console.log("\uD83D\uDD04 ModificaBobinaDialog: Salvataggio:",{selectedOption:p,selectedBobina:g,cavoId:i?.id_cavo,cantiereId:u?.id_cantiere}),i)try{if(C(!0),z(""),"assegna_nuova"===p){if(!g)return void m("Selezionare una bobina");let e=await d.At.updateMetriPosati({id_cavo:i.id_cavo,metri_posati:i.metratura_reale||0,id_bobina:g,force_over:!0});e.success?(l(`Bobina aggiornata con successo per il cavo ${i.id_cavo}`),$()):m(e.message||"Errore durante l'aggiornamento della bobina")}else if("rimuovi_bobina"===p){let e=await d.At.updateMetriPosati({id_cavo:i.id_cavo,metri_posati:i.metratura_reale||0,id_bobina:"BOBINA_VUOTA",force_over:!1});e.success?(l(`Bobina rimossa dal cavo ${i.id_cavo}`),$()):m(e.message||"Errore durante la rimozione della bobina")}else if("annulla_installazione"===p){let e=await d.At.updateMetriPosati({id_cavo:i.id_cavo,metri_posati:0,id_bobina:"BOBINA_VUOTA",force_over:!1});e.success?(l(`Installazione annullata per il cavo ${i.id_cavo}`),$()):m(e.message||"Errore durante l'annullamento dell'installazione")}}catch(e){console.error("❌ ModificaBobinaDialog: Errore salvataggio:",e),m("Errore durante il salvataggio")}finally{C(!1)}};return i?(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(eI.lG,{open:e,onOpenChange:$,children:(0,s.jsxs)(eI.Cf,{className:"max-w-4xl max-h-[90vh] flex flex-col",children:[(0,s.jsx)(eI.c7,{children:(0,s.jsxs)(eI.L3,{children:["Modifica Bobina Cavo ",i.id_cavo]})}),(0,s.jsxs)("div",{className:"flex-1 overflow-hidden space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(eS.A,{className:"h-5 w-5 text-blue-600"}),(0,s.jsx)("h3",{className:"font-medium text-gray-900",children:"Cavo Selezionato"})]}),(0,s.jsxs)("div",{className:"p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border-2 border-blue-200 shadow-sm",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 text-sm",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"font-semibold text-blue-700 min-w-[80px]",children:"Tipologia:"}),(0,s.jsx)("span",{className:"font-medium text-gray-800",children:i.tipologia||"N/A"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"font-semibold text-blue-700 min-w-[80px]",children:"Formazione:"}),(0,s.jsx)("span",{className:"font-medium text-gray-800",children:i.sezione||"N/A"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"font-semibold text-blue-700 min-w-[80px]",children:"Colore:"}),(0,s.jsx)("span",{className:"font-medium text-gray-800",children:i.colore_cavo||"N/A"})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"font-semibold text-blue-700 min-w-[80px]",children:"Da:"}),(0,s.jsx)("span",{className:"font-medium text-gray-800",children:i.ubicazione_partenza||"N/A"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"font-semibold text-blue-700 min-w-[80px]",children:"A:"}),(0,s.jsx)("span",{className:"font-medium text-gray-800",children:i.ubicazione_arrivo||"N/A"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"font-semibold text-blue-700 min-w-[80px]",children:"Metri:"}),(0,s.jsxs)("span",{className:"font-bold text-green-700",children:[i.metratura_reale||0," m posati"]}),(0,s.jsxs)("span",{className:"text-gray-500",children:["/ ",i.metratura_teorica||0," m teorici"]})]})]})]}),i.id_bobina&&"BOBINA_VUOTA"!==i.id_bobina&&(0,s.jsx)("div",{className:"mt-3 pt-3 border-t border-blue-200",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(eS.A,{className:"h-4 w-4 text-blue-600"}),(0,s.jsx)("span",{className:"font-semibold text-blue-700",children:"Bobina Attuale:"}),(0,s.jsx)("span",{className:"font-medium text-gray-800",children:(e=>{if(!e||"BOBINA_VUOTA"===e)return"VUOTA";if(e&&e.includes("_B"))return e.split("_B")[1];let a=j.find(a=>a.id_bobina===e);return a&&a.numero_bobina||e})(i.id_bobina)})]})})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("h3",{className:"font-medium",children:"Opzioni di modifica"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,s.jsx)("input",{type:"radio",name:"operazione",value:"assegna_nuova",checked:"assegna_nuova"===p,onChange:e=>v(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,s.jsx)("span",{className:"text-sm",children:"Cambia bobina"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,s.jsx)("input",{type:"radio",name:"operazione",value:"rimuovi_bobina",checked:"rimuovi_bobina"===p,onChange:e=>v(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,s.jsx)("span",{className:"text-sm",children:"Bobina vuota"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-red-50",children:[(0,s.jsx)("input",{type:"radio",name:"operazione",value:"annulla_installazione",checked:"annulla_installazione"===p,onChange:e=>v(e.target.value),className:"w-4 h-4 text-red-600"}),(0,s.jsx)("span",{className:"text-sm text-red-600 font-medium",children:"Annulla posa (reset completo)"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,s.jsx)("input",{type:"radio",name:"operazione",value:"annulla_installazione",checked:"annulla_installazione"===p,onChange:e=>v(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,s.jsx)("span",{className:"text-sm text-red-600",children:"Annulla posa"})]})]})]}),"assegna_nuova"===p&&(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("h3",{className:"font-medium",children:"Seleziona bobina"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(eu.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,s.jsx)(h.p,{placeholder:"Cerca bobina per ID, tipologia o numero...",value:A,onChange:e=>k(e.target.value),className:"pl-10"})]}),(0,s.jsxs)("div",{className:"flex space-x-1 border-b",children:[(0,s.jsx)("button",{onClick:()=>E("compatibili"),className:`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${"compatibili"===S?"border-green-500 text-green-600 bg-green-50":"border-transparent text-gray-500 hover:text-gray-700"}`,children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(ew.A,{className:"h-4 w-4"}),(0,s.jsxs)("span",{children:["Bobine Compatibili (",I.length,")"]})]})}),(0,s.jsx)("button",{onClick:()=>E("incompatibili"),className:`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${"incompatibili"===S?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700"}`,children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(aN.A,{className:"h-4 w-4"}),(0,s.jsxs)("span",{children:["Bobine Incompatibili (",F.length,")"]})]})})]}),(0,s.jsx)("div",{className:"border rounded-lg h-64 overflow-y-auto",children:N?(0,s.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(eD.A,{className:"h-4 w-4 animate-spin"}),(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Caricamento bobine..."})]})}):(0,s.jsx)("div",{className:"p-2",children:"compatibili"===S?0===I.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"text-gray-500 text-sm mb-2",children:"Nessuna bobina compatibile trovata"}),(0,s.jsxs)("div",{className:"text-xs text-gray-400",children:["Cercando bobine con tipologia ",(0,s.jsx)("strong",{children:i.tipologia})," e formazione ",(0,s.jsx)("strong",{children:i.sezione})]})]}):(0,s.jsx)("div",{className:"space-y-2",children:I.map(e=>(0,s.jsx)("div",{onClick:()=>b(e.id_bobina),className:`p-3 rounded-lg cursor-pointer transition-all duration-200 ${g===e.id_bobina?"bg-blue-100 border-2 border-blue-300 shadow-md":"hover:bg-gray-50 border border-gray-200 hover:border-gray-300"}`,children:(0,s.jsxs)("div",{className:"flex justify-between items-start",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,s.jsx)("div",{className:"font-medium text-sm text-gray-900",children:e.id_bobina}),e.stato_bobina&&(0,s.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${"Disponibile"===e.stato_bobina?"bg-green-100 text-green-800":"In uso"===e.stato_bobina?"bg-blue-100 text-blue-800":"bg-yellow-100 text-yellow-800"}`,children:e.stato_bobina})]}),(0,s.jsxs)("div",{className:"text-xs text-gray-500 mb-1",children:[(0,s.jsx)("span",{className:"font-medium",children:e.tipologia})," • ",(0,s.jsx)("span",{children:e.sezione})]})]}),(0,s.jsxs)("div",{className:"text-right ml-3",children:[(0,s.jsxs)("div",{className:`text-sm font-medium ${e.metri_residui>0?"text-green-600":"text-gray-500"}`,children:[e.metri_residui,"m"]}),(0,s.jsx)("div",{className:"text-xs text-gray-400",children:e.metri_residui>0?"disponibili":"esaurita"})]})]})},e.id_bobina))}):0===F.length?(0,s.jsx)("div",{className:"text-center py-8 text-gray-500 text-sm",children:"Nessuna bobina incompatibile trovata"}):(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("div",{className:"p-3 bg-orange-50 border border-orange-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,s.jsx)(aN.A,{className:"h-4 w-4 text-orange-600 mt-0.5 flex-shrink-0"}),(0,s.jsxs)("div",{className:"text-sm text-orange-800",children:[(0,s.jsx)("div",{className:"font-medium mb-1",children:"Bobine Incompatibili"}),(0,s.jsx)("div",{className:"text-xs",children:"Selezionando una bobina incompatibile, le caratteristiche del cavo verranno aggiornate."})]})]})}),F.map(e=>(0,s.jsx)("div",{onClick:()=>b(e.id_bobina),className:`p-3 rounded-lg cursor-pointer transition-all duration-200 ${g===e.id_bobina?"bg-orange-100 border-2 border-orange-300 shadow-md":"hover:bg-gray-50 border border-gray-200 hover:border-gray-300"}`,children:(0,s.jsxs)("div",{className:"flex justify-between items-start",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,s.jsx)("div",{className:"font-medium text-sm text-gray-900",children:e.id_bobina}),(0,s.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800",children:"INCOMPATIBILE"})]}),(0,s.jsxs)("div",{className:"text-xs text-gray-500 mb-1",children:[(0,s.jsx)("span",{className:"font-medium",children:e.tipologia})," • ",(0,s.jsx)("span",{children:e.sezione})]})]}),(0,s.jsxs)("div",{className:"text-right ml-3",children:[(0,s.jsxs)("div",{className:`text-sm font-medium ${e.metri_residui>0?"text-orange-600":"text-gray-500"}`,children:[e.metri_residui,"m"]}),(0,s.jsx)("div",{className:"text-xs text-gray-400",children:e.metri_residui>0?"disponibili":"esaurita"})]})]})},e.id_bobina))]})})})]})]}),w&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(aN.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:w})]}),(0,s.jsxs)(eI.Es,{className:"flex justify-end space-x-2",children:[(0,s.jsx)(n.$,{variant:"outline",onClick:$,disabled:_,children:"Annulla"}),(0,s.jsxs)(n.$,{onClick:O,disabled:_||"assegna_nuova"===p&&!g,children:[_&&(0,s.jsx)(eD.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salva"]})]})]})})}):null}function a_({open:e,onClose:a,cavo:i,cantiere:r,onSuccess:l,onError:x}){let{cantiere:u}=(0,c.A)(),p=r||u,[v,g]=(0,t.useState)({metri_posati:"",id_bobina:""}),[b,j]=(0,t.useState)({}),[f,N]=(0,t.useState)({}),[y,_]=(0,t.useState)(!1),[C,w]=(0,t.useState)([]),[z,A]=(0,t.useState)(!1),[k,S]=(0,t.useState)(""),[E,I]=(0,t.useState)(!1),F=e=>{if(!e||"BOBINA_VUOTA"===e)return"VUOTA";if(e&&e.includes("_B"))return e.split("_B")[1];let a=C.find(a=>a.id_bobina===e);return a&&a.numero_bobina||e};C.filter(e=>{if(!k)return!0;let a=k.toLowerCase();return e.id_bobina.toLowerCase().includes(a)||e.tipologia.toLowerCase().includes(a)||e.formazione.toLowerCase().includes(a)||F(e.id_bobina).toLowerCase().includes(a)});let $=i?C.filter(e=>{let a=e.tipologia===i.tipologia&&e.sezione===i.sezione,s=""===k||e.id_bobina.toLowerCase().includes(k.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(k.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(k.toLowerCase());return a&&s&&e.metri_residui>0}):[],O=i?C.filter(e=>{let a=e.tipologia!==i.tipologia||e.sezione!==i.sezione,s=""===k||e.id_bobina.toLowerCase().includes(k.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(k.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(k.toLowerCase());return a&&s&&e.metri_residui>0}):[],D=e=>{console.log("\uD83C\uDFAF Bobina selezionata:",{id:e.id_bobina,numero:F(e.id_bobina),tipologia:e.tipologia,formazione:e.formazione,metri_residui:e.metri_residui}),g(a=>({...a,id_bobina:e.id_bobina})),j(e=>({...e,id_bobina:void 0}))},R=async()=>{if(console.log({cavo:i?.id_cavo,metri_posati:v.metri_posati,id_bobina:v.id_bobina}),!i)return;if(!v.metri_posati||0>parseFloat(v.metri_posati))return void x("Inserire metri posati validi (≥ 0)");if(!v.id_bobina)return void x("Selezionare una bobina o BOBINA VUOTA");let e=parseFloat(v.metri_posati);if("BOBINA_VUOTA"!==v.id_bobina){let e=C.find(e=>e.id_bobina===v.id_bobina);e&&e.metri_residui}try{if(_(!0),!p)throw Error("Cantiere non selezionato");console.log({cantiere:p.id_cantiere,cavo:i.id_cavo,metri:e,bobina:v.id_bobina,isBobinaVuota:"BOBINA_VUOTA"===v.id_bobina}),await d.At.updateMetriPosati(p.id_cantiere,i.id_cavo,e,v.id_bobina,!0),l(`Metri posati aggiornati con successo per il cavo ${i.id_cavo}: ${e}m`),a()}catch(e){x(e.response?.data?.detail||e.message||"Errore durante il salvataggio dei metri posati")}finally{_(!1)}},T=()=>{y||(g({metri_posati:"",id_bobina:""}),j({}),N({}),S(""),a())};return i?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(eI.lG,{open:e,onOpenChange:T,children:(0,s.jsxs)(eI.Cf,{className:"max-w-7xl h-[90vh] flex flex-col",children:[(0,s.jsxs)(eI.c7,{className:"flex-shrink-0",children:[(0,s.jsxs)(eI.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(af,{className:"h-5 w-5"}),"Inserisci Metri Posati - ",i.id_cavo]}),(0,s.jsx)(eI.rr,{children:"Inserisci i metri effettivamente posati per il cavo e seleziona una bobina o usa BOBINA VUOTA"})]}),(0,s.jsxs)("div",{className:"flex-1 overflow-y-auto space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-4",children:[(0,s.jsx)("div",{className:"lg:col-span-2",children:(0,s.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-200",children:[(0,s.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"Informazioni Cavo"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Tipologia:"})," ",i.tipologia||"N/A"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Da:"})," ",i.ubicazione_partenza||"N/A"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Formazione:"})," ",i.sezione||"N/A"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"A:"})," ",i.ubicazione_arrivo||"N/A"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Metri teorici:"})," ",i.metri_teorici||"N/A"," m"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Gi\xe0 posati:"})," ",i.metratura_reale||0," m"]})]})]})}),(0,s.jsx)("div",{className:"lg:col-span-1",children:(0,s.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-300 h-full",children:[(0,s.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"Metri da Installare"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"metri",className:"text-sm font-medium",children:"Metri Posati"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(h.p,{id:"metri",type:"number",value:v.metri_posati,onChange:e=>g(a=>({...a,metri_posati:e.target.value})),placeholder:"Inserisci metri posati",disabled:y,step:"0.1",min:"0",className:"text-lg font-bold text-center border-2 border-blue-400 focus:border-blue-600",autoFocus:!0}),(0,s.jsx)("span",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-lg font-bold text-blue-600",children:"m"})]}),b.metri_posati&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:b.metri_posati}),f.metri_posati&&(0,s.jsx)("p",{className:"text-sm text-amber-600",children:f.metri_posati})]})]})})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"font-semibold text-blue-800 text-lg",children:"Selezione Bobina"}),(0,s.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-12 gap-3 items-center",children:[(0,s.jsx)("div",{className:"sm:col-span-5",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(eu.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,s.jsx)(h.p,{placeholder:"ID, tipologia, formazione...",value:k,onChange:e=>S(e.target.value),className:"pl-10",disabled:y}),k&&(0,s.jsx)(n.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0",onClick:()=>S(""),children:(0,s.jsx)(er.A,{className:"h-4 w-4"})})]})}),(0,s.jsx)("div",{className:"sm:col-span-7",children:(0,s.jsxs)(n.$,{type:"button",variant:"BOBINA_VUOTA"===v.id_bobina?"default":"outline",className:`w-full h-10 font-bold flex items-center justify-center gap-2 ${"BOBINA_VUOTA"===v.id_bobina?"bg-green-600 hover:bg-green-700 text-white":"border-blue-400 text-blue-700 hover:bg-blue-50"}`,onClick:()=>{console.log("\uD83C\uDFAF BOBINA VUOTA selezionata - cavo sar\xe0 posato senza bobina specifica"),g(e=>({...e,id_bobina:"BOBINA_VUOTA"})),j(e=>{let a={...e};return delete a.id_bobina,a})},disabled:y,children:["BOBINA_VUOTA"===v.id_bobina&&(0,s.jsx)(ew.A,{className:"h-5 w-5"}),"BOBINA VUOTA"]})})]}),"BOBINA_VUOTA"===v.id_bobina&&(0,s.jsx)("div",{className:"mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-start gap-2",children:[(0,s.jsx)(ew.A,{className:"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0"}),(0,s.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,s.jsx)("p",{className:"font-medium",children:"Bobina Vuota Selezionata"}),(0,s.jsx)("p",{className:"mt-1",children:'Il cavo sar\xe0 posato senza assegnazione di bobina specifica. Potrai collegarlo a una bobina in seguito tramite la funzione "Modifica Bobina".'})]})]})})]}),z?(0,s.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,s.jsx)(eD.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,s.jsx)("span",{children:"Caricamento bobine..."})]}):(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h4",{className:"font-medium text-green-700 mb-2 flex items-center gap-2",children:[(0,s.jsx)(ew.A,{className:"h-4 w-4"}),"Bobine Compatibili (",$.length,")"]}),(0,s.jsx)("div",{className:"max-h-72 overflow-y-auto border rounded-lg",children:0===$.length?(0,s.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina compatibile trovata"}):(0,s.jsx)("div",{className:"divide-y",children:$.map(e=>(0,s.jsx)("div",{className:`p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ${v.id_bobina===e.id_bobina?"bg-green-100 border-green-500 shadow-md":"border-gray-200 hover:bg-green-50 hover:border-green-300"}`,onClick:()=>D(e),children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[v.id_bobina===e.id_bobina&&(0,s.jsx)(ew.A,{className:"h-5 w-5 text-green-600 flex-shrink-0"}),(0,s.jsx)("div",{className:"font-bold text-base min-w-fit",children:F(e.id_bobina)}),(0,s.jsxs)("div",{className:"text-sm text-gray-600 truncate",children:[e.tipologia," - ",e.sezione]})]}),(0,s.jsxs)(m.E,{variant:"outline",className:"bg-green-100 text-green-800 border-green-300 font-medium min-w-fit",children:[e.metri_residui,"m"]})]})},e.id_bobina))})})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("h4",{className:"font-medium text-amber-700 mb-2 flex items-center gap-2",children:[(0,s.jsx)(eR.A,{className:"h-4 w-4"}),"Bobine Incompatibili (",O.length,")"]}),(0,s.jsx)("div",{className:"max-h-72 overflow-y-auto border rounded-lg",children:0===O.length?(0,s.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina incompatibile trovata"}):(0,s.jsx)("div",{className:"divide-y",children:O.map(e=>(0,s.jsx)("div",{className:`p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ${v.id_bobina===e.id_bobina?"bg-amber-100 border-amber-500 shadow-md":"border-gray-200 hover:bg-amber-50 hover:border-amber-300"}`,onClick:()=>D(e),children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[v.id_bobina===e.id_bobina&&(0,s.jsx)(ew.A,{className:"h-5 w-5 text-amber-600 flex-shrink-0"}),(0,s.jsx)("div",{className:"font-bold text-base min-w-fit",children:F(e.id_bobina)}),(0,s.jsxs)("div",{className:"text-sm text-gray-600 truncate",children:[e.tipologia," - ",e.sezione]})]}),(0,s.jsxs)(m.E,{variant:"outline",className:"bg-amber-100 text-amber-800 border-amber-300 font-medium min-w-fit",children:[e.metri_residui,"m"]})]})},e.id_bobina))})})]})]}),0===C.length&&!z&&(0,s.jsxs)(o.Fc,{className:"border-amber-200 bg-amber-50",children:[(0,s.jsx)(eR.A,{className:"h-4 w-4 text-amber-600"}),(0,s.jsx)(o.TN,{className:"text-amber-800",children:"Non ci sono bobine disponibili. Puoi procedere con BOBINA VUOTA o aggiungere prima una nuova bobina."})]}),b.id_bobina&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(aN.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:b.id_bobina})]})]})]}),(0,s.jsxs)(eI.Es,{className:"flex-shrink-0 border-t pt-4 mt-4 flex justify-between items-center",children:[(0,s.jsx)("div",{children:"installato"===i.stato_installazione&&i.id_bobina&&(0,s.jsx)(n.$,{variant:"outline",onClick:()=>{I(!0)},disabled:y,className:"text-blue-600 border-blue-300 hover:bg-blue-50",children:"Modifica Bobina"})}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(n.$,{variant:"outline",onClick:T,disabled:y,children:"Annulla"}),(0,s.jsxs)(n.$,{onClick:R,disabled:y||!v.metri_posati||0>parseFloat(v.metri_posati)||!v.id_bobina,className:"bg-mariner-600 hover:bg-mariner-700 text-white disabled:bg-gray-400 disabled:text-gray-200",children:[y&&(0,s.jsx)(eD.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salva"]})]})]})]})}),(0,s.jsx)(ay,{open:E,onClose:()=>I(!1),cavo:i,onSuccess:e=>{l(e),I(!1),a()},onError:x})]}):null}var aC=i(69024);function aw({open:e,onClose:a,onConfirm:i,title:r,description:l,isLoading:c,isDangerous:d=!1}){let[m,x]=(0,t.useState)(!1);return(0,s.jsx)(eI.lG,{open:e,onOpenChange:a,children:(0,s.jsx)(eI.Cf,{className:"sm:max-w-[400px]","aria-describedby":"confirm-disconnect-description",children:m?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(eI.c7,{children:(0,s.jsx)(eI.L3,{className:"text-center text-orange-600",children:"Conferma Finale"})}),(0,s.jsxs)("div",{className:"py-4 text-center",children:[(0,s.jsx)("div",{className:"mx-auto w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-4",children:(0,s.jsx)(eR.A,{className:"h-6 w-6 text-orange-600"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Sei veramente sicuro?"}),(0,s.jsxs)("p",{className:"text-sm text-gray-600 mb-4",children:["Questa azione scollegher\xe0 ",(0,s.jsx)("strong",{children:"entrambi i lati"})," del cavo."]}),(0,s.jsx)("div",{className:"bg-orange-50 border border-orange-200 rounded-md p-3",children:(0,s.jsx)("p",{className:"text-sm text-orange-800 font-medium",children:"⚠️ Operazione irreversibile"})})]}),(0,s.jsxs)(eI.Es,{className:"gap-2",children:[(0,s.jsx)(n.$,{variant:"outline",onClick:()=>x(!1),disabled:c,className:"flex-1",children:"No, Annulla"}),(0,s.jsx)(n.$,{variant:"outline",onClick:()=>{i()},disabled:c,className:"flex-1 bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100 hover:border-orange-300",children:c?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(eD.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Scollegando..."]}):"S\xec, Scollega"})]})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(eI.c7,{children:[(0,s.jsxs)(eI.L3,{className:"flex items-center gap-2 text-orange-600",children:[(0,s.jsx)(eR.A,{className:"h-5 w-5"}),r]}),(0,s.jsx)(eI.rr,{id:"confirm-disconnect-description",children:l})]}),(0,s.jsx)("div",{className:"py-4",children:(0,s.jsxs)(o.Fc,{className:"border-orange-200 bg-orange-50",children:[(0,s.jsx)(eR.A,{className:"h-4 w-4 text-orange-600"}),(0,s.jsxs)(o.TN,{className:"text-orange-800",children:[(0,s.jsx)("strong",{children:"Attenzione:"})," Questa azione modificher\xe0 lo stato del collegamento del cavo."]})]})}),(0,s.jsxs)(eI.Es,{className:"gap-2",children:[(0,s.jsx)(n.$,{variant:"outline",onClick:a,disabled:c,className:"flex-1 hover:bg-gray-50",children:"Annulla"}),(0,s.jsxs)(n.$,{variant:"outline",onClick:()=>{d?x(!0):i()},disabled:c,className:"flex-1 bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100 hover:border-orange-300",children:[(0,s.jsx)(eR.A,{className:"mr-2 h-4 w-4"}),d?"Procedi":"Conferma"]})]})]})})})}let az=function({open:e,onClose:a,cavo:i,onSuccess:r,onError:l}){let{cantiere:m}=(0,c.A)(),x=(0,an.E)(),[u,h]=(0,t.useState)("cantiere"),[p,v]=(0,t.useState)(!1),[g,b]=(0,t.useState)(""),[j,f]=(0,t.useState)({open:!1,type:null,title:"",description:""}),N=(0,t.useRef)(null),y=(0,t.useRef)(null),_=(0,t.useRef)(null),[C,w]=(0,t.useState)(""),z=e=>{w(e),setTimeout(()=>w(""),1e3)},A=()=>{if(!i)return{stato:"non_collegato",descrizione:"Non collegato"};switch(i.collegamento||i.collegamenti||0){case 1:return{stato:"partenza",descrizione:"\uD83D\uDFE2⚪ Collegato lato partenza"};case 2:return{stato:"arrivo",descrizione:"⚪\uD83D\uDFE2 Collegato lato arrivo"};case 3:return{stato:"completo",descrizione:"\uD83D\uDFE2\uD83D\uDFE2 Completamente collegato"};default:return{stato:"non_collegato",descrizione:"⚪⚪ Non collegato"}}},k=async()=>{if(i&&m)try{v(!0),b(""),z("Collegamento in corso..."),await d.At.collegaCavo(m.id_cantiere,i.id_cavo,"partenza",u);let e=`Collegamento lato partenza completato per il cavo ${i.id_cavo}`;z(e),x.success("Successo",e),r&&r(),a()}catch(a){let e=a.response?.data?.detail||a.message||"Errore durante il collegamento";b(e),z(`Errore: ${e}`),l&&l(e)}finally{v(!1)}},S=async()=>{if(i&&m)try{v(!0),b(""),z("Collegamento in corso..."),await d.At.collegaCavo(m.id_cantiere,i.id_cavo,"arrivo",u);let e=`Collegamento lato arrivo completato per il cavo ${i.id_cavo}`;z(e),x.success("Successo",e),r&&r(),a()}catch(a){let e=a.response?.data?.detail||a.message||"Errore durante il collegamento";b(e),z(`Errore: ${e}`),l&&l(e)}finally{v(!1)}},E=async()=>{if(i&&m)try{v(!0),b(""),z("Collegamento entrambi i lati in corso..."),await d.At.collegaCavo(m.id_cantiere,i.id_cavo,"entrambi",u);let e=`Collegamento completo per il cavo ${i.id_cavo}`;z(e),x.success("Successo",e),r&&r(),a()}catch(a){let e=a.response?.data?.detail||a.message||"Errore durante il collegamento";b(e),z(`Errore: ${e}`),l&&l(e)}finally{v(!1)}},I=async()=>{if(i&&m&&j.type)try{v(!0),b(""),z("Scollegamento in corso..."),await d.At.scollegaCavo(m.id_cantiere,i.id_cavo,"entrambi"===j.type?void 0:j.type);let e="entrambi"===j.type?"":` lato ${j.type}`,s=`Scollegamento${e} completato per il cavo ${i.id_cavo}`;z(s),x.success("Successo",s),r&&r(),f({open:!1,type:null,title:"",description:""}),a()}catch(a){let e=a.response?.data?.detail||a.message||"Errore durante lo scollegamento";b(e),z(`Errore: ${e}`),l&&l(e)}finally{v(!1)}};if(!i)return null;let F=A(),$=(i.metri_posati||i.metratura_reale||0)>0;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(aC.bL,{children:(0,s.jsx)("div",{"aria-live":"polite","aria-atomic":"true",children:C})}),(0,s.jsx)(eI.lG,{open:e,onOpenChange:a,children:(0,s.jsxs)(eI.Cf,{className:"sm:max-w-[600px] max-h-[90vh] overflow-y-auto",ref:N,"aria-describedby":"collegamenti-description",children:[(0,s.jsxs)(eI.c7,{children:[(0,s.jsxs)(eI.L3,{className:"flex items-center gap-2 text-blue-600",children:[(0,s.jsx)(ab.A,{className:"h-5 w-5"}),"Gestione Collegamenti - ",i.id_cavo]}),(0,s.jsxs)(eI.rr,{id:"collegamenti-description",children:["Gestisci i collegamenti del cavo ",i.id_cavo,". Usa i tasti 1, 2, 3 per azioni rapide."]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-200",children:(0,s.jsxs)("div",{className:"text-sm font-medium text-blue-800",children:["Informazioni Cavo / Tipologia: ",i.tipologia||"N/A"," / Da: ",i.ubicazione_partenza||"N/A"," / Formazione: ",i.sezione||"N/A"," / A: ",i.ubicazione_arrivo||"N/A"," / Metri Posati: ",i.metratura_reale||0," m"]})}),(0,s.jsx)("div",{className:"p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border",children:(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{children:[(0,s.jsx)(eF.J,{className:"text-sm font-medium text-gray-700",children:"Stato Collegamento"}),(0,s.jsxs)("div",{className:"mt-1 text-lg font-semibold flex items-center gap-2",children:["completo"===F.stato&&(0,s.jsx)(ew.A,{className:"h-5 w-5 text-green-600"}),"non_collegato"===F.stato&&(0,s.jsx)(aN.A,{className:"h-5 w-5 text-gray-400"}),("partenza"===F.stato||"arrivo"===F.stato)&&(0,s.jsx)(eR.A,{className:"h-5 w-5 text-orange-500"}),F.descrizione]})]})})}),!$&&(0,s.jsxs)(o.Fc,{className:"border-orange-200 bg-orange-50",children:[(0,s.jsx)(aN.A,{className:"h-4 w-4 text-orange-600"}),(0,s.jsxs)(o.TN,{className:"text-orange-800",children:[(0,s.jsx)("strong",{children:"Attenzione:"})," Il cavo deve essere installato prima di poter essere collegato."]})]}),g&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(aN.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:g})]}),$&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(eF.J,{className:"text-sm font-medium",children:"Responsabile Collegamento"}),(0,s.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg border",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(ew.A,{className:"h-4 w-4 text-green-600"}),(0,s.jsx)("span",{className:"font-medium",children:"Cantiere"})]}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Collegamento eseguito dal responsabile del cantiere"})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(eF.J,{className:"text-sm font-medium",children:"Azioni Disponibili"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-3",children:[(0,s.jsx)(n.$,{ref:y,onClick:()=>{if(!i)return;let e=A();"partenza"===e.stato||"completo"===e.stato?f({open:!0,type:"partenza",title:"Scollega lato partenza",description:`Vuoi scollegare il lato partenza del cavo ${i.id_cavo}?`}):k()},disabled:p,className:"w-full h-12 text-left justify-start bg-green-50 text-green-700 border-green-200 hover:bg-green-100 hover:border-green-300",variant:"outline",children:(0,s.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-sm font-bold text-green-700",children:"1"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:"partenza"===F.stato||"completo"===F.stato?"Scollega Partenza":"Collega Partenza"}),(0,s.jsx)("div",{className:"text-xs text-green-600",children:"partenza"===F.stato||"completo"===F.stato?"Rimuovi collegamento lato partenza":"Connetti il lato partenza del cavo"})]})]}),p?(0,s.jsx)(eD.A,{className:"h-4 w-4 animate-spin"}):(0,s.jsx)(ab.A,{className:"h-4 w-4"})]})}),(0,s.jsx)(n.$,{onClick:()=>{if(!i)return;let e=A();"arrivo"===e.stato||"completo"===e.stato?f({open:!0,type:"arrivo",title:"Scollega lato arrivo",description:`Vuoi scollegare il lato arrivo del cavo ${i.id_cavo}?`}):S()},disabled:p,className:"w-full h-12 text-left justify-start bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 hover:border-blue-300",variant:"outline",children:(0,s.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-sm font-bold text-blue-700",children:"2"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:"arrivo"===F.stato||"completo"===F.stato?"Scollega Arrivo":"Collega Arrivo"}),(0,s.jsx)("div",{className:"text-xs text-blue-600",children:"arrivo"===F.stato||"completo"===F.stato?"Rimuovi collegamento lato arrivo":"Connetti il lato arrivo del cavo"})]})]}),p?(0,s.jsx)(eD.A,{className:"h-4 w-4 animate-spin"}):(0,s.jsx)(ab.A,{className:"h-4 w-4"})]})}),(0,s.jsx)(n.$,{onClick:()=>{i&&("completo"===A().stato?f({open:!0,type:"entrambi",title:"Scollega entrambi i lati",description:`Vuoi scollegare completamente il cavo ${i.id_cavo}? Questa operazione rimuover\xe0 tutti i collegamenti.`}):E())},disabled:p,className:"w-full h-12 text-left justify-start bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100 hover:border-purple-300",variant:"outline",children:(0,s.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-sm font-bold text-purple-700",children:"3"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:"completo"===F.stato?"Scollega Completamente":"Collega Entrambi"}),(0,s.jsx)("div",{className:"text-xs text-purple-600",children:"completo"===F.stato?"Rimuovi tutti i collegamenti":"Connetti entrambi i lati del cavo"})]})]}),p?(0,s.jsx)(eD.A,{className:"h-4 w-4 animate-spin"}):(0,s.jsx)(ab.A,{className:"h-4 w-4"})]})})]})]})]})]}),(0,s.jsx)(eI.Es,{children:(0,s.jsxs)(n.$,{ref:_,variant:"outline",onClick:a,disabled:p,className:"hover:bg-gray-50",children:[(0,s.jsx)(er.A,{className:"mr-2 h-4 w-4"}),"Chiudi"]})})]})}),(0,s.jsx)(aw,{open:j.open,onClose:()=>f({open:!1,type:null,title:"",description:""}),onConfirm:I,title:j.title,description:j.description,isLoading:p,isDangerous:"entrambi"===j.type})]})};var aA=i(29867);function ak({open:e,onClose:a,cavo:i,onSuccess:r,onError:l}){let{cantiere:m}=(0,c.A)(),{toast:x}=(0,aA.dj)(),[u,v]=(0,t.useState)({responsabile_certificazione:"",data_certificazione:new Date().toISOString().split("T")[0],esito_certificazione:"CONFORME",note_certificazione:""}),[g,b]=(0,t.useState)([]),[j,f]=(0,t.useState)(!1),[N,y]=(0,t.useState)(!1),[_,C]=(0,t.useState)(""),[w,z]=(0,t.useState)(""),A=e=>{z(e),setTimeout(()=>z(""),1e3)},k=()=>!!i&&3===(i.collegamento||i.collegamenti||0),S=async()=>{if(!i||!m)return!1;try{return A("Collegamento automatico in corso..."),await d.At.collegaCavo(m.id_cantiere,i.id_cavo,"entrambi","cantiere"),A("Cavo collegato automaticamente"),!0}catch(e){return console.error("Errore nel collegamento automatico:",e),!1}},E=async()=>{if(i&&m){if(!u.responsabile_certificazione){C("Seleziona un responsabile per la certificazione"),A("Errore: Seleziona un responsabile per la certificazione");return}try{if(f(!0),C(""),A("Certificazione in corso..."),!k()&&(A("Collegamento automatico del cavo..."),!await S())){C("Impossibile collegare automaticamente il cavo. Collegalo manualmente prima di certificare."),A("Errore: Impossibile collegare automaticamente il cavo");return}let e={id_cavo:i.id_cavo,responsabile_certificazione:u.responsabile_certificazione,data_certificazione:u.data_certificazione,esito_certificazione:u.esito_certificazione,note_certificazione:u.note_certificazione||null};await d.km.createCertificazione(m.id_cantiere,e);let s=`Certificazione completata per il cavo ${i.id_cavo}`;A(s),x({title:"Successo",description:s}),r&&r(s),a()}catch(a){let e=a.response?.data?.detail||a.message||"Errore durante la certificazione";C(e),A(`Errore: ${e}`),l&&l(e)}finally{f(!1)}}},I=async()=>{if(i&&m)try{f(!0),C(""),A("Generazione PDF in corso...");let e=await d.km.generatePDF(m.id_cantiere,i.id_cavo),a=window.URL.createObjectURL(new Blob([e.data])),s=document.createElement("a");s.href=a,s.setAttribute("download",`certificato_${i.id_cavo}.pdf`),document.body.appendChild(s),s.click(),s.remove(),window.URL.revokeObjectURL(a);let t=`PDF certificato generato per il cavo ${i.id_cavo}`;A(t),x({title:"Successo",description:t}),r&&r(t)}catch(a){let e=a.response?.data?.detail||a.message||"Errore durante la generazione del PDF";C(e),A(`Errore: ${e}`),l&&l(e)}finally{f(!1)}};if(!i)return null;let F="Installato"===i.stato_installazione||"INSTALLATO"===i.stato_installazione||"POSATO"===i.stato_installazione||(i.metri_posati||i.metratura_reale||0)>0,$=k(),O=i.responsabile_partenza&&i.responsabile_arrivo,D=!!i&&(!0===i.certificato||"SI"===i.certificato||"CERTIFICATO"===i.certificato);return(0,s.jsxs)(eI.lG,{open:e,onOpenChange:a,children:[(0,s.jsx)("div",{"aria-live":"polite","aria-atomic":"true",className:"sr-only",children:w}),(0,s.jsxs)(eI.Cf,{className:"sm:max-w-[700px] max-h-[90vh] overflow-y-auto","aria-describedby":"certificazione-description",children:[(0,s.jsxs)(eI.c7,{children:[(0,s.jsxs)(eI.L3,{className:"flex items-center gap-2 text-blue-600",children:[(0,s.jsx)(am.A,{className:"h-5 w-5"}),"Gestione Certificazione - ",i.id_cavo]}),(0,s.jsxs)(eI.rr,{id:"certificazione-description",children:["Certifica il cavo ",i.id_cavo," secondo normativa CEI 64-8 o genera il PDF del certificato"]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-200",children:(0,s.jsxs)("div",{className:"text-sm font-medium text-blue-800",children:["Informazioni Cavo / Tipologia: ",i.tipologia||"N/A"," / Da: ",i.ubicazione_partenza||"N/A"," / Formazione: ",i.sezione||"N/A"," / A: ",i.ubicazione_arrivo||"N/A"," / Metri Posati: ",i.metratura_reale||0," m"]})}),(0,s.jsxs)("div",{className:"p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border",children:[(0,s.jsx)(eF.J,{className:"text-sm font-medium text-gray-700",children:"Stato Cavo"}),(0,s.jsxs)("div",{className:"mt-2 space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[F?(0,s.jsx)(ew.A,{className:"w-4 h-4 text-green-600"}):(0,s.jsx)(aN.A,{className:"w-4 h-4 text-red-500"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:F?"Installato/Posato":"Non installato"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[$?(0,s.jsx)(ew.A,{className:"w-4 h-4 text-green-600"}):(0,s.jsx)(aN.A,{className:"w-4 h-4 text-orange-500"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:$?"Collegato":"Non collegato"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[D?(0,s.jsx)(ew.A,{className:"w-4 h-4 text-green-600"}):(0,s.jsx)(aN.A,{className:"w-4 h-4 text-gray-400"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:D?"Certificato":"Non certificato"})]})]})]}),!F&&(0,s.jsxs)(o.Fc,{className:"border-red-200 bg-red-50",children:[(0,s.jsx)(aN.A,{className:"h-4 w-4 text-red-600"}),(0,s.jsxs)(o.TN,{className:"text-red-800",children:[(0,s.jsx)("strong",{children:"ATTENZIONE:"})," Il cavo deve essere installato/posato prima di poter essere certificato secondo CEI 64-8."]})]}),F&&!($&&O)&&(0,s.jsxs)(o.Fc,{className:"border-amber-200 bg-amber-50",children:[(0,s.jsx)(aN.A,{className:"h-4 w-4 text-amber-600"}),(0,s.jsxs)(o.TN,{className:"text-amber-800",children:[(0,s.jsx)("strong",{children:"ATTENZIONE:"}),' Il cavo non risulta completamente collegato. Durante la certificazione sar\xe0 possibile collegarlo automaticamente a "cantiere" su entrambi i lati.']})]}),_&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(aN.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:_})]}),D?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)(o.Fc,{className:"border-green-200 bg-green-50",children:[(0,s.jsx)(am.A,{className:"h-4 w-4 text-green-600"}),(0,s.jsxs)(o.TN,{className:"text-green-800",children:[(0,s.jsx)("strong",{children:"CERTIFICATO:"})," Questo cavo \xe8 gi\xe0 stato certificato. Puoi generare il PDF del certificato."]})]}),(0,s.jsxs)(n.$,{onClick:I,disabled:j,className:"w-full bg-green-600 hover:bg-green-700 text-white",size:"lg",children:[j?(0,s.jsx)(eD.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,s.jsx)(ez.A,{className:"h-4 w-4 mr-2"}),"Genera PDF Certificato"]})]}):F&&(0,s.jsxs)("div",{className:"space-y-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border-2 border-blue-200",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-blue-800 mb-4",children:"\uD83D\uDCCB Dati Certificazione CEI 64-8"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"responsabile",className:"text-sm font-medium text-gray-700",children:"Responsabile Certificazione *"}),(0,s.jsxs)(p.l6,{value:u.responsabile_certificazione,onValueChange:e=>v(a=>({...a,responsabile_certificazione:e})),disabled:N,children:[(0,s.jsx)(p.bq,{className:"border-2 border-gray-300 focus:border-blue-500",children:(0,s.jsx)(p.yv,{placeholder:"Seleziona responsabile..."})}),(0,s.jsx)(p.gC,{children:g.map(e=>(0,s.jsxs)(p.eb,{value:e.nome_responsabile,children:[e.nome_responsabile,e.numero_telefono&&` - ${e.numero_telefono}`]},e.id))})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"data",className:"text-sm font-medium text-gray-700",children:"Data Certificazione"}),(0,s.jsx)(h.p,{id:"data",type:"date",value:u.data_certificazione,onChange:e=>v(a=>({...a,data_certificazione:e.target.value})),className:"border-2 border-gray-300 focus:border-blue-500"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"esito",className:"text-sm font-medium text-gray-700",children:"Esito Certificazione"}),(0,s.jsxs)(p.l6,{value:u.esito_certificazione,onValueChange:e=>v(a=>({...a,esito_certificazione:e})),children:[(0,s.jsx)(p.bq,{className:"border-2 border-gray-300 focus:border-blue-500",children:(0,s.jsx)(p.yv,{})}),(0,s.jsxs)(p.gC,{children:[(0,s.jsx)(p.eb,{value:"CONFORME",children:"✅ CONFORME"}),(0,s.jsx)(p.eb,{value:"NON_CONFORME",children:"❌ NON CONFORME"}),(0,s.jsx)(p.eb,{value:"CONFORME_CON_RISERVA",children:"⚠️ CONFORME CON RISERVA"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"note",className:"text-sm font-medium text-gray-700",children:"Note (opzionale)"}),(0,s.jsx)(e$.T,{id:"note",placeholder:"Inserisci eventuali note sulla certificazione...",value:u.note_certificazione,onChange:e=>v(a=>({...a,note_certificazione:e.target.value})),rows:3,className:"border-2 border-gray-300 focus:border-blue-500"})]}),(0,s.jsxs)(n.$,{onClick:E,disabled:j||!u.responsabile_certificazione,className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3",size:"lg",children:[j?(0,s.jsx)(eD.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,s.jsx)(am.A,{className:"h-4 w-4 mr-2"}),"Certifica Cavo CEI 64-8"]})]})]}),(0,s.jsx)(eI.Es,{className:"border-t pt-4",children:(0,s.jsxs)(n.$,{variant:"outline",onClick:a,disabled:j,className:"flex items-center gap-2",children:[(0,s.jsx)(er.A,{className:"h-4 w-4"}),"Chiudi"]})})]})]})}var aS=i(6727);function aE({open:e,onClose:a,caviSelezionati:i,tipoComanda:r,onSuccess:l,onError:m}){let{cantiere:x}=(0,c.A)(),[u,h]=(0,t.useState)({tipo_comanda:r||"POSA",responsabile:"",note:""}),[v,g]=(0,t.useState)([]),[b,j]=(0,t.useState)(!1),[f,N]=(0,t.useState)(!1),[y,_]=(0,t.useState)(""),C=async()=>{if(x){if(!u.responsabile)return void _("Seleziona un responsabile per la comanda");if(0===i.length)return void _("Seleziona almeno un cavo per la comanda");try{j(!0),_("");let e={tipo_comanda:u.tipo_comanda,responsabile:u.responsabile,note:u.note||null},s=await d.CV.createComandaWithCavi(x.id_cantiere,e,i);l(`Comanda ${s.data.codice_comanda} creata con successo per ${i.length} cavi`),a()}catch(e){m(e.response?.data?.detail||e.message||"Errore durante la creazione della comanda")}finally{j(!1)}}};return(0,s.jsx)(eI.lG,{open:e,onOpenChange:a,children:(0,s.jsxs)(eI.Cf,{className:"sm:max-w-[600px]",children:[(0,s.jsxs)(eI.c7,{children:[(0,s.jsxs)(eI.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(aS.A,{className:"h-5 w-5"}),"Crea Nuova Comanda"]}),(0,s.jsxs)(eI.rr,{children:["Crea una nuova comanda per ",i.length," cavi selezionati"]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,s.jsxs)(eF.J,{className:"text-sm font-medium",children:["Cavi Selezionati (",i.length,")"]}),(0,s.jsx)("div",{className:"mt-2 max-h-32 overflow-y-auto",children:(0,s.jsxs)("div",{className:"flex flex-wrap gap-1",children:[i.slice(0,10).map(e=>(0,s.jsx)("span",{className:"inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded",children:e},e)),i.length>10&&(0,s.jsxs)("span",{className:"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded",children:["+",i.length-10," altri..."]})]})})]}),y&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(aN.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:y})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"tipo",children:"Tipo Comanda *"}),(0,s.jsxs)(p.l6,{value:u.tipo_comanda,onValueChange:e=>h(a=>({...a,tipo_comanda:e})),children:[(0,s.jsx)(p.bq,{children:(0,s.jsx)(p.yv,{})}),(0,s.jsxs)(p.gC,{children:[(0,s.jsx)(p.eb,{value:"POSA",children:"\uD83D\uDD27 Posa Cavi"}),(0,s.jsx)(p.eb,{value:"COLLEGAMENTO_PARTENZA",children:"\uD83D\uDD0C Collegamento Partenza"}),(0,s.jsx)(p.eb,{value:"COLLEGAMENTO_ARRIVO",children:"⚡ Collegamento Arrivo"}),(0,s.jsx)(p.eb,{value:"CERTIFICAZIONE",children:"\uD83D\uDCCB Certificazione"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"responsabile",children:"Responsabile *"}),(0,s.jsxs)(p.l6,{value:u.responsabile,onValueChange:e=>h(a=>({...a,responsabile:e})),disabled:f,children:[(0,s.jsx)(p.bq,{children:(0,s.jsx)(p.yv,{placeholder:"Seleziona responsabile..."})}),(0,s.jsx)(p.gC,{children:v.map(e=>(0,s.jsx)(p.eb,{value:e.nome_responsabile,children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(eA.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:e.nome_responsabile}),e.numero_telefono&&(0,s.jsxs)("span",{className:"text-xs text-gray-500",children:["- ",e.numero_telefono]})]})},e.id))})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"note",children:"Note (opzionale)"}),(0,s.jsx)(e$.T,{id:"note",placeholder:"Inserisci eventuali note per la comanda...",value:u.note,onChange:e=>h(a=>({...a,note:e.target.value})),rows:3})]}),(0,s.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,s.jsx)(eF.J,{className:"text-sm font-medium",children:"Riepilogo Comanda"}),(0,s.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Tipo:"})," ",(e=>{switch(e){case"POSA":return"Posa Cavi";case"COLLEGAMENTO_PARTENZA":return"Collegamento Partenza";case"COLLEGAMENTO_ARRIVO":return"Collegamento Arrivo";case"CERTIFICAZIONE":return"Certificazione";default:return e}})(u.tipo_comanda)]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Responsabile:"})," ",u.responsabile||"Non selezionato"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Cavi:"})," ",i.length," selezionati"]}),u.note&&(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Note:"})," ",u.note]})]})]})]}),(0,s.jsxs)(eI.Es,{children:[(0,s.jsx)(n.$,{variant:"outline",onClick:a,disabled:b,children:"Annulla"}),(0,s.jsxs)(n.$,{onClick:C,disabled:b||!u.responsabile||0===i.length,children:[b?(0,s.jsx)(eD.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,s.jsx)(aS.A,{className:"h-4 w-4 mr-2"}),"Crea Comanda"]})]})]})})}var aI=i(16023);let aF=(0,ea.A)("file-spreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]]);function a$({open:e,onClose:a,tipo:i,onSuccess:r,onError:l}){let{cantiere:m}=(0,c.A)(),[x,u]=(0,t.useState)(null),[p,v]=(0,t.useState)(""),[g,b]=(0,t.useState)(!1),[j,f]=(0,t.useState)(""),[N,y]=(0,t.useState)(0),_=(0,t.useRef)(null),C=async()=>{if(x&&m){if("cavi"===i&&!p.trim())return void f("Inserisci il codice revisione per l'importazione cavi");try{let e;if(b(!0),f(""),y(0),e="cavi"===i?await d.mg.importCavi(m.id_cantiere,x,p.trim()):await d.mg.importBobine(m.id_cantiere,x),y(100),e.data.success){let s=e.data.details,t=e.data.message;"cavi"===i&&s?.cavi_importati?t+=` (${s.cavi_importati} cavi importati)`:"bobine"===i&&s?.bobine_importate&&(t+=` (${s.bobine_importate} bobine importate)`),r(t),a()}else l(e.data.message||"Errore durante l'importazione")}catch(e){l(e.response?.data?.detail||e.message||"Errore durante l'importazione del file")}finally{b(!1),y(0)}}},w=()=>{g||(u(null),v(""),f(""),y(0),_.current&&(_.current.value=""),a())},z=()=>"cavi"===i?"Cavi":"Bobine";return(0,s.jsx)(eI.lG,{open:e,onOpenChange:w,children:(0,s.jsxs)(eI.Cf,{className:"sm:max-w-[600px]",children:[(0,s.jsxs)(eI.c7,{children:[(0,s.jsxs)(eI.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(aI.A,{className:"h-5 w-5"}),"Importa ",z()," da Excel"]}),(0,s.jsxs)(eI.rr,{children:["Carica un file Excel per importare ",z().toLowerCase()," nel cantiere"]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,s.jsx)(eF.J,{className:"text-sm font-medium",children:"Requisiti File"}),(0,s.jsx)("ul",{className:"mt-2 space-y-1 text-sm text-gray-600",children:("cavi"===i?["File Excel (.xlsx o .xls)","Colonne richieste: ID_CAVO, SISTEMA, UTILITY, TIPOLOGIA, ecc.","Prima riga deve contenere le intestazioni","Codice revisione obbligatorio per tracciabilit\xe0"]:["File Excel (.xlsx o .xls)","Colonne richieste: NUMERO_BOBINA, UTILITY, TIPOLOGIA, METRI_TOTALI, ecc.","Prima riga deve contenere le intestazioni","I metri residui saranno impostati uguali ai metri totali"]).map((e,a)=>(0,s.jsxs)("li",{className:"flex items-start gap-2",children:[(0,s.jsx)("span",{className:"text-blue-500 mt-0.5",children:"•"}),(0,s.jsx)("span",{children:e})]},a))})]}),j&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(aN.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:j})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"file",children:"File Excel *"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(h.p,{ref:_,id:"file",type:"file",accept:".xlsx,.xls",onChange:e=>{let a=e.target.files?.[0];if(a){if(!["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel"].includes(a.type)&&!a.name.toLowerCase().endsWith(".xlsx")&&!a.name.toLowerCase().endsWith(".xls"))return void f("Seleziona un file Excel valido (.xlsx o .xls)");u(a),f("")}},disabled:g,className:"flex-1"}),x&&(0,s.jsxs)("div",{className:"flex items-center gap-1 text-green-600",children:[(0,s.jsx)(ew.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"text-sm",children:"File selezionato"})]})]}),x&&(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,s.jsx)(aF,{className:"h-4 w-4 inline mr-1"}),x.name," (",(x.size/1024/1024).toFixed(2)," MB)"]})]}),"cavi"===i&&(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"revisione",children:"Codice Revisione *"}),(0,s.jsx)(h.p,{id:"revisione",value:p,onChange:e=>v(e.target.value),placeholder:"es. REV001, V1.0, 2024-01",disabled:g}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Codice identificativo della revisione per tracciabilit\xe0 delle modifiche"})]}),g&&(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(eD.A,{className:"h-4 w-4 animate-spin"}),(0,s.jsx)("span",{className:"text-sm",children:"Caricamento in corso..."})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${N}%`}})})]}),x&&(0,s.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,s.jsx)(eF.J,{className:"text-sm font-medium",children:"Riepilogo Importazione"}),(0,s.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Tipo:"})," ",z()]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"File:"})," ",x.name]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Dimensione:"})," ",(x.size/1024/1024).toFixed(2)," MB"]}),"cavi"===i&&p&&(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Revisione:"})," ",p]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Cantiere:"})," ",m?.nome_cantiere]})]})]})]}),(0,s.jsxs)(eI.Es,{children:[(0,s.jsx)(n.$,{variant:"outline",onClick:w,disabled:g,children:"Annulla"}),(0,s.jsxs)(n.$,{onClick:C,disabled:g||!x||"cavi"===i&&!p.trim(),children:[g?(0,s.jsx)(eD.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,s.jsx)(aI.A,{className:"h-4 w-4 mr-2"}),"Importa ",z()]})]})]})})}var aO=i(61611);function aD({open:e,onClose:a,onSuccess:i,onError:r}){let{cantiere:l}=(0,c.A)(),[m,u]=(0,t.useState)({cavi:!0,bobine:!0,comande:!1,certificazioni:!1,responsabili:!1}),[h,p]=(0,t.useState)(!1),[v,g]=(0,t.useState)(""),b=(e,a)=>{u(i=>({...i,[e]:a}))},j=async()=>{if(l)try{p(!0);let e=await d.mg.exportCavi(l.id_cantiere),a=window.URL.createObjectURL(new Blob([e.data])),s=document.createElement("a");s.href=a,s.setAttribute("download",`cavi_${l.nome_cantiere}_${new Date().toISOString().split("T")[0]}.xlsx`),document.body.appendChild(s),s.click(),s.remove(),window.URL.revokeObjectURL(a),i("Export cavi completato con successo")}catch(e){r(e.response?.data?.detail||e.message||"Errore durante l'export dei cavi")}finally{p(!1)}},f=async()=>{if(l)try{p(!0);let e=await d.mg.exportBobine(l.id_cantiere),a=window.URL.createObjectURL(new Blob([e.data])),s=document.createElement("a");s.href=a,s.setAttribute("download",`bobine_${l.nome_cantiere}_${new Date().toISOString().split("T")[0]}.xlsx`),document.body.appendChild(s),s.click(),s.remove(),window.URL.revokeObjectURL(a),i("Export bobine completato con successo")}catch(e){r(e.response?.data?.detail||e.message||"Errore durante l'export delle bobine")}finally{p(!1)}},N=async()=>{if(l)try{p(!0),g("");let e=[];m.cavi&&e.push(j()),m.bobine&&e.push(f()),m.comande,m.certificazioni,m.responsabili,await Promise.all(e);let s=Object.values(m).filter(Boolean).length;i(`Export completato: ${s} file scaricati`),a()}catch(e){r(e.response?.data?.detail||e.message||"Errore durante l'export dei dati")}finally{p(!1)}},y=[{key:"cavi",label:"Cavi",description:"Esporta tutti i cavi del cantiere con stato, collegamenti e certificazioni",icon:(0,s.jsx)(aO.A,{className:"h-4 w-4"}),available:!0},{key:"bobine",label:"Bobine",description:"Esporta tutte le bobine del parco cavi con metri residui e assegnazioni",icon:(0,s.jsx)(aF,{className:"h-4 w-4"}),available:!0},{key:"comande",label:"Comande",description:"Esporta tutte le comande con cavi assegnati e responsabili",icon:(0,s.jsx)(aF,{className:"h-4 w-4"}),available:!1},{key:"certificazioni",label:"Certificazioni",description:"Esporta tutte le certificazioni con esiti e responsabili",icon:(0,s.jsx)(aF,{className:"h-4 w-4"}),available:!1},{key:"responsabili",label:"Responsabili",description:"Esporta tutti i responsabili con contatti e ruoli",icon:(0,s.jsx)(aF,{className:"h-4 w-4"}),available:!1}];return(0,s.jsx)(eI.lG,{open:e,onOpenChange:a,children:(0,s.jsxs)(eI.Cf,{className:"sm:max-w-[600px]",children:[(0,s.jsxs)(eI.c7,{children:[(0,s.jsxs)(eI.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(ez.A,{className:"h-5 w-5"}),"Esporta Dati Cantiere"]}),(0,s.jsxs)(eI.rr,{children:["Seleziona i dati da esportare dal cantiere ",l?.nome_cantiere]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[v&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(aN.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:v})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(eF.J,{className:"text-sm font-medium",children:"Seleziona Dati da Esportare"}),y.map(e=>(0,s.jsxs)("div",{className:`flex items-start space-x-3 p-3 rounded-lg border ${e.available?"bg-white":"bg-gray-50"}`,children:[(0,s.jsx)(x.S,{id:e.key,checked:m[e.key],onCheckedChange:a=>b(e.key,a),disabled:!e.available||h}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,(0,s.jsxs)(eF.J,{htmlFor:e.key,className:`font-medium ${!e.available?"text-gray-500":""}`,children:[e.label,!e.available&&(0,s.jsx)("span",{className:"ml-2 text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded",children:"In sviluppo"})]})]}),(0,s.jsx)("p",{className:`text-sm mt-1 ${!e.available?"text-gray-400":"text-gray-600"}`,children:e.description})]})]},e.key))]}),(0,s.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,s.jsx)(eF.J,{className:"text-sm font-medium",children:"Informazioni Export"}),(0,s.jsxs)("ul",{className:"mt-2 space-y-1 text-sm text-gray-600",children:[(0,s.jsx)("li",{children:"• I file saranno scaricati in formato Excel (.xlsx)"}),(0,s.jsx)("li",{children:"• I nomi file includeranno data e nome cantiere"}),(0,s.jsx)("li",{children:"• I dati esportati riflettono lo stato attuale del database"}),(0,s.jsx)("li",{children:"• L'export non modifica i dati originali"})]})]}),Object.values(m).filter(Boolean).length>0&&(0,s.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,s.jsx)(eF.J,{className:"text-sm font-medium",children:"Riepilogo Export"}),(0,s.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Cantiere:"})," ",l?.nome_cantiere]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"File da scaricare:"})," ",Object.values(m).filter(Boolean).length]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Data export:"})," ",new Date().toLocaleDateString("it-IT")]})]})]})]}),(0,s.jsxs)(eI.Es,{children:[(0,s.jsx)(n.$,{variant:"outline",onClick:a,disabled:h,children:"Annulla"}),(0,s.jsxs)(n.$,{onClick:N,disabled:h||0===Object.values(m).filter(Boolean).length,children:[h?(0,s.jsx)(eD.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,s.jsx)(ez.A,{className:"h-4 w-4 mr-2"}),"Esporta ",Object.values(m).filter(Boolean).length>0?`(${Object.values(m).filter(Boolean).length})`:""]})]})]})})}let aR={id_cavo:"",utility:"",sistema:"",colore_cavo:"",tipologia:"",sezione:"",ubicazione_partenza:"",utenza_partenza:"",descrizione_utenza_partenza:"",ubicazione_arrivo:"",utenza_arrivo:"",descrizione_utenza_arrivo:"",metri_teorici:""};function aT({open:e,onClose:a,cantiere:i,onSuccess:r,onError:l}){let{cantiere:m}=(0,c.A)(),x=i||m,[u,p]=(0,t.useState)(aR),[v,g]=(0,t.useState)(!1),[b,j]=(0,t.useState)({}),f=(e,a)=>{p(i=>({...i,[e]:"id_cavo"===e?a.toUpperCase():a})),b[e]&&j(a=>{let i={...a};return delete i[e],i})},N=()=>{let e={};if(u.id_cavo.trim()||(e.id_cavo="ID Cavo \xe8 obbligatorio"),u.utility.trim()||(e.utility="Utility \xe8 obbligatoria"),u.metri_teorici.trim()){let a=parseFloat(u.metri_teorici);(isNaN(a)||a<=0)&&(e.metri_teorici="Metri Teorici deve essere un numero positivo")}else e.metri_teorici="Metri Teorici sono obbligatori";return j(e),0===Object.keys(e).length},y=async()=>{if(N()){if(!x?.id_cantiere)return void l("Cantiere non selezionato");try{g(!0);let e={...u,metri_teorici:parseFloat(u.metri_teorici),id_cantiere:x.id_cantiere};await d.At.createCavo(parseInt(x.id_cantiere),e),r(`Cavo ${u.id_cavo} aggiunto con successo`),_()}catch(e){l(e.response?.data?.detail||e.message||"Errore durante l'aggiunta del cavo")}finally{g(!1)}}},_=()=>{v||(p(aR),j({}),a())};return(0,s.jsx)(eI.lG,{open:e,onOpenChange:_,children:(0,s.jsxs)(eI.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,s.jsxs)(eI.c7,{children:[(0,s.jsxs)(eI.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(ef.A,{className:"h-5 w-5"}),"Aggiungi Nuovo Cavo"]}),(0,s.jsxs)(eI.rr,{children:["Inserisci i dati del nuovo cavo per il cantiere ",x?.nome_cantiere]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Informazioni Generali"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"id_cavo",children:"ID Cavo *"}),(0,s.jsx)(h.p,{id:"id_cavo",value:u.id_cavo,onChange:e=>f("id_cavo",e.target.value),placeholder:"Es. C001",className:b.id_cavo?"border-red-500":""}),b.id_cavo&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:b.id_cavo})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"utility",children:"Utility *"}),(0,s.jsx)(h.p,{id:"utility",value:u.utility,onChange:e=>f("utility",e.target.value),placeholder:"Es. ENEL",className:b.utility?"border-red-500":""}),b.utility&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:b.utility})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"sistema",children:"Sistema"}),(0,s.jsx)(h.p,{id:"sistema",value:u.sistema,onChange:e=>f("sistema",e.target.value),placeholder:"Es. MT"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Caratteristiche Tecniche"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"colore_cavo",children:"Colore Cavo"}),(0,s.jsx)(h.p,{id:"colore_cavo",value:u.colore_cavo,onChange:e=>f("colore_cavo",e.target.value),placeholder:"Es. Nero"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"tipologia",children:"Tipologia"}),(0,s.jsx)(h.p,{id:"tipologia",value:u.tipologia,onChange:e=>f("tipologia",e.target.value),placeholder:"Es. ARE4H5E"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"sezione",children:"Formazione"}),(0,s.jsx)(h.p,{id:"sezione",value:u.sezione,onChange:e=>f("sezione",e.target.value),placeholder:"Es. 3X240+120"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Partenza"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"ubicazione_partenza",children:"Ubicazione Partenza"}),(0,s.jsx)(h.p,{id:"ubicazione_partenza",value:u.ubicazione_partenza,onChange:e=>f("ubicazione_partenza",e.target.value)})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"utenza_partenza",children:"Utenza Partenza"}),(0,s.jsx)(h.p,{id:"utenza_partenza",value:u.utenza_partenza,onChange:e=>f("utenza_partenza",e.target.value)})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"descrizione_utenza_partenza",children:"Descrizione Utenza Partenza"}),(0,s.jsx)(h.p,{id:"descrizione_utenza_partenza",value:u.descrizione_utenza_partenza,onChange:e=>f("descrizione_utenza_partenza",e.target.value)})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Arrivo"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"ubicazione_arrivo",children:"Ubicazione Arrivo"}),(0,s.jsx)(h.p,{id:"ubicazione_arrivo",value:u.ubicazione_arrivo,onChange:e=>f("ubicazione_arrivo",e.target.value)})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"utenza_arrivo",children:"Utenza Arrivo"}),(0,s.jsx)(h.p,{id:"utenza_arrivo",value:u.utenza_arrivo,onChange:e=>f("utenza_arrivo",e.target.value)})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"descrizione_utenza_arrivo",children:"Descrizione Utenza Arrivo"}),(0,s.jsx)(h.p,{id:"descrizione_utenza_arrivo",value:u.descrizione_utenza_arrivo,onChange:e=>f("descrizione_utenza_arrivo",e.target.value)})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Metratura"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"metri_teorici",children:"Metri Teorici *"}),(0,s.jsx)(h.p,{id:"metri_teorici",type:"number",step:"0.01",min:"0",value:u.metri_teorici,onChange:e=>f("metri_teorici",e.target.value),placeholder:"Es. 100.50",className:b.metri_teorici?"border-red-500":""}),b.metri_teorici&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:b.metri_teorici})]})})]}),Object.keys(b).length>0&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(aN.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:"Correggere i campi evidenziati in rosso prima di salvare."})]})]}),(0,s.jsxs)(eI.Es,{children:[(0,s.jsx)(n.$,{variant:"outline",onClick:_,disabled:v,children:"Annulla"}),(0,s.jsxs)(n.$,{onClick:y,disabled:v,children:[v&&(0,s.jsx)(eD.A,{className:"mr-2 h-4 w-4 animate-spin"}),v?"Aggiungendo...":"Aggiungi Cavo"]})]})]})})}function aP({open:e,onClose:a,cavo:i,cantiere:r,onSuccess:l,onError:m}){let{cantiere:x}=(0,c.A)(),u=r||x,[p,v]=(0,t.useState)({id_cavo:"",utility:"",sistema:"",colore_cavo:"",tipologia:"",sezione:"",ubicazione_partenza:"",utenza_partenza:"",descrizione_utenza_partenza:"",ubicazione_arrivo:"",utenza_arrivo:"",descrizione_utenza_arrivo:"",metri_teorici:""}),[g,b]=(0,t.useState)(!1),[j,f]=(0,t.useState)({}),N=(e,a)=>{v(i=>({...i,[e]:"id_cavo"===e?a.toUpperCase():a})),j[e]&&f(a=>{let i={...a};return delete i[e],i})},y=()=>{let e={};if(p.id_cavo.trim()||(e.id_cavo="ID Cavo \xe8 obbligatorio"),p.utility.trim()||(e.utility="Utility \xe8 obbligatoria"),p.metri_teorici.trim()){let a=parseFloat(p.metri_teorici);(isNaN(a)||a<=0)&&(e.metri_teorici="Metri Teorici deve essere un numero positivo")}else e.metri_teorici="Metri Teorici sono obbligatori";return f(e),0===Object.keys(e).length},_=async()=>{if(y()){if(!i?.id_cavo)return void m("Cavo non selezionato");if(!u?.id_cantiere)return void m("Cantiere non selezionato");try{b(!0);let e={...p,metri_teorici:parseFloat(p.metri_teorici),id_cantiere:u.id_cantiere};await d.At.updateCavo(parseInt(u.id_cantiere),i.id_cavo,e),l(`Cavo ${p.id_cavo} modificato con successo`),C()}catch(e){m(e.response?.data?.detail||e.message||"Errore durante la modifica del cavo")}finally{b(!1)}}},C=()=>{g||(f({}),a())},w=i&&(i.metratura_reale>0||"da installare"!==i.stato_installazione);return i?(0,s.jsx)(eI.lG,{open:e,onOpenChange:C,children:(0,s.jsxs)(eI.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,s.jsxs)(eI.c7,{children:[(0,s.jsxs)(eI.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(ad.A,{className:"h-5 w-5"}),"Modifica Cavo: ",i.id_cavo]}),(0,s.jsxs)(eI.rr,{children:["Modifica i dati del cavo nel cantiere ",u?.nome_cantiere]})]}),w&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(eR.A,{className:"h-4 w-4"}),(0,s.jsxs)(o.TN,{children:[(0,s.jsx)("strong",{children:"Attenzione:"})," Questo cavo \xe8 gi\xe0 stato installato. La modifica potrebbe influire sui dati di installazione esistenti."]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Informazioni Generali"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"id_cavo",children:"ID Cavo *"}),(0,s.jsx)(h.p,{id:"id_cavo",value:p.id_cavo,onChange:e=>N("id_cavo",e.target.value),placeholder:"Es. C001",className:j.id_cavo?"border-red-500":""}),j.id_cavo&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:j.id_cavo})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"utility",children:"Utility *"}),(0,s.jsx)(h.p,{id:"utility",value:p.utility,onChange:e=>N("utility",e.target.value),placeholder:"Es. ENEL",className:j.utility?"border-red-500":""}),j.utility&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:j.utility})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"sistema",children:"Sistema"}),(0,s.jsx)(h.p,{id:"sistema",value:p.sistema,onChange:e=>N("sistema",e.target.value),placeholder:"Es. MT"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Caratteristiche Tecniche"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"colore_cavo",children:"Colore Cavo"}),(0,s.jsx)(h.p,{id:"colore_cavo",value:p.colore_cavo,onChange:e=>N("colore_cavo",e.target.value),placeholder:"Es. Nero"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"tipologia",children:"Tipologia"}),(0,s.jsx)(h.p,{id:"tipologia",value:p.tipologia,onChange:e=>N("tipologia",e.target.value),placeholder:"Es. ARE4H5E"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"sezione",children:"Formazione"}),(0,s.jsx)(h.p,{id:"sezione",value:p.sezione,onChange:e=>N("sezione",e.target.value),placeholder:"Es. 3X240+120"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Partenza"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"ubicazione_partenza",children:"Ubicazione Partenza"}),(0,s.jsx)(h.p,{id:"ubicazione_partenza",value:p.ubicazione_partenza,onChange:e=>N("ubicazione_partenza",e.target.value)})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"utenza_partenza",children:"Utenza Partenza"}),(0,s.jsx)(h.p,{id:"utenza_partenza",value:p.utenza_partenza,onChange:e=>N("utenza_partenza",e.target.value)})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"descrizione_utenza_partenza",children:"Descrizione Utenza Partenza"}),(0,s.jsx)(h.p,{id:"descrizione_utenza_partenza",value:p.descrizione_utenza_partenza,onChange:e=>N("descrizione_utenza_partenza",e.target.value)})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Arrivo"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"ubicazione_arrivo",children:"Ubicazione Arrivo"}),(0,s.jsx)(h.p,{id:"ubicazione_arrivo",value:p.ubicazione_arrivo,onChange:e=>N("ubicazione_arrivo",e.target.value)})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"utenza_arrivo",children:"Utenza Arrivo"}),(0,s.jsx)(h.p,{id:"utenza_arrivo",value:p.utenza_arrivo,onChange:e=>N("utenza_arrivo",e.target.value)})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"descrizione_utenza_arrivo",children:"Descrizione Utenza Arrivo"}),(0,s.jsx)(h.p,{id:"descrizione_utenza_arrivo",value:p.descrizione_utenza_arrivo,onChange:e=>N("descrizione_utenza_arrivo",e.target.value)})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Metratura"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eF.J,{htmlFor:"metri_teorici",children:"Metri Teorici *"}),(0,s.jsx)(h.p,{id:"metri_teorici",type:"number",step:"0.01",min:"0",value:p.metri_teorici,onChange:e=>N("metri_teorici",e.target.value),placeholder:"Es. 100.50",className:j.metri_teorici?"border-red-500":""}),j.metri_teorici&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:j.metri_teorici})]})})]}),Object.keys(j).length>0&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(aN.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:"Correggere i campi evidenziati in rosso prima di salvare."})]})]}),(0,s.jsxs)(eI.Es,{children:[(0,s.jsx)(n.$,{variant:"outline",onClick:C,disabled:g,children:"Annulla"}),(0,s.jsxs)(n.$,{onClick:_,disabled:g,children:[g&&(0,s.jsx)(eD.A,{className:"mr-2 h-4 w-4 animate-spin"}),g?"Salvando...":"Salva Modifiche"]})]})]})}):null}var aM=i(72942),aL=i(43),aB=i(18853),aU=i(83721),aG="Radio",[aq,aV]=(0,b.A)(aG),[aJ,aZ]=aq(aG),aH=t.forwardRef((e,a)=>{let{__scopeRadio:i,name:r,checked:l=!1,required:n,disabled:o,value:c="on",onCheck:d,form:m,...x}=e,[u,h]=t.useState(null),p=(0,g.s)(a,e=>h(e)),b=t.useRef(!1),j=!u||m||!!u.closest("form");return(0,s.jsxs)(aJ,{scope:i,checked:l,disabled:o,children:[(0,s.jsx)(z.sG.button,{type:"button",role:"radio","aria-checked":l,"data-state":aY(l),"data-disabled":o?"":void 0,disabled:o,value:c,...x,ref:p,onClick:(0,v.m)(e.onClick,e=>{l||d?.(),j&&(b.current=e.isPropagationStopped(),b.current||e.stopPropagation())})}),j&&(0,s.jsx)(aX,{control:u,bubbles:!b.current,name:r,value:c,checked:l,required:n,disabled:o,form:m,style:{transform:"translateX(-100%)"}})]})});aH.displayName=aG;var aW="RadioIndicator",aK=t.forwardRef((e,a)=>{let{__scopeRadio:i,forceMount:t,...r}=e,l=aZ(aW,i);return(0,s.jsx)(w.C,{present:t||l.checked,children:(0,s.jsx)(z.sG.span,{"data-state":aY(l.checked),"data-disabled":l.disabled?"":void 0,...r,ref:a})})});aK.displayName=aW;var aX=t.forwardRef(({__scopeRadio:e,control:a,checked:i,bubbles:r=!0,...l},n)=>{let o=t.useRef(null),c=(0,g.s)(o,n),d=(0,aU.Z)(i),m=(0,aB.X)(a);return t.useEffect(()=>{let e=o.current;if(!e)return;let a=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(d!==i&&a){let s=new Event("click",{bubbles:r});a.call(e,i),e.dispatchEvent(s)}},[d,i,r]),(0,s.jsx)(z.sG.input,{type:"radio","aria-hidden":!0,defaultChecked:i,...l,tabIndex:-1,ref:c,style:{...l.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function aY(e){return e?"checked":"unchecked"}aX.displayName="RadioBubbleInput";var aQ=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],a0="RadioGroup",[a1,a2]=(0,b.A)(a0,[aM.RG,aV]),a4=(0,aM.RG)(),a5=aV(),[a3,a6]=a1(a0),a7=t.forwardRef((e,a)=>{let{__scopeRadioGroup:i,name:t,defaultValue:r,value:l,required:n=!1,disabled:o=!1,orientation:c,dir:d,loop:m=!0,onValueChange:x,...u}=e,h=a4(i),p=(0,aL.jH)(d),[v,g]=(0,k.i)({prop:l,defaultProp:r??null,onChange:x,caller:a0});return(0,s.jsx)(a3,{scope:i,name:t,required:n,disabled:o,value:v,onValueChange:g,children:(0,s.jsx)(aM.bL,{asChild:!0,...h,orientation:c,dir:p,loop:m,children:(0,s.jsx)(z.sG.div,{role:"radiogroup","aria-required":n,"aria-orientation":c,"data-disabled":o?"":void 0,dir:p,...u,ref:a})})})});a7.displayName=a0;var a8="RadioGroupItem",a9=t.forwardRef((e,a)=>{let{__scopeRadioGroup:i,disabled:r,...l}=e,n=a6(a8,i),o=n.disabled||r,c=a4(i),d=a5(i),m=t.useRef(null),x=(0,g.s)(a,m),u=n.value===l.value,h=t.useRef(!1);return t.useEffect(()=>{let e=e=>{aQ.includes(e.key)&&(h.current=!0)},a=()=>h.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",a),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",a)}},[]),(0,s.jsx)(aM.q7,{asChild:!0,...c,focusable:!o,active:u,children:(0,s.jsx)(aH,{disabled:o,required:n.required,checked:u,...d,...l,name:n.name,ref:x,onCheck:()=>n.onValueChange(l.value),onKeyDown:(0,v.m)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,v.m)(l.onFocus,()=>{h.current&&m.current?.click()})})})});a9.displayName=a8;var ie=t.forwardRef((e,a)=>{let{__scopeRadioGroup:i,...t}=e,r=a5(i);return(0,s.jsx)(aK,{...r,...t,ref:a})});ie.displayName="RadioGroupIndicator";let ia=(0,ea.A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]),ii=t.forwardRef(({className:e,...a},i)=>(0,s.jsx)(a7,{className:(0,Q.cn)("grid gap-2",e),...a,ref:i}));ii.displayName=a7.displayName;let is=t.forwardRef(({className:e,...a},i)=>(0,s.jsx)(a9,{ref:i,className:(0,Q.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...a,children:(0,s.jsx)(ie,{className:"flex items-center justify-center",children:(0,s.jsx)(ia,{className:"h-2.5 w-2.5 fill-current text-current"})})}));function it({open:e,onClose:a,cavo:i,cantiere:r,onSuccess:l,onError:m}){let{cantiere:x}=(0,c.A)(),u=r||x,[h,p]=(0,t.useState)(!1),[v,g]=(0,t.useState)("spare"),b=async()=>{if(!i?.id_cavo)return void m("Cavo non selezionato");if(!u?.id_cantiere)return void m("Cantiere non selezionato");try{if(p(!0),console.log(`Iniziando operazione ${v} per cavo ${i.id_cavo}`),"spare"===v){console.log("Chiamando markAsSpare API...");let e=await d.At.markAsSpare(parseInt(u.id_cantiere),i.id_cavo,!0);console.log("Risultato markAsSpare:",e),l(`Cavo ${i.id_cavo} marcato come SPARE`)}else{console.log("Chiamando deleteCavo API...");let e=await d.At.deleteCavo(parseInt(u.id_cantiere),i.id_cavo);console.log("Risultato deleteCavo:",e),l(`Cavo ${i.id_cavo} eliminato definitivamente`)}j()}catch(a){console.error("Errore durante operazione:",a);let e=a.response?.data?.detail||a.message||"Errore durante l'eliminazione del cavo";console.error("Messaggio errore:",e),m(e)}finally{p(!1)}},j=()=>{h||(g("spare"),a())};if(!i)return null;let f=i.metratura_reale>0||i.stato_installazione&&"da installare"!==i.stato_installazione.toLowerCase(),N=!f,y={id:i.id_cavo,tipologia:i.tipologia||"N/A",sezione:i.sezione||"N/A",metri_teorici:i.metri_teorici||0,metri_reali:i.metratura_reale||0,stato:i.stato_installazione||"N/A",bobina:i.id_bobina||"N/A"};return(0,s.jsx)(eI.lG,{open:e,onOpenChange:j,children:(0,s.jsxs)(eI.Cf,{className:"max-w-2xl",children:[(0,s.jsxs)(eI.c7,{children:[(0,s.jsxs)(eI.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(eE.A,{className:"h-5 w-5"}),"Elimina Cavo: ",i.id_cavo]}),(0,s.jsxs)(eI.rr,{children:["Scegli come gestire l'eliminazione del cavo dal cantiere ",u?.nome_cantiere]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Informazioni Cavo"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"ID:"}),(0,s.jsx)("span",{className:"ml-2 font-medium",children:y.id})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Tipologia:"}),(0,s.jsx)("span",{className:"ml-2 font-medium",children:y.tipologia})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Formazione:"}),(0,s.jsx)("span",{className:"ml-2 font-medium",children:y.sezione})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Stato:"}),(0,s.jsx)("span",{className:"ml-2 font-medium",children:y.stato})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Metri Teorici:"}),(0,s.jsx)("span",{className:"ml-2 font-medium",children:y.metri_teorici})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Metri Installati:"}),(0,s.jsx)("span",{className:"ml-2 font-medium",children:y.metri_reali})]}),(0,s.jsxs)("div",{className:"col-span-2",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Bobina:"}),(0,s.jsx)("span",{className:"ml-2 font-medium",children:y.bobina})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Modalit\xe0 di Eliminazione"}),(0,s.jsxs)(ii,{value:v,onValueChange:e=>g(e),children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(is,{value:"spare",id:"spare"}),(0,s.jsxs)(eF.J,{htmlFor:"spare",className:"flex-1",children:[(0,s.jsx)("div",{className:"font-medium",children:"Marca come SPARE"}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Il cavo viene marcato come spare/consumato ma rimane nel database per tracciabilit\xe0"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(is,{value:"permanent",id:"permanent",disabled:!N}),(0,s.jsxs)(eF.J,{htmlFor:"permanent",className:`flex-1 ${!N?"opacity-50":""}`,children:[(0,s.jsx)("div",{className:"font-medium",children:"Eliminazione Definitiva"}),(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:["Il cavo viene rimosso completamente dal database",!N&&" (non disponibile per cavi installati)"]})]})]})]})]}),f&&(0,s.jsxs)(o.Fc,{children:[(0,s.jsx)(eN.A,{className:"h-4 w-4"}),(0,s.jsxs)(o.TN,{children:[(0,s.jsx)("strong",{children:"Cavo Installato:"})," Questo cavo ha metri installati. Si consiglia di marcarlo come SPARE piuttosto che eliminarlo definitivamente."]})]}),"permanent"===v&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(eR.A,{className:"h-4 w-4"}),(0,s.jsxs)(o.TN,{children:[(0,s.jsx)("strong",{children:"Attenzione:"})," L'eliminazione definitiva \xe8 irreversibile. Tutti i dati del cavo verranno persi permanentemente."]})]}),"spare"===v&&(0,s.jsxs)(o.Fc,{children:[(0,s.jsx)(eN.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:"Il cavo verr\xe0 marcato come SPARE e non apparir\xe0 pi\xf9 nelle liste attive, ma rimarr\xe0 nel database per eventuali controlli futuri."})]})]}),(0,s.jsxs)(eI.Es,{children:[(0,s.jsx)(n.$,{variant:"outline",onClick:j,disabled:h,children:"Annulla"}),(0,s.jsxs)(n.$,{variant:"destructive",onClick:b,disabled:h,children:[h&&(0,s.jsx)(eD.A,{className:"mr-2 h-4 w-4 animate-spin"}),h?"Eliminando...":"spare"===v?"Marca come SPARE":"Elimina Definitivamente"]})]})]})})}function ir(){let{user:e,cantiere:a,isAuthenticated:i,isLoading:m}=(0,c.A)();(0,r.useRouter)();let x=(0,an.E)(),[u,h]=(0,t.useState)([]),[p,v]=(0,t.useState)([]),[g,b]=(0,t.useState)(!0),[j,f]=(0,t.useState)(""),[N,y]=(0,t.useState)([]),[_,C]=(0,t.useState)(!1),[w,z]=(0,t.useState)([]),[A,k]=(0,t.useState)(""),[S,E]=(0,t.useState)({open:!1,cavo:null}),[I,F]=(0,t.useState)({open:!1,cavo:null}),[$,O]=(0,t.useState)({open:!1,cavo:null}),[D,R]=(0,t.useState)({open:!1,cavo:null}),[T,P]=(0,t.useState)({open:!1}),[M,L]=(0,t.useState)({open:!1}),[B,U]=(0,t.useState)(!1),[G,q]=(0,t.useState)(!1),[V,J]=(0,t.useState)({open:!1,cavo:null}),[Z,H]=(0,t.useState)({open:!1,cavo:null}),[W,K]=(0,t.useState)({totali:0,installati:0,collegati:0,certificati:0,percentualeInstallazione:0,percentualeCollegamento:0,percentualeCertificazione:0,metriTotali:0,metriInstallati:0,metriCollegati:0,metriCertificati:0}),[X,Y]=(0,t.useState)(0),Q=a||(X>0?{id_cantiere:X,commessa:`Cantiere ${X}`}:null),ee=async()=>{try{b(!0),f("");try{let e=await d.At.getCavi(X),a=e.filter(e=>!e.spare),i=e.filter(e=>e.spare);h(a),v(i),ea(a)}catch(e){try{let e=await fetch(`http://localhost:8001/api/cavi/debug/${X}`),a=await e.json();if(a.cavi&&Array.isArray(a.cavi)){let e=a.cavi.filter(e=>!e.spare),i=a.cavi.filter(e=>e.spare);h(e),v(i),ea(e),f("⚠️ Dati caricati tramite endpoint debug (problema autenticazione)")}else throw Error("Formato dati debug non valido")}catch(a){throw e}}}catch(e){f(`Errore nel caricamento dei cavi: ${e.response?.data?.detail||e.message}`)}finally{b(!1)}},ea=e=>{let a=e.length,i=e.filter(e=>(e.metri_posati||e.metratura_reale||0)>0).length,s=e.filter(e=>3===(e.collegamento||e.collegamenti)).length,t=e.filter(e=>e.certificato).length,r=e.reduce((e,a)=>e+(a.metri_teorici||0),0),l=e.reduce((e,a)=>e+(a.metri_posati||0),0);K({totali:a,installati:i,collegati:s,certificati:t,percentualeInstallazione:a>0?Math.round(i/a*100):0,percentualeCollegamento:a>0?Math.round(s/a*100):0,percentualeCertificazione:a>0?Math.round(t/a*100):0,metriTotali:r,metriInstallati:l,metriCollegati:e.filter(e=>3===e.collegamento).reduce((e,a)=>e+(a.metri_posati||0),0),metriCertificati:e.filter(e=>e.certificato).reduce((e,a)=>e+(a.metri_posati||0),0)})},ei=async e=>{try{await new Promise(e=>setTimeout(e,1e3)),h(a=>a.map(a=>a.id_cavo===e?{...a,collegamento:0,collegamenti:0}:a)),await ee(),x.success("Cavo Scollegato",`Il cavo ${e} \xe8 stato scollegato con successo.`)}catch(e){throw x.error("Errore Scollegamento","Impossibile scollegare il cavo. Riprova."),e}},es=async e=>{try{await new Promise(e=>setTimeout(e,2e3));let a=new Blob(["PDF Content"],{type:"application/pdf"}),i=window.URL.createObjectURL(a),s=document.createElement("a");s.href=i,s.download=e.fileName,document.body.appendChild(s),s.click(),window.URL.revokeObjectURL(i),document.body.removeChild(s),x.success("PDF Generato",`Certificato per il cavo ${e.cavoId} generato con successo.`)}catch(e){throw x.error("Errore Generazione PDF","Impossibile generare il certificato. Riprova."),e}},et=async e=>{try{await new Promise(e=>setTimeout(e,1500)),h(a=>a.map(a=>a.id_cavo===e?{...a,certificato:!0,data_certificazione:new Date().toISOString()}:a)),await ee(),x.success("Cavo Certificato",`Il cavo ${e} \xe8 stato certificato con successo.`)}catch(e){throw x.error("Errore Certificazione","Impossibile certificare il cavo. Riprova."),e}},er=(e,a,i)=>{switch(a){case"insert_meters":E({open:!0,cavo:e});break;case"modify_reel":F({open:!0,cavo:e});break;case"view_command":x({title:"Visualizza Comanda",description:`Apertura comanda ${i} per cavo ${e.id_cavo}`});break;case"connect_cable":case"connect_arrival":case"connect_departure":case"disconnect_cable":case"manage_connections":O({open:!0,cavo:e});break;case"create_certificate":case"generate_pdf":R({open:!0,cavo:e})}},el=(e,a)=>{switch(a){case"view_details":x({title:"Visualizza Dettagli",description:`Apertura dettagli per cavo ${e.id_cavo}`});break;case"edit":J({open:!0,cavo:e});break;case"delete":H({open:!0,cavo:e});break;case"add_new":q(!0);break;case"select":N.includes(e.id_cavo)?(y(N.filter(a=>a!==e.id_cavo)),x({title:"Cavo Deselezionato",description:`Cavo ${e.id_cavo} deselezionato`})):(y([...N,e.id_cavo]),x({title:"Cavo Selezionato",description:`Cavo ${e.id_cavo} selezionato`}));break;case"copy_id":navigator.clipboard.writeText(e.id_cavo),x({title:"ID Copiato",description:`ID cavo ${e.id_cavo} copiato negli appunti`});break;case"copy_details":let i=`ID: ${e.id_cavo}, Tipologia: ${e.tipologia}, Formazione: ${e.formazione||e.sezione}, Metri: ${e.metri_teorici}`;navigator.clipboard.writeText(i),x({title:"Dettagli Copiati",description:"Dettagli cavo copiati negli appunti"});break;case"add_to_command":x({title:"Aggiungi a Comanda",description:"Funzione aggiunta a comanda in sviluppo"});break;case"remove_from_command":x({title:"Rimuovi da Comanda",description:"Funzione rimozione da comanda in sviluppo"});break;case"create_command_posa":P({open:!0,tipoComanda:"POSA"});break;case"create_command_collegamento_partenza":P({open:!0,tipoComanda:"COLLEGAMENTO_PARTENZA"});break;case"create_command_collegamento_arrivo":P({open:!0,tipoComanda:"COLLEGAMENTO_ARRIVO"});break;case"create_command_certificazione":P({open:!0,tipoComanda:"CERTIFICAZIONE"});break;case"add_multiple_to_command":x({title:"Aggiungi Tutti a Comanda",description:"Funzione aggiunta multipla a comanda in sviluppo"});break;case"remove_multiple_from_commands":x({title:"Rimuovi Tutti dalle Comande",description:"Funzione rimozione multipla dalle comande in sviluppo"});break;default:x({title:"Azione non implementata",description:`Azione ${a} non ancora implementata`})}},en=e=>{x({title:"Operazione completata",description:e}),ee()},eo=e=>{x({title:"Errore",description:e,variant:"destructive"})};return m||g?(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsx)(eD.A,{className:"h-8 w-8 animate-spin"})}):X?j?(0,s.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(aN.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:j})]}),(0,s.jsx)(n.$,{onClick:ee,className:"mt-4",children:"Riprova"})]}):(0,s.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,s.jsx)(aj,{cavi:u,filteredCavi:w,revisioneCorrente:A,className:"mb-2"}),(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsx)(ah,{cavi:u,loading:g,selectionEnabled:_,selectedCavi:N,onSelectionChange:y,onStatusAction:er,onContextMenuAction:el,onDisconnectCable:ei,onGeneratePDF:es,onCertifyCable:et})}),p.length>0&&(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsxs)(l.Zp,{children:[(0,s.jsx)(l.aR,{children:(0,s.jsxs)(l.ZB,{className:"flex items-center space-x-2",children:[(0,s.jsx)(eS.A,{className:"h-5 w-5"}),(0,s.jsxs)("span",{children:["Cavi Spare (",p.length,")"]})]})}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)(ah,{cavi:p,loading:g,selectionEnabled:!1,onStatusAction:er,onContextMenuAction:el,onDisconnectCable:ei,onGeneratePDF:es,onCertifyCable:et})})]})}),(0,s.jsx)(a_,{open:S.open,onClose:()=>E({open:!1,cavo:null}),cavo:S.cavo,cantiere:Q,onSuccess:en,onError:eo}),(0,s.jsx)(ay,{open:I.open,onClose:()=>F({open:!1,cavo:null}),cavo:I.cavo,cantiere:Q,onSuccess:en,onError:eo}),(0,s.jsx)(az,{open:$.open,onClose:()=>O({open:!1,cavo:null}),cavo:$.cavo,onSuccess:en,onError:eo}),(0,s.jsx)(ak,{open:D.open,onClose:()=>R({open:!1,cavo:null}),cavo:D.cavo,onSuccess:en,onError:eo}),(0,s.jsx)(aE,{open:T.open,onClose:()=>P({open:!1}),caviSelezionati:N,tipoComanda:T.tipoComanda,onSuccess:en,onError:eo}),(0,s.jsx)(a$,{open:M.open,onClose:()=>L({open:!1}),tipo:M.tipo||"cavi",onSuccess:en,onError:eo}),(0,s.jsx)(aD,{open:B,onClose:()=>U(!1),onSuccess:en,onError:eo}),(0,s.jsx)(aT,{open:G,onClose:()=>q(!1),cantiere:a,onSuccess:e=>{en(e),ee()},onError:eo}),(0,s.jsx)(aP,{open:V.open,onClose:()=>J({open:!1,cavo:null}),cavo:V.cavo,cantiere:a,onSuccess:e=>{en(e),ee()},onError:eo}),(0,s.jsx)(it,{open:Z.open,onClose:()=>H({open:!1,cavo:null}),cavo:Z.cavo,cantiere:a,onSuccess:e=>{en(e),ee()},onError:eo})]}):(0,s.jsx)("div",{className:"max-w-[90%] mx-auto p-6",children:(0,s.jsxs)(o.Fc,{children:[(0,s.jsx)(aN.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:"Nessun cantiere selezionato. Seleziona un cantiere per visualizzare i cavi."})]})})}is.displayName=a9.displayName},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86561:(e,a,i)=>{"use strict";i.d(a,{A:()=>s});let s=(0,i(62688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},86691:(e,a,i)=>{"use strict";i.r(a),i.d(a,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c});var s=i(65239),t=i(48088),r=i(88170),l=i.n(r),n=i(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);i.d(a,o);let c={children:["",{children:["cavi",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,10698)),"C:\\CMS\\webapp-nextjs_2\\src\\app\\cavi\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,94431)),"C:\\CMS\\webapp-nextjs_2\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\CMS\\webapp-nextjs_2\\src\\app\\cavi\\page.tsx"],m={require:i,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/cavi/page",pathname:"/cavi",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},94735:e=>{"use strict";e.exports=require("events")}};var a=require("../../webpack-runtime.js");a.C(e);var i=e=>a(a.s=e),s=a.X(0,[447,588,658,142,400,340,471,415,109],()=>i(86691));module.exports=s})();