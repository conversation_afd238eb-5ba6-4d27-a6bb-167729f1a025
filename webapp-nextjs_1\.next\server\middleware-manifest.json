{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "KuIusqDD6OLJzwFfBncNZmvH4WjdfFWZOoEuCU5Iwuw=", "__NEXT_PREVIEW_MODE_ID": "53a42f59bda877f389105973507d14ca", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8dc28a4e0c4a9fe729d471d933abeb4ffb270ad77f7abdafaddbaf0219be2aab", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "30f11b757f3c912a51c85bc03fd4b005033f2079fac332bea905816eb51e04a7"}}}, "sortedMiddleware": ["/"], "functions": {}}