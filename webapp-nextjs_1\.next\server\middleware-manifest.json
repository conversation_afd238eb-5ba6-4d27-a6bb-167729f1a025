{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "KuIusqDD6OLJzwFfBncNZmvH4WjdfFWZOoEuCU5Iwuw=", "__NEXT_PREVIEW_MODE_ID": "7cd8c757b07c6d8b93b05272ba415b5b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b09583413a336a6b9bd53827cd2c4edb35da9389835c58ad36673860137eff7f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "49b4829b22a3f862ccc508cec705dbc94415ecbac182dcbaaf66d5bd3d2ff1ae"}}}, "sortedMiddleware": ["/"], "functions": {}}