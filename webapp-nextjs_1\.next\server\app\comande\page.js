(()=>{var e={};e.id=7484,e.ids=[7484],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,a,s)=>{"use strict";s.d(a,{A0:()=>n,BF:()=>l,Hj:()=>o,XI:()=>i,nA:()=>d,nd:()=>c});var t=s(60687);s(43210);var r=s(4780);function i({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,t.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm border-collapse",e),...a})})}function n({className:e,...a}){return(0,t.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",e),...a})}function l({className:e,...a}){return(0,t.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",e),...a})}function o({className:e,...a}){return(0,t.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("data-[state=selected]:bg-muted border-b",e),...a})}function c({className:e,...a}){return(0,t.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...a})}function d({className:e,...a}){return(0,t.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...a})}},8819:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13861:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15079:(e,a,s)=>{"use strict";s.d(a,{bq:()=>m,eb:()=>x,gC:()=>u,l6:()=>c,yv:()=>d});var t=s(60687);s(43210);var r=s(97822),i=s(78272),n=s(13964),l=s(3589),o=s(4780);function c({...e}){return(0,t.jsx)(r.bL,{"data-slot":"select",...e})}function d({...e}){return(0,t.jsx)(r.WT,{"data-slot":"select-value",...e})}function m({className:e,size:a="default",children:s,...n}){return(0,t.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":a,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[s,(0,t.jsx)(r.In,{asChild:!0,children:(0,t.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function u({className:e,children:a,position:s="popper",...i}){return(0,t.jsx)(r.ZL,{children:(0,t.jsxs)(r.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...i,children:[(0,t.jsx)(p,{}),(0,t.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,t.jsx)(h,{})]})})}function x({className:e,children:a,...s}){return(0,t.jsxs)(r.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,t.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,t.jsx)(r.VF,{children:(0,t.jsx)(n.A,{className:"size-4"})})}),(0,t.jsx)(r.p4,{children:a})]})}function p({className:e,...a}){return(0,t.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,t.jsx)(l.A,{className:"size-4"})})}function h({className:e,...a}){return(0,t.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,t.jsx)(i.A,{className:"size-4"})})}},15391:(e,a,s)=>{"use strict";s.d(a,{Fw:()=>d,NM:()=>c,Nj:()=>o,Tr:()=>n,mU:()=>t,qn:()=>u});let t={PRIMARY:{bg:"bg-blue-50",text:"text-blue-600",border:"border-blue-300",hover:"hover:bg-blue-50",active:"hover:border-blue-400",hex:"#007bff"},NEUTRAL:{text_dark:"text-gray-800",text_medium:"text-gray-600",text_light:"text-gray-500",bg_white:"bg-white",bg_light:"bg-gray-50",border:"border-gray-300",hex_dark:"#343A40",hex_medium:"#6c757d",hex_light:"#DEE2E6"},STATUS:{SUCCESS:{bg:"bg-green-50",text:"text-green-700",border:"border-green-200",hex:"#28A745"},WARNING:{bg:"bg-orange-50",text:"text-orange-700",border:"border-orange-200",hex:"#FD7E14"},ERROR:{bg:"bg-red-50",text:"text-red-700",border:"border-red-200",hex:"#DC3545"}}};t.STATUS.SUCCESS,t.STATUS.WARNING,t.NEUTRAL,t.STATUS.ERROR,t.NEUTRAL,t.STATUS.ERROR;let r={DA_INSTALLARE:t.NEUTRAL,INSTALLATO:t.STATUS.SUCCESS,COLLEGATO_PARTENZA:t.STATUS.WARNING,COLLEGATO_ARRIVO:t.STATUS.WARNING,COLLEGATO:t.STATUS.SUCCESS,CERTIFICATO:t.STATUS.SUCCESS,SPARE:t.STATUS.WARNING,ERRORE:t.STATUS.ERROR},i={ATTIVA:t.STATUS.SUCCESS,COMPLETATA:t.STATUS.SUCCESS,ANNULLATA:t.NEUTRAL,IN_CORSO:t.STATUS.WARNING,ERRORE:t.STATUS.ERROR},n=e=>{let a=r[e?.toUpperCase().replace(/\s+/g,"_")]||r.ERRORE;return{badge:`${a.bg} ${a.text} rounded-full px-3 py-1 text-xs font-medium`,text:a.text,bg:a.bg,border:a.border,hex:a.hex}},l=()=>({button:"inline-flex items-center justify-center gap-1 px-3 py-2 text-xs font-medium bg-white text-gray-800 border-b-2 border-[#315cfd] rounded-full cursor-pointer min-w-[4rem] h-8 transition-colors duration-300 hover:border-2 hover:bg-[#315cfd] hover:text-white",text:"text-gray-800",border:"border-b-[#315cfd]",hover:"hover:bg-[#315cfd] hover:text-white hover:border-2"}),o=()=>l(),c=()=>({text:`inline-flex items-center gap-1 px-2 py-1 text-xs font-medium ${t.NEUTRAL.text_light}`,color:t.NEUTRAL.text_light}),d=e=>{let a=i[e?.toUpperCase().replace(/\s+/g,"_")]||i.ERRORE;return{badge:`${a.bg} ${a.text} ${a.border}`,button:`${a.bg} ${a.text} ${a.border} ${a.hover}`,alert:`${a.bg} ${a.text} ${a.border}`,text:a.text,bg:a.bg,border:a.border,hover:a.hover,hex:a.hex}};t.STATUS.ERROR,t.STATUS.WARNING,t.NEUTRAL,t.NEUTRAL;let m={SUCCESS:t.STATUS.SUCCESS,WARNING:t.STATUS.WARNING,ERROR:t.STATUS.ERROR,NEUTRAL:t.NEUTRAL,PRIMARY:t.PRIMARY,PROGRESS:t.STATUS.SUCCESS,INFO:t.PRIMARY},u=e=>{let a=m[e]||m.NEUTRAL;return{badge:`${a.bg} ${a.text} rounded-full px-3 py-1 text-xs font-medium`,text:a.text,bg:a.bg,border:a.border,hex:a.hex}}},16353:(e,a,s)=>{Promise.resolve().then(s.bind(s,57842))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20992:(e,a,s)=>{"use strict";s.r(a),s.d(a,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var t=s(65239),r=s(48088),i=s(88170),n=s.n(i),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(a,o);let c={children:["",{children:["comande",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,57842)),"C:\\CMS\\webapp-nextjs_1\\src\\app\\comande\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\CMS\\webapp-nextjs_1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\CMS\\webapp-nextjs_1\\src\\app\\comande\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/comande/page",pathname:"/comande",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34729:(e,a,s)=>{"use strict";s.d(a,{T:()=>n});var t=s(60687),r=s(43210),i=s(4780);let n=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...a}));n.displayName="Textarea"},40211:(e,a,s)=>{"use strict";s.d(a,{C1:()=>_,bL:()=>N});var t=s(43210),r=s(98599),i=s(11273),n=s(70569),l=s(65551),o=s(83721),c=s(18853),d=s(46059),m=s(14163),u=s(60687),x="Checkbox",[p,h]=(0,i.A)(x),[v,g]=p(x);function f(e){let{__scopeCheckbox:a,checked:s,children:r,defaultChecked:i,disabled:n,form:o,name:c,onCheckedChange:d,required:m,value:p="on",internal_do_not_use_render:h}=e,[g,f]=(0,l.i)({prop:s,defaultProp:i??!1,onChange:d,caller:x}),[b,j]=t.useState(null),[N,A]=t.useState(null),_=t.useRef(!1),C=!b||!!o||!!b.closest("form"),y={checked:g,disabled:n,setChecked:f,control:b,setControl:j,name:c,form:o,value:p,hasConsumerStoppedPropagationRef:_,required:m,defaultChecked:!w(i)&&i,isFormControl:C,bubbleInput:N,setBubbleInput:A};return(0,u.jsx)(v,{scope:a,...y,children:"function"==typeof h?h(y):r})}var b="CheckboxTrigger",j=t.forwardRef(({__scopeCheckbox:e,onKeyDown:a,onClick:s,...i},l)=>{let{control:o,value:c,disabled:d,checked:x,required:p,setControl:h,setChecked:v,hasConsumerStoppedPropagationRef:f,isFormControl:j,bubbleInput:N}=g(b,e),A=(0,r.s)(l,h),_=t.useRef(x);return t.useEffect(()=>{let e=o?.form;if(e){let a=()=>v(_.current);return e.addEventListener("reset",a),()=>e.removeEventListener("reset",a)}},[o,v]),(0,u.jsx)(m.sG.button,{type:"button",role:"checkbox","aria-checked":w(x)?"mixed":x,"aria-required":p,"data-state":E(x),"data-disabled":d?"":void 0,disabled:d,value:c,...i,ref:A,onKeyDown:(0,n.m)(a,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,n.m)(s,e=>{v(e=>!!w(e)||!e),N&&j&&(f.current=e.isPropagationStopped(),f.current||e.stopPropagation())})})});j.displayName=b;var N=t.forwardRef((e,a)=>{let{__scopeCheckbox:s,name:t,checked:r,defaultChecked:i,required:n,disabled:l,value:o,onCheckedChange:c,form:d,...m}=e;return(0,u.jsx)(f,{__scopeCheckbox:s,checked:r,defaultChecked:i,disabled:l,required:n,onCheckedChange:c,name:t,form:d,value:o,internal_do_not_use_render:({isFormControl:e})=>(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(j,{...m,ref:a,__scopeCheckbox:s}),e&&(0,u.jsx)(y,{__scopeCheckbox:s})]})})});N.displayName=x;var A="CheckboxIndicator",_=t.forwardRef((e,a)=>{let{__scopeCheckbox:s,forceMount:t,...r}=e,i=g(A,s);return(0,u.jsx)(d.C,{present:t||w(i.checked)||!0===i.checked,children:(0,u.jsx)(m.sG.span,{"data-state":E(i.checked),"data-disabled":i.disabled?"":void 0,...r,ref:a,style:{pointerEvents:"none",...e.style}})})});_.displayName=A;var C="CheckboxBubbleInput",y=t.forwardRef(({__scopeCheckbox:e,...a},s)=>{let{control:i,hasConsumerStoppedPropagationRef:n,checked:l,defaultChecked:d,required:x,disabled:p,name:h,value:v,form:f,bubbleInput:b,setBubbleInput:j}=g(C,e),N=(0,r.s)(s,j),A=(0,o.Z)(l),_=(0,c.X)(i);t.useEffect(()=>{if(!b)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,a=!n.current;if(A!==l&&e){let s=new Event("click",{bubbles:a});b.indeterminate=w(l),e.call(b,!w(l)&&l),b.dispatchEvent(s)}},[b,A,l,n]);let y=t.useRef(!w(l)&&l);return(0,u.jsx)(m.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:d??y.current,required:x,disabled:p,name:h,value:v,form:f,...a,tabIndex:-1,ref:N,style:{...a.style,..._,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function w(e){return"indeterminate"===e}function E(e){return w(e)?"indeterminate":e?"checked":"unchecked"}y.displayName=C},41550:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},44493:(e,a,s)=>{"use strict";s.d(a,{BT:()=>o,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>n});var t=s(60687);s(43210);var r=s(4780);function i({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...a})}function n({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...a})}function l({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...a})}function o({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...a})}function c({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...a})}},46657:(e,a,s)=>{"use strict";s.d(a,{k:()=>N});var t=s(60687),r=s(43210),i=s(11273),n=s(14163),l="Progress",[o,c]=(0,i.A)(l),[d,m]=o(l),u=r.forwardRef((e,a)=>{var s,r;let{__scopeProgress:i,value:l=null,max:o,getValueLabel:c=h,...m}=e;(o||0===o)&&!f(o)&&console.error((s=`${o}`,`Invalid prop \`max\` of value \`${s}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let u=f(o)?o:100;null===l||b(l,u)||console.error((r=`${l}`,`Invalid prop \`value\` of value \`${r}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let x=b(l,u)?l:null,p=g(x)?c(x,u):void 0;return(0,t.jsx)(d,{scope:i,value:x,max:u,children:(0,t.jsx)(n.sG.div,{"aria-valuemax":u,"aria-valuemin":0,"aria-valuenow":g(x)?x:void 0,"aria-valuetext":p,role:"progressbar","data-state":v(x,u),"data-value":x??void 0,"data-max":u,...m,ref:a})})});u.displayName=l;var x="ProgressIndicator",p=r.forwardRef((e,a)=>{let{__scopeProgress:s,...r}=e,i=m(x,s);return(0,t.jsx)(n.sG.div,{"data-state":v(i.value,i.max),"data-value":i.value??void 0,"data-max":i.max,...r,ref:a})});function h(e,a){return`${Math.round(e/a*100)}%`}function v(e,a){return null==e?"indeterminate":e===a?"complete":"loading"}function g(e){return"number"==typeof e}function f(e){return g(e)&&!isNaN(e)&&e>0}function b(e,a){return g(e)&&!isNaN(e)&&e<=a&&e>=0}p.displayName=x;var j=s(4780);function N({className:e,value:a,...s}){return(0,t.jsx)(u,{"data-slot":"progress",className:(0,j.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...s,children:(0,t.jsx)(p,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(a||0)}%)`}})})}},52801:(e,a,s)=>{Promise.resolve().then(s.bind(s,83967))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56896:(e,a,s)=>{"use strict";s.d(a,{S:()=>l});var t=s(60687);s(43210);var r=s(40211),i=s(13964),n=s(4780);function l({className:e,...a}){return(0,t.jsx)(r.bL,{"data-slot":"checkbox",className:(0,n.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...a,children:(0,t.jsx)(r.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,t.jsx)(i.A,{className:"size-3.5"})})})}},57842:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs_1\\\\src\\\\app\\\\comande\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_1\\src\\app\\comande\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},63503:(e,a,s)=>{"use strict";s.d(a,{Cf:()=>m,Es:()=>x,L3:()=>p,c7:()=>u,lG:()=>l,rr:()=>h,zM:()=>o});var t=s(60687);s(43210);var r=s(26134),i=s(11860),n=s(4780);function l({...e}){return(0,t.jsx)(r.bL,{"data-slot":"dialog",...e})}function o({...e}){return(0,t.jsx)(r.l9,{"data-slot":"dialog-trigger",...e})}function c({...e}){return(0,t.jsx)(r.ZL,{"data-slot":"dialog-portal",...e})}function d({className:e,...a}){return(0,t.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...a})}function m({className:e,children:a,showCloseButton:s=!0,...l}){return(0,t.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,t.jsx)(d,{}),(0,t.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...l,children:[a,s&&(0,t.jsxs)(r.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(i.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",e),...a})}function x({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...a})}function p({className:e,...a}){return(0,t.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",e),...a})}function h({className:e,...a}){return(0,t.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...a})}},70440:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>r});var t=s(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80013:(e,a,s)=>{"use strict";s.d(a,{J:()=>n});var t=s(60687);s(43210);var r=s(78148),i=s(4780);function n({className:e,...a}){return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...a})}},81630:e=>{"use strict";e.exports=require("http")},83967:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>K});var t=s(60687),r=s(43210),i=s(44493),n=s(29523),l=s(96834),o=s(89667),c=s(15391),d=s(6211),m=s(63213),u=s(62185),x=s(63503),p=s(80013),h=s(34729),v=s(15079),g=s(91821),f=s(6727),b=s(93613),j=s(41862),N=s(58869);let A={POSA:"POSA",COLLEGAMENTO_PARTENZA:"COLLEGAMENTO_PARTENZA",COLLEGAMENTO_ARRIVO:"COLLEGAMENTO_ARRIVO",CERTIFICAZIONE:"CERTIFICAZIONE"},_={INSTALLATO:"Installato"};function C(e,a,s){let t={isValid:!0,errors:[],warnings:[],info:[],caviValidi:[],caviProblematici:[]};return e&&0!==e.length?s&&""!==s.trim()?(e.forEach(e=>{let r=function(e,a,s){let t={id_cavo:e.id_cavo,isValid:!0,errors:[],warnings:[],info:[]},r=function(e,a){let s={errors:[],warnings:[],info:[]},t=e.stato_installazione===_.INSTALLATO,r=e.metratura_reale&&parseFloat(e.metratura_reale)>0,i=e.collegamenti&&parseInt(e.collegamenti)>0,n="CERTIFICATO"===e.stato_certificazione;switch(a){case A.POSA:t&&s.errors.push(`Cavo ${e.id_cavo} \xe8 gi\xe0 installato e non pu\xf2 essere assegnato a comanda POSA`),r&&s.warnings.push(`Cavo ${e.id_cavo} ha gi\xe0 metratura reale registrata`);break;case A.COLLEGAMENTO_PARTENZA:case A.COLLEGAMENTO_ARRIVO:t||r||s.warnings.push(`Cavo ${e.id_cavo} non risulta installato. Verificare prerequisiti.`),i&&s.warnings.push(`Cavo ${e.id_cavo} risulta gi\xe0 collegato`);break;case A.CERTIFICAZIONE:t||s.errors.push(`Cavo ${e.id_cavo} deve essere installato per la certificazione`),i||s.warnings.push(`Cavo ${e.id_cavo} non risulta collegato. Verificare prerequisiti.`),n&&s.warnings.push(`Cavo ${e.id_cavo} \xe8 gi\xe0 certificato`)}return s}(e,a);t.errors.push(...r.errors),t.warnings.push(...r.warnings),t.info.push(...r.info);let i=function(e,a){let s={errors:[],warnings:[],info:[]};switch(a){case A.POSA:e.comanda_posa&&s.errors.push(`Cavo ${e.id_cavo} ha gi\xe0 comanda POSA assegnata: ${e.comanda_posa}`);break;case A.COLLEGAMENTO_PARTENZA:e.comanda_partenza&&s.errors.push(`Cavo ${e.id_cavo} ha gi\xe0 comanda COLLEGAMENTO_PARTENZA assegnata: ${e.comanda_partenza}`);break;case A.COLLEGAMENTO_ARRIVO:e.comanda_arrivo&&s.errors.push(`Cavo ${e.id_cavo} ha gi\xe0 comanda COLLEGAMENTO_ARRIVO assegnata: ${e.comanda_arrivo}`);break;case A.CERTIFICAZIONE:e.comanda_certificazione&&s.errors.push(`Cavo ${e.id_cavo} ha gi\xe0 comanda CERTIFICAZIONE assegnata: ${e.comanda_certificazione}`)}return s}(e,a);t.errors.push(...i.errors),t.warnings.push(...i.warnings),t.info.push(...i.info);let n=function(e,a){let s={warnings:[],info:[]};switch(a){case A.COLLEGAMENTO_PARTENZA:case A.COLLEGAMENTO_ARRIVO:!e.comanda_posa&&(!e.metratura_reale||0>=parseFloat(e.metratura_reale))&&s.warnings.push(`Cavo ${e.id_cavo} non ha comanda posa assegnata e non risulta installato. Verificare prerequisiti.`);break;case A.CERTIFICAZIONE:e.comanda_partenza||e.comanda_arrivo||s.warnings.push(`Cavo ${e.id_cavo} non ha comande di collegamento assegnate. Verificare prerequisiti.`)}return s}(e,a);t.warnings.push(...n.warnings),t.info.push(...n.info);let l=function(e,a,s){let t={warnings:[],info:[]},r=[...new Set(Object.values({posa:e.responsabile_posa||"",partenza:e.responsabile_partenza||"",arrivo:e.responsabile_arrivo||"",certificazione:e.responsabile_certificazione||""}).filter(e=>e&&""!==e.trim()))];return r.length>1&&!r.includes(s)&&t.warnings.push(`Cavo ${e.id_cavo} ha gi\xe0 responsabili diversi (${r.join(", ")}). Nuovo responsabile: ${s}`),t}(e,0,s);return t.warnings.push(...l.warnings),t.info.push(...l.info),t.isValid=0===t.errors.length,t}(e,a,s);t.errors.push(...r.errors),t.warnings.push(...r.warnings),t.info.push(...r.info),r.isValid?t.caviValidi.push(e):t.caviProblematici.push({cavo:e,issues:r.errors})}),t.isValid=0===t.errors.length):(t.errors.push("Responsabile non specificato"),t.isValid=!1):(t.errors.push("Nessun cavo selezionato per la comanda"),t.isValid=!1),t}function y(e){let a=[];return e.errors.length>0&&(a.push(`❌ Errori (${e.errors.length}):`),e.errors.forEach(e=>a.push(`  • ${e}`))),e.warnings.length>0&&(a.push(`⚠️ Avvisi (${e.warnings.length}):`),e.warnings.forEach(e=>a.push(`  • ${e}`))),e.info.length>0&&(a.push(`ℹ️ Informazioni (${e.info.length}):`),e.info.forEach(e=>a.push(`  • ${e}`))),e.caviValidi.length>0&&a.push(`✅ Cavi validi: ${e.caviValidi.length}`),e.caviProblematici.length>0&&a.push(`❌ Cavi problematici: ${e.caviProblematici.length}`),a.join("\n")}function w({open:e,onClose:a,caviSelezionati:s=[],tipoComanda:i,onSuccess:l,onError:c,onComandaCreated:d}){let[A,_]=(0,r.useState)(!1),[w,E]=(0,r.useState)(""),[S,R]=(0,r.useState)([]),[O,T]=(0,r.useState)(!1),[z,k]=(0,r.useState)(""),[I,L]=(0,r.useState)(!1),{cantiere:$}=(0,m.A)(),[P,M]=(0,r.useState)(0),[V,G]=(0,r.useState)({tipo_comanda:i||"POSA",responsabile:"",descrizione:"",data_scadenza:"",numero_componenti_squadra:1}),U=async()=>{try{let e;if(_(!0),E(""),!V.tipo_comanda)return void E("Seleziona il tipo di comanda");if(!V.responsabile)return void E("Seleziona un responsabile");if(!P||P<=0)return void E("Cantiere non selezionato");if(s.length>0){let e=C(s,V.tipo_comanda,V.responsabile);if(!e.isValid){E("Validazione cavi fallita. Controllare i dettagli nella sezione validazione."),k(y(e)),L(!0);return}e.warnings.length>0&&(k(y(e)),L(!0))}let t={tipo_comanda:V.tipo_comanda,responsabile:V.responsabile,descrizione:V.descrizione||null,data_scadenza:V.data_scadenza||null,numero_componenti_squadra:V.numero_componenti_squadra};e=s&&s.length>0?await u.CV.createComandaWithCavi(P,t,s):await u.CV.createComanda(P,t);let r=e?.data?.codice_comanda||e?.codice_comanda,i=s&&s.length>0?`Comanda ${r} creata con successo per ${s.length} cavi`:`Comanda ${r} creata con successo`;l(i),d?.(),a()}catch(e){c(e.response?.data?.detail||e.message||"Errore durante la creazione della comanda")}finally{_(!1)}};return(0,t.jsx)(x.lG,{open:e,onOpenChange:a,children:(0,t.jsxs)(x.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(x.c7,{children:[(0,t.jsxs)(x.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"h-5 w-5"}),"Crea Nuova Comanda"]}),(0,t.jsx)(x.rr,{children:s&&s.length>0?`Crea una comanda per ${s.length} cavi selezionati`:"Crea una nuova comanda di lavoro"})]}),(0,t.jsxs)("div",{className:"space-y-6 py-4",children:[w&&(0,t.jsxs)(g.Fc,{variant:"destructive",children:[(0,t.jsx)(b.A,{className:"h-4 w-4"}),(0,t.jsx)(g.TN,{children:w})]}),s.length>0&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("h4",{className:"text-sm font-medium",children:["Validazione Cavi (",s.length," selezionati)"]}),(0,t.jsx)(n.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{if(0===s.length){k("Nessun cavo selezionato per la validazione"),L(!0);return}k(y(C(s,V.tipo_comanda,V.responsabile))),L(!0)},disabled:!V.tipo_comanda||!V.responsabile,children:"Valida Cavi"})]}),I&&z&&(0,t.jsxs)(g.Fc,{children:[(0,t.jsx)(b.A,{className:"h-4 w-4"}),(0,t.jsx)(g.TN,{children:(0,t.jsx)("pre",{className:"whitespace-pre-wrap text-xs font-mono",children:z})})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"tipo",children:"Tipo Comanda *"}),(0,t.jsxs)(v.l6,{value:V.tipo_comanda,onValueChange:e=>G(a=>({...a,tipo_comanda:e})),children:[(0,t.jsx)(v.bq,{children:(0,t.jsx)(v.yv,{})}),(0,t.jsx)(v.gC,{children:[{value:"POSA",label:"\uD83D\uDD27 Posa Cavi",description:"Installazione e posa dei cavi"},{value:"COLLEGAMENTO_PARTENZA",label:"\uD83D\uDD0C Collegamento Partenza",description:"Collegamento lato partenza"},{value:"COLLEGAMENTO_ARRIVO",label:"⚡ Collegamento Arrivo",description:"Collegamento lato arrivo"},{value:"CERTIFICAZIONE",label:"\uD83D\uDCCB Certificazione",description:"Test e certificazione"}].map(e=>(0,t.jsx)(v.eb,{value:e.value,children:(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.label}),(0,t.jsx)("div",{className:"text-sm text-slate-500",children:e.description})]})},e.value))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"responsabile",children:"Responsabile *"}),O?(0,t.jsxs)("div",{className:"flex items-center gap-2 p-2 text-sm text-slate-500",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 animate-spin"}),"Caricamento responsabili..."]}):(0,t.jsxs)(v.l6,{value:V.responsabile,onValueChange:e=>G(a=>({...a,responsabile:e})),children:[(0,t.jsx)(v.bq,{children:(0,t.jsx)(v.yv,{placeholder:"Seleziona responsabile"})}),(0,t.jsx)(v.gC,{children:S.map(e=>(0,t.jsx)(v.eb,{value:e.nome_responsabile,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(N.A,{className:"h-4 w-4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.nome_responsabile}),e.numero_telefono&&(0,t.jsx)("div",{className:"text-sm text-slate-500",children:e.numero_telefono})]})]})},e.id_responsabile))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"descrizione",children:"Descrizione"}),(0,t.jsx)(h.T,{id:"descrizione",placeholder:"Descrizione opzionale della comanda...",value:V.descrizione,onChange:e=>G(a=>({...a,descrizione:e.target.value})),rows:3})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"data_scadenza",children:"Data Scadenza"}),(0,t.jsx)(o.p,{id:"data_scadenza",type:"date",value:V.data_scadenza,onChange:e=>G(a=>({...a,data_scadenza:e.target.value}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"squadra",children:"Componenti Squadra"}),(0,t.jsx)(o.p,{id:"squadra",type:"number",min:"1",max:"20",value:V.numero_componenti_squadra,onChange:e=>G(a=>({...a,numero_componenti_squadra:parseInt(e.target.value)||1}))})]})]}),s&&s.length>0&&(0,t.jsxs)("div",{className:"p-3 bg-blue-50 rounded-lg border border-blue-200",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 text-blue-700",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{className:"font-medium",children:["Cavi da assegnare: ",s.length]})]}),(0,t.jsx)("div",{className:"text-sm text-blue-600 mt-1",children:"I cavi selezionati verranno automaticamente assegnati a questa comanda"})]})]}),(0,t.jsxs)(x.Es,{children:[(0,t.jsx)(n.$,{variant:"outline",onClick:a,disabled:A,children:"Annulla"}),(0,t.jsxs)(n.$,{onClick:U,disabled:A,children:[A&&(0,t.jsx)(j.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Crea Comanda"]})]})]})})}var E=s(41312),S=s(96474),R=s(11860),O=s(8819),T=s(62688);let z=(0,T.A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]);var k=s(41550),I=s(63143),L=s(88233);function $({open:e,onClose:a,onSuccess:s,onError:l}){let[c,d]=(0,r.useState)(!1),[h,v]=(0,r.useState)(""),[f,A]=(0,r.useState)([]),[_,C]=(0,r.useState)(null),[y,w]=(0,r.useState)(!1),{cantiere:T}=(0,m.A)(),[$,P]=(0,r.useState)(0),[M,V]=(0,r.useState)({nome_responsabile:"",numero_telefono:"",mail:""}),G=async()=>{try{d(!0);let e=await u.AR.getResponsabili($),a=e?.data||e||[];A(Array.isArray(a)?a:[])}catch(e){v("Errore durante il caricamento dei responsabili")}finally{d(!1)}},U=async()=>{try{d(!0),v("");let e={nome_responsabile:M.nome_responsabile.trim(),numero_telefono:M.numero_telefono.trim()||null,mail:M.mail.trim()||null},a=function(e){var a,s;let t=[];return e.nome_responsabile&&e.nome_responsabile.trim()||t.push("Il nome del responsabile \xe8 obbligatorio"),e.mail||e.numero_telefono||t.push("Almeno uno tra email e telefono deve essere specificato"),e.mail&&(a=e.mail,!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a))&&t.push("Formato email non valido"),e.numero_telefono&&(s=e.numero_telefono,!/^[\+]?[0-9\s\-\(\)]{8,15}$/.test(s.replace(/\s/g,"")))&&t.push("Formato telefono non valido"),{isValid:0===t.length,errors:t}}(e);if(!a.isValid)return void v(`Errori di validazione: ${a.errors.join(", ")}`);await u.AR.createResponsabile($,e),s("Responsabile aggiunto con successo"),V({nome_responsabile:"",numero_telefono:"",mail:""}),w(!1),G()}catch(e){v(e.response?.data?.detail||"Errore durante la creazione del responsabile")}finally{d(!1)}},F=e=>{V({nome_responsabile:e.nome_responsabile,numero_telefono:e.numero_telefono||"",mail:e.mail||""}),C(e.id_responsabile),w(!1)},q=async()=>{if(_)try{if(d(!0),v(""),!M.nome_responsabile.trim())return void v("Il nome del responsabile \xe8 obbligatorio");if(M.mail&&!M.mail.includes("@"))return void v("Inserisci un indirizzo email valido");let e={nome_responsabile:M.nome_responsabile.trim(),numero_telefono:M.numero_telefono.trim()||null,mail:M.mail.trim()||null};await u.AR.updateResponsabile($,_,e),s("Responsabile aggiornato con successo"),V({nome_responsabile:"",numero_telefono:"",mail:""}),C(null),G()}catch(e){v(e.response?.data?.detail||"Errore durante l'aggiornamento del responsabile")}finally{d(!1)}},Z=async(e,a)=>{if(confirm(`Sei sicuro di voler eliminare il responsabile "${a}"?`))try{d(!0),await u.AR.deleteResponsabile($,e),s("Responsabile eliminato con successo"),G()}catch(e){l(e.response?.data?.detail||"Errore durante l'eliminazione del responsabile")}finally{d(!1)}},D=()=>{C(null),w(!1),V({nome_responsabile:"",numero_telefono:"",mail:""}),v("")};return(0,t.jsx)(x.lG,{open:e,onOpenChange:a,children:(0,t.jsxs)(x.Cf,{className:"sm:max-w-[700px] max-h-[80vh] overflow-y-auto",children:[(0,t.jsxs)(x.c7,{children:[(0,t.jsxs)(x.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(E.A,{className:"h-5 w-5"}),"Gestisci Responsabili"]}),(0,t.jsxs)(x.rr,{children:["Gestisci i responsabili per il cantiere ",$]})]}),(0,t.jsxs)("div",{className:"space-y-6 py-4",children:[h&&(0,t.jsxs)(g.Fc,{variant:"destructive",children:[(0,t.jsx)(b.A,{className:"h-4 w-4"}),(0,t.jsx)(g.TN,{children:h})]}),!y&&!_&&(0,t.jsxs)(n.$,{onClick:()=>w(!0),className:"w-full",variant:"outline",children:[(0,t.jsx)(S.A,{className:"h-4 w-4 mr-2"}),"Aggiungi Nuovo Responsabile"]}),(y||_)&&(0,t.jsx)(i.Zp,{className:"border-2 border-blue-200",children:(0,t.jsxs)(i.Wu,{className:"p-4 space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h4",{className:"font-medium",children:_?"Modifica Responsabile":"Nuovo Responsabile"}),(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:D,children:(0,t.jsx)(R.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"nome",children:"Nome Responsabile *"}),(0,t.jsx)(o.p,{id:"nome",placeholder:"Nome e cognome",value:M.nome_responsabile,onChange:e=>V(a=>({...a,nome_responsabile:e.target.value}))})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"telefono",children:"Numero Telefono"}),(0,t.jsx)(o.p,{id:"telefono",placeholder:"+39 ************",value:M.numero_telefono,onChange:e=>V(a=>({...a,numero_telefono:e.target.value}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"email",children:"Email"}),(0,t.jsx)(o.p,{id:"email",type:"email",placeholder:"<EMAIL>",value:M.mail,onChange:e=>V(a=>({...a,mail:e.target.value}))})]})]})]}),(0,t.jsxs)("div",{className:"flex gap-2 pt-2",children:[(0,t.jsxs)(n.$,{onClick:_?q:U,disabled:c,className:"flex-1",children:[c&&(0,t.jsx)(j.A,{className:"mr-2 h-4 w-4 animate-spin"}),(0,t.jsx)(O.A,{className:"mr-2 h-4 w-4"}),_?"Aggiorna":"Aggiungi"]}),(0,t.jsx)(n.$,{variant:"outline",onClick:D,children:"Annulla"})]})]})}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("h4",{className:"font-medium flex items-center gap-2",children:[(0,t.jsx)(E.A,{className:"h-4 w-4"}),"Responsabili Esistenti (",f.length,")"]}),c&&0===f.length?(0,t.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 animate-spin"}),"Caricamento responsabili..."]})}):0===f.length?(0,t.jsx)("div",{className:"text-center py-8 text-slate-500",children:"Nessun responsabile trovato"}):(0,t.jsx)("div",{className:"space-y-2",children:f.map(e=>(0,t.jsx)(i.Zp,{className:_===e.id_responsabile?"border-blue-300":"",children:(0,t.jsx)(i.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 text-slate-500"}),(0,t.jsx)("span",{className:"font-medium",children:e.nome_responsabile})]}),(0,t.jsxs)("div",{className:"space-y-1 text-sm text-slate-600",children:[e.numero_telefono&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(z,{className:"h-3 w-3"}),e.numero_telefono]}),e.mail&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(k.A,{className:"h-3 w-3"}),e.mail]})]})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>F(e),disabled:c||_===e.id_responsabile,children:(0,t.jsx)(I.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>Z(e.id_responsabile,e.nome_responsabile),disabled:c,className:"text-red-600 hover:text-red-700",children:(0,t.jsx)(L.A,{className:"h-4 w-4"})})]})]})})},e.id_responsabile))})]})]}),(0,t.jsx)(x.Es,{children:(0,t.jsx)(n.$,{variant:"outline",onClick:a,children:"Chiudi"})})]})})}var P=s(46657),M=s(58559),V=s(40228),G=s(48730),U=s(5336),F=s(23361);function q({open:e,onClose:a,codiceComanda:s,onSuccess:o,onError:d}){let[u,p]=(0,r.useState)(!1),[h,v]=(0,r.useState)(""),[A,_]=(0,r.useState)(null),{cantiere:C}=(0,m.A)(),[y,w]=(0,r.useState)(0),S=e=>new Date(e).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"});return s?(0,t.jsx)(x.lG,{open:e,onOpenChange:a,children:(0,t.jsxs)(x.Cf,{className:"sm:max-w-[800px] max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(x.c7,{children:[(0,t.jsxs)(x.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"h-5 w-5"}),"Dettagli Comanda ",s]}),(0,t.jsx)(x.rr,{children:"Visualizza tutti i dettagli e lo stato della comanda"})]}),(0,t.jsxs)("div",{className:"space-y-6 py-4",children:[h&&(0,t.jsxs)(g.Fc,{variant:"destructive",children:[(0,t.jsx)(b.A,{className:"h-4 w-4"}),(0,t.jsx)(g.TN,{children:h})]}),u?(0,t.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(j.A,{className:"h-5 w-5 animate-spin"}),"Caricamento dettagli comanda..."]})}):A?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(M.A,{className:"h-5 w-5"}),"Informazioni Generali"]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(e=>{let a={POSA:{label:"Posa Cavi",icon:"\uD83D\uDD27"},COLLEGAMENTO_PARTENZA:{label:"Collegamento Partenza",icon:"\uD83D\uDD0C"},COLLEGAMENTO_ARRIVO:{label:"Collegamento Arrivo",icon:"⚡"},CERTIFICAZIONE:{label:"Certificazione",icon:"\uD83D\uDCCB"}}[e]||{label:e,icon:"❓"};return(0,t.jsxs)(l.E,{variant:"outline",className:"bg-blue-50 text-blue-700 border-blue-200",children:[a.icon," ",a.label]})})(A.tipo_comanda),(e=>{let a=(0,c.Fw)(e);return(0,t.jsx)(l.E,{className:a.badge,children:e})})(A.stato)]})]})}),(0,t.jsx)(i.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(V.A,{className:"h-4 w-4 text-slate-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Data Creazione"}),(0,t.jsx)("p",{className:"font-medium",children:S(A.data_creazione)})]})]}),A.data_scadenza&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(G.A,{className:"h-4 w-4 text-slate-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Scadenza"}),(0,t.jsx)("p",{className:"font-medium",children:S(A.data_scadenza)})]})]}),A.data_completamento&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(U.A,{className:"h-4 w-4 text-green-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Completamento"}),(0,t.jsx)("p",{className:"font-medium text-green-700",children:S(A.data_completamento)})]})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(E.A,{className:"h-4 w-4 text-slate-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Componenti Squadra"}),(0,t.jsxs)("p",{className:"font-medium",children:[A.numero_componenti_squadra," persone"]})]})]}),A.descrizione&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-slate-500",children:"Descrizione"}),(0,t.jsx)("p",{className:"font-medium",children:A.descrizione})]})]})]})})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(N.A,{className:"h-5 w-5"}),"Responsabile"]})}),(0,t.jsx)(i.Wu,{children:(0,t.jsx)("div",{className:"flex items-start gap-4",children:(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium text-lg",children:A.responsabile||"Non assegnato"}),A.responsabile_dettagli&&(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm text-slate-600",children:[A.responsabile_dettagli.numero_telefono&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(z,{className:"h-3 w-3"}),A.responsabile_dettagli.numero_telefono]}),A.responsabile_dettagli.mail&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(k.A,{className:"h-3 w-3"}),A.responsabile_dettagli.mail]})]})]})})})]}),A.progresso&&(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(M.A,{className:"h-5 w-5"}),"Progresso Lavori"]})}),(0,t.jsx)(i.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Completamento"}),(0,t.jsxs)("span",{className:"text-sm font-medium",children:[A.progresso.percentuale,"%"]})]}),(0,t.jsx)(P.k,{value:A.progresso.percentuale,className:"h-2"}),(0,t.jsxs)("div",{className:"flex justify-between text-sm text-slate-600",children:[(0,t.jsxs)("span",{children:[A.progresso.completati," completati"]}),(0,t.jsxs)("span",{children:[A.progresso.totale," totali"]})]})]})})]}),A.cavi_assegnati&&A.cavi_assegnati.length>0&&(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(F.A,{className:"h-5 w-5"}),"Cavi Assegnati (",A.cavi_assegnati.length,")"]})}),(0,t.jsx)(i.Wu,{children:(0,t.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:A.cavi_assegnati.map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-2 bg-slate-50 rounded",children:[(0,t.jsx)("span",{className:"font-mono text-sm",children:e.id_cavo||e}),e.stato&&(0,t.jsx)(l.E,{variant:"outline",className:"text-xs",children:e.stato})]},a))})})]})]}):(0,t.jsx)("div",{className:"text-center py-8 text-slate-500",children:"Nessun dettaglio disponibile"})]}),(0,t.jsxs)(x.Es,{children:[(0,t.jsx)(n.$,{variant:"outline",onClick:a,children:"Chiudi"}),A&&(0,t.jsx)(n.$,{onClick:()=>{o("Funzione di modifica in sviluppo")},children:"Modifica Comanda"})]})]})}):null}var Z=s(43649),D=s(29867);function B({open:e,onClose:a,codiceComanda:s,tipoComanda:i,onSuccess:c,onError:d}){let[m,h]=(0,r.useState)([]),[v,g]=(0,r.useState)({}),[f,b]=(0,r.useState)(!1),[N,A]=(0,r.useState)(!1),[_,C]=(0,r.useState)(null),{toast:y}=(0,D.dj)(),w=(e,a,s)=>{g(t=>({...t,[e]:{...t[e],[a]:s}}))},E=async()=>{try{A(!0),C(null);let e="",t={};"POSA"===i?(e="dati-posa",t={dati_posa:v}):("COLLEGAMENTO_PARTENZA"===i||"COLLEGAMENTO_ARRIVO"===i)&&(e="dati-collegamento",t={dati_collegamento:v}),await u.CV.updateDatiComanda(s,e,t);let r="POSA"===i?"Metri posati inseriti con successo":"Metri collegati inseriti con successo";c?.(r),y({title:"Successo",description:r}),a()}catch(a){let e=a.response?.data?.detail||a.message||"Errore nel salvataggio";C(e),d?.(e),y({title:"Errore",description:e,variant:"destructive"})}finally{A(!1)}};return(0,t.jsx)(x.lG,{open:e,onOpenChange:a,children:(0,t.jsxs)(x.Cf,{className:"max-w-4xl max-h-[80vh] overflow-y-auto",children:[(0,t.jsxs)(x.c7,{children:[(0,t.jsxs)(x.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(U.A,{className:"h-5 w-5 text-blue-600"}),(()=>{switch(i){case"POSA":return"Inserisci Metri Posati";case"COLLEGAMENTO_PARTENZA":return"Inserisci Metri Collegati - Partenza";case"COLLEGAMENTO_ARRIVO":return"Inserisci Metri Collegati - Arrivo";default:return"Inserisci Metri"}})()]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[(()=>{switch(i){case"POSA":return"Inserisci i metri realmente posati per ogni cavo";case"COLLEGAMENTO_PARTENZA":return"Inserisci i metri collegati lato partenza";case"COLLEGAMENTO_ARRIVO":return"Inserisci i metri collegati lato arrivo";default:return"Inserisci i metri"}})()," - Comanda: ",s]})]}),f?(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,t.jsx)(j.A,{className:"h-6 w-6 animate-spin mr-2"}),"Caricamento cavi..."]}):_?(0,t.jsxs)("div",{className:"flex items-center justify-center py-8 text-red-600",children:[(0,t.jsx)(Z.A,{className:"h-5 w-5 mr-2"}),_]}):0===m.length?(0,t.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Nessun cavo trovato per questa comanda"}):(0,t.jsx)("div",{className:"space-y-4",children:m.map(e=>(0,t.jsxs)("div",{className:"border rounded-lg p-4 bg-gray-50",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-blue-600",children:e.id_cavo}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[e.tipologia," - ",e.formazione," - ",e.metratura_teorica,"m teorici"]})]}),(0,t.jsx)(l.E,{variant:"Installato"===e.stato_installazione?"default":"secondary",children:e.stato_installazione})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(p.J,{htmlFor:`metri-${e.id_cavo}`,children:"POSA"===i?"Metri Posati":"Metri Collegati"}),(0,t.jsx)(o.p,{id:`metri-${e.id_cavo}`,type:"number",min:"0",step:"0.1",value:v[e.id_cavo]?.metratura_reale||0,onChange:a=>w(e.id_cavo,"metratura_reale",parseFloat(a.target.value)||0),className:"mt-1"})]}),"POSA"===i&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(p.J,{htmlFor:`persone-${e.id_cavo}`,children:"Persone Impiegate"}),(0,t.jsx)(o.p,{id:`persone-${e.id_cavo}`,type:"number",min:"1",value:v[e.id_cavo]?.numero_persone_impiegate||1,onChange:a=>w(e.id_cavo,"numero_persone_impiegate",parseInt(a.target.value)||1),className:"mt-1"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(p.J,{htmlFor:`sistemazione-${e.id_cavo}`,children:"Sistemazione"}),(0,t.jsx)(o.p,{id:`sistemazione-${e.id_cavo}`,value:v[e.id_cavo]?.sistemazione||"",onChange:a=>w(e.id_cavo,"sistemazione",a.target.value),className:"mt-1",placeholder:"Es: Interrato, Aereo..."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(p.J,{htmlFor:`fascettatura-${e.id_cavo}`,children:"Fascettatura"}),(0,t.jsx)(o.p,{id:`fascettatura-${e.id_cavo}`,value:v[e.id_cavo]?.fascettatura||"",onChange:a=>w(e.id_cavo,"fascettatura",a.target.value),className:"mt-1",placeholder:"Es: Standard, Rinforzata..."})]})]})]})]},e.id_cavo))}),(0,t.jsxs)("div",{className:"flex justify-end gap-2 pt-4 border-t",children:[(0,t.jsx)(n.$,{variant:"outline",onClick:a,disabled:N,children:"Annulla"}),(0,t.jsx)(n.$,{onClick:E,disabled:N||0===m.length,className:"bg-blue-600 hover:bg-blue-700",children:N?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"h-4 w-4 animate-spin mr-2"}),"Salvando..."]}):"Salva Metri"})]})]})})}var J=s(56896);let W=(0,T.A)("wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]]);function H({open:e,onClose:a,codiceComanda:s,onSuccess:i,onError:c}){let[d,u]=(0,r.useState)([]),[h,f]=(0,r.useState)([]),[N,A]=(0,r.useState)({}),[_,C]=(0,r.useState)({}),[y,w]=(0,r.useState)(!1),[E,S]=(0,r.useState)(!1),[R,O]=(0,r.useState)(null),[T,z]=(0,r.useState)(""),[k,I]=(0,r.useState)(0),[L,$]=(0,r.useState)([]),[P,M]=(0,r.useState)(null),{toast:V}=(0,D.dj)(),{cantiere:G}=(0,m.A)(),F=(e,a)=>{let s=parseFloat(a)||0;A(a=>{let t={...a,[e]:{...a[e],metratura_reale:s}};if(T&&"BOBINA_VUOTA"!==T){let a=h.find(e=>e.id_bobina===T);if(a){let s=0;Object.keys(t).forEach(e=>{let a=t[e]?.metratura_reale||0;a>0&&(s+=a)});let r=a.metri_residui-s;if(I(r),r<0&&!P){M(e);let a=[];Object.keys(t).forEach(s=>{0===(t[s]?.metratura_reale||0)&&s!==e&&a.push(s)}),$(a)}else r>=0&&P===e&&(M(null),$([]))}}return t}),q(e,s)},q=(e,a)=>{let s=d.find(a=>a.id_cavo===e);s&&C(t=>{let r={...t};return delete r[e],a>0&&s.metratura_teorica,r})},Z=(e,a)=>{let s=parseInt(a)||1;A(a=>({...a,[e]:{...a[e],numero_persone_impiegate:s}}))},B=(e,a)=>{A(s=>({...s,[e]:{...s[e],sistemazione:a}}))},H=(e,a)=>{A(s=>({...s,[e]:{...s[e],fascettatura:a}}))},X=async()=>{try{if(S(!0),O(null),Object.keys(_).length>0)return void O("Correggere gli errori di validazione prima di salvare");let e={};if(Object.keys(N).forEach(a=>{let s=N[a];if((s?.metratura_reale||0)>0){let t=P===a||k<0;e[a]={...s,id_bobina:T||"BOBINA_VUOTA",force_over:t}}}),0===Object.keys(e).length)return void O("Inserire almeno un metro per almeno un cavo");console.log("\uD83D\uDCBE InserisciMetriPosatiDialog: Salvataggio dati posa:",{codiceComanda:s,caviDaSalvare:Object.keys(e).length,datiPosaFiltrati:e,selectedBobina:T,metriResiduiSimulati:k,cavoCheCausaOver:P});let t=await fetch(`/api/comande/${s}/dati-posa-bobine`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`},body:JSON.stringify(e)});if(!t.ok){let e=await t.json();throw Error(e.detail||"Errore durante il salvataggio")}let r=`Metri posati inseriti con successo per ${Object.keys(e).length} cavi`;i?.(r),V({title:"Successo",description:r}),a()}catch(a){let e=a?.response?.data?.detail||"Errore durante il salvataggio dei metri posati";O(e),c?.(e)}finally{S(!1)}};return(0,t.jsx)(x.lG,{open:e,onOpenChange:a,children:(0,t.jsxs)(x.Cf,{className:"max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(x.c7,{children:[(0,t.jsxs)(x.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(W,{className:"h-5 w-5 text-blue-600"}),"Inserisci Metri Posati - Comanda ",s]}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Inserisci i metri posati per ogni cavo della comanda POSA"})]}),R&&(0,t.jsxs)(g.Fc,{variant:"destructive",children:[(0,t.jsx)(b.A,{className:"h-4 w-4"}),(0,t.jsx)(g.TN,{children:R})]}),y?(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,t.jsx)(j.A,{className:"h-6 w-6 animate-spin mr-2"}),"Caricamento cavi..."]}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"border rounded-lg p-4 bg-blue-50",children:[(0,t.jsx)("h3",{className:"font-medium text-blue-900 mb-3",children:"Selezione Bobina Principale"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(p.J,{htmlFor:"bobina-principale",children:"Bobina da Utilizzare"}),(0,t.jsxs)(v.l6,{value:T,onValueChange:e=>{if(z(e),$([]),M(null),A(a=>{let s={...a};return Object.keys(s).forEach(a=>{s[a]={...s[a],id_bobina:e}}),s}),e&&"BOBINA_VUOTA"!==e){let a=h.find(a=>a.id_bobina===e);a&&I(a.metri_residui)}else I(0)},children:[(0,t.jsx)(v.bq,{children:(0,t.jsx)(v.yv,{placeholder:"Seleziona bobina principale..."})}),(0,t.jsxs)(v.gC,{children:[(0,t.jsx)(v.eb,{value:"BOBINA_VUOTA",children:"\uD83D\uDD04 BOBINA_VUOTA (Assegna dopo)"}),h.map(e=>(0,t.jsxs)(v.eb,{value:e.id_bobina,children:["✅ ",e.id_bobina," - ",e.tipologia," ",e.sezione," (",e.metri_residui,"m)"]},e.id_bobina))]})]})]}),T&&"BOBINA_VUOTA"!==T&&(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsx)("span",{className:"font-medium",children:"Metri Residui: "}),(0,t.jsxs)("span",{className:k<0?"text-red-600 font-bold":"text-green-600",children:[k.toFixed(1),"m"]})]}),k<0&&(0,t.jsx)(l.E,{variant:"destructive",className:"text-xs",children:"OVER"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"font-medium",children:["Cavi da Installare (",d.length,")"]}),d.map(e=>{let a=N[e.id_cavo],s=_[e.id_cavo],r=L.includes(e.id_cavo),i=P===e.id_cavo,n=k<0&&"BOBINA_VUOTA"!==T;return(0,t.jsxs)("div",{className:`border rounded-lg p-4 space-y-4 ${r?"bg-red-50 border-red-200":i?"bg-orange-50 border-orange-200":""}`,children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-medium flex items-center gap-2",children:[e.id_cavo,r&&(0,t.jsx)(l.E,{variant:"destructive",className:"text-xs",children:"BLOCCATO"}),i&&(0,t.jsx)(l.E,{variant:"outline",className:"text-xs border-orange-500 text-orange-700",children:"CAUSA OVER"})]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[e.tipologia," - ",e.formazione," - ",e.metratura_teorica,"m teorici"]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(l.E,{variant:"Installato"===e.stato_installazione?"default":"secondary",children:e.stato_installazione}),n&&(0,t.jsx)(l.E,{variant:"destructive",className:"text-xs",children:"OVER"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(p.J,{htmlFor:`metri-${e.id_cavo}`,children:"Metri Posati *"}),(0,t.jsx)(o.p,{id:`metri-${e.id_cavo}`,type:"number",min:"0",step:"0.1",value:a?.metratura_reale||"",onChange:a=>F(e.id_cavo,a.target.value),className:s?"border-red-500":r?"border-red-300 bg-red-50":"",placeholder:r?"Bloccato (OVER)":"0.0",disabled:r}),s&&(0,t.jsx)("p",{className:"text-xs text-red-500 mt-1",children:s}),r&&(0,t.jsx)("p",{className:"text-xs text-red-600 mt-1",children:"⚠️ Cavo bloccato: bobina in stato OVER"}),i&&!r&&(0,t.jsx)("p",{className:"text-xs text-orange-600 mt-1",children:"⚠️ Questo cavo causa lo stato OVER della bobina"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(p.J,{htmlFor:`persone-${e.id_cavo}`,children:"Persone Impiegate"}),(0,t.jsx)(o.p,{id:`persone-${e.id_cavo}`,type:"number",min:"1",value:a?.numero_persone_impiegate||1,onChange:a=>Z(e.id_cavo,a.target.value)})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(J.S,{id:`sistemazione-${e.id_cavo}`,checked:a?.sistemazione||!1,onCheckedChange:a=>B(e.id_cavo,!!a)}),(0,t.jsx)(p.J,{htmlFor:`sistemazione-${e.id_cavo}`,children:"Sistemazione"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(J.S,{id:`fascettatura-${e.id_cavo}`,checked:a?.fascettatura||!1,onCheckedChange:a=>H(e.id_cavo,!!a)}),(0,t.jsx)(p.J,{htmlFor:`fascettatura-${e.id_cavo}`,children:"Fascettatura"})]})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,t.jsx)("span",{className:"font-medium",children:"Bobina assegnata: "}),(0,t.jsx)("span",{className:"BOBINA_VUOTA"===T?"text-orange-600":"text-blue-600",children:T||"Nessuna"})]})]},e.id_cavo)})]})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-2 pt-4 border-t",children:[(0,t.jsx)(n.$,{variant:"outline",onClick:a,children:"Annulla"}),(0,t.jsx)(n.$,{onClick:X,disabled:E||y||0===d.length,children:E?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"h-4 w-4 animate-spin mr-2"}),"Salvando..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(U.A,{className:"h-4 w-4 mr-2"}),"Salva Metri Posati"]})})]})]})})}var X=s(99270),Y=s(13861);function K(){let[e,a]=(0,r.useState)("active"),[s,x]=(0,r.useState)([]),[p,h]=(0,r.useState)([]),[v,g]=(0,r.useState)(!0),[b,N]=(0,r.useState)(""),[A,_]=(0,r.useState)(""),[C,y]=(0,r.useState)("all"),[R,O]=(0,r.useState)("all"),[T,z]=(0,r.useState)(!1),[k,P]=(0,r.useState)(!1),[M,V]=(0,r.useState)(!1),[G,U]=(0,r.useState)(!1),[F,J]=(0,r.useState)(!1),[W,K]=(0,r.useState)(null),[Q,ee]=(0,r.useState)(null),{user:ea,cantiere:es}=(0,m.A)(),{toast:et}=(0,D.dj)(),[er,ei]=(0,r.useState)(0),en=async()=>{try{if(g(!0),N(""),!er||er<=0)return void N("Cantiere non selezionato");let[e,a]=await Promise.all([u.CV.getComande(er),u.AR.getResponsabili(er)]),s=e?.data?.comande||e?.comande||e?.data||e||[],t=a?.data||a||[];x(Array.isArray(s)?s:[]),h(Array.isArray(t)?t:[])}catch(e){N(e.response?.data?.detail||"Errore durante il caricamento dei dati")}finally{g(!1)}},el=e=>{et({title:"Successo",description:e}),en()},eo=e=>{et({title:"Errore",description:e,variant:"destructive"})},ec=async e=>{if(confirm(`Sei sicuro di voler eliminare la comanda ${e}?`))try{g(!0),await u.CV.deleteComanda(er,e),el(`Comanda ${e} eliminata con successo`)}catch(e){eo("Errore durante l'eliminazione della comanda")}finally{g(!1)}},ed=e=>{switch(e){case"COMPLETATA":return(0,t.jsx)(l.E,{className:"bg-green-100 text-green-800",children:"Completata"});case"IN_CORSO":return(0,t.jsx)(l.E,{className:"bg-blue-100 text-blue-800",children:"In Corso"});case"ASSEGNATA":return(0,t.jsx)(l.E,{className:"bg-yellow-100 text-yellow-800",children:"Assegnata"});case"CREATA":return(0,t.jsx)(l.E,{className:"bg-gray-100 text-gray-800",children:"Creata"});case"ANNULLATA":return(0,t.jsx)(l.E,{className:"bg-red-100 text-red-800",children:"Annullata"});default:return(0,t.jsx)(l.E,{variant:"secondary",children:e})}},em=e=>{let a=(0,c.Fw)(e);return(0,t.jsx)(l.E,{className:a.badge,children:{POSA:"\uD83D\uDD27 Posa",COLLEGAMENTO_PARTENZA:"\uD83D\uDD0C Coll. Partenza",COLLEGAMENTO_ARRIVO:"⚡ Coll. Arrivo",CERTIFICAZIONE:"\uD83D\uDCCB Certificazione"}[e]||e.replace(/_/g," ")})},eu=Array.isArray(s)?s.filter(a=>{let s=!0;switch(e){case"active":s="IN_CORSO"===a.stato||"ASSEGNATA"===a.stato||"CREATA"===a.stato;break;case"completed":s="COMPLETATA"===a.stato;break;default:s=!0}let t=""===A||a.codice_comanda.toLowerCase().includes(A.toLowerCase())||a.descrizione&&a.descrizione.toLowerCase().includes(A.toLowerCase())||a.responsabile&&a.responsabile.toLowerCase().includes(A.toLowerCase()),r="all"===C||a.responsabile===C,i="all"===R||a.tipo_comanda===R;return s&&t&&r&&i}):[],ex={totali:Array.isArray(s)?s.length:0,in_corso:Array.isArray(s)?s.filter(e=>"IN_CORSO"===e.stato).length:0,completate:Array.isArray(s)?s.filter(e=>"COMPLETATA"===e.stato).length:0,pianificate:Array.isArray(s)?s.filter(e=>"CREATA"===e.stato||"ASSEGNATA"===e.stato).length:0,filtrate:eu.length};return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:[(0,t.jsxs)("div",{className:"max-w-[90%] mx-auto space-y-6",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-slate-900 mb-2",children:"Gestione Comande"}),(0,t.jsx)("p",{className:"text-slate-600",children:er>0?`Cantiere ${er}`:"Nessun cantiere selezionato"})]}),(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(X.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,t.jsx)(o.p,{placeholder:"Cerca per codice, responsabile, tipo, stato o descrizione...",value:A,onChange:e=>_(e.target.value),className:"pl-10 bg-gray-50 hover:bg-blue-50 focus:bg-white transition-colors"})]})}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-2 mb-6",children:[(0,t.jsxs)(n.$,{onClick:()=>z(!0),className:"bg-blue-600 hover:bg-blue-700 text-white",children:[(0,t.jsx)(S.A,{className:"h-4 w-4 mr-2"}),"Nuova Comanda"]}),(0,t.jsxs)(n.$,{variant:"outline",onClick:()=>{et({title:"Funzione in sviluppo",description:"L'assegnazione cavi sar\xe0 disponibile presto"})},disabled:0===eu.length,children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Assegna Cavi"]}),(0,t.jsxs)(n.$,{variant:"outline",onClick:()=>P(!0),children:[(0,t.jsx)(E.A,{className:"h-4 w-4 mr-2"}),"Gestisci Responsabili"]})]}),(0,t.jsx)("div",{className:"mb-4",children:(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["Elenco Comande (",eu.length," di ",ex.totali,")"]})}),(0,t.jsx)(i.Zp,{className:"border border-gray-200 rounded-lg",children:(0,t.jsx)(i.Wu,{className:"p-0",children:(0,t.jsxs)(d.XI,{children:[(0,t.jsx)(d.A0,{children:(0,t.jsxs)(d.Hj,{className:"bg-gray-50",children:[(0,t.jsx)(d.nd,{className:"font-semibold",children:"Codice"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Tipo"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Responsabile"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Contatti"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Stato"}),(0,t.jsx)(d.nd,{className:"font-semibold",children:"Data Creazione"}),(0,t.jsx)(d.nd,{className:"font-semibold text-center",children:"Cavi"}),(0,t.jsx)(d.nd,{className:"font-semibold text-center",children:"Completamento"}),(0,t.jsx)(d.nd,{className:"font-semibold text-center",children:"Azioni"})]})}),(0,t.jsx)(d.BF,{children:v?(0,t.jsx)(d.Hj,{children:(0,t.jsx)(d.nA,{colSpan:9,className:"text-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 animate-spin"}),"Caricamento comande..."]})})}):b?(0,t.jsx)(d.Hj,{children:(0,t.jsx)(d.nA,{colSpan:9,className:"text-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2 text-red-600",children:[(0,t.jsx)(Z.A,{className:"h-4 w-4"}),b]})})}):0===eu.length?(0,t.jsx)(d.Hj,{children:(0,t.jsx)(d.nA,{colSpan:9,className:"text-center py-8 text-slate-500",children:"Nessuna comanda trovata"})}):eu.map(e=>(0,t.jsxs)(d.Hj,{className:"hover:bg-gray-50",children:[(0,t.jsx)(d.nA,{children:(0,t.jsx)("div",{className:"font-semibold text-blue-600",children:e.codice_comanda})}),(0,t.jsx)(d.nA,{children:em(e.tipo_comanda)}),(0,t.jsx)(d.nA,{children:(0,t.jsx)("div",{className:"font-medium",children:e.responsabile||"Non assegnato"})}),(0,t.jsx)(d.nA,{children:(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[e.responsabile_telefono&&(0,t.jsxs)("div",{children:["\uD83D\uDCDE ",e.responsabile_telefono]}),e.responsabile_email&&(0,t.jsxs)("div",{children:["✉️ ",e.responsabile_email]})]})}),(0,t.jsx)(d.nA,{children:ed(e.stato)}),(0,t.jsx)(d.nA,{children:(0,t.jsx)("div",{className:"text-sm",children:new Date(e.data_creazione).toLocaleDateString("it-IT")})}),(0,t.jsx)(d.nA,{className:"text-center",children:(0,t.jsx)("div",{className:"font-semibold text-blue-600",children:e.numero_cavi_assegnati||0})}),(0,t.jsx)(d.nA,{className:"text-center",children:(0,t.jsxs)("div",{className:"font-semibold",children:[(e.percentuale_completamento||0).toFixed(1),"%"]})}),(0,t.jsx)(d.nA,{className:"text-center",children:(0,t.jsxs)("div",{className:"flex gap-1 justify-center",children:[(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>{K(e.codice_comanda),V(!0)},title:"Visualizza",children:(0,t.jsx)(Y.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>{et({title:"Funzione in sviluppo",description:"La modifica comande sar\xe0 disponibile presto"})},title:"Modifica",children:(0,t.jsx)(I.A,{className:"h-4 w-4"})}),["POSA","COLLEGAMENTO_PARTENZA","COLLEGAMENTO_ARRIVO"].includes(e.tipo_comanda)&&(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>{K(e.codice_comanda),ee(e.tipo_comanda),"POSA"===e.tipo_comanda?J(!0):U(!0)},title:"POSA"===e.tipo_comanda?"Inserisci Metri Posati":"Inserisci Metri Collegati",children:(0,t.jsx)(f.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>ec(e.codice_comanda),disabled:v,className:"text-red-600 hover:text-red-700",title:"Elimina",children:(0,t.jsx)(L.A,{className:"h-4 w-4"})})]})})]},e.codice_comanda))})]})})})]}),(0,t.jsx)(w,{open:T,onClose:()=>z(!1),onSuccess:el,onError:eo,onComandaCreated:()=>en()}),(0,t.jsx)($,{open:k,onClose:()=>P(!1),onSuccess:el,onError:eo}),(0,t.jsx)(q,{open:M,onClose:()=>{V(!1),K(null)},codiceComanda:W,onSuccess:el,onError:eo}),(0,t.jsx)(B,{open:G,onClose:()=>{U(!1),K(null),ee(null)},codiceComanda:W||"",tipoComanda:Q||"POSA",onSuccess:e=>{el(e),loadComande()},onError:eo}),(0,t.jsx)(H,{open:F,onClose:()=>{J(!1),K(null),ee(null)},codiceComanda:W||"",onSuccess:e=>{el(e),loadComande()},onError:eo})]})}},83997:e=>{"use strict";e.exports=require("tty")},88233:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},89667:(e,a,s)=>{"use strict";s.d(a,{p:()=>n});var t=s(60687),r=s(43210),i=s(4780);let n=r.forwardRef(({className:e,type:a,...s},r)=>(0,t.jsx)("input",{type:a,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),ref:r,...s}));n.displayName="Input"},94735:e=>{"use strict";e.exports=require("events")},96834:(e,a,s)=>{"use strict";s.d(a,{E:()=>o});var t=s(60687);s(43210);var r=s(8730),i=s(24224),n=s(4780);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:a,asChild:s=!1,...i}){let o=s?r.DX:"span";return(0,t.jsx)(o,{"data-slot":"badge",className:(0,n.cn)(l({variant:a}),e),...i})}},99270:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var a=require("../../webpack-runtime.js");a.C(e);var s=e=>a(a.s=e),t=a.X(0,[4447,6539,1658,7400,9464,4951],()=>s(20992));module.exports=t})();