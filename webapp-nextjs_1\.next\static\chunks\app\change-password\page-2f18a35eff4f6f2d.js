(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1143],{381:(e,a,s)=>{"use strict";s.d(a,{A:()=>l});let l=(0,s(19946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},10764:(e,a,s)=>{Promise.resolve().then(s.bind(s,92789))},92789:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>x});var l=s(95155);s(12115);var t=s(6874),r=s.n(t),c=s(381),i=s(35169),n=s(30285),d=s(59471);function x(){return(0,l.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-slate-100 flex items-center justify-center p-4",children:(0,l.jsxs)("div",{className:"w-full max-w-md space-y-6",children:[(0,l.jsxs)("div",{className:"text-center space-y-2",children:[(0,l.jsx)("div",{className:"flex justify-center",children:(0,l.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center",children:(0,l.jsx)(c.A,{className:"w-8 h-8 text-white"})})}),(0,l.jsx)("h1",{className:"text-2xl font-bold text-slate-900",children:"CABLYS"}),(0,l.jsx)("p",{className:"text-slate-600",children:"Cambia Password"})]}),(0,l.jsx)(d.G,{}),(0,l.jsx)("div",{className:"text-center",children:(0,l.jsx)(r(),{href:"/admin",children:(0,l.jsxs)(n.$,{variant:"ghost",className:"text-slate-600 hover:text-slate-900",children:[(0,l.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"Torna alla Dashboard"]})})})]})})}}},e=>{var a=a=>e(e.s=a);e.O(0,[3455,2600,6127,9471,8441,1684,7358],()=>a(10764)),_N_E=e.O()}]);