(()=>{var e={};e.id=318,e.ids=[318],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},54247:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>h,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var r={};s.r(r),s.d(r,{GET:()=>u,POST:()=>p});var o=s(96559),a=s(48088),n=s(37719),i=s(32190);async function u(e){try{let t=e.headers.get("authorization");if(!t||!t.startsWith("Bearer "))return i.NextResponse.json({detail:"Token di autorizzazione mancante"},{status:401});let s="http://localhost:8001";console.log("\uD83D\uDD04 Users API: Proxying request to backend:",`${s}/api/users`);let r=await fetch(`${s}/api/users`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:t}});if(console.log("\uD83D\uDCE1 Users API: Backend response status:",r.status),!r.ok){let e=await r.json().catch(()=>({detail:"Errore sconosciuto"}));return console.error("❌ Users API: Backend error:",e),i.NextResponse.json(e,{status:r.status})}let o=await r.json();return console.log("\uD83D\uDCE1 Users API: Backend response data:",o),i.NextResponse.json(o,{status:r.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("❌ Users API: Error:",e),i.NextResponse.json({detail:"Errore interno del server"},{status:500})}}async function p(e){try{let t=await e.json(),s=e.headers.get("authorization");if(!s||!s.startsWith("Bearer "))return i.NextResponse.json({detail:"Token di autorizzazione mancante"},{status:401});let r="http://localhost:8001";console.log("\uD83D\uDD04 Users API: Proxying POST request to backend:",`${r}/api/users`);let o=await fetch(`${r}/api/users`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:s},body:JSON.stringify(t)});if(console.log("\uD83D\uDCE1 Users API: Backend response status:",o.status),!o.ok){let e=await o.json().catch(()=>({detail:"Errore sconosciuto"}));return console.error("❌ Users API: Backend error:",e),i.NextResponse.json(e,{status:o.status})}let a=await o.json();return console.log("\uD83D\uDCE1 Users API: Backend response data:",a),i.NextResponse.json(a,{status:o.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("❌ Users API: POST Error:",e),i.NextResponse.json({detail:"Errore interno del server"},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/users/route",pathname:"/api/users",filename:"route",bundlePath:"app/api/users/route"},resolvedPagePath:"C:\\CMS\\webapp-nextjs_1\\src\\app\\api\\users\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:h}=c;function x(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580],()=>s(54247));module.exports=r})();