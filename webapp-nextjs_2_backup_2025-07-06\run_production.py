#!/usr/bin/env python
# run_production.py - Sistema di avvio per ambiente di produzione
import sys
import os
from pathlib import Path

# Aggiungi il percorso corrente al PYTHONPATH
current_dir = Path(__file__).resolve().parent
sys.path.insert(0, str(current_dir))

from run_system_advanced import CablysRunSystem

def main():
    """Avvio per ambiente di produzione"""
    print("🏭 Avvio ambiente di produzione CABLYS...")
    
    # Usa la configurazione di produzione
    run_system = CablysRunSystem("run_config_production.json")
    
    # Imposta variabili ambiente per produzione
    os.environ["NODE_ENV"] = "production"
    os.environ["NEXT_TELEMETRY_DISABLED"] = "1"
    
    success = run_system.run()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
