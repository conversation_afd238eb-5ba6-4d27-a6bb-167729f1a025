exports.id=415,exports.ids=[415],exports.modules={4780:(e,t,a)=>{"use strict";a.d(t,{cn:()=>o});var r=a(49384),s=a(82348);function o(...e){return(0,s.QP)((0,r.$)(e))}},10501:(e,t,a)=>{"use strict";a.d(t,{Navbar:()=>T});var r=a(60687),s=a(43210),o=a(16189),i=a(29523),n=a(63213),l=a(32192),d=a(17313),c=a(23361),u=a(19080),p=a(10022),m=a(53411),h=a(6727),x=a(58559),v=a(41862),b=a(56085),g=a(78272),f=a(16023),j=a(70334),w=a(31158),C=a(45583),N=a(41312),y=a(40083),$=a(11860),A=a(12941),S=a(76242);let k=(e,t,a,r)=>{let s={name:"owner"===e?"Menu Admin":"user"===e?"Lista Cantieri":"cantieri_user"===e?"Gestione Cavi":"Home",href:"owner"===e?"/admin":"user"===e?"/cantieri":"cantieri_user"===e?"/cavi":"/",icon:l.A};if("owner"===e&&!t)return[s];if("user"===e||t&&a?.role==="user"){let e=[s];return t&&e.push({name:"Cantieri",href:"/cantieri",icon:d.A}),r&&e.push({name:"Visualizza Cavi",href:"/cavi",icon:c.A},{name:"Parco Cavi",href:"/parco-cavi",icon:u.A},{name:"Gestione Excel",href:"/excel",icon:p.A,hasDropdown:!0},{name:"Report",href:"/reports",icon:m.A},{name:"Gestione Comande",href:"/comande",icon:h.A},{name:"Produttivit\xe0",href:"/productivity",icon:x.A}),e}if("cantieri_user"===e||t&&a?.role==="cantieri_user"){let t=[s];return r&&("cantieri_user"!==e&&t.push({name:"Visualizza Cavi",href:"/cavi",icon:c.A}),t.push({name:"Parco Cavi",href:"/parco-cavi",icon:u.A},{name:"Gestione Excel",href:"/excel",icon:p.A,hasDropdown:!0},{name:"Report",href:"/reports",icon:m.A},{name:"Gestione Comande",href:"/comande",icon:h.A},{name:"Produttivit\xe0",href:"/productivity",icon:x.A})),t}return[s]};function T(){let[e,t]=(0,s.useState)(!1),[a,l]=(0,s.useState)(!1),[u,p]=(0,s.useState)(!1),[m,h]=(0,s.useState)({}),[x,T]=(0,s.useState)(null),_=(0,s.useRef)(null),P=(0,o.usePathname)(),E=(0,o.useRouter)(),{user:I,cantiere:z,isAuthenticated:R,isImpersonating:D,impersonatedUser:B,logout:M}=(0,n.A)(),U=z?.id_cantiere||0,L=z?.commessa||`Cantiere ${U}`,O=k(I?.ruolo,D,B,U),F=async(e,a)=>{if(P!==e){h(e=>({...e,[a]:!0})),p(!0);try{await E.push(e)}catch(e){console.error("Navigation error:",e)}finally{setTimeout(()=>{h(e=>({...e,[a]:!1})),p(!1),t(!1)},300)}}},G=async()=>{h(e=>({...e,logout:!0}));try{await M()}catch(e){console.error("Logout error:",e)}finally{h(e=>({...e,logout:!1}))}};return"/login"!==P&&R?(0,r.jsxs)("nav",{className:"fixed top-0 left-0 right-0 z-50 bg-white border-b border-slate-200 shadow-sm",children:[(0,r.jsx)("div",{className:"max-w-[90%] mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between h-16",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,r.jsx)(S.Bc,{children:(0,r.jsxs)(S.m_,{children:[(0,r.jsx)(S.k$,{asChild:!0,children:(0,r.jsxs)("div",{className:"flex items-center space-x-3 cursor-pointer group transition-all duration-300 hover:scale-105",onClick:()=>F("/","Home"),children:[(0,r.jsxs)("div",{className:"relative w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center group-hover:shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300",children:[m.Home?(0,r.jsx)(v.A,{className:"w-5 h-5 text-white animate-spin"}):(0,r.jsx)(c.A,{className:"w-5 h-5 text-white group-hover:rotate-12 transition-transform duration-300"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg opacity-0 group-hover:opacity-20 transition-opacity duration-300"})]}),(0,r.jsxs)("div",{className:"hidden sm:block",children:[(0,r.jsxs)("h1",{className:"text-xl font-bold text-slate-900 group-hover:text-blue-700 transition-colors duration-300",children:["CABLYS",(0,r.jsx)(b.A,{className:"inline w-4 h-4 ml-1 text-blue-500 opacity-0 group-hover:opacity-100 transition-all duration-300"})]}),(0,r.jsx)("p",{className:"text-xs text-slate-500 -mt-1 group-hover:text-slate-600 transition-colors duration-300",children:"Cable Installation System"})]})]})}),(0,r.jsx)(S.ZI,{children:(0,r.jsx)("p",{children:"Torna alla home"})})]})}),(0,r.jsx)("div",{className:"hidden md:flex items-center space-x-1",children:O.map(e=>{let t=P===e.href||"/"!==e.href&&P.startsWith(e.href),s=e.icon;return e.hasDropdown&&"Gestione Excel"===e.name?(0,r.jsxs)("div",{className:"relative",ref:_,children:[(0,r.jsx)(S.Bc,{children:(0,r.jsxs)(S.m_,{children:[(0,r.jsx)(S.k$,{asChild:!0,children:(0,r.jsxs)(i.$,{variant:"ghost",size:"sm",className:`group flex items-center space-x-2 px-3 py-2 transition-all duration-300 ease-in-out rounded-lg border border-transparent relative overflow-hidden ${t?"bg-gradient-to-r from-blue-100 to-blue-50 text-blue-700 shadow-sm border-blue-200":"text-slate-600 hover:text-slate-900 hover:bg-gradient-to-r hover:from-blue-50 hover:to-slate-50 hover:border-blue-200 hover:shadow-sm"}`,onClick:()=>l(!a),onMouseEnter:()=>T(e.name),onMouseLeave:()=>T(null),children:[(0,r.jsx)(s,{className:`w-4 h-4 transition-all duration-300 ${x===e.name?"scale-110":""}`}),(0,r.jsx)("span",{className:"hidden lg:inline font-medium",children:e.name}),(0,r.jsx)(g.A,{className:`w-3 h-3 transition-transform duration-300 ${a?"rotate-180":""}`}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-all duration-700"})]})}),(0,r.jsx)(S.ZI,{children:(0,r.jsx)("p",{children:"Gestione file Excel"})})]})}),a&&(0,r.jsx)("div",{className:"absolute top-full left-0 mt-2 w-52 bg-white border border-slate-200 rounded-xl shadow-xl z-50 overflow-hidden animate-in slide-in-from-top-2 duration-200",children:(0,r.jsxs)("div",{className:"py-2",children:[(0,r.jsx)("div",{className:"block px-4 py-3 text-sm text-slate-700 hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 hover:text-green-700 transition-all duration-200 cursor-pointer group",onClick:()=>{F("/excel/import","Importa Excel"),l(!1)},children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(f.A,{className:"w-4 h-4 group-hover:scale-110 transition-transform duration-200"}),(0,r.jsx)("span",{className:"font-medium",children:"Importa Excel"}),(0,r.jsx)(j.A,{className:"w-3 h-3 opacity-0 group-hover:opacity-100 transition-all duration-200 ml-auto"})]})}),(0,r.jsx)("div",{className:"block px-4 py-3 text-sm text-slate-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-cyan-50 hover:text-blue-700 transition-all duration-200 cursor-pointer group",onClick:()=>{F("/excel/export","Esporta Excel"),l(!1)},children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(w.A,{className:"w-4 h-4 group-hover:scale-110 transition-transform duration-200"}),(0,r.jsx)("span",{className:"font-medium",children:"Esporta Excel"}),(0,r.jsx)(j.A,{className:"w-3 h-3 opacity-0 group-hover:opacity-100 transition-all duration-200 ml-auto"})]})})]})})]},e.name):(0,r.jsx)(S.Bc,{children:(0,r.jsxs)(S.m_,{children:[(0,r.jsx)(S.k$,{asChild:!0,children:(0,r.jsxs)("div",{className:`group relative cursor-pointer transition-all duration-300 ease-in-out rounded-lg border border-transparent overflow-hidden ${t?"bg-gradient-to-r from-blue-100 to-blue-50 text-blue-700 shadow-sm border-blue-200":"text-slate-600 hover:text-slate-900 hover:bg-gradient-to-r hover:from-blue-50 hover:to-slate-50 hover:border-blue-200 hover:shadow-sm"}`,onClick:()=>F(e.href,e.name),onMouseEnter:()=>T(e.name),onMouseLeave:()=>T(null),children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-2",children:[m[e.name]?(0,r.jsx)(v.A,{className:"w-4 h-4 animate-spin"}):(0,r.jsx)(s,{className:`w-4 h-4 transition-all duration-300 ${x===e.name?"scale-110":""} ${t?"text-blue-600":""}`}),(0,r.jsx)("span",{className:`hidden lg:inline font-medium transition-all duration-300 ${t?"text-blue-700":""}`,children:e.name}),e.name.includes("Produttivit\xe0")&&(0,r.jsx)(C.A,{className:"w-3 h-3 text-yellow-500 animate-pulse"})]}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-all duration-700"}),t&&(0,r.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600"})]})}),(0,r.jsx)(S.ZI,{children:(0,r.jsx)("p",{children:e.name})})]})},e.name)})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 ml-8",children:[U&&U>0&&(0,r.jsx)(S.Bc,{children:(0,r.jsxs)(S.m_,{children:[(0,r.jsx)(S.k$,{asChild:!0,children:(0,r.jsxs)("div",{className:"hidden sm:flex items-center space-x-2 px-3 py-2 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg hover:shadow-sm transition-all duration-300 cursor-default group",children:[(0,r.jsx)(d.A,{className:"w-4 h-4 text-blue-600 group-hover:scale-110 transition-transform duration-300"}),(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("span",{className:"text-blue-900 font-semibold",children:L}),(0,r.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full inline-block ml-2 animate-pulse"})]})]})}),(0,r.jsx)(S.ZI,{children:(0,r.jsxs)("p",{children:["Cantiere attivo: ",L]})})]})}),(0,r.jsxs)("div",{className:"hidden sm:flex items-center space-x-3",children:[(0,r.jsx)(S.Bc,{children:(0,r.jsxs)(S.m_,{children:[(0,r.jsx)(S.k$,{asChild:!0,children:(0,r.jsxs)("div",{className:"flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-slate-50 transition-all duration-300 cursor-default group",children:[(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("p",{className:"text-sm font-semibold text-slate-900 group-hover:text-slate-700 transition-colors duration-300",children:D&&B?B.username:I?.username}),(0,r.jsx)("p",{className:"text-xs text-slate-500 group-hover:text-slate-600 transition-colors duration-300",children:I?.ruolo==="owner"?"Amministratore":I?.ruolo==="user"?"Utente Standard":I?.ruolo==="cantieri_user"?"Utente Cantiere":I?.ruolo||""})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center group-hover:shadow-lg group-hover:shadow-green-500/25 transition-all duration-300",children:(0,r.jsx)(N.A,{className:"w-4 h-4 text-white"})}),(0,r.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white animate-pulse"})]})]})}),(0,r.jsxs)(S.ZI,{children:[(0,r.jsxs)("p",{children:["Utente: ",D&&B?B.username:I?.username]}),(0,r.jsxs)("p",{children:["Ruolo: ",I?.ruolo]})]})]})}),(0,r.jsx)(S.Bc,{children:(0,r.jsxs)(S.m_,{children:[(0,r.jsx)(S.k$,{asChild:!0,children:(0,r.jsxs)(i.$,{variant:"ghost",size:"sm",onClick:G,disabled:m.logout,className:"group relative overflow-hidden hover:bg-red-50 hover:text-red-600 transition-all duration-300 ease-in-out rounded-lg border border-transparent hover:border-red-200 hover:shadow-sm",children:[m.logout?(0,r.jsx)(v.A,{className:"w-4 h-4 animate-spin"}):(0,r.jsx)(y.A,{className:"w-4 h-4 group-hover:scale-110 transition-transform duration-300"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-red-100 to-transparent opacity-0 group-hover:opacity-30 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-all duration-700"})]})}),(0,r.jsx)(S.ZI,{children:(0,r.jsx)("p",{children:D?"Torna al menu admin":"Logout"})})]})})]}),(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsx)(S.Bc,{children:(0,r.jsxs)(S.m_,{children:[(0,r.jsx)(S.k$,{asChild:!0,children:(0,r.jsxs)(i.$,{variant:"ghost",size:"sm",onClick:()=>t(!e),className:"group relative overflow-hidden text-slate-600 hover:bg-blue-50 hover:text-blue-600 transition-all duration-300 ease-in-out rounded-lg border border-transparent hover:border-blue-200 hover:shadow-sm",children:[(0,r.jsx)("div",{className:"relative z-10",children:e?(0,r.jsx)($.A,{className:"w-5 h-5 group-hover:rotate-90 transition-transform duration-300"}):(0,r.jsx)(A.A,{className:"w-5 h-5 group-hover:scale-110 transition-transform duration-300"})}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-blue-100 to-transparent opacity-0 group-hover:opacity-30 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-all duration-700"})]})}),(0,r.jsx)(S.ZI,{children:(0,r.jsx)("p",{children:e?"Chiudi menu":"Apri menu"})})]})})})]})]})}),e&&(0,r.jsx)("div",{className:"md:hidden border-t border-slate-200 bg-gradient-to-b from-white to-slate-50 shadow-lg animate-in slide-in-from-top-2 duration-300",children:(0,r.jsxs)("div",{className:"px-4 pt-4 pb-6 space-y-2",children:[(0,r.jsxs)("div",{className:"mb-4 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center",children:(0,r.jsx)(N.A,{className:"w-4 h-4 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-semibold text-slate-900",children:D&&B?B.username:I?.username}),(0,r.jsx)("p",{className:"text-xs text-slate-600",children:I?.ruolo==="owner"?"Amministratore":I?.ruolo==="user"?"Utente Standard":I?.ruolo==="cantieri_user"?"Utente Cantiere":I?.ruolo||""})]})]}),U&&U>0&&(0,r.jsx)("div",{className:"mt-2 pt-2 border-t border-blue-200",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(d.A,{className:"w-3 h-3 text-blue-600"}),(0,r.jsx)("span",{className:"text-xs text-blue-900 font-medium",children:L}),(0,r.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full animate-pulse"})]})})]}),O.map(e=>{let a=P===e.href||"/"!==e.href&&P.startsWith(e.href),s=e.icon;return(0,r.jsxs)("div",{className:`group relative overflow-hidden rounded-lg transition-all duration-300 ${a?"bg-gradient-to-r from-blue-100 to-blue-50 border border-blue-200 shadow-sm":"hover:bg-gradient-to-r hover:from-blue-50 hover:to-slate-50 hover:border-blue-200 border border-transparent"}`,onClick:()=>{F(e.href,e.name),t(!1)},children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 p-3 cursor-pointer",children:[m[e.name]?(0,r.jsx)(v.A,{className:"w-5 h-5 animate-spin text-blue-600"}):(0,r.jsx)(s,{className:`w-5 h-5 transition-all duration-300 group-hover:scale-110 ${a?"text-blue-600":"text-slate-600 group-hover:text-blue-600"}`}),(0,r.jsx)("span",{className:`font-medium transition-all duration-300 ${a?"text-blue-700":"text-slate-700 group-hover:text-blue-700"}`,children:e.name}),e.name.includes("Produttivit\xe0")&&(0,r.jsx)(C.A,{className:"w-4 h-4 text-yellow-500 animate-pulse ml-auto"}),a&&(0,r.jsx)(j.A,{className:"w-4 h-4 text-blue-600 ml-auto"})]}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-all duration-700"})]},e.name)}),(0,r.jsx)("div",{className:"mt-4 pt-4 border-t border-slate-200",children:(0,r.jsxs)("div",{className:"group relative overflow-hidden rounded-lg transition-all duration-300 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 hover:border-red-200 border border-transparent cursor-pointer",onClick:()=>{G(),t(!1)},children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 p-3",children:[m.logout?(0,r.jsx)(v.A,{className:"w-5 h-5 animate-spin text-red-600"}):(0,r.jsx)(y.A,{className:"w-5 h-5 text-slate-600 group-hover:text-red-600 group-hover:scale-110 transition-all duration-300"}),(0,r.jsx)("span",{className:"font-medium text-slate-700 group-hover:text-red-700 transition-all duration-300",children:D?"Torna al menu admin":"Logout"})]}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-red-100 to-transparent opacity-0 group-hover:opacity-30 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-all duration-700"})]})})]})})]}):null}},10613:(e,t,a)=>{Promise.resolve().then(a.bind(a,69336)),Promise.resolve().then(a.bind(a,10501)),Promise.resolve().then(a.bind(a,70524)),Promise.resolve().then(a.bind(a,91347)),Promise.resolve().then(a.bind(a,63213))},29131:(e,t,a)=>{"use strict";a.d(t,{AuthProvider:()=>s});var r=a(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_2\\src\\contexts\\AuthContext.tsx","useAuth");let s=(0,r.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_2\\src\\contexts\\AuthContext.tsx","AuthProvider")},29523:(e,t,a)=>{"use strict";a.d(t,{$:()=>l,r:()=>n});var r=a(60687);a(43210);var s=a(8730),o=a(24224),i=a(4780);let n=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:a,asChild:o=!1,...l}){let d=o?s.DX:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,i.cn)(n({variant:t,size:a,className:e})),...l})}},29867:(e,t,a)=>{"use strict";a.d(t,{dj:()=>p});var r=a(43210);let s=0,o=new Map,i=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);o.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=t;return a?i(a):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function c(e){d=n(d,e),l.forEach(e=>{e(d)})}function u({...e}){let t=(s=(s+1)%Number.MAX_VALUE).toString(),a=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||a()}}}),{id:t,dismiss:a,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function p(){let[e,t]=(0,r.useState)(d);return{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},50346:()=>{},54679:(e,t,a)=>{"use strict";a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs_2\\\\src\\\\components\\\\layout\\\\MainContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_2\\src\\components\\layout\\MainContent.tsx","default")},57562:(e,t,a)=>{"use strict";a.d(t,{ToastProvider:()=>s});var r=a(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call useToast() from the server but useToast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_2\\src\\components\\ui\\toast-notification.tsx","useToast");let s=(0,r.registerClientReference)(function(){throw Error("Attempted to call ToastProvider() from the server but ToastProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_2\\src\\components\\ui\\toast-notification.tsx","ToastProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useToastActions() from the server but useToastActions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_2\\src\\components\\ui\\toast-notification.tsx","useToastActions")},61135:()=>{},62185:(e,t,a)=>{"use strict";a.d(t,{AR:()=>d,At:()=>i,CV:()=>l,FH:()=>s,Fw:()=>n,ZQ:()=>o,_I:()=>m,dG:()=>h,km:()=>c,mg:()=>u,ug:()=>p});let r=a(51060).A.create({baseURL:"http://localhost:8001",timeout:3e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>e,e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response?.status,Promise.reject(e)));let s={get:async(e,t)=>(await r.get(e,t)).data,post:async(e,t,a)=>(await r.post(e,t,a)).data,put:async(e,t,a)=>(await r.put(e,t,a)).data,patch:async(e,t,a)=>(await r.patch(e,t,a)).data,delete:async(e,t)=>(await r.delete(e,t)).data},o={login:async e=>{let t=new FormData;return t.append("username",e.username),t.append("password",e.password),(await r.post("/api/auth/login",t,{headers:{"Content-Type":"application/x-www-form-urlencoded"}})).data},loginCantiere:async e=>(await r.post("/api/auth/login/cantiere",{codice_univoco:e.codice_cantiere,password:e.password_cantiere})).data,verifyToken:async()=>(await r.post("/api/auth/test-token")).data,logout:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),window.location.href="/login"}},i={getCavi:(e,t)=>s.get(`/api/cavi/${e}`,{params:t}),getCavo:(e,t)=>s.get(`/api/cavi/${e}/${t}`),checkCavo:(e,t)=>s.get(`/api/cavi/${e}/check/${t}`),createCavo:(e,t)=>s.post(`/api/cavi/${e}`,t),updateCavo:(e,t,a)=>s.put(`/api/cavi/${e}/${t}`,a),deleteCavo:(e,t,a)=>s.delete(`/api/cavi/${e}/${t}`,{data:a}),updateMetriPosati:(e,t,a,r,o)=>s.post(`/api/cavi/${e}/${t}/metri-posati`,{metri_posati:a,id_bobina:r,force_over:o||!1}),updateBobina:(e,t,a,r)=>s.post(`/api/cavi/${e}/${t}/bobina`,{id_bobina:a,force_over:r||!1}),cancelInstallation:(e,t)=>s.post(`/api/cavi/${e}/${t}/cancel-installation`),collegaCavo:(e,t,a,r)=>s.post(`/api/cavi/${e}/${t}/collegamento`,{lato:a,responsabile:r}),scollegaCavo:(e,t,a)=>s.delete(`/api/cavi/${e}/${t}/collegamento`,{data:{lato:a}}),markAsSpare:(e,t,a)=>s.put(`/api/cavi/${e}/${t}/spare`,{spare:+!!a}),debugCavi:e=>s.get(`/api/cavi/debug/${e}`),debugCaviRaw:e=>s.get(`/api/cavi/debug/raw/${e}`)},n={getBobine:(e,t)=>s.get(`/api/parco-cavi/${e}`,{params:t}),getBobina:(e,t)=>s.get(`/api/parco-cavi/${e}/${t}`),getBobineCompatibili:(e,t)=>s.get(`/api/parco-cavi/${e}/compatibili`,{params:t}),createBobina:(e,t)=>s.post(`/api/parco-cavi/${e}`,t),updateBobina:(e,t,a)=>s.put(`/api/parco-cavi/${e}/${t}`,a),deleteBobina:(e,t)=>s.delete(`/api/parco-cavi/${e}/${t}`),isFirstBobinaInsertion:e=>s.get(`/api/parco-cavi/${e}/is-first-insertion`),updateBobina:(e,t,a)=>s.put(`/api/parco-cavi/${e}/${t}`,a),deleteBobina:(e,t)=>s.delete(`/api/parco-cavi/${e}/${t}`),checkDisponibilita:(e,t,a)=>s.get(`/api/parco-cavi/${e}/${t}/disponibilita`,{params:{metri_richiesti:a}})},l={getComande:e=>s.get(`/api/comande/cantiere/${e}`),getComanda:(e,t)=>s.get(`/api/comande/${t}`),getCaviComanda:e=>s.get(`/api/comande/${e}/cavi`),createComanda:(e,t)=>s.post(`/api/comande/cantiere/${e}`,t),createComandaWithCavi:(e,t,a)=>s.post(`/api/comande/cantiere/${e}/crea-con-cavi`,t,{params:{lista_id_cavi:a}}),updateDatiComanda:(e,t,a)=>s.put(`/api/comande/${e}/${t}`,a),updateComanda:(e,t,a)=>s.put(`/api/comande/cantiere/${e}/${t}`,a),deleteComanda:(e,t)=>s.delete(`/api/comande/cantiere/${e}/${t}`),assegnaCavi:(e,t,a)=>s.post(`/api/comande/cantiere/${e}/${t}/assegna-cavi`,{cavi_ids:a}),rimuoviCavi:(e,t,a)=>s.delete(`/api/comande/cantiere/${e}/${t}/rimuovi-cavi`,{data:{cavi_ids:a}}),getStatistiche:e=>s.get(`/api/comande/cantiere/${e}/statistiche`),cambiaStato:(e,t,a)=>s.put(`/api/comande/cantiere/${e}/${t}/stato`,{nuovo_stato:a})},d={getResponsabili:e=>s.get(`/api/responsabili/cantiere/${e}`),createResponsabile:(e,t)=>s.post(`/api/responsabili/${e}`,t),updateResponsabile:(e,t,a)=>s.put(`/api/responsabili/${e}/${t}`,a),deleteResponsabile:(e,t)=>s.delete(`/api/responsabili/${e}/${t}`)},c={getCertificazioni:e=>s.get(`/api/cantieri/${e}/certificazioni`),createCertificazione:(e,t)=>s.post(`/api/cantieri/${e}/certificazioni`,t),generatePDF:(e,t)=>s.get(`/api/cantieri/${e}/pdf-cei-64-8/${t}`,{responseType:"blob"})},u={importCavi:(e,t,a)=>{let r=new FormData;return r.append("file",t),r.append("revisione",a),s.post(`/api/excel/${e}/import-cavi`,r,{headers:{"Content-Type":"multipart/form-data"}})},importBobine:(e,t)=>{let a=new FormData;return a.append("file",t),s.post(`/api/excel/${e}/import-parco-bobine`,a,{headers:{"Content-Type":"multipart/form-data"}})},exportCavi:e=>s.get(`/api/excel/${e}/export-cavi`,{responseType:"blob"}),exportBobine:e=>s.get(`/api/excel/${e}/export-parco-bobine`,{responseType:"blob"})},p={getReportAvanzamento:e=>s.get(`/api/reports/${e}/avanzamento`),getReportBOQ:e=>s.get(`/api/reports/${e}/boq`),getReportUtilizzoBobine:e=>s.get(`/api/reports/${e}/storico-bobine`),getReportProgress:e=>s.get(`/api/reports/${e}/progress`),getReportPosaPeriodo:(e,t,a)=>{let r=new URLSearchParams;t&&r.append("data_inizio",t),a&&r.append("data_fine",a);let o=r.toString();return s.get(`/api/reports/${e}/posa-periodo${o?`?${o}`:""}`)}},m={getCantieri:()=>s.get("/api/cantieri"),getCantiere:e=>s.get(`/api/cantieri/${e}`),createCantiere:e=>s.post("/api/cantieri",e),updateCantiere:(e,t)=>s.put(`/api/cantieri/${e}`,t),getCantiereStatistics:e=>s.get(`/api/cantieri/${e}/statistics`)},h={getUsers:()=>s.get("/api/users"),getUser:e=>s.get(`/api/users/${e}`),createUser:e=>s.post("/api/users",e),updateUser:(e,t)=>s.put(`/api/users/${e}`,t),deleteUser:e=>s.delete(`/api/users/${e}`),toggleUserStatus:e=>s.get(`/api/users/toggle/${e}`),checkExpiredUsers:()=>s.get("/api/users/check-expired"),impersonateUser:e=>s.post("/api/auth/impersonate",{user_id:e}),getDatabaseData:()=>s.get("/api/users/db-raw"),resetDatabase:()=>s.post("/api/admin/reset-database")}},63213:(e,t,a)=>{"use strict";a.d(t,{A:()=>n,AuthProvider:()=>l});var r=a(60687),s=a(43210),o=a(62185);let i=(0,s.createContext)(void 0);function n(){let e=(0,s.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function l({children:e}){let[t,a]=(0,s.useState)(null),[n,l]=(0,s.useState)(null),[d,c]=(0,s.useState)(!0),[u,p]=(0,s.useState)(()=>!1),[m,h]=(0,s.useState)(()=>null),[x,v]=(0,s.useState)(null),[b,g]=(0,s.useState)(null),[f,j]=(0,s.useState)(null),w=!!t&&t.id_utente||!!n&&n.id_cantiere,C=async()=>{try{return void c(!1)}catch(e){a(null),l(null)}finally{setTimeout(()=>{c(!1)},500)}},N=async(e,t)=>{try{return c(!0),await o.ZQ.login({username:e,password:t}),{success:!1,error:"Errore durante il salvataggio dei dati"}}catch(e){return console.error("❌ AuthContext: Errore durante login:",e),{success:!1,error:e.response?.data?.detail||e.message||"Errore durante il login"}}finally{c(!1)}},y=async(e,t)=>{try{return c(!0),await o.ZQ.loginCantiere({codice_cantiere:e,password_cantiere:t}),{success:!1,error:"Errore durante il salvataggio dei dati del cantiere"}}catch(e){return console.error("❌ AuthContext: Errore durante login cantiere:",e),{success:!1,error:e.response?.data?.detail||e.message||"Errore durante il login del cantiere"}}finally{c(!1)}},$=async e=>{try{await o.dG.impersonateUser(e)}catch(e){throw e}};return(0,r.jsx)(i.Provider,{value:{user:t,cantiere:n,isAuthenticated:w,isLoading:d,isImpersonating:u,impersonatedUser:m,expirationWarning:x,daysUntilExpiration:b,expirationDate:f,login:N,loginCantiere:y,logout:()=>{},checkAuth:C,impersonateUser:$,selectCantiere:e=>{if(e&&e.id_cantiere){let t=e.commessa||`Cantiere ${e.id_cantiere}`;localStorage.setItem("selectedCantiereId",e.id_cantiere.toString()),localStorage.setItem("selectedCantiereName",t),l({...e,commessa:t})}},clearCantiere:()=>{l(null)},dismissExpirationWarning:()=>{v(null),g(null),j(null)}},children:e})}},68757:(e,t,a)=>{Promise.resolve().then(a.bind(a,54679)),Promise.resolve().then(a.bind(a,93319)),Promise.resolve().then(a.bind(a,57562)),Promise.resolve().then(a.bind(a,79737)),Promise.resolve().then(a.bind(a,29131))},69336:(e,t,a)=>{"use strict";a.d(t,{default:()=>h});var r=a(60687),s=a(43210),o=a(63213);let i=()=>{let{isAuthenticated:e,user:t,cantiere:a,checkAuth:r,logout:i}=(0,o.A)(),n=(0,s.useRef)(null),l=(0,s.useRef)(Date.now()),d=()=>{n.current&&(clearInterval(n.current),n.current=null)};return(0,s.useEffect)(()=>{},[e,t,a]),(0,s.useEffect)(()=>()=>{d()},[]),{updateActivity:()=>{l.current=Date.now()},checkSessionExpiry:()=>!0}};var n=a(91821),l=a(29523),d=a(43649),c=a(48730),u=a(40228),p=a(11860);function m(){let{expirationWarning:e,daysUntilExpiration:t,expirationDate:a,dismissExpirationWarning:s}=(0,o.A)();return e?(0,r.jsx)(n.Fc,{variant:0===t||1===t?"destructive":"default",className:"mb-4 border-l-4",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-2",children:[0===t?(0,r.jsx)(d.A,{className:"h-4 w-4"}):1===t?(0,r.jsx)(c.A,{className:"h-4 w-4"}):(0,r.jsx)(u.A,{className:"h-4 w-4"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)(n.TN,{className:"font-medium",children:e}),a&&(0,r.jsxs)(n.TN,{className:"text-sm mt-1 opacity-90",children:["Data di scadenza: ",new Date(a).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit",year:"numeric"})]}),(0,r.jsx)(n.TN,{className:"text-sm mt-2 opacity-80",children:"Contatta l'amministratore per rinnovare il tuo account."})]})]}),(0,r.jsx)(l.$,{variant:"ghost",size:"sm",onClick:s,className:"h-6 w-6 p-0 hover:bg-transparent",children:(0,r.jsx)(p.A,{className:"h-4 w-4"})})]})}):null}function h({children:e}){let{isAuthenticated:t}=(0,o.A)();return i(),(0,r.jsxs)("main",{className:"pt-16",children:[t&&(0,r.jsx)("div",{className:"container mx-auto px-4 py-2",children:(0,r.jsx)(m,{})}),e]})}},70524:(e,t,a)=>{"use strict";a.d(t,{E:()=>v,ToastProvider:()=>x});var r=a(60687),s=a(43210),o=a(51215),i=a(5336),n=a(93613),l=a(43649),d=a(96882),c=a(11860);let u=(0,s.createContext)(void 0),p={success:{icon:i.A,bgColor:"bg-green-50",borderColor:"border-green-200",iconColor:"text-green-600",titleColor:"text-green-900",descColor:"text-green-700"},error:{icon:n.A,bgColor:"bg-red-50",borderColor:"border-red-200",iconColor:"text-red-600",titleColor:"text-red-900",descColor:"text-red-700"},warning:{icon:l.A,bgColor:"bg-orange-50",borderColor:"border-orange-200",iconColor:"text-orange-600",titleColor:"text-orange-900",descColor:"text-orange-700"},info:{icon:d.A,bgColor:"bg-blue-50",borderColor:"border-blue-200",iconColor:"text-blue-600",titleColor:"text-blue-900",descColor:"text-blue-700"}};function m({toast:e,onRemove:t}){let[a,o]=(0,s.useState)(!1),[i,n]=(0,s.useState)(!1),l=p[e.type],d=l.icon;return(0,r.jsx)("div",{className:`
        transform transition-all duration-300 ease-in-out mb-3
        ${a&&!i?"translate-x-0 opacity-100 scale-100":"translate-x-full opacity-0 scale-95"}
      `,children:(0,r.jsx)("div",{className:`
        max-w-sm w-full ${l.bgColor} ${l.borderColor} border-l-4 
        rounded-lg shadow-lg p-4 pointer-events-auto
      `,children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(d,{className:`w-5 h-5 ${l.iconColor}`})}),(0,r.jsxs)("div",{className:"ml-3 flex-1",children:[(0,r.jsx)("p",{className:`text-sm font-medium ${l.titleColor}`,children:e.title}),e.description&&(0,r.jsx)("p",{className:`mt-1 text-sm ${l.descColor}`,children:e.description}),e.action&&(0,r.jsx)("div",{className:"mt-3",children:(0,r.jsx)("button",{onClick:e.action.onClick,className:`
                    text-sm font-medium ${l.iconColor} 
                    hover:underline focus:outline-none focus:underline
                  `,children:e.action.label})})]}),(0,r.jsx)("div",{className:"ml-4 flex-shrink-0",children:(0,r.jsx)("button",{onClick:()=>{n(!0),setTimeout(()=>t(e.id),300)},className:`
                inline-flex ${l.descColor} hover:${l.titleColor} 
                focus:outline-none focus:${l.titleColor} transition-colors
              `,children:(0,r.jsx)(c.A,{className:"w-4 h-4"})})})]})})})}function h({toasts:e,removeToast:t}){let[a,i]=(0,s.useState)(!1);return a?(0,o.createPortal)((0,r.jsx)("div",{className:"fixed top-4 right-4 z-50 pointer-events-none",children:(0,r.jsx)("div",{className:"flex flex-col-reverse",children:e.map(e=>(0,r.jsx)(m,{toast:e,onRemove:t},e.id))})}),document.body):null}function x({children:e}){let[t,a]=(0,s.useState)([]),o=e=>{a(t=>t.filter(t=>t.id!==e))};return(0,r.jsxs)(u.Provider,{value:{toasts:t,addToast:e=>{let t=Math.random().toString(36).substr(2,9);a(a=>[...a,{...e,id:t}])},removeToast:o,clearToasts:()=>{a([])}},children:[e,(0,r.jsx)(h,{toasts:t,removeToast:o})]})}function v(){let{addToast:e}=function(){let e=(0,s.useContext)(u);if(!e)throw Error("useToast must be used within a ToastProvider");return e}();return{success:(t,a,r)=>e({type:"success",title:t,description:a,action:r}),error:(t,a,r)=>e({type:"error",title:t,description:a,action:r,duration:7e3}),warning:(t,a,r)=>e({type:"warning",title:t,description:a,action:r,duration:6e3}),info:(t,a,r)=>e({type:"info",title:t,description:a,action:r}),cavoDisconnected:t=>e({type:"success",title:"Cavo Scollegato",description:`Il cavo ${t} \xe8 stato scollegato con successo.`}),pdfGenerated:(t,a)=>e({type:"success",title:"PDF Generato",description:`Certificato per il cavo ${a} salvato come ${t}.`,action:{label:"Apri Cartella",onClick:()=>{console.log("Apertura cartella download...")}}}),certificationError:(t,a)=>e({type:"error",title:"Certificazione Fallita",description:`Impossibile certificare il cavo ${t}: ${a}`,duration:8e3}),actionInProgress:(t,a)=>e({type:"info",title:`${t} in Corso`,description:`Elaborazione del cavo ${a}...`,duration:3e3})}}},76242:(e,t,a)=>{"use strict";a.d(t,{Bc:()=>n,ZI:()=>c,k$:()=>d,m_:()=>l});var r=a(60687),s=a(43210),o=a(9989),i=a(4780);let n=o.Kq,l=o.bL,d=o.l9,c=s.forwardRef(({className:e,sideOffset:t=4,...a},s)=>(0,r.jsx)(o.UC,{ref:s,sideOffset:t,className:(0,i.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...a}));c.displayName=o.UC.displayName},79737:(e,t,a)=>{"use strict";a.d(t,{Toaster:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_2\\src\\components\\ui\\toaster.tsx","Toaster")},85543:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,16444,23)),Promise.resolve().then(a.t.bind(a,16042,23)),Promise.resolve().then(a.t.bind(a,88170,23)),Promise.resolve().then(a.t.bind(a,49477,23)),Promise.resolve().then(a.t.bind(a,29345,23)),Promise.resolve().then(a.t.bind(a,12089,23)),Promise.resolve().then(a.t.bind(a,46577,23)),Promise.resolve().then(a.t.bind(a,31307,23))},91347:(e,t,a)=>{"use strict";a.d(t,{Toaster:()=>d});var r=a(60687),s=a(29867),o=a(29523),i=a(93613),n=a(5336),l=a(11860);function d(){let{toasts:e,dismiss:t}=(0,s.dj)();return(0,r.jsx)("div",{className:"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",children:e.map(e=>(0,r.jsxs)("div",{className:"group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",style:{backgroundColor:"destructive"===e.variant?"#fef2f2":"#f0f9ff",borderColor:"destructive"===e.variant?"#fecaca":"#bae6fd"},children:[(0,r.jsxs)("div",{className:"flex items-start space-x-2",children:["destructive"===e.variant?(0,r.jsx)(i.A,{className:"h-4 w-4 text-red-600 mt-0.5"}):(0,r.jsx)(n.A,{className:"h-4 w-4 text-green-600 mt-0.5"}),(0,r.jsxs)("div",{className:"grid gap-1",children:[e.title&&(0,r.jsx)("div",{className:`text-sm font-semibold ${"destructive"===e.variant?"text-red-900":"text-gray-900"}`,children:e.title}),e.description&&(0,r.jsx)("div",{className:`text-sm ${"destructive"===e.variant?"text-red-700":"text-gray-700"}`,children:e.description})]})]}),(0,r.jsx)(o.$,{variant:"ghost",size:"sm",className:"absolute right-1 top-1 h-6 w-6 p-0 hover:bg-transparent",onClick:()=>t(e.id),children:(0,r.jsx)(l.A,{className:"h-3 w-3"})})]},e.id))})}},91821:(e,t,a)=>{"use strict";a.d(t,{Fc:()=>l,TN:()=>d});var r=a(60687),s=a(43210),o=a(24224),i=a(4780);let n=(0,o.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),l=s.forwardRef(({className:e,variant:t,...a},s)=>(0,r.jsx)("div",{ref:s,role:"alert",className:(0,i.cn)(n({variant:t}),e),...a}));l.displayName="Alert",s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("h5",{ref:a,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",e),...t})).displayName="AlertTitle";let d=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",e),...t}));d.displayName="AlertDescription"},93319:(e,t,a)=>{"use strict";a.d(t,{Navbar:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call Navbar() from the server but Navbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_2\\src\\components\\layout\\Navbar.tsx","Navbar")},94431:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>h,metadata:()=>m});var r=a(37413),s=a(35759),o=a.n(s),i=a(29404),n=a.n(i);a(61135),a(50346);var l=a(93319),d=a(54679),c=a(29131),u=a(79737),p=a(57562);let m={title:"CABLYS - Cable Installation Advance System",description:"Sistema avanzato per la gestione dell'installazione cavi",manifest:"/manifest.json",themeColor:"#2563eb",viewport:"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no",appleWebApp:{capable:!0,statusBarStyle:"default",title:"CABLYS"}};function h({children:e}){return(0,r.jsx)("html",{lang:"it",children:(0,r.jsx)("body",{className:`${o().variable} ${n().variable} antialiased`,children:(0,r.jsx)(c.AuthProvider,{children:(0,r.jsxs)(p.ToastProvider,{children:[(0,r.jsxs)("div",{className:"min-h-screen bg-slate-50",children:[(0,r.jsx)(l.Navbar,{}),(0,r.jsx)(d.default,{children:e})]}),(0,r.jsx)(u.Toaster,{})]})})})})}},99455:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,86346,23)),Promise.resolve().then(a.t.bind(a,27924,23)),Promise.resolve().then(a.t.bind(a,35656,23)),Promise.resolve().then(a.t.bind(a,40099,23)),Promise.resolve().then(a.t.bind(a,38243,23)),Promise.resolve().then(a.t.bind(a,28827,23)),Promise.resolve().then(a.t.bind(a,62763,23)),Promise.resolve().then(a.t.bind(a,97173,23))}};