(()=>{var e={};e.id=1245,e.ids=[1245],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48248:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>l,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>u});var a=t(96559),n=t(48088),i=t(37719),o=t(32190);async function u(e,{params:r}){try{let e=parseInt(r.cantiereId);if(isNaN(e))return o.NextResponse.json({error:"ID cantiere non valido"},{status:400});let t={temperature:18.5,humidity:68,pressure:1013.2,description:"Partly cloudy",city:"Milano",country:"IT",timestamp:new Date().toISOString(),isDemo:!0,source:"cantiere_database"};return o.NextResponse.json({success:!0,data:t,message:"Dati meteorologici recuperati con successo"})}catch(e){return console.error("Errore nel recupero dati meteo:",e),o.NextResponse.json({error:"Errore interno del server"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/cantieri/[cantiereId]/weather/route",pathname:"/api/cantieri/[cantiereId]/weather",filename:"route",bundlePath:"app/api/cantieri/[cantiereId]/weather/route"},resolvedPagePath:"C:\\CMS\\webapp-nextjs_1\\src\\app\\api\\cantieri\\[cantiereId]\\weather\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:d,serverHooks:l}=c;function x(){return(0,i.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:d})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580],()=>t(48248));module.exports=s})();