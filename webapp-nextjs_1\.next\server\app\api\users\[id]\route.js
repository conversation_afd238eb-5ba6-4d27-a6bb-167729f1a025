(()=>{var e={};e.id=6100,e.ids=[6100],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{},98056:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>j,routeModule:()=>l,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>h});var r={};s.r(r),s.d(r,{DELETE:()=>p,GET:()=>u,PUT:()=>c});var o=s(96559),a=s(48088),n=s(37719),i=s(32190);async function u(e,{params:t}){try{let s=t.id,r=e.headers.get("authorization");if(!r||!r.startsWith("Bearer "))return i.NextResponse.json({detail:"Token di autorizzazione mancante"},{status:401});let o="http://localhost:8001";console.log("\uD83D\uDD04 User API: Proxying request to backend:",`${o}/api/users/${s}`);let a=await fetch(`${o}/api/users/${s}`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:r}});if(console.log("\uD83D\uDCE1 User API: Backend response status:",a.status),!a.ok){let e=await a.json().catch(()=>({detail:"Errore sconosciuto"}));return console.error("❌ User API: Backend error:",e),i.NextResponse.json(e,{status:a.status})}let n=await a.json();return console.log("\uD83D\uDCE1 User API: Backend response data:",n),i.NextResponse.json(n,{status:a.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("❌ User API: Error:",e),i.NextResponse.json({detail:"Errore interno del server"},{status:500})}}async function c(e,{params:t}){try{let s=t.id,r=await e.json(),o=e.headers.get("authorization");if(!o||!o.startsWith("Bearer "))return i.NextResponse.json({detail:"Token di autorizzazione mancante"},{status:401});let a="http://localhost:8001";console.log("\uD83D\uDD04 User API: Proxying PUT request to backend:",`${a}/api/users/${s}`);let n=await fetch(`${a}/api/users/${s}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:o},body:JSON.stringify(r)});if(console.log("\uD83D\uDCE1 User API: Backend response status:",n.status),!n.ok){let e=await n.json().catch(()=>({detail:"Errore sconosciuto"}));return console.error("❌ User API: Backend error:",e),i.NextResponse.json(e,{status:n.status})}let u=await n.json();return console.log("\uD83D\uDCE1 User API: Backend response data:",u),i.NextResponse.json(u,{status:n.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("❌ User API: PUT Error:",e),i.NextResponse.json({detail:"Errore interno del server"},{status:500})}}async function p(e,{params:t}){try{let s=t.id,r=e.headers.get("authorization");if(!r||!r.startsWith("Bearer "))return i.NextResponse.json({detail:"Token di autorizzazione mancante"},{status:401});let o="http://localhost:8001";console.log("\uD83D\uDD04 User API: Proxying DELETE request to backend:",`${o}/api/users/${s}`);let a=await fetch(`${o}/api/users/${s}`,{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:r}});if(console.log("\uD83D\uDCE1 User API: Backend response status:",a.status),!a.ok){let e=await a.json().catch(()=>({detail:"Errore sconosciuto"}));return console.error("❌ User API: Backend error:",e),i.NextResponse.json(e,{status:a.status})}let n=await a.json();return console.log("\uD83D\uDCE1 User API: Backend response data:",n),i.NextResponse.json(n,{status:a.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("❌ User API: DELETE Error:",e),i.NextResponse.json({detail:"Errore interno del server"},{status:500})}}let l=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/users/[id]/route",pathname:"/api/users/[id]",filename:"route",bundlePath:"app/api/users/[id]/route"},resolvedPagePath:"C:\\CMS\\webapp-nextjs_1\\src\\app\\api\\users\\[id]\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:d,workUnitAsyncStorage:h,serverHooks:x}=l;function j(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:h})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580],()=>s(98056));module.exports=r})();