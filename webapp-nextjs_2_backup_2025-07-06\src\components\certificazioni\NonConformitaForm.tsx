'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle, Save, X, AlertTriangle } from 'lucide-react'
import { NonConformita, NonConformitaCreate } from '@/types/certificazioni'
import { nonConformitaApi, caviApi, responsabiliApi } from '@/lib/api'

interface NonConformitaFormProps {
  cantiereId: number
  nonConformita?: NonConformita | null
  onSuccess: () => void
  onCancel: () => void
}

interface Cavo {
  id_cavo: string
  tipologia?: string
  sezione?: string
}

interface Responsabile {
  id_responsabile: number
  nome_responsabile: string
}

export default function NonConformitaForm({ 
  cantiereId, 
  nonConformita, 
  onSuccess, 
  onCancel 
}: NonConformitaFormProps) {
  const [formData, setFormData] = useState<NonConformitaCreate>({
    id_cavo: '',
    tipo_non_conformita: '',
    descrizione: '',
    severita: 'MEDIA',
    stato: 'APERTA',
    id_responsabile_rilevazione: 0,
    note_risoluzione: '',
    data_risoluzione: null
  })

  const [cavi, setCavi] = useState<Cavo[]>([])
  const [responsabili, setResponsabili] = useState<Responsabile[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingData, setIsLoadingData] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})

  const isEdit = !!nonConformita

  // Carica dati iniziali
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoadingData(true)
        const [caviResponse, responsabiliResponse] = await Promise.all([
          caviApi.getCavi(cantiereId),
          responsabiliApi.getResponsabili(cantiereId)
        ])

        setCavi(caviResponse.data || [])
        setResponsabili(responsabiliResponse.data || [])

        // Se è in modalità edit, popola il form
        if (nonConformita) {
          setFormData({
            id_cavo: nonConformita.id_cavo,
            tipo_non_conformita: nonConformita.tipo_non_conformita,
            descrizione: nonConformita.descrizione,
            severita: nonConformita.severita,
            stato: nonConformita.stato,
            id_responsabile_rilevazione: nonConformita.id_responsabile_rilevazione,
            note_risoluzione: nonConformita.note_risoluzione || '',
            data_risoluzione: nonConformita.data_risoluzione
          })
        }
      } catch (error) {
        console.error('Errore nel caricamento dati:', error)
        setError('Errore nel caricamento dei dati')
      } finally {
        setIsLoadingData(false)
      }
    }

    loadData()
  }, [cantiereId, nonConformita])

  const handleInputChange = (field: keyof NonConformitaCreate, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Rimuovi errore di validazione se presente
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {}

    if (!formData.id_cavo) {
      errors.id_cavo = 'ID Cavo è obbligatorio'
    }

    if (!formData.tipo_non_conformita) {
      errors.tipo_non_conformita = 'Tipo non conformità è obbligatorio'
    }

    if (!formData.descrizione.trim()) {
      errors.descrizione = 'Descrizione è obbligatoria'
    }

    if (formData.id_responsabile_rilevazione === 0) {
      errors.id_responsabile_rilevazione = 'Responsabile rilevazione è obbligatorio'
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      if (isEdit && nonConformita) {
        await nonConformitaApi.updateNonConformita(nonConformita.id_non_conformita, formData)
      } else {
        await nonConformitaApi.createNonConformita(cantiereId, formData)
      }
      
      onSuccess()
    } catch (error: any) {
      console.error('Errore nel salvataggio:', error)
      setError(error.response?.data?.detail || 'Errore nel salvataggio della non conformità')
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoadingData) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-slate-600">Caricamento dati...</span>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-slate-900 flex items-center gap-2">
            <AlertTriangle className="h-6 w-6 text-orange-600" />
            {isEdit ? 'Modifica Non Conformità' : 'Nuova Non Conformità'}
          </h1>
          <p className="text-slate-600 mt-1">
            {isEdit ? 'Aggiorna i dettagli della non conformità' : 'Registra una nuova non conformità'}
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Informazioni Base */}
        <Card className="border-l-4 border-l-orange-500">
          <CardHeader className="bg-gradient-to-r from-orange-50 to-transparent">
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              Informazioni Base
            </CardTitle>
            <CardDescription>Dettagli principali della non conformità</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="id_cavo" className="text-sm font-medium">ID Cavo *</Label>
                <Select value={formData.id_cavo} onValueChange={(value) => handleInputChange('id_cavo', value)}>
                  <SelectTrigger className={`transition-colors ${validationErrors.id_cavo ? 'border-red-500 focus:ring-red-500' : 'focus:ring-orange-500'}`}>
                    <SelectValue placeholder="Seleziona cavo" />
                  </SelectTrigger>
                  <SelectContent>
                    {cavi.map((cavo) => (
                      <SelectItem key={cavo.id_cavo} value={cavo.id_cavo}>
                        <div className="flex items-center gap-2">
                          <span className="font-mono text-sm">{cavo.id_cavo}</span>
                          {cavo.tipologia && <span className="text-slate-500">- {cavo.tipologia}</span>}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {validationErrors.id_cavo && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {validationErrors.id_cavo}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="tipo_non_conformita" className="text-sm font-medium">Tipo Non Conformità *</Label>
                <Select 
                  value={formData.tipo_non_conformita} 
                  onValueChange={(value) => handleInputChange('tipo_non_conformita', value)}
                >
                  <SelectTrigger className={`transition-colors ${validationErrors.tipo_non_conformita ? 'border-red-500 focus:ring-red-500' : 'focus:ring-orange-500'}`}>
                    <SelectValue placeholder="Seleziona tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ISOLAMENTO">🔌 Isolamento</SelectItem>
                    <SelectItem value="CONTINUITA">🔗 Continuità</SelectItem>
                    <SelectItem value="RESISTENZA">⚡ Resistenza</SelectItem>
                    <SelectItem value="INSTALLAZIONE">🔧 Installazione</SelectItem>
                    <SelectItem value="COLLEGAMENTO">🔌 Collegamento</SelectItem>
                    <SelectItem value="DOCUMENTAZIONE">📋 Documentazione</SelectItem>
                    <SelectItem value="ALTRO">❓ Altro</SelectItem>
                  </SelectContent>
                </Select>
                {validationErrors.tipo_non_conformita && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {validationErrors.tipo_non_conformita}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="severita" className="text-sm font-medium">Severità</Label>
                <Select value={formData.severita} onValueChange={(value) => handleInputChange('severita', value)}>
                  <SelectTrigger className="focus:ring-orange-500">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="BASSA">🟢 Bassa</SelectItem>
                    <SelectItem value="MEDIA">🟡 Media</SelectItem>
                    <SelectItem value="ALTA">🟠 Alta</SelectItem>
                    <SelectItem value="CRITICA">🔴 Critica</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="stato" className="text-sm font-medium">Stato</Label>
                <Select value={formData.stato} onValueChange={(value) => handleInputChange('stato', value)}>
                  <SelectTrigger className="focus:ring-orange-500">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="APERTA">🔴 Aperta</SelectItem>
                    <SelectItem value="IN_RISOLUZIONE">🟡 In Risoluzione</SelectItem>
                    <SelectItem value="RISOLTA">🟢 Risolta</SelectItem>
                    <SelectItem value="CHIUSA">⚫ Chiusa</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="descrizione" className="text-sm font-medium">Descrizione *</Label>
              <Textarea
                id="descrizione"
                value={formData.descrizione}
                onChange={(e) => handleInputChange('descrizione', e.target.value)}
                className={`transition-colors min-h-[100px] ${validationErrors.descrizione ? 'border-red-500 focus:ring-red-500' : 'focus:ring-orange-500'}`}
                placeholder="Descrivi dettagliatamente la non conformità rilevata..."
                rows={4}
              />
              {validationErrors.descrizione && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {validationErrors.descrizione}
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Responsabili e Date */}
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="bg-gradient-to-r from-blue-50 to-transparent">
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-blue-600" />
              Responsabili e Date
            </CardTitle>
            <CardDescription>Informazioni su rilevazione e risoluzione</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="id_responsabile_rilevazione" className="text-sm font-medium">Responsabile Rilevazione *</Label>
                <Select
                  value={formData.id_responsabile_rilevazione.toString()}
                  onValueChange={(value) => handleInputChange('id_responsabile_rilevazione', parseInt(value))}
                >
                  <SelectTrigger className={`transition-colors ${validationErrors.id_responsabile_rilevazione ? 'border-red-500 focus:ring-red-500' : 'focus:ring-blue-500'}`}>
                    <SelectValue placeholder="Seleziona responsabile" />
                  </SelectTrigger>
                  <SelectContent>
                    {responsabili.map((responsabile) => (
                      <SelectItem key={responsabile.id_responsabile} value={responsabile.id_responsabile.toString()}>
                        {responsabile.nome_responsabile}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {validationErrors.id_responsabile_rilevazione && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {validationErrors.id_responsabile_rilevazione}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="data_risoluzione" className="text-sm font-medium">Data Risoluzione</Label>
                <Input
                  id="data_risoluzione"
                  type="date"
                  value={formData.data_risoluzione || ''}
                  onChange={(e) => handleInputChange('data_risoluzione', e.target.value || null)}
                  className="focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="note_risoluzione" className="text-sm font-medium">Note Risoluzione</Label>
              <Textarea
                id="note_risoluzione"
                value={formData.note_risoluzione}
                onChange={(e) => handleInputChange('note_risoluzione', e.target.value)}
                className="transition-colors min-h-[80px] focus:ring-blue-500"
                placeholder="Descrivi le azioni intraprese per risolvere la non conformità..."
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Error Display */}
        {error && (
          <Alert variant="destructive" className="border-l-4 border-l-red-500">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="font-medium">{error}</AlertDescription>
          </Alert>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 pt-6 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
            className="px-6"
          >
            <X className="h-4 w-4 mr-2" />
            Annulla
          </Button>
          
          <Button
            type="submit"
            disabled={isLoading}
            className="px-6 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Salvataggio...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                {isEdit ? 'Aggiorna' : 'Crea'} Non Conformità
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}
