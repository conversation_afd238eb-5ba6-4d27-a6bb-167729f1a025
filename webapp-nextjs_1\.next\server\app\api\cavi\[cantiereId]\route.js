(()=>{var e={};e.id=6264,e.ids=[6264],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},90910:(e,t,a)=>{"use strict";a.r(t),a.d(t,{patchFetch:()=>y,routeModule:()=>l,serverHooks:()=>v,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>d});var r={};a.r(r),a.d(r,{GET:()=>c,POST:()=>p});var s=a(96559),o=a(48088),n=a(37719),i=a(32190);async function c(e,{params:t}){try{let{cantiereId:a}=await t,r=e.headers.get("authorization");if(!r||!r.startsWith("Bearer "))return i.NextResponse.json({detail:"Token di autorizzazione mancante"},{status:401});let{searchParams:s}=new URL(e.url),o=new URLSearchParams;s.forEach((e,t)=>{o.append(t,e)});let n=o.toString(),c=`http://localhost:8001/api/cavi/${a}${n?`?${n}`:""}`;console.log("\uD83D\uDD04 Cavi API: Proxying request to backend:",c);let p=await fetch(c,{method:"GET",headers:{"Content-Type":"application/json",Authorization:r}});if(console.log("\uD83D\uDCE1 Cavi API: Backend response status:",p.status),!p.ok){let e=await p.json().catch(()=>({detail:"Errore sconosciuto"}));return console.error("❌ Cavi API: Backend error:",e),i.NextResponse.json(e,{status:p.status})}let l=await p.json();console.log("\uD83D\uDCE1 Cavi API: Backend response data:",l),console.log("\uD83D\uDCE1 Cavi API: Data type:",typeof l),console.log("\uD83D\uDCE1 Cavi API: Is array:",Array.isArray(l));let u=l;return l&&"object"==typeof l&&!Array.isArray(l)&&(l.cavi&&Array.isArray(l.cavi)?u=l.cavi:l.data&&Array.isArray(l.data)?u=l.data:l.items&&Array.isArray(l.items)&&(u=l.items)),console.log("\uD83D\uDCE1 Cavi API: Final cavi data:",u),console.log("\uD83D\uDCE1 Cavi API: Final data type:",typeof u),console.log("\uD83D\uDCE1 Cavi API: Final is array:",Array.isArray(u)),i.NextResponse.json(u,{status:p.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("❌ Cavi API: Error:",e),i.NextResponse.json({detail:"Errore interno del server"},{status:500})}}async function p(e,{params:t}){try{let{cantiereId:a}=await t,r=await e.json(),s=e.headers.get("authorization");if(!s||!s.startsWith("Bearer "))return i.NextResponse.json({detail:"Token di autorizzazione mancante"},{status:401});let o="http://localhost:8001";console.log("\uD83D\uDD04 Cavi API: Proxying POST request to backend:",`${o}/api/cavi/${a}`);let n=await fetch(`${o}/api/cavi/${a}`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:s},body:JSON.stringify(r)});if(console.log("\uD83D\uDCE1 Cavi API: Backend response status:",n.status),!n.ok){let e=await n.json().catch(()=>({detail:"Errore sconosciuto"}));return console.error("❌ Cavi API: Backend error:",e),i.NextResponse.json(e,{status:n.status})}let c=await n.json();return console.log("\uD83D\uDCE1 Cavi API: Backend response data:",c),i.NextResponse.json(c,{status:n.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("❌ Cavi API: POST Error:",e),i.NextResponse.json({detail:"Errore interno del server"},{status:500})}}let l=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/cavi/[cantiereId]/route",pathname:"/api/cavi/[cantiereId]",filename:"route",bundlePath:"app/api/cavi/[cantiereId]/route"},resolvedPagePath:"C:\\CMS\\webapp-nextjs_1\\src\\app\\api\\cavi\\[cantiereId]\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:u,workUnitAsyncStorage:d,serverHooks:v}=l;function y(){return(0,n.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:d})}},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,580],()=>a(90910));module.exports=r})();