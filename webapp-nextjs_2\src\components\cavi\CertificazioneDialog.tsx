'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle, Award, FileText, Download, CheckCircle, X } from 'lucide-react'
import { Cavo } from '@/types'
import { certificazioniApi, responsabiliApi, caviApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'
import { useToast } from '@/hooks/use-toast'

interface CertificazioneDialogProps {
  open: boolean
  onClose: () => void
  cavo: Cavo | null
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

interface Responsabile {
  id: number
  nome_responsabile: string
  numero_telefono?: string
  mail?: string
}

export default function CertificazioneDialog({
  open,
  onClose,
  cavo,
  onSuccess,
  onError
}: CertificazioneDialogProps) {
  const { cantiere } = useAuth()
  const { toast } = useToast()
  const [formData, setFormData] = useState({
    responsabile_certificazione: '',
    data_certificazione: new Date().toISOString().split('T')[0],
    esito_certificazione: 'CONFORME',
    note_certificazione: ''
  })
  const [responsabili, setResponsabili] = useState<Responsabile[]>([])
  const [loading, setLoading] = useState(false)
  const [loadingResponsabili, setLoadingResponsabili] = useState(false)
  const [error, setError] = useState('')
  const [screenReaderAnnouncement, setScreenReaderAnnouncement] = useState('')

  // Funzione per annunci screen reader
  const announceToScreenReader = (message: string) => {
    setScreenReaderAnnouncement(message)
    setTimeout(() => setScreenReaderAnnouncement(''), 1000)
  }

  // Reset form quando si apre il dialog
  useEffect(() => {
    if (open && cavo) {
      setFormData({
        responsabile_certificazione: '',
        data_certificazione: new Date().toISOString().split('T')[0],
        esito_certificazione: 'CONFORME',
        note_certificazione: ''
      })
      setError('')
      loadResponsabili()
    }
  }, [open, cavo])

  const loadResponsabili = async () => {
    if (!cantiere) return

    try {
      setLoadingResponsabili(true)
      const response = await responsabiliApi.getResponsabili(cantiere.id_cantiere)
      setResponsabili(response.data)
    } catch (error) {
      setResponsabili([])
    } finally {
      setLoadingResponsabili(false)
    }
  }

  const isCavoCollegato = () => {
    if (!cavo) return false
    const collegamento = cavo.collegamento || cavo.collegamenti || 0
    return collegamento === 3 // Completamente collegato
  }

  const isCavoCertificato = () => {
    if (!cavo) return false
    return cavo.certificato === true || cavo.certificato === 'SI' || cavo.certificato === 'CERTIFICATO'
  }

  // Funzione per collegare automaticamente il cavo durante la certificazione
  const autoCollegaCavo = async () => {
    if (!cavo || !cantiere) return false

    try {
      announceToScreenReader('Collegamento automatico in corso...')
      
      // Collega entrambi i lati a "cantiere"
      await caviApi.collegaCavo(
        cantiere.id_cantiere,
        cavo.id_cavo,
        'entrambi',
        'cantiere'
      )

      announceToScreenReader('Cavo collegato automaticamente')
      return true
    } catch (error) {
      console.error('Errore nel collegamento automatico:', error)
      return false
    }
  }

  const handleCertifica = async () => {
    if (!cavo || !cantiere) return

    if (!formData.responsabile_certificazione) {
      setError('Seleziona un responsabile per la certificazione')
      announceToScreenReader('Errore: Seleziona un responsabile per la certificazione')
      return
    }

    try {
      setLoading(true)
      setError('')
      announceToScreenReader('Certificazione in corso...')

      // Se il cavo non è collegato, prova a collegarlo automaticamente
      const isCollegato = isCavoCollegato()
      if (!isCollegato) {
        announceToScreenReader('Collegamento automatico del cavo...')
        const collegamentoRiuscito = await autoCollegaCavo()
        if (!collegamentoRiuscito) {
          setError('Impossibile collegare automaticamente il cavo. Collegalo manualmente prima di certificare.')
          announceToScreenReader('Errore: Impossibile collegare automaticamente il cavo')
          return
        }
      }

      const certificazioneData = {
        id_cavo: cavo.id_cavo,
        responsabile_certificazione: formData.responsabile_certificazione,
        data_certificazione: formData.data_certificazione,
        esito_certificazione: formData.esito_certificazione,
        note_certificazione: formData.note_certificazione || null
      }

      await certificazioniApi.createCertificazione(cantiere.id_cantiere, certificazioneData)

      const message = `Certificazione completata per il cavo ${cavo.id_cavo}`
      announceToScreenReader(message)
      toast({
        title: "Successo",
        description: message,
      })
      
      if (onSuccess) onSuccess(message)
      onClose()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la certificazione'
      setError(errorMessage)
      announceToScreenReader(`Errore: ${errorMessage}`)
      if (onError) onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleGeneraPDF = async () => {
    if (!cavo || !cantiere) return

    try {
      setLoading(true)
      setError('')
      announceToScreenReader('Generazione PDF in corso...')

      const response = await certificazioniApi.generatePDF(cantiere.id_cantiere, cavo.id_cavo)
      
      // Crea un link per il download
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `certificato_${cavo.id_cavo}.pdf`)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)

      const message = `PDF certificato generato per il cavo ${cavo.id_cavo}`
      announceToScreenReader(message)
      toast({
        title: "Successo",
        description: message,
      })
      
      if (onSuccess) onSuccess(message)
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la generazione del PDF'
      setError(errorMessage)
      announceToScreenReader(`Errore: ${errorMessage}`)
      if (onError) onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  if (!cavo) return null

  // Controlli di validazione allineati con webapp originale CEI 64-8
  const isInstalled = cavo.stato_installazione === 'Installato' ||
                     cavo.stato_installazione === 'INSTALLATO' ||
                     cavo.stato_installazione === 'POSATO' ||
                     (cavo.metri_posati || cavo.metratura_reale || 0) > 0

  const isCollegato = isCavoCollegato()
  const hasResponsabili = cavo.responsabile_partenza && cavo.responsabile_arrivo
  const isCompletelyConnected = isCollegato && hasResponsabili
  const isCertificato = isCavoCertificato()

  // Per la certificazione CEI 64-8 basta che sia posato (il collegamento può essere gestito durante la certificazione)
  const puoEssereCertificato = isInstalled

  return (
    <Dialog open={open} onOpenChange={onClose}>
      {/* Screen reader announcements */}
      <div 
        aria-live="polite" 
        aria-atomic="true" 
        className="sr-only"
      >
        {screenReaderAnnouncement}
      </div>
      
      <DialogContent 
        className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto"
        aria-describedby="certificazione-description"
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-blue-600">
            <Award className="h-5 w-5" />
            Gestione Certificazione - {cavo.id_cavo}
          </DialogTitle>
          <DialogDescription id="certificazione-description">
            Certifica il cavo {cavo.id_cavo} secondo normativa CEI 64-8 o genera il PDF del certificato
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Informazioni cavo */}
          <div className="p-4 bg-blue-50 rounded-lg border-2 border-blue-200">
            <div className="text-sm font-medium text-blue-800">
              Informazioni Cavo / Tipologia: {cavo.tipologia || 'N/A'} / Da: {cavo.ubicazione_partenza || 'N/A'} / Formazione: {cavo.sezione || 'N/A'} / A: {cavo.ubicazione_arrivo || 'N/A'} / Metri Posati: {cavo.metratura_reale || 0} m
            </div>
          </div>

          {/* Stato attuale con icone migliorate */}
          <div className="p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border">
            <Label className="text-sm font-medium text-gray-700">Stato Cavo</Label>
            <div className="mt-2 space-y-2">
              <div className="flex items-center gap-2">
                {isInstalled ? (
                  <CheckCircle className="w-4 h-4 text-green-600" />
                ) : (
                  <AlertCircle className="w-4 h-4 text-red-500" />
                )}
                <span className="text-sm font-medium">
                  {isInstalled ? 'Installato/Posato' : 'Non installato'}
                </span>
              </div>
              <div className="flex items-center gap-2">
                {isCollegato ? (
                  <CheckCircle className="w-4 h-4 text-green-600" />
                ) : (
                  <AlertCircle className="w-4 h-4 text-orange-500" />
                )}
                <span className="text-sm font-medium">
                  {isCollegato ? 'Collegato' : 'Non collegato'}
                </span>
              </div>
              <div className="flex items-center gap-2">
                {isCertificato ? (
                  <CheckCircle className="w-4 h-4 text-green-600" />
                ) : (
                  <AlertCircle className="w-4 h-4 text-gray-400" />
                )}
                <span className="text-sm font-medium">
                  {isCertificato ? 'Certificato' : 'Non certificato'}
                </span>
              </div>
            </div>
          </div>

          {/* Alert di validazione */}
          {!puoEssereCertificato && (
            <Alert className="border-red-200 bg-red-50">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                <strong>ATTENZIONE:</strong> Il cavo deve essere installato/posato prima di poter essere certificato secondo CEI 64-8.
              </AlertDescription>
            </Alert>
          )}

          {puoEssereCertificato && !isCompletelyConnected && (
            <Alert className="border-amber-200 bg-amber-50">
              <AlertCircle className="h-4 w-4 text-amber-600" />
              <AlertDescription className="text-amber-800">
                <strong>ATTENZIONE:</strong> Il cavo non risulta completamente collegato.
                Durante la certificazione sarà possibile collegarlo automaticamente a "cantiere" su entrambi i lati.
              </AlertDescription>
            </Alert>
          )}

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {isCertificato ? (
            // Cavo già certificato - mostra opzione per generare PDF
            <div className="space-y-4">
              <Alert className="border-green-200 bg-green-50">
                <Award className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-800">
                  <strong>CERTIFICATO:</strong> Questo cavo è già stato certificato. Puoi generare il PDF del certificato.
                </AlertDescription>
              </Alert>

              <Button
                onClick={handleGeneraPDF}
                disabled={loading}
                className="w-full bg-green-600 hover:bg-green-700 text-white"
                size="lg"
              >
                {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Download className="h-4 w-4 mr-2" />}
                Genera PDF Certificato
              </Button>
            </div>
          ) : (
            // Cavo non certificato - mostra form per certificazione (CEI 64-8: basta che sia posato)
            puoEssereCertificato && (
              <div className="space-y-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border-2 border-blue-200">
                <div className="text-sm font-medium text-blue-800 mb-4">
                  📋 Dati Certificazione CEI 64-8
                </div>

                {/* Responsabile */}
                <div className="space-y-2">
                  <Label htmlFor="responsabile" className="text-sm font-medium text-gray-700">
                    Responsabile Certificazione *
                  </Label>
                  <Select
                    value={formData.responsabile_certificazione}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, responsabile_certificazione: value }))}
                    disabled={loadingResponsabili}
                  >
                    <SelectTrigger className="border-2 border-gray-300 focus:border-blue-500">
                      <SelectValue placeholder="Seleziona responsabile..." />
                    </SelectTrigger>
                    <SelectContent>
                      {responsabili.map((resp) => (
                        <SelectItem key={resp.id} value={resp.nome_responsabile}>
                          {resp.nome_responsabile}
                          {resp.numero_telefono && ` - ${resp.numero_telefono}`}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Data certificazione */}
                <div className="space-y-2">
                  <Label htmlFor="data" className="text-sm font-medium text-gray-700">
                    Data Certificazione
                  </Label>
                  <Input
                    id="data"
                    type="date"
                    value={formData.data_certificazione}
                    onChange={(e) => setFormData(prev => ({ ...prev, data_certificazione: e.target.value }))}
                    className="border-2 border-gray-300 focus:border-blue-500"
                  />
                </div>

                {/* Esito */}
                <div className="space-y-2">
                  <Label htmlFor="esito" className="text-sm font-medium text-gray-700">
                    Esito Certificazione
                  </Label>
                  <Select
                    value={formData.esito_certificazione}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, esito_certificazione: value }))}
                  >
                    <SelectTrigger className="border-2 border-gray-300 focus:border-blue-500">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="CONFORME">✅ CONFORME</SelectItem>
                      <SelectItem value="NON_CONFORME">❌ NON CONFORME</SelectItem>
                      <SelectItem value="CONFORME_CON_RISERVA">⚠️ CONFORME CON RISERVA</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Note */}
                <div className="space-y-2">
                  <Label htmlFor="note" className="text-sm font-medium text-gray-700">
                    Note (opzionale)
                  </Label>
                  <Textarea
                    id="note"
                    placeholder="Inserisci eventuali note sulla certificazione..."
                    value={formData.note_certificazione}
                    onChange={(e) => setFormData(prev => ({ ...prev, note_certificazione: e.target.value }))}
                    rows={3}
                    className="border-2 border-gray-300 focus:border-blue-500"
                  />
                </div>

                <Button
                  onClick={handleCertifica}
                  disabled={loading || !formData.responsabile_certificazione}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3"
                  size="lg"
                >
                  {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Award className="h-4 w-4 mr-2" />}
                  Certifica Cavo CEI 64-8
                </Button>
              </div>
            )
          )}
        </div>

        <DialogFooter className="border-t pt-4">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={loading}
            className="flex items-center gap-2"
          >
            <X className="h-4 w-4" />
            Chiudi
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
