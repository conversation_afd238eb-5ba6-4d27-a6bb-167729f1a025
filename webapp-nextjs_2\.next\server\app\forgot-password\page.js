(()=>{var e={};e.id=162,e.ids=[162],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23562:(e,r,s)=>{"use strict";s.d(r,{k:()=>n});var t=s(60687);s(43210);var a=s(25177),i=s(4780);function n({className:e,value:r,...s}){return(0,t.jsx)(a.bL,{"data-slot":"progress",className:(0,i.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...s,children:(0,t.jsx)(a.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(r||0)}%)`}})})}},24782:(e,r,s)=>{Promise.resolve().then(s.bind(s,36200))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36200:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs_2\\\\src\\\\app\\\\forgot-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_2\\src\\app\\forgot-password\\page.tsx","default")},41550:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},44493:(e,r,s)=>{"use strict";s.d(r,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>i,aR:()=>n});var t=s(60687);s(43210);var a=s(4780);function i({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function n({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function l({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...r})}function o({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...r})}function d({className:e,...r}){return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...r})}},48735:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>j});var t=s(60687),a=s(43210),i=s(85814),n=s.n(i),l=s(23361),o=s(28559),d=s(29523),c=s(89667),u=s(80013),p=s(44493),x=s(91821);s(23562);var m=s(41550),h=s(5336),f=s(43649),g=s(99891),b=s(4780);function v(){let[e,r]=(0,a.useState)({email:"",userType:"user"}),[s,i]=(0,a.useState)(!1),[n,l]=(0,a.useState)(null),o=async s=>{s.preventDefault(),i(!0),l(null);try{if(!e.email)throw Error("L'indirizzo email \xe8 obbligatorio");let s=await fetch("/api/password/request-password-reset",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e.email,user_type:e.userType})}),t=await s.json();if(s.ok&&t.success)l({type:"success",text:"Se l'email \xe8 registrata, riceverai le istruzioni per il reset della password."}),r({email:"",userType:"user"});else throw Error(t.detail||t.message||"Errore durante la richiesta di reset")}catch(e){l({type:"error",text:e instanceof Error?e.message:"Errore durante la richiesta di reset"})}finally{i(!1)}};return(0,t.jsxs)(p.Zp,{className:"w-full max-w-md mx-auto",children:[(0,t.jsxs)(p.aR,{className:"space-y-1",children:[(0,t.jsxs)(p.ZB,{className:"text-2xl flex items-center gap-2",children:[(0,t.jsx)(m.A,{className:"h-5 w-5 text-mariner-600"}),"Recupera Password"]}),(0,t.jsx)(p.BT,{children:"Inserisci la tua email per ricevere le istruzioni di reset"})]}),(0,t.jsxs)(p.Wu,{children:[(0,t.jsxs)("form",{onSubmit:o,className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{children:"Tipo di Account"}),(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,t.jsx)("input",{type:"radio",value:"user",checked:"user"===e.userType,onChange:e=>r(r=>({...r,userType:e.target.value})),className:"text-mariner-600"}),(0,t.jsx)("span",{children:"Utente"})]}),(0,t.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,t.jsx)("input",{type:"radio",value:"cantiere",checked:"cantiere"===e.userType,onChange:e=>r(r=>({...r,userType:e.target.value})),className:"text-mariner-600"}),(0,t.jsx)("span",{children:"Cantiere"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(u.J,{htmlFor:"email",children:"Indirizzo Email"}),(0,t.jsx)(c.p,{id:"email",type:"email",value:e.email,onChange:e=>r(r=>({...r,email:e.target.value})),placeholder:"<EMAIL>",required:!0})]}),n&&(0,t.jsxs)(x.Fc,{className:(0,b.cn)("success"===n.type?"border-green-200 bg-green-50":"border-red-200 bg-red-50"),children:["success"===n.type?(0,t.jsx)(h.A,{className:"h-4 w-4 text-green-600"}):(0,t.jsx)(f.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)(x.TN,{className:(0,b.cn)("success"===n.type?"text-green-800":"text-red-800"),children:n.text})]}),(0,t.jsx)(d.$,{type:"submit",className:"w-full bg-mariner-600 hover:bg-mariner-700",disabled:s,children:s?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Invio in corso..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Invia Link di Reset"]})})]}),(0,t.jsx)("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-start gap-2",children:[(0,t.jsx)(g.A,{className:"h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,t.jsx)("p",{className:"font-medium mb-1",children:"Informazioni sulla Sicurezza"}),(0,t.jsxs)("ul",{className:"space-y-1 text-xs",children:[(0,t.jsx)("li",{children:"• Il link di reset \xe8 valido per 30 minuti"}),(0,t.jsx)("li",{children:"• Pu\xf2 essere utilizzato una sola volta"}),(0,t.jsx)("li",{children:"• Se non ricevi l'email, controlla la cartella spam"}),(0,t.jsx)("li",{children:"• Per motivi di sicurezza, non riveleremo se l'email \xe8 registrata"})]})]})]})})]})]})}function j(){return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-slate-100 flex items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"w-full max-w-md space-y-6",children:[(0,t.jsxs)("div",{className:"text-center space-y-2",children:[(0,t.jsx)("div",{className:"flex justify-center",children:(0,t.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center",children:(0,t.jsx)(l.A,{className:"w-8 h-8 text-white"})})}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-slate-900",children:"CABLYS"}),(0,t.jsx)("p",{className:"text-slate-600",children:"Recupero Password"})]}),(0,t.jsx)(v,{}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)(n(),{href:"/login",children:(0,t.jsxs)(d.$,{variant:"ghost",className:"text-slate-600 hover:text-slate-900",children:[(0,t.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"Torna al Login"]})})})]})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57743:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var t=s(65239),a=s(48088),i=s(88170),n=s.n(i),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(r,o);let d={children:["",{children:["forgot-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,36200)),"C:\\CMS\\webapp-nextjs_2\\src\\app\\forgot-password\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\CMS\\webapp-nextjs_2\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\CMS\\webapp-nextjs_2\\src\\app\\forgot-password\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/forgot-password/page",pathname:"/forgot-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64534:(e,r,s)=>{Promise.resolve().then(s.bind(s,48735))},70440:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80013:(e,r,s)=>{"use strict";s.d(r,{J:()=>n});var t=s(60687);s(43210);var a=s(78148),i=s(4780);function n({className:e,...r}){return(0,t.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...r})}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},89667:(e,r,s)=>{"use strict";s.d(r,{p:()=>i});var t=s(60687);s(43210);var a=s(4780);function i({className:e,type:r,...s}){return(0,t.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...s})}},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,588,658,371,415],()=>s(57743));module.exports=t})();