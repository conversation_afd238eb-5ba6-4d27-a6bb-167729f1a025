"use strict";exports.id=251,exports.ids=[251],exports.modules={26134:(e,t,r)=>{r.d(t,{G$:()=>V,Hs:()=>j,UC:()=>er,VY:()=>eo,ZL:()=>ee,bL:()=>Y,bm:()=>ea,hE:()=>en,hJ:()=>et,l9:()=>Q});var n=r(43210),o=r(70569),a=r(98599),i=r(11273),s=r(96963),l=r(65551),d=r(31355),c=r(32547),u=r(25028),p=r(46059),f=r(14163),g=r(1359),m=r(42247),h=r(63376),x=r(8730),y=r(60687),v="Dialog",[b,j]=(0,i.A)(v),[C,D]=b(v),R=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:i,modal:d=!0}=e,c=n.useRef(null),u=n.useRef(null),[p,f]=(0,l.i)({prop:o,defaultProp:a??!1,onChange:i,caller:v});return(0,y.jsx)(C,{scope:t,triggerRef:c,contentRef:u,contentId:(0,s.B)(),titleId:(0,s.B)(),descriptionId:(0,s.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};R.displayName=v;var k="DialogTrigger",w=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=D(k,r),s=(0,a.s)(t,i.triggerRef);return(0,y.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":K(i.open),...n,ref:s,onClick:(0,o.m)(e.onClick,i.onOpenToggle)})});w.displayName=k;var E="DialogPortal",[I,N]=b(E,{forceMount:void 0}),O=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,i=D(E,t);return(0,y.jsx)(I,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,y.jsx)(p.C,{present:r||i.open,children:(0,y.jsx)(u.Z,{asChild:!0,container:a,children:e})}))})};O.displayName=E;var _="DialogOverlay",A=n.forwardRef((e,t)=>{let r=N(_,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=D(_,e.__scopeDialog);return a.modal?(0,y.jsx)(p.C,{present:n||a.open,children:(0,y.jsx)(P,{...o,ref:t})}):null});A.displayName=_;var F=(0,x.TL)("DialogOverlay.RemoveScroll"),P=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D(_,r);return(0,y.jsx)(m.A,{as:F,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(f.sG.div,{"data-state":K(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),G="DialogContent",L=n.forwardRef((e,t)=>{let r=N(G,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=D(G,e.__scopeDialog);return(0,y.jsx)(p.C,{present:n||a.open,children:a.modal?(0,y.jsx)(T,{...o,ref:t}):(0,y.jsx)(q,{...o,ref:t})})});L.displayName=G;var T=n.forwardRef((e,t)=>{let r=D(G,e.__scopeDialog),i=n.useRef(null),s=(0,a.s)(t,r.contentRef,i);return n.useEffect(()=>{let e=i.current;if(e)return(0,h.Eq)(e)},[]),(0,y.jsx)(B,{...e,ref:s,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),q=n.forwardRef((e,t)=>{let r=D(G,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,y.jsx)(B,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||r.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let n=t.target;r.triggerRef.current?.contains(n)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),B=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:s,...l}=e,u=D(G,r),p=n.useRef(null),f=(0,a.s)(t,p);return(0,g.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:s,children:(0,y.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":K(u.open),...l,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(z,{titleId:u.titleId}),(0,y.jsx)(J,{contentRef:p,descriptionId:u.descriptionId})]})]})}),M="DialogTitle",S=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D(M,r);return(0,y.jsx)(f.sG.h2,{id:o.titleId,...n,ref:t})});S.displayName=M;var $="DialogDescription",W=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D($,r);return(0,y.jsx)(f.sG.p,{id:o.descriptionId,...n,ref:t})});W.displayName=$;var Z="DialogClose",H=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=D(Z,r);return(0,y.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function K(e){return e?"open":"closed"}H.displayName=Z;var U="DialogTitleWarning",[V,X]=(0,i.q)(U,{contentName:G,titleName:M,docsSlug:"dialog"}),z=({titleId:e})=>{let t=X(U),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return n.useEffect(()=>{e&&(document.getElementById(e)||console.error(r))},[r,e]),null},J=({contentRef:e,descriptionId:t})=>{let r=X("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return n.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&(document.getElementById(t)||console.warn(o))},[o,e,t]),null},Y=R,Q=w,ee=O,et=A,er=L,en=S,eo=W,ea=H},40211:(e,t,r)=>{r.d(t,{C1:()=>D,bL:()=>j});var n=r(43210),o=r(98599),a=r(11273),i=r(70569),s=r(65551),l=r(83721),d=r(18853),c=r(46059),u=r(14163),p=r(60687),f="Checkbox",[g,m]=(0,a.A)(f),[h,x]=g(f);function y(e){let{__scopeCheckbox:t,checked:r,children:o,defaultChecked:a,disabled:i,form:l,name:d,onCheckedChange:c,required:u,value:g="on",internal_do_not_use_render:m}=e,[x,y]=(0,s.i)({prop:r,defaultProp:a??!1,onChange:c,caller:f}),[v,b]=n.useState(null),[j,C]=n.useState(null),D=n.useRef(!1),R=!v||!!l||!!v.closest("form"),k={checked:x,disabled:i,setChecked:y,control:v,setControl:b,name:d,form:l,value:g,hasConsumerStoppedPropagationRef:D,required:u,defaultChecked:!w(a)&&a,isFormControl:R,bubbleInput:j,setBubbleInput:C};return(0,p.jsx)(h,{scope:t,...k,children:"function"==typeof m?m(k):o})}var v="CheckboxTrigger",b=n.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:r,...a},s)=>{let{control:l,value:d,disabled:c,checked:f,required:g,setControl:m,setChecked:h,hasConsumerStoppedPropagationRef:y,isFormControl:b,bubbleInput:j}=x(v,e),C=(0,o.s)(s,m),D=n.useRef(f);return n.useEffect(()=>{let e=l?.form;if(e){let t=()=>h(D.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[l,h]),(0,p.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":w(f)?"mixed":f,"aria-required":g,"data-state":E(f),"data-disabled":c?"":void 0,disabled:c,value:d,...a,ref:C,onKeyDown:(0,i.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.m)(r,e=>{h(e=>!!w(e)||!e),j&&b&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})})});b.displayName=v;var j=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:o,defaultChecked:a,required:i,disabled:s,value:l,onCheckedChange:d,form:c,...u}=e;return(0,p.jsx)(y,{__scopeCheckbox:r,checked:o,defaultChecked:a,disabled:s,required:i,onCheckedChange:d,name:n,form:c,value:l,internal_do_not_use_render:({isFormControl:e})=>(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(b,{...u,ref:t,__scopeCheckbox:r}),e&&(0,p.jsx)(k,{__scopeCheckbox:r})]})})});j.displayName=f;var C="CheckboxIndicator",D=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...o}=e,a=x(C,r);return(0,p.jsx)(c.C,{present:n||w(a.checked)||!0===a.checked,children:(0,p.jsx)(u.sG.span,{"data-state":E(a.checked),"data-disabled":a.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});D.displayName=C;var R="CheckboxBubbleInput",k=n.forwardRef(({__scopeCheckbox:e,...t},r)=>{let{control:a,hasConsumerStoppedPropagationRef:i,checked:s,defaultChecked:c,required:f,disabled:g,name:m,value:h,form:y,bubbleInput:v,setBubbleInput:b}=x(R,e),j=(0,o.s)(r,b),C=(0,l.Z)(s),D=(0,d.X)(a);n.useEffect(()=>{if(!v)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!i.current;if(C!==s&&e){let r=new Event("click",{bubbles:t});v.indeterminate=w(s),e.call(v,!w(s)&&s),v.dispatchEvent(r)}},[v,C,s,i]);let k=n.useRef(!w(s)&&s);return(0,p.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:c??k.current,required:f,disabled:g,name:m,value:h,form:y,...t,tabIndex:-1,ref:j,style:{...t.style,...D,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function w(e){return"indeterminate"===e}function E(e){return w(e)?"indeterminate":e?"checked":"unchecked"}k.displayName=R},41550:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},99270:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};