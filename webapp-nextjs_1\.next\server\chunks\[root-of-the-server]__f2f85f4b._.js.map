{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_1/src/app/api/cavi/%5BcantiereId%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\r\n\r\nexport async function GET(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ cantiereId: string }> }\r\n) {\r\n  try {\r\n    const { cantiereId } = await params\r\n\r\n    // Estrai il token di autorizzazione dall'header\r\n    const authHeader = request.headers.get('authorization')\r\n\r\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\r\n      return NextResponse.json(\r\n        {\r\n          detail: 'Token di autorizzazione mancante'\r\n        },\r\n        { status: 401 }\r\n      )\r\n    }\r\n\r\n    // Estrai i parametri di query\r\n    const { searchParams } = new URL(request.url)\r\n    const queryParams = new URLSearchParams()\r\n\r\n    // Passa tutti i parametri al backend\r\n    searchParams.forEach((value, key) => {\r\n      queryParams.append(key, value)\r\n    })\r\n\r\n    // Proxy la richiesta al backend FastAPI\r\n    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'\r\n    const queryString = queryParams.toString()\r\n    const url = `${backendUrl}/api/cavi/${cantiereId}${queryString ? `?${queryString}` : ''}`\r\n\r\n    console.log('🔄 Cavi API: Proxying request to backend:', url)\r\n\r\n    const response = await fetch(url, {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': authHeader\r\n      }\r\n    })\r\n\r\n    console.log('📡 Cavi API: Backend response status:', response.status)\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => ({ detail: 'Errore sconosciuto' }))\r\n      console.error('❌ Cavi API: Backend error:', errorData)\r\n      return NextResponse.json(errorData, {\r\n        status: response.status\r\n      })\r\n    }\r\n\r\n    const data = await response.json()\r\n    console.log('📡 Cavi API: Backend response data:', data)\r\n    console.log('📡 Cavi API: Data type:', typeof data)\r\n    console.log('📡 Cavi API: Is array:', Array.isArray(data))\r\n\r\n    // Verifica se i dati sono un array o se sono wrappati in un oggetto\r\n    let caviData = data\r\n    if (data && typeof data === 'object' && !Array.isArray(data)) {\r\n      // Se i dati sono un oggetto, cerca l'array dentro\r\n      if (data.cavi && Array.isArray(data.cavi)) {\r\n        caviData = data.cavi\r\n      } else if (data.data && Array.isArray(data.data)) {\r\n        caviData = data.data\r\n      } else if (data.items && Array.isArray(data.items)) {\r\n        caviData = data.items\r\n      }\r\n    }\r\n\r\n    console.log('📡 Cavi API: Final cavi data:', caviData)\r\n    console.log('📡 Cavi API: Final data type:', typeof caviData)\r\n    console.log('📡 Cavi API: Final is array:', Array.isArray(caviData))\r\n\r\n    return NextResponse.json(caviData, {\r\n      status: response.status,\r\n      headers: {\r\n        'Content-Type': 'application/json'\r\n      }\r\n    })\r\n\r\n  } catch (error) {\r\n    console.error('❌ Cavi API: Error:', error)\r\n    return NextResponse.json(\r\n      {\r\n        detail: 'Errore interno del server'\r\n      },\r\n      { status: 500 }\r\n    )\r\n  }\r\n}\r\n\r\nexport async function POST(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ cantiereId: string }> }\r\n) {\r\n  try {\r\n    const { cantiereId } = await params\r\n    const body = await request.json()\r\n\r\n    // Estrai il token di autorizzazione dall'header\r\n    const authHeader = request.headers.get('authorization')\r\n\r\n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\r\n      return NextResponse.json(\r\n        {\r\n          detail: 'Token di autorizzazione mancante'\r\n        },\r\n        { status: 401 }\r\n      )\r\n    }\r\n\r\n    // Proxy la richiesta al backend FastAPI\r\n    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'\r\n\r\n    console.log('🔄 Cavi API: Proxying POST request to backend:', `${backendUrl}/api/cavi/${cantiereId}`)\r\n\r\n    const response = await fetch(`${backendUrl}/api/cavi/${cantiereId}`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': authHeader\r\n      },\r\n      body: JSON.stringify(body)\r\n    })\r\n\r\n    console.log('📡 Cavi API: Backend response status:', response.status)\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => ({ detail: 'Errore sconosciuto' }))\r\n      console.error('❌ Cavi API: Backend error:', errorData)\r\n      return NextResponse.json(errorData, {\r\n        status: response.status\r\n      })\r\n    }\r\n\r\n    const data = await response.json()\r\n    console.log('📡 Cavi API: Backend response data:', data)\r\n\r\n    return NextResponse.json(data, {\r\n      status: response.status,\r\n      headers: {\r\n        'Content-Type': 'application/json'\r\n      }\r\n    })\r\n\r\n  } catch (error) {\r\n    console.error('❌ Cavi API: POST Error:', error)\r\n    return NextResponse.json(\r\n      {\r\n        detail: 'Errore interno del server'\r\n      },\r\n      { status: 500 }\r\n    )\r\n  }\r\n}"], "names": [], "mappings": ";;;;AAAA;;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA+C;IAEvD,IAAI;QACF,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM;QAE7B,gDAAgD;QAChD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QAEvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,QAAQ;YACV,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,8BAA8B;QAC9B,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,cAAc,IAAI;QAExB,qCAAqC;QACrC,aAAa,OAAO,CAAC,CAAC,OAAO;YAC3B,YAAY,MAAM,CAAC,KAAK;QAC1B;QAEA,wCAAwC;QACxC,MAAM,aAAa,6DAAmC;QACtD,MAAM,cAAc,YAAY,QAAQ;QACxC,MAAM,MAAM,GAAG,WAAW,UAAU,EAAE,aAAa,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAEzF,QAAQ,GAAG,CAAC,6CAA6C;QAEzD,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB;YACnB;QACF;QAEA,QAAQ,GAAG,CAAC,yCAAyC,SAAS,MAAM;QAEpE,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,QAAQ;gBAAqB,CAAC;YACrF,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,WAAW;gBAClC,QAAQ,SAAS,MAAM;YACzB;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,uCAAuC;QACnD,QAAQ,GAAG,CAAC,2BAA2B,OAAO;QAC9C,QAAQ,GAAG,CAAC,0BAA0B,MAAM,OAAO,CAAC;QAEpD,oEAAoE;QACpE,IAAI,WAAW;QACf,IAAI,QAAQ,OAAO,SAAS,YAAY,CAAC,MAAM,OAAO,CAAC,OAAO;YAC5D,kDAAkD;YAClD,IAAI,KAAK,IAAI,IAAI,MAAM,OAAO,CAAC,KAAK,IAAI,GAAG;gBACzC,WAAW,KAAK,IAAI;YACtB,OAAO,IAAI,KAAK,IAAI,IAAI,MAAM,OAAO,CAAC,KAAK,IAAI,GAAG;gBAChD,WAAW,KAAK,IAAI;YACtB,OAAO,IAAI,KAAK,KAAK,IAAI,MAAM,OAAO,CAAC,KAAK,KAAK,GAAG;gBAClD,WAAW,KAAK,KAAK;YACvB;QACF;QAEA,QAAQ,GAAG,CAAC,iCAAiC;QAC7C,QAAQ,GAAG,CAAC,iCAAiC,OAAO;QACpD,QAAQ,GAAG,CAAC,gCAAgC,MAAM,OAAO,CAAC;QAE1D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;YACjC,QAAQ,SAAS,MAAM;YACvB,SAAS;gBACP,gBAAgB;YAClB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,QAAQ;QACV,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KACpB,OAAoB,EACpB,EAAE,MAAM,EAA+C;IAEvD,IAAI;QACF,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM;QAC7B,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,gDAAgD;QAChD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QAEvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,QAAQ;YACV,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,wCAAwC;QACxC,MAAM,aAAa,6DAAmC;QAEtD,QAAQ,GAAG,CAAC,kDAAkD,GAAG,WAAW,UAAU,EAAE,YAAY;QAEpG,MAAM,WAAW,MAAM,MAAM,GAAG,WAAW,UAAU,EAAE,YAAY,EAAE;YACnE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB;YACnB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,QAAQ,GAAG,CAAC,yCAAyC,SAAS,MAAM;QAEpE,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,QAAQ;gBAAqB,CAAC;YACrF,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,WAAW;gBAClC,QAAQ,SAAS,MAAM;YACzB;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,uCAAuC;QAEnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;YAC7B,QAAQ,SAAS,MAAM;YACvB,SAAS;gBACP,gBAAgB;YAClB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,QAAQ;QACV,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}