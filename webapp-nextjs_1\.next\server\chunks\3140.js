"use strict";exports.id=3140,exports.ids=[3140],exports.modules={12207:(e,a,s)=>{s.d(a,{A:()=>j});var t=s(60687),r=s(43210),i=s(44493),n=s(29523),l=s(80013),o=s(89667),d=s(34729),c=s(15079),m=s(91821),x=s(84027),u=s(93613),h=s(11860),p=s(41862),g=s(8819),v=s(62185);function j({cantiereId:e,strumento:a,onSuccess:s,onCancel:j}){let[b,f]=(0,r.useState)({nome:"",marca:"",modello:"",numero_serie:"",data_calibrazione:"",data_scadenza_calibrazione:"",note:"",tipo_strumento:"MEGGER",ente_certificatore:"",numero_certificato_calibrazione:"",range_misura:"",precisione:"",stato_strumento:"ATTIVO"}),[N,_]=(0,r.useState)(!1),[y,z]=(0,r.useState)(""),[C,w]=(0,r.useState)({}),S=!!a,A=()=>{let e={};if(b.nome.trim()||(e.nome="Il nome \xe8 obbligatorio"),b.marca.trim()||(e.marca="La marca \xe8 obbligatoria"),b.modello.trim()||(e.modello="Il modello \xe8 obbligatorio"),b.numero_serie.trim()||(e.numero_serie="Il numero di serie \xe8 obbligatorio"),b.data_calibrazione||(e.data_calibrazione="La data di calibrazione \xe8 obbligatoria"),b.data_scadenza_calibrazione||(e.data_scadenza_calibrazione="La data di scadenza \xe8 obbligatoria"),b.data_calibrazione&&b.data_scadenza_calibrazione){let a=new Date(b.data_calibrazione);new Date(b.data_scadenza_calibrazione)<=a&&(e.data_scadenza_calibrazione="La data di scadenza deve essere successiva alla calibrazione")}return w(e),0===Object.keys(e).length},O=async t=>{if(t.preventDefault(),A())try{_(!0),z(""),S&&a?await v.kw.updateStrumento(e,a.id_strumento,b):await v.kw.createStrumento(e,b),s()}catch(e){z(e.response?.data?.detail||"Errore durante il salvataggio")}finally{_(!1)}},F=(e,a)=>{f(s=>({...s,[e]:a})),C[e]&&w(a=>{let s={...a};return delete s[e],s})},I=e=>{if(F("data_calibrazione",e),e&&!b.data_scadenza_calibrazione){let a=new Date(new Date(e));a.setFullYear(a.getFullYear()+1),F("data_scadenza_calibrazione",a.toISOString().split("T")[0])}};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsxs)("div",{children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold text-slate-900 flex items-center gap-3",children:[(0,t.jsx)(x.A,{className:"h-6 w-6 text-blue-600"}),S?"Modifica Strumento":"Nuovo Strumento"]}),(0,t.jsx)("p",{className:"text-slate-600 mt-1",children:S?"Modifica i dati dello strumento esistente":"Aggiungi un nuovo strumento di misura"})]})}),(0,t.jsxs)("form",{onSubmit:O,className:"space-y-6",children:[(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Informazioni Base"}),(0,t.jsx)(i.BT,{children:"Dati identificativi dello strumento"})]}),(0,t.jsx)(i.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(l.J,{htmlFor:"nome",children:"Nome Strumento *"}),(0,t.jsx)(o.p,{id:"nome",value:b.nome,onChange:e=>F("nome",e.target.value),className:C.nome?"border-red-500":"",placeholder:"es. Megger MFT1741"}),C.nome&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:C.nome})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(l.J,{htmlFor:"tipo_strumento",children:"Tipo Strumento"}),(0,t.jsxs)(c.l6,{value:b.tipo_strumento,onValueChange:e=>F("tipo_strumento",e),children:[(0,t.jsx)(c.bq,{children:(0,t.jsx)(c.yv,{})}),(0,t.jsxs)(c.gC,{children:[(0,t.jsx)(c.eb,{value:"MEGGER",children:"Megger"}),(0,t.jsx)(c.eb,{value:"MULTIMETRO",children:"Multimetro"}),(0,t.jsx)(c.eb,{value:"OSCILLOSCOPIO",children:"Oscilloscopio"}),(0,t.jsx)(c.eb,{value:"ALTRO",children:"Altro"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(l.J,{htmlFor:"marca",children:"Marca *"}),(0,t.jsx)(o.p,{id:"marca",value:b.marca,onChange:e=>F("marca",e.target.value),className:C.marca?"border-red-500":"",placeholder:"es. Fluke, Megger, Keysight"}),C.marca&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:C.marca})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(l.J,{htmlFor:"modello",children:"Modello *"}),(0,t.jsx)(o.p,{id:"modello",value:b.modello,onChange:e=>F("modello",e.target.value),className:C.modello?"border-red-500":"",placeholder:"es. MFT1741, 87V"}),C.modello&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:C.modello})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(l.J,{htmlFor:"numero_serie",children:"Numero Serie *"}),(0,t.jsx)(o.p,{id:"numero_serie",value:b.numero_serie,onChange:e=>F("numero_serie",e.target.value),className:C.numero_serie?"border-red-500":"",placeholder:"Numero di serie univoco"}),C.numero_serie&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:C.numero_serie})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(l.J,{htmlFor:"stato_strumento",children:"Stato"}),(0,t.jsxs)(c.l6,{value:b.stato_strumento,onValueChange:e=>F("stato_strumento",e),children:[(0,t.jsx)(c.bq,{children:(0,t.jsx)(c.yv,{})}),(0,t.jsxs)(c.gC,{children:[(0,t.jsx)(c.eb,{value:"ATTIVO",children:"Attivo"}),(0,t.jsx)(c.eb,{value:"SCADUTO",children:"Scaduto"}),(0,t.jsx)(c.eb,{value:"FUORI_SERVIZIO",children:"Fuori Servizio"})]})]})]})]})})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Calibrazione"}),(0,t.jsx)(i.BT,{children:"Informazioni sulla calibrazione dello strumento"})]}),(0,t.jsx)(i.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(l.J,{htmlFor:"data_calibrazione",children:"Data Calibrazione *"}),(0,t.jsx)(o.p,{id:"data_calibrazione",type:"date",value:b.data_calibrazione,onChange:e=>I(e.target.value),className:C.data_calibrazione?"border-red-500":""}),C.data_calibrazione&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:C.data_calibrazione})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(l.J,{htmlFor:"data_scadenza_calibrazione",children:"Data Scadenza *"}),(0,t.jsx)(o.p,{id:"data_scadenza_calibrazione",type:"date",value:b.data_scadenza_calibrazione,onChange:e=>F("data_scadenza_calibrazione",e.target.value),className:C.data_scadenza_calibrazione?"border-red-500":""}),C.data_scadenza_calibrazione&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:C.data_scadenza_calibrazione})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(l.J,{htmlFor:"ente_certificatore",children:"Ente Certificatore"}),(0,t.jsx)(o.p,{id:"ente_certificatore",value:b.ente_certificatore,onChange:e=>F("ente_certificatore",e.target.value),placeholder:"es. LAT 123, ACCREDIA"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(l.J,{htmlFor:"numero_certificato_calibrazione",children:"Numero Certificato"}),(0,t.jsx)(o.p,{id:"numero_certificato_calibrazione",value:b.numero_certificato_calibrazione,onChange:e=>F("numero_certificato_calibrazione",e.target.value),placeholder:"Numero del certificato di calibrazione"})]})]})})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Specifiche Tecniche"}),(0,t.jsx)(i.BT,{children:"Caratteristiche tecniche dello strumento"})]}),(0,t.jsxs)(i.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(l.J,{htmlFor:"range_misura",children:"Range di Misura"}),(0,t.jsx)(o.p,{id:"range_misura",value:b.range_misura,onChange:e=>F("range_misura",e.target.value),placeholder:"es. 0-1000V, 0-20A"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(l.J,{htmlFor:"precisione",children:"Precisione"}),(0,t.jsx)(o.p,{id:"precisione",value:b.precisione,onChange:e=>F("precisione",e.target.value),placeholder:"es. \xb10.1%, \xb12 digit"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(l.J,{htmlFor:"note",children:"Note"}),(0,t.jsx)(d.T,{id:"note",value:b.note,onChange:e=>F("note",e.target.value),placeholder:"Note aggiuntive sullo strumento...",rows:3})]})]})]}),y&&(0,t.jsxs)(m.Fc,{className:"border-red-200 bg-red-50",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)(m.TN,{className:"text-red-800",children:y})]}),(0,t.jsxs)("div",{className:"flex gap-3 justify-end pt-4 border-t border-gray-200",children:[(0,t.jsxs)(n.$,{variant:"outline",onClick:j,disabled:N,children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Annulla"]}),(0,t.jsxs)(n.$,{onClick:O,disabled:N,children:[N?(0,t.jsx)(p.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),S?"Aggiorna":"Salva"]})]})]})]})}},15079:(e,a,s)=>{s.d(a,{bq:()=>m,eb:()=>u,gC:()=>x,l6:()=>d,yv:()=>c});var t=s(60687);s(43210);var r=s(97822),i=s(78272),n=s(13964),l=s(3589),o=s(4780);function d({...e}){return(0,t.jsx)(r.bL,{"data-slot":"select",...e})}function c({...e}){return(0,t.jsx)(r.WT,{"data-slot":"select-value",...e})}function m({className:e,size:a="default",children:s,...n}){return(0,t.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":a,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[s,(0,t.jsx)(r.In,{asChild:!0,children:(0,t.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function x({className:e,children:a,position:s="popper",...i}){return(0,t.jsx)(r.ZL,{children:(0,t.jsxs)(r.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...i,children:[(0,t.jsx)(h,{}),(0,t.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,t.jsx)(p,{})]})})}function u({className:e,children:a,...s}){return(0,t.jsxs)(r.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,t.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,t.jsx)(r.VF,{children:(0,t.jsx)(n.A,{className:"size-4"})})}),(0,t.jsx)(r.p4,{children:a})]})}function h({className:e,...a}){return(0,t.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,t.jsx)(l.A,{className:"size-4"})})}function p({className:e,...a}){return(0,t.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,t.jsx)(i.A,{className:"size-4"})})}},17680:(e,a,s)=>{s.d(a,{A:()=>N});var t=s(60687),r=s(43210),i=s(29523),n=s(91821),l=s(80013),o=s(89667),d=s(34729),c=s(15079),m=s(63503),x=s(41862),u=s(10022),h=s(11860),p=s(93613),g=s(96474),v=s(84027),j=s(8819),b=s(62185),f=s(12207);function N({cantiereId:e,certificazione:a,strumenti:s,preselectedCavoId:N,onSuccess:_,onCancel:y,onStrumentiUpdate:z}){let[C,w]=(0,r.useState)(!1),{formData:S,cavi:A,responsabili:O,strumenti:F,weatherData:I,selectedCavo:k,isLoading:M,isSaving:T,isLoadingWeather:E,error:R,validationErrors:D,isWeatherOverride:J,isEdit:L,isCavoLocked:V,handleInputChange:Z,handleSubmit:B,setIsWeatherOverride:$,refreshStrumenti:q,onCancel:U}=function({cantiereId:e,certificazione:a,preselectedCavoId:s,onSuccess:t,onCancel:i}){let[n,l]=(0,r.useState)({id_cantiere:e,tipo_certificato:"SINGOLO",stato_certificato:"BOZZA",valore_continuita:"CONFORME",tensione_prova_isolamento:500,temperatura_prova:20,umidita_prova:50}),[o,d]=(0,r.useState)(!0),[c,m]=(0,r.useState)(!1),[x,u]=(0,r.useState)(!1),[h,p]=(0,r.useState)(""),[g,v]=(0,r.useState)({}),[j,f]=(0,r.useState)(!1),[N,_]=(0,r.useState)([]),[y,z]=(0,r.useState)([]),[C,w]=(0,r.useState)([]),[S,A]=(0,r.useState)(null),O=!!a,F=n.id_cavo?N.find(e=>e.id_cavo===n.id_cavo):null;async function I(){if(!function(){let e={};return n.id_cavo||(e.id_cavo="Seleziona un cavo"),(!n.valore_isolamento||n.valore_isolamento<=0)&&(e.valore_isolamento="Inserisci un valore di isolamento valido"),n.valore_continuita||(e.valore_continuita="Seleziona il risultato della continuit\xe0"),n.valore_resistenza||(e.valore_resistenza="Inserisci un valore di resistenza"),v(e),0===Object.keys(e).length}())return void p("Correggi gli errori nel form prima di continuare");try{let s;m(!0),p("");let r=function(){let e={...n};return S&&!j&&(e.temperatura_prova=S.temperature,e.umidita_prova=S.humidity),delete e.id_cantiere,delete e.data_certificazione,delete e.numero_certificato,delete e.id_certificazione,delete e.timestamp_creazione,delete e.timestamp_modifica,console.log("\uD83D\uDCE1 Dati certificazione da inviare:",e),e}();(s=O&&a?await b.km.updateCertificazione(e,a.id_certificazione,r):await b.km.createCertificazione(e,r)).data&&t(s.data)}catch(e){console.error("Errore salvataggio:",e),p(e.response?.data?.detail||"Errore durante il salvataggio")}finally{m(!1)}}return{formData:n,cavi:N,responsabili:y,strumenti:C,weatherData:S,selectedCavo:F,isLoading:o,isSaving:c,isLoadingWeather:x,error:h,validationErrors:g,isWeatherOverride:j,isEdit:O,isCavoLocked:!!s,handleInputChange:function(e,a){l(s=>({...s,[e]:a})),g[e]&&v(a=>{let s={...a};return delete s[e],s})},handleSubmit:I,setIsWeatherOverride:f,refreshStrumenti:async function a(){try{let a=await b.kw.getStrumenti(e);w(a.data||[])}catch(e){console.error("Errore ricaricamento strumenti:",e)}},onCancel:i}}({cantiereId:e,certificazione:a,strumenti:s,preselectedCavoId:N,onSuccess:_,onCancel:y}),G=F.length>0?F:s;return M?(0,t.jsx)("div",{className:"flex items-center justify-center h-96",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(x.A,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Caricamento dati certificazione..."})]})}):(0,t.jsxs)("div",{className:"min-w-[800px] max-w-4xl mx-auto bg-white",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"p-1.5 bg-blue-100 rounded-lg",children:(0,t.jsx)(u.A,{className:"h-4 w-4 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-lg font-semibold text-gray-900",children:L?"Modifica Certificazione":"Nuova Certificazione CEI 64-8"}),N&&(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:["Cavo: ",N]})]})]}),(0,t.jsx)(i.$,{variant:"ghost",size:"sm",onClick:y,className:"text-gray-400 hover:text-gray-600 h-8 w-8 p-0",children:(0,t.jsx)(h.A,{className:"h-4 w-4"})})]}),R&&(0,t.jsx)("div",{className:"p-4 border-b border-red-200 bg-red-50",children:(0,t.jsxs)(n.Fc,{variant:"destructive",className:"border-red-200 bg-red-50 py-2",children:[(0,t.jsx)(p.A,{className:"h-4 w-4"}),(0,t.jsx)(n.TN,{className:"text-sm",children:R})]})}),(0,t.jsxs)("form",{onSubmit:e=>{e.preventDefault(),B()},className:"p-6 space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 pb-2 border-b border-gray-100",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 text-blue-600"}),(0,t.jsx)("h2",{className:"text-base font-semibold text-gray-900",children:"Informazioni Base"}),(0,t.jsx)("span",{className:"text-xs text-gray-500",children:"Dati principali del cavo e operatore"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[k&&(0,t.jsxs)("div",{className:"md:col-span-2 p-4 bg-gray-50 rounded-lg border",children:[(0,t.jsxs)("h3",{className:"text-sm font-medium text-gray-700 mb-3",children:["Cavo: ",k.id_cavo]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Tipologia:"})," ",(0,t.jsx)("span",{className:"font-bold",children:k.tipologia||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Sezione:"})," ",(0,t.jsx)("span",{className:"font-bold",children:k.sezione||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Sistema:"})," ",(0,t.jsx)("span",{className:"font-bold",children:k.sistema||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Utility:"})," ",(0,t.jsx)("span",{className:"font-bold",children:k.utility||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Da:"})," ",(0,t.jsx)("span",{className:"font-bold",children:k.ubicazione_partenza||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"A:"})," ",(0,t.jsx)("span",{className:"font-bold",children:k.ubicazione_arrivo||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Metri Teorici:"})," ",(0,t.jsx)("span",{className:"font-bold",children:k.metri_teorici||0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Metri Reali:"})," ",(0,t.jsx)("span",{className:"font-bold",children:k.metratura_reale||0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Stato:"})," ",(0,t.jsx)("span",{className:"font-bold",children:k.stato_installazione||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Bobina:"})," ",(0,t.jsx)("span",{className:"font-bold",children:k.id_bobina||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Colore:"})," ",(0,t.jsx)("span",{className:"font-bold",children:k.colore_cavo||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-gray-600",children:"Collegamenti:"})," ",(0,t.jsx)("span",{className:"font-bold",children:k.collegamenti||0})]})]})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(l.J,{htmlFor:"id_operatore",className:"text-xs font-medium text-gray-700",children:"Operatore"}),(0,t.jsxs)(c.l6,{value:S.id_operatore||"",onValueChange:e=>Z("id_operatore",e),children:[(0,t.jsx)(c.bq,{className:"h-9 text-sm border-gray-300",children:(0,t.jsx)(c.yv,{placeholder:"Seleziona operatore..."})}),(0,t.jsx)(c.gC,{children:O.map(e=>(0,t.jsx)(c.eb,{value:e.id_responsabile.toString(),children:(0,t.jsx)("span",{className:"text-sm",children:e.nome_responsabile})},e.id_responsabile))})]})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(l.J,{htmlFor:"id_strumento",className:"text-xs font-medium text-gray-700",children:"Strumento di Misura"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(c.l6,{value:S.id_strumento?.toString()||"",onValueChange:e=>{let a=G.find(a=>a.id_strumento===parseInt(e));Z("id_strumento",parseInt(e)),a&&Z("strumento_utilizzato",`${a.marca} ${a.modello}`)},children:[(0,t.jsx)(c.bq,{className:"h-9 text-sm border-gray-300 flex-1",children:(0,t.jsx)(c.yv,{placeholder:"Seleziona strumento..."})}),(0,t.jsx)(c.gC,{children:G.map(e=>(0,t.jsxs)(c.eb,{value:e.id_strumento.toString(),children:[(0,t.jsx)("span",{className:"font-medium text-sm",children:e.nome}),(0,t.jsxs)("span",{className:"text-xs text-gray-500 ml-2",children:[e.marca," ",e.modello]})]},e.id_strumento))})]}),(0,t.jsx)(i.$,{type:"button",variant:"outline",size:"sm",className:"h-9 w-9 p-0 shrink-0",onClick:()=>w(!0),title:"Aggiungi nuovo strumento",children:(0,t.jsx)(g.A,{className:"h-4 w-4"})})]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 pb-2 border-b border-gray-100",children:[(0,t.jsx)("span",{className:"text-lg",children:"\uD83C\uDF24️"}),(0,t.jsx)("h2",{className:"text-base font-semibold text-gray-900",children:"Condizioni Ambientali"}),(0,t.jsx)("span",{className:"text-xs text-gray-500",children:"Temperatura e umidit\xe0 durante la certificazione"})]}),I&&(0,t.jsx)("div",{className:`p-3 rounded-lg border ${I.isDemo?"bg-amber-50 border-amber-200":"bg-emerald-50 border-emerald-200"}`,children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[E?(0,t.jsx)(x.A,{className:"h-4 w-4 animate-spin text-blue-600"}):(0,t.jsx)("span",{className:"text-lg",children:I.isDemo?"\uD83D\uDD27":"\uD83C\uDF24️"}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"text-sm font-semibold text-gray-900",children:[I.temperature,"\xb0C • ",I.humidity,"% UR"]}),I.city&&(0,t.jsx)("div",{className:"text-xs text-gray-600",children:I.city})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["\uD83D\uDCE1 ",I.source]})]}),(0,t.jsx)(i.$,{type:"button",variant:J?"default":"outline",size:"sm",onClick:()=>$(!J),className:"h-7 text-xs",children:J?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(h.A,{className:"h-3 w-3 mr-1"}),"Automatico"]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(v.A,{className:"h-3 w-3 mr-1"}),"Manuale"]})})]})}),J&&(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(l.J,{htmlFor:"temperatura_prova",className:"text-xs font-medium text-gray-700",children:"Temperatura (\xb0C)"}),(0,t.jsx)(o.p,{id:"temperatura_prova",type:"number",step:"0.1",value:S.temperatura_prova||"",onChange:e=>Z("temperatura_prova",parseFloat(e.target.value)),placeholder:"20.0",className:"h-8 text-sm"})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(l.J,{htmlFor:"umidita_prova",className:"text-xs font-medium text-gray-700",children:"Umidit\xe0 Relativa (%)"}),(0,t.jsx)(o.p,{id:"umidita_prova",type:"number",min:"0",max:"100",value:S.umidita_prova||"",onChange:e=>Z("umidita_prova",parseFloat(e.target.value)),placeholder:"50",className:"h-8 text-sm"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 pb-2 border-b border-gray-100",children:[(0,t.jsx)("span",{className:"text-lg",children:"⚡"}),(0,t.jsx)("h2",{className:"text-base font-semibold text-gray-900",children:"Misurazioni e Test CEI 64-8"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(l.J,{htmlFor:"valore_continuita",className:"text-xs font-medium text-gray-700",children:"Continuit\xe0"}),(0,t.jsxs)(c.l6,{value:S.valore_continuita||"OK",onValueChange:e=>Z("valore_continuita",e),children:[(0,t.jsx)(c.bq,{className:"h-8 text-sm",children:(0,t.jsx)(c.yv,{})}),(0,t.jsxs)(c.gC,{children:[(0,t.jsx)(c.eb,{value:"OK",children:"✅ OK"}),(0,t.jsx)(c.eb,{value:"NON_OK",children:"❌ NON OK"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(l.J,{htmlFor:"valore_isolamento",className:"text-xs font-medium text-gray-700",children:"Isolamento (MΩ)"}),(0,t.jsx)(o.p,{id:"valore_isolamento",value:S.valore_isolamento||"",onChange:e=>Z("valore_isolamento",e.target.value),placeholder:"500",className:"h-8 text-sm"})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(l.J,{htmlFor:"valore_resistenza",className:"text-xs font-medium text-gray-700",children:"Resistenza (Ω)"}),(0,t.jsx)(o.p,{id:"valore_resistenza",value:S.valore_resistenza||"",onChange:e=>Z("valore_resistenza",e.target.value),placeholder:"0.5",className:"h-8 text-sm"})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(l.J,{htmlFor:"tensione_prova_isolamento",className:"text-xs font-medium text-gray-700",children:"Tensione Prova (V)"}),(0,t.jsx)(o.p,{id:"tensione_prova_isolamento",type:"number",value:S.tensione_prova_isolamento||"",onChange:e=>Z("tensione_prova_isolamento",parseInt(e.target.value)),placeholder:"500",className:"h-8 text-sm"})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(l.J,{htmlFor:"esito_complessivo",className:"text-xs font-medium text-gray-700",children:"Esito Complessivo"}),(0,t.jsxs)(c.l6,{value:S.esito_complessivo||"CONFORME",onValueChange:e=>Z("esito_complessivo",e),children:[(0,t.jsx)(c.bq,{className:"h-8 text-sm",children:(0,t.jsx)(c.yv,{})}),(0,t.jsxs)(c.gC,{children:[(0,t.jsx)(c.eb,{value:"CONFORME",children:"✅ CONFORME"}),(0,t.jsx)(c.eb,{value:"NON_CONFORME",children:"❌ NON CONFORME"}),(0,t.jsx)(c.eb,{value:"CONFORME_CON_OSSERVAZIONI",children:"⚠️ CONFORME CON OSSERVAZIONI"})]})]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 pb-2 border-b border-gray-100",children:[(0,t.jsx)("span",{className:"text-lg",children:"\uD83D\uDCDD"}),(0,t.jsx)("h2",{className:"text-base font-semibold text-gray-900",children:"Note"})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(l.J,{htmlFor:"note",className:"text-xs font-medium text-gray-700",children:"Osservazioni aggiuntive"}),(0,t.jsx)(d.T,{id:"note",value:S.note||"",onChange:e=>Z("note",e.target.value),placeholder:"Inserisci eventuali note o osservazioni...",className:"h-16 text-sm resize-none"})]})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-3 pt-4 border-t border-gray-200",children:[(0,t.jsx)(i.$,{type:"button",variant:"outline",onClick:y,disabled:T,className:"px-4 h-9",children:"Annulla"}),(0,t.jsx)(i.$,{type:"submit",disabled:T,className:"px-6 h-9 bg-green-600 hover:bg-green-700",children:T?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(x.A,{className:"h-4 w-4 animate-spin mr-2"}),"Salvando..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Salva e Chiudi"]})})]})]}),(0,t.jsx)(m.lG,{open:C,onOpenChange:w,children:(0,t.jsxs)(m.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)(m.c7,{children:(0,t.jsx)(m.L3,{children:"Aggiungi Nuovo Strumento"})}),(0,t.jsx)(f.A,{cantiereId:e,onSuccess:()=>{w(!1),q(),z&&z()},onCancel:()=>w(!1)})]})})]})}},34729:(e,a,s)=>{s.d(a,{T:()=>n});var t=s(60687),r=s(43210),i=s(4780);let n=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...a}));n.displayName="Textarea"},44493:(e,a,s)=>{s.d(a,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>i,aR:()=>n});var t=s(60687);s(43210);var r=s(4780);function i({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...a})}function n({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...a})}function l({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...a})}function o({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...a})}function d({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...a})}},63503:(e,a,s)=>{s.d(a,{Cf:()=>m,Es:()=>u,L3:()=>h,c7:()=>x,lG:()=>l,rr:()=>p,zM:()=>o});var t=s(60687);s(43210);var r=s(26134),i=s(11860),n=s(4780);function l({...e}){return(0,t.jsx)(r.bL,{"data-slot":"dialog",...e})}function o({...e}){return(0,t.jsx)(r.l9,{"data-slot":"dialog-trigger",...e})}function d({...e}){return(0,t.jsx)(r.ZL,{"data-slot":"dialog-portal",...e})}function c({className:e,...a}){return(0,t.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...a})}function m({className:e,children:a,showCloseButton:s=!0,...l}){return(0,t.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,t.jsx)(c,{}),(0,t.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...l,children:[a,s&&(0,t.jsxs)(r.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(i.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",e),...a})}function u({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...a})}function h({className:e,...a}){return(0,t.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",e),...a})}function p({className:e,...a}){return(0,t.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...a})}},70440:(e,a,s)=>{s.r(a),s.d(a,{default:()=>r});var t=s(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},80013:(e,a,s)=>{s.d(a,{J:()=>n});var t=s(60687);s(43210);var r=s(78148),i=s(4780);function n({className:e,...a}){return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...a})}},89667:(e,a,s)=>{s.d(a,{p:()=>n});var t=s(60687),r=s(43210),i=s(4780);let n=r.forwardRef(({className:e,type:a,...s},r)=>(0,t.jsx)("input",{type:a,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),ref:r,...s}));n.displayName="Input"},96834:(e,a,s)=>{s.d(a,{E:()=>o});var t=s(60687);s(43210);var r=s(8730),i=s(24224),n=s(4780);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:a,asChild:s=!1,...i}){let o=s?r.DX:"span";return(0,t.jsx)(o,{"data-slot":"badge",className:(0,n.cn)(l({variant:a}),e),...i})}}};