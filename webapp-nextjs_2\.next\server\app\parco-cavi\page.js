(()=>{var e={};e.id=562,e.ids=[562],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26134:(e,i,a)=>{"use strict";a.d(i,{G$:()=>K,Hs:()=>N,UC:()=>ea,VY:()=>es,ZL:()=>ee,bL:()=>X,bm:()=>er,hE:()=>et,hJ:()=>ei,l9:()=>Q});var t=a(43210),s=a(70569),r=a(98599),n=a(11273),o=a(96963),l=a(65551),c=a(31355),d=a(32547),u=a(25028),m=a(46059),x=a(14163),p=a(1359),b=a(42247),h=a(63376),g=a(8730),v=a(60687),f="Dialog",[j,N]=(0,n.A)(f),[_,y]=j(f),w=e=>{let{__scopeDialog:i,children:a,open:s,defaultOpen:r,onOpenChange:n,modal:c=!0}=e,d=t.useRef(null),u=t.useRef(null),[m,x]=(0,l.i)({prop:s,defaultProp:r??!1,onChange:n,caller:f});return(0,v.jsx)(_,{scope:i,triggerRef:d,contentRef:u,contentId:(0,o.B)(),titleId:(0,o.B)(),descriptionId:(0,o.B)(),open:m,onOpenChange:x,onOpenToggle:t.useCallback(()=>x(e=>!e),[x]),modal:c,children:a})};w.displayName=f;var z="DialogTrigger",D=t.forwardRef((e,i)=>{let{__scopeDialog:a,...t}=e,n=y(z,a),o=(0,r.s)(i,n.triggerRef);return(0,v.jsx)(x.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":n.open,"aria-controls":n.contentId,"data-state":J(n.open),...t,ref:o,onClick:(0,s.m)(e.onClick,n.onOpenToggle)})});D.displayName=z;var C="DialogPortal",[S,A]=j(C,{forceMount:void 0}),E=e=>{let{__scopeDialog:i,forceMount:a,children:s,container:r}=e,n=y(C,i);return(0,v.jsx)(S,{scope:i,forceMount:a,children:t.Children.map(s,e=>(0,v.jsx)(m.C,{present:a||n.open,children:(0,v.jsx)(u.Z,{asChild:!0,container:r,children:e})}))})};E.displayName=C;var T="DialogOverlay",I=t.forwardRef((e,i)=>{let a=A(T,e.__scopeDialog),{forceMount:t=a.forceMount,...s}=e,r=y(T,e.__scopeDialog);return r.modal?(0,v.jsx)(m.C,{present:t||r.open,children:(0,v.jsx)(R,{...s,ref:i})}):null});I.displayName=T;var F=(0,g.TL)("DialogOverlay.RemoveScroll"),R=t.forwardRef((e,i)=>{let{__scopeDialog:a,...t}=e,s=y(T,a);return(0,v.jsx)(b.A,{as:F,allowPinchZoom:!0,shards:[s.contentRef],children:(0,v.jsx)(x.sG.div,{"data-state":J(s.open),...t,ref:i,style:{pointerEvents:"auto",...t.style}})})}),O="DialogContent",B=t.forwardRef((e,i)=>{let a=A(O,e.__scopeDialog),{forceMount:t=a.forceMount,...s}=e,r=y(O,e.__scopeDialog);return(0,v.jsx)(m.C,{present:t||r.open,children:r.modal?(0,v.jsx)(k,{...s,ref:i}):(0,v.jsx)(M,{...s,ref:i})})});B.displayName=O;var k=t.forwardRef((e,i)=>{let a=y(O,e.__scopeDialog),n=t.useRef(null),o=(0,r.s)(i,a.contentRef,n);return t.useEffect(()=>{let e=n.current;if(e)return(0,h.Eq)(e)},[]),(0,v.jsx)($,{...e,ref:o,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,s.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),a.triggerRef.current?.focus()}),onPointerDownOutside:(0,s.m)(e.onPointerDownOutside,e=>{let i=e.detail.originalEvent,a=0===i.button&&!0===i.ctrlKey;(2===i.button||a)&&e.preventDefault()}),onFocusOutside:(0,s.m)(e.onFocusOutside,e=>e.preventDefault())})}),M=t.forwardRef((e,i)=>{let a=y(O,e.__scopeDialog),s=t.useRef(!1),r=t.useRef(!1);return(0,v.jsx)($,{...e,ref:i,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:i=>{e.onCloseAutoFocus?.(i),i.defaultPrevented||(s.current||a.triggerRef.current?.focus(),i.preventDefault()),s.current=!1,r.current=!1},onInteractOutside:i=>{e.onInteractOutside?.(i),i.defaultPrevented||(s.current=!0,"pointerdown"===i.detail.originalEvent.type&&(r.current=!0));let t=i.target;a.triggerRef.current?.contains(t)&&i.preventDefault(),"focusin"===i.detail.originalEvent.type&&r.current&&i.preventDefault()}})}),$=t.forwardRef((e,i)=>{let{__scopeDialog:a,trapFocus:s,onOpenAutoFocus:n,onCloseAutoFocus:o,...l}=e,u=y(O,a),m=t.useRef(null),x=(0,r.s)(i,m);return(0,p.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(d.n,{asChild:!0,loop:!0,trapped:s,onMountAutoFocus:n,onUnmountAutoFocus:o,children:(0,v.jsx)(c.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":J(u.open),...l,ref:x,onDismiss:()=>u.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(Z,{titleId:u.titleId}),(0,v.jsx)(Y,{contentRef:m,descriptionId:u.descriptionId})]})]})}),L="DialogTitle",P=t.forwardRef((e,i)=>{let{__scopeDialog:a,...t}=e,s=y(L,a);return(0,v.jsx)(x.sG.h2,{id:s.titleId,...t,ref:i})});P.displayName=L;var U="DialogDescription",V=t.forwardRef((e,i)=>{let{__scopeDialog:a,...t}=e,s=y(U,a);return(0,v.jsx)(x.sG.p,{id:s.descriptionId,...t,ref:i})});V.displayName=U;var q="DialogClose",G=t.forwardRef((e,i)=>{let{__scopeDialog:a,...t}=e,r=y(q,a);return(0,v.jsx)(x.sG.button,{type:"button",...t,ref:i,onClick:(0,s.m)(e.onClick,()=>r.onOpenChange(!1))})});function J(e){return e?"open":"closed"}G.displayName=q;var H="DialogTitleWarning",[K,W]=(0,n.q)(H,{contentName:O,titleName:L,docsSlug:"dialog"}),Z=({titleId:e})=>{let i=W(H),a=`\`${i.contentName}\` requires a \`${i.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${i.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${i.docsSlug}`;return t.useEffect(()=>{e&&(document.getElementById(e)||console.error(a))},[a,e]),null},Y=({contentRef:e,descriptionId:i})=>{let a=W("DialogDescriptionWarning"),s=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${a.contentName}}.`;return t.useEffect(()=>{let a=e.current?.getAttribute("aria-describedby");i&&a&&(document.getElementById(i)||console.warn(s))},[s,e,i]),null},X=w,Q=D,ee=E,ei=I,ea=B,et=P,es=V,er=G},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29844:(e,i,a)=>{"use strict";a.d(i,{tU:()=>A,av:()=>I,j7:()=>E,Xi:()=>T});var t=a(60687),s=a(43210),r=a(70569),n=a(11273),o=a(72942),l=a(46059),c=a(14163),d=a(43),u=a(65551),m=a(96963),x="Tabs",[p,b]=(0,n.A)(x,[o.RG]),h=(0,o.RG)(),[g,v]=p(x),f=s.forwardRef((e,i)=>{let{__scopeTabs:a,value:s,onValueChange:r,defaultValue:n,orientation:o="horizontal",dir:l,activationMode:p="automatic",...b}=e,h=(0,d.jH)(l),[v,f]=(0,u.i)({prop:s,onChange:r,defaultProp:n??"",caller:x});return(0,t.jsx)(g,{scope:a,baseId:(0,m.B)(),value:v,onValueChange:f,orientation:o,dir:h,activationMode:p,children:(0,t.jsx)(c.sG.div,{dir:h,"data-orientation":o,...b,ref:i})})});f.displayName=x;var j="TabsList",N=s.forwardRef((e,i)=>{let{__scopeTabs:a,loop:s=!0,...r}=e,n=v(j,a),l=h(a);return(0,t.jsx)(o.bL,{asChild:!0,...l,orientation:n.orientation,dir:n.dir,loop:s,children:(0,t.jsx)(c.sG.div,{role:"tablist","aria-orientation":n.orientation,...r,ref:i})})});N.displayName=j;var _="TabsTrigger",y=s.forwardRef((e,i)=>{let{__scopeTabs:a,value:s,disabled:n=!1,...l}=e,d=v(_,a),u=h(a),m=D(d.baseId,s),x=C(d.baseId,s),p=s===d.value;return(0,t.jsx)(o.q7,{asChild:!0,...u,focusable:!n,active:p,children:(0,t.jsx)(c.sG.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":x,"data-state":p?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:m,...l,ref:i,onMouseDown:(0,r.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(s)}),onKeyDown:(0,r.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(s)}),onFocus:(0,r.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;p||n||!e||d.onValueChange(s)})})})});y.displayName=_;var w="TabsContent",z=s.forwardRef((e,i)=>{let{__scopeTabs:a,value:r,forceMount:n,children:o,...d}=e,u=v(w,a),m=D(u.baseId,r),x=C(u.baseId,r),p=r===u.value,b=s.useRef(p);return s.useEffect(()=>{let e=requestAnimationFrame(()=>b.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,t.jsx)(l.C,{present:n||p,children:({present:a})=>(0,t.jsx)(c.sG.div,{"data-state":p?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":m,hidden:!a,id:x,tabIndex:0,...d,ref:i,style:{...e.style,animationDuration:b.current?"0s":void 0},children:a&&o})})});function D(e,i){return`${e}-trigger-${i}`}function C(e,i){return`${e}-content-${i}`}z.displayName=w;var S=a(4780);let A=f,E=s.forwardRef(({className:e,...i},a)=>(0,t.jsx)(N,{ref:a,className:(0,S.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...i}));E.displayName=N.displayName;let T=s.forwardRef(({className:e,...i},a)=>(0,t.jsx)(y,{ref:a,className:(0,S.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...i}));T.displayName=y.displayName;let I=s.forwardRef(({className:e,...i},a)=>(0,t.jsx)(z,{ref:a,className:(0,S.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...i}));I.displayName=z.displayName},33873:e=>{"use strict";e.exports=require("path")},51895:(e,i,a)=>{"use strict";a.r(i),a.d(i,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var t=a(65239),s=a(48088),r=a(88170),n=a.n(r),o=a(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);a.d(i,l);let c={children:["",{children:["parco-cavi",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,71902)),"C:\\CMS\\webapp-nextjs_2\\src\\app\\parco-cavi\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\CMS\\webapp-nextjs_2\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\CMS\\webapp-nextjs_2\\src\\app\\parco-cavi\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/parco-cavi/page",pathname:"/parco-cavi",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,i,a)=>{"use strict";a.d(i,{Cf:()=>u,Es:()=>x,L3:()=>p,c7:()=>m,lG:()=>o,rr:()=>b,zM:()=>l});var t=a(60687);a(43210);var s=a(26134),r=a(11860),n=a(4780);function o({...e}){return(0,t.jsx)(s.bL,{"data-slot":"dialog",...e})}function l({...e}){return(0,t.jsx)(s.l9,{"data-slot":"dialog-trigger",...e})}function c({...e}){return(0,t.jsx)(s.ZL,{"data-slot":"dialog-portal",...e})}function d({className:e,...i}){return(0,t.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...i})}function u({className:e,children:i,showCloseButton:a=!0,...o}){return(0,t.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,t.jsx)(d,{}),(0,t.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...o,children:[i,a&&(0,t.jsxs)(s.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(r.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m({className:e,...i}){return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",e),...i})}function x({className:e,...i}){return(0,t.jsx)("div",{"data-slot":"dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...i})}function p({className:e,...i}){return(0,t.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",e),...i})}function b({className:e,...i}){return(0,t.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...i})}},64435:(e,i,a)=>{"use strict";a.r(i),a.d(i,{default:()=>Y});var t=a(60687),s=a(43210),r=a(44493),n=a(29523),o=a(96834),l=a(89667),c=a(6211),d=a(91821),u=a(63213),m=a(62185);let x={DISPONIBILE:"Disponibile",IN_USO:"In uso",TERMINATA:"Terminata",OVER:"Over"},p=(e,i)=>e<0?x.OVER:0===e?x.TERMINATA:e<i?x.IN_USO:x.DISPONIBILE,b=e=>e===x.DISPONIBILE||e===x.IN_USO,h=e=>{switch(e){case x.DISPONIBILE:return"hover:bg-green-50";case x.IN_USO:return"hover:bg-yellow-50";case x.TERMINATA:return"hover:bg-red-50";case x.OVER:return"hover:bg-red-100";default:return"hover:bg-gray-50"}},g=e=>e!==x.OVER&&e!==x.TERMINATA,v=(e,i)=>i<=0?0:Math.max(0,Math.min(100,(i-e)/i*100)),f=e=>`${e.toFixed(1)}m`,j=e=>{switch(e){case x.DISPONIBILE:return"Bobina disponibile per nuove installazioni";case x.IN_USO:return"Bobina parzialmente utilizzata";case x.TERMINATA:return"Bobina completamente esaurita";case x.OVER:return"Bobina sovra-utilizzata (metri negativi)";default:return"Stato non definito"}};var N=a(63503),_=a(80013),y=a(96474),w=a(41862),z=a(93613);let D={numero_bobina:"",utility:"",tipologia:"",n_conduttori:"0",sezione:"",metri_totali:"",ubicazione_bobina:"TBD",fornitore:"TBD",n_DDT:"TBD",data_DDT:"",configurazione:"s"};function C({open:e,onClose:i,cantiereId:a,onSuccess:r,onError:o}){let[c,u]=(0,s.useState)(D),[x,p]=(0,s.useState)(!1),[b,h]=(0,s.useState)(""),[g,v]=(0,s.useState)("1"),[f,j]=(0,s.useState)(!1),[C,S]=(0,s.useState)(!0),[A,E]=(0,s.useState)(""),[T,I]=(0,s.useState)(!1),F=async()=>{if(a&&!(a<=0))try{let e=await m.Fw.getBobine(a);if(e&&e.length>0){let i=e.filter(e=>e.numero_bobina&&/^\d+$/.test(e.numero_bobina));if(i.length>0){let e=Math.max(...i.map(e=>parseInt(e.numero_bobina,10))),a=String(e+1);v(a),u(e=>({...e,numero_bobina:a}))}else v("1"),u(e=>({...e,numero_bobina:"1"}))}else v("1"),u(e=>({...e,numero_bobina:"1"}))}catch(e){v("1"),u(e=>({...e,numero_bobina:"1"}))}},R=(e,i)=>{u(a=>({...a,[e]:i})),h("")},O=async e=>{E(e),u(i=>({...i,configurazione:e})),I(!1),"s"===e?await F():u(e=>({...e,numero_bobina:""}))},B=()=>{if(!c.numero_bobina.trim())return"Il numero bobina \xe8 obbligatorio";if("m"===c.configurazione){let e=c.numero_bobina.trim();if(/[\s\\/:*?"<>|]/.test(e))return'Il numero bobina non pu\xf2 contenere spazi o caratteri speciali come \\ / : * ? " < > |'}if(!c.utility.trim())return"La utility \xe8 obbligatoria";if(!c.tipologia.trim())return"La tipologia \xe8 obbligatoria";if(!c.sezione.trim())return"La formazione \xe8 obbligatoria";if(!c.metri_totali.trim())return"I metri totali sono obbligatori";let e=parseFloat(c.metri_totali);return isNaN(e)||e<=0?"I metri totali devono essere un numero positivo":null},k=async()=>{let e=B();if(e)return void h(e);try{if(p(!0),h(""),!a||a<=0)throw Error("Cantiere non selezionato");let e={numero_bobina:c.numero_bobina.trim(),utility:c.utility.trim().toUpperCase(),tipologia:c.tipologia.trim().toUpperCase(),n_conduttori:"0",sezione:c.sezione.trim(),metri_totali:parseFloat(c.metri_totali),ubicazione_bobina:c.ubicazione_bobina.trim()||"TBD",fornitore:c.fornitore.trim()||"TBD",n_DDT:c.n_DDT.trim()||"TBD",data_DDT:c.data_DDT||null,configurazione:c.configurazione};await m.Fw.createBobina(a,e),r(`Bobina ${c.numero_bobina} creata con successo`),i()}catch(i){let e=i.response?.data?.detail||i.message||"Errore durante la creazione della bobina";e.includes("gi\xe0 presente nel cantiere")||e.includes("gi\xe0 esistente")?h(`⚠️ Bobina con numero ${c.numero_bobina} gi\xe0 esistente. Scegli un numero diverso.`):o(e)}finally{p(!1)}},M=()=>{x||(u(D),h(""),i())};return(0,t.jsx)(N.lG,{open:e,onOpenChange:M,children:(0,t.jsxs)(N.Cf,{className:"max-h-[95vh] overflow-y-auto",style:{width:"1000px !important",maxWidth:"95vw !important",minWidth:"1000px"},children:[(0,t.jsxs)(N.c7,{children:[(0,t.jsxs)(N.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(y.A,{className:"h-5 w-5"}),"Crea Nuova Bobina"]}),(0,t.jsx)(N.rr,{})]}),(0,t.jsxs)("div",{className:"grid gap-4 py-4",children:[f&&(0,t.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,t.jsx)(w.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,t.jsx)("span",{children:"Verifica configurazione..."})]}),T&&!f&&(0,t.jsxs)("div",{className:"border rounded-lg p-4 bg-blue-50",children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"Seleziona configurazione per questo cantiere"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Questa scelta determiner\xe0 come verranno numerati tutti i futuri inserimenti di bobine in questo cantiere."}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-3",children:[(0,t.jsx)(n.$,{variant:"outline",className:"justify-start h-auto p-4",onClick:()=>O("s"),children:(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("div",{className:"font-medium",children:"Standard (s) - Numerazione automatica"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"I numeri bobina vengono generati automaticamente: 1, 2, 3..."})]})}),(0,t.jsx)(n.$,{variant:"outline",className:"justify-start h-auto p-4",onClick:()=>O("m"),children:(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("div",{className:"font-medium",children:"Manuale (m) - Inserimento manuale"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Puoi inserire numeri personalizzati: A123, TEST01, ecc."})]})})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[!T&&!f&&(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(_.J,{htmlFor:"numero_bobina",className:"text-right",children:"Bobina *"}),(0,t.jsx)(l.p,{id:"numero_bobina",value:c.numero_bobina,onChange:e=>R("numero_bobina",e.target.value),placeholder:"s"===c.configurazione?"Generato automaticamente":"Es: A123, TEST01",disabled:x||"s"===c.configurazione,className:`col-span-2 ${"s"===c.configurazione?"bg-gray-50":""}`})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(_.J,{htmlFor:"utility",className:"text-right",children:"Utility *"}),(0,t.jsx)(l.p,{id:"utility",value:c.utility,onChange:e=>R("utility",e.target.value),className:"col-span-2",placeholder:"Es: ENEL, TIM, OPEN FIBER",disabled:x})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(_.J,{htmlFor:"tipologia",className:"text-right",children:"Tipologia *"}),(0,t.jsx)(l.p,{id:"tipologia",value:c.tipologia,onChange:e=>R("tipologia",e.target.value),className:"col-span-2",placeholder:"Es: FO, RAME",disabled:x})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(_.J,{htmlFor:"sezione",className:"text-right",children:"Formazione *"}),(0,t.jsx)(l.p,{id:"sezione",value:c.sezione,onChange:e=>R("sezione",e.target.value),className:"col-span-2",placeholder:"Es: 9/125, 50/125, 1.5",disabled:x})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(_.J,{htmlFor:"metri_totali",className:"text-right",children:"Metri Totali *"}),(0,t.jsx)(l.p,{id:"metri_totali",type:"number",step:"0.1",min:"0",value:c.metri_totali,onChange:e=>R("metri_totali",e.target.value),className:"col-span-2",placeholder:"Es: 1000",disabled:x})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(_.J,{htmlFor:"ubicazione_bobina",className:"text-right",children:"Ubicazione"}),(0,t.jsx)(l.p,{id:"ubicazione_bobina",value:c.ubicazione_bobina,onChange:e=>R("ubicazione_bobina",e.target.value),className:"col-span-2",placeholder:"Es: Magazzino A, Cantiere",disabled:x})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(_.J,{htmlFor:"fornitore",className:"text-right",children:"Fornitore"}),(0,t.jsx)(l.p,{id:"fornitore",value:c.fornitore,onChange:e=>R("fornitore",e.target.value),className:"col-span-2",placeholder:"Es: Prysmian, Nexans",disabled:x})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(_.J,{htmlFor:"n_DDT",className:"text-right",children:"N\xb0 DDT"}),(0,t.jsx)(l.p,{id:"n_DDT",value:c.n_DDT,onChange:e=>R("n_DDT",e.target.value),className:"col-span-2",placeholder:"Es: DDT001",disabled:x})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(_.J,{htmlFor:"data_DDT",className:"text-right",children:"Data DDT"}),(0,t.jsx)(l.p,{id:"data_DDT",type:"date",value:c.data_DDT,onChange:e=>R("data_DDT",e.target.value),className:"col-span-2",disabled:x})]})]})]}),b&&(0,t.jsxs)(d.Fc,{variant:"destructive",children:[(0,t.jsx)(z.A,{className:"h-4 w-4"}),(0,t.jsx)(d.TN,{children:b})]})]}),(0,t.jsxs)(N.Es,{children:[(0,t.jsx)(n.$,{variant:"outline",onClick:M,disabled:x||T,children:"Annulla"}),(0,t.jsxs)(n.$,{onClick:k,disabled:x||T||f,children:[x&&(0,t.jsx)(w.A,{className:"mr-2 h-4 w-4 animate-spin"}),x?"Creando...":"Crea Bobina"]})]})]})})}var S=a(15079),A=a(19080),E=a(96882);function T({open:e,onClose:i,bobina:a,onSuccess:r,onError:o}){let{cantiere:c}=(0,u.A)(),[x,p]=(0,s.useState)({numero_bobina:"",utility:"",tipologia:"",sezione:"",metri_totali:"",metri_residui:"",stato_bobina:"",ubicazione_bobina:"",fornitore:"",n_DDT:"",data_DDT:"",configurazione:""}),[b,h]=(0,s.useState)({}),[g,v]=(0,s.useState)({}),[f,j]=(0,s.useState)(!1),y=(e,i)=>{p(t=>{let s={...t,[e]:i};return"metri_totali"===e&&a&&(s.metri_residui=Math.max(0,(parseFloat(i)||0)-(a.metri_totali-a.metri_residui)).toString()),s})},D=()=>!!a&&("Over"===a.stato_bobina||"Disponibile"===a.stato_bobina),C=e=>!!a&&(!!["fornitore","ubicazione_bobina","n_DDT","data_DDT"].includes(e)||"Disponibile"===a.stato_bobina),T=async()=>{if(a&&c&&!(Object.keys(b).length>0)){if(!D())return void o("La bobina non pu\xf2 essere modificata nel suo stato attuale");try{j(!0);let e={utility:x.utility,tipologia:x.tipologia,sezione:x.sezione,metri_totali:parseFloat(x.metri_totali),ubicazione_bobina:x.ubicazione_bobina,fornitore:x.fornitore,n_DDT:x.n_DDT,data_DDT:x.data_DDT||null};await m.Fw.updateBobina(c.id_cantiere,a.id_bobina,e),r(`Bobina ${a.numero_bobina} aggiornata con successo`),i()}catch(e){o(e.response?.data?.detail||e.message||"Errore durante la modifica della bobina")}finally{j(!1)}}},I=()=>{f||(p({numero_bobina:"",utility:"",tipologia:"",sezione:"",metri_totali:"",metri_residui:"",stato_bobina:"",ubicazione_bobina:"",fornitore:"",n_DDT:"",data_DDT:"",configurazione:""}),h({}),v({}),i())};return a?(0,t.jsx)(N.lG,{open:e,onOpenChange:I,children:(0,t.jsxs)(N.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(N.c7,{children:[(0,t.jsxs)(N.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(A.A,{className:"h-5 w-5"}),"Modifica Bobina"]}),(0,t.jsxs)(N.rr,{children:["Modifica i dati della bobina ",a.numero_bobina]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)(d.Fc,{className:"border-blue-200 bg-blue-50",children:[(0,t.jsx)(E.A,{className:"h-4 w-4 text-blue-600"}),(0,t.jsxs)(d.TN,{className:"text-blue-800",children:[(0,t.jsx)("div",{className:"font-semibold mb-1",children:"Condizioni per la modifica:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside text-sm space-y-1",children:[(0,t.jsx)("li",{children:'La bobina deve essere nello stato "Disponibile"'}),(0,t.jsx)("li",{children:"La bobina non deve essere associata a nessun cavo"}),(0,t.jsx)("li",{children:"Se modifichi i metri totali, i metri residui verranno aggiornati automaticamente"})]}),(0,t.jsxs)("div",{className:"mt-2 text-sm",children:[(0,t.jsx)("strong",{children:"Stato attuale:"})," ",a.stato_bobina]})]})]}),Object.keys(g).length>0&&(0,t.jsxs)(d.Fc,{className:"border-amber-200 bg-amber-50",children:[(0,t.jsx)(z.A,{className:"h-4 w-4 text-amber-600"}),(0,t.jsxs)(d.TN,{className:"text-amber-800",children:[(0,t.jsx)("div",{className:"font-semibold",children:"Attenzione:"}),(0,t.jsx)("ul",{className:"list-disc list-inside text-sm",children:Object.values(g).map((e,i)=>(0,t.jsx)("li",{children:e},i))})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(_.J,{htmlFor:"numero_bobina",children:"ID Bobina"}),(0,t.jsx)(l.p,{id:"numero_bobina",value:x.numero_bobina,disabled:!0,className:"bg-gray-100 font-semibold"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(_.J,{htmlFor:"utility",children:"Utility *"}),(0,t.jsx)(l.p,{id:"utility",value:x.utility,onChange:e=>y("utility",e.target.value),disabled:f||!C("utility"),className:b.utility?"border-red-500":""}),b.utility&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:b.utility})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(_.J,{htmlFor:"tipologia",children:"Tipologia *"}),(0,t.jsx)(l.p,{id:"tipologia",value:x.tipologia,onChange:e=>y("tipologia",e.target.value),disabled:f||!C("tipologia"),className:b.tipologia?"border-red-500":""}),b.tipologia&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:b.tipologia})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(_.J,{htmlFor:"sezione",children:"Formazione *"}),(0,t.jsx)(l.p,{id:"sezione",value:x.sezione,onChange:e=>y("sezione",e.target.value),disabled:f||!C("sezione"),className:b.sezione?"border-red-500":""}),b.sezione&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:b.sezione})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(_.J,{htmlFor:"metri_totali",children:"Metri Totali *"}),(0,t.jsx)(l.p,{id:"metri_totali",type:"number",value:x.metri_totali,onChange:e=>y("metri_totali",e.target.value),disabled:f||!C("metri_totali"),className:b.metri_totali?"border-red-500":"",step:"0.1",min:"0"}),b.metri_totali&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:b.metri_totali})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(_.J,{htmlFor:"metri_residui",children:"Metri Residui"}),(0,t.jsx)(l.p,{id:"metri_residui",type:"number",value:x.metri_residui,disabled:!0,className:"bg-gray-100"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"I metri residui non possono essere modificati direttamente"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(_.J,{htmlFor:"stato_bobina",children:"Stato Bobina"}),(0,t.jsxs)(S.l6,{value:x.stato_bobina,disabled:!0,children:[(0,t.jsx)(S.bq,{className:"bg-gray-100",children:(0,t.jsx)(S.yv,{})}),(0,t.jsxs)(S.gC,{children:[(0,t.jsx)(S.eb,{value:"Disponibile",children:"Disponibile"}),(0,t.jsx)(S.eb,{value:"In uso",children:"In uso"}),(0,t.jsx)(S.eb,{value:"Terminata",children:"Terminata"}),(0,t.jsx)(S.eb,{value:"Danneggiata",children:"Danneggiata"}),(0,t.jsx)(S.eb,{value:"Over",children:"Over"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(_.J,{htmlFor:"ubicazione_bobina",children:"Ubicazione Bobina"}),(0,t.jsx)(l.p,{id:"ubicazione_bobina",value:x.ubicazione_bobina,onChange:e=>y("ubicazione_bobina",e.target.value),disabled:f})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(_.J,{htmlFor:"fornitore",children:"Fornitore"}),(0,t.jsx)(l.p,{id:"fornitore",value:x.fornitore,onChange:e=>y("fornitore",e.target.value),disabled:f})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(_.J,{htmlFor:"n_DDT",children:"Numero DDT"}),(0,t.jsx)(l.p,{id:"n_DDT",value:x.n_DDT,onChange:e=>y("n_DDT",e.target.value),disabled:f})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(_.J,{htmlFor:"data_DDT",children:"Data DDT (YYYY-MM-DD)"}),(0,t.jsx)(l.p,{id:"data_DDT",type:"date",value:x.data_DDT,onChange:e=>y("data_DDT",e.target.value),disabled:f,className:b.data_DDT?"border-red-500":""}),b.data_DDT&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:b.data_DDT})]}),(0,t.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[(0,t.jsx)(_.J,{htmlFor:"configurazione",children:"Modalit\xe0 Numerazione"}),(0,t.jsx)(l.p,{id:"configurazione",value:"s"===x.configurazione?"Automatica":"Manuale",disabled:!0,className:"bg-gray-100"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"s"===x.configurazione?"Numerazione progressiva automatica (1, 2, 3, ...)":"Inserimento manuale dell'ID bobina (es. A123, SPEC01, ...)"})]})]})]}),(0,t.jsxs)(N.Es,{children:[(0,t.jsx)(n.$,{variant:"outline",onClick:I,disabled:f,children:"Annulla"}),(0,t.jsxs)(n.$,{onClick:T,disabled:f||Object.keys(b).length>0||!D(),className:"bg-mariner-600 hover:bg-mariner-700",children:[f&&(0,t.jsx)(w.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salva"]})]})]})}):null}var I=a(88233),F=a(43649);function R({open:e,onClose:i,bobina:a,cantiereId:r,onSuccess:o,onError:l}){let[c,u]=(0,s.useState)(!1),p=async()=>{if(a)try{if(u(!0),!r||r<=0)throw Error("Cantiere non selezionato");let e=a.id_bobina.split("_B")[1],t=await m.Fw.deleteBobina(r,e),s=`Bobina ${a.numero_bobina} eliminata con successo`;t.data?.is_last_bobina&&(s+=". Era l'ultima bobina del cantiere."),o(s),i()}catch(e){l(e.response?.data?.detail||e.message||"Errore durante l'eliminazione della bobina")}finally{u(!1)}},h=()=>{c||i()};if(!a)return null;let g=a.stato_bobina===x.OVER,v=!g&&b(a.stato_bobina)&&a.metri_residui===a.metri_totali,f=g?{type:"error",title:"Bobina OVER - Eliminazione bloccata",message:"Le bobine in stato OVER non possono essere eliminate per preservare la tracciabilit\xe0 del sistema. Contattare l'amministratore per la gestione di bobine OVER."}:b(a.stato_bobina)?a.metri_residui!==a.metri_totali?{type:"error",title:"Bobina in uso",message:`La bobina ha ${a.metri_residui}m residui su ${a.metri_totali}m totali. Solo le bobine completamente disponibili possono essere eliminate.`}:{type:"warning",title:"Conferma eliminazione",message:"Questa operazione \xe8 irreversibile. La bobina verr\xe0 rimossa definitivamente dal parco cavi."}:{type:"error",title:"Eliminazione non consentita",message:`La bobina \xe8 in stato "${a.stato_bobina}" e non pu\xf2 essere eliminata. ${j(a.stato_bobina)}`};return(0,t.jsx)(N.lG,{open:e,onOpenChange:h,children:(0,t.jsxs)(N.Cf,{className:"max-w-md",children:[(0,t.jsxs)(N.c7,{children:[(0,t.jsxs)(N.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(I.A,{className:"h-5 w-5 text-red-600"}),"Elimina Bobina"]}),(0,t.jsxs)(N.rr,{children:["Stai per eliminare la bobina ",a.numero_bobina]})]}),(0,t.jsxs)("div",{className:"py-4",children:[(0,t.jsxs)("div",{className:"bg-slate-50 p-4 rounded-lg mb-4",children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"Dettagli bobina:"}),(0,t.jsxs)("div",{className:"text-sm space-y-1",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Bobina:"})," ",a.numero_bobina]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Utility:"})," ",a.utility]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Tipologia:"})," ",a.tipologia]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Sezione:"})," ",a.sezione]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Stato:"})," ",a.stato_bobina]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Metri:"})," ",a.metri_residui,"m / ",a.metri_totali,"m"]}),a.ubicazione_bobina&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Ubicazione:"})," ",a.ubicazione_bobina]})]})]}),(0,t.jsxs)(d.Fc,{variant:"error"===f.type?"destructive":"default",children:[(0,t.jsx)(F.A,{className:"h-4 w-4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:f.title}),(0,t.jsx)(d.TN,{className:"mt-1",children:f.message})]})]})]}),(0,t.jsxs)(N.Es,{children:[(0,t.jsx)(n.$,{variant:"outline",onClick:h,disabled:c,children:"Annulla"}),(0,t.jsxs)(n.$,{variant:"destructive",onClick:p,disabled:c||!v,children:[c&&(0,t.jsx)(w.A,{className:"mr-2 h-4 w-4 animate-spin"}),c?"Eliminando...":"Elimina Bobina"]})]})]})})}var O=a(29844),B=a(23361),k=a(99270);let M=(e,i)=>{let[a,t]=(0,s.useState)(e);return(0,s.useEffect)(()=>{let a=setTimeout(()=>{t(e)},i);return()=>{clearTimeout(a)}},[e,i]),a};function $({open:e,onClose:i,bobina:a,cantiereId:r,onSuccess:o,onError:c}){let[d,u]=(0,s.useState)(!1),[x,p]=(0,s.useState)(!1),[b,h]=(0,s.useState)(!1),[g,v]=(0,s.useState)([]),[f,j]=(0,s.useState)([]),[_,y]=(0,s.useState)([]),[z,D]=(0,s.useState)({}),[C,S]=(0,s.useState)({}),[A,E]=(0,s.useState)(""),T=M(A,300),[I,F]=(0,s.useState)("id_cavo"),[R,$]=(0,s.useState)("asc"),[L,P]=(0,s.useState)("all"),[U,V]=(0,s.useState)("all"),[q,G]=(0,s.useState)(""),[J,H]=(0,s.useState)(""),[K,W]=(0,s.useState)(1),[Z,Y]=(0,s.useState)(20),X=(0,s.useMemo)(()=>{let e=e=>e.filter(e=>{let i=!T||e.id_cavo.toLowerCase().includes(T.toLowerCase())||e.tipologia?.toLowerCase().includes(T.toLowerCase())||e.ubicazione_partenza?.toLowerCase().includes(T.toLowerCase())||e.ubicazione_arrivo?.toLowerCase().includes(T.toLowerCase()),a="all"===L||e.tipologia===L,t="all"===U||e.sezione===U,s=parseFloat(e.metri_teorici?.toString()||"0"),r=!q||s>=parseFloat(q),n=!J||s<=parseFloat(J);return i&&a&&t&&r&&n}),i=e=>[...e].sort((e,i)=>{let a,t;switch(I){case"id_cavo":a=e.id_cavo,t=i.id_cavo;break;case"metri_teorici":a=parseFloat(e.metri_teorici?.toString()||"0"),t=parseFloat(i.metri_teorici?.toString()||"0");break;case"tipologia":a=e.tipologia||"",t=i.tipologia||"";break;case"ubicazione_partenza":a=e.ubicazione_partenza||"",t=i.ubicazione_partenza||"";break;default:return 0}if("string"!=typeof a)return"asc"===R?a-t:t-a;{let e=a.localeCompare(t);return"asc"===R?e:-e}});return{compatibiliFiltrati:i(e(g)),incompatibiliFiltrati:i(e(f))}},[g,f,T,I,R,L,U,q,J]),Q=(0,s.useMemo)(()=>{let{compatibiliFiltrati:e,incompatibiliFiltrati:i}=X,a=(K-1)*Z,t=a+Z;return{compatibili:e.slice(a,t),incompatibili:i.slice(a,t),totalCompatibili:e.length,totalIncompatibili:i.length,totalPages:Math.ceil(Math.max(e.length,i.length)/Z)}},[X,K,Z]),ee=(0,s.useMemo)(()=>{let e=Object.values(z).reduce((e,i)=>e+parseFloat(i||"0"),0),i=a?.metri_residui||0,t=Object.entries(z).some(([e,a])=>parseFloat(a||"0")>i),s=_.some(e=>e._isIncompatible),r=t||a?.stato_bobina==="OVER",n=Math.max(0,e-i);return{metriTotaliSelezionati:e,metriResiduiBobina:i,isOverState:r,metriEccedenza:n,percentualeUtilizzo:i>0?e/i*100:0,hasSingleCavoOver:t,hasIncompatibleCavi:s}},[z,a,_]);(0,s.useMemo)(()=>Array.from(new Set([...g,...f].map(e=>e.tipologia).filter(Boolean))).sort(),[g,f]),(0,s.useMemo)(()=>Array.from(new Set([...g,...f].map(e=>e.sezione).filter(Boolean))).sort(),[g,f]);let ei=(e,i)=>{if(console.log({cavoId:e.id_cavo,isCompatible:i,metriTeorici:e.metri_teorici}),_.some(i=>i.id_cavo===e.id_cavo))y(i=>i.filter(i=>i.id_cavo!==e.id_cavo)),D(i=>{let a={...i};return delete a[e.id_cavo],a});else{let a={...e,_isIncompatible:!i};y(e=>[...e,a]),D(i=>({...i,[e.id_cavo]:"0"}))}},ea=(e,i)=>{a?.metri_residui,parseFloat(i||"0");let t=_.find(i=>i.id_cavo===e);t?._isIncompatible,S(i=>{let a={...i};return delete a[e],a}),D(a=>({...a,[e]:i}))},et=(0,s.useMemo)(()=>{let e=a?.metri_residui||0,i=!1,t=[],s=[],r=null;for(let a of _){let n=parseFloat(z[a.id_cavo]||"0");n>0?i?s.push(a.id_cavo):(e-n<0?(t.push(a.id_cavo),r=a.id_cavo,i=!0):t.push(a.id_cavo),e-=n):t.push(a.id_cavo)}return{metriResiduiSimulati:e,caviValidi:t,caviBloccati:s,bobinaGiaOver:i,cavoCheCausaOver:r}},[_,z,a?.metri_residui]),es=()=>et,er=async()=>{if(!r||!a)return;if(0===_.length)return void c("Nessun cavo selezionato");let e=_.filter(e=>{let i=z[e.id_cavo];return!i||""===i.trim()||isNaN(parseFloat(i))||0>parseFloat(i)});if(e.length>0)return void c(`Metri posati mancanti o non validi per: ${e.map(e=>e.id_cavo).join(", ")}`);try{h(!0);let{caviValidi:e,caviBloccati:t}=es(),s=_.filter(e=>{let i=parseFloat(z[e.id_cavo]||"0"),a=t.includes(e.id_cavo);return i>0&&!a});if(0===s.length)return void c("Nessun cavo valido da salvare (tutti bloccati o senza metri)");console.log({caviSelezionati:_.length,caviValidi:e.length,caviBloccati:t.length,caviDaSalvare:s.length});let n=[],l=[],d=a?.metri_residui||0;for(let e of s)try{let i=z[e.id_cavo],t=parseFloat(i),s=d-t<0,o=e._isIncompatible||!1,l=s||o;console.log({metriPosati:t,metriResiduiCorrente:d,causaOver:s,isIncompatible:o,needsForceOver:l}),await m.At.updateMetriPosati(r,e.id_cavo,t,a.id_bobina,l),d-=t,n.push({cavo:e.id_cavo,metriPosati:t,success:!0,wasIncompatible:e._isIncompatible,wasForceOver:l})}catch(a){let i="Errore sconosciuto";if(a.response){let e=a.response.status,t=a.response.data;i=400===e?t?.message||t?.error||"Richiesta non valida (400)":404===e?"Cavo o bobina non trovati (404)":409===e?"Conflitto: cavo gi\xe0 assegnato o bobina non disponibile (409)":500===e?"Errore interno del server (500)":`Errore HTTP ${e}: ${t?.message||t?.error||"Errore del server"}`}else i=a.request?"Errore di connessione al server":a.message||"Errore di validazione";l.push({cavo:e.id_cavo,error:i})}if(0===l.length){let e=n.filter(e=>e.wasIncompatible).length,a=n.filter(e=>e.wasForceOver).length,t=`${n.length} cavi aggiornati con successo`;e>0&&(t+=` (${e} incompatibili)`),a>0&&(t+=` (${a} con force_over)`),o(t),i()}else c(`Errori: ${l.map(e=>`${e.cavo}: ${e.error}`).join(", ")}`)}catch(i){let e="Errore durante il salvataggio dei cavi";if(i.response){let a=i.response.status,t=i.response.data;e=400===a?`Errore di validazione: ${t?.message||t?.error||"Dati non validi"}`:401===a?"Sessione scaduta. Effettua nuovamente il login.":403===a?"Non hai i permessi per questa operazione":500===a?"Errore interno del server. Riprova pi\xf9 tardi.":`Errore del server (${a}): ${t?.message||t?.error||"Errore sconosciuto"}`}else e=i.request?"Impossibile contattare il server. Verifica la connessione.":i.message||"Errore imprevisto durante il salvataggio";c(e)}finally{h(!1)}},en=()=>{b||(y([]),D({}),S({}),i())};if(!a)return null;let eo=e=>{let i=e.match(/C\d+_B(\d+)/);return i?i[1]:e},el=(e,i)=>(console.log({isCompatible:i,caviLength:e.length,primi3Cavi:e.slice(0,3).map(e=>({id:e.id_cavo,tipologia:e.tipologia,sezione:e.sezione}))}),0===e.length)?(0,t.jsx)("div",{className:"h-[300px] flex items-center justify-center text-gray-500 border rounded",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(B.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,t.jsxs)("div",{children:["Nessun cavo ",i?"compatibile":"incompatibile"," disponibile"]}),(0,t.jsx)("div",{className:"text-xs mt-2 text-gray-400",children:i?`Cerca cavi con tipologia "${a?.tipologia}" e formazione "${a?.sezione}"`:"I cavi incompatibili hanno tipologia o formazione diverse"})]})}):(0,t.jsx)("div",{className:"space-y-1 h-[300px] overflow-y-auto border rounded p-2 w-full",children:e.map((e,a)=>{let s=_.some(i=>i.id_cavo===e.id_cavo),r=z[e.id_cavo]||"",{caviBloccati:n,cavoCheCausaOver:o}=et,l=s&&n.includes(e.id_cavo),c=s&&e.id_cavo===o;return(0,t.jsxs)("div",{className:`border rounded px-3 py-2 transition-colors ${l?"border-red-300 bg-red-50":c?"border-orange-300 bg-orange-50":s?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300 hover:bg-gray-50"}`,children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 w-full overflow-hidden",children:[(0,t.jsx)("input",{type:"checkbox",checked:s,onChange:a=>{ei(e,i)},className:"h-4 w-4 text-blue-600 border-gray-300 rounded flex-shrink-0"}),(0,t.jsxs)("div",{className:"flex items-center gap-2 flex-1 min-w-0 overflow-hidden",children:[(0,t.jsx)("span",{className:"font-medium text-gray-900 flex-shrink-0",children:e.id_cavo}),(0,t.jsx)("span",{className:"text-xs bg-gray-100 px-1.5 py-0.5 rounded flex-shrink-0",children:e.tipologia}),(0,t.jsx)("span",{className:"text-xs bg-gray-100 px-1.5 py-0.5 rounded flex-shrink-0",children:e.sezione}),(0,t.jsxs)("span",{className:"text-xs text-gray-600 flex-shrink-0",children:[e.metri_teorici,"m"]}),l&&(0,t.jsx)("span",{className:"text-xs bg-red-100 text-red-700 px-1.5 py-0.5 rounded flex-shrink-0 font-medium",children:"BLOCCATO"}),c&&(0,t.jsx)("span",{className:"text-xs bg-orange-100 text-orange-700 px-1.5 py-0.5 rounded flex-shrink-0 font-medium",children:"CAUSA OVER"}),(0,t.jsxs)("span",{className:"text-xs text-gray-500 truncate min-w-0",children:[e.ubicazione_partenza||"N/A"," → ",e.ubicazione_arrivo||"N/A"]})]}),s&&(0,t.jsxs)("div",{className:"flex items-center gap-1 flex-shrink-0",children:[(0,t.jsx)("label",{className:"text-xs text-gray-600",children:"m:"}),(0,t.jsx)("input",{type:"number",step:"0.1",min:"0",value:r,onChange:i=>{ea(e.id_cavo,i.target.value)},className:"w-16 px-1 py-1 border rounded text-xs",placeholder:"0"})]})]}),C[e.id_cavo]&&(0,t.jsx)("div",{className:"text-red-600 text-xs mt-1 ml-7",children:C[e.id_cavo]})]},e.id_cavo)})});return(0,t.jsx)(t.Fragment,{children:(0,t.jsx)(N.lG,{open:e,onOpenChange:en,children:(0,t.jsxs)(N.Cf,{className:"h-[85vh] w-full max-w-5xl overflow-hidden",style:{width:"950px !important",maxWidth:"95vw !important",minWidth:"850px"},children:[(0,t.jsxs)(N.c7,{className:"pb-0",children:[(0,t.jsxs)(N.L3,{className:"flex items-center gap-2 mb-0 text-lg",children:[(0,t.jsx)(B.A,{className:"h-5 w-5"}),"\uD83D\uDD25 NUOVO SISTEMA OVER - Aggiungi cavi alla bobina ",eo(a.id_bobina)]}),(0,t.jsx)(N.rr,{className:"mb-0 text-xs text-gray-600 mt-0",children:"Seleziona cavi e inserisci metri posati (SISTEMA AGGIORNATO)"})]}),(0,t.jsxs)("div",{className:"space-y-1 mt-2",children:[(0,t.jsx)("div",{className:"bg-gray-50 px-3 py-1 rounded text-sm",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsxs)("span",{children:[(0,t.jsxs)("strong",{children:["Bobina ",eo(a.id_bobina)]})," • ",a.tipologia," • ",a.sezione]}),(0,t.jsxs)("span",{children:["Residui: ",(0,t.jsxs)("strong",{children:[a.metri_residui,"m"]})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)("span",{children:["Selezionati: ",(0,t.jsx)("strong",{children:_.length})," cavi • ",(0,t.jsxs)("strong",{children:[ee.metriTotaliSelezionati.toFixed(1),"m"]})]}),ee.isOverState&&(0,t.jsx)("span",{className:"text-red-600 font-medium",children:"OVER!"})]})]})}),(0,t.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,t.jsxs)("div",{className:"flex-1 relative",children:[(0,t.jsx)(k.A,{className:"absolute left-2 top-2 h-4 w-4 text-gray-400"}),(0,t.jsx)(l.p,{placeholder:"Cerca cavi...",value:A,onChange:e=>E(e.target.value),className:"pl-8 h-8 text-sm"})]}),(0,t.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>{y([]),D({}),S({})},disabled:0===_.length,className:"h-8 px-3 text-sm",children:"Reset"})]}),x&&(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,t.jsx)(w.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,t.jsx)("span",{children:"Caricamento cavi..."})]}),!x&&(0,t.jsxs)(O.tU,{defaultValue:"compatibili",className:"w-full",children:[(0,t.jsxs)(O.j7,{className:"flex justify-center gap-6 bg-transparent border-0 h-auto p-0",children:[(0,t.jsxs)(O.Xi,{value:"compatibili",className:"tab-trigger flex items-center gap-2",children:[(0,t.jsx)("span",{className:"w-2 h-2 rounded-full bg-green-500"}),"Cavi Compatibili (",Q.totalCompatibili,")"]}),(0,t.jsxs)(O.Xi,{value:"incompatibili",className:"tab-trigger flex items-center gap-2",children:[(0,t.jsx)("span",{className:"w-2 h-2 rounded-full bg-orange-500"}),"Cavi Incompatibili (",Q.totalIncompatibili,")"]})]}),(0,t.jsx)(O.av,{value:"compatibili",className:"mt-4 w-full overflow-hidden",children:(0,t.jsx)("div",{className:"w-full overflow-hidden",children:el(Q.compatibili,!0)})}),(0,t.jsx)(O.av,{value:"incompatibili",className:"mt-4 w-full overflow-hidden",children:(0,t.jsx)("div",{className:"w-full overflow-hidden",children:el(Q.incompatibili,!1)})})]})]}),(0,t.jsxs)(N.Es,{className:"flex justify-between items-center",children:[(0,t.jsx)("div",{className:"text-sm text-gray-600",children:_.length>0?(()=>{let{metriResiduiSimulati:e,caviValidi:i,caviBloccati:s,cavoCheCausaOver:r}=et,n=_.reduce((e,i)=>e+parseFloat(z[i.id_cavo]||"0"),0),o=(a?.metri_residui||0)-e,l=_.filter(e=>e._isIncompatible).length;return(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{children:[_.length," cavi selezionati • ",n.toFixed(1),"m totali"]}),(0,t.jsxs)("div",{children:["✅ ",i.length," salvabili • ❌ ",s.length," bloccati"]}),(0,t.jsxs)("div",{children:["Usati: ",o.toFixed(1),"m / ",a?.metri_residui||0,"m",e<0&&r&&(0,t.jsxs)("span",{className:"text-orange-600 font-medium ml-2",children:["(OVER da ",r,": +",Math.abs(e).toFixed(1),"m)"]})]}),l>0&&(0,t.jsxs)("div",{className:"text-orange-600 font-medium",children:["⚠️ ",l," cavi incompatibili"]})]})})():(0,t.jsx)("div",{children:"Nessun cavo selezionato"})}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(n.$,{variant:"outline",onClick:en,disabled:b,children:"Annulla"}),(0,t.jsxs)(n.$,{onClick:er,disabled:b||0===_.length,className:ee.isOverState?"bg-orange-600 hover:bg-orange-700":"",children:[b&&(0,t.jsx)(w.A,{className:"mr-2 h-4 w-4 animate-spin"}),b?"Salvataggio...":ee.isOverState?`Salva ${_.length} cavi (OVER)`:`Salva ${_.length} cavi`]})]})]})]})})})}var L=a(53411),P=a(5336),U=a(48730);function V({bobine:e,filteredBobine:i,className:a}){let n=(0,s.useMemo)(()=>{let a=e.length,t=i.length,s=i.filter(e=>"Disponibile"===e.stato_bobina).length,r=i.filter(e=>"In uso"===e.stato_bobina).length,n=i.filter(e=>"Terminata"===e.stato_bobina).length,o=i.filter(e=>"Over"===e.stato_bobina).length,l=i.reduce((e,i)=>e+(i.metri_totali||0),0),c=i.reduce((e,i)=>e+(i.metri_residui||0),0),d=l-c,u=l>0?Math.round(d/l*100):0;return{totalBobine:a,filteredCount:t,disponibili:s,inUso:r,terminate:n,over:o,metriTotali:l,metriResidui:c,metriUtilizzati:d,percentualeUtilizzo:u}},[e,i]);return(0,t.jsx)(r.Zp,{className:a,children:(0,t.jsxs)(r.Wu,{className:"p-1.5",children:[(0,t.jsx)("div",{className:"flex items-center justify-between mb-1",children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5",children:[(0,t.jsx)(L.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,t.jsx)("span",{className:"text-xs font-semibold text-mariner-900",children:"Statistiche Bobine"})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-mariner-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(A.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-mariner-900 text-sm",children:n.filteredCount}),(0,t.jsxs)("div",{className:"text-xs text-mariner-600",children:["di ",n.totalBobine," bobine"]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-green-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(P.A,{className:"h-3.5 w-3.5 text-green-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-green-700 text-sm",children:n.disponibili}),(0,t.jsx)("div",{className:"text-xs text-green-600",children:"disponibili"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-yellow-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(U.A,{className:"h-3.5 w-3.5 text-yellow-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-yellow-700 text-sm",children:n.inUso}),(0,t.jsx)("div",{className:"text-xs text-yellow-600",children:"in uso"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-red-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(F.A,{className:"h-3.5 w-3.5 text-red-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-red-700 text-sm",children:n.terminate}),(0,t.jsx)("div",{className:"text-xs text-red-600",children:"terminate"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-red-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(z.A,{className:"h-3.5 w-3.5 text-red-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-red-700 text-sm",children:n.over}),(0,t.jsx)("div",{className:"text-xs text-red-600",children:"over"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-indigo-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)("div",{className:"h-3.5 w-3.5 flex items-center justify-center",children:(0,t.jsx)("div",{className:"h-2 w-2 bg-indigo-600 rounded-full"})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"font-bold text-indigo-700 text-sm",children:[n.metriUtilizzati.toLocaleString(),"m"]}),(0,t.jsxs)("div",{className:"text-xs text-indigo-600",children:["di ",n.metriTotali.toLocaleString(),"m"]})]})]})]}),n.filteredCount>0&&(0,t.jsxs)("div",{className:"mt-2 bg-gray-50 p-2 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex justify-between text-xs font-medium text-gray-700 mb-1",children:[(0,t.jsx)("span",{children:"Utilizzo Complessivo Bobine"}),(0,t.jsxs)("span",{className:`font-bold ${n.percentualeUtilizzo>=80?"text-amber-700":n.percentualeUtilizzo>=60?"text-orange-700":n.percentualeUtilizzo>=40?"text-yellow-700":"text-emerald-700"}`,children:[n.percentualeUtilizzo,"%"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:`h-2 rounded-full transition-all duration-500 ease-in-out ${n.percentualeUtilizzo>=80?"bg-gradient-to-r from-amber-500 to-amber-600":n.percentualeUtilizzo>=60?"bg-gradient-to-r from-orange-500 to-orange-600":n.percentualeUtilizzo>=40?"bg-gradient-to-r from-yellow-500 to-yellow-600":"bg-gradient-to-r from-emerald-500 to-emerald-600"}`,style:{width:`${Math.min(n.percentualeUtilizzo,100)}%`}})}),(0,t.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-0.5",children:[(0,t.jsx)("span",{children:"Metri utilizzati vs totali disponibili"}),(0,t.jsxs)("span",{children:[n.metriResidui.toLocaleString(),"m residui"]})]})]})]})})}var q=a(62688);let G=(0,q.A)("file-up",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M12 12v6",key:"3ahymv"}],["path",{d:"m15 15-3-3-3 3",key:"15xj92"}]]),J=(0,q.A)("file-down",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M12 18v-6",key:"17g6i2"}],["path",{d:"m9 15 3 3 3-3",key:"1npd3o"}]]);var H=a(63143),K=a(51215);function W({children:e,items:i,onAction:a,disabled:r=!1}){let[n,o]=(0,s.useState)(!1),[l,c]=(0,s.useState)({x:0,y:0}),[d,u]=(0,s.useState)(null),m=(0,s.useRef)(null),x=(0,s.useRef)(null),p=e=>{e.disabled||(o(!1),a(e.action,d))},b=e=>{switch(e){case"warning":return"text-amber-600 hover:bg-amber-50";case"danger":return"text-red-600 hover:bg-red-50";default:return"text-gray-700 hover:bg-gray-100"}};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{ref:x,onContextMenu:e=>{if(r)return;e.preventDefault(),e.stopPropagation(),x.current?.getBoundingClientRect();let a=e.clientX,t=e.clientY,s=40*i.length,n=window.innerWidth;c({x:a+200>n?a-200:a,y:t+s>window.innerHeight?t-s:t}),u(e.currentTarget.dataset),o(!0)},className:"w-full h-full",children:e}),n?(0,K.createPortal)((0,t.jsx)("div",{ref:m,className:"fixed z-50 min-w-[200px] bg-white border border-gray-200 rounded-md shadow-lg py-1",style:{left:l.x,top:l.y},children:i.map((e,i)=>e.separator?(0,t.jsx)("div",{className:"border-t border-gray-200 my-1"},`separator-${i}`):(0,t.jsxs)("button",{className:`w-full px-3 py-2 text-left text-sm flex items-center gap-2 transition-colors ${e.disabled?"text-gray-400 cursor-not-allowed":b(e.color)}`,onClick:()=>p(e),disabled:e.disabled,children:[e.icon&&(0,t.jsx)("span",{className:"w-4 h-4",children:e.icon}),(0,t.jsx)("span",{children:e.label})]},e.id))}),document.body):null]})}var Z=a(15391);function Y(){let[e,i]=(0,s.useState)(""),[a,b]=(0,s.useState)("all"),[N,_]=(0,s.useState)([]),[D,S]=(0,s.useState)(!0),[A,E]=(0,s.useState)(""),{user:F,cantiere:O,isLoading:M}=(0,u.A)(),[L,q]=(0,s.useState)(0),[K,Y]=(0,s.useState)(!1),[X,Q]=(0,s.useState)(!1),[ee,ei]=(0,s.useState)(!1),[ea,et]=(0,s.useState)(!1),[es,er]=(0,s.useState)(null),[en,eo]=(0,s.useState)(""),[el,ec]=(0,s.useState)(""),ed=async()=>{try{if(S(!0),E(""),!L||L<=0){E("Cantiere non selezionato. Seleziona un cantiere per visualizzare le bobine."),_([]);return}let e=await m.Fw.getBobine(L);_(e||[])}catch(e){E(e.response?.data?.detail||"Errore durante il caricamento delle bobine"),_([])}finally{S(!1)}},eu=e=>{er(e),et(!0)},em=e=>{er(e),Q(!0)},ex=e=>{er(e),ei(!0)},ep=e=>{eo(e),ed()},eb=e=>{ec(e)},eh=()=>{eo("Funzione import in sviluppo")},eg=()=>{eo("Funzione export in sviluppo")},ev=(e,i,a)=>{let s=e||p(i,a),r=(0,Z.t2)(s),n={disponibile:P.A,in_uso:U.A,terminata:z.A,over:z.A}[s?.toLowerCase()]||z.A;return(0,t.jsxs)(o.E,{className:`flex items-center gap-1 font-medium ${r.badge}`,title:j(s),children:[(0,t.jsx)(n,{className:`h-3 w-3 ${r.text}`}),s.toUpperCase()]})},ef=N.filter(i=>{let t=i.numero_bobina?.toLowerCase().includes(e.toLowerCase())||i.tipologia?.toLowerCase().includes(e.toLowerCase())||i.utility?.toLowerCase().includes(e.toLowerCase()),s=!0;if("all"!==a){let e=i.stato_bobina||p(i.metri_residui,i.metri_totali);switch(a){case"disponibile":s=e===x.DISPONIBILE;break;case"in_uso":s=e===x.IN_USO;break;case"esaurita":s=e===x.TERMINATA;break;case"over":s=e===x.OVER}}return t&&s});return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:[(0,t.jsxs)("div",{className:"max-w-[90%] mx-auto space-y-6",children:[(!L||L<=0)&&!M&&(0,t.jsxs)(d.Fc,{variant:"destructive",children:[(0,t.jsx)(z.A,{className:"h-4 w-4"}),(0,t.jsxs)(d.TN,{children:[(0,t.jsx)("strong",{children:"Attenzione:"})," Nessun cantiere selezionato. Seleziona un cantiere dal menu principale per visualizzare e gestire le bobine."]})]}),(0,t.jsx)(V,{bobine:N,filteredBobine:ef,className:"mb-6"}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(k.A,{className:"h-5 w-5"}),"Ricerca e Filtri"]})}),(0,t.jsx)(r.Wu,{children:(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)(l.p,{placeholder:"Cerca per bobina, tipologia o utility...",value:e,onChange:e=>i(e.target.value),className:"w-full"})}),(0,t.jsx)("div",{className:"flex gap-2",children:["all","disponibile","in_uso","esaurita","over"].map(e=>(0,t.jsx)(n.$,{variant:a===e?"default":"outline",size:"sm",onClick:()=>b(e),children:"all"===e?"Tutte":"disponibile"===e?"Disponibili":"in_uso"===e?"In Uso":"esaurita"===e?"Esaurite":"Over"},e))})]})})]}),(0,t.jsx)(W,{items:[{id:"import",label:"Importa Bobine",icon:(0,t.jsx)(G,{className:"h-4 w-4"}),action:"import",disabled:!L||L<=0},{id:"export",label:"Esporta Bobine",icon:(0,t.jsx)(J,{className:"h-4 w-4"}),action:"export",disabled:!L||L<=0},{id:"separator1",separator:!0},{id:"add_bobina",label:"Aggiungi Bobina",icon:(0,t.jsx)(y.A,{className:"h-4 w-4"}),action:"add_bobina",disabled:!L||L<=0}],onAction:(e,i)=>{switch(e){case"import":eh();break;case"export":eg();break;case"add_bobina":Y(!0)}},children:(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(r.ZB,{children:["Elenco Bobine (",ef.length,")"]}),(0,t.jsx)(r.BT,{children:"Gestione completa delle bobine con stato utilizzo e metrature. Clicca tasto destro per opzioni aggiuntive."})]}),(0,t.jsxs)(n.$,{size:"sm",onClick:()=>Y(!0),disabled:!L||L<=0,title:!L||L<=0?"Seleziona un cantiere per creare una bobina":"Crea nuova bobina",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Nuova Bobina"]})]})}),(0,t.jsx)(r.Wu,{children:(0,t.jsx)("div",{className:"rounded-md border",children:(0,t.jsxs)(c.XI,{children:[(0,t.jsx)(c.A0,{children:(0,t.jsxs)(c.Hj,{children:[(0,t.jsx)(c.nd,{children:"Bobina"}),(0,t.jsx)(c.nd,{children:"Utility"}),(0,t.jsx)(c.nd,{children:"Tipologia"}),(0,t.jsx)(c.nd,{children:"Formazione"}),(0,t.jsx)(c.nd,{children:"Metrature"}),(0,t.jsx)(c.nd,{children:"Utilizzo"}),(0,t.jsx)(c.nd,{children:"Stato"}),(0,t.jsx)(c.nd,{children:"Ubicazione"}),(0,t.jsx)(c.nd,{children:"Azioni"})]})}),(0,t.jsx)(c.BF,{children:D?(0,t.jsx)(c.Hj,{children:(0,t.jsx)(c.nA,{colSpan:9,className:"text-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)(w.A,{className:"h-4 w-4 animate-spin"}),"Caricamento bobine..."]})})}):A?(0,t.jsx)(c.Hj,{children:(0,t.jsx)(c.nA,{colSpan:9,className:"text-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2 text-red-600",children:[(0,t.jsx)(z.A,{className:"h-4 w-4"}),A]})})}):0===ef.length?(0,t.jsx)(c.Hj,{children:(0,t.jsx)(c.nA,{colSpan:9,className:"text-center py-8 text-slate-500",children:"Nessuna bobina trovata"})}):ef.map(e=>{let i=v(e.metri_residui,e.metri_totali),a=e.stato_bobina||p(e.metri_residui,e.metri_totali),s=h(a);return(0,t.jsxs)(c.Hj,{className:`transition-colors ${s}`,children:[(0,t.jsx)(c.nA,{className:"font-medium",children:e.numero_bobina||"-"}),(0,t.jsx)(c.nA,{children:e.utility||"-"}),(0,t.jsx)(c.nA,{children:e.tipologia||"-"}),(0,t.jsx)(c.nA,{children:(0,t.jsx)("div",{className:"text-sm font-medium",children:e.sezione||"-"})}),(0,t.jsx)(c.nA,{children:(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsxs)("div",{children:["Residui: ",(0,t.jsx)("span",{className:"font-medium",children:f(e.metri_residui)})]}),(0,t.jsxs)("div",{className:"text-slate-500",children:["Totali: ",f(e.metri_totali)]})]})}),(0,t.jsx)(c.nA,{children:(0,t.jsxs)("div",{className:"text-sm font-medium",children:[Math.round(i),"%"]})}),(0,t.jsx)(c.nA,{children:ev(e.stato_bobina,e.metri_residui,e.metri_totali)}),(0,t.jsx)(c.nA,{children:(0,t.jsx)(o.E,{variant:"outline",children:e.ubicazione_bobina||"Non specificata"})}),(0,t.jsx)(c.nA,{children:(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>eu(e),disabled:!g(a),title:a===x.OVER?"Bobina OVER - Non pu\xf2 accettare nuovi cavi":a===x.TERMINATA?"Bobina terminata - Non pu\xf2 accettare nuovi cavi":"Aggiungi cavo a bobina",className:g(a)?"":"opacity-50 cursor-not-allowed",children:(0,t.jsx)(B.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>em(e),title:a===x.OVER?"Modifica bobina (limitata per bobine OVER)":"Modifica bobina",children:(0,t.jsx)(H.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>ex(e),disabled:a===x.OVER||a!==x.DISPONIBILE,title:a===x.OVER?"Bobina OVER - Non pu\xf2 essere eliminata":a!==x.DISPONIBILE?"Solo bobine disponibili possono essere eliminate":"Elimina bobina",className:a===x.OVER||a!==x.DISPONIBILE?"opacity-50 cursor-not-allowed":"",children:(0,t.jsx)(I.A,{className:"h-4 w-4"})})]})})]},e.id_bobina)})})]})})})]})}),en&&(0,t.jsx)("div",{className:"fixed top-4 right-4 z-50",children:(0,t.jsxs)(d.Fc,{className:"bg-green-50 border-green-200",children:[(0,t.jsx)(P.A,{className:"h-4 w-4 text-green-600"}),(0,t.jsx)(d.TN,{className:"text-green-800",children:en})]})}),el&&(0,t.jsx)("div",{className:"fixed top-4 right-4 z-50",children:(0,t.jsxs)(d.Fc,{variant:"destructive",children:[(0,t.jsx)(z.A,{className:"h-4 w-4"}),(0,t.jsx)(d.TN,{children:el})]})})]}),(0,t.jsx)(C,{open:K,onClose:()=>Y(!1),cantiereId:L,onSuccess:ep,onError:eb}),(0,t.jsx)(T,{open:X,onClose:()=>Q(!1),bobina:es,cantiereId:L,onSuccess:e=>{eo(e),ed()},onError:e=>{ec(e)}}),(0,t.jsx)(R,{open:ee,onClose:()=>ei(!1),bobina:es,cantiereId:L,onSuccess:e=>{eo(e),ed()},onError:e=>{ec(e)}}),(0,t.jsx)($,{open:ea,onClose:()=>et(!1),bobina:es,cantiereId:L,onSuccess:ep,onError:eb})]})}},71902:(e,i,a)=>{"use strict";a.r(i),a.d(i,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs_2\\\\src\\\\app\\\\parco-cavi\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_2\\src\\app\\parco-cavi\\page.tsx","default")},72942:(e,i,a)=>{"use strict";a.d(i,{RG:()=>N,bL:()=>E,q7:()=>T});var t=a(43210),s=a(70569),r=a(9510),n=a(98599),o=a(11273),l=a(96963),c=a(14163),d=a(13495),u=a(65551),m=a(43),x=a(60687),p="rovingFocusGroup.onEntryFocus",b={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[g,v,f]=(0,r.N)(h),[j,N]=(0,o.A)(h,[f]),[_,y]=j(h),w=t.forwardRef((e,i)=>(0,x.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,x.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,x.jsx)(z,{...e,ref:i})})}));w.displayName=h;var z=t.forwardRef((e,i)=>{let{__scopeRovingFocusGroup:a,orientation:r,loop:o=!1,dir:l,currentTabStopId:g,defaultCurrentTabStopId:f,onCurrentTabStopIdChange:j,onEntryFocus:N,preventScrollOnEntryFocus:y=!1,...w}=e,z=t.useRef(null),D=(0,n.s)(i,z),C=(0,m.jH)(l),[S,E]=(0,u.i)({prop:g,defaultProp:f??null,onChange:j,caller:h}),[T,I]=t.useState(!1),F=(0,d.c)(N),R=v(a),O=t.useRef(!1),[B,k]=t.useState(0);return t.useEffect(()=>{let e=z.current;if(e)return e.addEventListener(p,F),()=>e.removeEventListener(p,F)},[F]),(0,x.jsx)(_,{scope:a,orientation:r,dir:C,loop:o,currentTabStopId:S,onItemFocus:t.useCallback(e=>E(e),[E]),onItemShiftTab:t.useCallback(()=>I(!0),[]),onFocusableItemAdd:t.useCallback(()=>k(e=>e+1),[]),onFocusableItemRemove:t.useCallback(()=>k(e=>e-1),[]),children:(0,x.jsx)(c.sG.div,{tabIndex:T||0===B?-1:0,"data-orientation":r,...w,ref:D,style:{outline:"none",...e.style},onMouseDown:(0,s.m)(e.onMouseDown,()=>{O.current=!0}),onFocus:(0,s.m)(e.onFocus,e=>{let i=!O.current;if(e.target===e.currentTarget&&i&&!T){let i=new CustomEvent(p,b);if(e.currentTarget.dispatchEvent(i),!i.defaultPrevented){let e=R().filter(e=>e.focusable);A([e.find(e=>e.active),e.find(e=>e.id===S),...e].filter(Boolean).map(e=>e.ref.current),y)}}O.current=!1}),onBlur:(0,s.m)(e.onBlur,()=>I(!1))})})}),D="RovingFocusGroupItem",C=t.forwardRef((e,i)=>{let{__scopeRovingFocusGroup:a,focusable:r=!0,active:n=!1,tabStopId:o,children:d,...u}=e,m=(0,l.B)(),p=o||m,b=y(D,a),h=b.currentTabStopId===p,f=v(a),{onFocusableItemAdd:j,onFocusableItemRemove:N,currentTabStopId:_}=b;return t.useEffect(()=>{if(r)return j(),()=>N()},[r,j,N]),(0,x.jsx)(g.ItemSlot,{scope:a,id:p,focusable:r,active:n,children:(0,x.jsx)(c.sG.span,{tabIndex:h?0:-1,"data-orientation":b.orientation,...u,ref:i,onMouseDown:(0,s.m)(e.onMouseDown,e=>{r?b.onItemFocus(p):e.preventDefault()}),onFocus:(0,s.m)(e.onFocus,()=>b.onItemFocus(p)),onKeyDown:(0,s.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void b.onItemShiftTab();if(e.target!==e.currentTarget)return;let i=function(e,i,a){var t;let s=(t=e.key,"rtl"!==a?t:"ArrowLeft"===t?"ArrowRight":"ArrowRight"===t?"ArrowLeft":t);if(!("vertical"===i&&["ArrowLeft","ArrowRight"].includes(s))&&!("horizontal"===i&&["ArrowUp","ArrowDown"].includes(s)))return S[s]}(e,b.orientation,b.dir);if(void 0!==i){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let a=f().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===i)a.reverse();else if("prev"===i||"next"===i){"prev"===i&&a.reverse();let t=a.indexOf(e.currentTarget);a=b.loop?function(e,i){return e.map((a,t)=>e[(i+t)%e.length])}(a,t+1):a.slice(t+1)}setTimeout(()=>A(a))}}),children:"function"==typeof d?d({isCurrentTabStop:h,hasTabStop:null!=_}):d})})});C.displayName=D;var S={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function A(e,i=!1){let a=document.activeElement;for(let t of e)if(t===a||(t.focus({preventScroll:i}),document.activeElement!==a))return}var E=w,T=C},74075:e=>{"use strict";e.exports=require("zlib")},75108:(e,i,a)=>{Promise.resolve().then(a.bind(a,71902))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82724:(e,i,a)=>{Promise.resolve().then(a.bind(a,64435))},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},99270:(e,i,a)=>{"use strict";a.d(i,{A:()=>t});let t=(0,a(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var i=require("../../webpack-runtime.js");i.C(e);var a=e=>i(i.s=e),t=i.X(0,[447,588,658,142,400,340,415,109],()=>a(51895));module.exports=t})();