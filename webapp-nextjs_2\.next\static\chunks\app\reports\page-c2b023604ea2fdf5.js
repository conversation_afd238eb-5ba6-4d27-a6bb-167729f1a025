(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5022],{16294:(e,t,s)=>{Promise.resolve().then(s.bind(s,79680))},17313:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>c,av:()=>d,j7:()=>o,tU:()=>n});var i=s(95155),a=s(12115),r=s(60704),l=s(59434);let n=r.bL,o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,i.jsx)(r.B8,{ref:t,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...a})});o.displayName=r.B8.displayName;let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,i.jsx)(r.l9,{ref:t,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...a})});c.displayName=r.l9.displayName;let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,i.jsx)(r.UC,{ref:t,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...a})});d.displayName=r.UC.displayName},24944:(e,t,s)=>{"use strict";s.d(t,{k:()=>l});var i=s(95155);s(12115);var a=s(55863),r=s(59434);function l(e){let{className:t,value:s,...l}=e;return(0,i.jsx)(a.bL,{"data-slot":"progress",className:(0,r.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",t),...l,children:(0,i.jsx)(a.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(s||0),"%)")}})})}},26126:(e,t,s)=>{"use strict";s.d(t,{E:()=>o});var i=s(95155);s(12115);var a=s(99708),r=s(74466),l=s(59434);let n=(0,r.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:s,asChild:r=!1,...o}=e,c=r?a.DX:"span";return(0,i.jsx)(c,{"data-slot":"badge",className:(0,l.cn)(n({variant:s}),t),...o})}},30285:(e,t,s)=>{"use strict";s.d(t,{$:()=>o,r:()=>n});var i=s(95155);s(12115);var a=s(99708),r=s(74466),l=s(59434);let n=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:s,size:r,asChild:o=!1,...c}=e,d=o?a.DX:"button";return(0,i.jsx)(d,{"data-slot":"button",className:(0,l.cn)(n({variant:s,size:r,className:t})),...c})}},59434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>r});var i=s(52596),a=s(39688);function r(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,i.$)(t))}},66695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>n,Zp:()=>r,aR:()=>l});var i=s(95155);s(12115);var a=s(59434);function r(e){let{className:t,...s}=e;return(0,i.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function l(e){let{className:t,...s}=e;return(0,i.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function n(e){let{className:t,...s}=e;return(0,i.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...s})}function o(e){let{className:t,...s}=e;return(0,i.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...s})}function c(e){let{className:t,...s}=e;return(0,i.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...s})}},79680:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>R});var i=s(95155),a=s(12115),r=s(66695),l=s(30285),n=s(26126),o=s(24944),c=s(17313),d=s(40283),x=s(25731),m=s(53904),h=s(51154),u=s(85339),p=s(16785),j=s(57434),b=s(37108),v=s(71539),g=s(40646),N=s(33109),f=s(79397),w=s(14186),y=s(69074),_=s(91788),z=s(1243),A=s(83540),B=s(93774),k=s(94754),C=s(96025),S=s(16238),T=s(94517),Z=s(17398);function R(){var e,t,s,R,P,I,E,D,M,W,L,F,O;let[q,U]=(0,a.useState)("avanzamento"),[Q,X]=(0,a.useState)("month"),[$,G]=(0,a.useState)(null),[V,K]=(0,a.useState)(null),[H,J]=(0,a.useState)(null),[Y,ee]=(0,a.useState)(null),[et,es]=(0,a.useState)(!0),[ei,ea]=(0,a.useState)(""),{user:er,cantiere:el,isLoading:en}=(0,d.A)();(0,a.useEffect)(()=>{let e=async()=>{es(!0);try{let e=null==el?void 0:el.id_cantiere;if(localStorage.getItem("token"),!e){ea('Nessun cantiere selezionato. Seleziona un cantiere da "Gestisci Cantieri".'),es(!1);return}let t=function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e4;return Promise.race([fetch(e,t),new Promise((t,i)=>setTimeout(()=>i(Error("Timeout dopo ".concat(s/1e3,"s per ").concat(e))),s))])},s=t("http://localhost:8001/api/reports/".concat(e,"/progress?formato=video"),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}}).then(e=>e.json()).then(e=>e).catch(e=>({content:null,error:e.message.includes("Timeout")?"Timeout API Progress":"Errore API Progress"})),i=t("http://localhost:8001/api/reports/".concat(e,"/boq?formato=video"),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}}).then(e=>e.json()).then(e=>e).catch(e=>({content:null,error:e.message.includes("Timeout")?"Timeout API BOQ":"Errore API BOQ"})),a=t("http://localhost:8001/api/reports/".concat(e,"/storico-bobine?formato=video"),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}}).then(e=>e.json()).then(e=>e).catch(e=>({content:null,error:e.message.includes("Timeout")?"Timeout API Utilizzo Bobine":"Errore API Utilizzo Bobine"})),[r,l,n]=await Promise.all([s,i,a]);ee(r),K(l),J(n);let o=[r,l,n].some(e=>{var t;return null==e||null==(t=e.error)?void 0:t.includes("Timeout")});(null==r?void 0:r.content)||(null==l?void 0:l.content)||(null==n?void 0:n.content)||r||l||n?o?ea("Alcuni report hanno riscontrato timeout. I dati disponibili sono mostrati sotto."):ea(""):ea("Errore nel caricamento dei report. Riprova pi\xf9 tardi."),es(!1)}catch(e){ea("Errore nel caricamento dei report. Riprova pi\xf9 tardi.")}finally{es(!1)}};en||((null==el?void 0:el.id_cantiere)?e():(es(!1),ea('Nessun cantiere selezionato. Seleziona un cantiere da "Gestisci Cantieri".')))},[null==el?void 0:el.id_cantiere,en]);let eo=()=>{(null==el?void 0:el.id_cantiere)&&(es(!0),ea(""),ee(null),K(null),J(null),setTimeout(()=>{window.location.reload()},100))},ec=async function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{let t,s=null==el?void 0:el.id_cantiere;if(!s)return;switch(e){case"progress":t=await x.ug.getReportProgress(s);break;case"boq":t=await x.ug.getReportBOQ(s);break;case"utilizzo-bobine":t=await x.ug.getReportUtilizzoBobine(s);break;default:return}t.data.file_url&&window.open(t.data.file_url,"_blank")}catch(e){}};return(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,i.jsxs)("div",{className:"max-w-[90%] mx-auto space-y-6",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-slate-900",children:"Reports"}),(0,i.jsx)("p",{className:"text-slate-600 mt-1",children:(null==el?void 0:el.commessa)?"Cantiere: ".concat(el.commessa):"Seleziona un cantiere per visualizzare i report"})]}),(0,i.jsx)("div",{className:"flex gap-2",children:(0,i.jsxs)(l.$,{variant:"outline",size:"sm",onClick:eo,children:[(0,i.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Aggiorna"]})})]}),et||en?(0,i.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(h.A,{className:"h-6 w-6 animate-spin"}),(0,i.jsx)("span",{children:"Caricamento report..."})]})}):ei?(0,i.jsxs)("div",{className:"p-6 border border-amber-200 rounded-lg bg-amber-50",children:[(0,i.jsxs)("div",{className:"flex items-center mb-4",children:[(0,i.jsx)(u.A,{className:"h-5 w-5 text-amber-600 mr-2"}),(0,i.jsx)("span",{className:"text-amber-800 font-medium",children:ei.includes("Nessun cantiere selezionato")||ei.includes("Cantiere non selezionato")?"Cantiere non selezionato":ei.includes("timeout")||ei.includes("Timeout")?"Timeout API":"Errore caricamento report"})]}),(0,i.jsx)("p",{className:"text-amber-700 mb-4",children:ei}),ei.includes("timeout")||ei.includes("Timeout")?(0,i.jsx)("div",{className:"mb-4 p-3 bg-blue-50 border border-blue-200 rounded",children:(0,i.jsxs)("p",{className:"text-blue-800 text-sm",children:["\uD83D\uDCA1 ",(0,i.jsx)("strong",{children:"Suggerimento:"})," Le API stanno impiegando pi\xf9 tempo del previsto. Prova ad aggiornare la pagina o riprova tra qualche minuto."]})}):null,(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsx)(l.$,{onClick:()=>window.location.href="/cantieri",className:"bg-amber-600 hover:bg-amber-700 text-white",children:"Gestisci Cantieri"}),(0,i.jsxs)(l.$,{variant:"outline",onClick:eo,className:"border-amber-600 text-amber-700 hover:bg-amber-100",children:[(0,i.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Riprova"]})]})]}):(0,i.jsxs)(c.tU,{value:q,onValueChange:U,className:"w-full",children:[(0,i.jsxs)(c.j7,{className:"grid w-full grid-cols-4",children:[(0,i.jsxs)(c.Xi,{value:"avanzamento",className:"flex items-center gap-2",children:[(0,i.jsx)(p.A,{className:"h-4 w-4"}),"Avanzamento"]}),(0,i.jsxs)(c.Xi,{value:"boq",className:"flex items-center gap-2",children:[(0,i.jsx)(j.A,{className:"h-4 w-4"}),"BOQ"]}),(0,i.jsxs)(c.Xi,{value:"bobine",className:"flex items-center gap-2",children:[(0,i.jsx)(b.A,{className:"h-4 w-4"}),"Bobine"]}),(0,i.jsxs)(c.Xi,{value:"produttivita",className:"flex items-center gap-2",children:[(0,i.jsx)(v.A,{className:"h-4 w-4"}),"Produttivit\xe0"]})]}),(0,i.jsx)(c.av,{value:"avanzamento",className:"space-y-6",children:(null==Y?void 0:Y.content)?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,i.jsxs)(r.Zp,{className:"border-l-4 border-l-blue-500",children:[(0,i.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,i.jsx)(r.ZB,{className:"text-sm font-medium text-slate-600",children:"Metri Totali"}),(0,i.jsx)(p.A,{className:"h-4 w-4 text-blue-500"})]}),(0,i.jsxs)(r.Wu,{children:[(0,i.jsxs)("div",{className:"text-2xl font-bold text-slate-900",children:[(null==(e=Y.content.metri_totali)?void 0:e.toLocaleString())||0,"m"]}),(0,i.jsxs)("p",{className:"text-xs text-slate-500 mt-2",children:[Y.content.totale_cavi||0," cavi totali"]})]})]}),(0,i.jsxs)(r.Zp,{className:"border-l-4 border-l-green-500",children:[(0,i.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,i.jsx)(r.ZB,{className:"text-sm font-medium text-slate-600",children:"Metri Posati"}),(0,i.jsx)(g.A,{className:"h-4 w-4 text-green-500"})]}),(0,i.jsxs)(r.Wu,{children:[(0,i.jsxs)("div",{className:"text-2xl font-bold text-slate-900",children:[(null==(t=Y.content.metri_posati)?void 0:t.toLocaleString())||0,"m"]}),(0,i.jsx)(o.k,{value:Y.content.percentuale_avanzamento||0,className:"mt-2"}),(0,i.jsxs)("p",{className:"text-xs text-slate-500 mt-2",children:[(null==(s=Y.content.percentuale_avanzamento)?void 0:s.toFixed(1))||0,"% completato"]})]})]}),(0,i.jsxs)(r.Zp,{className:"border-l-4 border-l-purple-500",children:[(0,i.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,i.jsx)(r.ZB,{className:"text-sm font-medium text-slate-600",children:"Media Giornaliera"}),(0,i.jsx)(N.A,{className:"h-4 w-4 text-purple-500"})]}),(0,i.jsxs)(r.Wu,{children:[(0,i.jsxs)("div",{className:"text-2xl font-bold text-slate-900",children:[(null==(R=Y.content.media_giornaliera)?void 0:R.toFixed(1))||0,"m"]}),(0,i.jsx)("p",{className:"text-xs text-slate-500",children:"metri/giorno"}),(0,i.jsxs)("div",{className:"flex items-center mt-2",children:[(0,i.jsx)(f.A,{className:"h-3 w-3 text-purple-500 mr-1"}),(0,i.jsxs)("span",{className:"text-xs text-purple-600",children:[Y.content.giorni_lavorativi_effettivi||0," giorni attivi"]})]})]})]}),(0,i.jsxs)(r.Zp,{className:"border-l-4 border-l-orange-500",children:[(0,i.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,i.jsx)(r.ZB,{className:"text-sm font-medium text-slate-600",children:"Completamento"}),(0,i.jsx)(w.A,{className:"h-4 w-4 text-orange-500"})]}),(0,i.jsxs)(r.Wu,{children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-slate-900",children:Y.content.data_completamento||"N/A"}),(0,i.jsx)("p",{className:"text-xs text-slate-500",children:"stima completamento"}),(0,i.jsxs)("div",{className:"flex items-center mt-2",children:[(0,i.jsx)(y.A,{className:"h-3 w-3 text-orange-500 mr-1"}),(0,i.jsxs)("span",{className:"text-xs text-orange-600",children:[Y.content.giorni_stimati||0," giorni rimanenti"]})]})]})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{className:"flex flex-row items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(r.ZB,{children:"Posa Recente"}),(0,i.jsx)(r.BT,{children:"Ultimi 10 giorni di attivit\xe0"})]}),(0,i.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>ec("progress","pdf"),children:[(0,i.jsx)(_.A,{className:"h-4 w-4 mr-2"}),"PDF"]})]}),(0,i.jsx)(r.Wu,{children:(0,i.jsx)(A.u,{width:"100%",height:300,children:(0,i.jsxs)(B.E,{data:Y.content.posa_recente||[],children:[(0,i.jsx)(k.d,{strokeDasharray:"3 3"}),(0,i.jsx)(C.W,{dataKey:"data"}),(0,i.jsx)(S.h,{}),(0,i.jsx)(T.m,{}),(0,i.jsx)(Z.y,{dataKey:"metri",fill:"#3b82f6",name:"Metri Posati"})]})})})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsx)(r.ZB,{children:"Stato Cavi"}),(0,i.jsx)(r.BT,{children:"Distribuzione per stato di avanzamento"})]}),(0,i.jsx)(r.Wu,{children:(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("span",{className:"text-sm font-medium",children:"Cavi Posati"}),(0,i.jsx)(n.E,{variant:"secondary",children:Y.content.cavi_posati||0})]}),(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("span",{className:"text-sm font-medium",children:"Cavi Rimanenti"}),(0,i.jsx)(n.E,{variant:"outline",children:Y.content.cavi_rimanenti||0})]}),(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("span",{className:"text-sm font-medium",children:"Percentuale Cavi"}),(0,i.jsxs)(n.E,{variant:"default",children:[(null==(P=Y.content.percentuale_cavi)?void 0:P.toFixed(1))||0,"%"]})]})]})})]})]})]}):(0,i.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,i.jsx)(p.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-300"}),(0,i.jsx)("p",{children:"Nessun dato di avanzamento disponibile"})]})}),(0,i.jsx)(c.av,{value:"boq",className:"space-y-6",children:(null==V?void 0:V.error)?(0,i.jsxs)("div",{className:"text-center py-12",children:[(0,i.jsx)(u.A,{className:"h-12 w-12 mx-auto mb-4 text-amber-500"}),(0,i.jsx)("h3",{className:"text-lg font-semibold text-slate-700 mb-2",children:"API BOQ Temporaneamente Non Disponibile"}),(0,i.jsx)("p",{className:"text-slate-500 mb-4",children:"Il servizio BOQ sta riscontrando problemi di performance."}),(0,i.jsx)("p",{className:"text-sm text-slate-400",children:"Stiamo lavorando per risolvere il problema."})]}):(null==V?void 0:V.content)?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold",children:"\uD83D\uDCCB Bill of Quantities - Distinta Materiali"}),(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Riepilogo completo dei materiali per tipologia di cavo"})]}),(0,i.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>ec("boq","excel"),children:[(0,i.jsx)(_.A,{className:"h-4 w-4 mr-2"}),"Excel"]})]}),V.content.metri_orfani&&V.content.metri_orfani.metri_orfani_totali>0&&(0,i.jsxs)(r.Zp,{className:"border-red-200 bg-red-50",children:[(0,i.jsx)(r.aR,{className:"pb-3",children:(0,i.jsxs)(r.ZB,{className:"text-red-700 flex items-center",children:[(0,i.jsx)(z.A,{className:"h-5 w-5 mr-2"}),"\uD83D\uDEA8 METRI POSATI SENZA TRACCIABILIT\xc0 BOBINA"]})}),(0,i.jsxs)(r.Wu,{children:[(0,i.jsxs)("p",{className:"text-red-800 font-medium mb-2",children:[(0,i.jsxs)("strong",{children:[V.content.metri_orfani.metri_orfani_totali,"m"]})," installati con BOBINA_VUOTA (",V.content.metri_orfani.num_cavi_orfani," cavi)"]}),(0,i.jsx)("div",{className:"text-sm text-red-700 space-y-1",children:Array.isArray(V.content.metri_orfani.dettaglio_per_categoria)?V.content.metri_orfani.dettaglio_per_categoria.map((e,t)=>(0,i.jsxs)("div",{children:["• ",(0,i.jsxs)("strong",{children:[e.tipologia," ",e.formazione]}),": ",e.metri_orfani,"m (",e.num_cavi," cavi)"]},t)):(0,i.jsx)("div",{children:"Dettaglio metri orfani non disponibile"})}),(0,i.jsx)("div",{className:"mt-3 p-3 bg-amber-50 border border-amber-200 rounded",children:(0,i.jsxs)("p",{className:"text-amber-800 text-sm",children:["⚠️ ",(0,i.jsx)("strong",{children:"NOTA:"})," I metri orfani NON sono inclusi nel calcolo acquisti. Prima di acquistare, verificare se questi metri possono essere associati a bobine esistenti."]})})]})]}),V.content.riepilogo&&(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,i.jsxs)(r.Zp,{className:"border-l-4 border-l-blue-500",children:[(0,i.jsx)(r.aR,{className:"pb-2",children:(0,i.jsx)(r.ZB,{className:"text-sm font-medium text-slate-600",children:"Metri da Acquistare"})}),(0,i.jsxs)(r.Wu,{children:[(0,i.jsxs)("div",{className:"text-2xl font-bold text-blue-600",children:[(null==(I=V.content.riepilogo.totale_metri_mancanti)?void 0:I.toLocaleString())||0,"m"]}),(0,i.jsx)("p",{className:"text-xs text-slate-500 mt-1",children:"per completamento progetto"})]})]}),(0,i.jsxs)(r.Zp,{className:"border-l-4 border-l-green-500",children:[(0,i.jsx)(r.aR,{className:"pb-2",children:(0,i.jsx)(r.ZB,{className:"text-sm font-medium text-slate-600",children:"Metri Residui"})}),(0,i.jsxs)(r.Wu,{children:[(0,i.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[(null==(E=V.content.riepilogo.totale_metri_residui)?void 0:E.toLocaleString())||0,"m"]}),(0,i.jsx)("p",{className:"text-xs text-slate-500 mt-1",children:"disponibili in magazzino"})]})]}),(0,i.jsxs)(r.Zp,{className:"border-l-4 border-l-purple-500",children:[(0,i.jsx)(r.aR,{className:"pb-2",children:(0,i.jsx)(r.ZB,{className:"text-sm font-medium text-slate-600",children:"Completamento"})}),(0,i.jsxs)(r.Wu,{children:[(0,i.jsxs)("div",{className:"text-2xl font-bold text-purple-600",children:[(null==(D=V.content.riepilogo.percentuale_completamento)?void 0:D.toFixed(1))||0,"%"]}),(0,i.jsx)("p",{className:"text-xs text-slate-500 mt-1",children:"progetto completato"})]})]}),(0,i.jsxs)(r.Zp,{className:"border-l-4 border-l-orange-500",children:[(0,i.jsx)(r.aR,{className:"pb-2",children:(0,i.jsx)(r.ZB,{className:"text-sm font-medium text-slate-600",children:"Categorie"})}),(0,i.jsxs)(r.Wu,{children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:V.content.riepilogo.categorie_necessitano_acquisto||0}),(0,i.jsx)("p",{className:"text-xs text-slate-500 mt-1",children:"necessitano acquisto"})]})]})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsx)(r.ZB,{children:"Distinta Materiali"}),(0,i.jsx)(r.BT,{children:"Fabbisogno materiali raggruppati per tipologia e formazione"})]}),(0,i.jsx)(r.Wu,{children:(0,i.jsx)("div",{className:"overflow-x-auto",children:(0,i.jsxs)("table",{className:"w-full text-sm",children:[(0,i.jsx)("thead",{children:(0,i.jsxs)("tr",{className:"border-b",children:[(0,i.jsx)("th",{className:"text-left p-2",children:"Tipologia"}),(0,i.jsx)("th",{className:"text-left p-2",children:"Formazione"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Cavi"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Metri Teorici"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Metri Posati"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Metri da Posare"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Bobine"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Metri Residui"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Metri Mancanti"}),(0,i.jsx)("th",{className:"text-center p-2",children:"Acquisto"})]})}),(0,i.jsx)("tbody",{children:null==(M=V.content.distinta_materiali)?void 0:M.map((e,t)=>{var s,a,r,l,o;return(0,i.jsxs)("tr",{className:"border-b hover:bg-slate-50 ".concat(e.ha_bobina_vuota?"bg-red-50":""),children:[(0,i.jsx)("td",{className:"p-2 font-medium",children:e.tipologia}),(0,i.jsx)("td",{className:"p-2",children:e.formazione}),(0,i.jsx)("td",{className:"p-2 text-right",children:e.num_cavi}),(0,i.jsx)("td",{className:"p-2 text-right",children:null==(s=e.metri_teorici_totali)?void 0:s.toLocaleString()}),(0,i.jsx)("td",{className:"p-2 text-right",children:null==(a=e.metri_reali_posati)?void 0:a.toLocaleString()}),(0,i.jsx)("td",{className:"p-2 text-right",children:null==(r=e.metri_da_posare)?void 0:r.toLocaleString()}),(0,i.jsx)("td",{className:"p-2 text-right",children:e.num_bobine}),(0,i.jsx)("td",{className:"p-2 text-right",children:null==(l=e.metri_residui)?void 0:l.toLocaleString()}),(0,i.jsx)("td",{className:"p-2 text-right font-medium",children:e.metri_mancanti>0?(0,i.jsxs)("span",{className:"text-red-600",children:[null==(o=e.metri_mancanti)?void 0:o.toLocaleString(),"m"]}):(0,i.jsx)("span",{className:"text-green-600",children:"0m"})}),(0,i.jsx)("td",{className:"p-2 text-center",children:e.necessita_acquisto?(0,i.jsx)(n.E,{variant:"destructive",children:"S\xec"}):(0,i.jsx)(n.E,{variant:"secondary",children:"No"})})]},t)})})]})})})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsx)(r.ZB,{children:"Bobine Disponibili"}),(0,i.jsx)(r.BT,{children:"Inventario bobine per tipologia"})]}),(0,i.jsx)(r.Wu,{children:(0,i.jsx)("div",{className:"overflow-x-auto",children:(0,i.jsxs)("table",{className:"w-full text-sm",children:[(0,i.jsx)("thead",{children:(0,i.jsxs)("tr",{className:"border-b",children:[(0,i.jsx)("th",{className:"text-left p-2",children:"Tipologia"}),(0,i.jsx)("th",{className:"text-left p-2",children:"Formazione"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Numero Bobine"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Metri Disponibili"})]})}),(0,i.jsx)("tbody",{children:null==(W=V.content.bobine_per_tipo)?void 0:W.map((e,t)=>{var s;return(0,i.jsxs)("tr",{className:"border-b hover:bg-slate-50",children:[(0,i.jsx)("td",{className:"p-2 font-medium",children:e.tipologia}),(0,i.jsx)("td",{className:"p-2",children:e.formazione}),(0,i.jsx)("td",{className:"p-2 text-right",children:e.num_bobine}),(0,i.jsx)("td",{className:"p-2 text-right",children:(0,i.jsx)("span",{className:e.metri_disponibili>0?"text-green-600":"text-red-600",children:null==(s=e.metri_disponibili)?void 0:s.toLocaleString()})})]},t)})})]})})})]})]}):(0,i.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,i.jsx)(j.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-300"}),(0,i.jsx)("p",{children:"Nessun dato BOQ disponibile"})]})}),(0,i.jsx)(c.av,{value:"bobine",className:"space-y-6",children:(null==H?void 0:H.content)?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold",children:"Utilizzo Bobine"}),(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Stato e utilizzo delle bobine"})]}),(0,i.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>ec("utilizzo-bobine","excel"),children:[(0,i.jsx)(_.A,{className:"h-4 w-4 mr-2"}),"Excel"]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,i.jsx)(r.ZB,{className:"text-sm font-medium text-slate-600",children:"Totale Bobine"}),(0,i.jsx)(b.A,{className:"h-4 w-4 text-blue-500"})]}),(0,i.jsxs)(r.Wu,{children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-slate-900",children:H.content.totale_bobine||0}),(0,i.jsx)("p",{className:"text-xs text-slate-500",children:"bobine nel cantiere"})]})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,i.jsx)(r.ZB,{className:"text-sm font-medium text-slate-600",children:"Bobine Attive"}),(0,i.jsx)(f.A,{className:"h-4 w-4 text-green-500"})]}),(0,i.jsxs)(r.Wu,{children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-slate-900",children:(null==(L=H.content.bobine)?void 0:L.filter(e=>"In uso"===e.stato||"Disponibile"===e.stato).length)||0}),(0,i.jsx)("p",{className:"text-xs text-slate-500",children:"disponibili/in uso"})]})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,i.jsx)(r.ZB,{className:"text-sm font-medium text-slate-600",children:"Utilizzo Medio"}),(0,i.jsx)(N.A,{className:"h-4 w-4 text-purple-500"})]}),(0,i.jsxs)(r.Wu,{children:[(0,i.jsxs)("div",{className:"text-2xl font-bold text-slate-900",children:[(null==(F=H.content.bobine)?void 0:F.length)>0?(H.content.bobine.reduce((e,t)=>e+(t.percentuale_utilizzo||0),0)/H.content.bobine.length).toFixed(1):0,"%"]}),(0,i.jsx)("p",{className:"text-xs text-slate-500",children:"utilizzo medio"})]})]})]}),(0,i.jsxs)(r.Zp,{children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsx)(r.ZB,{children:"Dettaglio Bobine"}),(0,i.jsx)(r.BT,{children:"Stato dettagliato di tutte le bobine"})]}),(0,i.jsx)(r.Wu,{children:(0,i.jsx)("div",{className:"overflow-x-auto",children:(0,i.jsxs)("table",{className:"w-full text-sm",children:[(0,i.jsx)("thead",{children:(0,i.jsxs)("tr",{className:"border-b",children:[(0,i.jsx)("th",{className:"text-left p-2",children:"Codice"}),(0,i.jsx)("th",{className:"text-left p-2",children:"Tipologia"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Metri Totali"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Metri Utilizzati"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Metri Residui"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Utilizzo %"}),(0,i.jsx)("th",{className:"text-left p-2",children:"Stato"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Cavi"})]})}),(0,i.jsx)("tbody",{children:null==(O=H.content.bobine)?void 0:O.map((e,t)=>{var s,a,r,l;return(0,i.jsxs)("tr",{className:"border-b hover:bg-slate-50",children:[(0,i.jsx)("td",{className:"p-2 font-medium",children:e.codice}),(0,i.jsx)("td",{className:"p-2",children:e.tipologia}),(0,i.jsx)("td",{className:"p-2 text-right",children:null==(s=e.metri_totali)?void 0:s.toLocaleString()}),(0,i.jsx)("td",{className:"p-2 text-right",children:null==(a=e.metri_utilizzati)?void 0:a.toLocaleString()}),(0,i.jsx)("td",{className:"p-2 text-right",children:null==(r=e.metri_residui)?void 0:r.toLocaleString()}),(0,i.jsx)("td",{className:"p-2 text-right",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsxs)("span",{children:[null==(l=e.percentuale_utilizzo)?void 0:l.toFixed(1),"%"]}),(0,i.jsx)(o.k,{value:Math.min(e.percentuale_utilizzo||0,100),className:"w-16 h-2"})]})}),(0,i.jsx)("td",{className:"p-2",children:(0,i.jsx)(n.E,{variant:"Disponibile"===e.stato?"default":"In uso"===e.stato?"secondary":"Terminata"===e.stato?"outline":"Over"===e.stato?"destructive":"outline",children:e.stato})}),(0,i.jsx)("td",{className:"p-2 text-right",children:e.totale_cavi_associati||0})]},t)})})]})})})]})]}):(0,i.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,i.jsx)(b.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-300"}),(0,i.jsx)("p",{children:"Nessun dato bobine disponibile"})]})}),(0,i.jsx)(c.av,{value:"produttivita",className:"space-y-6",children:(0,i.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,i.jsx)(v.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-300"}),(0,i.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Produttivit\xe0"}),(0,i.jsx)("p",{children:"Funzionalit\xe0 in fase di sviluppo"}),(0,i.jsx)("p",{className:"text-sm mt-2",children:"Includer\xe0 calcoli IAP, statistiche team e analisi performance"})]})})]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3464,3455,8034,1537,283,8441,1684,7358],()=>t(16294)),_N_E=e.O()}]);