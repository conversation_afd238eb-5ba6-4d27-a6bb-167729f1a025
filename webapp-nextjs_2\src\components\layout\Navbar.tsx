'use client'

import { useState, useEffect, useRef } from 'react'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { PrimaryButton, QuickButton } from '@/components/ui/animated-button'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/AuthContext'
import {
  Cable,
  Home,
  Activity,
  BarChart3,
  Settings,
  Users,
  Menu,
  X,
  Building2,
  ClipboardList,
  FileText,
  LogOut,
  Package,
  ChevronDown,
  Upload,
  Download,
  Loader2,
  Zap,
  Sparkles,
  ArrowRight
} from 'lucide-react'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'

const getNavigation = (userRole: string | undefined, isImpersonating: boolean, impersonatedUser: any, cantiereId?: number) => {
  // Home button - testo personalizzato come nella webapp originale
  const homeButton = {
    name: userRole === 'owner' ? "Menu Admin" :
          userRole === 'user' ? "Lista Cantieri" :
          userRole === 'cantieri_user' ? "Gestione Cavi" : "Home",
    href: userRole === 'owner' ? '/admin' :
          userRole === 'user' ? '/cantieri' :
          userRole === 'cantieri_user' ? '/cavi' : '/',
    icon: Home
  }

  if (userRole === 'owner' && !isImpersonating) {
    // Solo amministratore - solo il pulsante Home che va al pannello admin
    return [homeButton]
  }

  if (userRole === 'user' || (isImpersonating && impersonatedUser?.role === 'user')) {
    // Utente standard - Home + eventualmente cantieri se impersonificato
    const nav = [homeButton]
    if (isImpersonating) {
      nav.push({ name: 'Cantieri', href: '/cantieri', icon: Building2 })
    }

    // Se un cantiere è selezionato, aggiungi i menu di gestione come nella webapp originale
    if (cantiereId) {
      nav.push(
        { name: 'Visualizza Cavi', href: '/cavi', icon: Cable },
        { name: 'Parco Cavi', href: '/parco-cavi', icon: Package },
        { name: 'Gestione Excel', href: '/excel', icon: FileText, hasDropdown: true },
        { name: 'Report', href: '/reports', icon: BarChart3 },
        { name: 'Gestione Comande', href: '/comande', icon: ClipboardList },
        { name: 'Produttività', href: '/productivity', icon: Activity },
      )
    }

    return nav
  }

  if (userRole === 'cantieri_user' || (isImpersonating && impersonatedUser?.role === 'cantieri_user')) {
    // Utente cantiere - menu completo come nella webapp originale
    const nav = [homeButton]

    // Se un cantiere è selezionato, aggiungi i menu di gestione
    if (cantiereId) {
      // Se non è cantieri_user diretto, aggiungi Visualizza Cavi
      if (userRole !== 'cantieri_user') {
        nav.push({ name: 'Visualizza Cavi', href: '/cavi', icon: Cable })
      }

      nav.push(
        { name: 'Parco Cavi', href: '/parco-cavi', icon: Package },
        { name: 'Gestione Excel', href: '/excel', icon: FileText, hasDropdown: true },
        { name: 'Report', href: '/reports', icon: BarChart3 },
        { name: 'Gestione Comande', href: '/comande', icon: ClipboardList },
        { name: 'Produttività', href: '/productivity', icon: Activity },
      )
    }

    return nav
  }

  // Default
  return [homeButton]
}

export function Navbar() {
  const [isOpen, setIsOpen] = useState(false)
  const [excelDropdownOpen, setExcelDropdownOpen] = useState(false)
  const [isNavigating, setIsNavigating] = useState(false)
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({})
  const [hoveredItem, setHoveredItem] = useState<string | null>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const pathname = usePathname()
  const router = useRouter()
  const { user, cantiere, isAuthenticated, isImpersonating, impersonatedUser, logout } = useAuth()

  // Recupera l'ID del cantiere selezionato dal localStorage o dal context
  const cantiereId = cantiere?.id_cantiere || (typeof window !== 'undefined' ? parseInt(localStorage.getItem('selectedCantiereId') || '0') : 0)
  const cantiereName = cantiere?.commessa || (typeof window !== 'undefined' ? localStorage.getItem('selectedCantiereName') : '') || `Cantiere ${cantiereId}`

  const navigation = getNavigation(user?.ruolo, isImpersonating, impersonatedUser, cantiereId)

  // Gestione navigazione con loading states
  const handleNavigation = async (href: string, itemName: string) => {
    if (pathname === href) return

    setLoadingStates(prev => ({ ...prev, [itemName]: true }))
    setIsNavigating(true)

    try {
      await router.push(href)
    } catch (error) {
      console.error('Navigation error:', error)
    } finally {
      // Delay per mostrare il feedback visivo
      setTimeout(() => {
        setLoadingStates(prev => ({ ...prev, [itemName]: false }))
        setIsNavigating(false)
        setIsOpen(false) // Chiudi menu mobile
      }, 300)
    }
  }

  // Gestione logout con loading
  const handleLogout = async () => {
    setLoadingStates(prev => ({ ...prev, 'logout': true }))
    try {
      await logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      setLoadingStates(prev => ({ ...prev, 'logout': false }))
    }
  }

  // Chiudi dropdown quando si clicca fuori
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setExcelDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Reset loading states quando cambia pathname
  useEffect(() => {
    setLoadingStates({})
    setIsNavigating(false)
  }, [pathname])

  // Non mostrare navbar nella pagina di login
  if (pathname === '/login') {
    return null
  }

  // Se non autenticato, non mostrare navbar
  if (!isAuthenticated) {
    return null
  }

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-white border-b border-slate-200 shadow-sm">
      <div className="max-w-[90%] mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">

          {/* Tutto a sinistra: Logo + Navigation */}
          <div className="flex items-center space-x-6">
            {/* Logo e Brand con animazioni */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div
                    className="flex items-center space-x-3 cursor-pointer group transition-all duration-300 hover:scale-105"
                    onClick={() => handleNavigation('/', 'Home')}
                  >
                    <div className="relative w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center group-hover:shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
                      {loadingStates['Home'] ? (
                        <Loader2 className="w-5 h-5 text-white animate-spin" />
                      ) : (
                        <Cable className="w-5 h-5 text-white group-hover:rotate-12 transition-transform duration-300" />
                      )}
                      <div className="absolute inset-0 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                    </div>
                    <div className="hidden sm:block">
                      <h1 className="text-xl font-bold text-slate-900 group-hover:text-blue-700 transition-colors duration-300">
                        CABLYS
                        <Sparkles className="inline w-4 h-4 ml-1 text-blue-500 opacity-0 group-hover:opacity-100 transition-all duration-300" />
                      </h1>
                      <p className="text-xs text-slate-500 -mt-1 group-hover:text-slate-600 transition-colors duration-300">
                        Cable Installation System
                      </p>
                    </div>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Torna alla home</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {/* Navigation Desktop - allontanata dal logo */}
            <div className="hidden md:flex items-center space-x-1">
            {navigation.map((item) => {
              const isActive = pathname === item.href ||
                             (item.href !== '/' && pathname.startsWith(item.href))
              const Icon = item.icon

              // Gestione speciale per il dropdown Excel
              if (item.hasDropdown && item.name === 'Gestione Excel') {
                return (
                  <div key={item.name} className="relative" ref={dropdownRef}>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className={`group flex items-center space-x-2 px-3 py-2 transition-all duration-300 ease-in-out rounded-lg border border-transparent relative overflow-hidden ${
                              isActive
                                ? 'bg-gradient-to-r from-blue-100 to-blue-50 text-blue-700 shadow-sm border-blue-200'
                                : 'text-slate-600 hover:text-slate-900 hover:bg-gradient-to-r hover:from-blue-50 hover:to-slate-50 hover:border-blue-200 hover:shadow-sm'
                            }`}
                            onClick={() => setExcelDropdownOpen(!excelDropdownOpen)}
                            onMouseEnter={() => setHoveredItem(item.name)}
                            onMouseLeave={() => setHoveredItem(null)}
                          >
                            <Icon className={`w-4 h-4 transition-all duration-300 ${hoveredItem === item.name ? 'scale-110' : ''}`} />
                            <span className="hidden lg:inline font-medium">{item.name}</span>
                            <ChevronDown className={`w-3 h-3 transition-transform duration-300 ${excelDropdownOpen ? 'rotate-180' : ''}`} />

                            {/* Effetto shimmer al hover */}
                            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-all duration-700"></div>
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Gestione file Excel</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>

                    {excelDropdownOpen && (
                      <div className="absolute top-full left-0 mt-2 w-52 bg-white border border-slate-200 rounded-xl shadow-xl z-50 overflow-hidden animate-in slide-in-from-top-2 duration-200">
                        <div className="py-2">
                          <div
                            className="block px-4 py-3 text-sm text-slate-700 hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 hover:text-green-700 transition-all duration-200 cursor-pointer group"
                            onClick={() => {
                              handleNavigation('/excel/import', 'Importa Excel')
                              setExcelDropdownOpen(false)
                            }}
                          >
                            <div className="flex items-center space-x-3">
                              <Upload className="w-4 h-4 group-hover:scale-110 transition-transform duration-200" />
                              <span className="font-medium">Importa Excel</span>
                              <ArrowRight className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-all duration-200 ml-auto" />
                            </div>
                          </div>
                          <div
                            className="block px-4 py-3 text-sm text-slate-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-cyan-50 hover:text-blue-700 transition-all duration-200 cursor-pointer group"
                            onClick={() => {
                              handleNavigation('/excel/export', 'Esporta Excel')
                              setExcelDropdownOpen(false)
                            }}
                          >
                            <div className="flex items-center space-x-3">
                              <Download className="w-4 h-4 group-hover:scale-110 transition-transform duration-200" />
                              <span className="font-medium">Esporta Excel</span>
                              <ArrowRight className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-all duration-200 ml-auto" />
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )
              }

              return (
                <TooltipProvider key={item.name}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div
                        className={`group relative cursor-pointer transition-all duration-300 ease-in-out rounded-lg border border-transparent overflow-hidden ${
                          isActive
                            ? 'bg-gradient-to-r from-blue-100 to-blue-50 text-blue-700 shadow-sm border-blue-200'
                            : 'text-slate-600 hover:text-slate-900 hover:bg-gradient-to-r hover:from-blue-50 hover:to-slate-50 hover:border-blue-200 hover:shadow-sm'
                        }`}
                        onClick={() => handleNavigation(item.href, item.name)}
                        onMouseEnter={() => setHoveredItem(item.name)}
                        onMouseLeave={() => setHoveredItem(null)}
                      >
                        <div className="flex items-center space-x-2 px-3 py-2">
                          {loadingStates[item.name] ? (
                            <Loader2 className="w-4 h-4 animate-spin" />
                          ) : (
                            <Icon className={`w-4 h-4 transition-all duration-300 ${
                              hoveredItem === item.name ? 'scale-110' : ''
                            } ${isActive ? 'text-blue-600' : ''}`} />
                          )}
                          <span className={`hidden lg:inline font-medium transition-all duration-300 ${
                            isActive ? 'text-blue-700' : ''
                          }`}>
                            {item.name}
                          </span>

                          {/* Badge per Produttività */}
                          {item.name.includes('Produttività') && (
                            <Zap className="w-3 h-3 text-yellow-500 animate-pulse" />
                          )}
                        </div>

                        {/* Effetto shimmer al hover */}
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-all duration-700"></div>

                        {/* Indicatore attivo */}
                        {isActive && (
                          <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600"></div>
                        )}
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{item.name}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )
            })}
            </div>
          </div>

          {/* User Info a destra con più margine */}
          <div className="flex items-center space-x-4 ml-8">
            {/* Display cantiere selezionato - versione migliorata */}
            {cantiereId && cantiereId > 0 && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="hidden sm:flex items-center space-x-2 px-3 py-2 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg hover:shadow-sm transition-all duration-300 cursor-default group">
                      <Building2 className="w-4 h-4 text-blue-600 group-hover:scale-110 transition-transform duration-300" />
                      <div className="text-sm">
                        <span className="text-blue-900 font-semibold">{cantiereName}</span>
                        <div className="w-2 h-2 bg-green-400 rounded-full inline-block ml-2 animate-pulse"></div>
                      </div>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Cantiere attivo: {cantiereName}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}

            <div className="hidden sm:flex items-center space-x-3">
              {/* User Avatar e Info */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-slate-50 transition-all duration-300 cursor-default group">
                      <div className="text-right">
                        <p className="text-sm font-semibold text-slate-900 group-hover:text-slate-700 transition-colors duration-300">
                          {isImpersonating && impersonatedUser ? impersonatedUser.username : user?.username}
                        </p>
                        <p className="text-xs text-slate-500 group-hover:text-slate-600 transition-colors duration-300">
                          {user?.ruolo === 'owner' ? 'Amministratore' :
                           user?.ruolo === 'user' ? 'Utente Standard' :
                           user?.ruolo === 'cantieri_user' ? 'Utente Cantiere' : user?.ruolo || ''}
                        </p>
                      </div>
                      <div className="relative">
                        <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center group-hover:shadow-lg group-hover:shadow-green-500/25 transition-all duration-300">
                          <Users className="w-4 h-4 text-white" />
                        </div>
                        <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white animate-pulse"></div>
                      </div>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Utente: {isImpersonating && impersonatedUser ? impersonatedUser.username : user?.username}</p>
                    <p>Ruolo: {user?.ruolo}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              {/* Logout Button */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleLogout}
                      disabled={loadingStates['logout']}
                      className="group relative overflow-hidden hover:bg-red-50 hover:text-red-600 transition-all duration-300 ease-in-out rounded-lg border border-transparent hover:border-red-200 hover:shadow-sm"
                    >
                      {loadingStates['logout'] ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        <LogOut className="w-4 h-4 group-hover:scale-110 transition-transform duration-300" />
                      )}

                      {/* Effetto shimmer al hover */}
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-red-100 to-transparent opacity-0 group-hover:opacity-30 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-all duration-700"></div>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{isImpersonating ? 'Torna al menu admin' : 'Logout'}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>

            {/* Mobile menu button migliorato */}
            <div className="md:hidden">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setIsOpen(!isOpen)}
                      className="group relative overflow-hidden text-slate-600 hover:bg-blue-50 hover:text-blue-600 transition-all duration-300 ease-in-out rounded-lg border border-transparent hover:border-blue-200 hover:shadow-sm"
                    >
                      <div className="relative z-10">
                        {isOpen ? (
                          <X className="w-5 h-5 group-hover:rotate-90 transition-transform duration-300" />
                        ) : (
                          <Menu className="w-5 h-5 group-hover:scale-110 transition-transform duration-300" />
                        )}
                      </div>

                      {/* Effetto shimmer al hover */}
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-100 to-transparent opacity-0 group-hover:opacity-30 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-all duration-700"></div>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{isOpen ? 'Chiudi menu' : 'Apri menu'}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Navigation migliorato */}
      {isOpen && (
        <div className="md:hidden border-t border-slate-200 bg-gradient-to-b from-white to-slate-50 shadow-lg animate-in slide-in-from-top-2 duration-300">
          <div className="px-4 pt-4 pb-6 space-y-2">
            {/* Info utente mobile */}
            <div className="mb-4 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
                  <Users className="w-4 h-4 text-white" />
                </div>
                <div>
                  <p className="text-sm font-semibold text-slate-900">
                    {isImpersonating && impersonatedUser ? impersonatedUser.username : user?.username}
                  </p>
                  <p className="text-xs text-slate-600">
                    {user?.ruolo === 'owner' ? 'Amministratore' :
                     user?.ruolo === 'user' ? 'Utente Standard' :
                     user?.ruolo === 'cantieri_user' ? 'Utente Cantiere' : user?.ruolo || ''}
                  </p>
                </div>
              </div>

              {/* Cantiere mobile */}
              {cantiereId && cantiereId > 0 && (
                <div className="mt-2 pt-2 border-t border-blue-200">
                  <div className="flex items-center space-x-2">
                    <Building2 className="w-3 h-3 text-blue-600" />
                    <span className="text-xs text-blue-900 font-medium">{cantiereName}</span>
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  </div>
                </div>
              )}
            </div>

            {/* Navigation items */}
            {navigation.map((item) => {
              const isActive = pathname === item.href ||
                             (item.href !== '/' && pathname.startsWith(item.href))
              const Icon = item.icon

              return (
                <div
                  key={item.name}
                  className={`group relative overflow-hidden rounded-lg transition-all duration-300 ${
                    isActive
                      ? 'bg-gradient-to-r from-blue-100 to-blue-50 border border-blue-200 shadow-sm'
                      : 'hover:bg-gradient-to-r hover:from-blue-50 hover:to-slate-50 hover:border-blue-200 border border-transparent'
                  }`}
                  onClick={() => {
                    handleNavigation(item.href, item.name)
                    setIsOpen(false)
                  }}
                >
                  <div className="flex items-center space-x-3 p-3 cursor-pointer">
                    {loadingStates[item.name] ? (
                      <Loader2 className="w-5 h-5 animate-spin text-blue-600" />
                    ) : (
                      <Icon className={`w-5 h-5 transition-all duration-300 group-hover:scale-110 ${
                        isActive ? 'text-blue-600' : 'text-slate-600 group-hover:text-blue-600'
                      }`} />
                    )}
                    <span className={`font-medium transition-all duration-300 ${
                      isActive ? 'text-blue-700' : 'text-slate-700 group-hover:text-blue-700'
                    }`}>
                      {item.name}
                    </span>

                    {/* Badge per Produttività */}
                    {item.name.includes('Produttività') && (
                      <Zap className="w-4 h-4 text-yellow-500 animate-pulse ml-auto" />
                    )}

                    {isActive && (
                      <ArrowRight className="w-4 h-4 text-blue-600 ml-auto" />
                    )}
                  </div>

                  {/* Effetto shimmer al hover */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-all duration-700"></div>
                </div>
              )
            })}

            {/* Logout button mobile */}
            <div className="mt-4 pt-4 border-t border-slate-200">
              <div
                className="group relative overflow-hidden rounded-lg transition-all duration-300 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 hover:border-red-200 border border-transparent cursor-pointer"
                onClick={() => {
                  handleLogout()
                  setIsOpen(false)
                }}
              >
                <div className="flex items-center space-x-3 p-3">
                  {loadingStates['logout'] ? (
                    <Loader2 className="w-5 h-5 animate-spin text-red-600" />
                  ) : (
                    <LogOut className="w-5 h-5 text-slate-600 group-hover:text-red-600 group-hover:scale-110 transition-all duration-300" />
                  )}
                  <span className="font-medium text-slate-700 group-hover:text-red-700 transition-all duration-300">
                    {isImpersonating ? 'Torna al menu admin' : 'Logout'}
                  </span>
                </div>

                {/* Effetto shimmer al hover */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-red-100 to-transparent opacity-0 group-hover:opacity-30 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-all duration-700"></div>
              </div>
            </div>
          </div>
        </div>
      )}
    </nav>
  )
}
