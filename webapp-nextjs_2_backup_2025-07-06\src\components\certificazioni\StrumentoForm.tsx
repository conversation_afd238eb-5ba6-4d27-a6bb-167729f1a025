'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle, Save, X, Settings, Wrench, Calendar, Award } from 'lucide-react'
import { StrumentoCertificato, StrumentoCertificatoCreate } from '@/types/certificazioni'
import { strumentiApi } from '@/lib/api'

interface StrumentoFormProps {
  cantiereId: number
  strumento?: StrumentoCertificato | null
  onSuccess: () => void
  onCancel: () => void
}

export default function StrumentoForm({
  cantiereId,
  strumento,
  onSuccess,
  onCancel
}: StrumentoFormProps) {
  const [formData, setFormData] = useState<StrumentoCertificatoCreate>({
    nome: '',
    marca: '',
    modello: '',
    numero_serie: '',
    data_calibrazione: '',
    data_scadenza_calibrazione: '',
    note: '',
    tipo_strumento: 'MEGGER',
    ente_certificatore: '',
    numero_certificato_calibrazione: '',
    range_misura: '',
    precisione: '',
    stato_strumento: 'ATTIVO'
  })

  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState('')
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})

  const isEdit = !!strumento

  useEffect(() => {
    if (strumento) {
      setFormData({
        nome: strumento.nome,
        marca: strumento.marca,
        modello: strumento.modello,
        numero_serie: strumento.numero_serie,
        data_calibrazione: strumento.data_calibrazione.split('T')[0],
        data_scadenza_calibrazione: strumento.data_scadenza_calibrazione.split('T')[0],
        note: strumento.note || '',
        tipo_strumento: strumento.tipo_strumento || 'MEGGER',
        ente_certificatore: strumento.ente_certificatore || '',
        numero_certificato_calibrazione: strumento.numero_certificato_calibrazione || '',
        range_misura: strumento.range_misura || '',
        precisione: strumento.precisione || '',
        stato_strumento: strumento.stato_strumento || 'ATTIVO'
      })
    }
  }, [strumento])

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {}

    if (!formData.nome.trim()) {
      errors.nome = 'Il nome è obbligatorio'
    }

    if (!formData.marca.trim()) {
      errors.marca = 'La marca è obbligatoria'
    }

    if (!formData.modello.trim()) {
      errors.modello = 'Il modello è obbligatorio'
    }

    if (!formData.numero_serie.trim()) {
      errors.numero_serie = 'Il numero di serie è obbligatorio'
    }

    if (!formData.data_calibrazione) {
      errors.data_calibrazione = 'La data di calibrazione è obbligatoria'
    }

    if (!formData.data_scadenza_calibrazione) {
      errors.data_scadenza_calibrazione = 'La data di scadenza è obbligatoria'
    }

    // Validazione date
    if (formData.data_calibrazione && formData.data_scadenza_calibrazione) {
      const calibrazione = new Date(formData.data_calibrazione)
      const scadenza = new Date(formData.data_scadenza_calibrazione)
      
      if (scadenza <= calibrazione) {
        errors.data_scadenza_calibrazione = 'La data di scadenza deve essere successiva alla calibrazione'
      }
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleInputChange = (field: keyof StrumentoCertificatoCreate, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Rimuovi errore di validazione se presente
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSaving(true)
    setError('')

    try {
      if (isEdit && strumento) {
        await strumentiApi.updateStrumento(strumento.id_strumento, formData)
      } else {
        await strumentiApi.createStrumento(cantiereId, formData)
      }
      
      onSuccess()
    } catch (error: any) {
      console.error('Errore nel salvataggio strumento:', error)
      setError(error.response?.data?.detail || 'Errore nel salvataggio dello strumento')
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-slate-900 flex items-center gap-2">
            <Settings className="h-6 w-6 text-blue-600" />
            {isEdit ? 'Modifica Strumento' : 'Nuovo Strumento'}
          </h1>
          <p className="text-slate-600 mt-1">
            {isEdit ? 'Aggiorna i dettagli dello strumento' : 'Registra un nuovo strumento di certificazione'}
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Informazioni Base */}
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="bg-gradient-to-r from-blue-50 to-transparent">
            <CardTitle className="flex items-center gap-2">
              <Wrench className="h-5 w-5 text-blue-600" />
              Informazioni Base
            </CardTitle>
            <CardDescription>Dettagli identificativi dello strumento</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="nome" className="text-sm font-medium">Nome Strumento *</Label>
                <Input
                  id="nome"
                  value={formData.nome}
                  onChange={(e) => handleInputChange('nome', e.target.value)}
                  className={`transition-colors ${validationErrors.nome ? 'border-red-500 focus:ring-red-500' : 'focus:ring-blue-500'}`}
                  placeholder="es. Megger MIT1025"
                />
                {validationErrors.nome && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {validationErrors.nome}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="tipo_strumento" className="text-sm font-medium">Tipo Strumento</Label>
                <Select value={formData.tipo_strumento} onValueChange={(value) => handleInputChange('tipo_strumento', value)}>
                  <SelectTrigger className="focus:ring-blue-500">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="MEGGER">🔌 Megger</SelectItem>
                    <SelectItem value="MULTIMETRO">⚡ Multimetro</SelectItem>
                    <SelectItem value="TESTER_ISOLAMENTO">🛡️ Tester Isolamento</SelectItem>
                    <SelectItem value="TESTER_CONTINUITA">🔗 Tester Continuità</SelectItem>
                    <SelectItem value="ALTRO">❓ Altro</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="marca" className="text-sm font-medium">Marca *</Label>
                <Input
                  id="marca"
                  value={formData.marca}
                  onChange={(e) => handleInputChange('marca', e.target.value)}
                  className={`transition-colors ${validationErrors.marca ? 'border-red-500 focus:ring-red-500' : 'focus:ring-blue-500'}`}
                  placeholder="es. Fluke, Megger, Metrel"
                />
                {validationErrors.marca && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {validationErrors.marca}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="modello" className="text-sm font-medium">Modello *</Label>
                <Input
                  id="modello"
                  value={formData.modello}
                  onChange={(e) => handleInputChange('modello', e.target.value)}
                  className={`transition-colors ${validationErrors.modello ? 'border-red-500 focus:ring-red-500' : 'focus:ring-blue-500'}`}
                  placeholder="es. MIT1025, 1587"
                />
                {validationErrors.modello && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {validationErrors.modello}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="numero_serie" className="text-sm font-medium">Numero Serie *</Label>
                <Input
                  id="numero_serie"
                  value={formData.numero_serie}
                  onChange={(e) => handleInputChange('numero_serie', e.target.value)}
                  className={`transition-colors ${validationErrors.numero_serie ? 'border-red-500 focus:ring-red-500' : 'focus:ring-blue-500'}`}
                  placeholder="Numero di serie univoco"
                />
                {validationErrors.numero_serie && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {validationErrors.numero_serie}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="stato_strumento" className="text-sm font-medium">Stato</Label>
                <Select value={formData.stato_strumento} onValueChange={(value) => handleInputChange('stato_strumento', value)}>
                  <SelectTrigger className="focus:ring-blue-500">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ATTIVO">🟢 Attivo</SelectItem>
                    <SelectItem value="MANUTENZIONE">🟡 In Manutenzione</SelectItem>
                    <SelectItem value="FUORI_SERVIZIO">🔴 Fuori Servizio</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Calibrazione e Certificazione */}
        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="bg-gradient-to-r from-green-50 to-transparent">
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5 text-green-600" />
              Calibrazione e Certificazione
            </CardTitle>
            <CardDescription>Informazioni sulla calibrazione e certificazione dello strumento</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="data_calibrazione" className="text-sm font-medium">Data Calibrazione *</Label>
                <Input
                  id="data_calibrazione"
                  type="date"
                  value={formData.data_calibrazione}
                  onChange={(e) => handleInputChange('data_calibrazione', e.target.value)}
                  className={`transition-colors ${validationErrors.data_calibrazione ? 'border-red-500 focus:ring-red-500' : 'focus:ring-green-500'}`}
                />
                {validationErrors.data_calibrazione && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {validationErrors.data_calibrazione}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="data_scadenza_calibrazione" className="text-sm font-medium">Data Scadenza Calibrazione *</Label>
                <Input
                  id="data_scadenza_calibrazione"
                  type="date"
                  value={formData.data_scadenza_calibrazione}
                  onChange={(e) => handleInputChange('data_scadenza_calibrazione', e.target.value)}
                  className={`transition-colors ${validationErrors.data_scadenza_calibrazione ? 'border-red-500 focus:ring-red-500' : 'focus:ring-green-500'}`}
                />
                {validationErrors.data_scadenza_calibrazione && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {validationErrors.data_scadenza_calibrazione}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="ente_certificatore" className="text-sm font-medium">Ente Certificatore</Label>
                <Input
                  id="ente_certificatore"
                  value={formData.ente_certificatore}
                  onChange={(e) => handleInputChange('ente_certificatore', e.target.value)}
                  className="transition-colors focus:ring-green-500"
                  placeholder="es. LAT 123, ACCREDIA"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="numero_certificato_calibrazione" className="text-sm font-medium">N° Certificato Calibrazione</Label>
                <Input
                  id="numero_certificato_calibrazione"
                  value={formData.numero_certificato_calibrazione}
                  onChange={(e) => handleInputChange('numero_certificato_calibrazione', e.target.value)}
                  className="transition-colors focus:ring-green-500"
                  placeholder="Numero del certificato"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="range_misura" className="text-sm font-medium">Range di Misura</Label>
                <Input
                  id="range_misura"
                  value={formData.range_misura}
                  onChange={(e) => handleInputChange('range_misura', e.target.value)}
                  className="transition-colors focus:ring-green-500"
                  placeholder="es. 0.01 MΩ - 10 GΩ"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="precisione" className="text-sm font-medium">Precisione</Label>
                <Input
                  id="precisione"
                  value={formData.precisione}
                  onChange={(e) => handleInputChange('precisione', e.target.value)}
                  className="transition-colors focus:ring-green-500"
                  placeholder="es. ±2% ±3 digit"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="note" className="text-sm font-medium">Note</Label>
              <Textarea
                id="note"
                value={formData.note}
                onChange={(e) => handleInputChange('note', e.target.value)}
                className="transition-colors min-h-[80px] focus:ring-green-500"
                placeholder="Note aggiuntive sullo strumento..."
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Error Display */}
        {error && (
          <Alert variant="destructive" className="border-l-4 border-l-red-500">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="font-medium">{error}</AlertDescription>
          </Alert>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 pt-6 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSaving}
            className="px-6"
          >
            <X className="h-4 w-4 mr-2" />
            Annulla
          </Button>
          
          <Button
            type="submit"
            disabled={isSaving}
            className="px-6 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
          >
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Salvataggio...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                {isEdit ? 'Aggiorna' : 'Crea'} Strumento
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}
