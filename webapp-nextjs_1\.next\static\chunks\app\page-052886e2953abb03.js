(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{28370:(e,a,o)=>{Promise.resolve().then(o.bind(o,33792))},33792:(e,a,o)=>{"use strict";o.r(a),o.d(a,{default:()=>s});var n=o(95155),r=o(12115),t=o(35695),i=o(40283);function s(){let{user:e,cantiere:a,isAuthenticated:o,isLoading:s}=(0,i.A)(),l=(0,t.useRouter)();return(0,r.useEffect)(()=>{if(console.log("\uD83C\uDFE0 HomePage: Stato corrente:",{isLoading:s,isAuthenticated:o,user:e?{id:e.id_utente,ruolo:e.ruolo}:null,cantiere:a?{id:a.id_cantiere}:null}),s)return void console.log("\uD83C\uDFE0 HomePage: Ancora in caricamento, attendo...");if(!o){console.log("\uD83C\uDFE0 HomePage: Utente NON autenticato, reindirizzamento a /login"),l.replace("/login");return}o&&e?(console.log("\uD83C\uDFE0 HomePage: Utente autenticato, reindirizzamento in base al ruolo:",e.ruolo),"owner"===e.ruolo?(console.log("\uD83C\uDFE0 HomePage: Reindirizzamento admin a /admin"),l.replace("/admin")):"user"===e.ruolo?(console.log("\uD83C\uDFE0 HomePage: Reindirizzamento utente standard a /cantieri"),l.replace("/cantieri")):"cantieri_user"===e.ruolo?(console.log("\uD83C\uDFE0 HomePage: Reindirizzamento utente cantiere a /cavi"),l.replace("/cavi")):(console.log("\uD83C\uDFE0 HomePage: Ruolo sconosciuto, reindirizzamento a /login"),l.replace("/login"))):o&&a&&!e?(console.log("\uD83C\uDFE0 HomePage: Login cantiere rilevato, reindirizzamento a /cavi"),l.replace("/cavi")):!o||e||a||(console.error("\uD83C\uDFE0 HomePage: Stato inconsistente - autenticato ma senza user/cantiere"),l.replace("/login"))},[o,s,e,a,l]),(0,n.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-slate-900 mb-4",children:"CABLYS"}),(0,n.jsx)("p",{className:"text-slate-600 mb-6",children:s?"Verifica autenticazione...":"Reindirizzamento in corso..."}),(0,n.jsx)("div",{className:"w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto"})]})})}},35695:(e,a,o)=>{"use strict";var n=o(18999);o.o(n,"useParams")&&o.d(a,{useParams:function(){return n.useParams}}),o.o(n,"usePathname")&&o.d(a,{usePathname:function(){return n.usePathname}}),o.o(n,"useRouter")&&o.d(a,{useRouter:function(){return n.useRouter}}),o.o(n,"useSearchParams")&&o.d(a,{useSearchParams:function(){return n.useSearchParams}})}},e=>{var a=a=>e(e.s=a);e.O(0,[3464,283,8441,1684,7358],()=>a(28370)),_N_E=e.O()}]);