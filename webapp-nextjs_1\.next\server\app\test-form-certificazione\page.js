(()=>{var e={};e.id=5408,e.ids=[5408],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8819:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},9593:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("cloud",[["path",{d:"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z",key:"p7xjir"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24157:(e,s,t)=>{Promise.resolve().then(t.bind(t,48598))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},45583:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},45989:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("test-tube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5c-1.4 0-2.5-1.1-2.5-2.5V2",key:"125lnx"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]])},47026:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>o});var a=t(65239),r=t(48088),i=t(88170),l=t.n(i),n=t(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let o={children:["",{children:["test-form-certificazione",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,73080)),"C:\\CMS\\webapp-nextjs_1\\src\\app\\test-form-certificazione\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\CMS\\webapp-nextjs_1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\CMS\\webapp-nextjs_1\\src\\app\\test-form-certificazione\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/test-form-certificazione/page",pathname:"/test-form-certificazione",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},47342:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},48598:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>j});var a=t(60687),r=t(43210),i=t(44493),l=t(29523),n=t(96834),c=t(45989),o=t(10022),d=t(9593),m=t(5336),x=t(47342),p=t(84027),h=t(45583),u=t(17680);function j(){let[e,s]=(0,r.useState)(!1),[t]=(0,r.useState)(1);return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:[(0,a.jsxs)("div",{className:"max-w-6xl mx-auto space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-slate-900 flex items-center gap-3",children:[(0,a.jsx)(c.A,{className:"h-8 w-8 text-blue-600"}),"Test Form Certificazione CEI 64-8"]}),(0,a.jsx)("p",{className:"text-slate-600 mt-1",children:"Test delle automazioni e dell'interfaccia utente"})]}),(0,a.jsxs)(l.$,{onClick:()=>s(!0),disabled:e,children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Apri Form Certificazione"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"h-5 w-5 text-blue-500"}),"Dati Meteorologici Automatici"]}),(0,a.jsx)(i.BT,{children:"Test del caricamento automatico"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-green-500"}),(0,a.jsx)("span",{className:"text-sm",children:"Caricamento automatico all'apertura del form"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-green-500"}),(0,a.jsx)("span",{className:"text-sm",children:"Visualizzazione temperatura e umidit\xe0"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-green-500"}),(0,a.jsx)("span",{className:"text-sm",children:"Precompilazione campi temperatura/umidit\xe0"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-green-500"}),(0,a.jsx)("span",{className:"text-sm",children:"Indicatore fonte dati (demo/live)"})]})]})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"h-5 w-5 text-green-500"}),"Collegamento Automatico Cavi"]}),(0,a.jsx)(i.BT,{children:"Test della logica CEI 64-8"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-green-500"}),(0,a.jsx)("span",{className:"text-sm",children:"Verifica stato collegamento cavo"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-green-500"}),(0,a.jsx)("span",{className:"text-sm",children:"Dialog di conferma se non collegato"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-green-500"}),(0,a.jsx)("span",{className:"text-sm",children:'Collegamento automatico a "cantiere"'})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-green-500"}),(0,a.jsx)("span",{className:"text-sm",children:"Aggiornamento stato cavi"})]})]})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(p.A,{className:"h-5 w-5 text-purple-500"}),"Validazione Automatica"]}),(0,a.jsx)(i.BT,{children:"Test della conformit\xe0 automatica"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-green-500"}),(0,a.jsx)("span",{className:"text-sm",children:"Calcolo automatico conformit\xe0"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-green-500"}),(0,a.jsx)("span",{className:"text-sm",children:"Confronto con valore minimo isolamento"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-green-500"}),(0,a.jsx)("span",{className:"text-sm",children:"Impostazione stato certificato"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-green-500"}),(0,a.jsx)("span",{className:"text-sm",children:"Esito complessivo automatico"})]})]})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-5 w-5 text-orange-500"}),"Interfaccia Utente"]}),(0,a.jsx)(i.BT,{children:"Test dell'esperienza utente"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-green-500"}),(0,a.jsx)("span",{className:"text-sm",children:"Layout responsive e moderno"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-green-500"}),(0,a.jsx)("span",{className:"text-sm",children:"Feedback visivo per operazioni"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-green-500"}),(0,a.jsx)("span",{className:"text-sm",children:"Validazione campi in tempo reale"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-green-500"}),(0,a.jsx)("span",{className:"text-sm",children:"Gestione errori e stati di caricamento"})]})]})})]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5"}),"Istruzioni per il Test"]}),(0,a.jsx)(i.BT,{children:"Come testare le funzionalit\xe0 implementate"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-semibold text-blue-900 mb-2",children:"1. Test Dati Meteorologici"}),(0,a.jsx)("p",{className:"text-blue-800 text-sm",children:'Apri il form e verifica che nella sezione "Condizioni Ambientali" vengano mostrati automaticamente temperatura e umidit\xe0. I campi dovrebbero essere precompilati.'})]}),(0,a.jsxs)("div",{className:"p-4 bg-green-50 border border-green-200 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-semibold text-green-900 mb-2",children:"2. Test Collegamento Automatico"}),(0,a.jsx)("p",{className:"text-green-800 text-sm",children:"Seleziona un cavo non completamente collegato e prova a salvare. Dovrebbe apparire un dialog di conferma per il collegamento automatico."})]}),(0,a.jsxs)("div",{className:"p-4 bg-purple-50 border border-purple-200 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-semibold text-purple-900 mb-2",children:"3. Test Validazione Automatica"}),(0,a.jsx)("p",{className:"text-purple-800 text-sm",children:'Inserisci un valore di isolamento (es. 600 MΩ) e verifica che lo stato venga automaticamente impostato su "CONFORME" se superiore al minimo.'})]}),(0,a.jsxs)("div",{className:"p-4 bg-orange-50 border border-orange-200 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-semibold text-orange-900 mb-2",children:"4. Test Interfaccia"}),(0,a.jsx)("p",{className:"text-orange-800 text-sm",children:"Verifica la responsivit\xe0, i feedback visivi, la validazione dei campi e la gestione degli stati di caricamento durante le operazioni."})]})]})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Risultati Attesi"}),(0,a.jsx)(i.BT,{children:"Cosa dovrebbe succedere durante il test"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(n.E,{className:"bg-green-100 text-green-800 border-green-200",children:"✅ Dati meteorologici caricati automaticamente"}),(0,a.jsx)(n.E,{className:"bg-green-100 text-green-800 border-green-200",children:"✅ Campi temperatura/umidit\xe0 precompilati"}),(0,a.jsx)(n.E,{className:"bg-green-100 text-green-800 border-green-200",children:"✅ Dialog conferma collegamento automatico"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(n.E,{className:"bg-green-100 text-green-800 border-green-200",children:"✅ Stato conformit\xe0 calcolato automaticamente"}),(0,a.jsx)(n.E,{className:"bg-green-100 text-green-800 border-green-200",children:"✅ Interfaccia responsive e moderna"}),(0,a.jsx)(n.E,{className:"bg-green-100 text-green-800 border-green-200",children:"✅ Feedback visivo per tutte le operazioni"})]})]})})]})]}),e&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,a.jsx)("div",{className:"bg-white rounded-lg max-w-6xl w-full max-h-[95vh] min-w-[800px] overflow-y-auto",children:(0,a.jsx)("div",{className:"p-6",children:(0,a.jsx)(u.A,{cantiereId:t,certificazione:null,strumenti:[],onSuccess:()=>{s(!1),alert("✅ Certificazione creata con successo!\n\nVerifica che:\n- I dati meteorologici siano stati caricati automaticamente\n- Il collegamento automatico sia stato eseguito se necessario\n- La conformit\xe0 sia stata determinata automaticamente")},onCancel:()=>{s(!1)}})})})})]})}},54013:(e,s,t)=>{Promise.resolve().then(t.bind(t,73080))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73080:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs_1\\\\src\\\\app\\\\test-form-certificazione\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_1\\src\\app\\test-form-certificazione\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84027:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4447,6539,1658,7400,9464,4951,3140],()=>t(47026));module.exports=a})();