(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3986],{34428:(e,a,t)=>{Promise.resolve().then(t.bind(t,75449))},46102:(e,a,t)=>{"use strict";t.d(a,{Bc:()=>n,ZI:()=>d,k$:()=>c,m_:()=>o});var i=t(95155),s=t(12115),l=t(89613),r=t(59434);let n=l.Kq,o=l.bL,c=l.l9,d=s.forwardRef((e,a)=>{let{className:t,sideOffset:s=4,...n}=e;return(0,i.jsx)(l.UC,{ref:a,sideOffset:s,className:(0,r.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...n})});d.displayName=l.UC.displayName},46510:(e,a,t)=>{"use strict";t.d(a,{E:()=>b,ToastProvider:()=>p});var i=t(95155),s=t(12115),l=t(47650),r=t(40646),n=t(85339),o=t(1243),c=t(81284),d=t(54416);let m=(0,s.createContext)(void 0),x={success:{icon:r.A,bgColor:"bg-green-50",borderColor:"border-green-200",iconColor:"text-green-600",titleColor:"text-green-900",descColor:"text-green-700"},error:{icon:n.A,bgColor:"bg-red-50",borderColor:"border-red-200",iconColor:"text-red-600",titleColor:"text-red-900",descColor:"text-red-700"},warning:{icon:o.A,bgColor:"bg-orange-50",borderColor:"border-orange-200",iconColor:"text-orange-600",titleColor:"text-orange-900",descColor:"text-orange-700"},info:{icon:c.A,bgColor:"bg-blue-50",borderColor:"border-blue-200",iconColor:"text-blue-600",titleColor:"text-blue-900",descColor:"text-blue-700"}};function u(e){let{toast:a,onRemove:t}=e,[l,r]=(0,s.useState)(!1),[n,o]=(0,s.useState)(!1),c=x[a.type],m=c.icon;(0,s.useEffect)(()=>{let e=setTimeout(()=>r(!0),50);return()=>clearTimeout(e)},[]),(0,s.useEffect)(()=>{if(0!==a.duration){let e=setTimeout(()=>{u()},a.duration||5e3);return()=>clearTimeout(e)}},[a.duration]);let u=()=>{o(!0),setTimeout(()=>t(a.id),300)};return(0,i.jsx)("div",{className:"\n        transform transition-all duration-300 ease-in-out mb-3\n        ".concat(l&&!n?"translate-x-0 opacity-100 scale-100":"translate-x-full opacity-0 scale-95","\n      "),children:(0,i.jsx)("div",{className:"\n        max-w-sm w-full ".concat(c.bgColor," ").concat(c.borderColor," border-l-4 \n        rounded-lg shadow-lg p-4 pointer-events-auto\n      "),children:(0,i.jsxs)("div",{className:"flex items-start",children:[(0,i.jsx)("div",{className:"flex-shrink-0",children:(0,i.jsx)(m,{className:"w-5 h-5 ".concat(c.iconColor)})}),(0,i.jsxs)("div",{className:"ml-3 flex-1",children:[(0,i.jsx)("p",{className:"text-sm font-medium ".concat(c.titleColor),children:a.title}),a.description&&(0,i.jsx)("p",{className:"mt-1 text-sm ".concat(c.descColor),children:a.description}),a.action&&(0,i.jsx)("div",{className:"mt-3",children:(0,i.jsx)("button",{onClick:a.action.onClick,className:"\n                    text-sm font-medium ".concat(c.iconColor," \n                    hover:underline focus:outline-none focus:underline\n                  "),children:a.action.label})})]}),(0,i.jsx)("div",{className:"ml-4 flex-shrink-0",children:(0,i.jsx)("button",{onClick:u,className:"\n                inline-flex ".concat(c.descColor," hover:").concat(c.titleColor," \n                focus:outline-none focus:").concat(c.titleColor," transition-colors\n              "),children:(0,i.jsx)(d.A,{className:"w-4 h-4"})})})]})})})}function h(e){let{toasts:a,removeToast:t}=e,[r,n]=(0,s.useState)(!1);return((0,s.useEffect)(()=>{n(!0)},[]),r)?(0,l.createPortal)((0,i.jsx)("div",{className:"fixed top-4 right-4 z-50 pointer-events-none",children:(0,i.jsx)("div",{className:"flex flex-col-reverse",children:a.map(e=>(0,i.jsx)(u,{toast:e,onRemove:t},e.id))})}),document.body):null}function p(e){let{children:a}=e,[t,l]=(0,s.useState)([]),r=e=>{l(a=>a.filter(a=>a.id!==e))};return(0,i.jsxs)(m.Provider,{value:{toasts:t,addToast:e=>{let a=Math.random().toString(36).substr(2,9);l(t=>[...t,{...e,id:a}])},removeToast:r,clearToasts:()=>{l([])}},children:[a,(0,i.jsx)(h,{toasts:t,removeToast:r})]})}function b(){let{addToast:e}=function(){let e=(0,s.useContext)(m);if(!e)throw Error("useToast must be used within a ToastProvider");return e}();return{success:(a,t,i)=>e({type:"success",title:a,description:t,action:i}),error:(a,t,i)=>e({type:"error",title:a,description:t,action:i,duration:7e3}),warning:(a,t,i)=>e({type:"warning",title:a,description:t,action:i,duration:6e3}),info:(a,t,i)=>e({type:"info",title:a,description:t,action:i}),cavoDisconnected:a=>e({type:"success",title:"Cavo Scollegato",description:"Il cavo ".concat(a," \xe8 stato scollegato con successo.")}),pdfGenerated:(a,t)=>e({type:"success",title:"PDF Generato",description:"Certificato per il cavo ".concat(t," salvato come ").concat(a,"."),action:{label:"Apri Cartella",onClick:()=>{console.log("Apertura cartella download...")}}}),certificationError:(a,t)=>e({type:"error",title:"Certificazione Fallita",description:"Impossibile certificare il cavo ".concat(a,": ").concat(t),duration:8e3}),actionInProgress:(a,t)=>e({type:"info",title:"".concat(a," in Corso"),description:"Elaborazione del cavo ".concat(t,"..."),duration:3e3})}}},47262:(e,a,t)=>{"use strict";t.d(a,{S:()=>n});var i=t(95155);t(12115);var s=t(76981),l=t(5196),r=t(59434);function n(e){let{className:a,...t}=e;return(0,i.jsx)(s.bL,{"data-slot":"checkbox",className:(0,r.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...t,children:(0,i.jsx)(s.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,i.jsx)(l.A,{className:"size-3.5"})})})}},54165:(e,a,t)=>{"use strict";t.d(a,{Cf:()=>m,Es:()=>u,L3:()=>h,c7:()=>x,lG:()=>n,rr:()=>p,zM:()=>o});var i=t(95155);t(12115);var s=t(15452),l=t(54416),r=t(59434);function n(e){let{...a}=e;return(0,i.jsx)(s.bL,{"data-slot":"dialog",...a})}function o(e){let{...a}=e;return(0,i.jsx)(s.l9,{"data-slot":"dialog-trigger",...a})}function c(e){let{...a}=e;return(0,i.jsx)(s.ZL,{"data-slot":"dialog-portal",...a})}function d(e){let{className:a,...t}=e;return(0,i.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,r.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...t})}function m(e){let{className:a,children:t,showCloseButton:n=!0,...o}=e;return(0,i.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,i.jsx)(d,{}),(0,i.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,r.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...o,children:[t,n&&(0,i.jsxs)(s.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,i.jsx)(l.A,{}),(0,i.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x(e){let{className:a,...t}=e;return(0,i.jsx)("div",{"data-slot":"dialog-header",className:(0,r.cn)("flex flex-col gap-2 text-center sm:text-left",a),...t})}function u(e){let{className:a,...t}=e;return(0,i.jsx)("div",{"data-slot":"dialog-footer",className:(0,r.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...t})}function h(e){let{className:a,...t}=e;return(0,i.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,r.cn)("text-lg leading-none font-semibold",a),...t})}function p(e){let{className:a,...t}=e;return(0,i.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,r.cn)("text-muted-foreground text-sm",a),...t})}},55365:(e,a,t)=>{"use strict";t.d(a,{Fc:()=>o,TN:()=>c});var i=t(95155),s=t(12115),l=t(74466),r=t(59434);let n=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=s.forwardRef((e,a)=>{let{className:t,variant:s,...l}=e;return(0,i.jsx)("div",{ref:a,role:"alert",className:(0,r.cn)(n({variant:s}),t),...l})});o.displayName="Alert",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,i.jsx)("h5",{ref:a,className:(0,r.cn)("mb-1 font-medium leading-none tracking-tight",t),...s})}).displayName="AlertTitle";let c=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,i.jsx)("div",{ref:a,className:(0,r.cn)("text-sm [&_p]:leading-relaxed",t),...s})});c.displayName="AlertDescription"},75449:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>eG});var i=t(95155),s=t(12115),l=t(35695),r=t(66695),n=t(30285),o=t(55365),c=t(40283),d=t(25731),m=t(26126),x=t(47262);t(63743);var u=t(85127),h=t(62523),p=t(59409),b=t(20547),g=t(59434);let v=b.bL,f=b.l9,j=s.forwardRef((e,a)=>{let{className:t,align:s="center",sideOffset:l=4,...r}=e;return(0,i.jsx)(b.ZL,{children:(0,i.jsx)(b.UC,{ref:a,align:s,sideOffset:l,className:(0,g.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...r})})});j.displayName=b.UC.displayName;var N=t(21492),C=t(39881),y=t(58832),_=t(54416),w=t(66932),A=t(52278),z=t(42355),S=t(13052),E=t(12767);function I(e){let{data:a=[],columns:t=[],loading:l=!1,emptyMessage:o="Nessun dato disponibile",onFilteredDataChange:c,renderRow:d,className:x,pagination:h=!0,defaultRowsPerPage:b=25}=e,[I,T]=(0,s.useState)({key:null,direction:null}),[O,F]=(0,s.useState)({}),[D,R]=(0,s.useState)({}),[B,P]=(0,s.useState)(0),[L,M]=(0,s.useState)(b),$=e=>[...new Set(a.map(a=>a[e]).filter(Boolean))].sort(),U=(0,s.useMemo)(()=>{let e=[...a];return Object.entries(O).forEach(a=>{let[t,i]=a;!i.value||Array.isArray(i.value)&&0===i.value.length||"string"==typeof i.value&&""===i.value.trim()||(e=e.filter(e=>{let a=e[t];if("select"===i.type)return(Array.isArray(i.value)?i.value:[i.value]).includes(a);if("text"===i.type){let e=i.value.toLowerCase(),t=String(a||"").toLowerCase();return"equals"===i.operator?t===e:t.includes(e)}if("number"===i.type){let e=parseFloat(a),t=parseFloat(i.value);if(isNaN(e)||isNaN(t))return!1;switch(i.operator){case"equals":default:return e===t;case"gt":return e>t;case"lt":return e<t;case"gte":return e>=t;case"lte":return e<=t}}return!0}))}),I.key&&I.direction&&e.sort((e,a)=>{let t=e[I.key],i=a[I.key];if(null==t&&null==i)return 0;if(null==t)return"asc"===I.direction?-1:1;if(null==i)return"asc"===I.direction?1:-1;let s=parseFloat(t),l=parseFloat(i),r=!isNaN(s)&&!isNaN(l),n=0;return n=r?s-l:String(t).localeCompare(String(i)),"asc"===I.direction?n:-n}),e},[a,O,I]),V=(0,s.useMemo)(()=>{if(!h)return U;let e=B*L,a=e+L;return U.slice(e,a)},[U,B,L,h]);(0,s.useEffect)(()=>{P(0)},[O]);let G=Math.ceil(U.length/L),Z=B*L+1,J=Math.min((B+1)*L,U.length);(0,s.useEffect)(()=>{c&&c(U)},[U,c]);let q=e=>{let a=t.find(a=>a.field===e);null!=a&&a.disableSort||T(a=>{if(a.key===e){if("asc"===a.direction)return{key:e,direction:"desc"};if("desc"===a.direction)return{key:null,direction:null}}return{key:e,direction:"asc"}})},H=(e,a)=>{F(t=>({...t,[e]:{...t[e],...a}}))},W=e=>{F(a=>{let t={...a};return delete t[e],t})},Y=e=>I.key!==e?(0,i.jsx)(N.A,{className:"h-3 w-3"}):"asc"===I.direction?(0,i.jsx)(C.A,{className:"h-3 w-3"}):"desc"===I.direction?(0,i.jsx)(y.A,{className:"h-3 w-3"}):(0,i.jsx)(N.A,{className:"h-3 w-3"}),Q=Object.keys(O).length>0;return l?(0,i.jsx)(r.Zp,{className:x,children:(0,i.jsx)(r.Wu,{className:"p-6",children:(0,i.jsx)("div",{className:"text-center",children:"Caricamento..."})})}):(0,i.jsxs)("div",{className:x,children:[Q&&(0,i.jsxs)("div",{className:"mb-4 flex flex-wrap gap-2 items-center",children:[(0,i.jsx)("span",{className:"text-sm text-muted-foreground",children:"Filtri attivi:"}),Object.entries(O).map(e=>{let[a,s]=e,l=t.find(e=>e.field===a);if(!l)return null;let r=Array.isArray(s.value)?s.value.join(", "):String(s.value);return(0,i.jsxs)(m.E,{variant:"secondary",className:"gap-1",children:[l.headerName,": ",r,(0,i.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-auto p-0 hover:bg-transparent",onClick:()=>W(a),children:(0,i.jsx)(_.A,{className:"h-3 w-3"})})]},a)}),(0,i.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>{F({})},className:"h-6 px-2 text-xs",children:"Pulisci tutti"})]}),(0,i.jsx)(r.Zp,{children:(0,i.jsx)(r.Wu,{className:"p-0",children:(0,i.jsxs)(u.XI,{children:[(0,i.jsx)(u.A0,{children:(0,i.jsx)(u.Hj,{className:"bg-mariner-50 hover:bg-mariner-50",children:t.map(e=>(0,i.jsx)(u.nd,{className:(0,g.cn)("font-semibold text-mariner-900 border-b border-mariner-200","center"===e.align&&"text-center","right"===e.align&&"text-right"),style:{width:e.width,...e.headerStyle},children:e.renderHeader?e.renderHeader():(0,i.jsxs)("div",{className:"relative group",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,i.jsx)("span",{className:"truncate",children:e.headerName}),(0,i.jsxs)("div",{className:"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:[!e.disableSort&&(0,i.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-4 w-4 p-0 hover:bg-mariner-100",onClick:()=>q(e.field),children:Y(e.field)}),!e.disableFilter&&(0,i.jsxs)(v,{open:D[e.field],onOpenChange:a=>R(t=>({...t,[e.field]:a})),children:[(0,i.jsx)(f,{asChild:!0,children:(0,i.jsx)(n.$,{variant:"ghost",size:"sm",className:(0,g.cn)("h-4 w-4 p-0 hover:bg-mariner-100",O[e.field]&&"text-mariner-600 opacity-100"),children:(0,i.jsx)(w.A,{className:"h-2.5 w-2.5"})})}),(0,i.jsx)(j,{className:"w-64",align:"start",children:(0,i.jsx)(k,{column:e,data:a,currentFilter:O[e.field],onFilterChange:a=>H(e.field,a),onClearFilter:()=>W(e.field),getUniqueValues:()=>$(e.field)})})]})]})]}),O[e.field]&&(0,i.jsx)("div",{className:"absolute -top-1 -right-1 h-2 w-2 bg-mariner-600 rounded-full"})]})},e.field))})}),(0,i.jsx)(u.BF,{children:V.length>0?V.map((e,a)=>d?d(e,B*L+a):(0,i.jsx)(u.Hj,{className:"hover:bg-mariner-50 border-b border-mariner-100",children:t.map(a=>(0,i.jsx)(u.nA,{className:(0,g.cn)("py-2 px-4","center"===a.align&&"text-center","right"===a.align&&"text-right"),style:a.cellStyle,children:a.renderCell?a.renderCell(e):e[a.field]},a.field))},a)):(0,i.jsx)(u.Hj,{children:(0,i.jsx)(u.nA,{colSpan:t.length,className:"text-center py-8 text-muted-foreground",children:o})})})]})})}),h&&U.length>0&&(0,i.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("span",{className:"text-sm text-muted-foreground",children:"Righe per pagina:"}),(0,i.jsxs)(p.l6,{value:L.toString(),onValueChange:e=>{M(Number(e)),P(0)},children:[(0,i.jsx)(p.bq,{className:"w-20",children:(0,i.jsx)(p.yv,{})}),(0,i.jsxs)(p.gC,{children:[(0,i.jsx)(p.eb,{value:"10",children:"10"}),(0,i.jsx)(p.eb,{value:"25",children:"25"}),(0,i.jsx)(p.eb,{value:"50",children:"50"}),(0,i.jsx)(p.eb,{value:"100",children:"100"}),(0,i.jsx)(p.eb,{value:U.length.toString(),children:"Tutto"})]})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("span",{className:"text-sm text-muted-foreground",children:U.length>0?"".concat(Z,"-").concat(J," di ").concat(U.length):"0 di 0"}),(0,i.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,i.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>P(0),disabled:0===B,className:"h-8 w-8 p-0",children:(0,i.jsx)(A.A,{className:"h-4 w-4"})}),(0,i.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>P(e=>Math.max(0,e-1)),disabled:0===B,className:"h-8 w-8 p-0",children:(0,i.jsx)(z.A,{className:"h-4 w-4"})}),(0,i.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>P(e=>Math.min(G-1,e+1)),disabled:B>=G-1,className:"h-8 w-8 p-0",children:(0,i.jsx)(S.A,{className:"h-4 w-4"})}),(0,i.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>P(G-1),disabled:B>=G-1,className:"h-8 w-8 p-0",children:(0,i.jsx)(E.A,{className:"h-4 w-4"})})]})]})]})]})}function k(e){let{column:a,currentFilter:t,onFilterChange:l,onClearFilter:r,getUniqueValues:o}=e,[c,d]=(0,s.useState)((null==t?void 0:t.value)||""),[m,u]=(0,s.useState)((null==t?void 0:t.operator)||"contains"),b=o(),g="number"!==a.dataType&&b.length<=20,v="number"===a.dataType,f=()=>{g?l({type:"select",value:Array.isArray(c)?c:[c]}):v?l({type:"number",value:c,operator:m}):l({type:"text",value:c,operator:m})};return(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"font-medium text-sm",children:["Filtra ",a.headerName]}),g?(0,i.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:b.map(e=>(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(x.S,{id:"filter-".concat(e),checked:Array.isArray(c)?c.includes(e):c===e,onCheckedChange:a=>{Array.isArray(c)?d(a?[...c,e]:c.filter(a=>a!==e)):d(a?[e]:[])}}),(0,i.jsx)("label",{htmlFor:"filter-".concat(e),className:"text-sm",children:e})]},e))}):(0,i.jsxs)("div",{className:"space-y-2",children:[v&&(0,i.jsxs)(p.l6,{value:m,onValueChange:u,children:[(0,i.jsx)(p.bq,{children:(0,i.jsx)(p.yv,{})}),(0,i.jsxs)(p.gC,{children:[(0,i.jsx)(p.eb,{value:"equals",children:"Uguale a"}),(0,i.jsx)(p.eb,{value:"gt",children:"Maggiore di"}),(0,i.jsx)(p.eb,{value:"lt",children:"Minore di"}),(0,i.jsx)(p.eb,{value:"gte",children:"Maggiore o uguale"}),(0,i.jsx)(p.eb,{value:"lte",children:"Minore o uguale"})]})]}),!v&&(0,i.jsxs)(p.l6,{value:m,onValueChange:u,children:[(0,i.jsx)(p.bq,{children:(0,i.jsx)(p.yv,{})}),(0,i.jsxs)(p.gC,{children:[(0,i.jsx)(p.eb,{value:"contains",children:"Contiene"}),(0,i.jsx)(p.eb,{value:"equals",children:"Uguale a"})]})]}),(0,i.jsx)(h.p,{placeholder:"Cerca ".concat(a.headerName.toLowerCase(),"..."),value:c,onChange:e=>d(e.target.value),onKeyDown:e=>"Enter"===e.key&&f()})]}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsx)(n.$,{size:"sm",onClick:f,children:"Applica"}),(0,i.jsx)(n.$,{size:"sm",variant:"outline",onClick:r,children:"Pulisci"})]})]})}var T=t(85057),O=t(47924),F=t(47863),D=t(66474),R=t(40133),B=t(19145),P=t(18979),L=t(40646),M=t(71539),$=t(37108),U=t(381),V=t(46102);function G(e){let{cavi:a=[],onFilteredDataChange:t,loading:l=!1,selectionEnabled:o=!1,onSelectionToggle:c}=e,[d,x]=(0,s.useState)(""),[u,b]=(0,s.useState)("contains"),[g,v]=(0,s.useState)(!1),[f,j]=(0,s.useState)("all"),[N,C]=(0,s.useState)("all"),[y,A]=(0,s.useState)("all"),[z,S]=(0,s.useState)("all"),[E,I]=(0,s.useState)(""),[k,G]=(0,s.useState)(""),Z=e=>e?e.toString().toLowerCase().trim():"",J=e=>{let a=e.match(/^([A-Z]+)(\d+)([A-Z]*)$/);return a?{prefix:a[1],number:a[2],suffix:a[3]||""}:{prefix:"",number:e,suffix:""}},q=(0,s.useCallback)((e,a,t)=>{let i=Z(a);if(!i)return!0;let s=Z(e.id_cavo),{prefix:l,number:r,suffix:n}=J(e.id_cavo||""),o=Z(e.tipologia),c=Z(e.formazione||e.sezione),d=Z(e.utility),m=Z(e.sistema),x=Z(e.da||e.ubicazione_partenza),u=Z(e.a||e.ubicazione_arrivo),h=Z(e.utenza_partenza),p=Z(e.utenza_arrivo),b=[s,l,r,n,o,c,d,m,x,u,h,p,Z(e.id_bobina),"BOBINA_VUOTA"===e.id_bobina?"bobina vuota":null===e.id_bobina?"":Z(e.id_bobina)],g=[{value:e.metri_teorici,name:"metri_teorici"},{value:e.metratura_reale||e.metri_posati,name:"metratura_reale"},{value:parseFloat(c),name:"formazione"}],v=i.match(/^([><=]+)(\d+(?:\.\d+)?)$/);if(v){let e=v[1],a=parseFloat(v[2]);return g.some(t=>{if(null==t.value||isNaN(t.value))return!1;switch(e){case">":return t.value>a;case">=":return t.value>=a;case"<":return t.value<a;case"<=":return t.value<=a;case"=":return t.value===a;default:return!1}})}let f=parseFloat(i);return!!(!isNaN(f)&&g.some(e=>null!=e.value&&!isNaN(e.value)&&e.value===f))||(t?b.some(e=>e===i):b.some(e=>e.includes(i)))},[]),H=(0,s.useCallback)(e=>{let a=e;return"all"!==f&&(a=a.filter(e=>{switch(f){case"installati":return"Installato"===e.stato_installazione||e.metri_posati&&e.metri_posati>0||e.metratura_reale&&e.metratura_reale>0;case"in_corso":return"In corso"===e.stato_installazione;case"da_installare":return"Installato"!==e.stato_installazione&&"In corso"!==e.stato_installazione&&!(e.metri_posati&&e.metri_posati>0)&&!(e.metratura_reale&&e.metratura_reale>0);default:return!0}})),"all"!==N&&(a=a.filter(e=>{let a=e.collegamento||e.collegamenti||0;switch(N){case"collegati":return 3===a;case"parziali":return 1===a||2===a;case"non_collegati":return 0===a;default:return!0}})),"all"!==y&&(a=a.filter(e=>{let a=!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato;return"certificati"===y?a:!a})),"all"!==z&&(a=a.filter(e=>Z(e.sistema)===Z(z))),(E||k)&&(a=a.filter(e=>{let a=e.metri_posati||e.metratura_reale||0,t=E?parseFloat(E):0,i=k?parseFloat(k):1/0;return a>=t&&a<=i})),a},[f,N,y,z,E,k,Z]),W=(0,s.useCallback)(()=>{let e=a;if(d.trim()){let a=d.split(",").map(e=>e.trim()).filter(e=>e.length>0);e="equals"===u?1===a.length?e.filter(e=>q(e,a[0],!0)):e.filter(e=>a.every(a=>q(e,a,!0))):e.filter(e=>a.some(a=>q(e,a,!1)))}e=H(e),null==t||t(e)},[d,u,a,t,q,H]),Y=(0,s.useMemo)(()=>({systems:[...new Set(a.map(e=>e.sistema).filter(Boolean))].sort()}),[a]),Q=()=>{x(""),j("all"),C("all"),A("all"),S("all"),I(""),G("")},K=d.trim()||"all"!==f||"all"!==N||"all"!==y||"all"!==z||E||k;(0,s.useEffect)(()=>{W()},[W,f,N,y,z,E,k]);let X=e=>{x(e)};return(0,i.jsx)(r.Zp,{className:"mb-4 shadow-sm border-slate-200",children:(0,i.jsxs)(r.Wu,{className:"p-4",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,i.jsxs)("div",{className:"flex-1 relative",children:[(0,i.jsx)(O.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,i.jsx)(h.p,{placeholder:"Ricerca intelligente cavi (ID, sistema, metri, stato...)...",value:d,onChange:e=>X(e.target.value),disabled:l,className:"pl-10 pr-12 h-10 border-slate-300 focus:border-blue-500 focus:ring-blue-500"}),d&&(0,i.jsx)(n.$,{variant:"ghost",size:"sm",className:"absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-slate-100",onClick:()=>{x(""),b("contains")},children:(0,i.jsx)(_.A,{className:"h-3 w-3"})})]}),(0,i.jsx)(V.Bc,{children:(0,i.jsxs)(V.m_,{children:[(0,i.jsx)(V.k$,{asChild:!0,children:(0,i.jsx)("div",{className:"w-36",children:(0,i.jsxs)(p.l6,{value:u,onValueChange:e=>b(e),children:[(0,i.jsx)(p.bq,{className:"h-10 border-slate-300",children:(0,i.jsx)(p.yv,{})}),(0,i.jsxs)(p.gC,{children:[(0,i.jsx)(p.eb,{value:"contains",children:"Contiene"}),(0,i.jsx)(p.eb,{value:"equals",children:"Uguale a"})]})]})})}),(0,i.jsx)(V.ZI,{children:(0,i.jsx)("p",{children:"Modalit\xe0 di ricerca: contiene o corrispondenza esatta"})})]})}),(0,i.jsx)(V.Bc,{children:(0,i.jsxs)(V.m_,{children:[(0,i.jsx)(V.k$,{asChild:!0,children:(0,i.jsxs)(n.$,{variant:g?"default":"outline",size:"sm",onClick:()=>v(!g),className:"flex items-center gap-2 transition-all duration-200 hover:scale-105",children:[(0,i.jsx)(w.A,{className:"h-4 w-4"}),"Filtri",g?(0,i.jsx)(F.A,{className:"h-4 w-4"}):(0,i.jsx)(D.A,{className:"h-4 w-4"}),K&&(0,i.jsx)(m.E,{variant:"secondary",className:"ml-1 bg-blue-100 text-blue-800 text-xs",children:"Attivi"})]})}),(0,i.jsx)(V.ZI,{children:(0,i.jsx)("p",{children:"Apri filtri avanzati per ricerca dettagliata"})})]})}),K&&(0,i.jsx)(V.Bc,{children:(0,i.jsxs)(V.m_,{children:[(0,i.jsx)(V.k$,{asChild:!0,children:(0,i.jsxs)(n.$,{variant:"outline",size:"sm",onClick:Q,disabled:l,className:"flex items-center gap-2 text-red-600 border-red-200 hover:bg-red-50 transition-all duration-200",children:[(0,i.jsx)(R.A,{className:"h-4 w-4"}),"Reset"]})}),(0,i.jsx)(V.ZI,{children:(0,i.jsx)("p",{children:"Rimuovi tutti i filtri attivi"})})]})}),c&&(0,i.jsx)(V.Bc,{children:(0,i.jsxs)(V.m_,{children:[(0,i.jsx)(V.k$,{asChild:!0,children:(0,i.jsxs)(n.$,{variant:o?"default":"outline",size:"sm",onClick:c,className:"flex items-center gap-2 transition-all duration-200 hover:scale-105",children:[o?(0,i.jsx)(B.A,{className:"h-4 w-4"}):(0,i.jsx)(P.A,{className:"h-4 w-4"}),o?"Disabilita":"Selezione"]})}),(0,i.jsx)(V.ZI,{children:(0,i.jsx)("p",{children:o?"Disabilita la selezione multipla":"Abilita la selezione multipla"})})]})})]}),g&&(0,i.jsxs)("div",{className:"mt-4 pt-4 border-t border-slate-200",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)(T.J,{className:"text-sm font-medium text-slate-700 flex items-center gap-2",children:[(0,i.jsx)(L.A,{className:"w-4 h-4 text-green-600"}),"Stato Installazione"]}),(0,i.jsxs)(p.l6,{value:f,onValueChange:j,children:[(0,i.jsx)(p.bq,{className:"h-9 border-slate-300",children:(0,i.jsx)(p.yv,{placeholder:"Tutti gli stati"})}),(0,i.jsxs)(p.gC,{children:[(0,i.jsx)(p.eb,{value:"all",children:"Tutti gli stati"}),(0,i.jsx)(p.eb,{value:"installati",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"Installati"]})}),(0,i.jsx)(p.eb,{value:"in_corso",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),"In Corso"]})}),(0,i.jsx)(p.eb,{value:"da_installare",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-gray-500 rounded-full"}),"Da Installare"]})})]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)(T.J,{className:"text-sm font-medium text-slate-700 flex items-center gap-2",children:[(0,i.jsx)(M.A,{className:"w-4 h-4 text-blue-600"}),"Collegamento"]}),(0,i.jsxs)(p.l6,{value:N,onValueChange:C,children:[(0,i.jsx)(p.bq,{className:"h-9 border-slate-300",children:(0,i.jsx)(p.yv,{placeholder:"Tutti i collegamenti"})}),(0,i.jsxs)(p.gC,{children:[(0,i.jsx)(p.eb,{value:"all",children:"Tutti i collegamenti"}),(0,i.jsx)(p.eb,{value:"collegati",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"Collegati (3/3)"]})}),(0,i.jsx)(p.eb,{value:"parziali",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),"Parziali (1-2/3)"]})}),(0,i.jsx)(p.eb,{value:"non_collegati",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-gray-500 rounded-full"}),"Non Collegati (0/3)"]})})]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)(T.J,{className:"text-sm font-medium text-slate-700 flex items-center gap-2",children:[(0,i.jsx)($.A,{className:"w-4 h-4 text-purple-600"}),"Certificazione"]}),(0,i.jsxs)(p.l6,{value:y,onValueChange:A,children:[(0,i.jsx)(p.bq,{className:"h-9 border-slate-300",children:(0,i.jsx)(p.yv,{placeholder:"Tutte le certificazioni"})}),(0,i.jsxs)(p.gC,{children:[(0,i.jsx)(p.eb,{value:"all",children:"Tutte le certificazioni"}),(0,i.jsx)(p.eb,{value:"certificati",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-purple-500 rounded-full"}),"Certificati"]})}),(0,i.jsx)(p.eb,{value:"non_certificati",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-gray-500 rounded-full"}),"Non Certificati"]})})]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)(T.J,{className:"text-sm font-medium text-slate-700 flex items-center gap-2",children:[(0,i.jsx)(U.A,{className:"w-4 h-4 text-indigo-600"}),"Sistema"]}),(0,i.jsxs)(p.l6,{value:z,onValueChange:S,children:[(0,i.jsx)(p.bq,{className:"h-9 border-slate-300",children:(0,i.jsx)(p.yv,{placeholder:"Tutti i sistemi"})}),(0,i.jsxs)(p.gC,{children:[(0,i.jsx)(p.eb,{value:"all",children:"Tutti i sistemi"}),Y.systems.map(e=>(0,i.jsx)(p.eb,{value:e,children:e},e))]})]})]})]}),(0,i.jsx)("div",{className:"mt-4 grid grid-cols-1 md:grid-cols-2 gap-4",children:(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(T.J,{className:"text-sm font-medium text-slate-700",children:"Range Metri Installati"}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(h.p,{type:"number",placeholder:"Min",value:E,onChange:e=>I(e.target.value),className:"h-9 border-slate-300"}),(0,i.jsx)("span",{className:"text-slate-500",children:"-"}),(0,i.jsx)(h.p,{type:"number",placeholder:"Max",value:k,onChange:e=>G(e.target.value),className:"h-9 border-slate-300"})]})]})}),K&&(0,i.jsxs)("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(w.A,{className:"w-4 h-4 text-blue-600"}),(0,i.jsx)("span",{className:"text-sm font-medium text-blue-800",children:"Filtri Attivi:"})]}),(0,i.jsxs)(n.$,{variant:"ghost",size:"sm",onClick:Q,className:"text-blue-600 hover:text-blue-800 hover:bg-blue-100",children:[(0,i.jsx)(_.A,{className:"w-4 h-4 mr-1"}),"Rimuovi Tutti"]})]}),(0,i.jsxs)("div",{className:"flex flex-wrap gap-2 mt-2",children:[d&&(0,i.jsxs)(m.E,{variant:"secondary",className:"bg-blue-100 text-blue-800",children:['Ricerca: "',d,'"']}),"all"!==f&&(0,i.jsxs)(m.E,{variant:"secondary",className:"bg-green-100 text-green-800",children:["Stato: ",f]}),"all"!==N&&(0,i.jsxs)(m.E,{variant:"secondary",className:"bg-blue-100 text-blue-800",children:["Collegamento: ",N]}),"all"!==y&&(0,i.jsxs)(m.E,{variant:"secondary",className:"bg-purple-100 text-purple-800",children:["Certificazione: ",y]}),"all"!==z&&(0,i.jsxs)(m.E,{variant:"secondary",className:"bg-indigo-100 text-indigo-800",children:["Sistema: ",z]}),(E||k)&&(0,i.jsxs)(m.E,{variant:"secondary",className:"bg-orange-100 text-orange-800",children:["Metri: ",E||"0"," - ",k||"∞"]})]})]})]}),d&&!g&&(0,i.jsx)("div",{className:"mt-3 p-2 bg-slate-50 rounded-lg border border-slate-200",children:(0,i.jsxs)("div",{className:"text-xs text-slate-600 flex items-center gap-2",children:[(0,i.jsx)("span",{children:"\uD83D\uDCA1"}),(0,i.jsx)("span",{children:"Suggerimenti: Usa virgole per ricerche multiple • Operatori: >100, <=50 per numeri"})]})})]})})}function Z(e){let{text:a,maxLength:t=20,className:l=""}=e,[r,n]=(0,s.useState)(!1),[o,c]=(0,s.useState)({x:0,y:0});if(!a)return(0,i.jsx)("span",{className:"text-gray-400",children:"-"});let d=a.length>t,m=d?"".concat(a.substring(0,t),"..."):a;return d?(0,i.jsxs)("div",{className:"relative inline-block",children:[(0,i.jsx)("span",{className:"cursor-help ".concat(l),style:{textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden",maxWidth:"100%",display:"inline-block"},onMouseEnter:e=>{c({x:e.clientX,y:e.clientY}),n(!0)},onMouseMove:e=>{c({x:e.clientX,y:e.clientY})},onMouseLeave:()=>n(!1),title:a,children:m}),r&&(0,i.jsxs)("div",{className:"fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg pointer-events-none",style:{top:o.y-40,left:o.x-150,maxWidth:"300px",wordWrap:"break-word",whiteSpace:"normal"},children:[a,(0,i.jsx)("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0",style:{borderLeft:"5px solid transparent",borderRight:"5px solid transparent",borderTop:"5px solid #1f2937"}})]})]}):(0,i.jsx)("span",{className:l,children:a})}var J=t(57434),q=t(91788),H=t(17580),W=t(62525),Y=t(54165),Q=t(88539),K=t(71847),X=t(28883),ee=t(51154),ea=t(1243),et=t(17649);let ei=et.bL;et.l9;let es=et.ZL,el=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,i.jsx)(et.hJ,{className:(0,g.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...s,ref:a})});el.displayName=et.hJ.displayName;let er=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,i.jsxs)(es,{children:[(0,i.jsx)(el,{}),(0,i.jsx)(et.UC,{ref:a,className:(0,g.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...s})]})});er.displayName=et.UC.displayName;let en=e=>{let{className:a,...t}=e;return(0,i.jsx)("div",{className:(0,g.cn)("flex flex-col space-y-2 text-center sm:text-left",a),...t})};en.displayName="AlertDialogHeader";let eo=e=>{let{className:a,...t}=e;return(0,i.jsx)("div",{className:(0,g.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...t})};eo.displayName="AlertDialogFooter";let ec=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,i.jsx)(et.hE,{ref:a,className:(0,g.cn)("text-lg font-semibold",t),...s})});ec.displayName=et.hE.displayName;let ed=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,i.jsx)(et.VY,{ref:a,className:(0,g.cn)("text-sm text-muted-foreground",t),...s})});ed.displayName=et.VY.displayName;let em=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,i.jsx)(et.rc,{ref:a,className:(0,g.cn)((0,n.r)(),t),...s})});em.displayName=et.rc.displayName;let ex=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,i.jsx)(et.ZD,{ref:a,className:(0,g.cn)((0,n.r)({variant:"outline"}),"mt-2 sm:mt-0",t),...s})});ex.displayName=et.ZD.displayName;var eu=t(54861),eh=t(81284);let ep={danger:{icon:eu.A,iconColor:"text-red-600",buttonClass:"bg-red-600 hover:bg-red-700 focus:ring-red-500",borderClass:"border-l-red-500"},warning:{icon:ea.A,iconColor:"text-orange-600",buttonClass:"bg-orange-600 hover:bg-orange-700 focus:ring-orange-500",borderClass:"border-l-orange-500"},info:{icon:eh.A,iconColor:"text-blue-600",buttonClass:"bg-blue-600 hover:bg-blue-700 focus:ring-blue-500",borderClass:"border-l-blue-500"},success:{icon:L.A,iconColor:"text-green-600",buttonClass:"bg-green-600 hover:bg-green-700 focus:ring-green-500",borderClass:"border-l-green-500"}};function eb(e){let{isOpen:a,onClose:t,onConfirm:l,title:r,description:n,confirmText:o="Conferma",cancelText:c="Annulla",variant:d="info",isLoading:m=!1,icon:x}=e,[u,h]=(0,s.useState)(!1),p=ep[d],b=x||p.icon;(0,s.useEffect)(()=>{let e=e=>{"Escape"!==e.key||!a||m||u||t()};return a&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[a,m,u,t]);let g=async()=>{try{h(!0);let e=l();e instanceof Promise&&await e,await new Promise(e=>setTimeout(e,300))}catch(e){console.error("Errore durante conferma:",e),h(!1);return}finally{h(!1)}},v=m||u;return(0,i.jsx)(ei,{open:a,onOpenChange:t,children:(0,i.jsxs)(er,{className:"border-l-4 ".concat(p.borderClass," max-w-md"),children:[(0,i.jsxs)(en,{children:[(0,i.jsxs)(ec,{className:"flex items-center space-x-3",children:[(0,i.jsx)(b,{className:"w-6 h-6 ".concat(p.iconColor," flex-shrink-0")}),(0,i.jsx)("span",{className:"text-lg font-semibold text-slate-900",children:r})]}),(0,i.jsx)(ed,{className:"text-base text-slate-600 leading-relaxed mt-2",children:n})]}),(0,i.jsxs)(eo,{className:"gap-3 mt-6",children:[(0,i.jsx)(ex,{onClick:t,disabled:v,className:"px-6 py-2 border-slate-300 text-slate-700 hover:bg-slate-50 focus:ring-slate-500",children:c}),(0,i.jsx)(em,{onClick:g,disabled:v,className:"px-6 py-2 text-white font-medium transition-all duration-200 ".concat(p.buttonClass," disabled:opacity-50 disabled:cursor-not-allowed"),children:v?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(ee.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Elaborazione..."]}):o})]})]})})}function eg(e){let{isOpen:a,onClose:t,onConfirm:l,cavo:r}=e,[n,o]=(0,s.useState)(!1),c=async()=>{if(r){o(!0);try{await l(r.id_cavo),t()}catch(e){console.error("Errore durante scollegamento:",e)}finally{o(!1)}}};return r?(0,i.jsx)(eb,{isOpen:a,onClose:t,onConfirm:c,title:"Conferma Scollegamento Cavo",description:"Sei sicuro di voler scollegare il cavo ".concat(r.id_cavo,"? Questa azione potrebbe influenzare lo stato di altri componenti e dovr\xe0 essere ricollegato manualmente."),confirmText:"Scollega",cancelText:"Annulla",variant:"warning",isLoading:n,icon:(0,i.jsx)(K.A,{className:"w-6 h-6"})}):null}function ev(e){let{isOpen:a,onClose:t,onGenerate:l,cavo:r}=e,[o,c]=(0,s.useState)(!1),[d,m]=(0,s.useState)({cavoId:"",fileName:"",format:"standard",includeTestData:!0,includePhotos:!1,emailRecipient:"",notes:""});(0,s.useState)(()=>{r&&m(e=>({...e,cavoId:r.id_cavo,fileName:"Certificato_".concat(r.id_cavo,"_").concat(new Date().toISOString().split("T")[0],".pdf")}))},[r]);let x=async()=>{if(r){c(!0);try{await l(d),t()}catch(e){console.error("Errore durante generazione PDF:",e)}finally{c(!1)}}};return r?(0,i.jsx)(Y.lG,{open:a,onOpenChange:t,children:(0,i.jsxs)(Y.Cf,{className:"max-w-2xl border-l-4 border-l-blue-500",children:[(0,i.jsxs)(Y.c7,{className:"bg-gradient-to-r from-blue-50 to-transparent p-6 -m-6 mb-4",children:[(0,i.jsxs)(Y.L3,{className:"flex items-center gap-3 text-xl",children:[(0,i.jsx)(J.A,{className:"w-6 h-6 text-blue-600"}),"Genera Certificato per Cavo ",r.id_cavo]}),(0,i.jsx)(Y.rr,{className:"text-base text-slate-600 mt-2",children:"Configura le opzioni per la generazione del certificato PDF"})]}),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"bg-slate-50 p-4 rounded-lg border",children:[(0,i.jsx)("h4",{className:"font-medium text-slate-900 mb-2",children:"Informazioni Cavo"}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"text-slate-600",children:"ID:"}),(0,i.jsx)("span",{className:"ml-2 font-mono",children:r.id_cavo})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"text-slate-600",children:"Sistema:"}),(0,i.jsx)("span",{className:"ml-2",children:r.sistema||"N/A"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"text-slate-600",children:"Tipologia:"}),(0,i.jsx)("span",{className:"ml-2",children:r.tipologia||"N/A"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"text-slate-600",children:"Stato:"}),(0,i.jsx)("span",{className:"ml-2",children:r.stato||"N/A"})]})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(T.J,{htmlFor:"fileName",children:"Nome File *"}),(0,i.jsx)(h.p,{id:"fileName",value:d.fileName,onChange:e=>m(a=>({...a,fileName:e.target.value})),placeholder:"Certificato_C001_2024-01-01.pdf",className:"font-mono text-sm"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(T.J,{htmlFor:"format",children:"Formato Certificato"}),(0,i.jsxs)(p.l6,{value:d.format,onValueChange:e=>m(a=>({...a,format:e})),children:[(0,i.jsx)(p.bq,{children:(0,i.jsx)(p.yv,{})}),(0,i.jsxs)(p.gC,{children:[(0,i.jsx)(p.eb,{value:"standard",children:"\uD83D\uDCC4 Standard CEI 64-8"}),(0,i.jsx)(p.eb,{value:"detailed",children:"\uD83D\uDCCB Dettagliato con Misure"}),(0,i.jsx)(p.eb,{value:"summary",children:"\uD83D\uDCDD Riassunto Esecutivo"})]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(T.J,{htmlFor:"emailRecipient",children:"Email Destinatario (Opzionale)"}),(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)(X.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400"}),(0,i.jsx)(h.p,{id:"emailRecipient",type:"email",value:d.emailRecipient,onChange:e=>m(a=>({...a,emailRecipient:e.target.value})),placeholder:"<EMAIL>",className:"pl-10"})]})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)(T.J,{children:"Opzioni Aggiuntive"}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,i.jsx)("input",{type:"checkbox",checked:d.includeTestData,onChange:e=>m(a=>({...a,includeTestData:e.target.checked})),className:"rounded border-slate-300 text-blue-600 focus:ring-blue-500"}),(0,i.jsx)("span",{className:"text-sm text-slate-700",children:"Includi Dati di Collaudo"})]}),(0,i.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,i.jsx)("input",{type:"checkbox",checked:d.includePhotos,onChange:e=>m(a=>({...a,includePhotos:e.target.checked})),className:"rounded border-slate-300 text-blue-600 focus:ring-blue-500"}),(0,i.jsx)("span",{className:"text-sm text-slate-700",children:"Includi Foto Installazione"})]})]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(T.J,{htmlFor:"notes",children:"Note Aggiuntive"}),(0,i.jsx)(Q.T,{id:"notes",value:d.notes,onChange:e=>m(a=>({...a,notes:e.target.value})),placeholder:"Note o commenti da includere nel certificato...",rows:3,className:"resize-none"})]})]}),(0,i.jsxs)(Y.Es,{className:"gap-3 pt-6 border-t",children:[(0,i.jsxs)(n.$,{variant:"outline",onClick:t,disabled:o,className:"px-6",children:[(0,i.jsx)(_.A,{className:"w-4 h-4 mr-2"}),"Annulla"]}),(0,i.jsx)(n.$,{onClick:x,disabled:o||!d.fileName.trim(),className:"px-6 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700",children:o?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(ee.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Generazione..."]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(q.A,{className:"w-4 h-4 mr-2"}),"Genera PDF"]})})]})]})}):null}function ef(e){let{isOpen:a,onClose:t,cavo:s,missingRequirements:l}=e;return s?(0,i.jsx)(Y.lG,{open:a,onOpenChange:t,children:(0,i.jsxs)(Y.Cf,{className:"max-w-md border-l-4 border-l-red-500",children:[(0,i.jsxs)(Y.c7,{className:"bg-gradient-to-r from-red-50 to-transparent p-6 -m-6 mb-4",children:[(0,i.jsxs)(Y.L3,{className:"flex items-center gap-3 text-xl",children:[(0,i.jsx)(ea.A,{className:"w-6 h-6 text-red-600"}),"Impossibile Certificare Cavo"]}),(0,i.jsxs)(Y.rr,{className:"text-base text-slate-600 mt-2",children:["Il cavo ",s.id_cavo," non pu\xf2 essere certificato nel suo stato attuale"]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)(o.Fc,{variant:"destructive",className:"border-red-200 bg-red-50",children:[(0,i.jsx)(ea.A,{className:"h-4 w-4"}),(0,i.jsx)(o.TN,{className:"font-medium",children:"Requisiti mancanti per la certificazione:"})]}),(0,i.jsx)("ul",{className:"space-y-2",children:l.map((e,a)=>(0,i.jsxs)("li",{className:"flex items-center gap-2 text-sm text-slate-700",children:[(0,i.jsx)(_.A,{className:"w-4 h-4 text-red-500 flex-shrink-0"}),e]},a))}),(0,i.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-200",children:[(0,i.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"\uD83D\uDCA1 Suggerimento"}),(0,i.jsx)("p",{className:"text-sm text-blue-800",children:"Completa tutti i requisiti sopra elencati prima di procedere con la certificazione. Puoi utilizzare le azioni disponibili nella tabella per aggiornare lo stato del cavo."})]})]}),(0,i.jsx)(Y.Es,{className:"pt-6 border-t",children:(0,i.jsxs)(n.$,{onClick:t,className:"px-6 bg-slate-600 hover:bg-slate-700",children:[(0,i.jsx)(U.A,{className:"w-4 h-4 mr-2"}),"Ho Capito"]})})]})}):null}var ej=t(46510);function eN(e){let{cavi:a=[],loading:t=!1,selectionEnabled:l=!1,selectedCavi:r=[],onSelectionChange:o,onStatusAction:c,onContextMenuAction:d,onDisconnectCable:h,onGeneratePDF:p,onCertifyCable:b}=e,[g,v]=(0,s.useState)(a),[f,j]=(0,s.useState)(a),[w,k]=(0,s.useState)(l),[T,O]=(0,s.useState)("id_cavo"),[F,D]=(0,s.useState)("asc"),[R,B]=(0,s.useState)({disconnect:{isOpen:!1,cavo:null},generatePDF:{isOpen:!1,cavo:null},certificationError:{isOpen:!1,cavo:null,missingRequirements:[]}}),[P,L]=(0,s.useState)(1),[M,Y]=(0,s.useState)(25),[Q,K]=(0,s.useState)(!1),X=(0,ej.E)(),ee=function(e,a){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];B(i=>({...i,[e]:{isOpen:!0,cavo:a,missingRequirements:t}}))},ea=e=>{B(a=>({...a,[e]:{isOpen:!1,cavo:null,missingRequirements:[]}}))},et=e=>{T===e?D(e=>"asc"===e?"desc":"asc"):(O(e),D("asc"))},ei=(0,s.useMemo)(()=>[...g].sort((e,a)=>{let t=e[T],i=a[T];return(("metri_teorici"===T||"metri_posati"===T)&&(t=parseFloat(t)||0,i=parseFloat(i)||0),"string"==typeof t&&(t=t.toLowerCase()),"string"==typeof i&&(i=i.toLowerCase()),t<i)?"asc"===F?-1:1:t>i?"asc"===F?1:-1:0}),[g,T,F]);(0,s.useEffect)(()=>{v(a)},[a]);let es=(0,s.useMemo)(()=>{let e=(P-1)*M,a=e+M,t=ei.slice(e,a),i=Math.ceil(ei.length/M),s=P<i,l=P>1;return{cavi:t,totalItems:ei.length,totalPages:i,hasNextPage:s,hasPrevPage:l,startIndex:e+1,endIndex:Math.min(a,ei.length)}},[ei,P,M]);(0,s.useEffect)(()=>{j(es.cavi)},[es.cavi]),(0,s.useEffect)(()=>{L(1)},[g.length]);let el=async e=>{try{h&&(await h(e),X.cavoDisconnected(e))}catch(e){X.error("Errore Scollegamento","Impossibile scollegare il cavo. Riprova.")}},er=async e=>{try{p&&(await p(e),X.pdfGenerated(e.fileName,e.cavoId))}catch(e){X.error("Errore Generazione PDF","Impossibile generare il certificato. Riprova.")}},en=async e=>{let a=[];if(e.metri_posati&&0!==parseFloat(e.metri_posati)||a.push("Metri posati non inseriti"),"Collegato"!==e.stato&&a.push("Cavo non collegato"),e.data_installazione||a.push("Data installazione mancante"),a.length>0)return void ee("certificationError",e,a);try{b&&(X.actionInProgress("Certificazione",e.id_cavo),await b(e.id_cavo),X.success("Cavo Certificato","Il cavo ".concat(e.id_cavo," \xe8 stato certificato con successo.")))}catch(a){X.certificationError(e.id_cavo,"Errore durante il processo di certificazione")}},eo=e=>{L(Math.max(1,Math.min(e,es.totalPages)))},ec=()=>{null==o||o([])},ed=es.cavi.length>0&&es.cavi.every(e=>r.includes(e.id_cavo)),em=es.cavi.some(e=>r.includes(e.id_cavo)),ex=e=>{let{field:a,children:t,className:s=""}=e;return(0,i.jsx)(V.Bc,{children:(0,i.jsxs)(V.m_,{children:[(0,i.jsx)(V.k$,{asChild:!0,children:(0,i.jsxs)("button",{onClick:()=>et(a),className:"flex items-center gap-2 font-medium text-left hover:text-blue-600 transition-colors ".concat(s),children:[t,T===a?"asc"===F?(0,i.jsx)(C.A,{className:"w-4 h-4 text-blue-600"}):(0,i.jsx)(y.A,{className:"w-4 h-4 text-blue-600"}):(0,i.jsx)(N.A,{className:"w-4 h-4 text-slate-400"})]})}),(0,i.jsx)(V.ZI,{children:(0,i.jsxs)("p",{children:["Clicca per ordinare per ",t]})})]})})},eu=e=>{let{onClick:a,icon:t,tooltip:s,variant:l="default",disabled:r=!1,className:o=""}=e;return(0,i.jsx)(V.Bc,{children:(0,i.jsxs)(V.m_,{children:[(0,i.jsx)(V.k$,{asChild:!0,children:(0,i.jsx)(n.$,{size:"sm",onClick:a,disabled:r,className:"h-8 w-8 p-0 ".concat({default:"bg-blue-600 hover:bg-blue-700 text-white",danger:"bg-red-600 hover:bg-red-700 text-white",success:"bg-green-600 hover:bg-green-700 text-white",warning:"bg-orange-600 hover:bg-orange-700 text-white"}[l]," ").concat(o," transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"),children:(0,i.jsx)(t,{className:"w-4 h-4"})})}),(0,i.jsx)(V.ZI,{children:(0,i.jsx)("p",{children:s})})]})})},eh=e=>{o&&o(e?f.map(e=>e.id_cavo):[])},ep=(e,a)=>{o&&o(a?[...r,e]:r.filter(a=>a!==e))},eb=(0,s.useMemo)(()=>{let e=[{field:"id_cavo",headerName:"ID",dataType:"text",width:70,align:"left",renderHeader:()=>(0,i.jsx)(ex,{field:"id_cavo",children:"ID"}),renderCell:e=>(0,i.jsx)("span",{className:"font-semibold text-mariner-900",children:e.id_cavo})},{field:"sistema",headerName:"Sistema",dataType:"text",width:80,renderHeader:()=>(0,i.jsx)(ex,{field:"sistema",children:"Sistema"}),renderCell:e=>(0,i.jsx)(Z,{text:e.sistema||"",maxLength:8})},{field:"utility",headerName:"Utility",dataType:"text",width:80,renderCell:e=>(0,i.jsx)(Z,{text:e.utility||"",maxLength:8})},{field:"tipologia",headerName:"Tipologia",dataType:"text",width:100,renderCell:e=>(0,i.jsx)(Z,{text:e.tipologia||"",maxLength:12})},{field:"formazione",headerName:"Form.",dataType:"text",align:"left",width:60,renderCell:e=>e.formazione||e.sezione},{field:"metri_teorici",headerName:"M.Teor.",dataType:"number",align:"left",width:70,renderHeader:()=>(0,i.jsx)(ex,{field:"metri_teorici",children:"M.Teor."}),renderCell:e=>e.metri_teorici?e.metri_teorici.toFixed(1):"0"},{field:"metri_posati",headerName:"M.Reali",dataType:"number",align:"left",width:70,renderHeader:()=>(0,i.jsx)(ex,{field:"metri_posati",children:"M.Reali"}),renderCell:e=>{let a=e.metri_posati||e.metratura_reale||0;return a?a.toFixed(1):"0"}},{field:"ubicazione_partenza",headerName:"Da",dataType:"text",width:140,renderCell:e=>(0,i.jsx)(Z,{text:e.da||e.ubicazione_partenza||"",maxLength:18})},{field:"ubicazione_arrivo",headerName:"A",dataType:"text",width:140,renderCell:e=>(0,i.jsx)(Z,{text:e.a||e.ubicazione_arrivo||"",maxLength:18})},{field:"id_bobina",headerName:"Bobina",dataType:"text",width:80,align:"center",renderCell:e=>{let a=e.id_bobina;if(a,!a||"N/A"===a)return(0,i.jsx)("span",{className:"text-gray-400",children:"-"});if("BOBINA_VUOTA"===a)return(0,i.jsx)(m.E,{variant:"outline",className:"text-xs px-2 py-0.5 text-orange-600 border-orange-300 bg-orange-50",children:"Vuota"});let t=a.match(/_B(.+)$/);return t||(t=a.match(/_b(.+)$/))||(t=a.match(/c\d+_[bB](\d+)$/))||(t=a.match(/(\d+)$/))?(0,i.jsx)("span",{className:"font-medium",children:t[1]}):(0,i.jsx)("span",{className:"font-medium text-xs",children:a})}},{field:"stato_installazione",headerName:"Stato",dataType:"text",align:"left",width:120,disableFilter:!0,renderHeader:()=>(0,i.jsx)(ex,{field:"stato",children:"Stato"}),renderCell:e=>eN(e)},{field:"collegamenti",headerName:"Collegamenti",dataType:"text",align:"left",width:180,disableFilter:!0,disableSort:!0,renderCell:e=>eC(e)},{field:"certificato",headerName:"Certificato",dataType:"text",align:"left",width:130,disableFilter:!0,disableSort:!0,renderCell:e=>ey(e)}];return w&&e.unshift({field:"selection",headerName:"",disableFilter:!0,disableSort:!0,width:50,align:"left",renderHeader:()=>(0,i.jsx)(x.S,{checked:r.length===f.length&&f.length>0,onCheckedChange:eh}),renderCell:e=>(0,i.jsx)(x.S,{checked:r.includes(e.id_cavo),onCheckedChange:a=>ep(e.id_cavo,a),onClick:e=>e.stopPropagation()})}),e},[w,r,f,eh,ep]),eN=e=>{let a=e.metri_posati||e.metratura_reale||0,t=e.comanda_posa,s=e.comanda_partenza,l=e.comanda_arrivo,r=e.comanda_certificazione,n=t||s||l||r;if(n&&"In corso"===e.stato_installazione)return(0,i.jsx)(m.E,{className:"bg-blue-600 text-white cursor-pointer hover:bg-blue-700 hover:scale-105 transition-all duration-200 hover:shadow-md px-3 py-1 font-semibold",onClick:a=>{a.stopPropagation(),null==c||c(e,"view_command",n)},children:n});let o=e.stato_installazione||"Da installare";return"Installato"===o||a>0?(0,i.jsx)(m.E,{className:"bg-green-100 text-green-800 cursor-pointer hover:bg-green-200 hover:text-green-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-green-300",onClick:a=>{a.stopPropagation(),null==c||c(e,"modify_reel")},children:"Installato"}):"In corso"===o?(0,i.jsx)(m.E,{className:"bg-yellow-100 text-yellow-800 cursor-pointer hover:bg-yellow-200 hover:text-yellow-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-yellow-300",onClick:a=>{a.stopPropagation(),null==c||c(e,"insert_meters")},children:"In corso"}):(0,i.jsx)(m.E,{variant:"outline",className:"text-gray-700 cursor-pointer hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium",onClick:a=>{a.stopPropagation(),null==c||c(e,"insert_meters")},children:"Da installare"})},eC=e=>{let a,t,s,l=e.metri_posati>0||e.metratura_reale>0,r=e.collegamento||e.collegamenti||0;if(!l)return(0,i.jsx)(m.E,{variant:"outline",className:"text-gray-400 cursor-not-allowed bg-gray-50 border-gray-200 px-3 py-1",children:"✕ Non disponibile"});switch(r){case 0:a="⚪⚪ Collega",t="connect_cable",s="bg-gray-100 text-gray-700 cursor-pointer hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-gray-300";break;case 1:a="\uD83D\uDFE2⚪ Completa",t="connect_arrival",s="bg-yellow-100 text-yellow-800 cursor-pointer hover:bg-yellow-200 hover:text-yellow-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-yellow-300";break;case 2:a="⚪\uD83D\uDFE2 Completa",t="connect_departure",s="bg-yellow-100 text-yellow-800 cursor-pointer hover:bg-yellow-200 hover:text-yellow-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-yellow-300";break;case 3:a="\uD83D\uDFE2\uD83D\uDFE2 Scollega",t="disconnect_cable",s="bg-green-100 text-green-800 cursor-pointer hover:bg-green-200 hover:text-green-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-green-300";break;default:a="Gestisci",t="manage_connections",s="bg-blue-100 text-blue-800 cursor-pointer hover:bg-blue-200 hover:text-blue-900 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border border-blue-300"}return(0,i.jsx)(m.E,{className:s,onClick:a=>{a.stopPropagation(),"disconnect_cable"===t?ee("disconnect",e):null==c||c(e,t)},children:a})},ey=e=>{let a=e.metri_posati>0||e.metratura_reale>0,t=!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato;return a?t?(0,i.jsxs)("div",{className:"flex gap-1",children:[(0,i.jsx)(eu,{onClick:()=>ee("generatePDF",e),icon:J.A,tooltip:"Genera Certificato PDF",variant:"success",className:"h-7 w-7"}),(0,i.jsx)(eu,{onClick:()=>ee("generatePDF",e),icon:q.A,tooltip:"Scarica Certificato",variant:"default",className:"h-7 w-7"})]}):(0,i.jsx)(m.E,{variant:"outline",className:"text-purple-700 cursor-pointer hover:bg-purple-50 hover:text-purple-800 hover:border-purple-300 transition-all duration-200 hover:scale-105 px-3 py-1 font-medium border-purple-200",onClick:a=>{a.stopPropagation(),en(e)},children:"\uD83D\uDD50 Certifica"}):(0,i.jsx)(m.E,{variant:"outline",className:"text-gray-400 cursor-not-allowed bg-gray-50 border-gray-200 px-3 py-1",children:"✕ Non disponibile"})};return(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)(G,{cavi:a,onFilteredDataChange:e=>{v(e)},loading:t,selectionEnabled:w,onSelectionToggle:()=>{k(!w),K(!w&&r.length>0)}}),(0,i.jsxs)("div",{className:"mb-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 bg-slate-50 p-4 rounded-lg border border-slate-200",children:[(0,i.jsxs)("div",{className:"flex items-center gap-4",children:[(0,i.jsxs)("div",{className:"text-sm text-slate-600",children:["Mostrando ",(0,i.jsx)("span",{className:"font-semibold",children:es.startIndex})," - ",(0,i.jsx)("span",{className:"font-semibold",children:es.endIndex})," di ",(0,i.jsx)("span",{className:"font-semibold",children:es.totalItems})," cavi"]}),w&&(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(V.Bc,{children:(0,i.jsxs)(V.m_,{children:[(0,i.jsx)(V.k$,{asChild:!0,children:(0,i.jsxs)("div",{onClick:ed?()=>{let e=new Set(es.cavi.map(e=>e.id_cavo)),a=r.filter(a=>!e.has(a));null==o||o(a)}:()=>{let e=[...new Set([...r,...es.cavi.map(e=>e.id_cavo)])];null==o||o(e)},className:"flex items-center gap-2 px-3 py-2 text-sm font-medium border border-slate-200 rounded-md hover:bg-slate-50 hover:border-slate-300 transition-all duration-200 cursor-pointer",children:[(0,i.jsx)(x.S,{checked:ed,ref:e=>{e&&(e.indeterminate=em&&!ed)}}),"Pagina"]})}),(0,i.jsx)(V.ZI,{children:(0,i.jsx)("p",{children:ed?"Deseleziona tutti i cavi visibili":"Seleziona tutti i cavi visibili"})})]})}),(0,i.jsx)(V.Bc,{children:(0,i.jsxs)(V.m_,{children:[(0,i.jsx)(V.k$,{asChild:!0,children:(0,i.jsxs)(n.$,{variant:"outline",size:"sm",onClick:r.length===ei.length?ec:()=>{let e=ei.map(e=>e.id_cavo);null==o||o(e)},className:"flex items-center gap-2",children:[(0,i.jsx)(H.A,{className:"w-4 h-4"}),"Tutti (",ei.length,")"]})}),(0,i.jsx)(V.ZI,{children:(0,i.jsx)("p",{children:r.length===ei.length?"Deseleziona tutti i cavi":"Seleziona tutti i cavi filtrati"})})]})}),r.length>0&&(0,i.jsxs)(m.E,{variant:"secondary",className:"bg-blue-100 text-blue-800",children:[r.length," selezionati"]})]})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("span",{className:"text-sm text-slate-600",children:"Righe per pagina:"}),(0,i.jsxs)("select",{value:M,onChange:e=>{Y(Number(e.target.value)),L(1)},className:"border border-slate-300 rounded px-2 py-1 text-sm",children:[(0,i.jsx)("option",{value:10,children:"10"}),(0,i.jsx)("option",{value:25,children:"25"}),(0,i.jsx)("option",{value:50,children:"50"}),(0,i.jsx)("option",{value:100,children:"100"})]})]})]}),(0,i.jsx)(I,{data:g,columns:eb,loading:t,emptyMessage:"Nessun cavo disponibile",onFilteredDataChange:e=>{j(e)},renderRow:(e,a)=>{let t=r.includes(e.id_cavo);return(0,i.jsx)(u.Hj,{className:"\n          ".concat(t?"bg-blue-50 border-blue-200":"bg-white","\n          hover:bg-blue-50 hover:border-blue-200 hover:shadow-sm\n          cursor-pointer border-b border-gray-200\n          transition-all duration-200 ease-in-out\n          ").concat(t?"ring-1 ring-blue-300":"","\n        "),onClick:()=>w&&ep(e.id_cavo,!t),onContextMenu:a=>{a.preventDefault(),null==d||d(e,"context_menu")},children:eb.map(a=>(0,i.jsx)(u.nA,{className:"\n              py-2 px-2 text-sm text-left\n              ".concat(t?"text-blue-900":"text-gray-900","\n              transition-colors duration-200\n            "),style:{width:a.width,...a.cellStyle},onClick:e=>{["stato_installazione","collegamenti","certificato"].includes(a.field)&&e.stopPropagation()},children:a.renderCell?a.renderCell(e):e[a.field]||(0,i.jsx)("span",{className:"text-gray-400",children:"-"})},a.field))},e.id_cavo)}}),es.totalPages>1&&(0,i.jsxs)("div",{className:"mt-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 bg-white p-4 rounded-lg border border-slate-200",children:[(0,i.jsxs)("div",{className:"text-sm text-slate-600",children:["Pagina ",(0,i.jsx)("span",{className:"font-semibold",children:P})," di ",(0,i.jsx)("span",{className:"font-semibold",children:es.totalPages})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(V.Bc,{children:(0,i.jsxs)(V.m_,{children:[(0,i.jsx)(V.k$,{asChild:!0,children:(0,i.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>eo(1),disabled:!es.hasPrevPage,className:"p-2",children:(0,i.jsx)(A.A,{className:"w-4 h-4"})})}),(0,i.jsx)(V.ZI,{children:(0,i.jsx)("p",{children:"Prima pagina"})})]})}),(0,i.jsx)(V.Bc,{children:(0,i.jsxs)(V.m_,{children:[(0,i.jsx)(V.k$,{asChild:!0,children:(0,i.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>eo(P-1),disabled:!es.hasPrevPage,className:"p-2",children:(0,i.jsx)(z.A,{className:"w-4 h-4"})})}),(0,i.jsx)(V.ZI,{children:(0,i.jsx)("p",{children:"Pagina precedente"})})]})}),(0,i.jsx)("div",{className:"flex items-center gap-1",children:Array.from({length:Math.min(5,es.totalPages)},(e,a)=>{let t;return t=es.totalPages<=5||P<=3?a+1:P>=es.totalPages-2?es.totalPages-4+a:P-2+a,(0,i.jsx)(n.$,{variant:P===t?"default":"outline",size:"sm",onClick:()=>eo(t),className:"w-8 h-8 p-0",children:t},t)})}),(0,i.jsx)(V.Bc,{children:(0,i.jsxs)(V.m_,{children:[(0,i.jsx)(V.k$,{asChild:!0,children:(0,i.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>eo(P+1),disabled:!es.hasNextPage,className:"p-2",children:(0,i.jsx)(S.A,{className:"w-4 h-4"})})}),(0,i.jsx)(V.ZI,{children:(0,i.jsx)("p",{children:"Pagina successiva"})})]})}),(0,i.jsx)(V.Bc,{children:(0,i.jsxs)(V.m_,{children:[(0,i.jsx)(V.k$,{asChild:!0,children:(0,i.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>eo(es.totalPages),disabled:!es.hasNextPage,className:"p-2",children:(0,i.jsx)(E.A,{className:"w-4 h-4"})})}),(0,i.jsx)(V.ZI,{children:(0,i.jsx)("p",{children:"Ultima pagina"})})]})})]})]}),w&&r.length>0&&(0,i.jsx)("div",{className:"fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-white border border-slate-300 rounded-lg shadow-xl z-50 p-4 min-w-[600px]",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full animate-pulse"}),(0,i.jsxs)(m.E,{variant:"secondary",className:"bg-blue-100 text-blue-800 font-semibold",children:[r.length," cavi selezionati"]})]}),(0,i.jsx)(V.Bc,{children:(0,i.jsxs)(V.m_,{children:[(0,i.jsx)(V.k$,{asChild:!0,children:(0,i.jsxs)(n.$,{variant:"ghost",size:"sm",onClick:ec,className:"text-slate-600 hover:text-slate-800",children:[(0,i.jsx)(_.A,{className:"w-4 h-4 mr-1"}),"Deseleziona tutto"]})}),(0,i.jsx)(V.ZI,{children:(0,i.jsx)("p",{children:"Rimuovi la selezione da tutti i cavi"})})]})})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(V.Bc,{children:(0,i.jsxs)(V.m_,{children:[(0,i.jsx)(V.k$,{asChild:!0,children:(0,i.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>{X.actionInProgress("Esportazione","".concat(r.length," cavi"))},className:"flex items-center gap-2 hover:bg-green-50 hover:border-green-300",children:[(0,i.jsx)(q.A,{className:"w-4 h-4"}),"Esporta"]})}),(0,i.jsx)(V.ZI,{children:(0,i.jsx)("p",{children:"Esporta i cavi selezionati in Excel"})})]})}),(0,i.jsx)(V.Bc,{children:(0,i.jsxs)(V.m_,{children:[(0,i.jsx)(V.k$,{asChild:!0,children:(0,i.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>{X.actionInProgress("Generazione PDF","".concat(r.length," cavi"))},className:"flex items-center gap-2 hover:bg-blue-50 hover:border-blue-300",children:[(0,i.jsx)(J.A,{className:"w-4 h-4"}),"PDF Bulk"]})}),(0,i.jsx)(V.ZI,{children:(0,i.jsx)("p",{children:"Genera PDF per tutti i cavi selezionati"})})]})}),(0,i.jsx)(V.Bc,{children:(0,i.jsxs)(V.m_,{children:[(0,i.jsx)(V.k$,{asChild:!0,children:(0,i.jsxs)(n.$,{size:"sm",onClick:()=>{X.actionInProgress("Aggiornamento Stato","".concat(r.length," cavi"))},className:"flex items-center gap-2 hover:bg-yellow-50 hover:border-yellow-300",children:[(0,i.jsx)(U.A,{className:"w-4 h-4"}),"Stato"]})}),(0,i.jsx)(V.ZI,{children:(0,i.jsx)("p",{children:"Cambia stato per tutti i cavi selezionati"})})]})}),(0,i.jsx)(V.Bc,{children:(0,i.jsxs)(V.m_,{children:[(0,i.jsx)(V.k$,{asChild:!0,children:(0,i.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>{X.actionInProgress("Assegnazione Comanda","".concat(r.length," cavi"))},className:"flex items-center gap-2 hover:bg-purple-50 hover:border-purple-300",children:[(0,i.jsx)($.A,{className:"w-4 h-4"}),"Comanda"]})}),(0,i.jsx)(V.ZI,{children:(0,i.jsx)("p",{children:"Assegna comanda a tutti i cavi selezionati"})})]})}),(0,i.jsx)("div",{className:"w-px h-6 bg-slate-300"}),(0,i.jsx)(V.Bc,{children:(0,i.jsxs)(V.m_,{children:[(0,i.jsx)(V.k$,{asChild:!0,children:(0,i.jsxs)(n.$,{variant:"destructive",size:"sm",onClick:()=>{confirm("Sei sicuro di voler eliminare ".concat(r.length," cavi selezionati?"))&&X.actionInProgress("Eliminazione","".concat(r.length," cavi"))},className:"flex items-center gap-2",children:[(0,i.jsx)(W.A,{className:"w-4 h-4"}),"Elimina"]})}),(0,i.jsx)(V.ZI,{children:(0,i.jsx)("p",{children:"Elimina tutti i cavi selezionati (azione irreversibile)"})})]})})]})]})}),(0,i.jsx)(eg,{isOpen:R.disconnect.isOpen,onClose:()=>ea("disconnect"),onConfirm:el,cavo:R.disconnect.cavo}),(0,i.jsx)(ev,{isOpen:R.generatePDF.isOpen,onClose:()=>ea("generatePDF"),onGenerate:er,cavo:R.generatePDF.cavo}),(0,i.jsx)(ef,{isOpen:R.certificationError.isOpen,onClose:()=>ea("certificationError"),cavo:R.certificationError.cavo,missingRequirements:R.certificationError.missingRequirements})]})}var eC=t(33109),ey=t(72713),e_=t(14186),ew=t(79397);function eA(e){let{cavi:a,filteredCavi:t,className:l,revisioneCorrente:n,activeFilter:o="all",onFilterChange:c}=e,d=(0,s.useMemo)(()=>{let e=a.length,i=t.length,s=t.filter(e=>"Installato"===e.stato_installazione||e.metri_posati&&e.metri_posati>0||e.metratura_reale&&e.metratura_reale>0).length,l=t.filter(e=>"In corso"===e.stato_installazione).length,r=t.filter(e=>3===(e.collegamento||e.collegamenti||0)).length,n=t.filter(e=>{let a=e.collegamento||e.collegamenti||0;return 1===a||2===a}).length,o=t.filter(e=>0===(e.collegamento||e.collegamenti||0)&&(e.metri_posati>0||e.metratura_reale>0)).length,c=t.filter(e=>!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato).length,d=t.reduce((e,a)=>e+(a.metri_teorici||0),0),m=t.reduce((e,a)=>e+(a.metri_posati||a.metratura_reale||0),0),x=0===i?0:Math.round(100*(((s-r)*2+(r-c)*3.5+4*c)/(4*i)*100))/100;return{totalCavi:e,filteredCount:i,installati:s,inCorso:l,daInstallare:i-s-l,collegati:r,parzialmenteCollegati:n,nonCollegati:o,certificati:c,metriTotali:d,metriInstallati:m,percentualeInstallazione:x}},[a,t]),x=e=>{let{icon:a,label:t,value:s,total:l,color:r,filterType:n,tooltip:d,trend:m}=e,x=o===n,u=l?Math.round(s/l*100):0;return(0,i.jsx)(V.Bc,{children:(0,i.jsxs)(V.m_,{children:[(0,i.jsx)(V.k$,{asChild:!0,children:(0,i.jsxs)("div",{onClick:()=>null==c?void 0:c(x?"all":n),className:"\n                relative p-3 rounded-lg border-2 transition-all duration-200 cursor-pointer\n                hover:scale-105 hover:shadow-md group\n                ".concat(x?"".concat(r," border-current shadow-lg ring-2 ring-offset-2 ring-current/20"):"bg-white border-slate-200 hover:border-slate-300","\n              "),children:[x&&(0,i.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-blue-600 rounded-full flex items-center justify-center",children:(0,i.jsx)(w.A,{className:"w-2 h-2 text-white"})}),(0,i.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,i.jsx)(a,{className:"w-5 h-5 ".concat(x?"text-white":"text-slate-600")}),void 0!==m&&(0,i.jsxs)("div",{className:"flex items-center text-xs ".concat(x?"text-white/80":"text-slate-500"),children:[(0,i.jsx)(eC.A,{className:"w-3 h-3 mr-1"}),m>0?"+":"",m,"%"]})]}),(0,i.jsx)("div",{className:"text-2xl font-bold ".concat(x?"text-white":"text-slate-900"),children:s}),(0,i.jsxs)("div",{className:"flex items-center justify-between mt-1",children:[(0,i.jsx)("span",{className:"text-sm font-medium ".concat(x?"text-white/90":"text-slate-600"),children:t}),l&&(0,i.jsxs)("span",{className:"text-xs ".concat(x?"text-white/80":"text-slate-500"),children:[u,"%"]})]}),l&&(0,i.jsx)("div",{className:"mt-2 w-full bg-slate-200 rounded-full h-1.5",children:(0,i.jsx)("div",{className:"h-1.5 rounded-full transition-all duration-300 ".concat(x?"bg-white/80":r.replace("bg-","bg-").replace("text-","bg-")),style:{width:"".concat(Math.min(u,100),"%")}})}),(0,i.jsx)("div",{className:"absolute inset-0 rounded-lg bg-gradient-to-r from-transparent to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200"})]})}),(0,i.jsx)(V.ZI,{side:"bottom",className:"max-w-xs",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("p",{className:"font-medium",children:d}),l&&(0,i.jsxs)("p",{className:"text-xs text-slate-500 mt-1",children:["Clicca per filtrare • ",s," di ",l," cavi (",u,"%)"]})]})})]})})};return(0,i.jsx)(r.Zp,{className:l,children:(0,i.jsxs)(r.Wu,{className:"p-1.5",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ey.A,{className:"h-5 w-5 text-mariner-600"}),(0,i.jsx)("span",{className:"text-lg font-semibold text-mariner-900",children:"Statistiche Cavi"}),"all"!==o&&(0,i.jsxs)(m.E,{variant:"secondary",className:"bg-blue-100 text-blue-800 flex items-center gap-1",children:[(0,i.jsx)(w.A,{className:"w-3 h-3"}),"Filtro attivo",(0,i.jsx)("button",{onClick:()=>null==c?void 0:c("all"),className:"ml-1 hover:bg-blue-200 rounded-full p-0.5",children:(0,i.jsx)(_.A,{className:"w-3 h-3"})})]})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[n&&(0,i.jsxs)(m.E,{variant:"outline",className:"text-sm font-medium",children:["Rev. ",n]}),(0,i.jsxs)(m.E,{variant:"secondary",className:"bg-slate-100 text-slate-700",children:[d.filteredCount," di ",d.totalCavi," cavi"]})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4",children:[(0,i.jsx)(x,{icon:L.A,label:"Installati",value:d.installati,total:d.filteredCount,color:"bg-green-500 text-white",filterType:"installati",tooltip:"Cavi completamente installati con metri posati registrati",trend:5}),(0,i.jsx)(x,{icon:e_.A,label:"In Corso",value:d.inCorso,total:d.filteredCount,color:"bg-yellow-500 text-white",filterType:"in_corso",tooltip:"Cavi in fase di installazione",trend:-2}),(0,i.jsx)(x,{icon:ea.A,label:"Da Installare",value:d.daInstallare,total:d.filteredCount,color:"bg-gray-500 text-white",filterType:"da_installare",tooltip:"Cavi non ancora iniziati",trend:-3}),(0,i.jsx)(x,{icon:M.A,label:"Collegati",value:d.collegati,total:d.filteredCount,color:"bg-blue-500 text-white",filterType:"collegati",tooltip:"Cavi completamente collegati su entrambi i lati",trend:8}),(0,i.jsx)(x,{icon:$.A,label:"Certificati",value:d.certificati,total:d.filteredCount,color:"bg-purple-500 text-white",filterType:"certificati",tooltip:"Cavi con certificazione completata",trend:12}),(0,i.jsxs)("div",{className:"bg-gradient-to-br from-indigo-50 to-indigo-100 p-4 rounded-lg border border-indigo-200",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,i.jsx)(ew.A,{className:"w-5 h-5 text-indigo-600"}),(0,i.jsxs)("span",{className:"text-xs text-indigo-600 font-medium",children:[Math.round(d.metriInstallati/d.metriTotali*100),"%"]})]}),(0,i.jsxs)("div",{className:"text-2xl font-bold text-indigo-900 mb-1",children:[d.metriInstallati.toLocaleString(),"m"]}),(0,i.jsxs)("div",{className:"text-sm text-indigo-700 mb-2",children:["di ",d.metriTotali.toLocaleString(),"m"]}),(0,i.jsx)("div",{className:"w-full bg-indigo-200 rounded-full h-2",children:(0,i.jsx)("div",{className:"h-2 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-full transition-all duration-300",style:{width:"".concat(Math.min(d.metriInstallati/d.metriTotali*100,100),"%")}})})]})]}),d.filteredCount>0&&(0,i.jsxs)("div",{className:"mt-6 bg-gradient-to-br from-slate-50 to-slate-100 p-4 rounded-xl border border-slate-200 shadow-sm",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(eC.A,{className:"w-5 h-5 text-slate-600"}),(0,i.jsx)("span",{className:"text-sm font-semibold text-slate-700",children:"IAP - Indice Avanzamento Ponderato"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsxs)("span",{className:"text-2xl font-bold ".concat(d.percentualeInstallazione>=80?"text-emerald-600":d.percentualeInstallazione>=50?"text-yellow-600":d.percentualeInstallazione>=25?"text-orange-600":"text-red-600"),children:[d.percentualeInstallazione.toFixed(1),"%"]}),(0,i.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(d.percentualeInstallazione>=80?"bg-emerald-500":d.percentualeInstallazione>=50?"bg-yellow-500":d.percentualeInstallazione>=25?"bg-orange-500":"bg-red-500"," animate-pulse")})]})]}),(0,i.jsx)("div",{className:"relative w-full bg-slate-200 rounded-full h-3 mb-3 overflow-hidden",children:(0,i.jsx)("div",{className:"h-3 rounded-full transition-all duration-1000 ease-out relative ".concat(d.percentualeInstallazione>=80?"bg-gradient-to-r from-emerald-400 via-emerald-500 to-emerald-600":d.percentualeInstallazione>=50?"bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600":d.percentualeInstallazione>=25?"bg-gradient-to-r from-orange-400 via-orange-500 to-orange-600":"bg-gradient-to-r from-red-400 via-red-500 to-red-600"),style:{width:"".concat(Math.min(d.percentualeInstallazione,100),"%")},children:(0,i.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"})})}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-xs",children:[(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsx)("div",{className:"text-slate-600 font-medium",children:"Composizione Pesi:"}),(0,i.jsxs)("div",{className:"text-slate-500",children:["• Posa: ",(0,i.jsx)("span",{className:"font-semibold text-green-600",children:"2.0"}),(0,i.jsx)("br",{}),"• Collegamento: ",(0,i.jsx)("span",{className:"font-semibold text-blue-600",children:"1.5"}),(0,i.jsx)("br",{}),"• Certificazione: ",(0,i.jsx)("span",{className:"font-semibold text-purple-600",children:"0.5"})]})]}),(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsx)("div",{className:"text-slate-600 font-medium",children:"Stato Attuale:"}),(0,i.jsxs)("div",{className:"text-slate-500",children:["• ",(0,i.jsx)("span",{className:"font-semibold text-green-600",children:d.installati})," Installati",(0,i.jsx)("br",{}),"• ",(0,i.jsx)("span",{className:"font-semibold text-blue-600",children:d.collegati})," Collegati",(0,i.jsx)("br",{}),"• ",(0,i.jsx)("span",{className:"font-semibold text-purple-600",children:d.certificati})," Certificati"]})]})]})]})]})})}var ez=t(6740),eS=t(85339);function eE(e){let{open:a,onClose:t,cavo:l,cantiere:r,onSuccess:x,onError:u}=e,{cantiere:p}=(0,c.A)(),b=r||p,[g,v]=(0,s.useState)("assegna_nuova"),[f,j]=(0,s.useState)(""),[N,C]=(0,s.useState)([]),[y,_]=(0,s.useState)(!1),[w,A]=(0,s.useState)(!1),[z,S]=(0,s.useState)(""),[E,I]=(0,s.useState)(""),[k,T]=(0,s.useState)("compatibili");(0,s.useEffect)(()=>{a&&(v("assegna_nuova"),j(""),I(""),T("compatibili"),S(""),(null==b?void 0:b.id_cantiere)&&F())},[a,null==b?void 0:b.id_cantiere]);let F=async()=>{if(!(null==b?void 0:b.id_cantiere))return void S("Cantiere non disponibile");try{_(!0),S(""),console.log("\uD83D\uDD04 ModificaBobinaDialog: Caricamento bobine per cantiere:",b.id_cantiere);let e=await d.Fw.getBobine(b.id_cantiere),a=[];if(Array.isArray(e))a=e;else if(e&&Array.isArray(e.data))a=e.data;else if(e&&e.bobine&&Array.isArray(e.bobine))a=e.bobine;else throw Error("Formato risposta API non valido");let t=a.filter(e=>"Terminata"!==e.stato_bobina&&"Over"!==e.stato_bobina&&e.metri_residui>0);C(t),console.log("✅ ModificaBobinaDialog: Bobine caricate:",t.length),console.log("\uD83D\uDCCB ModificaBobinaDialog: Dettaglio bobine:",t.map(e=>({id:e.id_bobina,tipologia:e.tipologia,formazione:e.formazione,metri_residui:e.metri_residui})))}catch(e){console.error("❌ ModificaBobinaDialog: Errore caricamento bobine:",e),S("Errore nel caricamento delle bobine: ".concat(e.message)),C([])}finally{_(!1)}},D=e=>{if(!e||"BOBINA_VUOTA"===e)return"VUOTA";if(e&&e.includes("_B"))return e.split("_B")[1];let a=N.find(a=>a.id_bobina===e);return a&&a.numero_bobina||e},R=(()=>{if(!l)return[];let e=N.filter(e=>{let a=e.tipologia===l.tipologia&&e.formazione===l.sezione,t=""===E||e.id_bobina.toLowerCase().includes(E.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(E.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(E.toLowerCase());return a&&t&&e.metri_residui>0});return console.log("\uD83D\uDD0D ModificaBobinaDialog: Filtro compatibili:",{cavoTipologia:l.tipologia,cavoSezione:l.sezione,totaleBobine:N.length,bobineCompatibili:e.length,searchText:E}),e})(),B=l?N.filter(e=>{let a=e.tipologia!==l.tipologia||e.formazione!==l.sezione,t=""===E||e.id_bobina.toLowerCase().includes(E.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(E.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(E.toLowerCase());return a&&t&&e.metri_residui>0}):[];(0,s.useEffect)(()=>{a&&l&&(F(),v("assegna_nuova"),j(""),I(""),S(""))},[a,l,b]);let P=()=>{v("assegna_nuova"),j(""),I(""),S(""),t()},M=async()=>{if(console.log("\uD83D\uDD04 ModificaBobinaDialog: Salvataggio:",{selectedOption:g,selectedBobina:f,cavoId:null==l?void 0:l.id_cavo,cantiereId:null==b?void 0:b.id_cantiere}),l)try{if(A(!0),S(""),"assegna_nuova"===g){if(!f)return void u("Selezionare una bobina");let e=await d.At.updateMetriPosati(null==b?void 0:b.id_cantiere,l.id_cavo,l.metratura_reale||0,f,!0);console.log("✅ ModificaBobinaDialog: Bobina aggiornata:",e),x("Bobina aggiornata con successo per il cavo ".concat(l.id_cavo)),P()}else if("rimuovi_bobina"===g){let e=await d.At.updateMetriPosati(null==b?void 0:b.id_cantiere,l.id_cavo,l.metratura_reale||0,"BOBINA_VUOTA",!0);console.log("✅ ModificaBobinaDialog: Bobina rimossa:",e),x("Bobina rimossa con successo dal cavo ".concat(l.id_cavo)),P()}}catch(t){var e,a;console.error("❌ ModificaBobinaDialog: Errore salvataggio:",t),u((null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||t.message||"Errore durante la modifica della bobina")}finally{A(!1)}};return l?(0,i.jsx)(i.Fragment,{children:(0,i.jsx)(Y.lG,{open:a,onOpenChange:P,children:(0,i.jsxs)(Y.Cf,{className:"max-w-4xl max-h-[90vh] flex flex-col",children:[(0,i.jsx)(Y.c7,{children:(0,i.jsxs)(Y.L3,{children:["Modifica Bobina Cavo ",l.id_cavo]})}),(0,i.jsxs)("div",{className:"flex-1 overflow-hidden space-y-6",children:[(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)($.A,{className:"h-5 w-5 text-blue-600"}),(0,i.jsx)("h3",{className:"font-medium text-gray-900",children:"Cavo Selezionato"})]}),(0,i.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-200",children:(0,i.jsxs)("div",{className:"text-sm font-medium text-blue-800",children:["Informazioni Cavo / Tipologia: ",l.tipologia||"N/A"," / Da: ",l.ubicazione_partenza||"N/A"," / Formazione: ",l.sezione||"N/A"," / A: ",l.ubicazione_arrivo||"N/A"," / Metri Posati: ",l.metratura_reale||0," m"]})})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)("h3",{className:"font-medium",children:"Opzioni di modifica"}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,i.jsx)("input",{type:"radio",name:"operazione",value:"assegna_nuova",checked:"assegna_nuova"===g,onChange:e=>v(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,i.jsx)("span",{className:"text-sm",children:"Cambia bobina"})]}),(0,i.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,i.jsx)("input",{type:"radio",name:"operazione",value:"rimuovi_bobina",checked:"rimuovi_bobina"===g,onChange:e=>v(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,i.jsx)("span",{className:"text-sm",children:"Bobina vuota"})]})]})]}),"assegna_nuova"===g&&(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)("h3",{className:"font-medium",children:"Seleziona bobina"}),(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)(O.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,i.jsx)(h.p,{placeholder:"Cerca bobina per ID, tipologia o numero...",value:E,onChange:e=>I(e.target.value),className:"pl-10"})]}),(0,i.jsxs)("div",{className:"flex space-x-1 border-b",children:[(0,i.jsx)("button",{onClick:()=>T("compatibili"),className:"px-4 py-2 text-sm font-medium border-b-2 transition-colors ".concat("compatibili"===k?"border-green-500 text-green-600 bg-green-50":"border-transparent text-gray-500 hover:text-gray-700"),children:(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(L.A,{className:"h-4 w-4"}),(0,i.jsxs)("span",{children:["Bobine Compatibili (",R.length,")"]})]})}),(0,i.jsx)("button",{onClick:()=>T("incompatibili"),className:"px-4 py-2 text-sm font-medium border-b-2 transition-colors ".concat("incompatibili"===k?"border-amber-500 text-amber-600 bg-amber-50":"border-transparent text-gray-500 hover:text-gray-700"),children:(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(ea.A,{className:"h-4 w-4"}),(0,i.jsxs)("span",{children:["Bobine Incompatibili (",B.length,")"]})]})})]}),(0,i.jsx)("div",{className:"max-h-64 overflow-y-auto border rounded-lg",children:y?(0,i.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,i.jsx)(ee.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,i.jsx)("span",{children:"Caricamento bobine..."})]}):(0,i.jsx)("div",{className:"divide-y",children:"compatibili"===k?0===R.length?(0,i.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina compatibile trovata"}):R.map(e=>(0,i.jsx)("div",{className:"p-3 cursor-pointer transition-all ".concat(f===e.id_bobina?"bg-green-100 border-l-4 border-green-500":"hover:bg-green-50"),onClick:()=>j(e.id_bobina),children:(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[f===e.id_bobina&&(0,i.jsx)(L.A,{className:"h-5 w-5 text-green-600"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium text-sm",children:D(e.id_bobina)}),(0,i.jsxs)("div",{className:"text-xs text-gray-500",children:[e.tipologia," - ",e.formazione]})]})]}),(0,i.jsxs)(m.E,{variant:"outline",className:"bg-green-100 text-green-800 border-green-300",children:[e.metri_residui,"m"]})]})},e.id_bobina)):0===B.length?(0,i.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina incompatibile trovata"}):B.map(e=>(0,i.jsx)("div",{className:"p-3 cursor-pointer transition-all ".concat(f===e.id_bobina?"bg-amber-100 border-l-4 border-amber-500":"hover:bg-amber-50"),onClick:()=>j(e.id_bobina),children:(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[f===e.id_bobina&&(0,i.jsx)(L.A,{className:"h-5 w-5 text-amber-600"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium text-sm",children:D(e.id_bobina)}),(0,i.jsxs)("div",{className:"text-xs text-gray-500",children:[e.tipologia," - ",e.formazione]})]})]}),(0,i.jsxs)(m.E,{variant:"outline",className:"bg-amber-100 text-amber-800 border-amber-300",children:[e.metri_residui,"m"]})]})},e.id_bobina))})})]}),z&&(0,i.jsxs)(o.Fc,{variant:"destructive",children:[(0,i.jsx)(eS.A,{className:"h-4 w-4"}),(0,i.jsx)(o.TN,{children:z})]})]}),(0,i.jsxs)(Y.Es,{children:[(0,i.jsx)(n.$,{variant:"outline",onClick:P,disabled:w,children:"Annulla"}),(0,i.jsxs)(n.$,{onClick:M,disabled:w,children:[w&&(0,i.jsx)(ee.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salva"]})]})]})})}):null}function eI(e){let{open:a,onClose:t,cavo:l,cantiere:r,onSuccess:x,onError:u}=e,{cantiere:p}=(0,c.A)(),b=r||p,[g,v]=(0,s.useState)({metri_posati:"",id_bobina:""});(0,s.useEffect)(()=>{console.log("\uD83D\uDCCA InserisciMetriDialog: FormData aggiornato:",{hasMetri:!!g.metri_posati,hasBobina:!!g.id_bobina,metri_posati:g.metri_posati,id_bobina:g.id_bobina})},[g]);let[f,j]=(0,s.useState)({}),[N,C]=(0,s.useState)({}),[y,w]=(0,s.useState)(!1),[A,z]=(0,s.useState)([]),[S,E]=(0,s.useState)(!1),[I,k]=(0,s.useState)(""),[F,D]=(0,s.useState)(!1);(0,s.useEffect)(()=>{a&&b&&U()},[a,b]),(0,s.useEffect)(()=>{a&&l&&(v({metri_posati:"",id_bobina:""}),j({}),C({}),k(""))},[a,l]);let R=e=>{if(!e||"BOBINA_VUOTA"===e)return"VUOTA";if(e&&e.includes("_B"))return e.split("_B")[1];let a=A.find(a=>a.id_bobina===e);return a&&a.numero_bobina||e};A.filter(e=>{if(!I)return!0;let a=I.toLowerCase();return e.id_bobina.toLowerCase().includes(a)||e.tipologia.toLowerCase().includes(a)||e.formazione.toLowerCase().includes(a)||R(e.id_bobina).toLowerCase().includes(a)});let B=l?A.filter(e=>{let a=e.tipologia===l.tipologia&&e.sezione===l.sezione,t=""===I||e.id_bobina.toLowerCase().includes(I.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(I.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(I.toLowerCase());return a&&t&&e.metri_residui>0}):[],P=l?A.filter(e=>{let a=e.tipologia!==l.tipologia||e.sezione!==l.sezione,t=""===I||e.id_bobina.toLowerCase().includes(I.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(I.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(I.toLowerCase());return a&&t&&e.metri_residui>0}):[],M=e=>{console.log("\uD83C\uDFAF Bobina selezionata:",{id:e.id_bobina,numero:R(e.id_bobina),tipologia:e.tipologia,formazione:e.formazione,metri_residui:e.metri_residui}),v(a=>({...a,id_bobina:e.id_bobina})),j(e=>({...e,id_bobina:void 0}))};(0,s.useEffect)(()=>{a&&l&&(b&&U(),v({metri_posati:"0",id_bobina:""}),j({}),C({}),k(""))},[a,l,b]),(0,s.useEffect)(()=>{g.metri_posati&&l?$(parseFloat(g.metri_posati)):(j(e=>({...e,metri_posati:void 0})),C(e=>({...e,metri_posati:void 0})))},[g.metri_posati,l]);let $=e=>{if(!l)return;let a={...f},t={...N};delete a.metri_posati,delete t.metri_posati,e>1.1*(l.metri_teorici||0)?t.metri_posati="Attenzione: i metri posati superano del 10% i metri teorici (".concat(l.metri_teorici,"m)"):e>(l.metri_teorici||0)&&(t.metri_posati="Metratura superiore ai metri teorici"),j(a),C(t)},U=async()=>{if(console.log({cavo:!!l,cantiere:!!b,cavoId:null==l?void 0:l.id_cavo,cantiereId:null==b?void 0:b.id_cantiere}),l&&b)try{E(!0);let e=await d.Fw.getBobine(b.id_cantiere),a=[];if(Array.isArray(e))a=e;else if(e&&Array.isArray(e.data))a=e.data;else if(e&&e.bobine&&Array.isArray(e.bobine))a=e.bobine;else throw Error("Formato risposta API non valido");let t=a.filter(e=>"Terminata"!==e.stato_bobina&&"Over"!==e.stato_bobina&&e.metri_residui>0);if(l){console.log({tipologia:l.tipologia,sezione:l.sezione});let e=t.filter(e=>e.tipologia===l.tipologia&&e.sezione===l.sezione),a=t.filter(e=>e.tipologia!==l.tipologia||e.sezione!==l.sezione);e.sort((e,a)=>a.metri_residui-e.metri_residui),a.sort((e,a)=>a.metri_residui-e.metri_residui);let i=[...e,...a];z(i)}else t.sort((e,a)=>a.metri_residui-e.metri_residui),z(t)}catch(i){var e,a,t;console.log({message:i.message,response:i.response,status:null==(e=i.response)?void 0:e.status,data:null==(a=i.response)?void 0:a.data}),(null==(t=i.response)?void 0:t.status)!==404&&u("Errore nel caricamento delle bobine. Puoi comunque usare BOBINA VUOTA."),z([])}finally{E(!1)}},V=async()=>{if(console.log({cavo:null==l?void 0:l.id_cavo,metri_posati:g.metri_posati,id_bobina:g.id_bobina}),!l)return;if(!g.metri_posati||0>parseFloat(g.metri_posati))return void u("Inserire metri posati validi (≥ 0)");if(!g.id_bobina)return void u("Selezionare una bobina o BOBINA VUOTA");let e=parseFloat(g.metri_posati);if("BOBINA_VUOTA"!==g.id_bobina){let e=A.find(e=>e.id_bobina===g.id_bobina);e&&e.metri_residui}try{if(w(!0),!b)throw Error("Cantiere non selezionato");console.log({cantiere:b.id_cantiere,cavo:l.id_cavo,metri:e,bobina:g.id_bobina,isBobinaVuota:"BOBINA_VUOTA"===g.id_bobina}),await d.At.updateMetriPosati(b.id_cantiere,l.id_cavo,e,g.id_bobina,!0),x("Metri posati aggiornati con successo per il cavo ".concat(l.id_cavo,": ").concat(e,"m")),t()}catch(e){var a,i;u((null==(i=e.response)||null==(a=i.data)?void 0:a.detail)||e.message||"Errore durante il salvataggio dei metri posati")}finally{w(!1)}},G=()=>{y||(v({metri_posati:"",id_bobina:""}),j({}),C({}),k(""),t())};return l?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(Y.lG,{open:a,onOpenChange:G,children:(0,i.jsxs)(Y.Cf,{className:"max-w-7xl h-[90vh] flex flex-col",children:[(0,i.jsxs)(Y.c7,{className:"flex-shrink-0",children:[(0,i.jsxs)(Y.L3,{className:"flex items-center gap-2",children:[(0,i.jsx)(ez.A,{className:"h-5 w-5"}),"Inserisci Metri Posati - ",l.id_cavo]}),(0,i.jsx)(Y.rr,{children:"Inserisci i metri effettivamente posati per il cavo e seleziona una bobina o usa BOBINA VUOTA"})]}),(0,i.jsxs)("div",{className:"flex-1 overflow-y-auto space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-4",children:[(0,i.jsx)("div",{className:"lg:col-span-2",children:(0,i.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-200",children:[(0,i.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"Informazioni Cavo"}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Tipologia:"})," ",l.tipologia||"N/A"]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Da:"})," ",l.ubicazione_partenza||"N/A"]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Formazione:"})," ",l.sezione||"N/A"]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"A:"})," ",l.ubicazione_arrivo||"N/A"]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Metri teorici:"})," ",l.metri_teorici||"N/A"," m"]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Gi\xe0 posati:"})," ",l.metratura_reale||0," m"]})]})]})}),(0,i.jsx)("div",{className:"lg:col-span-1",children:(0,i.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-300 h-full",children:[(0,i.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"Metri da Installare"}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(T.J,{htmlFor:"metri",className:"text-sm font-medium",children:"Metri Posati"}),(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)(h.p,{id:"metri",type:"number",value:g.metri_posati,onChange:e=>v(a=>({...a,metri_posati:e.target.value})),placeholder:"Inserisci metri posati",disabled:y,step:"0.1",min:"0",className:"text-lg font-bold text-center border-2 border-blue-400 focus:border-blue-600",autoFocus:!0}),(0,i.jsx)("span",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-lg font-bold text-blue-600",children:"m"})]}),f.metri_posati&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:f.metri_posati}),N.metri_posati&&(0,i.jsx)("p",{className:"text-sm text-amber-600",children:N.metri_posati})]})]})})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h3",{className:"font-semibold text-blue-800 text-lg",children:"Selezione Bobina"}),(0,i.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-12 gap-3 items-center",children:[(0,i.jsx)("div",{className:"sm:col-span-5",children:(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)(O.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,i.jsx)(h.p,{placeholder:"ID, tipologia, formazione...",value:I,onChange:e=>k(e.target.value),className:"pl-10",disabled:y}),I&&(0,i.jsx)(n.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0",onClick:()=>k(""),children:(0,i.jsx)(_.A,{className:"h-4 w-4"})})]})}),(0,i.jsx)("div",{className:"sm:col-span-7",children:(0,i.jsxs)(n.$,{type:"button",variant:"BOBINA_VUOTA"===g.id_bobina?"default":"outline",className:"w-full h-10 font-bold flex items-center justify-center gap-2 ".concat("BOBINA_VUOTA"===g.id_bobina?"bg-green-600 hover:bg-green-700 text-white":"border-blue-400 text-blue-700 hover:bg-blue-50"),onClick:()=>{console.log("\uD83C\uDFAF BOBINA VUOTA selezionata - cavo sar\xe0 posato senza bobina specifica"),v(e=>({...e,id_bobina:"BOBINA_VUOTA"})),j(e=>{let a={...e};return delete a.id_bobina,a})},disabled:y,children:["BOBINA_VUOTA"===g.id_bobina&&(0,i.jsx)(L.A,{className:"h-5 w-5"}),"BOBINA VUOTA"]})})]}),"BOBINA_VUOTA"===g.id_bobina&&(0,i.jsx)("div",{className:"mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:(0,i.jsxs)("div",{className:"flex items-start gap-2",children:[(0,i.jsx)(L.A,{className:"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0"}),(0,i.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,i.jsx)("p",{className:"font-medium",children:"Bobina Vuota Selezionata"}),(0,i.jsx)("p",{className:"mt-1",children:'Il cavo sar\xe0 posato senza assegnazione di bobina specifica. Potrai collegarlo a una bobina in seguito tramite la funzione "Modifica Bobina".'})]})]})})]}),S?(0,i.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,i.jsx)(ee.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,i.jsx)("span",{children:"Caricamento bobine..."})]}):(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("h4",{className:"font-medium text-green-700 mb-2 flex items-center gap-2",children:[(0,i.jsx)(L.A,{className:"h-4 w-4"}),"Bobine Compatibili (",B.length,")"]}),(0,i.jsx)("div",{className:"max-h-72 overflow-y-auto border rounded-lg",children:0===B.length?(0,i.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina compatibile trovata"}):(0,i.jsx)("div",{className:"divide-y",children:B.map(e=>(0,i.jsx)("div",{className:"p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ".concat(g.id_bobina===e.id_bobina?"bg-green-100 border-green-500 shadow-md":"border-gray-200 hover:bg-green-50 hover:border-green-300"),onClick:()=>M(e),children:(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[g.id_bobina===e.id_bobina&&(0,i.jsx)(L.A,{className:"h-5 w-5 text-green-600 flex-shrink-0"}),(0,i.jsx)("div",{className:"font-bold text-base min-w-fit",children:R(e.id_bobina)}),(0,i.jsxs)("div",{className:"text-sm text-gray-600 truncate",children:[e.tipologia," - ",e.sezione]})]}),(0,i.jsxs)(m.E,{variant:"outline",className:"bg-green-100 text-green-800 border-green-300 font-medium min-w-fit",children:[e.metri_residui,"m"]})]})},e.id_bobina))})})]}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("h4",{className:"font-medium text-amber-700 mb-2 flex items-center gap-2",children:[(0,i.jsx)(ea.A,{className:"h-4 w-4"}),"Bobine Incompatibili (",P.length,")"]}),(0,i.jsx)("div",{className:"max-h-72 overflow-y-auto border rounded-lg",children:0===P.length?(0,i.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina incompatibile trovata"}):(0,i.jsx)("div",{className:"divide-y",children:P.map(e=>(0,i.jsx)("div",{className:"p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ".concat(g.id_bobina===e.id_bobina?"bg-amber-100 border-amber-500 shadow-md":"border-gray-200 hover:bg-amber-50 hover:border-amber-300"),onClick:()=>M(e),children:(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[g.id_bobina===e.id_bobina&&(0,i.jsx)(L.A,{className:"h-5 w-5 text-amber-600 flex-shrink-0"}),(0,i.jsx)("div",{className:"font-bold text-base min-w-fit",children:R(e.id_bobina)}),(0,i.jsxs)("div",{className:"text-sm text-gray-600 truncate",children:[e.tipologia," - ",e.sezione]})]}),(0,i.jsxs)(m.E,{variant:"outline",className:"bg-amber-100 text-amber-800 border-amber-300 font-medium min-w-fit",children:[e.metri_residui,"m"]})]})},e.id_bobina))})})]})]}),0===A.length&&!S&&(0,i.jsxs)(o.Fc,{className:"border-amber-200 bg-amber-50",children:[(0,i.jsx)(ea.A,{className:"h-4 w-4 text-amber-600"}),(0,i.jsx)(o.TN,{className:"text-amber-800",children:"Non ci sono bobine disponibili. Puoi procedere con BOBINA VUOTA o aggiungere prima una nuova bobina."})]}),f.id_bobina&&(0,i.jsxs)(o.Fc,{variant:"destructive",children:[(0,i.jsx)(eS.A,{className:"h-4 w-4"}),(0,i.jsx)(o.TN,{children:f.id_bobina})]})]})]}),(0,i.jsxs)(Y.Es,{className:"flex-shrink-0 border-t pt-4 mt-4 flex justify-between items-center",children:[(0,i.jsx)("div",{children:"installato"===l.stato_installazione&&l.id_bobina&&(0,i.jsx)(n.$,{variant:"outline",onClick:()=>{D(!0)},disabled:y,className:"text-blue-600 border-blue-300 hover:bg-blue-50",children:"Modifica Bobina"})}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsx)(n.$,{variant:"outline",onClick:G,disabled:y,children:"Annulla"}),(0,i.jsxs)(n.$,{onClick:V,disabled:y||!g.metri_posati||0>parseFloat(g.metri_posati)||!g.id_bobina,className:"bg-mariner-600 hover:bg-mariner-700 text-white disabled:bg-gray-400 disabled:text-gray-200",children:[y&&(0,i.jsx)(ee.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salva"]})]})]})]})}),(0,i.jsx)(eE,{open:F,onClose:()=>D(!1),cavo:l,onSuccess:e=>{x(e),D(!1),t()},onError:u})]}):null}var ek=t(2564),eT=t(87481);function eO(e){let{open:a,onClose:t,onConfirm:l,title:r,description:c,isLoading:d,isDangerous:m=!1}=e,[x,u]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{a||u(!1)},[a]),(0,s.useEffect)(()=>{let e=e=>{"Escape"===e.key&&a&&!d&&t()};if(a)return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[a,t,d]),(0,i.jsx)(Y.lG,{open:a,onOpenChange:t,children:(0,i.jsx)(Y.Cf,{className:"sm:max-w-[400px]","aria-describedby":"confirm-disconnect-description",children:x?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(Y.c7,{children:(0,i.jsx)(Y.L3,{className:"text-center text-orange-600",children:"Conferma Finale"})}),(0,i.jsxs)("div",{className:"py-4 text-center",children:[(0,i.jsx)("div",{className:"mx-auto w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-4",children:(0,i.jsx)(ea.A,{className:"h-6 w-6 text-orange-600"})}),(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Sei veramente sicuro?"}),(0,i.jsxs)("p",{className:"text-sm text-gray-600 mb-4",children:["Questa azione scollegher\xe0 ",(0,i.jsx)("strong",{children:"entrambi i lati"})," del cavo."]}),(0,i.jsx)("div",{className:"bg-orange-50 border border-orange-200 rounded-md p-3",children:(0,i.jsx)("p",{className:"text-sm text-orange-800 font-medium",children:"⚠️ Operazione irreversibile"})})]}),(0,i.jsxs)(Y.Es,{className:"gap-2",children:[(0,i.jsx)(n.$,{variant:"outline",onClick:()=>u(!1),disabled:d,className:"flex-1",children:"No, Annulla"}),(0,i.jsx)(n.$,{variant:"outline",onClick:()=>{l()},disabled:d,className:"flex-1 bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100 hover:border-orange-300",children:d?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(ee.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Scollegando..."]}):"S\xec, Scollega"})]})]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(Y.c7,{children:[(0,i.jsxs)(Y.L3,{className:"flex items-center gap-2 text-orange-600",children:[(0,i.jsx)(ea.A,{className:"h-5 w-5"}),r]}),(0,i.jsx)(Y.rr,{id:"confirm-disconnect-description",children:c})]}),(0,i.jsx)("div",{className:"py-4",children:(0,i.jsxs)(o.Fc,{className:"border-orange-200 bg-orange-50",children:[(0,i.jsx)(ea.A,{className:"h-4 w-4 text-orange-600"}),(0,i.jsxs)(o.TN,{className:"text-orange-800",children:[(0,i.jsx)("strong",{children:"Attenzione:"})," Questa azione modificher\xe0 lo stato del collegamento del cavo."]})]})}),(0,i.jsxs)(Y.Es,{className:"gap-2",children:[(0,i.jsx)(n.$,{variant:"outline",onClick:t,disabled:d,className:"flex-1 hover:bg-gray-50",children:"Annulla"}),(0,i.jsxs)(n.$,{variant:"outline",onClick:()=>{m?u(!0):l()},disabled:d,className:"flex-1 bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100 hover:border-orange-300",children:[(0,i.jsx)(ea.A,{className:"mr-2 h-4 w-4"}),m?"Procedi":"Conferma"]})]})]})})})}let eF=function(e){let{open:a,onClose:t,cavo:l,onSuccess:r,onError:m}=e,{cantiere:x}=(0,c.A)(),{toast:u}=(0,eT.dj)(),[h,p]=(0,s.useState)("cantiere"),[b,g]=(0,s.useState)(!1),[v,f]=(0,s.useState)(""),[j,N]=(0,s.useState)({open:!1,type:null,title:"",description:""}),C=(0,s.useRef)(null),y=(0,s.useRef)(null),w=(0,s.useRef)(null),[A,z]=(0,s.useState)("");(0,s.useEffect)(()=>{a&&l&&(p("cantiere"),f(""))},[a,l]),(0,s.useEffect)(()=>{if(a&&C.current){let e=C.current.querySelectorAll('button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])');e.length>0&&e[0].focus()}},[a]),(0,s.useEffect)(()=>{let e=e=>{if(a&&!b&&!j.open)switch(e.key){case"Escape":t();break;case"Tab":var i;let s=null==(i=C.current)?void 0:i.querySelectorAll('button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])');if(s&&s.length>0){let a=s[0],t=s[s.length-1];e.shiftKey&&document.activeElement===a?(e.preventDefault(),t.focus()):e.shiftKey||document.activeElement!==t||(e.preventDefault(),a.focus())}break;case"1":case"2":case"3":if(!b){e.preventDefault();let a=parseInt(e.key);1===a?I():2===a?O():3===a&&D()}}};if(a)return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[a,b,j.open]);let S=e=>{z(e),setTimeout(()=>z(""),1e3)},E=()=>{if(!l)return{stato:"non_collegato",descrizione:"Non collegato"};switch(l.collegamento||l.collegamenti||0){case 1:return{stato:"partenza",descrizione:"\uD83D\uDFE2⚪ Collegato lato partenza"};case 2:return{stato:"arrivo",descrizione:"⚪\uD83D\uDFE2 Collegato lato arrivo"};case 3:return{stato:"completo",descrizione:"\uD83D\uDFE2\uD83D\uDFE2 Completamente collegato"};default:return{stato:"non_collegato",descrizione:"⚪⚪ Non collegato"}}},I=()=>{if(!l)return;let e=E();"partenza"===e.stato||"completo"===e.stato?N({open:!0,type:"partenza",title:"Scollega lato partenza",description:"Vuoi scollegare il lato partenza del cavo ".concat(l.id_cavo,"?")}):k()},k=async()=>{if(l&&x)try{g(!0),f(""),S("Collegamento in corso..."),await d.At.collegaCavo(x.id_cantiere,l.id_cavo,"partenza",h);let e="Collegamento lato partenza completato per il cavo ".concat(l.id_cavo);S(e),u({title:"Successo",description:e}),r&&r(),t()}catch(i){var e,a;let t=(null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante il collegamento";f(t),S("Errore: ".concat(t)),m&&m(t)}finally{g(!1)}},O=()=>{if(!l)return;let e=E();"arrivo"===e.stato||"completo"===e.stato?N({open:!0,type:"arrivo",title:"Scollega lato arrivo",description:"Vuoi scollegare il lato arrivo del cavo ".concat(l.id_cavo,"?")}):F()},F=async()=>{if(l&&x)try{g(!0),f(""),S("Collegamento in corso..."),await d.At.collegaCavo(x.id_cantiere,l.id_cavo,"arrivo",h);let e="Collegamento lato arrivo completato per il cavo ".concat(l.id_cavo);S(e),u({title:"Successo",description:e}),r&&r(),t()}catch(i){var e,a;let t=(null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante il collegamento";f(t),S("Errore: ".concat(t)),m&&m(t)}finally{g(!1)}},D=()=>{l&&("completo"===E().stato?N({open:!0,type:"entrambi",title:"Scollega entrambi i lati",description:"Vuoi scollegare completamente il cavo ".concat(l.id_cavo,"? Questa operazione rimuover\xe0 tutti i collegamenti.")}):R())},R=async()=>{if(l&&x)try{g(!0),f(""),S("Collegamento entrambi i lati in corso..."),await d.At.collegaCavo(x.id_cantiere,l.id_cavo,"entrambi",h);let e="Collegamento completo per il cavo ".concat(l.id_cavo);S(e),u({title:"Successo",description:e}),r&&r(),t()}catch(i){var e,a;let t=(null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante il collegamento";f(t),S("Errore: ".concat(t)),m&&m(t)}finally{g(!1)}},B=async()=>{if(l&&x&&j.type)try{g(!0),f(""),S("Scollegamento in corso..."),await d.At.scollegaCavo(x.id_cantiere,l.id_cavo,"entrambi"===j.type?void 0:j.type);let e="entrambi"===j.type?"":" lato ".concat(j.type),a="Scollegamento".concat(e," completato per il cavo ").concat(l.id_cavo);S(a),u({title:"Successo",description:a}),r&&r(),N({open:!1,type:null,title:"",description:""}),t()}catch(i){var e,a;let t=(null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante lo scollegamento";f(t),S("Errore: ".concat(t)),m&&m(t)}finally{g(!1)}};if(!l)return null;let P=E(),$=(l.metri_posati||l.metratura_reale||0)>0;return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(ek.bL,{children:(0,i.jsx)("div",{"aria-live":"polite","aria-atomic":"true",children:A})}),(0,i.jsx)(Y.lG,{open:a,onOpenChange:t,children:(0,i.jsxs)(Y.Cf,{className:"sm:max-w-[600px] max-h-[90vh] overflow-y-auto",ref:C,"aria-describedby":"collegamenti-description",children:[(0,i.jsxs)(Y.c7,{children:[(0,i.jsxs)(Y.L3,{className:"flex items-center gap-2 text-blue-600",children:[(0,i.jsx)(M.A,{className:"h-5 w-5"}),"Gestione Collegamenti - ",l.id_cavo]}),(0,i.jsxs)(Y.rr,{id:"collegamenti-description",children:["Gestisci i collegamenti del cavo ",l.id_cavo,". Usa i tasti 1, 2, 3 per azioni rapide."]})]}),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-200",children:(0,i.jsxs)("div",{className:"text-sm font-medium text-blue-800",children:["Informazioni Cavo / Tipologia: ",l.tipologia||"N/A"," / Da: ",l.ubicazione_partenza||"N/A"," / Formazione: ",l.sezione||"N/A"," / A: ",l.ubicazione_arrivo||"N/A"," / Metri Posati: ",l.metratura_reale||0," m"]})}),(0,i.jsx)("div",{className:"p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border",children:(0,i.jsx)("div",{className:"flex items-center justify-between",children:(0,i.jsxs)("div",{children:[(0,i.jsx)(T.J,{className:"text-sm font-medium text-gray-700",children:"Stato Collegamento"}),(0,i.jsxs)("div",{className:"mt-1 text-lg font-semibold flex items-center gap-2",children:["completo"===P.stato&&(0,i.jsx)(L.A,{className:"h-5 w-5 text-green-600"}),"non_collegato"===P.stato&&(0,i.jsx)(eS.A,{className:"h-5 w-5 text-gray-400"}),("partenza"===P.stato||"arrivo"===P.stato)&&(0,i.jsx)(ea.A,{className:"h-5 w-5 text-orange-500"}),P.descrizione]})]})})}),!$&&(0,i.jsxs)(o.Fc,{className:"border-orange-200 bg-orange-50",children:[(0,i.jsx)(eS.A,{className:"h-4 w-4 text-orange-600"}),(0,i.jsxs)(o.TN,{className:"text-orange-800",children:[(0,i.jsx)("strong",{children:"Attenzione:"})," Il cavo deve essere installato prima di poter essere collegato."]})]}),v&&(0,i.jsxs)(o.Fc,{variant:"destructive",children:[(0,i.jsx)(eS.A,{className:"h-4 w-4"}),(0,i.jsx)(o.TN,{children:v})]}),$&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)(T.J,{className:"text-sm font-medium",children:"Responsabile Collegamento"}),(0,i.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg border",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(L.A,{className:"h-4 w-4 text-green-600"}),(0,i.jsx)("span",{className:"font-medium",children:"Cantiere"})]}),(0,i.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Collegamento eseguito dal responsabile del cantiere"})]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)(T.J,{className:"text-sm font-medium",children:"Azioni Disponibili"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 gap-3",children:[(0,i.jsx)(n.$,{ref:y,onClick:I,disabled:b,className:"w-full h-12 text-left justify-start bg-green-50 text-green-700 border-green-200 hover:bg-green-100 hover:border-green-300",variant:"outline",children:(0,i.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,i.jsx)("span",{className:"text-sm font-bold text-green-700",children:"1"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium",children:"partenza"===P.stato||"completo"===P.stato?"Scollega Partenza":"Collega Partenza"}),(0,i.jsx)("div",{className:"text-xs text-green-600",children:"partenza"===P.stato||"completo"===P.stato?"Rimuovi collegamento lato partenza":"Connetti il lato partenza del cavo"})]})]}),b?(0,i.jsx)(ee.A,{className:"h-4 w-4 animate-spin"}):(0,i.jsx)(M.A,{className:"h-4 w-4"})]})}),(0,i.jsx)(n.$,{onClick:O,disabled:b,className:"w-full h-12 text-left justify-start bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 hover:border-blue-300",variant:"outline",children:(0,i.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,i.jsx)("span",{className:"text-sm font-bold text-blue-700",children:"2"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium",children:"arrivo"===P.stato||"completo"===P.stato?"Scollega Arrivo":"Collega Arrivo"}),(0,i.jsx)("div",{className:"text-xs text-blue-600",children:"arrivo"===P.stato||"completo"===P.stato?"Rimuovi collegamento lato arrivo":"Connetti il lato arrivo del cavo"})]})]}),b?(0,i.jsx)(ee.A,{className:"h-4 w-4 animate-spin"}):(0,i.jsx)(M.A,{className:"h-4 w-4"})]})}),(0,i.jsx)(n.$,{onClick:D,disabled:b,className:"w-full h-12 text-left justify-start bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100 hover:border-purple-300",variant:"outline",children:(0,i.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:(0,i.jsx)("span",{className:"text-sm font-bold text-purple-700",children:"3"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium",children:"completo"===P.stato?"Scollega Completamente":"Collega Entrambi"}),(0,i.jsx)("div",{className:"text-xs text-purple-600",children:"completo"===P.stato?"Rimuovi tutti i collegamenti":"Connetti entrambi i lati del cavo"})]})]}),b?(0,i.jsx)(ee.A,{className:"h-4 w-4 animate-spin"}):(0,i.jsx)(M.A,{className:"h-4 w-4"})]})})]})]})]})]}),(0,i.jsx)(Y.Es,{children:(0,i.jsxs)(n.$,{ref:w,variant:"outline",onClick:t,disabled:b,className:"hover:bg-gray-50",children:[(0,i.jsx)(_.A,{className:"mr-2 h-4 w-4"}),"Chiudi"]})})]})}),(0,i.jsx)(eO,{open:j.open,onClose:()=>N({open:!1,type:null,title:"",description:""}),onConfirm:B,title:j.title,description:j.description,isLoading:b,isDangerous:"entrambi"===j.type})]})};var eD=t(69037);function eR(e){let{open:a,onClose:t,cavo:l,onSuccess:r,onError:m}=e,{cantiere:x}=(0,c.A)(),{toast:u}=(0,eT.dj)(),[b,g]=(0,s.useState)({responsabile_certificazione:"",data_certificazione:new Date().toISOString().split("T")[0],esito_certificazione:"CONFORME",note_certificazione:""}),[v,f]=(0,s.useState)([]),[j,N]=(0,s.useState)(!1),[C,y]=(0,s.useState)(!1),[w,A]=(0,s.useState)(""),[z,S]=(0,s.useState)(""),E=e=>{S(e),setTimeout(()=>S(""),1e3)};(0,s.useEffect)(()=>{a&&l&&(g({responsabile_certificazione:"",data_certificazione:new Date().toISOString().split("T")[0],esito_certificazione:"CONFORME",note_certificazione:""}),A(""),I())},[a,l]);let I=async()=>{if(x)try{y(!0);let e=await d.AR.getResponsabili(x.id_cantiere);f(e.data)}catch(e){f([])}finally{y(!1)}},k=()=>!!l&&3===(l.collegamento||l.collegamenti||0),O=async()=>{if(!l||!x)return!1;try{return E("Collegamento automatico in corso..."),await d.At.collegaCavo(x.id_cantiere,l.id_cavo,"entrambi","cantiere"),E("Cavo collegato automaticamente"),!0}catch(e){return console.error("Errore nel collegamento automatico:",e),!1}},F=async()=>{if(l&&x){if(!b.responsabile_certificazione){A("Seleziona un responsabile per la certificazione"),E("Errore: Seleziona un responsabile per la certificazione");return}try{if(N(!0),A(""),E("Certificazione in corso..."),!k()&&(E("Collegamento automatico del cavo..."),!await O())){A("Impossibile collegare automaticamente il cavo. Collegalo manualmente prima di certificare."),E("Errore: Impossibile collegare automaticamente il cavo");return}let e={id_cavo:l.id_cavo,responsabile_certificazione:b.responsabile_certificazione,data_certificazione:b.data_certificazione,esito_certificazione:b.esito_certificazione,note_certificazione:b.note_certificazione||null};await d.km.createCertificazione(x.id_cantiere,e);let a="Certificazione completata per il cavo ".concat(l.id_cavo);E(a),u({title:"Successo",description:a}),r&&r(a),t()}catch(i){var e,a;let t=(null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante la certificazione";A(t),E("Errore: ".concat(t)),m&&m(t)}finally{N(!1)}}},D=async()=>{if(l&&x)try{N(!0),A(""),E("Generazione PDF in corso...");let e=await d.km.generatePDF(x.id_cantiere,l.id_cavo),a=window.URL.createObjectURL(new Blob([e.data])),t=document.createElement("a");t.href=a,t.setAttribute("download","certificato_".concat(l.id_cavo,".pdf")),document.body.appendChild(t),t.click(),t.remove(),window.URL.revokeObjectURL(a);let i="PDF certificato generato per il cavo ".concat(l.id_cavo);E(i),u({title:"Successo",description:i}),r&&r(i)}catch(i){var e,a;let t=(null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante la generazione del PDF";A(t),E("Errore: ".concat(t)),m&&m(t)}finally{N(!1)}};if(!l)return null;let R="Installato"===l.stato_installazione||"INSTALLATO"===l.stato_installazione||"POSATO"===l.stato_installazione||(l.metri_posati||l.metratura_reale||0)>0,B=k(),P=l.responsabile_partenza&&l.responsabile_arrivo,M=!!l&&(!0===l.certificato||"SI"===l.certificato||"CERTIFICATO"===l.certificato);return(0,i.jsxs)(Y.lG,{open:a,onOpenChange:t,children:[(0,i.jsx)("div",{"aria-live":"polite","aria-atomic":"true",className:"sr-only",children:z}),(0,i.jsxs)(Y.Cf,{className:"sm:max-w-[700px] max-h-[90vh] overflow-y-auto","aria-describedby":"certificazione-description",children:[(0,i.jsxs)(Y.c7,{children:[(0,i.jsxs)(Y.L3,{className:"flex items-center gap-2 text-blue-600",children:[(0,i.jsx)(eD.A,{className:"h-5 w-5"}),"Gestione Certificazione - ",l.id_cavo]}),(0,i.jsxs)(Y.rr,{id:"certificazione-description",children:["Certifica il cavo ",l.id_cavo," secondo normativa CEI 64-8 o genera il PDF del certificato"]})]}),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-200",children:(0,i.jsxs)("div",{className:"text-sm font-medium text-blue-800",children:["Informazioni Cavo / Tipologia: ",l.tipologia||"N/A"," / Da: ",l.ubicazione_partenza||"N/A"," / Formazione: ",l.sezione||"N/A"," / A: ",l.ubicazione_arrivo||"N/A"," / Metri Posati: ",l.metratura_reale||0," m"]})}),(0,i.jsxs)("div",{className:"p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border",children:[(0,i.jsx)(T.J,{className:"text-sm font-medium text-gray-700",children:"Stato Cavo"}),(0,i.jsxs)("div",{className:"mt-2 space-y-2",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[R?(0,i.jsx)(L.A,{className:"w-4 h-4 text-green-600"}):(0,i.jsx)(eS.A,{className:"w-4 h-4 text-red-500"}),(0,i.jsx)("span",{className:"text-sm font-medium",children:R?"Installato/Posato":"Non installato"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[B?(0,i.jsx)(L.A,{className:"w-4 h-4 text-green-600"}):(0,i.jsx)(eS.A,{className:"w-4 h-4 text-orange-500"}),(0,i.jsx)("span",{className:"text-sm font-medium",children:B?"Collegato":"Non collegato"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[M?(0,i.jsx)(L.A,{className:"w-4 h-4 text-green-600"}):(0,i.jsx)(eS.A,{className:"w-4 h-4 text-gray-400"}),(0,i.jsx)("span",{className:"text-sm font-medium",children:M?"Certificato":"Non certificato"})]})]})]}),!R&&(0,i.jsxs)(o.Fc,{className:"border-red-200 bg-red-50",children:[(0,i.jsx)(eS.A,{className:"h-4 w-4 text-red-600"}),(0,i.jsxs)(o.TN,{className:"text-red-800",children:[(0,i.jsx)("strong",{children:"ATTENZIONE:"})," Il cavo deve essere installato/posato prima di poter essere certificato secondo CEI 64-8."]})]}),R&&!(B&&P)&&(0,i.jsxs)(o.Fc,{className:"border-amber-200 bg-amber-50",children:[(0,i.jsx)(eS.A,{className:"h-4 w-4 text-amber-600"}),(0,i.jsxs)(o.TN,{className:"text-amber-800",children:[(0,i.jsx)("strong",{children:"ATTENZIONE:"}),' Il cavo non risulta completamente collegato. Durante la certificazione sar\xe0 possibile collegarlo automaticamente a "cantiere" su entrambi i lati.']})]}),w&&(0,i.jsxs)(o.Fc,{variant:"destructive",children:[(0,i.jsx)(eS.A,{className:"h-4 w-4"}),(0,i.jsx)(o.TN,{children:w})]}),M?(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)(o.Fc,{className:"border-green-200 bg-green-50",children:[(0,i.jsx)(eD.A,{className:"h-4 w-4 text-green-600"}),(0,i.jsxs)(o.TN,{className:"text-green-800",children:[(0,i.jsx)("strong",{children:"CERTIFICATO:"})," Questo cavo \xe8 gi\xe0 stato certificato. Puoi generare il PDF del certificato."]})]}),(0,i.jsxs)(n.$,{onClick:D,disabled:j,className:"w-full bg-green-600 hover:bg-green-700 text-white",size:"lg",children:[j?(0,i.jsx)(ee.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,i.jsx)(q.A,{className:"h-4 w-4 mr-2"}),"Genera PDF Certificato"]})]}):R&&(0,i.jsxs)("div",{className:"space-y-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border-2 border-blue-200",children:[(0,i.jsx)("div",{className:"text-sm font-medium text-blue-800 mb-4",children:"\uD83D\uDCCB Dati Certificazione CEI 64-8"}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(T.J,{htmlFor:"responsabile",className:"text-sm font-medium text-gray-700",children:"Responsabile Certificazione *"}),(0,i.jsxs)(p.l6,{value:b.responsabile_certificazione,onValueChange:e=>g(a=>({...a,responsabile_certificazione:e})),disabled:C,children:[(0,i.jsx)(p.bq,{className:"border-2 border-gray-300 focus:border-blue-500",children:(0,i.jsx)(p.yv,{placeholder:"Seleziona responsabile..."})}),(0,i.jsx)(p.gC,{children:v.map(e=>(0,i.jsxs)(p.eb,{value:e.nome_responsabile,children:[e.nome_responsabile,e.numero_telefono&&" - ".concat(e.numero_telefono)]},e.id))})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(T.J,{htmlFor:"data",className:"text-sm font-medium text-gray-700",children:"Data Certificazione"}),(0,i.jsx)(h.p,{id:"data",type:"date",value:b.data_certificazione,onChange:e=>g(a=>({...a,data_certificazione:e.target.value})),className:"border-2 border-gray-300 focus:border-blue-500"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(T.J,{htmlFor:"esito",className:"text-sm font-medium text-gray-700",children:"Esito Certificazione"}),(0,i.jsxs)(p.l6,{value:b.esito_certificazione,onValueChange:e=>g(a=>({...a,esito_certificazione:e})),children:[(0,i.jsx)(p.bq,{className:"border-2 border-gray-300 focus:border-blue-500",children:(0,i.jsx)(p.yv,{})}),(0,i.jsxs)(p.gC,{children:[(0,i.jsx)(p.eb,{value:"CONFORME",children:"✅ CONFORME"}),(0,i.jsx)(p.eb,{value:"NON_CONFORME",children:"❌ NON CONFORME"}),(0,i.jsx)(p.eb,{value:"CONFORME_CON_RISERVA",children:"⚠️ CONFORME CON RISERVA"})]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(T.J,{htmlFor:"note",className:"text-sm font-medium text-gray-700",children:"Note (opzionale)"}),(0,i.jsx)(Q.T,{id:"note",placeholder:"Inserisci eventuali note sulla certificazione...",value:b.note_certificazione,onChange:e=>g(a=>({...a,note_certificazione:e.target.value})),rows:3,className:"border-2 border-gray-300 focus:border-blue-500"})]}),(0,i.jsxs)(n.$,{onClick:F,disabled:j||!b.responsabile_certificazione,className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3",size:"lg",children:[j?(0,i.jsx)(ee.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,i.jsx)(eD.A,{className:"h-4 w-4 mr-2"}),"Certifica Cavo CEI 64-8"]})]})]}),(0,i.jsx)(Y.Es,{className:"border-t pt-4",children:(0,i.jsxs)(n.$,{variant:"outline",onClick:t,disabled:j,className:"flex items-center gap-2",children:[(0,i.jsx)(_.A,{className:"h-4 w-4"}),"Chiudi"]})})]})]})}var eB=t(25273);function eP(e){let{open:a,onClose:t,caviSelezionati:l,tipoComanda:r,onSuccess:m,onError:x}=e,{cantiere:u}=(0,c.A)(),[h,b]=(0,s.useState)({tipo_comanda:r||"POSA",responsabile:"",note:""}),[g,v]=(0,s.useState)([]),[f,j]=(0,s.useState)(!1),[N,C]=(0,s.useState)(!1),[y,_]=(0,s.useState)("");(0,s.useEffect)(()=>{a&&(b({tipo_comanda:r||"POSA",responsabile:"",note:""}),_(""),w())},[a,r]);let w=async()=>{if(u)try{C(!0);let e=await d.AR.getResponsabili(u.id_cantiere);v(e.data)}catch(e){v([])}finally{C(!1)}},A=async()=>{if(u){if(!h.responsabile)return void _("Seleziona un responsabile per la comanda");if(0===l.length)return void _("Seleziona almeno un cavo per la comanda");try{j(!0),_("");let e={tipo_comanda:h.tipo_comanda,responsabile:h.responsabile,note:h.note||null},a=await d.CV.createComandaWithCavi(u.id_cantiere,e,l);m("Comanda ".concat(a.data.codice_comanda," creata con successo per ").concat(l.length," cavi")),t()}catch(t){var e,a;x((null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||t.message||"Errore durante la creazione della comanda")}finally{j(!1)}}};return(0,i.jsx)(Y.lG,{open:a,onOpenChange:t,children:(0,i.jsxs)(Y.Cf,{className:"sm:max-w-[600px]",children:[(0,i.jsxs)(Y.c7,{children:[(0,i.jsxs)(Y.L3,{className:"flex items-center gap-2",children:[(0,i.jsx)(eB.A,{className:"h-5 w-5"}),"Crea Nuova Comanda"]}),(0,i.jsxs)(Y.rr,{children:["Crea una nuova comanda per ",l.length," cavi selezionati"]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,i.jsxs)(T.J,{className:"text-sm font-medium",children:["Cavi Selezionati (",l.length,")"]}),(0,i.jsx)("div",{className:"mt-2 max-h-32 overflow-y-auto",children:(0,i.jsxs)("div",{className:"flex flex-wrap gap-1",children:[l.slice(0,10).map(e=>(0,i.jsx)("span",{className:"inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded",children:e},e)),l.length>10&&(0,i.jsxs)("span",{className:"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded",children:["+",l.length-10," altri..."]})]})})]}),y&&(0,i.jsxs)(o.Fc,{variant:"destructive",children:[(0,i.jsx)(eS.A,{className:"h-4 w-4"}),(0,i.jsx)(o.TN,{children:y})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(T.J,{htmlFor:"tipo",children:"Tipo Comanda *"}),(0,i.jsxs)(p.l6,{value:h.tipo_comanda,onValueChange:e=>b(a=>({...a,tipo_comanda:e})),children:[(0,i.jsx)(p.bq,{children:(0,i.jsx)(p.yv,{})}),(0,i.jsxs)(p.gC,{children:[(0,i.jsx)(p.eb,{value:"POSA",children:"\uD83D\uDD27 Posa Cavi"}),(0,i.jsx)(p.eb,{value:"COLLEGAMENTO_PARTENZA",children:"\uD83D\uDD0C Collegamento Partenza"}),(0,i.jsx)(p.eb,{value:"COLLEGAMENTO_ARRIVO",children:"⚡ Collegamento Arrivo"}),(0,i.jsx)(p.eb,{value:"CERTIFICAZIONE",children:"\uD83D\uDCCB Certificazione"})]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(T.J,{htmlFor:"responsabile",children:"Responsabile *"}),(0,i.jsxs)(p.l6,{value:h.responsabile,onValueChange:e=>b(a=>({...a,responsabile:e})),disabled:N,children:[(0,i.jsx)(p.bq,{children:(0,i.jsx)(p.yv,{placeholder:"Seleziona responsabile..."})}),(0,i.jsx)(p.gC,{children:g.map(e=>(0,i.jsx)(p.eb,{value:e.nome_responsabile,children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(H.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:e.nome_responsabile}),e.numero_telefono&&(0,i.jsxs)("span",{className:"text-xs text-gray-500",children:["- ",e.numero_telefono]})]})},e.id))})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(T.J,{htmlFor:"note",children:"Note (opzionale)"}),(0,i.jsx)(Q.T,{id:"note",placeholder:"Inserisci eventuali note per la comanda...",value:h.note,onChange:e=>b(a=>({...a,note:e.target.value})),rows:3})]}),(0,i.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,i.jsx)(T.J,{className:"text-sm font-medium",children:"Riepilogo Comanda"}),(0,i.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Tipo:"})," ",(e=>{switch(e){case"POSA":return"Posa Cavi";case"COLLEGAMENTO_PARTENZA":return"Collegamento Partenza";case"COLLEGAMENTO_ARRIVO":return"Collegamento Arrivo";case"CERTIFICAZIONE":return"Certificazione";default:return e}})(h.tipo_comanda)]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Responsabile:"})," ",h.responsabile||"Non selezionato"]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Cavi:"})," ",l.length," selezionati"]}),h.note&&(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Note:"})," ",h.note]})]})]})]}),(0,i.jsxs)(Y.Es,{children:[(0,i.jsx)(n.$,{variant:"outline",onClick:t,disabled:f,children:"Annulla"}),(0,i.jsxs)(n.$,{onClick:A,disabled:f||!h.responsabile||0===l.length,children:[f?(0,i.jsx)(ee.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,i.jsx)(eB.A,{className:"h-4 w-4 mr-2"}),"Crea Comanda"]})]})]})})}var eL=t(29869),eM=t(64261);function e$(e){let{open:a,onClose:t,tipo:l,onSuccess:r,onError:m}=e,{cantiere:x}=(0,c.A)(),[u,p]=(0,s.useState)(null),[b,g]=(0,s.useState)(""),[v,f]=(0,s.useState)(!1),[j,N]=(0,s.useState)(""),[C,y]=(0,s.useState)(0),_=(0,s.useRef)(null),w=async()=>{if(u&&x){if("cavi"===l&&!b.trim())return void N("Inserisci il codice revisione per l'importazione cavi");try{let e;if(f(!0),N(""),y(0),e="cavi"===l?await d.mg.importCavi(x.id_cantiere,u,b.trim()):await d.mg.importBobine(x.id_cantiere,u),y(100),e.data.success){let a=e.data.details,i=e.data.message;"cavi"===l&&(null==a?void 0:a.cavi_importati)?i+=" (".concat(a.cavi_importati," cavi importati)"):"bobine"===l&&(null==a?void 0:a.bobine_importate)&&(i+=" (".concat(a.bobine_importate," bobine importate)")),r(i),t()}else m(e.data.message||"Errore durante l'importazione")}catch(t){var e,a;m((null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||t.message||"Errore durante l'importazione del file")}finally{f(!1),y(0)}}},A=()=>{v||(p(null),g(""),N(""),y(0),_.current&&(_.current.value=""),t())},z=()=>"cavi"===l?"Cavi":"Bobine";return(0,i.jsx)(Y.lG,{open:a,onOpenChange:A,children:(0,i.jsxs)(Y.Cf,{className:"sm:max-w-[600px]",children:[(0,i.jsxs)(Y.c7,{children:[(0,i.jsxs)(Y.L3,{className:"flex items-center gap-2",children:[(0,i.jsx)(eL.A,{className:"h-5 w-5"}),"Importa ",z()," da Excel"]}),(0,i.jsxs)(Y.rr,{children:["Carica un file Excel per importare ",z().toLowerCase()," nel cantiere"]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,i.jsx)(T.J,{className:"text-sm font-medium",children:"Requisiti File"}),(0,i.jsx)("ul",{className:"mt-2 space-y-1 text-sm text-gray-600",children:("cavi"===l?["File Excel (.xlsx o .xls)","Colonne richieste: ID_CAVO, SISTEMA, UTILITY, TIPOLOGIA, ecc.","Prima riga deve contenere le intestazioni","Codice revisione obbligatorio per tracciabilit\xe0"]:["File Excel (.xlsx o .xls)","Colonne richieste: NUMERO_BOBINA, UTILITY, TIPOLOGIA, METRI_TOTALI, ecc.","Prima riga deve contenere le intestazioni","I metri residui saranno impostati uguali ai metri totali"]).map((e,a)=>(0,i.jsxs)("li",{className:"flex items-start gap-2",children:[(0,i.jsx)("span",{className:"text-blue-500 mt-0.5",children:"•"}),(0,i.jsx)("span",{children:e})]},a))})]}),j&&(0,i.jsxs)(o.Fc,{variant:"destructive",children:[(0,i.jsx)(eS.A,{className:"h-4 w-4"}),(0,i.jsx)(o.TN,{children:j})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(T.J,{htmlFor:"file",children:"File Excel *"}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(h.p,{ref:_,id:"file",type:"file",accept:".xlsx,.xls",onChange:e=>{var a;let t=null==(a=e.target.files)?void 0:a[0];if(t){if(!["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel"].includes(t.type)&&!t.name.toLowerCase().endsWith(".xlsx")&&!t.name.toLowerCase().endsWith(".xls"))return void N("Seleziona un file Excel valido (.xlsx o .xls)");p(t),N("")}},disabled:v,className:"flex-1"}),u&&(0,i.jsxs)("div",{className:"flex items-center gap-1 text-green-600",children:[(0,i.jsx)(L.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{className:"text-sm",children:"File selezionato"})]})]}),u&&(0,i.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,i.jsx)(eM.A,{className:"h-4 w-4 inline mr-1"}),u.name," (",(u.size/1024/1024).toFixed(2)," MB)"]})]}),"cavi"===l&&(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(T.J,{htmlFor:"revisione",children:"Codice Revisione *"}),(0,i.jsx)(h.p,{id:"revisione",value:b,onChange:e=>g(e.target.value),placeholder:"es. REV001, V1.0, 2024-01",disabled:v}),(0,i.jsx)("p",{className:"text-sm text-gray-500",children:"Codice identificativo della revisione per tracciabilit\xe0 delle modifiche"})]}),v&&(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(ee.A,{className:"h-4 w-4 animate-spin"}),(0,i.jsx)("span",{className:"text-sm",children:"Caricamento in corso..."})]}),(0,i.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,i.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(C,"%")}})})]}),u&&(0,i.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,i.jsx)(T.J,{className:"text-sm font-medium",children:"Riepilogo Importazione"}),(0,i.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Tipo:"})," ",z()]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"File:"})," ",u.name]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Dimensione:"})," ",(u.size/1024/1024).toFixed(2)," MB"]}),"cavi"===l&&b&&(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Revisione:"})," ",b]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Cantiere:"})," ",null==x?void 0:x.nome_cantiere]})]})]})]}),(0,i.jsxs)(Y.Es,{children:[(0,i.jsx)(n.$,{variant:"outline",onClick:A,disabled:v,children:"Annulla"}),(0,i.jsxs)(n.$,{onClick:w,disabled:v||!u||"cavi"===l&&!b.trim(),children:[v?(0,i.jsx)(ee.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,i.jsx)(eL.A,{className:"h-4 w-4 mr-2"}),"Importa ",z()]})]})]})})}var eU=t(54213);function eV(e){let{open:a,onClose:t,onSuccess:l,onError:r}=e,{cantiere:m}=(0,c.A)(),[u,h]=(0,s.useState)({cavi:!0,bobine:!0,comande:!1,certificazioni:!1,responsabili:!1}),[p,b]=(0,s.useState)(!1),[g,v]=(0,s.useState)(""),f=(e,a)=>{h(t=>({...t,[e]:a}))},j=async()=>{if(m)try{b(!0);let e=await d.mg.exportCavi(m.id_cantiere),a=window.URL.createObjectURL(new Blob([e.data])),t=document.createElement("a");t.href=a,t.setAttribute("download","cavi_".concat(m.nome_cantiere,"_").concat(new Date().toISOString().split("T")[0],".xlsx")),document.body.appendChild(t),t.click(),t.remove(),window.URL.revokeObjectURL(a),l("Export cavi completato con successo")}catch(t){var e,a;r((null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||t.message||"Errore durante l'export dei cavi")}finally{b(!1)}},N=async()=>{if(m)try{b(!0);let e=await d.mg.exportBobine(m.id_cantiere),a=window.URL.createObjectURL(new Blob([e.data])),t=document.createElement("a");t.href=a,t.setAttribute("download","bobine_".concat(m.nome_cantiere,"_").concat(new Date().toISOString().split("T")[0],".xlsx")),document.body.appendChild(t),t.click(),t.remove(),window.URL.revokeObjectURL(a),l("Export bobine completato con successo")}catch(t){var e,a;r((null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||t.message||"Errore durante l'export delle bobine")}finally{b(!1)}},C=async()=>{if(m)try{b(!0),v("");let e=[];u.cavi&&e.push(j()),u.bobine&&e.push(N()),u.comande,u.certificazioni,u.responsabili,await Promise.all(e);let a=Object.values(u).filter(Boolean).length;l("Export completato: ".concat(a," file scaricati")),t()}catch(t){var e,a;r((null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||t.message||"Errore durante l'export dei dati")}finally{b(!1)}},y=[{key:"cavi",label:"Cavi",description:"Esporta tutti i cavi del cantiere con stato, collegamenti e certificazioni",icon:(0,i.jsx)(eU.A,{className:"h-4 w-4"}),available:!0},{key:"bobine",label:"Bobine",description:"Esporta tutte le bobine del parco cavi con metri residui e assegnazioni",icon:(0,i.jsx)(eM.A,{className:"h-4 w-4"}),available:!0},{key:"comande",label:"Comande",description:"Esporta tutte le comande con cavi assegnati e responsabili",icon:(0,i.jsx)(eM.A,{className:"h-4 w-4"}),available:!1},{key:"certificazioni",label:"Certificazioni",description:"Esporta tutte le certificazioni con esiti e responsabili",icon:(0,i.jsx)(eM.A,{className:"h-4 w-4"}),available:!1},{key:"responsabili",label:"Responsabili",description:"Esporta tutti i responsabili con contatti e ruoli",icon:(0,i.jsx)(eM.A,{className:"h-4 w-4"}),available:!1}];return(0,i.jsx)(Y.lG,{open:a,onOpenChange:t,children:(0,i.jsxs)(Y.Cf,{className:"sm:max-w-[600px]",children:[(0,i.jsxs)(Y.c7,{children:[(0,i.jsxs)(Y.L3,{className:"flex items-center gap-2",children:[(0,i.jsx)(q.A,{className:"h-5 w-5"}),"Esporta Dati Cantiere"]}),(0,i.jsxs)(Y.rr,{children:["Seleziona i dati da esportare dal cantiere ",null==m?void 0:m.nome_cantiere]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[g&&(0,i.jsxs)(o.Fc,{variant:"destructive",children:[(0,i.jsx)(eS.A,{className:"h-4 w-4"}),(0,i.jsx)(o.TN,{children:g})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)(T.J,{className:"text-sm font-medium",children:"Seleziona Dati da Esportare"}),y.map(e=>(0,i.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg border ".concat(e.available?"bg-white":"bg-gray-50"),children:[(0,i.jsx)(x.S,{id:e.key,checked:u[e.key],onCheckedChange:a=>f(e.key,a),disabled:!e.available||p}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,(0,i.jsxs)(T.J,{htmlFor:e.key,className:"font-medium ".concat(e.available?"":"text-gray-500"),children:[e.label,!e.available&&(0,i.jsx)("span",{className:"ml-2 text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded",children:"In sviluppo"})]})]}),(0,i.jsx)("p",{className:"text-sm mt-1 ".concat(e.available?"text-gray-600":"text-gray-400"),children:e.description})]})]},e.key))]}),(0,i.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,i.jsx)(T.J,{className:"text-sm font-medium",children:"Informazioni Export"}),(0,i.jsxs)("ul",{className:"mt-2 space-y-1 text-sm text-gray-600",children:[(0,i.jsx)("li",{children:"• I file saranno scaricati in formato Excel (.xlsx)"}),(0,i.jsx)("li",{children:"• I nomi file includeranno data e nome cantiere"}),(0,i.jsx)("li",{children:"• I dati esportati riflettono lo stato attuale del database"}),(0,i.jsx)("li",{children:"• L'export non modifica i dati originali"})]})]}),Object.values(u).filter(Boolean).length>0&&(0,i.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,i.jsx)(T.J,{className:"text-sm font-medium",children:"Riepilogo Export"}),(0,i.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Cantiere:"})," ",null==m?void 0:m.nome_cantiere]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"File da scaricare:"})," ",Object.values(u).filter(Boolean).length]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Data export:"})," ",new Date().toLocaleDateString("it-IT")]})]})]})]}),(0,i.jsxs)(Y.Es,{children:[(0,i.jsx)(n.$,{variant:"outline",onClick:t,disabled:p,children:"Annulla"}),(0,i.jsxs)(n.$,{onClick:C,disabled:p||0===Object.values(u).filter(Boolean).length,children:[p?(0,i.jsx)(ee.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,i.jsx)(q.A,{className:"h-4 w-4 mr-2"}),"Esporta ",Object.values(u).filter(Boolean).length>0?"(".concat(Object.values(u).filter(Boolean).length,")"):""]})]})]})})}function eG(){let{user:e,cantiere:a,isAuthenticated:t,isLoading:m}=(0,c.A)(),x=(0,l.useRouter)(),u=(0,ej.E)();console.log({cantiereFromAuth:a,user:e,userRole:null==e?void 0:e.ruolo});let[h,p]=(0,s.useState)([]),[b,g]=(0,s.useState)([]),[v,f]=(0,s.useState)(!0),[j,N]=(0,s.useState)(""),[C,y]=(0,s.useState)([]),[_,w]=(0,s.useState)(!0),[A,z]=(0,s.useState)("all"),[S,E]=(0,s.useState)([]),[I,k]=(0,s.useState)("");(0,s.useEffect)(()=>{let e=h;if(a&&(e=e.filter(e=>e.cantiere===a.nome)),"all"!==A)switch(A){case"installati":e=e.filter(e=>"Installato"===e.stato_installazione||e.metri_posati&&e.metri_posati>0||e.metratura_reale&&e.metratura_reale>0);break;case"in_corso":e=e.filter(e=>"In corso"===e.stato_installazione);break;case"da_installare":e=e.filter(e=>"Installato"!==e.stato_installazione&&"In corso"!==e.stato_installazione&&!(e.metri_posati&&e.metri_posati>0)&&!(e.metratura_reale&&e.metratura_reale>0));break;case"collegati":e=e.filter(e=>3===(e.collegamento||e.collegamenti||0));break;case"certificati":e=e.filter(e=>!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato)}E(e)},[h,a,A]);let[T,O]=(0,s.useState)({open:!1,cavo:null}),[F,D]=(0,s.useState)({open:!1,cavo:null}),[R,B]=(0,s.useState)({open:!1,cavo:null}),[P,L]=(0,s.useState)({open:!1,cavo:null}),[M,U]=(0,s.useState)({open:!1}),[V,G]=(0,s.useState)({open:!1}),[Z,J]=(0,s.useState)(!1),[q,H]=(0,s.useState)({totali:0,installati:0,collegati:0,certificati:0,percentualeInstallazione:0,percentualeCollegamento:0,percentualeCertificazione:0,metriTotali:0,metriInstallati:0,metriCollegati:0,metriCertificati:0}),[W,Y]=(0,s.useState)(0);(0,s.useEffect)(()=>{m||t||x.push("/login")},[t,m,x]),(0,s.useEffect)(()=>{{let e=(null==a?void 0:a.id_cantiere)||parseInt(localStorage.getItem("selectedCantiereId")||"0");Y(e),console.log({cantiereFromAuth:null==a?void 0:a.id_cantiere,storedId:e,localStorage:localStorage.getItem("selectedCantiereId")})}},[a]);let Q=a||(W>0?{id_cantiere:W,commessa:"Cantiere ".concat(W)}:null);(0,s.useEffect)(()=>{W&&W>0&&(X(),K())},[W]);let K=async()=>{try{let e=await fetch("http://localhost:8001/api/cavi/".concat(W,"/revisione-corrente"),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}});if(e.ok){let a=await e.json();k(a.revisione_corrente||"00")}else k("00")}catch(e){k("00")}},X=async()=>{try{f(!0),N("");try{let e=await d.At.getCavi(W),a=e.filter(e=>!e.spare),t=e.filter(e=>e.spare);p(a),g(t),ea(a)}catch(e){try{let e=await fetch("http://localhost:8001/api/cavi/debug/".concat(W)),a=await e.json();if(a.cavi&&Array.isArray(a.cavi)){let e=a.cavi.filter(e=>!e.spare),t=a.cavi.filter(e=>e.spare);p(e),g(t),ea(e),N("⚠️ Dati caricati tramite endpoint debug (problema autenticazione)")}else throw Error("Formato dati debug non valido")}catch(a){throw e}}}catch(t){var e,a;N("Errore nel caricamento dei cavi: ".concat((null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||t.message))}finally{f(!1)}},ea=e=>{let a=e.length,t=e.filter(e=>(e.metri_posati||e.metratura_reale||0)>0).length,i=e.filter(e=>3===(e.collegamento||e.collegamenti)).length,s=e.filter(e=>e.certificato).length,l=e.reduce((e,a)=>e+(a.metri_teorici||0),0),r=e.reduce((e,a)=>e+(a.metri_posati||0),0);H({totali:a,installati:t,collegati:i,certificati:s,percentualeInstallazione:a>0?Math.round(t/a*100):0,percentualeCollegamento:a>0?Math.round(i/a*100):0,percentualeCertificazione:a>0?Math.round(s/a*100):0,metriTotali:l,metriInstallati:r,metriCollegati:e.filter(e=>3===e.collegamento).reduce((e,a)=>e+(a.metri_posati||0),0),metriCertificati:e.filter(e=>e.certificato).reduce((e,a)=>e+(a.metri_posati||0),0)})},et=async e=>{try{await new Promise(e=>setTimeout(e,1e3)),p(a=>a.map(a=>a.id_cavo===e?{...a,collegamento:0,collegamenti:0}:a)),await X(),u.success("Cavo Scollegato","Il cavo ".concat(e," \xe8 stato scollegato con successo."))}catch(e){throw u.error("Errore Scollegamento","Impossibile scollegare il cavo. Riprova."),e}},ei=async e=>{try{await new Promise(e=>setTimeout(e,2e3));let a=new Blob(["PDF Content"],{type:"application/pdf"}),t=window.URL.createObjectURL(a),i=document.createElement("a");i.href=t,i.download=e.fileName,document.body.appendChild(i),i.click(),window.URL.revokeObjectURL(t),document.body.removeChild(i),u.success("PDF Generato","Certificato per il cavo ".concat(e.cavoId," generato con successo."))}catch(e){throw u.error("Errore Generazione PDF","Impossibile generare il certificato. Riprova."),e}},es=async e=>{try{await new Promise(e=>setTimeout(e,1500)),p(a=>a.map(a=>a.id_cavo===e?{...a,certificato:!0,data_certificazione:new Date().toISOString()}:a)),await X(),u.success("Cavo Certificato","Il cavo ".concat(e," \xe8 stato certificato con successo."))}catch(e){throw u.error("Errore Certificazione","Impossibile certificare il cavo. Riprova."),e}},el=(e,a,t)=>{switch(a){case"insert_meters":O({open:!0,cavo:e});break;case"modify_reel":D({open:!0,cavo:e});break;case"view_command":u({title:"Visualizza Comanda",description:"Apertura comanda ".concat(t," per cavo ").concat(e.id_cavo)});break;case"connect_cable":case"connect_arrival":case"connect_departure":case"disconnect_cable":case"manage_connections":B({open:!0,cavo:e});break;case"create_certificate":case"generate_pdf":L({open:!0,cavo:e})}},er=(e,a)=>{switch(a){case"view_details":u({title:"Visualizza Dettagli",description:"Apertura dettagli per cavo ".concat(e.id_cavo)});break;case"edit":u({title:"Modifica Cavo",description:"Funzione modifica cavo in sviluppo"});break;case"delete":u({title:"Elimina Cavo",description:"Funzione eliminazione cavo in sviluppo",variant:"destructive"});break;case"add_new":u({title:"Aggiungi Nuovo Cavo",description:"Funzione aggiunta nuovo cavo in sviluppo"});break;case"select":C.includes(e.id_cavo)?(y(C.filter(a=>a!==e.id_cavo)),u({title:"Cavo Deselezionato",description:"Cavo ".concat(e.id_cavo," deselezionato")})):(y([...C,e.id_cavo]),u({title:"Cavo Selezionato",description:"Cavo ".concat(e.id_cavo," selezionato")}));break;case"copy_id":navigator.clipboard.writeText(e.id_cavo),u({title:"ID Copiato",description:"ID cavo ".concat(e.id_cavo," copiato negli appunti")});break;case"copy_details":let t="ID: ".concat(e.id_cavo,", Tipologia: ").concat(e.tipologia,", Formazione: ").concat(e.formazione||e.sezione,", Metri: ").concat(e.metri_teorici);navigator.clipboard.writeText(t),u({title:"Dettagli Copiati",description:"Dettagli cavo copiati negli appunti"});break;case"add_to_command":u({title:"Aggiungi a Comanda",description:"Funzione aggiunta a comanda in sviluppo"});break;case"remove_from_command":u({title:"Rimuovi da Comanda",description:"Funzione rimozione da comanda in sviluppo"});break;case"create_command_posa":U({open:!0,tipoComanda:"POSA"});break;case"create_command_collegamento_partenza":U({open:!0,tipoComanda:"COLLEGAMENTO_PARTENZA"});break;case"create_command_collegamento_arrivo":U({open:!0,tipoComanda:"COLLEGAMENTO_ARRIVO"});break;case"create_command_certificazione":U({open:!0,tipoComanda:"CERTIFICAZIONE"});break;case"add_multiple_to_command":u({title:"Aggiungi Tutti a Comanda",description:"Funzione aggiunta multipla a comanda in sviluppo"});break;case"remove_multiple_from_commands":u({title:"Rimuovi Tutti dalle Comande",description:"Funzione rimozione multipla dalle comande in sviluppo"});break;default:u({title:"Azione non implementata",description:"Azione ".concat(a," non ancora implementata")})}},en=e=>{u({title:"Operazione completata",description:e}),X()},eo=e=>{u({title:"Errore",description:e,variant:"destructive"})};return m||v?(0,i.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,i.jsx)(ee.A,{className:"h-8 w-8 animate-spin"})}):W?j?(0,i.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,i.jsxs)(o.Fc,{variant:"destructive",children:[(0,i.jsx)(eS.A,{className:"h-4 w-4"}),(0,i.jsx)(o.TN,{children:j})]}),(0,i.jsx)(n.$,{onClick:X,className:"mt-4",children:"Riprova"})]}):(0,i.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,i.jsx)(eA,{cavi:h,filteredCavi:S,revisioneCorrente:I,activeFilter:A,onFilterChange:e=>{z(e)},className:"mb-2"}),(0,i.jsx)("div",{className:"mb-8",children:(0,i.jsx)(eN,{cavi:h,loading:v,selectionEnabled:_,selectedCavi:C,onSelectionChange:y,onStatusAction:el,onContextMenuAction:er,onDisconnectCable:et,onGeneratePDF:ei,onCertifyCable:es})}),b.length>0&&(0,i.jsx)("div",{className:"mb-8",children:(0,i.jsxs)(r.Zp,{children:[(0,i.jsx)(r.aR,{children:(0,i.jsxs)(r.ZB,{className:"flex items-center space-x-2",children:[(0,i.jsx)($.A,{className:"h-5 w-5"}),(0,i.jsxs)("span",{children:["Cavi Spare (",b.length,")"]})]})}),(0,i.jsx)(r.Wu,{children:(0,i.jsx)(eN,{cavi:b,loading:v,selectionEnabled:!1,onStatusAction:el,onContextMenuAction:er,onDisconnectCable:et,onGeneratePDF:ei,onCertifyCable:es})})]})}),!1,(0,i.jsx)(eI,{open:T.open,onClose:()=>O({open:!1,cavo:null}),cavo:T.cavo,cantiere:Q,onSuccess:en,onError:eo}),(0,i.jsx)(eE,{open:F.open,onClose:()=>D({open:!1,cavo:null}),cavo:F.cavo,cantiere:Q,onSuccess:en,onError:eo}),(0,i.jsx)(eF,{open:R.open,onClose:()=>B({open:!1,cavo:null}),cavo:R.cavo,onSuccess:en,onError:eo}),(0,i.jsx)(eR,{open:P.open,onClose:()=>L({open:!1,cavo:null}),cavo:P.cavo,onSuccess:en,onError:eo}),(0,i.jsx)(eP,{open:M.open,onClose:()=>U({open:!1}),caviSelezionati:C,tipoComanda:M.tipoComanda,onSuccess:en,onError:eo}),(0,i.jsx)(e$,{open:V.open,onClose:()=>G({open:!1}),tipo:V.tipo||"cavi",onSuccess:en,onError:eo}),(0,i.jsx)(eV,{open:Z,onClose:()=>J(!1),onSuccess:en,onError:eo})]}):(0,i.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,i.jsxs)(o.Fc,{children:[(0,i.jsx)(eS.A,{className:"h-4 w-4"}),(0,i.jsx)(o.TN,{children:"Nessun cantiere selezionato. Seleziona un cantiere per visualizzare i cavi."})]}),(0,i.jsxs)("div",{className:"mt-4 p-4 bg-gray-100 rounded",children:[(0,i.jsx)("h3",{className:"font-bold",children:"Debug Info:"}),(0,i.jsxs)("p",{children:["User: ",e?e.username:"Non autenticato"]}),(0,i.jsxs)("p",{children:["Cantiere context: ",a?a.commessa:"Nessuno"]}),(0,i.jsxs)("p",{children:["Token presente: ",localStorage.getItem("token")?"S\xec":"No"]})]})]})}},87481:(e,a,t)=>{"use strict";t.d(a,{dj:()=>x});var i=t(12115);let s=0,l=new Map,r=e=>{if(l.has(e))return;let a=setTimeout(()=>{l.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);l.set(e,a)},n=(e,a)=>{switch(a.type){case"ADD_TOAST":return{...e,toasts:[a.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===a.toast.id?{...e,...a.toast}:e)};case"DISMISS_TOAST":{let{toastId:t}=a;return t?r(t):e.toasts.forEach(e=>{r(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===t||void 0===t?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===a.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==a.toastId)}}},o=[],c={toasts:[]};function d(e){c=n(c,e),o.forEach(e=>{e(c)})}function m(e){let{...a}=e,t=(s=(s+1)%Number.MAX_VALUE).toString(),i=()=>d({type:"DISMISS_TOAST",toastId:t});return d({type:"ADD_TOAST",toast:{...a,id:t,open:!0,onOpenChange:e=>{e||i()}}}),{id:t,dismiss:i,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function x(){let[e,a]=(0,i.useState)(c);return(0,i.useEffect)(()=>(o.push(a),()=>{let e=o.indexOf(a);e>-1&&o.splice(e,1)}),[]),{...e,toast:m,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},88539:(e,a,t)=>{"use strict";t.d(a,{T:()=>r});var i=t(95155),s=t(12115),l=t(59434);let r=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,i.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:a,...s})});r.displayName="Textarea"}},e=>{var a=a=>e(e.s=a);e.O(0,[3464,3455,9121,5148,4283,8034,7131,5006,2199,9977,283,1642,8441,1684,7358],()=>a(34428)),_N_E=e.O()}]);