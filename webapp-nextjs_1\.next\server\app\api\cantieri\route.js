(()=>{var e={};e.id=1973,e.ids=[1973],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7648:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{GET:()=>c,POST:()=>u});var a=r(96559),n=r(48088),o=r(37719),i=r(32190);async function c(e){try{let t=e.headers.get("authorization");if(!t||!t.startsWith("Bearer "))return i.NextResponse.json({detail:"Token di autorizzazione mancante"},{status:401});let r="http://localhost:8001";console.log("\uD83D\uDD04 Cantieri API: Proxying request to backend:",`${r}/api/cantieri`);let s=await fetch(`${r}/api/cantieri`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:t}});if(console.log("\uD83D\uDCE1 Cantieri API: Backend response status:",s.status),!s.ok){let e=await s.json().catch(()=>({detail:"Errore sconosciuto"}));return console.error("❌ Cantieri API: Backend error:",e),i.NextResponse.json(e,{status:s.status})}let a=await s.json();return console.log("\uD83D\uDCE1 Cantieri API: Backend response data:",a),i.NextResponse.json(a,{status:s.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("❌ Cantieri API: Error:",e),i.NextResponse.json({detail:"Errore interno del server"},{status:500})}}async function u(e){try{let t=await e.json(),r=e.headers.get("authorization");if(!r||!r.startsWith("Bearer "))return i.NextResponse.json({detail:"Token di autorizzazione mancante"},{status:401});let s="http://localhost:8001";console.log("\uD83D\uDD04 Cantieri API: Proxying POST request to backend:",`${s}/api/cantieri`);let a=await fetch(`${s}/api/cantieri`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:r},body:JSON.stringify(t)});if(console.log("\uD83D\uDCE1 Cantieri API: Backend response status:",a.status),!a.ok){let e=await a.json().catch(()=>({detail:"Errore sconosciuto"}));return console.error("❌ Cantieri API: Backend error:",e),i.NextResponse.json(e,{status:a.status})}let n=await a.json();return console.log("\uD83D\uDCE1 Cantieri API: Backend response data:",n),i.NextResponse.json(n,{status:a.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("❌ Cantieri API: POST Error:",e),i.NextResponse.json({detail:"Errore interno del server"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/cantieri/route",pathname:"/api/cantieri",filename:"route",bundlePath:"app/api/cantieri/route"},resolvedPagePath:"C:\\CMS\\webapp-nextjs_1\\src\\app\\api\\cantieri\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:h}=p;function x(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(7648));module.exports=s})();