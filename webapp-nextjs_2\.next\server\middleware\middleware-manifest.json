{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|public).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "cYSsmFYrlpuVHFkFQexBs5v1kdv/7ugk/03t8Z8E4Hs=", "__NEXT_PREVIEW_MODE_ID": "6bb1ba943b369c26bce2b8a0948af857", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2100b9b9b3ee1024a4dbe1f3e1197011e90d36d94b717c00d3dfafa9122e64a6", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fd66246da3a5e27e02770f73b55429d4dbd48b0dd65c3907248fa3c09739c28f"}}}, "instrumentation": null, "functions": {}}