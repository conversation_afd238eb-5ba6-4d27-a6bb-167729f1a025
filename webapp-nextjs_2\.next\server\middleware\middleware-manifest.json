{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|public).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "cYSsmFYrlpuVHFkFQexBs5v1kdv/7ugk/03t8Z8E4Hs=", "__NEXT_PREVIEW_MODE_ID": "0d56731f80b8ff21b1e73e0ef530251d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7aaa9a7f16b7d13a67f7ad530c7f53e48a3d86f3a15ed26f27bc85458414b2ac", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "77abc96c00d60a4e133af0311dc18e0c0162adb9e02addb91b950b48b85e381a"}}}, "instrumentation": null, "functions": {}}