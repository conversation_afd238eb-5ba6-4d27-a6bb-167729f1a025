{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|public).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "cYSsmFYrlpuVHFkFQexBs5v1kdv/7ugk/03t8Z8E4Hs=", "__NEXT_PREVIEW_MODE_ID": "f578deba7372b8fb9237149d0c6f5f0b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0979f86cbd1c350edb1bcc397d820a32852372c30680beed617f2f5fff26985e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "240c3bf573d1da00447f4a1e43a74d71a1e718d88526fea17701215a8b009eb5"}}}, "instrumentation": null, "functions": {}}