{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|public).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "cYSsmFYrlpuVHFkFQexBs5v1kdv/7ugk/03t8Z8E4Hs=", "__NEXT_PREVIEW_MODE_ID": "66a1070ff7ba3f5a0dd1e8e3f7b7f91f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5389141621d65c3a22ada959dc0ed8af9eccd084a3c434d7248d5250d9619c5a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f36ff0b8f0f453a0f71fa303884ea0203a7e7ab2802506b68dbc09dcff5c1019"}}}, "instrumentation": null, "functions": {}}