"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4333],{17522:(e,a,i)=>{i.d(a,{jV:()=>r});var s=i(12115),t=i(40283);function r(){let{cantiere:e,isLoading:a}=(0,t.A)(),[i,r]=(0,s.useState)(null),[l,n]=(0,s.useState)(!0),[o,c]=(0,s.useState)(null),d=e=>{if(null==e)return!1;let a="string"==typeof e?parseInt(e,10):e;return!isNaN(a)&&!(a<=0)||(console.warn("\uD83C\uDFD7️ useCantiere: ID cantiere non valido:",e),!1)};return(0,s.useEffect)(()=>{if(a)return void console.log("\uD83C\uDFD7️ useCantiere: Autenticazione in corso...");n(!0),c(null);try{let a=null;if((null==e?void 0:e.id_cantiere)&&d(e.id_cantiere))a=e.id_cantiere,console.log("\uD83C\uDFD7️ useCantiere: Usando cantiere dal context (login cantiere):",a);else{let e=localStorage.getItem("cantiere_data");if(e)try{let i=JSON.parse(e);i.id_cantiere&&d(i.id_cantiere)&&(a=i.id_cantiere,console.log("\uD83C\uDFD7️ useCantiere: Usando cantiere da cantiere_data:",a))}catch(e){console.warn("\uD83C\uDFD7️ useCantiere: Errore parsing cantiere_data:",e)}if(!a){let e=localStorage.getItem("selectedCantiereId");e&&d(e)&&(a=parseInt(e,10),console.log("\uD83C\uDFD7️ useCantiere: Usando cantiere da selectedCantiereId:",a))}}a?(r(a),console.log("\uD83C\uDFD7️ useCantiere: Cantiere valido impostato:",a)):(console.warn("\uD83C\uDFD7️ useCantiere: Nessun cantiere valido trovato"),r(null),c("Nessun cantiere selezionato. Seleziona un cantiere per continuare."))}catch(e){console.error("\uD83C\uDFD7️ useCantiere: Errore nella gestione cantiere:",e),c("Errore nella gestione del cantiere selezionato."),r(null)}finally{n(!1)}},[e,a]),{cantiereId:i,cantiere:e,isValidCantiere:null!==i&&i>0,isLoading:l,error:o,validateCantiere:d,clearError:()=>c(null)}}},84333:(e,a,i)=>{i.d(a,{B:()=>_});var s=i(95155),t=i(12115),r=i(54165),l=i(30285),n=i(62523),o=i(85057),c=i(55365),d=i(37108),m=i(66140),b=i(381),x=i(1243),u=i(47924),g=i(51154),p=i(40646),h=i(25731),f=i(40283),v=i(17522);let N=e=>{let{icon:a,title:i,cableId:t,description:l}=e;return(0,s.jsxs)(r.c7,{children:[(0,s.jsxs)(r.L3,{className:"flex items-center gap-2",children:[a,(0,s.jsx)("span",{children:i})]}),l&&(0,s.jsx)(r.rr,{className:"text-sm text-muted-foreground",children:l})]})},j=e=>{let{children:a,className:i="sm:max-w-md",onKeyDown:t,ariaLabelledBy:l,ariaDescribedBy:n}=e;return(0,s.jsx)(r.Cf,{className:i,onKeyDown:t,"aria-labelledby":l,"aria-describedby":n,onPointerDownOutside:e=>e.preventDefault(),onEscapeKeyDown:e=>{t&&t(e)},children:a})},y=e=>{let{cavo:a}=e;return(0,s.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-5 mb-5",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,s.jsx)(d.A,{className:"h-5 w-5 text-blue-600"}),(0,s.jsx)("h3",{className:"font-semibold text-blue-800 text-base",children:"Informazioni Cavo"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-x-8 gap-y-4",children:[(0,s.jsxs)("div",{className:"info-item",children:[(0,s.jsx)("span",{className:"block text-xs text-gray-500 font-medium uppercase mb-1",children:"Tipologia"}),(0,s.jsx)("span",{className:"text-gray-900 font-bold text-sm",children:a.tipologia||"N/A"})]}),(0,s.jsxs)("div",{className:"info-item",children:[(0,s.jsx)("span",{className:"block text-xs text-gray-500 font-medium uppercase mb-1",children:"Formazione"}),(0,s.jsx)("span",{className:"text-gray-900 font-bold text-sm",children:a.sezione||"N/A"})]}),(0,s.jsxs)("div",{className:"info-item",children:[(0,s.jsx)("span",{className:"block text-xs text-gray-500 font-medium uppercase mb-1",children:"Da"}),(0,s.jsx)("span",{className:"text-gray-900 font-bold text-sm",children:a.ubicazione_partenza||"N/A"})]}),(0,s.jsxs)("div",{className:"info-item",children:[(0,s.jsx)("span",{className:"block text-xs text-gray-500 font-medium uppercase mb-1",children:"A"}),(0,s.jsx)("span",{className:"text-gray-900 font-bold text-sm",children:a.ubicazione_arrivo||"N/A"})]})]})]})},_=e=>{let{mode:a,open:i,onClose:_,cavo:A,cantiere:I,onSave:w}=e,[C,z]=(0,t.useState)(!1),[B,S]=(0,t.useState)(""),[O,k]=(0,t.useState)([]),[D,E]=(0,t.useState)(""),[U,M]=(0,t.useState)(""),[T,L]=(0,t.useState)(""),[V,F]=(0,t.useState)(null),[P,R]=(0,t.useState)("compatibili"),{user:$}=(0,f.A)(),{cantiereId:G,cantiere:J,isValidCantiere:K}=(0,v.jV)(),Z=I||J,q=(null==Z?void 0:Z.id_cantiere)||G,H=!!Z||K,Q=(0,t.useMemo)(()=>{if("aggiungi_metri"===a)return{title:"Inserisci Metri Posati: ".concat((null==A?void 0:A.id_cavo)||"N/A"),description:"Inserisci i metri effettivamente posati per il cavo e seleziona una bobina o usa BOBINA VUOTA",icon:(0,s.jsx)(m.A,{className:"h-5 w-5 text-green-500"}),primaryButtonText:"Salva"};{let e=(null==A?void 0:A.id_bobina)?A.id_bobina.split("_B")[1]||A.id_bobina.split("_b")[1]||A.id_bobina:"",a="Modifica Bobina Cavo: ".concat((null==A?void 0:A.id_cavo)||"N/A");return e&&(a+=" / Bobina: ".concat(e)),{title:a,description:"Seleziona una nuova bobina per il cavo o modifica i parametri",icon:(0,s.jsx)(b.A,{className:"h-5 w-5 text-blue-500"}),primaryButtonText:"Salva Modifiche"}}},[a,null==A?void 0:A.id_cavo,null==A?void 0:A.id_bobina]),W=(0,t.useMemo)(()=>"aggiungi_metri"===a||"modifica_bobina"===a,[a]);(0,t.useEffect)(()=>{i&&(S(""),E(""),M(""),F(null),"aggiungi_metri"===a?L(""):"modifica_bobina"===a&&A&&L(String(A.metratura_reale||A.metri_posati||0)))},[i,a,A]);let X=async()=>{var e;if(!A||!q||!H)return void console.log("\uD83D\uDD0D UnifiedModal: Caricamento bobine saltato - mancano dati:",{cavo:!!A,cantiereId:q,isValidCantiere:H,cantiereToUse:!!Z});console.log("\uD83D\uDD04 UnifiedModal: Caricamento bobine per cantiere:",q),console.log("\uD83D\uDD10 UnifiedModal: Debug autenticazione:",{user:!!$,token:!!localStorage.getItem("token"),tokenLength:(null==(e=localStorage.getItem("token"))?void 0:e.length)||0}),z(!0);try{let e=await h.Fw.getBobine(q),a=[];if(Array.isArray(e))a=e;else if(e&&Array.isArray(e.data))a=e.data;else if(e&&e.bobine&&Array.isArray(e.bobine))a=e.bobine;else throw Error("Formato risposta API non valido");console.log("\uD83D\uDCE6 UnifiedModal: Bobine ricevute:",a.length),console.log("\uD83D\uDCCB UnifiedModal: Dettaglio bobine:",a.map(e=>({id:e.id_bobina,tipologia:e.tipologia,sezione:e.sezione,metri_residui:e.metri_residui,stato:e.stato_bobina})));let i=a.filter(e=>"Terminata"!==e.stato_bobina&&"Over"!==e.stato_bobina&&e.metri_residui>0);console.log("✅ UnifiedModal: Bobine utilizzabili:",i.length);let s=i.map(e=>({...e,compatible:e.tipologia===A.tipologia&&e.sezione===A.sezione})),t=s.filter(e=>e.compatible).length,r=s.filter(e=>!e.compatible).length;console.log("\uD83C\uDFAF UnifiedModal: Compatibilit\xe0 bobine:",{compatibili:t,incompatibili:r}),k(s),S("")}catch(e){console.error("❌ UnifiedModal: Errore caricamento bobine:",e),S("Errore durante il caricamento delle bobine. Riprova."),k([])}finally{z(!1)}};(0,t.useEffect)(()=>{i&&A&&q&&H&&X()},[i,A,q,H]);let Y=(0,t.useMemo)(()=>{if(!D)return O;let e=D.toLowerCase();return O.filter(a=>{var i,s,t;let r=(null==(i=a.id_bobina)?void 0:i.toLowerCase())||"",l=(null==(s=a.tipologia)?void 0:s.toLowerCase())||"",n=(null==(t=a.numero_bobina)?void 0:t.toLowerCase())||"";return r.includes(e)||l.includes(e)||n.includes(e)})},[O,D]),ee=Y.filter(e=>e.compatible),ea=Y.filter(e=>!e.compatible),ei=e=>{if(!e.trim())return"Il campo metri \xe8 obbligatorio";let a=parseFloat(e);return isNaN(a)?"Inserire un valore numerico valido":a<0?"I metri non possono essere negativi":0===a?"I metri devono essere maggiori di zero":a>1e4?"Valore troppo elevato (massimo 10.000m)":""},es=e=>{S(e),setTimeout(()=>S(""),5e3)},et=async()=>{if(A){S(""),z(!0);try{let e={};if("aggiungi_metri"===a){let a=ei(T);if(a){es(a),z(!1);return}e={mode:"aggiungi_metri",cableId:A.id_cavo,metersToInstall:parseFloat(T),bobbinId:U||"BOBINA_VUOTA"}}else if("modifica_bobina"===a){if(!V){es("Selezionare un'opzione di modifica"),z(!1);return}if("cambia_bobina"===V&&!U){es("Selezionare una bobina per continuare"),z(!1);return}e={mode:"modifica_bobina",cableId:A.id_cavo,editOption:V,newBobbinId:U||null}}await w(e),_()}catch(a){var e,i;console.error("Errore salvataggio unified modal:",a),es((null==(i=a.response)||null==(e=i.data)?void 0:e.message)||a.message||"Errore durante il salvataggio. Riprovare.")}finally{z(!1)}}},er=()=>{C||_()},el=(0,t.useMemo)(()=>"aggiungi_metri"===a?T.trim()&&!ei(T):"modifica_bobina"===a&&!(!V||"annulla_posa"!==V&&(!T.trim()||ei(T)))&&("cambia_bobina"!==V||!!U),[a,T,V,U]);return A&&A.id_cavo?(0,s.jsx)(r.lG,{open:i,onOpenChange:er,children:(0,s.jsxs)(j,{className:"sm:max-w-3xl max-h-[85vh] overflow-hidden",onKeyDown:e=>{"Escape"===e.key&&er()},ariaLabelledBy:"unified-modal-title",children:[(0,s.jsx)(N,{icon:Q.icon,title:Q.title,cableId:A.id_cavo,description:Q.description}),(0,s.jsxs)("div",{className:"space-y-4 py-4 overflow-y-auto max-h-[calc(85vh-200px)]",children:[(0,s.jsx)(y,{cavo:A}),B&&(0,s.jsxs)(c.Fc,{className:"bg-red-50 border-red-200",role:"alert","aria-live":"polite",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 text-red-600","aria-hidden":"true"}),(0,s.jsx)(c.TN,{className:"text-red-800",children:B})]}),"aggiungi_metri"===a&&(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex gap-6 items-center",children:[(0,s.jsxs)("div",{className:"flex-shrink-0",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(o.J,{htmlFor:"meters-input",className:"text-sm font-medium text-gray-700",children:"Metri Installati:"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(n.p,{id:"meters-input",type:"number",value:T,onChange:e=>L(e.target.value),placeholder:"0",min:"0",max:"10000",step:"0.1",className:"pr-6 w-24",disabled:C,"aria-describedby":T&&ei(T)?"meters-error":void 0,"aria-invalid":T&&ei(T)?"true":"false"}),(0,s.jsx)("span",{className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm",children:"m"})]})]}),T&&ei(T)&&(0,s.jsx)("p",{id:"meters-error",className:"text-red-600 text-sm mt-1",role:"alert",children:ei(T)})]}),(0,s.jsx)("div",{className:"flex gap-4 flex-1 justify-end",children:(0,s.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>M("BOBINA_VUOTA"),className:"text-xs font-medium border-2 px-3 py-1.5 ".concat("BOBINA_VUOTA"===U?"bg-blue-100 border-blue-500 text-blue-800 shadow-lg":"border-blue-300 text-blue-600 hover:bg-blue-50 hover:border-blue-400"),disabled:C,children:"\uD83D\uDD04 BOBINA VUOTA"})})]})}),"modifica_bobina"===a&&(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex gap-6 items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsxs)(o.J,{className:"text-sm font-medium text-gray-700",children:["Metri Posati: ",(0,s.jsxs)("span",{className:"font-bold text-lg",children:[A.metratura_reale||A.metri_posati||0,"m"]})]})}),(0,s.jsxs)("div",{className:"flex gap-4 flex-1 justify-end",children:[(0,s.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>{F("bobina_vuota"),M(null)},className:"text-xs font-medium border-2 px-3 py-1.5 ".concat("bobina_vuota"===V?"bg-blue-100 border-blue-500 text-blue-800 shadow-lg":"border-blue-300 text-blue-600 hover:bg-blue-50 hover:border-blue-400"),disabled:C,children:"\uD83D\uDD04 BOBINA VUOTA"}),(0,s.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>{F("annulla_posa"),M(null)},className:"text-xs font-medium border-2 px-3 py-1.5 ".concat("annulla_posa"===V?"bg-blue-100 border-blue-500 text-blue-800 shadow-lg":"border-blue-300 text-blue-600 hover:bg-blue-50 hover:border-blue-400"),disabled:C,children:"❌ ANNULLA INSTALLAZIONE"})]})]})}),W&&(0,s.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[(0,s.jsxs)("h3",{className:"font-semibold text-gray-800 mb-3 flex items-center gap-2",children:[(0,s.jsx)(d.A,{className:"h-5 w-5"}),"Selezione Bobina"]}),(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,s.jsx)(n.p,{id:"search-bobina",value:D,onChange:e=>E(e.target.value),placeholder:"Cerca bobina per ID, tipologia o numero...",className:"pl-10",disabled:C})]})}),(0,s.jsxs)("div",{className:"flex border-b border-gray-200 mb-4",children:[(0,s.jsxs)("button",{onClick:()=>R("compatibili"),className:"px-4 py-2 text-sm font-medium border-b-2 transition-colors ".concat("compatibili"===P?"border-green-500 text-green-600 bg-green-50":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:["Compatibili (",ee.length,")"]}),(0,s.jsxs)("button",{onClick:()=>R("incompatibili"),className:"px-4 py-2 text-sm font-medium border-b-2 transition-colors ".concat("incompatibili"===P?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:["Incompatibili (",ea.length,")"]})]}),(0,s.jsx)("div",{className:"border rounded-lg h-64 overflow-y-auto",children:C?(0,s.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(g.A,{className:"h-4 w-4 animate-spin"}),(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Caricamento bobine..."})]})}):(0,s.jsxs)("div",{className:"p-2",children:["compatibili"===P?0===ee.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"text-gray-500 text-sm mb-2",children:"Nessuna bobina compatibile trovata"}),(0,s.jsxs)("div",{className:"text-xs text-gray-400",children:["Cercando bobine con tipologia ",(0,s.jsx)("strong",{children:null==A?void 0:A.tipologia})," e formazione ",(0,s.jsx)("strong",{children:null==A?void 0:A.sezione})]})]}):(0,s.jsx)("div",{className:"space-y-1",children:ee.map(e=>{let i=e.id_bobina.split("_B")[1]||e.id_bobina.split("_b")[1]||e.id_bobina;return(0,s.jsx)("div",{onClick:()=>{M(e.id_bobina),"modifica_bobina"===a&&F("cambia_bobina")},className:"p-3 rounded cursor-pointer transition-colors border-2 ".concat(U===e.id_bobina?"bg-blue-100 border-blue-500 shadow-md":"hover:bg-gray-50 border-gray-200 hover:border-gray-300"),children:(0,s.jsxs)("div",{className:"flex flex-wrap gap-6 text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-gray-500 font-medium",children:"ID:"}),(0,s.jsx)("span",{className:"text-gray-900 font-bold text-base",children:i})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-gray-500 font-medium",children:"TIPOLOGIA:"}),(0,s.jsx)("span",{className:"text-gray-900 font-semibold",children:e.tipologia})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-gray-500 font-medium",children:"FORMAZIONE:"}),(0,s.jsx)("span",{className:"text-gray-900 font-semibold",children:e.sezione})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-gray-500 font-medium",children:"RESIDUI:"}),(0,s.jsxs)("span",{className:"text-gray-900 font-bold",children:[e.metri_residui,"m"]})]})]})},e.id_bobina)})}):0===ea.length?(0,s.jsx)("div",{className:"text-center py-8",children:(0,s.jsx)("div",{className:"text-gray-500 text-sm",children:"Nessuna bobina incompatibile trovata"})}):(0,s.jsx)("div",{className:"space-y-1",children:ea.map(e=>{let i=e.id_bobina.split("_B")[1]||e.id_bobina.split("_b")[1]||e.id_bobina;return(0,s.jsx)("div",{onClick:()=>{M(e.id_bobina),"modifica_bobina"===a&&F("cambia_bobina")},className:"p-3 rounded cursor-pointer transition-colors border-2 ".concat(U===e.id_bobina?"bg-blue-100 border-blue-500 shadow-md":"hover:bg-gray-50 border-gray-200 hover:border-gray-300"),children:(0,s.jsxs)("div",{className:"flex flex-wrap gap-6 text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-gray-500 font-medium",children:"ID:"}),(0,s.jsx)("span",{className:"text-gray-900 font-bold text-base",children:i})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-gray-500 font-medium",children:"TIPOLOGIA:"}),(0,s.jsx)("span",{className:"text-gray-900 font-semibold",children:e.tipologia})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-gray-500 font-medium",children:"FORMAZIONE:"}),(0,s.jsx)("span",{className:"text-gray-900 font-semibold",children:e.sezione})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-gray-500 font-medium",children:"RESIDUI:"}),(0,s.jsxs)("span",{className:"text-gray-900 font-bold",children:[e.metri_residui,"m"]})]})]})},e.id_bobina)})}),!C&&0===O.length&&(0,s.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,s.jsx)(d.A,{className:"h-12 w-12 mx-auto mb-2 text-gray-300"}),(0,s.jsx)("p",{children:"Nessuna bobina disponibile"}),(0,s.jsx)("p",{className:"text-sm",children:"Verifica che ci siano bobine nel parco cavi"})]})]})})]})]}),(0,s.jsxs)(r.Es,{className:"gap-2",children:[(0,s.jsx)(l.$,{variant:"outline",onClick:er,disabled:C,className:"flex-1 hover:bg-gray-50",children:"Annulla"}),(0,s.jsx)(l.$,{onClick:et,disabled:C||!el,className:"flex-1 ".concat(el?"aggiungi_metri"===a?"bg-green-600 hover:bg-green-700":"bg-blue-600 hover:bg-blue-700":"opacity-50 cursor-not-allowed"),children:C?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salvando..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p.A,{className:"mr-2 h-4 w-4"}),Q.primaryButtonText]})})]})]})}):(console.warn("⚠️ UnifiedCableBobbinModal: Tentativo di apertura senza cavo valido:",A),null)}}}]);