'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { Loader2 } from 'lucide-react'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiresUser?: boolean
  requiresAdmin?: boolean
}

export default function ProtectedRoute({ 
  children, 
  requiresUser = false, 
  requiresAdmin = false 
}: ProtectedRouteProps) {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        router.push('/login')
        return
      }

      if (requiresAdmin && user?.ruolo !== 'admin') {
        router.push('/unauthorized')
        return
      }

      if (requiresUser && !user) {
        router.push('/login')
        return
      }
    }
  }, [isAuthenticated, isLoading, user, router, requiresAdmin, requiresUser])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  if (requiresAdmin && user?.ruolo !== 'admin') {
    return null
  }

  if (requiresUser && !user) {
    return null
  }

  return <>{children}</>
}
