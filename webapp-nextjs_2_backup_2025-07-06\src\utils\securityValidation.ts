/**
 * 🔒 SECURITY VALIDATION UTILITIES
 * Validazione robusta per prevenire attacchi di sicurezza
 */

// Caratteri pericolosi da rimuovere/escape
const DANGEROUS_CHARS = /[<>\"'&\x00-\x1f\x7f-\x9f]/g
const SQL_INJECTION_PATTERNS = /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi
const XSS_PATTERNS = /(<script|javascript:|vbscript:|onload|onerror|onclick)/gi

/**
 * Sanitizza input rimuovendo caratteri pericolosi
 */
export const sanitizeInput = (input: string): string => {
  if (typeof input !== 'string') return ''
  
  return input
    .trim()
    .replace(DANGEROUS_CHARS, '') // Rimuove caratteri pericolosi
    .replace(/\s+/g, ' ') // Normalizza spazi
    .substring(0, 1000) // <PERSON><PERSON> lunghezza massima
}

/**
 * Valida username con regole sicure
 */
export const validateUsername = (username: string): { isValid: boolean; error?: string } => {
  const sanitized = sanitizeInput(username)
  
  if (sanitized.length < 3) {
    return { isValid: false, error: 'Username deve essere almeno 3 caratteri' }
  }
  
  if (sanitized.length > 20) {
    return { isValid: false, error: 'Username non può superare 20 caratteri' }
  }
  
  if (!/^[a-zA-Z0-9._-]+$/.test(sanitized)) {
    return { isValid: false, error: 'Username può contenere solo lettere, numeri, punti, underscore e trattini' }
  }
  
  if (/^[._-]|[._-]$/.test(sanitized)) {
    return { isValid: false, error: 'Username non può iniziare o finire con caratteri speciali' }
  }
  
  return { isValid: true }
}

/**
 * Valida password con regole di sicurezza
 */
export const validatePassword = (password: string): { isValid: boolean; error?: string; strength: number } => {
  if (!password || password.length < 8) {
    return { isValid: false, error: 'Password deve essere almeno 8 caratteri', strength: 0 }
  }
  
  if (password.length > 128) {
    return { isValid: false, error: 'Password troppo lunga (max 128 caratteri)', strength: 0 }
  }
  
  let strength = 0
  
  // Controlla criteri di sicurezza
  if (/[a-z]/.test(password)) strength++
  if (/[A-Z]/.test(password)) strength++
  if (/[0-9]/.test(password)) strength++
  if (/[^a-zA-Z0-9]/.test(password)) strength++
  if (password.length >= 12) strength++
  
  if (strength < 3) {
    return { 
      isValid: false, 
      error: 'Password deve contenere almeno: 1 minuscola, 1 maiuscola, 1 numero o 1 carattere speciale',
      strength 
    }
  }
  
  // Controlla password comuni
  const commonPasswords = ['password', '123456', 'admin', 'qwerty', 'letmein']
  if (commonPasswords.some(common => password.toLowerCase().includes(common))) {
    return { isValid: false, error: 'Password troppo comune', strength }
  }
  
  return { isValid: true, strength }
}

/**
 * Valida email con controlli di sicurezza
 */
export const validateEmail = (email: string): { isValid: boolean; error?: string } => {
  const sanitized = sanitizeInput(email)
  
  if (!sanitized) {
    return { isValid: false, error: 'Email è obbligatoria' }
  }
  
  if (sanitized.length > 254) {
    return { isValid: false, error: 'Email troppo lunga' }
  }
  
  // Regex RFC 5322 semplificata ma sicura
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/
  
  if (!emailRegex.test(sanitized)) {
    return { isValid: false, error: 'Formato email non valido' }
  }
  
  return { isValid: true }
}

/**
 * Valida testo generico con controlli XSS
 */
export const validateText = (text: string, maxLength: number = 255): { isValid: boolean; error?: string } => {
  const sanitized = sanitizeInput(text)
  
  if (sanitized.length > maxLength) {
    return { isValid: false, error: `Testo troppo lungo (max ${maxLength} caratteri)` }
  }
  
  // Controlla pattern XSS
  if (XSS_PATTERNS.test(text)) {
    return { isValid: false, error: 'Contenuto non consentito rilevato' }
  }
  
  // Controlla pattern SQL injection
  if (SQL_INJECTION_PATTERNS.test(text)) {
    return { isValid: false, error: 'Contenuto non consentito rilevato' }
  }
  
  return { isValid: true }
}

/**
 * Valida ragione sociale con controlli business
 */
export const validateRagioneSociale = (ragioneSociale: string): { isValid: boolean; error?: string } => {
  const sanitized = sanitizeInput(ragioneSociale)
  
  if (!sanitized) {
    return { isValid: false, error: 'Ragione sociale è obbligatoria' }
  }
  
  if (sanitized.length < 2) {
    return { isValid: false, error: 'Ragione sociale troppo corta' }
  }
  
  if (sanitized.length > 100) {
    return { isValid: false, error: 'Ragione sociale troppo lunga (max 100 caratteri)' }
  }
  
  // Solo lettere, numeri, spazi e caratteri business comuni
  if (!/^[a-zA-Z0-9\s\.\-&']+$/.test(sanitized)) {
    return { isValid: false, error: 'Ragione sociale contiene caratteri non consentiti' }
  }
  
  return { isValid: true }
}

/**
 * Valida codice VAT/Partita IVA
 */
export const validateVAT = (vat: string): { isValid: boolean; error?: string } => {
  if (!vat) return { isValid: true } // VAT è opzionale
  
  const sanitized = sanitizeInput(vat).replace(/\s/g, '') // Rimuove spazi
  
  if (sanitized.length < 8 || sanitized.length > 15) {
    return { isValid: false, error: 'VAT deve essere tra 8 e 15 caratteri' }
  }
  
  // Solo numeri e lettere per VAT internazionali
  if (!/^[A-Z0-9]+$/i.test(sanitized)) {
    return { isValid: false, error: 'VAT può contenere solo lettere e numeri' }
  }
  
  return { isValid: true }
}

/**
 * Rate limiting semplice (in-memory)
 */
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

export const checkRateLimit = (key: string, maxAttempts: number, windowMs: number): boolean => {
  const now = Date.now()
  const record = rateLimitStore.get(key)
  
  if (!record || now > record.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs })
    return true
  }
  
  if (record.count >= maxAttempts) {
    return false
  }
  
  record.count++
  return true
}

/**
 * Genera token CSRF sicuro
 */
export const generateCSRFToken = (): string => {
  const array = new Uint8Array(32)
  crypto.getRandomValues(array)
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
}

/**
 * Valida form completo utente
 */
export const validateUserForm = (formData: {
  username: string
  password?: string
  ragione_sociale: string
  email?: string
  vat?: string
  indirizzo?: string
  nazione?: string
  referente_aziendale?: string
}): { isValid: boolean; errors: Record<string, string> } => {
  const errors: Record<string, string> = {}
  
  // Valida username
  const usernameValidation = validateUsername(formData.username)
  if (!usernameValidation.isValid) {
    errors.username = usernameValidation.error!
  }
  
  // Valida password (se presente)
  if (formData.password) {
    const passwordValidation = validatePassword(formData.password)
    if (!passwordValidation.isValid) {
      errors.password = passwordValidation.error!
    }
  }
  
  // Valida ragione sociale
  const ragioneSocialeValidation = validateRagioneSociale(formData.ragione_sociale)
  if (!ragioneSocialeValidation.isValid) {
    errors.ragione_sociale = ragioneSocialeValidation.error!
  }
  
  // Valida email (se presente)
  if (formData.email) {
    const emailValidation = validateEmail(formData.email)
    if (!emailValidation.isValid) {
      errors.email = emailValidation.error!
    }
  }
  
  // Valida VAT (se presente)
  if (formData.vat) {
    const vatValidation = validateVAT(formData.vat)
    if (!vatValidation.isValid) {
      errors.vat = vatValidation.error!
    }
  }
  
  // Valida campi testo opzionali
  if (formData.indirizzo) {
    const indirizzoValidation = validateText(formData.indirizzo, 200)
    if (!indirizzoValidation.isValid) {
      errors.indirizzo = indirizzoValidation.error!
    }
  }
  
  if (formData.nazione) {
    const nazioneValidation = validateText(formData.nazione, 50)
    if (!nazioneValidation.isValid) {
      errors.nazione = nazioneValidation.error!
    }
  }
  
  if (formData.referente_aziendale) {
    const referenteValidation = validateText(formData.referente_aziendale, 100)
    if (!referenteValidation.isValid) {
      errors.referente_aziendale = referenteValidation.error!
    }
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}
