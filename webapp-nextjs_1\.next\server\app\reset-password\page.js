(()=>{var e={};e.id=4700,e.ids=[4700],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},12597:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15199:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>k});var t=r(60687),a=r(43210),i=r(85814),n=r.n(i),o=r(23361),l=r(28559),d=r(29523),c=r(16189),u=r(89667),p=r(80013),m=r(44493),x=r(91821),h=r(46657),f=r(35071),w=r(64021),v=r(12597),g=r(13861),b=r(5336),j=r(43649),y=r(99891),N=r(4780);function P(){var e;let s=(0,c.useRouter)(),r=(0,c.useSearchParams)().get("token"),[i,n]=(0,a.useState)({newPassword:"",confirmPassword:""}),[o,l]=(0,a.useState)({new:!1,confirm:!1}),[P,k]=(0,a.useState)({score:0,feedback:[],isValid:!1}),[A,C]=(0,a.useState)(!1),[_,q]=(0,a.useState)(null),[z,S]=(0,a.useState)(null),M=async e=>{if(!e)return void k({score:0,feedback:[],isValid:!1});try{let s=await fetch("/api/password/validate-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({password:e})});if(s.ok){let e=await s.json();k({score:e.strength_score,feedback:e.suggestions||[],isValid:e.is_valid})}}catch(e){}},R=(e,s)=>{n(r=>({...r,[e]:s})),"newPassword"===e&&M(s)},T=async e=>{e.preventDefault(),C(!0),q(null);try{if(!i.newPassword||!i.confirmPassword)throw Error("Tutti i campi sono obbligatori");if(i.newPassword!==i.confirmPassword)throw Error("Le password non corrispondono");if(!P.isValid)throw Error("La password non rispetta i requisiti di sicurezza");if(!r)throw Error("Token di reset non valido");let e=await fetch("/api/password/confirm-password-reset",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({token:r,new_password:i.newPassword,confirm_password:i.confirmPassword})}),t=await e.json();if(e.ok&&t.success)q({type:"success",text:t.message}),setTimeout(()=>{s.push("/login")},3e3);else throw Error(t.detail||t.message||"Errore durante il reset della password")}catch(e){q({type:"error",text:e instanceof Error?e.message:"Errore durante il reset della password"})}finally{C(!1)}},E=e=>{l(s=>({...s,[e]:!s[e]}))};return!1===z?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,t.jsxs)(m.Zp,{className:"w-full max-w-md mx-auto",children:[(0,t.jsxs)(m.aR,{className:"text-center",children:[(0,t.jsxs)(m.ZB,{className:"text-2xl flex items-center justify-center gap-2 text-red-600",children:[(0,t.jsx)(f.A,{className:"h-6 w-6"}),"Token Non Valido"]}),(0,t.jsx)(m.BT,{children:"Il link di reset password non \xe8 valido o \xe8 scaduto"})]}),(0,t.jsxs)(m.Wu,{className:"text-center",children:[(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Il token potrebbe essere scaduto o gi\xe0 utilizzato. Richiedi un nuovo link di reset password."}),(0,t.jsx)(d.$,{onClick:()=>s.push("/forgot-password"),className:"bg-mariner-600 hover:bg-mariner-700",children:"Richiedi Nuovo Reset"})]})]})}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,t.jsxs)(m.Zp,{className:"w-full max-w-md mx-auto",children:[(0,t.jsxs)(m.aR,{className:"space-y-1",children:[(0,t.jsxs)(m.ZB,{className:"text-2xl flex items-center gap-2",children:[(0,t.jsx)(w.A,{className:"h-5 w-5 text-mariner-600"}),"Reimposta Password"]}),(0,t.jsx)(m.BT,{children:"Inserisci la tua nuova password sicura"})]}),(0,t.jsxs)(m.Wu,{children:[(0,t.jsxs)("form",{onSubmit:T,className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"newPassword",children:"Nuova Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(u.p,{id:"newPassword",type:o.new?"text":"password",value:i.newPassword,onChange:e=>R("newPassword",e.target.value),className:"pr-10",required:!0}),(0,t.jsx)(d.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>E("new"),children:o.new?(0,t.jsx)(v.A,{className:"h-4 w-4 text-gray-400"}):(0,t.jsx)(g.A,{className:"h-4 w-4 text-gray-400"})})]}),i.newPassword&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,t.jsx)("span",{children:"Forza password:"}),(0,t.jsx)("span",{className:(0,N.cn)("font-medium",P.score<2?"text-red-600":P.score<4?"text-yellow-600":"text-green-600"),children:(e=P.score)<2?"Debole":e<4?"Media":"Forte"})]}),(0,t.jsx)(h.k,{value:P.score/5*100,className:"h-2"}),P.feedback.length>0&&(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,t.jsx)("p",{className:"font-medium mb-1",children:"Suggerimenti:"}),(0,t.jsx)("ul",{className:"list-disc list-inside space-y-1",children:P.feedback.map((e,s)=>(0,t.jsx)("li",{children:e},s))})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(p.J,{htmlFor:"confirmPassword",children:"Conferma Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(u.p,{id:"confirmPassword",type:o.confirm?"text":"password",value:i.confirmPassword,onChange:e=>R("confirmPassword",e.target.value),className:"pr-10",required:!0}),(0,t.jsx)(d.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>E("confirm"),children:o.confirm?(0,t.jsx)(v.A,{className:"h-4 w-4 text-gray-400"}):(0,t.jsx)(g.A,{className:"h-4 w-4 text-gray-400"})})]}),i.confirmPassword&&(0,t.jsx)("div",{className:"flex items-center gap-2 text-sm",children:i.newPassword===i.confirmPassword?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(b.A,{className:"h-4 w-4 text-green-600"}),(0,t.jsx)("span",{className:"text-green-600",children:"Le password corrispondono"})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(f.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)("span",{className:"text-red-600",children:"Le password non corrispondono"})]})})]}),_&&(0,t.jsxs)(x.Fc,{className:(0,N.cn)("success"===_.type?"border-green-200 bg-green-50":"border-red-200 bg-red-50"),children:["success"===_.type?(0,t.jsx)(b.A,{className:"h-4 w-4 text-green-600"}):(0,t.jsx)(j.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsxs)(x.TN,{className:(0,N.cn)("success"===_.type?"text-green-800":"text-red-800"),children:[_.text,"success"===_.type&&(0,t.jsx)("div",{className:"mt-2 text-sm",children:"Verrai reindirizzato al login tra pochi secondi..."})]})]}),(0,t.jsx)(d.$,{type:"submit",className:"w-full bg-mariner-600 hover:bg-mariner-700",disabled:A||!P.isValid||i.newPassword!==i.confirmPassword,children:A?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Aggiornamento..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Reimposta Password"]})})]}),(0,t.jsx)("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-start gap-2",children:[(0,t.jsx)(y.A,{className:"h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,t.jsx)("p",{className:"font-medium mb-1",children:"Sicurezza"}),(0,t.jsxs)("ul",{className:"space-y-1 text-xs",children:[(0,t.jsx)("li",{children:"• Usa una password unica che non hai mai utilizzato prima"}),(0,t.jsx)("li",{children:"• Combina lettere maiuscole, minuscole, numeri e simboli"}),(0,t.jsx)("li",{children:"• Evita informazioni personali facilmente indovinabili"}),(0,t.jsx)("li",{children:"• Considera l'uso di un gestore di password"})]})]})]})})]})]})})}function k(){return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-slate-100 flex items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"w-full max-w-md space-y-6",children:[(0,t.jsxs)("div",{className:"text-center space-y-2",children:[(0,t.jsx)("div",{className:"flex justify-center",children:(0,t.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center",children:(0,t.jsx)(o.A,{className:"w-8 h-8 text-white"})})}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-slate-900",children:"CABLYS"}),(0,t.jsx)("p",{className:"text-slate-600",children:"Reimposta Password"})]}),(0,t.jsx)(a.Suspense,{fallback:(0,t.jsx)("div",{className:"text-center",children:"Caricamento..."}),children:(0,t.jsx)(P,{})}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)(n(),{href:"/login",children:(0,t.jsxs)(d.$,{variant:"ghost",className:"text-slate-600 hover:text-slate-900",children:[(0,t.jsx)(l.A,{className:"w-4 h-4 mr-2"}),"Torna al Login"]})})})]})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35071:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},44493:(e,s,r)=>{"use strict";r.d(s,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>i,aR:()=>n});var t=r(60687);r(43210);var a=r(4780);function i({className:e,...s}){return(0,t.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...s})}function n({className:e,...s}){return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...s})}function o({className:e,...s}){return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...s})}function l({className:e,...s}){return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...s})}function d({className:e,...s}){return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...s})}},46657:(e,s,r)=>{"use strict";r.d(s,{k:()=>j});var t=r(60687),a=r(43210),i=r(11273),n=r(14163),o="Progress",[l,d]=(0,i.A)(o),[c,u]=l(o),p=a.forwardRef((e,s)=>{var r,a;let{__scopeProgress:i,value:o=null,max:l,getValueLabel:d=h,...u}=e;(l||0===l)&&!v(l)&&console.error((r=`${l}`,`Invalid prop \`max\` of value \`${r}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let p=v(l)?l:100;null===o||g(o,p)||console.error((a=`${o}`,`Invalid prop \`value\` of value \`${a}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let m=g(o,p)?o:null,x=w(m)?d(m,p):void 0;return(0,t.jsx)(c,{scope:i,value:m,max:p,children:(0,t.jsx)(n.sG.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":w(m)?m:void 0,"aria-valuetext":x,role:"progressbar","data-state":f(m,p),"data-value":m??void 0,"data-max":p,...u,ref:s})})});p.displayName=o;var m="ProgressIndicator",x=a.forwardRef((e,s)=>{let{__scopeProgress:r,...a}=e,i=u(m,r);return(0,t.jsx)(n.sG.div,{"data-state":f(i.value,i.max),"data-value":i.value??void 0,"data-max":i.max,...a,ref:s})});function h(e,s){return`${Math.round(e/s*100)}%`}function f(e,s){return null==e?"indeterminate":e===s?"complete":"loading"}function w(e){return"number"==typeof e}function v(e){return w(e)&&!isNaN(e)&&e>0}function g(e,s){return w(e)&&!isNaN(e)&&e<=s&&e>=0}x.displayName=m;var b=r(4780);function j({className:e,value:s,...r}){return(0,t.jsx)(p,{"data-slot":"progress",className:(0,b.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...r,children:(0,t.jsx)(x,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(s||0)}%)`}})})}},54401:(e,s,r)=>{Promise.resolve().then(r.bind(r,85316))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60098:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var t=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(s,l);let d={children:["",{children:["reset-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,85316)),"C:\\CMS\\webapp-nextjs_1\\src\\app\\reset-password\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\CMS\\webapp-nextjs_1\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\CMS\\webapp-nextjs_1\\src\\app\\reset-password\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/reset-password/page",pathname:"/reset-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64021:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},70440:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});var t=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},78148:(e,s,r)=>{"use strict";r.d(s,{b:()=>o});var t=r(43210),a=r(14163),i=r(60687),n=t.forwardRef((e,s)=>(0,i.jsx)(a.sG.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var o=n},79551:e=>{"use strict";e.exports=require("url")},80013:(e,s,r)=>{"use strict";r.d(s,{J:()=>n});var t=r(60687);r(43210);var a=r(78148),i=r(4780);function n({className:e,...s}){return(0,t.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...s})}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85316:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs_1\\\\src\\\\app\\\\reset-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs_1\\src\\app\\reset-password\\page.tsx","default")},89667:(e,s,r)=>{"use strict";r.d(s,{p:()=>n});var t=r(60687),a=r(43210),i=r(4780);let n=a.forwardRef(({className:e,type:s,...r},a)=>(0,t.jsx)("input",{type:s,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),ref:a,...r}));n.displayName="Input"},90849:(e,s,r)=>{Promise.resolve().then(r.bind(r,15199))},94735:e=>{"use strict";e.exports=require("events")},99891:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,6539,1658,4951],()=>r(60098));module.exports=t})();