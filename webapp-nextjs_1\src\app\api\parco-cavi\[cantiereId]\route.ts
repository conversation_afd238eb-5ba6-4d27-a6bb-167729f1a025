import { NextRequest, NextResponse } from 'next/server'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ cantiereId: string }> }
) {
  try {
    const { cantiereId } = await params
    
    // Estrai il token di autorizzazione dall'header
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { 
          detail: 'Token di autorizzazione mancante' 
        }, 
        { status: 401 }
      )
    }

    // Estrai i parametri di query
    const { searchParams } = new URL(request.url)
    const queryParams = new URLSearchParams()
    
    // Passa tutti i parametri al backend
    searchParams.forEach((value, key) => {
      queryParams.append(key, value)
    })

    // Proxy la richiesta al backend FastAPI
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'
    const queryString = queryParams.toString()
    const url = `${backendUrl}/api/parco-cavi/${cantiereId}${queryString ? `?${queryString}` : ''}`
    
    console.log('🔄 Parco-Cavi API: Proxying request to backend:', url)
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader
      }
    })

    console.log('📡 Parco-Cavi API: Backend response status:', response.status)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: 'Errore sconosciuto' }))
      console.error('❌ Parco-Cavi API: Backend error:', errorData)
      return NextResponse.json(errorData, { 
        status: response.status 
      })
    }

    const data = await response.json()
    console.log('📡 Parco-Cavi API: Backend response data:', data)

    // Il backend restituisce direttamente l'array, quindi lo restituiamo così com'è
    return NextResponse.json(data, { 
      status: response.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })

  } catch (error) {
    console.error('❌ Parco-Cavi API: Error:', error)
    return NextResponse.json(
      { 
        detail: 'Errore interno del server' 
      }, 
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { cantiereId: string } }
) {
  try {
    const cantiereId = params.cantiereId
    const body = await request.json()
    
    // Estrai il token di autorizzazione dall'header
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { 
          detail: 'Token di autorizzazione mancante' 
        }, 
        { status: 401 }
      )
    }

    // Proxy la richiesta al backend FastAPI
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'
    
    console.log('🔄 Parco-Cavi API: Proxying POST request to backend:', `${backendUrl}/api/parco-cavi/${cantiereId}`)
    
    const response = await fetch(`${backendUrl}/api/parco-cavi/${cantiereId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader
      },
      body: JSON.stringify(body)
    })

    console.log('📡 Parco-Cavi API: Backend response status:', response.status)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: 'Errore sconosciuto' }))
      console.error('❌ Parco-Cavi API: Backend error:', errorData)
      return NextResponse.json(errorData, { 
        status: response.status 
      })
    }

    const data = await response.json()
    console.log('📡 Parco-Cavi API: Backend response data:', data)

    return NextResponse.json(data, { 
      status: response.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })

  } catch (error) {
    console.error('❌ Parco-Cavi API: POST Error:', error)
    return NextResponse.json(
      { 
        detail: 'Errore interno del server' 
      }, 
      { status: 500 }
    )
  }
}
