(()=>{var e={};e.id=3472,e.ids=[3472],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},60653:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>d,serverHooks:()=>l,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>c});var s={};t.r(s),t.d(s,{POST:()=>i});var a=t(96559),o=t(48088),n=t(37719),p=t(32190);async function i(e){try{let r=await e.json(),t=await fetch("http://localhost:8001/api/password/validate-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)}),s=await t.json();return p.NextResponse.json(s,{status:t.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("Password validation error:",e),p.NextResponse.json({success:!1,detail:"Errore interno del server"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/password/validate-password/route",pathname:"/api/password/validate-password",filename:"route",bundlePath:"app/api/password/validate-password/route"},resolvedPagePath:"C:\\CMS\\webapp-nextjs_1\\src\\app\\api\\password\\validate-password\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:u,workUnitAsyncStorage:c,serverHooks:l}=d;function w(){return(0,n.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:c})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580],()=>t(60653));module.exports=s})();