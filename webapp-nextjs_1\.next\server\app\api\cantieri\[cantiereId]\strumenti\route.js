(()=>{var e={};e.id=3534,e.ids=[3534],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17821:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>u,POST:()=>c});var n=r(96559),o=r(48088),a=r(37719),i=r(32190);async function u(e,{params:t}){try{let r=parseInt(t.cantiereId);if(isNaN(r))return i.NextResponse.json({error:"ID cantiere non valido"},{status:400});let s=e.headers.get("authorization");if(!s)return i.NextResponse.json({error:"Token di autenticazione richiesto"},{status:401});let n=await fetch(`http://localhost:8001/api/cantieri/${r}/strumenti`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:s}});if(!n.ok){let e=await n.json().catch(()=>({detail:"Errore sconosciuto"}));return i.NextResponse.json({error:e.detail||"Errore dal backend"},{status:n.status})}let o=await n.json();return i.NextResponse.json({success:!0,data:o,total:o.length})}catch(e){return console.error("Errore nel recupero strumenti:",e),i.NextResponse.json({error:"Errore interno del server"},{status:500})}}async function c(e,{params:t}){try{let r=parseInt(t.cantiereId),s=await e.json();if(isNaN(r))return i.NextResponse.json({error:"ID cantiere non valido"},{status:400});let n=e.headers.get("authorization");if(!n)return i.NextResponse.json({error:"Token di autenticazione richiesto"},{status:401});if(!s.nome||!s.marca||!s.modello||!s.numero_serie)return i.NextResponse.json({error:"Campi obbligatori mancanti"},{status:400});let o=await fetch(`http://localhost:8001/api/cantieri/${r}/strumenti`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:n},body:JSON.stringify(s)});if(!o.ok){let e=await o.json().catch(()=>({detail:"Errore sconosciuto"}));return i.NextResponse.json({error:e.detail||"Errore dal backend"},{status:o.status})}let a=await o.json();return i.NextResponse.json({success:!0,data:a},{status:201})}catch(e){return console.error("Errore nella creazione strumento:",e),i.NextResponse.json({error:"Errore interno del server"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/cantieri/[cantiereId]/strumenti/route",pathname:"/api/cantieri/[cantiereId]/strumenti",filename:"route",bundlePath:"app/api/cantieri/[cantiereId]/strumenti/route"},resolvedPagePath:"C:\\CMS\\webapp-nextjs_1\\src\\app\\api\\cantieri\\[cantiereId]\\strumenti\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:h}=p;function x(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(17821));module.exports=s})();