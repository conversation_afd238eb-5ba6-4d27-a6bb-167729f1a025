(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9483],{20185:(t,e,o)=>{"use strict";o.r(e),o.d(e,{default:()=>a});var n=o(95155),s=o(12115);function a(){let[t,e]=(0,s.useState)(""),[o,a]=(0,s.useState)(!1),i=async()=>{a(!0),e("Testing...");try{console.log("Starting admin login test...");let t=new FormData;t.append("username","admin"),t.append("password","admin"),console.log("Sending request to:","http://localhost:8001/api/auth/login");let o=await fetch("http://localhost:8001/api/auth/login",{method:"POST",body:t});console.log("Response status:",o.status),console.log("Response headers:",o.headers);let n=await o.json();console.log("Response data:",n),o.ok?e("SUCCESS: ".concat(JSON.stringify(n,null,2))):e("ERROR: ".concat(o.status," - ").concat(JSON.stringify(n,null,2)))}catch(t){console.error("Login test error:",t),e("EXCEPTION: ".concat(t.message))}finally{a(!1)}},l=async()=>{a(!0),e("Testing...");try{console.log("Starting cantiere login test...");let t=await fetch("http://localhost:8001/api/auth/login/cantiere",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({codice_univoco:"TEST123",password:"test123"})});console.log("Response status:",t.status);let o=await t.json();console.log("Response data:",o),t.ok?e("SUCCESS: ".concat(JSON.stringify(o,null,2))):e("ERROR: ".concat(t.status," - ").concat(JSON.stringify(o,null,2)))}catch(t){console.error("Cantiere login test error:",t),e("EXCEPTION: ".concat(t.message))}finally{a(!1)}};return(0,n.jsxs)("div",{style:{padding:"20px",fontFamily:"monospace"},children:[(0,n.jsx)("h1",{children:"Test Login Page"}),(0,n.jsxs)("div",{style:{marginBottom:"20px"},children:[(0,n.jsx)("button",{onClick:i,disabled:o,style:{padding:"10px 20px",marginRight:"10px",backgroundColor:"#007bff",color:"white",border:"none",borderRadius:"5px",cursor:o?"not-allowed":"pointer"},children:o?"Testing...":"Test Admin Login"}),(0,n.jsx)("button",{onClick:l,disabled:o,style:{padding:"10px 20px",backgroundColor:"#28a745",color:"white",border:"none",borderRadius:"5px",cursor:o?"not-allowed":"pointer"},children:o?"Testing...":"Test Cantiere Login"})]}),(0,n.jsx)("div",{style:{backgroundColor:"#f8f9fa",padding:"15px",borderRadius:"5px",whiteSpace:"pre-wrap",minHeight:"200px"},children:t||"Click a button to test login..."})]})}},23784:(t,e,o)=>{Promise.resolve().then(o.bind(o,20185))}},t=>{var e=e=>t(t.s=e);t.O(0,[8441,1684,7358],()=>e(23784)),_N_E=t.O()}]);