"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9815],{15452:(e,t,r)=>{r.d(t,{G$:()=>V,Hs:()=>j,UC:()=>er,VY:()=>eo,ZL:()=>ee,bL:()=>$,bm:()=>ea,hE:()=>en,hJ:()=>et,l9:()=>Q});var n=r(12115),o=r(85185),a=r(6101),l=r(46081),i=r(61285),s=r(5845),c=r(19178),d=r(25519),u=r(34378),p=r(28905),f=r(63655),g=r(92293),h=r(93795),m=r(38168),v=r(99708),y=r(95155),x="Dialog",[b,j]=(0,l.A)(x),[k,C]=b(x),D=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:l,modal:c=!0}=e,d=n.useRef(null),u=n.useRef(null),[p,f]=(0,s.i)({prop:o,defaultProp:null!=a&&a,onChange:l,caller:x});return(0,y.jsx)(k,{scope:t,triggerRef:d,contentRef:u,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:c,children:r})};D.displayName=x;var w="DialogTrigger",R=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=C(w,r),i=(0,a.s)(t,l.triggerRef);return(0,y.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":K(l.open),...n,ref:i,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});R.displayName=w;var E="DialogPortal",[I,N]=b(E,{forceMount:void 0}),_=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,l=C(E,t);return(0,y.jsx)(I,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,y.jsx)(p.C,{present:r||l.open,children:(0,y.jsx)(u.Z,{asChild:!0,container:a,children:e})}))})};_.displayName=E;var O="DialogOverlay",A=n.forwardRef((e,t)=>{let r=N(O,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=C(O,e.__scopeDialog);return a.modal?(0,y.jsx)(p.C,{present:n||a.open,children:(0,y.jsx)(P,{...o,ref:t})}):null});A.displayName=O;var F=(0,v.TL)("DialogOverlay.RemoveScroll"),P=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=C(O,r);return(0,y.jsx)(h.A,{as:F,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(f.sG.div,{"data-state":K(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),G="DialogContent",L=n.forwardRef((e,t)=>{let r=N(G,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=C(G,e.__scopeDialog);return(0,y.jsx)(p.C,{present:n||a.open,children:a.modal?(0,y.jsx)(T,{...o,ref:t}):(0,y.jsx)(q,{...o,ref:t})})});L.displayName=G;var T=n.forwardRef((e,t)=>{let r=C(G,e.__scopeDialog),l=n.useRef(null),i=(0,a.s)(t,r.contentRef,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,m.Eq)(e)},[]),(0,y.jsx)(M,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),q=n.forwardRef((e,t)=>{let r=C(G,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,y.jsx)(M,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,l;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(o.current||null==(l=r.triggerRef.current)||l.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var n,l;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let i=t.target;(null==(l=r.triggerRef.current)?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),M=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,u=C(G,r),p=n.useRef(null),f=(0,a.s)(t,p);return(0,g.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(d.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,y.jsx)(c.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":K(u.open),...s,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(J,{titleId:u.titleId}),(0,y.jsx)(Y,{contentRef:p,descriptionId:u.descriptionId})]})]})}),B="DialogTitle",S=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=C(B,r);return(0,y.jsx)(f.sG.h2,{id:o.titleId,...n,ref:t})});S.displayName=B;var W="DialogDescription",Z=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=C(W,r);return(0,y.jsx)(f.sG.p,{id:o.descriptionId,...n,ref:t})});Z.displayName=W;var H="DialogClose",z=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=C(H,r);return(0,y.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function K(e){return e?"open":"closed"}z.displayName=H;var U="DialogTitleWarning",[V,X]=(0,l.q)(U,{contentName:G,titleName:B,docsSlug:"dialog"}),J=e=>{let{titleId:t}=e,r=X(U),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},Y=e=>{let{contentRef:t,descriptionId:r}=e,o=X("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&n&&(document.getElementById(r)||console.warn(a))},[a,t,r]),null},$=D,Q=R,ee=_,et=A,er=L,en=S,eo=Z,ea=z},28883:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},47924:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},76981:(e,t,r)=>{r.d(t,{C1:()=>C,bL:()=>j});var n=r(12115),o=r(6101),a=r(46081),l=r(85185),i=r(5845),s=r(45503),c=r(11275),d=r(28905),u=r(63655),p=r(95155),f="Checkbox",[g,h]=(0,a.A)(f),[m,v]=g(f);function y(e){let{__scopeCheckbox:t,checked:r,children:o,defaultChecked:a,disabled:l,form:s,name:c,onCheckedChange:d,required:u,value:g="on",internal_do_not_use_render:h}=e,[v,y]=(0,i.i)({prop:r,defaultProp:null!=a&&a,onChange:d,caller:f}),[x,b]=n.useState(null),[j,k]=n.useState(null),C=n.useRef(!1),D=!x||!!s||!!x.closest("form"),w={checked:v,disabled:l,setChecked:y,control:x,setControl:b,name:c,form:s,value:g,hasConsumerStoppedPropagationRef:C,required:u,defaultChecked:!R(a)&&a,isFormControl:D,bubbleInput:j,setBubbleInput:k};return(0,p.jsx)(m,{scope:t,...w,children:"function"==typeof h?h(w):o})}var x="CheckboxTrigger",b=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,onKeyDown:a,onClick:i,...s}=e,{control:c,value:d,disabled:f,checked:g,required:h,setControl:m,setChecked:y,hasConsumerStoppedPropagationRef:b,isFormControl:j,bubbleInput:k}=v(x,r),C=(0,o.s)(t,m),D=n.useRef(g);return n.useEffect(()=>{let e=null==c?void 0:c.form;if(e){let t=()=>y(D.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[c,y]),(0,p.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":R(g)?"mixed":g,"aria-required":h,"data-state":E(g),"data-disabled":f?"":void 0,disabled:f,value:d,...s,ref:C,onKeyDown:(0,l.m)(a,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(i,e=>{y(e=>!!R(e)||!e),k&&j&&(b.current=e.isPropagationStopped(),b.current||e.stopPropagation())})})});b.displayName=x;var j=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:o,defaultChecked:a,required:l,disabled:i,value:s,onCheckedChange:c,form:d,...u}=e;return(0,p.jsx)(y,{__scopeCheckbox:r,checked:o,defaultChecked:a,disabled:i,required:l,onCheckedChange:c,name:n,form:d,value:s,internal_do_not_use_render:e=>{let{isFormControl:n}=e;return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(b,{...u,ref:t,__scopeCheckbox:r}),n&&(0,p.jsx)(w,{__scopeCheckbox:r})]})}})});j.displayName=f;var k="CheckboxIndicator",C=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...o}=e,a=v(k,r);return(0,p.jsx)(d.C,{present:n||R(a.checked)||!0===a.checked,children:(0,p.jsx)(u.sG.span,{"data-state":E(a.checked),"data-disabled":a.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});C.displayName=k;var D="CheckboxBubbleInput",w=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,...a}=e,{control:l,hasConsumerStoppedPropagationRef:i,checked:d,defaultChecked:f,required:g,disabled:h,name:m,value:y,form:x,bubbleInput:b,setBubbleInput:j}=v(D,r),k=(0,o.s)(t,j),C=(0,s.Z)(d),w=(0,c.X)(l);n.useEffect(()=>{if(!b)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!i.current;if(C!==d&&e){let r=new Event("click",{bubbles:t});b.indeterminate=R(d),e.call(b,!R(d)&&d),b.dispatchEvent(r)}},[b,C,d,i]);let E=n.useRef(!R(d)&&d);return(0,p.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=f?f:E.current,required:g,disabled:h,name:m,value:y,form:x,...a,tabIndex:-1,ref:k,style:{...a.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function R(e){return"indeterminate"===e}function E(e){return R(e)?"indeterminate":e?"checked":"unchecked"}w.displayName=D},92657:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);