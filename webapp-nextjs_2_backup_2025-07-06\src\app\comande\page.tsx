'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { getComandaColorClasses } from '@/utils/softColors'
import { Progress } from '@/components/ui/progress'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { useAuth } from '@/contexts/AuthContext'
import { comandeApi, responsabiliApi } from '@/lib/api'
import { Comanda, Responsabile } from '@/types'
import CreaComandaDialog from '@/components/comande/CreaComandaDialog'
import GestisciResponsabiliDialog from '@/components/comande/GestisciResponsabiliDialog'
import DettagliComandaDialog from '@/components/comande/DettagliComandaDialog'
import InserisciMetriDialog from '@/components/comande/InserisciMetriDialog'
import InserisciMetriPosatiDialog from '@/components/comande/InserisciMetriPosatiDialog'
import { useToast } from '@/hooks/use-toast'
import {
  ClipboardList,
  Plus,
  Users,
  Clock,
  CheckCircle,
  AlertTriangle,
  Play,
  Pause,
  Square,
  Eye,
  Edit,
  Trash2,
  Loader2,
  Search,
  Filter,
  Settings
} from 'lucide-react'

export default function ComandePage() {
  const [selectedTab, setSelectedTab] = useState('active')
  const [comande, setComande] = useState<Comanda[]>([])
  const [responsabili, setResponsabili] = useState<Responsabile[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedResponsabile, setSelectedResponsabile] = useState('all')
  const [selectedTipo, setSelectedTipo] = useState('all')
  const [showCreaComandaDialog, setShowCreaComandaDialog] = useState(false)
  const [showResponsabiliDialog, setShowResponsabiliDialog] = useState(false)
  const [showDettagliDialog, setShowDettagliDialog] = useState(false)
  const [showInserisciMetriDialog, setShowInserisciMetriDialog] = useState(false)
  const [showInserisciMetriPosatiDialog, setShowInserisciMetriPosatiDialog] = useState(false)
  const [selectedComandaCode, setSelectedComandaCode] = useState<string | null>(null)
  const [selectedComandaTipo, setSelectedComandaTipo] = useState<'POSA' | 'COLLEGAMENTO_PARTENZA' | 'COLLEGAMENTO_ARRIVO' | null>(null)

  const { user, cantiere } = useAuth()
  const { toast } = useToast()

  // Get cantiere ID con fallback al localStorage come nelle altre pagine
  const [cantiereId, setCantiereId] = useState<number>(0)

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedId = cantiere?.id_cantiere || parseInt(localStorage.getItem('selectedCantiereId') || '0')
      setCantiereId(storedId)

    }
  }, [cantiere])

  // Carica comande e responsabili dal backend
  useEffect(() => {
    if (cantiereId && cantiereId > 0) {
      loadData()
    }
  }, [cantiereId])

  const loadData = async () => {
    try {
      setIsLoading(true)
      setError('')

      if (!cantiereId || cantiereId <= 0) {
        setError('Cantiere non selezionato')
        return
      }

      const [comandeResponse, responsabiliData] = await Promise.all([
        comandeApi.getComande(cantiereId),
        responsabiliApi.getResponsabili(cantiereId)
      ])

      // Extract comande array from the response object
      const comandeData = comandeResponse?.data?.comande || comandeResponse?.comande || comandeResponse?.data || comandeResponse || []

      // Extract responsabili array from the response object
      const responsabiliArray = responsabiliData?.data || responsabiliData || []

      // Ensure we always set arrays
      setComande(Array.isArray(comandeData) ? comandeData : [])
      setResponsabili(Array.isArray(responsabiliArray) ? responsabiliArray : [])
    } catch (error: any) {
      setError(error.response?.data?.detail || 'Errore durante il caricamento dei dati')
    } finally {
      setIsLoading(false)
    }
  }

  // Funzioni per gestire i dialog
  const handleSuccess = (message: string) => {
    toast({
      title: "Successo",
      description: message,
    })
    loadData() // Ricarica i dati
  }

  const handleError = (message: string) => {
    toast({
      title: "Errore",
      description: message,
      variant: "destructive",
    })
  }

  const handleDeleteComanda = async (codiceComanda: string) => {
    if (!confirm(`Sei sicuro di voler eliminare la comanda ${codiceComanda}?`)) {
      return
    }

    try {
      setIsLoading(true)
      await comandeApi.deleteComanda(cantiereId, codiceComanda)
      handleSuccess(`Comanda ${codiceComanda} eliminata con successo`)
    } catch (error: any) {
      handleError('Errore durante l\'eliminazione della comanda')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCambiaStato = async (codiceComanda: string, nuovoStato: string) => {
    try {
      setIsLoading(true)
      await comandeApi.cambiaStato(cantiereId, codiceComanda, nuovoStato)
      handleSuccess(`Stato della comanda ${codiceComanda} cambiato in ${nuovoStato}`)
    } catch (error: any) {
      handleError('Errore durante il cambio di stato')
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusBadge = (stato: string) => {
    switch (stato) {
      case 'COMPLETATA':
        return <Badge className="bg-green-100 text-green-800">Completata</Badge>
      case 'IN_CORSO':
        return <Badge className="bg-blue-100 text-blue-800">In Corso</Badge>
      case 'ASSEGNATA':
        return <Badge className="bg-yellow-100 text-yellow-800">Assegnata</Badge>
      case 'CREATA':
        return <Badge className="bg-gray-100 text-gray-800">Creata</Badge>
      case 'ANNULLATA':
        return <Badge className="bg-red-100 text-red-800">Annullata</Badge>
      default:
        return <Badge variant="secondary">{stato}</Badge>
    }
  }

  const getTipoBadge = (tipo: string) => {
    const colorClasses = getComandaColorClasses(tipo)

    const tipoLabels: { [key: string]: string } = {
      'POSA': '🔧 Posa',
      'COLLEGAMENTO_PARTENZA': '🔌 Coll. Partenza',
      'COLLEGAMENTO_ARRIVO': '⚡ Coll. Arrivo',
      'CERTIFICAZIONE': '📋 Certificazione'
    }

    return (
      <Badge className={colorClasses.badge}>
        {tipoLabels[tipo] || tipo.replace(/_/g, ' ')}
      </Badge>
    )
  }

  const filteredComande = Array.isArray(comande) ? comande.filter(comanda => {
    // Filtro per tab
    let passesTabFilter = true
    switch (selectedTab) {
      case 'active':
        passesTabFilter = comanda.stato === 'IN_CORSO' || comanda.stato === 'ASSEGNATA' || comanda.stato === 'CREATA'
        break
      case 'completed':
        passesTabFilter = comanda.stato === 'COMPLETATA'
        break
      case 'all':
        passesTabFilter = true
        break
      default:
        passesTabFilter = true
    }

    // Filtro per ricerca
    const passesSearchFilter = searchTerm === '' ||
      comanda.codice_comanda.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (comanda.descrizione && comanda.descrizione.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (comanda.responsabile && comanda.responsabile.toLowerCase().includes(searchTerm.toLowerCase()))

    // Filtro per responsabile
    const passesResponsabileFilter = selectedResponsabile === 'all' || comanda.responsabile === selectedResponsabile

    // Filtro per tipo
    const passesTipoFilter = selectedTipo === 'all' || comanda.tipo_comanda === selectedTipo

    return passesTabFilter && passesSearchFilter && passesResponsabileFilter && passesTipoFilter
  }) : []

  const stats = {
    totali: Array.isArray(comande) ? comande.length : 0,
    in_corso: Array.isArray(comande) ? comande.filter(c => c.stato === 'IN_CORSO').length : 0,
    completate: Array.isArray(comande) ? comande.filter(c => c.stato === 'COMPLETATA').length : 0,
    pianificate: Array.isArray(comande) ? comande.filter(c => c.stato === 'CREATA' || c.stato === 'ASSEGNATA').length : 0,
    filtrate: filteredComande.length
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-[90%] mx-auto space-y-6">

        {/* Header semplice */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-slate-900 mb-2">Gestione Comande</h1>
          <p className="text-slate-600">
            {cantiereId > 0 ? `Cantiere ${typeof window !== 'undefined' ? localStorage.getItem('selectedCantiereName') || cantiereId : cantiereId}` : 'Nessun cantiere selezionato'}
          </p>
        </div>

        {/* Campo di ricerca come nella webapp originale */}
        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Cerca per codice, responsabile, tipo, stato o descrizione..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-gray-50 hover:bg-blue-50 focus:bg-white transition-colors"
            />
          </div>
        </div>

        {/* Toolbar con pulsanti come nella webapp originale */}
        <div className="flex flex-wrap gap-2 mb-6">
          <Button
            onClick={() => setShowCreaComandaDialog(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Plus className="h-4 w-4 mr-2" />
            Nuova Comanda
          </Button>
          <Button
            variant="outline"
            onClick={() => {
              // TODO: Implementare assegnazione cavi
              toast({
                title: "Funzione in sviluppo",
                description: "L'assegnazione cavi sarà disponibile presto",
              })
            }}
            disabled={filteredComande.length === 0}
          >
            <ClipboardList className="h-4 w-4 mr-2" />
            Assegna Cavi
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowResponsabiliDialog(true)}
          >
            <Users className="h-4 w-4 mr-2" />
            Gestisci Responsabili
          </Button>
        </div>

        {/* Header con conteggio comande */}
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-900">
            Elenco Comande ({filteredComande.length} di {stats.totali})
          </h3>
        </div>

        {/* Tabella comande come nella webapp originale */}
        <Card className="border border-gray-200 rounded-lg">
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50">
                  <TableHead className="font-semibold">Codice</TableHead>
                  <TableHead className="font-semibold">Tipo</TableHead>
                  <TableHead className="font-semibold">Responsabile</TableHead>
                  <TableHead className="font-semibold">Contatti</TableHead>
                  <TableHead className="font-semibold">Stato</TableHead>
                  <TableHead className="font-semibold">Data Creazione</TableHead>
                  <TableHead className="font-semibold text-center">Cavi</TableHead>
                  <TableHead className="font-semibold text-center">Completamento</TableHead>
                  <TableHead className="font-semibold text-center">Azioni</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-8">
                      <div className="flex items-center justify-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Caricamento comande...
                      </div>
                    </TableCell>
                  </TableRow>
                ) : error ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-8">
                      <div className="flex items-center justify-center gap-2 text-red-600">
                        <AlertTriangle className="h-4 w-4" />
                        {error}
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredComande.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-8 text-slate-500">
                      Nessuna comanda trovata
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredComande.map((comanda) => (
                    <TableRow key={comanda.codice_comanda} className="hover:bg-gray-50">
                      <TableCell>
                        <div className="font-semibold text-blue-600">
                          {comanda.codice_comanda}
                        </div>
                      </TableCell>
                      <TableCell>
                        {getTipoBadge(comanda.tipo_comanda)}
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">
                          {comanda.responsabile || 'Non assegnato'}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm text-gray-600">
                          {comanda.responsabile_telefono && (
                            <div>📞 {comanda.responsabile_telefono}</div>
                          )}
                          {comanda.responsabile_email && (
                            <div>✉️ {comanda.responsabile_email}</div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(comanda.stato)}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {new Date(comanda.data_creazione).toLocaleDateString('it-IT')}
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="font-semibold text-blue-600">
                          {comanda.numero_cavi_assegnati || 0}
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="font-semibold">
                          {(comanda.percentuale_completamento || 0).toFixed(1)}%
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="flex gap-1 justify-center">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedComandaCode(comanda.codice_comanda)
                              setShowDettagliDialog(true)
                            }}
                            title="Visualizza"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              // TODO: Implementare modifica comanda
                              toast({
                                title: "Funzione in sviluppo",
                                description: "La modifica comande sarà disponibile presto",
                              })
                            }}
                            title="Modifica"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          {['POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO'].includes(comanda.tipo_comanda) && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setSelectedComandaCode(comanda.codice_comanda)
                                setSelectedComandaTipo(comanda.tipo_comanda as 'POSA' | 'COLLEGAMENTO_PARTENZA' | 'COLLEGAMENTO_ARRIVO')

                                // Apri il dialog corretto in base al tipo di comanda
                                if (comanda.tipo_comanda === 'POSA') {
                                  setShowInserisciMetriPosatiDialog(true)
                                } else {
                                  setShowInserisciMetriDialog(true)
                                }
                              }}
                              title={comanda.tipo_comanda === 'POSA' ? 'Inserisci Metri Posati' : 'Inserisci Metri Collegati'}
                            >
                              <ClipboardList className="h-4 w-4" />
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteComanda(comanda.codice_comanda)}
                            disabled={isLoading}
                            className="text-red-600 hover:text-red-700"
                            title="Elimina"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

      </div>

      {/* Dialog per creare comanda */}
      <CreaComandaDialog
        open={showCreaComandaDialog}
        onClose={() => setShowCreaComandaDialog(false)}
        onSuccess={handleSuccess}
        onError={handleError}
        onComandaCreated={() => loadData()}
      />

      {/* Dialog per gestire responsabili */}
      <GestisciResponsabiliDialog
        open={showResponsabiliDialog}
        onClose={() => setShowResponsabiliDialog(false)}
        onSuccess={handleSuccess}
        onError={handleError}
      />

      {/* Dialog per dettagli comanda */}
      <DettagliComandaDialog
        open={showDettagliDialog}
        onClose={() => {
          setShowDettagliDialog(false)
          setSelectedComandaCode(null)
        }}
        codiceComanda={selectedComandaCode}
        onSuccess={handleSuccess}
        onError={handleError}
      />

      <InserisciMetriDialog
        open={showInserisciMetriDialog}
        onClose={() => {
          setShowInserisciMetriDialog(false)
          setSelectedComandaCode(null)
          setSelectedComandaTipo(null)
        }}
        codiceComanda={selectedComandaCode || ''}
        tipoComanda={selectedComandaTipo || 'POSA'}
        onSuccess={(message) => {
          handleSuccess(message)
          loadComande() // Ricarica le comande per aggiornare i dati
        }}
        onError={handleError}
      />

      <InserisciMetriPosatiDialog
        open={showInserisciMetriPosatiDialog}
        onClose={() => {
          setShowInserisciMetriPosatiDialog(false)
          setSelectedComandaCode(null)
          setSelectedComandaTipo(null)
        }}
        codiceComanda={selectedComandaCode || ''}
        onSuccess={(message) => {
          handleSuccess(message)
          loadComande() // Ricarica le comande per aggiornare i dati
        }}
        onError={handleError}
      />
    </div>
  )
}
