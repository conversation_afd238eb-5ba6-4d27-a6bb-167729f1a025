"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[168],{381:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},4229:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},15452:(e,t,a)=>{a.d(t,{G$:()=>J,Hs:()=>b,UC:()=>ea,VY:()=>er,ZL:()=>ee,bL:()=>Q,bm:()=>el,hE:()=>en,hJ:()=>et,l9:()=>X});var n=a(12115),r=a(85185),l=a(6101),o=a(46081),i=a(61285),s=a(5845),d=a(19178),u=a(25519),c=a(34378),p=a(28905),f=a(63655),h=a(92293),g=a(93795),v=a(38168),y=a(99708),m=a(95155),x="Dialog",[D,b]=(0,o.A)(x),[k,j]=D(x),A=e=>{let{__scopeDialog:t,children:a,open:r,defaultOpen:l,onOpenChange:o,modal:d=!0}=e,u=n.useRef(null),c=n.useRef(null),[p,f]=(0,s.i)({prop:r,defaultProp:null!=l&&l,onChange:o,caller:x});return(0,m.jsx)(k,{scope:t,triggerRef:u,contentRef:c,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:d,children:a})};A.displayName=x;var C="DialogTrigger",R=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,o=j(C,a),i=(0,l.s)(t,o.triggerRef);return(0,m.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":S(o.open),...n,ref:i,onClick:(0,r.m)(e.onClick,o.onOpenToggle)})});R.displayName=C;var M="DialogPortal",[w,I]=D(M,{forceMount:void 0}),N=e=>{let{__scopeDialog:t,forceMount:a,children:r,container:l}=e,o=j(M,t);return(0,m.jsx)(w,{scope:t,forceMount:a,children:n.Children.map(r,e=>(0,m.jsx)(p.C,{present:a||o.open,children:(0,m.jsx)(c.Z,{asChild:!0,container:l,children:e})}))})};N.displayName=M;var O="DialogOverlay",_=n.forwardRef((e,t)=>{let a=I(O,e.__scopeDialog),{forceMount:n=a.forceMount,...r}=e,l=j(O,e.__scopeDialog);return l.modal?(0,m.jsx)(p.C,{present:n||l.open,children:(0,m.jsx)(F,{...r,ref:t})}):null});_.displayName=O;var E=(0,y.TL)("DialogOverlay.RemoveScroll"),F=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,r=j(O,a);return(0,m.jsx)(g.A,{as:E,allowPinchZoom:!0,shards:[r.contentRef],children:(0,m.jsx)(f.sG.div,{"data-state":S(r.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),q="DialogContent",H=n.forwardRef((e,t)=>{let a=I(q,e.__scopeDialog),{forceMount:n=a.forceMount,...r}=e,l=j(q,e.__scopeDialog);return(0,m.jsx)(p.C,{present:n||l.open,children:l.modal?(0,m.jsx)(P,{...r,ref:t}):(0,m.jsx)(V,{...r,ref:t})})});H.displayName=q;var P=n.forwardRef((e,t)=>{let a=j(q,e.__scopeDialog),o=n.useRef(null),i=(0,l.s)(t,a.contentRef,o);return n.useEffect(()=>{let e=o.current;if(e)return(0,v.Eq)(e)},[]),(0,m.jsx)(z,{...e,ref:i,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,r.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=a.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,r.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,a=0===t.button&&!0===t.ctrlKey;(2===t.button||a)&&e.preventDefault()}),onFocusOutside:(0,r.m)(e.onFocusOutside,e=>e.preventDefault())})}),V=n.forwardRef((e,t)=>{let a=j(q,e.__scopeDialog),r=n.useRef(!1),l=n.useRef(!1);return(0,m.jsx)(z,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,o;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(r.current||null==(o=a.triggerRef.current)||o.focus(),t.preventDefault()),r.current=!1,l.current=!1},onInteractOutside:t=>{var n,o;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(l.current=!0));let i=t.target;(null==(o=a.triggerRef.current)?void 0:o.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&l.current&&t.preventDefault()}})}),z=n.forwardRef((e,t)=>{let{__scopeDialog:a,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:i,...s}=e,c=j(q,a),p=n.useRef(null),f=(0,l.s)(t,p);return(0,h.Oh)(),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(u.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:i,children:(0,m.jsx)(d.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":S(c.open),...s,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(Y,{titleId:c.titleId}),(0,m.jsx)($,{contentRef:p,descriptionId:c.descriptionId})]})]})}),G="DialogTitle",T=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,r=j(G,a);return(0,m.jsx)(f.sG.h2,{id:r.titleId,...n,ref:t})});T.displayName=G;var B="DialogDescription",Z=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,r=j(B,a);return(0,m.jsx)(f.sG.p,{id:r.descriptionId,...n,ref:t})});Z.displayName=B;var W="DialogClose",L=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,l=j(W,a);return(0,m.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,r.m)(e.onClick,()=>l.onOpenChange(!1))})});function S(e){return e?"open":"closed"}L.displayName=W;var U="DialogTitleWarning",[J,K]=(0,o.q)(U,{contentName:q,titleName:G,docsSlug:"dialog"}),Y=e=>{let{titleId:t}=e,a=K(U),r="`".concat(a.contentName,"` requires a `").concat(a.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(a.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(a.docsSlug);return n.useEffect(()=>{t&&(document.getElementById(t)||console.error(r))},[r,t]),null},$=e=>{let{contentRef:t,descriptionId:a}=e,r=K("DialogDescriptionWarning"),l="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return n.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");a&&n&&(document.getElementById(a)||console.warn(l))},[l,t,a]),null},Q=A,X=R,ee=N,et=_,ea=H,en=T,er=Z,el=L},38164:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},50589:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("cloud",[["path",{d:"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z",key:"p7xjir"}]])},57434:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},71539:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},75021:(e,t,a)=>{a.d(t,{A:()=>n});let n=(0,a(19946).A)("test-tube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5c-1.4 0-2.5-1.1-2.5-2.5V2",key:"125lnx"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]])}}]);