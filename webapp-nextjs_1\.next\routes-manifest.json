{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/api/cantieri/[cantiereId]", "regex": "^/api/cantieri/([^/]+?)(?:/)?$", "routeKeys": {"nxtPcantiereId": "nxtPcantiereId"}, "namedRegex": "^/api/cantieri/(?<nxtPcantiereId>[^/]+?)(?:/)?$"}, {"page": "/api/cantieri/[cantiereId]/certificazioni", "regex": "^/api/cantieri/([^/]+?)/certificazioni(?:/)?$", "routeKeys": {"nxtPcantiereId": "nxtPcantiereId"}, "namedRegex": "^/api/cantieri/(?<nxtPcantiereId>[^/]+?)/certificazioni(?:/)?$"}, {"page": "/api/cantieri/[cantiereId]/certificazioni/[certificazioneId]", "regex": "^/api/cantieri/([^/]+?)/certificazioni/([^/]+?)(?:/)?$", "routeKeys": {"nxtPcantiereId": "nxtPcantiereId", "nxtPcertificazioneId": "nxtPcertificazioneId"}, "namedRegex": "^/api/cantieri/(?<nxtPcantiereId>[^/]+?)/certificazioni/(?<nxtPcertificazioneId>[^/]+?)(?:/)?$"}, {"page": "/api/cantieri/[cantiereId]/statistics", "regex": "^/api/cantieri/([^/]+?)/statistics(?:/)?$", "routeKeys": {"nxtPcantiereId": "nxtPcantiereId"}, "namedRegex": "^/api/cantieri/(?<nxtPcantiereId>[^/]+?)/statistics(?:/)?$"}, {"page": "/api/cantieri/[cantiereId]/strumenti", "regex": "^/api/cantieri/([^/]+?)/strumenti(?:/)?$", "routeKeys": {"nxtPcantiereId": "nxtPcantiereId"}, "namedRegex": "^/api/cantieri/(?<nxtPcantiereId>[^/]+?)/strumenti(?:/)?$"}, {"page": "/api/cantieri/[cantiereId]/weather", "regex": "^/api/cantieri/([^/]+?)/weather(?:/)?$", "routeKeys": {"nxtPcantiereId": "nxtPcantiereId"}, "namedRegex": "^/api/cantieri/(?<nxtPcantiereId>[^/]+?)/weather(?:/)?$"}, {"page": "/api/cavi/[cantiereId]", "regex": "^/api/cavi/([^/]+?)(?:/)?$", "routeKeys": {"nxtPcantiereId": "nxtPcantiereId"}, "namedRegex": "^/api/cavi/(?<nxtPcantiereId>[^/]+?)(?:/)?$"}, {"page": "/api/cavi/[cantiereId]/[cavoId]/collegamento", "regex": "^/api/cavi/([^/]+?)/([^/]+?)/collegamento(?:/)?$", "routeKeys": {"nxtPcantiereId": "nxtPcantiereId", "nxtPcavoId": "nxtPcavoId"}, "namedRegex": "^/api/cavi/(?<nxtPcantiereId>[^/]+?)/(?<nxtPcavoId>[^/]+?)/collegamento(?:/)?$"}, {"page": "/api/parco-cavi/[cantiereId]", "regex": "^/api/parco\\-cavi/([^/]+?)(?:/)?$", "routeKeys": {"nxtPcantiereId": "nxtPcantiereId"}, "namedRegex": "^/api/parco\\-cavi/(?<nxtPcantiereId>[^/]+?)(?:/)?$"}, {"page": "/api/responsabili/cantiere/[cantiereId]", "regex": "^/api/responsabili/cantiere/([^/]+?)(?:/)?$", "routeKeys": {"nxtPcantiereId": "nxtPcantiereId"}, "namedRegex": "^/api/responsabili/cantiere/(?<nxtPcantiereId>[^/]+?)(?:/)?$"}, {"page": "/api/users/toggle/[id]", "regex": "^/api/users/toggle/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/users/toggle/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/users/[id]", "regex": "^/api/users/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/users/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/cantieri/[id]", "regex": "^/cantieri/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/cantieri/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/admin", "regex": "^/admin(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin(?:/)?$"}, {"page": "/cantieri", "regex": "^/cantieri(?:/)?$", "routeKeys": {}, "namedRegex": "^/cantieri(?:/)?$"}, {"page": "/cavi", "regex": "^/cavi(?:/)?$", "routeKeys": {}, "namedRegex": "^/cavi(?:/)?$"}, {"page": "/certificazioni", "regex": "^/certificazioni(?:/)?$", "routeKeys": {}, "namedRegex": "^/certificazioni(?:/)?$"}, {"page": "/change-password", "regex": "^/change\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/change\\-password(?:/)?$"}, {"page": "/comande", "regex": "^/comande(?:/)?$", "routeKeys": {}, "namedRegex": "^/comande(?:/)?$"}, {"page": "/debug-login", "regex": "^/debug\\-login(?:/)?$", "routeKeys": {}, "namedRegex": "^/debug\\-login(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/forgot-password", "regex": "^/forgot\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/forgot\\-password(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/parco-cavi", "regex": "^/parco\\-cavi(?:/)?$", "routeKeys": {}, "namedRegex": "^/parco\\-cavi(?:/)?$"}, {"page": "/productivity", "regex": "^/productivity(?:/)?$", "routeKeys": {}, "namedRegex": "^/productivity(?:/)?$"}, {"page": "/reports", "regex": "^/reports(?:/)?$", "routeKeys": {}, "namedRegex": "^/reports(?:/)?$"}, {"page": "/reset-password", "regex": "^/reset\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/reset\\-password(?:/)?$"}, {"page": "/simple-login", "regex": "^/simple\\-login(?:/)?$", "routeKeys": {}, "namedRegex": "^/simple\\-login(?:/)?$"}, {"page": "/test-certificazioni", "regex": "^/test\\-certificazioni(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-certificazioni(?:/)?$"}, {"page": "/test-form-certificazione", "regex": "^/test\\-form\\-certificazione(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-form\\-certificazione(?:/)?$"}, {"page": "/test-login", "regex": "^/test\\-login(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-login(?:/)?$"}, {"page": "/test-unified-modal", "regex": "^/test\\-unified\\-modal(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-unified\\-modal(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}