'use client'

import { useState, useEffect } from 'react'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Button } from '@/components/ui/button'
import { Loader2, AlertTriangle, CheckCircle, XCircle, Info } from 'lucide-react'

export type ConfirmationVariant = 'danger' | 'warning' | 'info' | 'success'

interface ConfirmationDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void | Promise<void>
  title: string
  description: string
  confirmText?: string
  cancelText?: string
  variant?: ConfirmationVariant
  isLoading?: boolean
  icon?: React.ReactNode
}

const variantConfig = {
  danger: {
    icon: XCircle,
    iconColor: 'text-red-600',
    buttonClass: 'bg-red-600 hover:bg-red-700 focus:ring-red-500',
    borderClass: 'border-l-red-500'
  },
  warning: {
    icon: AlertTriangle,
    iconColor: 'text-orange-600',
    buttonClass: 'bg-orange-600 hover:bg-orange-700 focus:ring-orange-500',
    borderClass: 'border-l-orange-500'
  },
  info: {
    icon: Info,
    iconColor: 'text-blue-600',
    buttonClass: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500',
    borderClass: 'border-l-blue-500'
  },
  success: {
    icon: CheckCircle,
    iconColor: 'text-green-600',
    buttonClass: 'bg-green-600 hover:bg-green-700 focus:ring-green-500',
    borderClass: 'border-l-green-500'
  }
}

export default function ConfirmationDialog({
  isOpen,
  onClose,
  onConfirm,
  title,
  description,
  confirmText = 'Conferma',
  cancelText = 'Annulla',
  variant = 'info',
  isLoading = false,
  icon
}: ConfirmationDialogProps) {
  const [internalLoading, setInternalLoading] = useState(false)
  const config = variantConfig[variant]
  const IconComponent = icon || config.icon

  // Gestione ESC key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen && !isLoading && !internalLoading) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      // Blocca lo scroll del body quando il modal è aperto
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, isLoading, internalLoading, onClose])

  const handleConfirm = async () => {
    try {
      setInternalLoading(true)
      const result = onConfirm()
      
      // Se onConfirm restituisce una Promise, aspettiamo
      if (result instanceof Promise) {
        await result
      }
      
      // Piccolo delay per mostrare il feedback
      await new Promise(resolve => setTimeout(resolve, 300))
    } catch (error) {
      console.error('Errore durante conferma:', error)
      // In caso di errore, non chiudiamo il dialog per permettere all'utente di riprovare
      setInternalLoading(false)
      return
    } finally {
      setInternalLoading(false)
    }
  }

  const loading = isLoading || internalLoading

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent className={`border-l-4 ${config.borderClass} max-w-md`}>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center space-x-3">
            <IconComponent className={`w-6 h-6 ${config.iconColor} flex-shrink-0`} />
            <span className="text-lg font-semibold text-slate-900">{title}</span>
          </AlertDialogTitle>
          <AlertDialogDescription className="text-base text-slate-600 leading-relaxed mt-2">
            {description}
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <AlertDialogFooter className="gap-3 mt-6">
          <AlertDialogCancel 
            onClick={onClose}
            disabled={loading}
            className="px-6 py-2 border-slate-300 text-slate-700 hover:bg-slate-50 focus:ring-slate-500"
          >
            {cancelText}
          </AlertDialogCancel>
          
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={loading}
            className={`px-6 py-2 text-white font-medium transition-all duration-200 ${config.buttonClass} disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            {loading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Elaborazione...
              </>
            ) : (
              confirmText
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

// Hook per gestire facilmente i dialog di conferma
export function useConfirmationDialog() {
  const [dialogState, setDialogState] = useState<{
    isOpen: boolean
    title: string
    description: string
    confirmText?: string
    cancelText?: string
    variant?: ConfirmationVariant
    onConfirm?: () => void | Promise<void>
    icon?: React.ReactNode
  }>({
    isOpen: false,
    title: '',
    description: ''
  })

  const showConfirmation = (config: Omit<typeof dialogState, 'isOpen'>) => {
    setDialogState({
      ...config,
      isOpen: true
    })
  }

  const hideConfirmation = () => {
    setDialogState(prev => ({ ...prev, isOpen: false }))
  }

  const handleConfirm = async () => {
    if (dialogState.onConfirm) {
      await dialogState.onConfirm()
    }
    hideConfirmation()
  }

  return {
    dialogState,
    showConfirmation,
    hideConfirmation,
    handleConfirm,
    ConfirmationDialog: () => (
      <ConfirmationDialog
        isOpen={dialogState.isOpen}
        onClose={hideConfirmation}
        onConfirm={handleConfirm}
        title={dialogState.title}
        description={dialogState.description}
        confirmText={dialogState.confirmText}
        cancelText={dialogState.cancelText}
        variant={dialogState.variant}
        icon={dialogState.icon}
      />
    )
  }
}
