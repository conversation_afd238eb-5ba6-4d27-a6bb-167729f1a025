(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4700],{4366:(e,s,r)=>{Promise.resolve().then(r.bind(r,67712))},24944:(e,s,r)=>{"use strict";r.d(s,{k:()=>n});var a=r(95155);r(12115);var t=r(55863),i=r(59434);function n(e){let{className:s,value:r,...n}=e;return(0,a.jsx)(t.bL,{"data-slot":"progress",className:(0,i.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",s),...n,children:(0,a.jsx)(t.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(r||0),"%)")}})})}},30285:(e,s,r)=>{"use strict";r.d(s,{$:()=>o,r:()=>d});var a=r(95155);r(12115);var t=r(99708),i=r(74466),n=r(59434);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:s,variant:r,size:i,asChild:o=!1,...l}=e,c=o?t.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,n.cn)(d({variant:r,size:i,className:s})),...l})}},32919:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(19946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},35695:(e,s,r)=>{"use strict";var a=r(18999);r.o(a,"useParams")&&r.d(s,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(s,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(s,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(s,{useSearchParams:function(){return a.useSearchParams}})},54861:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(19946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},55365:(e,s,r)=>{"use strict";r.d(s,{Fc:()=>o,TN:()=>l});var a=r(95155),t=r(12115),i=r(74466),n=r(59434);let d=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=t.forwardRef((e,s)=>{let{className:r,variant:t,...i}=e;return(0,a.jsx)("div",{ref:s,role:"alert",className:(0,n.cn)(d({variant:t}),r),...i})});o.displayName="Alert",t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("h5",{ref:s,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",r),...t})}).displayName="AlertTitle";let l=t.forwardRef((e,s)=>{let{className:r,...t}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",r),...t})});l.displayName="AlertDescription"},59434:(e,s,r)=>{"use strict";r.d(s,{cn:()=>i});var a=r(52596),t=r(39688);function i(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,t.QP)((0,a.$)(s))}},62523:(e,s,r)=>{"use strict";r.d(s,{p:()=>i});var a=r(95155);r(12115);var t=r(59434);function i(e){let{className:s,type:r,...i}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,t.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...i})}},66695:(e,s,r)=>{"use strict";r.d(s,{BT:()=>o,Wu:()=>l,ZB:()=>d,Zp:()=>i,aR:()=>n});var a=r(95155);r(12115);var t=r(59434);function i(e){let{className:s,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,t.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...r})}function n(e){let{className:s,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,t.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...r})}function d(e){let{className:s,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,t.cn)("leading-none font-semibold",s),...r})}function o(e){let{className:s,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,t.cn)("text-muted-foreground text-sm",s),...r})}function l(e){let{className:s,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,t.cn)("px-6",s),...r})}},67712:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>P});var a=r(95155),t=r(12115),i=r(6874),n=r.n(i),d=r(3493),o=r(35169),l=r(30285),c=r(35695),u=r(62523),m=r(85057),x=r(66695),h=r(55365),p=r(24944),f=r(54861),g=r(32919),v=r(78749),w=r(92657),b=r(40646),j=r(1243),y=r(75525),N=r(59434);function k(){var e;let s=(0,c.useRouter)(),r=(0,c.useSearchParams)().get("token"),[i,n]=(0,t.useState)({newPassword:"",confirmPassword:""}),[d,o]=(0,t.useState)({new:!1,confirm:!1}),[k,P]=(0,t.useState)({score:0,feedback:[],isValid:!1}),[A,z]=(0,t.useState)(!1),[S,R]=(0,t.useState)(null),[T,_]=(0,t.useState)(null);(0,t.useEffect)(()=>{if(!r){R({type:"error",text:"Token di reset mancante o non valido"}),_(!1);return}_(!0)},[r]);let C=async e=>{if(!e)return void P({score:0,feedback:[],isValid:!1});try{let s=await fetch("/api/password/validate-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({password:e})});if(s.ok){let e=await s.json();P({score:e.strength_score,feedback:e.suggestions||[],isValid:e.is_valid})}}catch(e){}},E=(e,s)=>{n(r=>({...r,[e]:s})),"newPassword"===e&&C(s)},F=async e=>{e.preventDefault(),z(!0),R(null);try{if(!i.newPassword||!i.confirmPassword)throw Error("Tutti i campi sono obbligatori");if(i.newPassword!==i.confirmPassword)throw Error("Le password non corrispondono");if(!k.isValid)throw Error("La password non rispetta i requisiti di sicurezza");if(!r)throw Error("Token di reset non valido");let e=await fetch("/api/password/confirm-password-reset",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({token:r,new_password:i.newPassword,confirm_password:i.confirmPassword})}),a=await e.json();if(e.ok&&a.success)R({type:"success",text:a.message}),setTimeout(()=>{s.push("/login")},3e3);else throw Error(a.detail||a.message||"Errore durante il reset della password")}catch(e){R({type:"error",text:e instanceof Error?e.message:"Errore durante il reset della password"})}finally{z(!1)}},V=e=>{o(s=>({...s,[e]:!s[e]}))};return!1===T?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,a.jsxs)(x.Zp,{className:"w-full max-w-md mx-auto",children:[(0,a.jsxs)(x.aR,{className:"text-center",children:[(0,a.jsxs)(x.ZB,{className:"text-2xl flex items-center justify-center gap-2 text-red-600",children:[(0,a.jsx)(f.A,{className:"h-6 w-6"}),"Token Non Valido"]}),(0,a.jsx)(x.BT,{children:"Il link di reset password non \xe8 valido o \xe8 scaduto"})]}),(0,a.jsxs)(x.Wu,{className:"text-center",children:[(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Il token potrebbe essere scaduto o gi\xe0 utilizzato. Richiedi un nuovo link di reset password."}),(0,a.jsx)(l.$,{onClick:()=>s.push("/forgot-password"),className:"bg-mariner-600 hover:bg-mariner-700",children:"Richiedi Nuovo Reset"})]})]})}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,a.jsxs)(x.Zp,{className:"w-full max-w-md mx-auto",children:[(0,a.jsxs)(x.aR,{className:"space-y-1",children:[(0,a.jsxs)(x.ZB,{className:"text-2xl flex items-center gap-2",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 text-mariner-600"}),"Reimposta Password"]}),(0,a.jsx)(x.BT,{children:"Inserisci la tua nuova password sicura"})]}),(0,a.jsxs)(x.Wu,{children:[(0,a.jsxs)("form",{onSubmit:F,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(m.J,{htmlFor:"newPassword",children:"Nuova Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.p,{id:"newPassword",type:d.new?"text":"password",value:i.newPassword,onChange:e=>E("newPassword",e.target.value),className:"pr-10",required:!0}),(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>V("new"),children:d.new?(0,a.jsx)(v.A,{className:"h-4 w-4 text-gray-400"}):(0,a.jsx)(w.A,{className:"h-4 w-4 text-gray-400"})})]}),i.newPassword&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{children:"Forza password:"}),(0,a.jsx)("span",{className:(0,N.cn)("font-medium",k.score<2?"text-red-600":k.score<4?"text-yellow-600":"text-green-600"),children:(e=k.score)<2?"Debole":e<4?"Media":"Forte"})]}),(0,a.jsx)(p.k,{value:k.score/5*100,className:"h-2"}),k.feedback.length>0&&(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"Suggerimenti:"}),(0,a.jsx)("ul",{className:"list-disc list-inside space-y-1",children:k.feedback.map((e,s)=>(0,a.jsx)("li",{children:e},s))})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(m.J,{htmlFor:"confirmPassword",children:"Conferma Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.p,{id:"confirmPassword",type:d.confirm?"text":"password",value:i.confirmPassword,onChange:e=>E("confirmPassword",e.target.value),className:"pr-10",required:!0}),(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>V("confirm"),children:d.confirm?(0,a.jsx)(v.A,{className:"h-4 w-4 text-gray-400"}):(0,a.jsx)(w.A,{className:"h-4 w-4 text-gray-400"})})]}),i.confirmPassword&&(0,a.jsx)("div",{className:"flex items-center gap-2 text-sm",children:i.newPassword===i.confirmPassword?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b.A,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)("span",{className:"text-green-600",children:"Le password corrispondono"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{className:"h-4 w-4 text-red-600"}),(0,a.jsx)("span",{className:"text-red-600",children:"Le password non corrispondono"})]})})]}),S&&(0,a.jsxs)(h.Fc,{className:(0,N.cn)("success"===S.type?"border-green-200 bg-green-50":"border-red-200 bg-red-50"),children:["success"===S.type?(0,a.jsx)(b.A,{className:"h-4 w-4 text-green-600"}):(0,a.jsx)(j.A,{className:"h-4 w-4 text-red-600"}),(0,a.jsxs)(h.TN,{className:(0,N.cn)("success"===S.type?"text-green-800":"text-red-800"),children:[S.text,"success"===S.type&&(0,a.jsx)("div",{className:"mt-2 text-sm",children:"Verrai reindirizzato al login tra pochi secondi..."})]})]}),(0,a.jsx)(l.$,{type:"submit",className:"w-full bg-mariner-600 hover:bg-mariner-700",disabled:A||!k.isValid||i.newPassword!==i.confirmPassword,children:A?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Aggiornamento..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Reimposta Password"]})})]}),(0,a.jsx)("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)(y.A,{className:"h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"Sicurezza"}),(0,a.jsxs)("ul",{className:"space-y-1 text-xs",children:[(0,a.jsx)("li",{children:"• Usa una password unica che non hai mai utilizzato prima"}),(0,a.jsx)("li",{children:"• Combina lettere maiuscole, minuscole, numeri e simboli"}),(0,a.jsx)("li",{children:"• Evita informazioni personali facilmente indovinabili"}),(0,a.jsx)("li",{children:"• Considera l'uso di un gestore di password"})]})]})]})})]})]})})}function P(){return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-slate-100 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md space-y-6",children:[(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center",children:(0,a.jsx)(d.A,{className:"w-8 h-8 text-white"})})}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-slate-900",children:"CABLYS"}),(0,a.jsx)("p",{className:"text-slate-600",children:"Reimposta Password"})]}),(0,a.jsx)(t.Suspense,{fallback:(0,a.jsx)("div",{className:"text-center",children:"Caricamento..."}),children:(0,a.jsx)(k,{})}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)(n(),{href:"/login",children:(0,a.jsxs)(l.$,{variant:"ghost",className:"text-slate-600 hover:text-slate-900",children:[(0,a.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"Torna al Login"]})})})]})})}},78749:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},85057:(e,s,r)=>{"use strict";r.d(s,{J:()=>n});var a=r(95155);r(12115);var t=r(40968),i=r(59434);function n(e){let{className:s,...r}=e;return(0,a.jsx)(t.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...r})}},92657:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[3455,416,8441,1684,7358],()=>s(4366)),_N_E=e.O()}]);