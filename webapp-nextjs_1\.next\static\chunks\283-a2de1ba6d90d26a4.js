"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[283],{25731:(e,t,a)=>{a.d(t,{AR:()=>d,At:()=>l,CV:()=>p,Fw:()=>s,ZQ:()=>r,_I:()=>h,dG:()=>S,km:()=>u,kw:()=>g,l9:()=>m,mg:()=>C,om:()=>v,ug:()=>_});let o=a(23464).A.create({baseURL:"http://localhost:3000",timeout:3e4,headers:{"Content-Type":"application/json"}}),i=()=>localStorage.getItem("token")||localStorage.getItem("access_token"),n=()=>{["token","access_token","user_data","cantiere_data","selectedCantiereId","selectedCantiereName","isImpersonating","impersonatedUser"].forEach(e=>{localStorage.removeItem(e)})};o.interceptors.request.use(e=>{let t=i();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),o.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&(console.log("\uD83D\uDEA8 API: Token non valido o scaduto, pulizia dati e reindirizzamento"),n(),window.location.pathname.includes("/login")||(window.location.href="/login")),Promise.reject(e)});let c={get:async(e,t)=>(await o.get(e,t)).data,post:async(e,t,a)=>(await o.post(e,t,a)).data,put:async(e,t,a)=>(await o.put(e,t,a)).data,delete:async(e,t)=>(await o.delete(e,t)).data},r={login:async e=>(await o.post("/api/auth/login",{username:e.username,password:e.password})).data,loginCantiere:e=>c.post("/api/auth/login/cantiere",{codice_univoco:e.codice_cantiere,password:e.password_cantiere}),verifyToken:()=>c.post("/api/auth/test-token"),logout:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),window.location.href="/login"}},l={getCavi:(e,t)=>c.get("/api/cavi/".concat(e),{params:t}),getCavo:(e,t)=>c.get("/api/cavi/".concat(e,"/").concat(t)),checkCavo:(e,t)=>c.get("/api/cavi/".concat(e,"/check/").concat(t)),createCavo:(e,t)=>c.post("/api/cavi/".concat(e),t),updateCavo:(e,t,a)=>c.put("/api/cavi/".concat(e,"/").concat(t),a),deleteCavo:(e,t,a)=>c.delete("/api/cavi/".concat(e,"/").concat(t),{data:a}),updateMetriPosati:(e,t,a,o,i)=>c.post("/api/cavi/".concat(e,"/").concat(t,"/metri-posati"),{metri_posati:a,id_bobina:o,force_over:i||!1}),updateBobina:(e,t,a,o)=>c.post("/api/cavi/".concat(e,"/").concat(t,"/bobina"),{id_bobina:a,force_over:o||!1}),cancelInstallation:(e,t)=>c.post("/api/cavi/".concat(e,"/").concat(t,"/cancel-installation")),collegaCavo:(e,t,a,o)=>c.post("/api/cavi/".concat(e,"/").concat(t,"/collegamento"),{lato:a,responsabile:o}),scollegaCavo:(e,t,a)=>{let o={};return a&&(o.data={lato:a}),c.delete("/api/cavi/".concat(e,"/").concat(t,"/collegamento"),o)},markAsSpare:function(e,t,a){let o=!(arguments.length>3)||void 0===arguments[3]||arguments[3];return a?c.post("/api/cavi/".concat(e,"/").concat(t,"/mark-as-spare"),{force:o}):c.post("/api/cavi/".concat(e,"/").concat(t,"/reactivate-spare"),{})},debugCavi:e=>c.get("/api/cavi/debug/".concat(e)),debugCaviRaw:e=>c.get("/api/cavi/debug/raw/".concat(e))},s={getBobine:(e,t)=>c.get("/api/parco-cavi/".concat(e),{params:t}),getBobina:(e,t)=>c.get("/api/parco-cavi/".concat(e,"/").concat(t)),getBobineCompatibili:(e,t)=>c.get("/api/parco-cavi/".concat(e,"/compatibili"),{params:t}),createBobina:(e,t)=>c.post("/api/parco-cavi/".concat(e),t),updateBobina:(e,t,a)=>c.put("/api/parco-cavi/".concat(e,"/").concat(t),a),deleteBobina:(e,t)=>c.delete("/api/parco-cavi/".concat(e,"/").concat(t)),isFirstBobinaInsertion:e=>c.get("/api/parco-cavi/".concat(e,"/is-first-insertion")),updateBobina:(e,t,a)=>c.put("/api/parco-cavi/".concat(e,"/").concat(t),a),deleteBobina:(e,t)=>c.delete("/api/parco-cavi/".concat(e,"/").concat(t)),checkDisponibilita:(e,t,a)=>c.get("/api/parco-cavi/".concat(e,"/").concat(t,"/disponibilita"),{params:{metri_richiesti:a}})},p={getComande:e=>c.get("/api/comande/cantiere/".concat(e)),getComanda:(e,t)=>c.get("/api/comande/".concat(t)),getCaviComanda:e=>c.get("/api/comande/".concat(e,"/cavi")),createComanda:(e,t)=>c.post("/api/comande/cantiere/".concat(e),t),createComandaWithCavi:(e,t,a)=>c.post("/api/comande/cantiere/".concat(e,"/crea-con-cavi"),t,{params:{lista_id_cavi:a}}),updateDatiComanda:(e,t,a)=>c.put("/api/comande/".concat(e,"/").concat(t),a),updateComanda:(e,t,a)=>c.put("/api/comande/cantiere/".concat(e,"/").concat(t),a),deleteComanda:(e,t)=>c.delete("/api/comande/cantiere/".concat(e,"/").concat(t)),assegnaCavi:(e,t,a)=>c.post("/api/comande/cantiere/".concat(e,"/").concat(t,"/assegna-cavi"),{cavi_ids:a}),rimuoviCavi:(e,t,a)=>c.delete("/api/comande/cantiere/".concat(e,"/").concat(t,"/rimuovi-cavi"),{data:{cavi_ids:a}}),getStatistiche:e=>c.get("/api/comande/cantiere/".concat(e,"/statistiche")),cambiaStato:(e,t,a)=>c.put("/api/comande/cantiere/".concat(e,"/").concat(t,"/stato"),{nuovo_stato:a})},d={getResponsabili:e=>c.get("/api/responsabili/cantiere/".concat(e)),createResponsabile:(e,t)=>c.post("/api/responsabili/".concat(e),t),updateResponsabile:(e,t,a)=>c.put("/api/responsabili/".concat(e,"/").concat(t),a),deleteResponsabile:(e,t)=>c.delete("/api/responsabili/".concat(e,"/").concat(t))},u={getCertificazioni:(e,t)=>c.get("/api/cantieri/".concat(e,"/certificazioni"),{params:t?{filtro_cavo:t}:{}}),createCertificazione:(e,t)=>c.post("/api/cantieri/".concat(e,"/certificazioni"),t),getCertificazione:(e,t)=>c.get("/api/cantieri/".concat(e,"/certificazioni/").concat(t)),updateCertificazione:(e,t,a)=>c.put("/api/cantieri/".concat(e,"/certificazioni/").concat(t),a),deleteCertificazione:(e,t)=>c.delete("/api/cantieri/".concat(e,"/certificazioni/").concat(t)),generatePDF:(e,t)=>c.get("/api/cantieri/".concat(e,"/certificazioni/").concat(t,"/pdf"),{responseType:"blob"}),getStatistiche:e=>c.get("/api/cantieri/".concat(e,"/certificazioni/statistiche")),exportCertificazioni:(e,t)=>c.get("/api/cantieri/".concat(e,"/certificazioni/export"),{params:t,responseType:"blob"}),generateReport:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"completo";return c.get("/api/cantieri/".concat(e,"/certificazioni/report/").concat(t))},bulkDelete:(e,t)=>c.post("/api/cantieri/".concat(e,"/certificazioni/bulk-delete"),{ids:t}),generateBulkPdf:(e,t)=>c.post("/api/cantieri/".concat(e,"/certificazioni/bulk-pdf"),{ids:t},{responseType:"blob"}),validateCertificazione:(e,t)=>c.post("/api/cantieri/".concat(e,"/certificazioni/validate"),t)},g={getStrumenti:e=>c.get("/api/cantieri/".concat(e,"/strumenti")),createStrumento:(e,t)=>c.post("/api/cantieri/".concat(e,"/strumenti"),t),updateStrumento:(e,t,a)=>c.put("/api/cantieri/".concat(e,"/strumenti/").concat(t),a),deleteStrumento:(e,t)=>c.delete("/api/cantieri/".concat(e,"/strumenti/").concat(t))},m={getRapporti:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:100;return c.get("/api/cantieri/".concat(e,"/rapporti"),{params:{skip:t,limit:a}})},createRapporto:(e,t)=>c.post("/api/cantieri/".concat(e,"/rapporti"),t),getRapporto:(e,t)=>c.get("/api/cantieri/".concat(e,"/rapporti/").concat(t)),updateRapporto:(e,t,a)=>c.put("/api/cantieri/".concat(e,"/rapporti/").concat(t),a),deleteRapporto:(e,t)=>c.delete("/api/cantieri/".concat(e,"/rapporti/").concat(t)),aggiornaStatistiche:(e,t)=>c.post("/api/cantieri/".concat(e,"/rapporti/").concat(t,"/aggiorna-statistiche"))},v={getNonConformita:e=>c.get("/api/cantieri/".concat(e,"/non-conformita")),createNonConformita:(e,t)=>c.post("/api/cantieri/".concat(e,"/non-conformita"),t),updateNonConformita:(e,t,a)=>c.put("/api/cantieri/".concat(e,"/non-conformita/").concat(t),a),deleteNonConformita:(e,t)=>c.delete("/api/cantieri/".concat(e,"/non-conformita/").concat(t))},C={importCavi:(e,t,a)=>{let o=new FormData;return o.append("file",t),o.append("revisione",a),c.post("/api/excel/".concat(e,"/import-cavi"),o,{headers:{"Content-Type":"multipart/form-data"}})},importBobine:(e,t)=>{let a=new FormData;return a.append("file",t),c.post("/api/excel/".concat(e,"/import-parco-bobine"),a,{headers:{"Content-Type":"multipart/form-data"}})},exportCavi:e=>c.get("/api/excel/".concat(e,"/export-cavi"),{responseType:"blob"}),exportBobine:e=>c.get("/api/excel/".concat(e,"/export-parco-bobine"),{responseType:"blob"})},_={getReportAvanzamento:e=>c.get("/api/reports/".concat(e,"/avanzamento")),getReportBOQ:e=>c.get("/api/reports/".concat(e,"/boq")),getReportUtilizzoBobine:e=>c.get("/api/reports/".concat(e,"/storico-bobine")),getReportProgress:e=>c.get("/api/reports/".concat(e,"/progress")),getReportPosaPeriodo:(e,t,a)=>{let o=new URLSearchParams;t&&o.append("data_inizio",t),a&&o.append("data_fine",a);let i=o.toString();return c.get("/api/reports/".concat(e,"/posa-periodo").concat(i?"?".concat(i):""))}},h={getCantieri:()=>c.get("/api/cantieri"),getCantiere:e=>c.get("/api/cantieri/".concat(e)),createCantiere:e=>c.post("/api/cantieri",e),updateCantiere:(e,t)=>c.put("/api/cantieri/".concat(e),t),getCantiereStatistics:e=>c.get("/api/cantieri/".concat(e,"/statistics")),getWeatherData:e=>c.get("/api/cantieri/".concat(e,"/weather"))},S={getUsers:()=>c.get("/api/users"),getUser:e=>c.get("/api/users/".concat(e)),createUser:e=>c.post("/api/users",e),updateUser:(e,t)=>c.put("/api/users/".concat(e),t),deleteUser:e=>c.delete("/api/users/".concat(e)),toggleUserStatus:e=>c.get("/api/users/toggle/".concat(e)),checkExpiredUsers:()=>c.get("/api/users/check-expired"),impersonateUser:e=>c.post("/api/auth/impersonate",{user_id:e}),getDatabaseData:()=>c.get("/api/users/db-raw"),resetDatabase:()=>c.post("/api/admin/reset-database")}},40283:(e,t,a)=>{a.d(t,{A:()=>r,AuthProvider:()=>l});var o=a(95155),i=a(12115),n=a(25731);let c=(0,i.createContext)(void 0);function r(){let e=(0,i.useContext)(c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function l(e){let{children:t}=e,[a,r]=(0,i.useState)(null),[l,s]=(0,i.useState)(null),[p,d]=(0,i.useState)(!0),[u,g]=(0,i.useState)(()=>"true"===localStorage.getItem("isImpersonating")),[m,v]=(0,i.useState)(()=>{{let e=localStorage.getItem("impersonatedUser");return e?JSON.parse(e):null}}),[C,_]=(0,i.useState)(null),[h,S]=(0,i.useState)(null),[I,f]=(0,i.useState)(null),b=!!a&&a.id_utente||!!l&&l.id_cantiere;(0,i.useEffect)(()=>{console.log("\uD83D\uDD10 AuthContext: Inizializzazione - controllo autenticazione esistente"),localStorage.getItem("token")?(console.log("\uD83D\uDD10 AuthContext: Token trovato, verifica validit\xe0"),k()):(console.log("\uD83D\uDD10 AuthContext: Nessun token trovato, richiesto login"),d(!1),r(null),s(null),v(null),g(!1))},[]),(0,i.useEffect)(()=>{if(a&&!p&&!l){let e=localStorage.getItem("selectedCantiereId"),t=localStorage.getItem("selectedCantiereName");if(e&&"null"!==e&&"undefined"!==e){let o=parseInt(e,10);!isNaN(o)&&o>0?s({id_cantiere:o,commessa:t||"Cantiere ".concat(o),codice_univoco:"",id_utente:a.id_utente}):(localStorage.removeItem("selectedCantiereId"),localStorage.removeItem("selectedCantiereName"))}}},[a,p,l]);let k=async()=>{try{if(d(!0),localStorage.getItem("token"))try{let e=await n.ZQ.verifyToken(),t={id_utente:e.user_id,username:e.username,ruolo:e.role};r(t);let a=!0===e.is_impersonated;if(g(a),a&&e.impersonated_id){let t={id:e.impersonated_id,username:e.impersonated_username,role:e.impersonated_role};v(t),localStorage.setItem("impersonatedUser",JSON.stringify(t)),localStorage.setItem("isImpersonating","true")}else v(null),localStorage.removeItem("impersonatedUser"),localStorage.removeItem("isImpersonating");if("cantieri_user"===e.role&&e.cantiere_id){let t={id_cantiere:e.cantiere_id,commessa:e.cantiere_name||"Cantiere ".concat(e.cantiere_id),codice_univoco:"",id_utente:e.user_id};console.log("\uD83C\uDFD7️ AuthContext: Impostazione cantiere per utente cantiere:",t),s(t),localStorage.setItem("selectedCantiereId",e.cantiere_id.toString()),localStorage.setItem("selectedCantiereName",t.commessa)}else{console.log("\uD83C\uDFD7️ AuthContext: Utente standard, controllo cantiere dal localStorage");let e=localStorage.getItem("cantiere_data");if(e)try{let t=JSON.parse(e);console.log("\uD83C\uDFD7️ AuthContext: Caricamento cantiere da cantiere_data:",t),s(t),localStorage.setItem("selectedCantiereId",t.id_cantiere.toString()),localStorage.setItem("selectedCantiereName",t.commessa)}catch(e){console.warn("\uD83C\uDFD7️ AuthContext: Errore parsing cantiere_data:",e),localStorage.removeItem("cantiere_data")}}}catch(e){console.error("\uD83D\uDD10 AuthContext: Token non valido:",e),localStorage.removeItem("token"),localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data"),localStorage.removeItem("selectedCantiereId"),localStorage.removeItem("selectedCantiereName"),localStorage.removeItem("isImpersonating"),localStorage.removeItem("impersonatedUser"),r(null),s(null),g(!1),v(null)}else console.log("\uD83D\uDD10 AuthContext: Nessun token trovato"),r(null),s(null)}catch(e){console.error("\uD83D\uDD10 AuthContext: Errore generale durante checkAuth:",e),localStorage.removeItem("token"),localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data"),localStorage.removeItem("isImpersonating"),localStorage.removeItem("impersonatedUser"),r(null),s(null),g(!1),v(null)}finally{console.log("\uD83D\uDD10 AuthContext: checkAuth completato, impostazione loading = false"),d(!1)}},x=async(e,t)=>{try{console.log("\uD83D\uDD10 AuthContext: Inizio login per:",e),d(!0);let a=await n.ZQ.login({username:e,password:t});console.log("\uD83D\uDCE1 AuthContext: Risposta backend ricevuta:",a);{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data"),localStorage.setItem("token",a.access_token),localStorage.setItem("access_token",a.access_token),console.log("\uD83D\uDCBE AuthContext: Token salvato nel localStorage");let e={id_utente:a.user_id,username:a.username,ruolo:a.role};return localStorage.setItem("user_data",JSON.stringify(e)),a.expiration_warning?(console.log("⚠️ AuthContext: Warning scadenza ricevuto:",a.expiration_warning),_(a.expiration_warning),S(a.days_until_expiration),f(a.expiration_date)):(_(null),S(null),f(null)),console.log("\uD83D\uDC64 AuthContext: Dati utente creati:",e),r(e),s(null),console.log("✅ AuthContext: Stato utente aggiornato"),setTimeout(()=>{console.log("\uD83D\uDD04 AuthContext: Verifica stato dopo login:",{user:e,isAuthenticated:!!(e&&e.id_utente)})},50),{success:!0,user:e}}}catch(e){var a,o;return console.error("❌ AuthContext: Errore durante login:",e),{success:!1,error:(null==(o=e.response)||null==(a=o.data)?void 0:a.detail)||e.message||"Errore durante il login"}}finally{d(!1)}},A=async(e,t)=>{try{console.log("\uD83D\uDD10 AuthContext: Inizio login cantiere:",e),d(!0);let a=await n.ZQ.loginCantiere({codice_cantiere:e,password_cantiere:t});console.log("\uD83D\uDD10 AuthContext: Risposta login cantiere:",a);{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.setItem("token",a.access_token),localStorage.setItem("access_token",a.access_token),console.log("\uD83D\uDD10 AuthContext: Token salvato");let t={id_cantiere:a.cantiere_id,commessa:a.cantiere_name,codice_univoco:e,id_utente:a.user_id};return console.log("\uD83D\uDD10 AuthContext: Dati cantiere preparati:",t),localStorage.setItem("cantiere_data",JSON.stringify(t)),console.log("\uD83D\uDD10 AuthContext: Dati cantiere salvati in localStorage"),s(t),r(null),console.log("\uD83D\uDD10 AuthContext: Context aggiornato"),await k(),console.log("\uD83D\uDD10 AuthContext: checkAuth completato"),{success:!0,cantiere:t}}}catch(e){var a,o;return console.error("❌ AuthContext: Errore durante login cantiere:",e),{success:!1,error:(null==(o=e.response)||null==(a=o.data)?void 0:a.detail)||e.message||"Errore durante il login cantiere"}}finally{d(!1)}},z=async e=>{try{let t=await n.dG.impersonateUser(e);{localStorage.setItem("token",t.access_token);let e={id:t.impersonated_id,username:t.impersonated_username,role:t.impersonated_role};return localStorage.setItem("impersonatedUser",JSON.stringify(e)),v(e),g(!0),localStorage.setItem("isImpersonating","true"),{impersonatedUser:e}}}catch(e){throw e}};return(0,o.jsx)(c.Provider,{value:{user:a,cantiere:l,isAuthenticated:b,isLoading:p,isImpersonating:u,impersonatedUser:m,expirationWarning:C,daysUntilExpiration:h,expirationDate:I,login:x,loginCantiere:A,logout:()=>{console.log("\uD83D\uDEAA AuthContext: Inizio logout"),["token","access_token","user_data","cantiere_data","selectedCantiereId","selectedCantiereName","isImpersonating","impersonatedUser"].forEach(e=>{localStorage.removeItem(e)}),sessionStorage.clear(),r(null),s(null),g(!1),v(null),_(null),S(null),f(null),console.log("\uD83D\uDEAA AuthContext: Logout completato, reindirizzamento a /login"),window.location.replace("/login")},checkAuth:k,impersonateUser:z,selectCantiere:e=>{if(!e||!e.id_cantiere||e.id_cantiere<=0)return void console.error("\uD83C\uDFD7️ AuthContext: Tentativo di selezione cantiere non valido:",e);try{let t=e.commessa||"Cantiere ".concat(e.id_cantiere);localStorage.setItem("selectedCantiereId",e.id_cantiere.toString()),localStorage.setItem("selectedCantiereName",t);let a={...e,commessa:t};console.log("\uD83C\uDFD7️ AuthContext: Cantiere selezionato:",a),s(a),localStorage.removeItem("cantiere_data")}catch(e){console.error("\uD83C\uDFD7️ AuthContext: Errore nella selezione cantiere:",e)}},clearCantiere:()=>{console.log("\uD83C\uDFD7️ AuthContext: Pulizia stato cantiere"),s(null),localStorage.removeItem("selectedCantiereId"),localStorage.removeItem("selectedCantiereName"),localStorage.removeItem("cantiere_data")},dismissExpirationWarning:()=>{_(null),S(null),f(null)}},children:t})}}}]);