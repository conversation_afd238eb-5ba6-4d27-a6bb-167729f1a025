(()=>{var e={};e.id=6123,e.ids=[6123],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31302:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>l,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>p});var o=t(96559),a=t(48088),n=t(37719),i=t(32190);async function p(e){try{let r=e.headers.get("authorization");if(!r||!r.startsWith("Bearer "))return i.NextResponse.json({detail:"Token di autorizzazione mancante"},{status:401});let t="http://localhost:8001";console.log("\uD83D\uDD04 Check Expired Users API: Proxying request to backend:",`${t}/api/users/check-expired`);let s=await fetch(`${t}/api/users/check-expired`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:r}});if(console.log("\uD83D\uDCE1 Check Expired Users API: Backend response status:",s.status),!s.ok){let e=await s.json().catch(()=>({detail:"Errore sconosciuto"}));return console.error("❌ Check Expired Users API: Backend error:",e),i.NextResponse.json(e,{status:s.status})}let o=await s.json();return console.log("\uD83D\uDCE1 Check Expired Users API: Backend response data:",o),i.NextResponse.json(o,{status:s.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("❌ Check Expired Users API: Error:",e),i.NextResponse.json({detail:"Errore interno del server"},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/users/check-expired/route",pathname:"/api/users/check-expired",filename:"route",bundlePath:"app/api/users/check-expired/route"},resolvedPagePath:"C:\\CMS\\webapp-nextjs_1\\src\\app\\api\\users\\check-expired\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:u,workUnitAsyncStorage:d,serverHooks:l}=c;function x(){return(0,n.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:d})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580],()=>t(31302));module.exports=s})();