'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { 
  Loader2, 
  AlertCircle, 
  Users, 
  Plus, 
  Edit, 
  Trash2, 
  Phone, 
  Mail,
  User,
  Save,
  X
} from 'lucide-react'
import { responsabiliApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'
import { validateResponsabile } from '@/utils/comandeValidation'

interface GestisciResponsabiliDialogProps {
  open: boolean
  onClose: () => void
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

interface Responsabile {
  id_responsabile: number
  nome_responsabile: string
  numero_telefono?: string
  mail?: string
  id_cantiere: number
}

interface FormData {
  nome_responsabile: string
  numero_telefono: string
  mail: string
}

export default function GestisciResponsabiliDialog({
  open,
  onClose,
  onSuccess,
  onError
}: GestisciResponsabiliDialogProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [responsabili, setResponsabili] = useState<Responsabile[]>([])
  const [editingId, setEditingId] = useState<number | null>(null)
  const [showAddForm, setShowAddForm] = useState(false)

  const { cantiere } = useAuth()

  // Get cantiere ID con fallback al localStorage
  const [cantiereId, setCantiereId] = useState<number>(0)

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedId = cantiere?.id_cantiere || parseInt(localStorage.getItem('selectedCantiereId') || '0')
      setCantiereId(storedId)
    }
  }, [cantiere])

  const [formData, setFormData] = useState<FormData>({
    nome_responsabile: '',
    numero_telefono: '',
    mail: ''
  })

  // Carica responsabili quando si apre il dialog
  useEffect(() => {
    if (open && cantiereId > 0) {
      loadResponsabili()
    }
  }, [open, cantiereId])

  // Reset form quando si chiude il dialog
  useEffect(() => {
    if (!open) {
      setFormData({
        nome_responsabile: '',
        numero_telefono: '',
        mail: ''
      })
      setError('')
      setEditingId(null)
      setShowAddForm(false)
    }
  }, [open])

  const loadResponsabili = async () => {
    try {
      setLoading(true)
      const response = await responsabiliApi.getResponsabili(cantiereId)
      const responsabiliData = response?.data || response || []
      setResponsabili(Array.isArray(responsabiliData) ? responsabiliData : [])
    } catch (error: any) {
      setError('Errore durante il caricamento dei responsabili')
    } finally {
      setLoading(false)
    }
  }

  const handleAdd = async () => {
    try {
      setLoading(true)
      setError('')

      const responsabileData = {
        nome_responsabile: formData.nome_responsabile.trim(),
        numero_telefono: formData.numero_telefono.trim() || null,
        mail: formData.mail.trim() || null
      }

      // Validazione con utility
      const validation = validateResponsabile(responsabileData)
      if (!validation.isValid) {
        setError(`Errori di validazione: ${validation.errors.join(', ')}`)
        return
      }

      await responsabiliApi.createResponsabile(cantiereId, responsabileData)
      
      onSuccess('Responsabile aggiunto con successo')
      setFormData({
        nome_responsabile: '',
        numero_telefono: '',
        mail: ''
      })
      setShowAddForm(false)
      loadResponsabili()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || 'Errore durante la creazione del responsabile'
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (responsabile: Responsabile) => {
    setFormData({
      nome_responsabile: responsabile.nome_responsabile,
      numero_telefono: responsabile.numero_telefono || '',
      mail: responsabile.mail || ''
    })
    setEditingId(responsabile.id_responsabile)
    setShowAddForm(false)
  }

  const handleUpdate = async () => {
    if (!editingId) return

    try {
      setLoading(true)
      setError('')

      // Validazioni
      if (!formData.nome_responsabile.trim()) {
        setError('Il nome del responsabile è obbligatorio')
        return
      }

      if (formData.mail && !formData.mail.includes('@')) {
        setError('Inserisci un indirizzo email valido')
        return
      }

      const responsabileData = {
        nome_responsabile: formData.nome_responsabile.trim(),
        numero_telefono: formData.numero_telefono.trim() || null,
        mail: formData.mail.trim() || null
      }

      await responsabiliApi.updateResponsabile(cantiereId, editingId, responsabileData)
      
      onSuccess('Responsabile aggiornato con successo')
      setFormData({
        nome_responsabile: '',
        numero_telefono: '',
        mail: ''
      })
      setEditingId(null)
      loadResponsabili()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || 'Errore durante l\'aggiornamento del responsabile'
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id: number, nome: string) => {
    if (!confirm(`Sei sicuro di voler eliminare il responsabile "${nome}"?`)) {
      return
    }

    try {
      setLoading(true)
      await responsabiliApi.deleteResponsabile(cantiereId, id)
      onSuccess('Responsabile eliminato con successo')
      loadResponsabili()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || 'Errore durante l\'eliminazione del responsabile'
      onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const cancelEdit = () => {
    setEditingId(null)
    setShowAddForm(false)
    setFormData({
      nome_responsabile: '',
      numero_telefono: '',
      mail: ''
    })
    setError('')
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Gestisci Responsabili
          </DialogTitle>
          <DialogDescription>
            Gestisci i responsabili per il cantiere {typeof window !== 'undefined' ? localStorage.getItem('selectedCantiereName') || cantiereId : cantiereId}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Pulsante aggiungi nuovo */}
          {!showAddForm && !editingId && (
            <Button 
              onClick={() => setShowAddForm(true)}
              className="w-full"
              variant="outline"
            >
              <Plus className="h-4 w-4 mr-2" />
              Aggiungi Nuovo Responsabile
            </Button>
          )}

          {/* Form per aggiungere/modificare */}
          {(showAddForm || editingId) && (
            <Card className="border-2 border-blue-200">
              <CardContent className="p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">
                    {editingId ? 'Modifica Responsabile' : 'Nuovo Responsabile'}
                  </h4>
                  <Button variant="ghost" size="sm" onClick={cancelEdit}>
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="nome">Nome Responsabile *</Label>
                    <Input
                      id="nome"
                      placeholder="Nome e cognome"
                      value={formData.nome_responsabile}
                      onChange={(e) => setFormData(prev => ({ ...prev, nome_responsabile: e.target.value }))}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="telefono">Numero Telefono</Label>
                      <Input
                        id="telefono"
                        placeholder="+39 ************"
                        value={formData.numero_telefono}
                        onChange={(e) => setFormData(prev => ({ ...prev, numero_telefono: e.target.value }))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={formData.mail}
                        onChange={(e) => setFormData(prev => ({ ...prev, mail: e.target.value }))}
                      />
                    </div>
                  </div>
                </div>

                <div className="flex gap-2 pt-2">
                  <Button 
                    onClick={editingId ? handleUpdate : handleAdd}
                    disabled={loading}
                    className="flex-1"
                  >
                    {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    <Save className="mr-2 h-4 w-4" />
                    {editingId ? 'Aggiorna' : 'Aggiungi'}
                  </Button>
                  <Button variant="outline" onClick={cancelEdit}>
                    Annulla
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Lista responsabili */}
          <div className="space-y-3">
            <h4 className="font-medium flex items-center gap-2">
              <Users className="h-4 w-4" />
              Responsabili Esistenti ({responsabili.length})
            </h4>

            {loading && responsabili.length === 0 ? (
              <div className="flex items-center justify-center py-8">
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Caricamento responsabili...
                </div>
              </div>
            ) : responsabili.length === 0 ? (
              <div className="text-center py-8 text-slate-500">
                Nessun responsabile trovato
              </div>
            ) : (
              <div className="space-y-2">
                {responsabili.map((responsabile) => (
                  <Card key={responsabile.id_responsabile} className={editingId === responsabile.id_responsabile ? 'border-blue-300' : ''}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <User className="h-4 w-4 text-slate-500" />
                            <span className="font-medium">{responsabile.nome_responsabile}</span>
                          </div>
                          <div className="space-y-1 text-sm text-slate-600">
                            {responsabile.numero_telefono && (
                              <div className="flex items-center gap-2">
                                <Phone className="h-3 w-3" />
                                {responsabile.numero_telefono}
                              </div>
                            )}
                            {responsabile.mail && (
                              <div className="flex items-center gap-2">
                                <Mail className="h-3 w-3" />
                                {responsabile.mail}
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(responsabile)}
                            disabled={loading || editingId === responsabile.id_responsabile}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(responsabile.id_responsabile, responsabile.nome_responsabile)}
                            disabled={loading}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Chiudi
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
